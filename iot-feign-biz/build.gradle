
plugins {
    id 'java'
}

jar{
    enabled = true
}

dependencies {
    implementation project(':iot-common')
    implementation project(':iot-model')
//    implementation('org.springframework.cloud:spring-cloud-starter-netflix-ribbon')
//    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
    implementation("com.playtika.reactivefeign:feign-reactor-webclient:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-cloud:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-spring-configuration:${feignReactiveVersion}")

    testImplementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')
}
