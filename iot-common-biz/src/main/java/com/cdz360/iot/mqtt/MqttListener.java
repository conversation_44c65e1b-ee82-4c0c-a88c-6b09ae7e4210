package com.cdz360.iot.mqtt;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.MqttUtils;
import java.nio.charset.StandardCharsets;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

@Slf4j
public class MqttListener {


    private boolean mqttConnectFlag = false;

    private AtomicLong msgCnt = new AtomicLong(1L);
    private MqttClient mqttClient = null;
    private MqttConnectOptions connOpts = null;

    private Timer timerMQTT = null;

    private ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newCachedThreadPool();

    private String name;

    private String clientId;

    private IMqttMsgHandler handler;

    public MqttListener(MqttProperties props, IMqttMsgHandler handler) {
        this.name = props.getName();
        this.handler = handler;
        this.clientId = MqttUtils.formatCloudClientId(props.getEnv(), props.getName(),
            props.getHostIp());
    }


    public boolean connect(MqttProperties props) {
        try {
            boolean isNewClient = mqttClient == null
                || !StringUtils.equals(mqttClient.getServerURI(), props.getUrl());
            if (isNewClient) {
                MemoryPersistence persistence = new MemoryPersistence();
                mqttClient = new MqttClient(props.getUrl(), this.clientId, persistence);
            }
            if (connOpts == null) {
                connOpts = new MqttConnectOptions();
            }
            connOpts.setUserName(props.getUsername());
            connOpts.setPassword(props.getPassword().toCharArray());
            connOpts.setCleanSession(true);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            connOpts.setKeepAliveInterval(10);
            connOpts.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);
            connOpts.setAutomaticReconnect(true);
            connOpts.setConnectionTimeout(30);
//            connOpts.setWill(props.getLwt(), gwno.getBytes(StandardCharsets.UTF_8), 1, false);

            log.info("{} Connecting to mqtt clientId = {}, broker = {}", this.name,
                this.clientId, props.getUrl());
            mqttClient.connect(connOpts);
            mqttConnectFlag = true;
            log.info("{} MQTT connected", this.name);
            mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("{} MQTT连接意外断开。", MqttListener.this.name, cause);
                    mqttConnectFlag = false;
                    // 立即调用
                    if (timerMQTT == null) {
                        timerMQTT = new Timer("Timer-MQTT");
                    }
                    timerMQTT.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            mqttConnectFlag = mqttClient.isConnected();
                            log.info("{} mqttConnectFlag: {}", MqttListener.this.name,
                                mqttConnectFlag);
                            if (!mqttConnectFlag) {
//                                iotUpService.login();
                            }
                        }
                    }, 60000);
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    log.info("{} topic: {}, message: {}", MqttListener.this.name, topic,
                        JsonUtils.toJsonString(message));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    log.info("{} token: {}", MqttListener.this.name, token.toString());
                }
            });

//            mqttClient.subscribe(props.getTopic(), (tp, msg) -> processMessage(tp, msg));

        } catch (MqttException me) {
            mqttConnectFlag = false;
            log.error("{} reason {}", this.name, me.getReasonCode());
            log.error("{} msg {}", this.name, me.getMessage());
            log.error("{} loc {}", this.name, me.getLocalizedMessage());
            log.error("{} cause {}", this.name, me.getMessage());
            log.error("{} exception ", this.name, me);
            log.error(me.getMessage(), me);
        }
        return mqttConnectFlag;
    }

    public boolean subscribe(String subName, String topic) {
        try {
            mqttClient.subscribe(topic, (tp, msg) -> processMessage(subName, tp, msg));
        } catch (MqttException me) {
            log.error("{} {} reason {}", this.name, subName, me.getReasonCode());
            log.error("{} {} msg {}", this.name, subName, me.getMessage());
            log.error("{} {} loc {}", this.name, subName, me.getLocalizedMessage());
            log.error("{} {} cause {}", this.name, subName, me.getMessage());
            log.error("{} {} exception ", this.name, subName, me);
            log.error(me.getMessage(), me);
            return false;
        }
        return true;
    }

    private void processMessage(String subName, String topic, MqttMessage message) {
        log.info("{}-{} mqtt topic: {}, msg: (id={},qos={},duplicate={},retained={})", this.name,
            subName, topic,
            message.getId(), message.getQos(), message.isDuplicate(), message.isRetained());
        Long idxTmp = msgCnt.getAndAdd(1L);

        executor.submit(() -> {
            log.debug("{}-{} idx: {}, 处理前", this.name, subName, idxTmp);
            this.handler.handleMessage(subName,  new String(message.getPayload(), StandardCharsets.UTF_8));
            log.debug("{}-{} idx: {}, 处理后", this.name, subName, idxTmp);
        });
    }
}
