package com.cdz360.iot.biz.rest;

import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.biz.IotCacheService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class IotRestBase {

    protected abstract IotCacheService getCacheService();

    protected void checkToken(String authHd, String gwno) {
        IotAssert.isTrue(!StringUtils.isBlank(gwno), "参数错误,网关编号不能为空");
        var tokenCache = getCacheService().getToken(gwno);
        var tokenHd = this.getToken(authHd);
        if (StringUtils.isBlank(tokenCache) || StringUtils.isBlank(tokenHd)
                || !tokenCache.equalsIgnoreCase(tokenHd)) {
            log.warn("token 校验失败. gwno = {}, 网关发送 token = {}, 缓存 token = {}", gwno, tokenHd, tokenCache);
            throw new DcTokenException(gwno, "登录已过期");
        }
    }

    /**
     * 解析 Authorization 头, 将token部分返回.
     * <p>
     * `Authorization: Basic c6697be63855fdfc675bb11a5b622feb91c2955241efce332c739ef95d6e1807`
     * </p>
     *
     * @param authHd
     * @return
     */
    protected String getToken(String authHd) {
        if (StringUtils.isBlank(authHd)) {
            return null;
        }
        String ret = GwRestUtils.getToken(authHd);
        if (StringUtils.isNotBlank(ret)) {
            return ret;
        } else {
            log.error("get token failed. {}", authHd);
            return null;
        }
    }
}
