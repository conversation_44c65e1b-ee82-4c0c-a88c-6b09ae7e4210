package com.cdz360.iot.biz.utils;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Repository
public class RedisUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }



    public void stringSet(String key, String value, int timeout, TimeUnit timeUnit) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }
    public String stringGet(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 入队
     *
     * @param key
     * @param value
     * @return
     */
    public Long rightPushList(String key, String value) {
        return stringRedisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 第一个元素
     *
     * @param key
     * @param value
     * @return
     */
    public Long leftPushList(String key, String value) {
        return stringRedisTemplate.opsForList().leftPush(key, value);
    }

    public Long leftPushAllList(String key, Collection<String> values) {
        return stringRedisTemplate.opsForList().leftPushAll(key, values);
    }

    /**
     * 出队
     *
     * @param key
     * @return
     */
    public String leftPopList(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }


    /**
     * 栈/队列长
     *
     * @param key
     * @return
     */
    public Long sizeList(String key) {
        return stringRedisTemplate.opsForList().size(key);
    }

    /**
     * 范围检索
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> rangeList(String key, long start, long end) {
        return stringRedisTemplate.opsForList().range(key, start, end);
    }

    public void trimList(String key, long start, long end) {
        stringRedisTemplate.opsForList().trim(key, start, end);
    }



    public void watch(String key) {
        stringRedisTemplate.watch(key);
    }

    public void multi() {
        stringRedisTemplate.multi();
    }

    public void expire(String key, long timeout, TimeUnit timeUnit) {
        stringRedisTemplate.expire(key, timeout, timeUnit);
    }

}
