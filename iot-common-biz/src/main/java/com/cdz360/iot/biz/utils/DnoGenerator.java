package com.cdz360.iot.biz.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.ro.ess.ds.BmsRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssBatteryBundleRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 设备编号(DNO)生成工具类 统一管理各种设备类型的DNO生成逻辑
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class DnoGenerator {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";

    @Value("${dno.generator.length:8}")
    private int dnoLength;

    @Value("${dno.generator.max-retry:20}")
    private int maxRetryTimes;

    /**
     * 设备类型到DNO存在性检查函数的映射
     */
    private Map<EssEquipType, Function<String, Boolean>> dnoExistsCheckers;

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private EssBatteryBundleRoDs essBatteryBundleRoDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private BmsRoDs bmsRoDs;

    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    /**
     * 初始化设备类型到DNO检查函数的映射
     */
    @PostConstruct
    public void initDnoCheckers() {
        dnoExistsCheckers = new HashMap<>();

        // 配置各设备类型的 DNO 存在性检查函数
        dnoExistsCheckers.put(EssEquipType.ESS, dno -> essRoDs.getByDno(dno) != null);
        dnoExistsCheckers.put(EssEquipType.SRS, dno -> srsRoDs.getByDno(dno) != null);
        dnoExistsCheckers.put(EssEquipType.BATTERY_PACK,
            dno -> essBatteryBundleRoDs.getByDno(dno) != null);
        dnoExistsCheckers.put(EssEquipType.PV_INV, dno -> gtiRoDs.getByDno(dno) != null);
        dnoExistsCheckers.put(EssEquipType.BMS, dno -> bmsRoDs.getByDno(dno) != null);
        dnoExistsCheckers.put(EssEquipType.GRID_GATEWAY_METER,
            dno -> meterRoDs.getByNo(dno) != null);
        dnoExistsCheckers.put(EssEquipType.PCS, dno -> pcsRoDs.getByDno(dno) != null);

        log.info("DNO检查器初始化完成，支持设备类型: {}", dnoExistsCheckers.keySet());
    }

    /**
     * 生成设备唯一编号(DNO) - 同步版本
     *
     * @param equipType 设备类型
     * @return 唯一的设备编号
     * @throws RuntimeException 当超过最大重试次数仍无法生成唯一DNO时抛出
     */
    public String genDno(EssEquipType equipType) {
        return genDnoInternal(equipType);
    }

    /**
     * 内部DNO生成逻辑
     */
    private String genDnoInternal(EssEquipType equipType) {
        log.debug("开始生成DNO，设备类型: {}", equipType);

        Function<String, Boolean> existsChecker = getExistsChecker(equipType);

        String dno = null;
        int retryCount = 0;

        do {
            dno = RandomStringUtils.random(dnoLength, RANDOM_CHAR);
            retryCount++;

            if (retryCount > maxRetryTimes) {
                String errorMsg = String.format(
                    "超过最大重试次数(%d)，无法为设备类型 %s 生成唯一DNO", maxRetryTimes, equipType);
                log.error(errorMsg);
                throw new DcServiceException(errorMsg);
            }

            if (retryCount > 1) {
                log.debug("DNO冲突，重试第{}次，设备类型: {}, 冲突DNO: {}", retryCount - 1, equipType,
                    dno);
            }

        } while (existsChecker.apply(dno));

        log.debug("DNO生成成功: {}, 设备类型: {}, 重试次数: {}", dno, equipType, retryCount - 1);
        return dno;
    }

    /**
     * 获取指定设备类型的DNO存在性检查函数
     */
    private Function<String, Boolean> getExistsChecker(EssEquipType equipType) {
        Function<String, Boolean> checker = dnoExistsCheckers.get(equipType);

        if (checker == null) {
            log.warn("未找到设备类型 {} 的DNO检查器，使用默认检查器(EssEquipRoDs)", equipType);
            // 使用默认的设备检查器
            return dno -> essEquipRoDs.getByDno(dno) != null;
        }

        return checker;
    }

}
