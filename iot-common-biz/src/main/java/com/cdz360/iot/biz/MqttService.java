package com.cdz360.iot.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.MqttUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @Classname MqttService
 * @Description
 * @Date 4/24/2020 9:57 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class MqttService implements InitializingBean {

    private final String mqttCloudClientPrefix = IotConstants.MQTT_CLOUD_CLIENT_PREFIX;
    private final long mqttReconnectDelay = 3000;
    @Value("${env}")
    private String env;
    @Value("${iot.gw.mqtt.id}")
    private String mqttId;
    @Value("${iot.gw.mqtt.type}")
    private String mqttType;
    @Value("${iot.gw.mqtt.group}")
    private String mqttGroup;
    @Value("${iot.gw.mqtt.serverUrl}")
    private String mqttServerUrl;
    @Value("${iot.gw.mqtt.topicPrefix}")
    private String mqttTopicPrefix;
    @Value("${iot.gw.mqtt.accessKey}")
    private String mqttAccessKey;
    @Value("${iot.gw.mqtt.accessSecret}")
    private String mqttSecret;
    @Value("${iot.gw.mqtt.username}")
    private String mqttUsername;
    @Value("${iot.gw.mqtt.password}")
    private String mqttPassword;

    private MqttAsyncClient asyncClient;
    private String selfId;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.selfId =
            this.mqttCloudClientPrefix + this.env + RandomStringUtils.randomAlphanumeric(6);
        init();
        this.connectMqtt();
    }

    private void init() throws MqttException {

        MemoryPersistence persistence = new MemoryPersistence();

        String clientId = MqttUtils.getMqttClientId(selfId, mqttGroup, false);

        asyncClient = new MqttAsyncClient(this.mqttServerUrl, clientId, persistence);

        log.info("Connecting to mqtt. clientId = {}, url = {}", clientId, this.mqttServerUrl);
    }

    private MqttConnectOptions getOpt() {
        MqttConnectOptions connOpts = new MqttConnectOptions();
        if (IotConstants.MQTT_TYPE_MOSQUITTO.equalsIgnoreCase(this.mqttType)) {
            connOpts.setUserName(this.mqttUsername);
            connOpts.setPassword(this.mqttPassword.toCharArray());
        } else {
            String clientId = MqttUtils.getMqttClientId(selfId, mqttGroup, false);
            connOpts.setUserName(MqttUtils.getMqttUsername(this.mqttId, this.mqttAccessKey));
            connOpts.setPassword(
                MqttUtils.getMqttPasscode(clientId, this.mqttSecret).toCharArray());
        }
        connOpts.setKeepAliveInterval(60);
        connOpts.setCleanSession(true);
        connOpts.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);
        connOpts.setAutomaticReconnect(true);
        connOpts.setConnectionTimeout(5000);
        return connOpts;
    }

    private void connectMqtt() throws MqttException {
        MqttConnectOptions connOpts = getOpt();
        //        sampleClient.connect(connOpts);
        if (asyncClient.isConnected()) {
            asyncClient.disconnect();
        }
        asyncClient.connect(connOpts).waitForCompletion();

        log.info("Connected");
        asyncClient.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                log.error("连接丢失: {}", mqttId);
                log.error(cause.getMessage(), cause);
                while (true) {
                    try {
                        Thread.sleep(mqttReconnectDelay);
                        connectMqtt();
                        break;
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                log.info("topic = {}, message = {}", topic, JsonUtils.toJsonString(message));
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                log.info("token = {}", token.toString());
            }
        });

        asyncClient.subscribe(String.format("%s_MQTT", this.mqttGroup), 0).waitForCompletion();

    }

    public void publishMessage(String gwno, boolean isSupervisor, String msg) {
        log.info("发送信息到MQTT. gwno: {}, isSupervisor = {}, msg: {}",
            gwno, isSupervisor, msg);

        try {
            MqttMessage message = new MqttMessage(msg.getBytes());
            message.setQos(1);
            String topic = MqttUtils.getMqTopic(gwno, this.env, mqttTopicPrefix, isSupervisor);
            asyncClient.publish(topic, message)
                .waitForCompletion();
            log.info("Message published. topic = {}, message.id = {}", topic, message.getId());
        } catch (MqttException me) {
            log.error("reason " + me.getReasonCode());
            log.error("msg " + me.getMessage());
            log.error("loc " + me.getLocalizedMessage());
            log.error("cause " + me.getCause());
            log.error("excep " + me);
            log.error(me.getMessage(), me);
        }
    }


}