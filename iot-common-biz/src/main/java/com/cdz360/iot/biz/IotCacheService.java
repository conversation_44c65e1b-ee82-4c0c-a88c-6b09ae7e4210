package com.cdz360.iot.biz;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class IotCacheService {
    private final Logger logger = LoggerFactory.getLogger(IotCacheService.class);

    @Autowired
    private RedisUtil redisUtil;

    public String getOrUpdateRealm(String gwno) {

        String key = "realm-" + gwno;

        String realm = redisUtil.stringGet(key);

        logger.info("gwno = {}, 对应缓存的realm = {}", gwno, realm);

        if (StringUtils.isEmpty(realm)) {
            realm = UUID.randomUUID().toString().toUpperCase();

            redisUtil.stringSet(key, realm, 60 * 10, TimeUnit.SECONDS);
        }

        return realm;
    }

    public String getToken(String gwno) {
        //logger.info("gettoken. gwno: {}", gwno);

        String key = this.formatTokenKey(gwno);

        String token = redisUtil.stringGet(key);

        //logger.info("gettoken. gwno: {}, token: {}", gwno, token);

        return token;
    }

    public String getSiteCtrlToken(String gwno) {
        String key = this.formatToCtrlkenKey(gwno);
        String token = redisUtil.stringGet(key);
        return token;
    }

    /**
     * 更新redis缓存中的token
     *
     * @param gwno
     * @param token
     */
    public void updateToken(String gwno, String token) {
        String key = this.formatTokenKey(gwno);
        redisUtil.stringSet(key, token, 2, TimeUnit.DAYS);
    }

    /**
     * 清除网关（场站控制器）token
     * @param gwno
     */
    public void removeToken(String gwno) {
        String key = this.formatTokenKey(gwno);
        redisUtil.stringSet(key, "", 1, TimeUnit.SECONDS);
    }

    private String formatTokenKey(String gwno) {
        return "token-" + gwno;
    }
    private String formatToCtrlkenKey(String gwno) {
        return "token-site-ctrl-" + gwno;
    }
}
