package com.cdz360.iot.biz;

import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.type.GwMqType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqService {


    @Autowired
    private RabbitMqService rabbitMqService;

    @Autowired
    private MqttService mqttService;

    public void publishMessage(GwInfoPo gwInfo, boolean isSupervisor, String msg) {
        if (GwMqType.MQ_TYPE_RABBITMQ == gwInfo.getMqType()) {
            rabbitMqService.publishMessage(gwInfo.getGwno(), msg);
        } else if (GwMqType.MQ_TYPE_MQTT == gwInfo.getMqType()) {
            mqttService.publishMessage(gwInfo.getGwno(), isSupervisor, msg);
        } else {
            log.error("消息队列类型错误. gwno = {}, mqType = {}",
                    gwInfo.getGwno(), gwInfo.getMqType());
        }
    }
}
