package com.cdz360.iot.biz.utils;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.gw.GwResMsg;

public class GwRestUtils {

    public static GwResMsg buildResponse(String seq) {
        return new GwResMsg(seq);
    }

    public static <T> GwObjResMsg<T> buildObjResponse(String seq, T data) {
        return new GwObjResMsg<>(seq, data);
    }

    public static String getToken(String authHd) {
        if (StringUtils.isBlank(authHd)) {
            return null;
        }
        try {
            return authHd.split(" ")[1];
        } catch (Exception e) {
//            log.error("get token failed.", e);
        }
        return null;
    }

    public static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;

            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }
}
