package com.cdz360.iot.biz;

import com.cdz360.data.sync.service.DcEventPublisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RabbitMqService {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    public void publishMessage(String gwno, String msg) {
        this.dcEventPublisher.publishIotGwCmd(gwno, msg);
    }
}
