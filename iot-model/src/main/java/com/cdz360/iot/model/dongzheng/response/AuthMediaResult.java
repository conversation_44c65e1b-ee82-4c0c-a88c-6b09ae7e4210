package com.cdz360.iot.model.dongzheng.response;

/**
 * @Classname AuthResult
 * @Description 东正接口返回鉴权结果后，转换的成这个类，用于表示该鉴权介质的信息，以便进行接下来的流程
 * @Date 2019/5/21 15:32
 * @Created by Rafael
 */
public class AuthMediaResult {
    private String mediaNo;//鉴权介质码
    private String mediaStatus;//状态
    private String stations;//允许在这些场站使用
    private Long commId;//商户id
    private Long frozenAmount;//冻结金额
    private Long balance;//余额

    private Integer defaultPayType;//扣款类型
    private Long payAccountId;//扣款id

    public Long getBalance() {
        return balance;
    }

    public AuthMediaResult setBalance(Long balance) {
        this.balance = balance;
        return this;
    }

    public String getMediaNo() {
        return mediaNo;
    }

    public AuthMediaResult setMediaNo(String mediaNo) {
        this.mediaNo = mediaNo;
        return this;
    }

    public String getMediaStatus() {
        return mediaStatus;
    }

    public AuthMediaResult setMediaStatus(String mediaStatus) {
        this.mediaStatus = mediaStatus;
        return this;
    }

    public String getStations() {
        return stations;
    }

    public AuthMediaResult setStations(String stations) {
        this.stations = stations;
        return this;
    }

    public Long getCommId() {
        return commId;
    }

    public AuthMediaResult setCommId(Long commId) {
        this.commId = commId;
        return this;
    }

    public Long getFrozenAmount() {
        return frozenAmount;
    }

    public AuthMediaResult setFrozenAmount(Long frozenAmount) {
        this.frozenAmount = frozenAmount;
        return this;
    }

    public Integer getDefaultPayType() {
        return defaultPayType;
    }

    public void setDefaultPayType(Integer defaultPayType) {
        this.defaultPayType = defaultPayType;
    }

    public Long getPayAccountId() {
        return payAccountId;
    }

    public void setPayAccountId(Long payAccountId) {
        this.payAccountId = payAccountId;
    }
}