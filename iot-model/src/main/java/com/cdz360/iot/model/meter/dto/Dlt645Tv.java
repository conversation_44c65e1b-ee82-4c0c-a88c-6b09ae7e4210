package com.cdz360.iot.model.meter.dto;

import com.cdz360.iot.model.modbus.type.P645DataType;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Dlt645Tv {

    private String name;

    private P645DataType addr;

    /**
     * 倍数
     */
    private BigDecimal multiple;

    private Boolean isPushData;

    private Object v;
}
