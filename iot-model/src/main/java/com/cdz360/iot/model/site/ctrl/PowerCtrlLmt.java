package com.cdz360.iot.model.site.ctrl;

import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Classname PowerCtrlLmt
 * @Description
 * @Date 4/21/2020 8:00 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PowerCtrlLmt extends BaseObject {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startDate;//季节变化开始日期[MMDD],例[0320]
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TimeDuring> timeDuring;//时段
}