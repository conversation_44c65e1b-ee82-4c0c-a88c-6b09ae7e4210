package com.cdz360.iot.model.park.po;

import com.cdz360.iot.model.park.type.ParkMsgStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "道闸系统推送信息")
public class ParkMsgPo {

	@NotNull(message = "id 不能为 null")
	private Integer id;

	@NotNull(message = "status 不能为 null")
	private ParkMsgStatusType status;

	@Schema(description = "停车订单id")
	@NotNull(message = "parkOrderId 不能为 null")
	private String parkOrderId;

	@NotNull(message = "createTIme 不能为 null")
	private Date createTIme;

	@NotNull(message = "updateTime 不能为 null")
	private Date updateTime;

	@NotNull(message = "msg 不能为 null")
	private String msg;

	@NotNull(message = "response 能为 null")
	private String response;


}
