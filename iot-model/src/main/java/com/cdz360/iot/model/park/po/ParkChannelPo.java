package com.cdz360.iot.model.park.po;


import com.cdz360.iot.model.park.type.ParkChannelType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "场站道闸信息")

public class ParkChannelPo {



	@Schema(description = "记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "车场id")

	@NotNull(message = "parkId 不能为 null")

	private Long parkId;



	@Schema(description = "通道编号")

	@NotNull(message = "channelId 不能为 null")

	private Long channelId;



	@Schema(description = "设备号")

	@NotNull(message = "deviceId 不能为 null")

	@Size(max = 100, message = "deviceId 长度不能超过 100")

	private String deviceId;


	@Schema(description = "是否有效: true(有效); false(无效/删除)")
	private Boolean enable;


	@Schema(description = "通道类型: 0:入口，1：出口，2出入口")

	@NotNull(message = "passType 不能为 null")

	private ParkChannelType passType;



	@Schema(description = "通道名称")

	@NotNull(message = "channelName 不能为 null")

	@Size(max = 100, message = "channelName 长度不能超过 100")

	private String channelName;



	@Schema(description = "第三方记录ID")

	@NotNull(message = "thirdRecId 不能为 null")

	@Size(max = 100, message = "thirdRecId 长度不能超过 100")

	private String thirdRecId;



	private Date createTime;



	private Date updateTime;





}

