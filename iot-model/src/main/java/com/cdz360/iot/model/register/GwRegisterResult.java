package com.cdz360.iot.model.register;

import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonProperty;

public class GwRegisterResult extends BaseObject {
    @JsonProperty(value = "n")
    private String gwno;

    private String passcode;

    public String getGwno() {
        return gwno;
    }

    public GwRegisterResult setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public String getPasscode() {
        return passcode;
    }

    public GwRegisterResult setPasscode(String passcode) {
        this.passcode = passcode;
        return this;
    }
}
