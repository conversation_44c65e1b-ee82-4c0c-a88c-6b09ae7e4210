package com.cdz360.iot.model.evse;

import com.cdz360.base.model.charge.dto.BatteryDto;
import com.cdz360.base.model.charge.dto.BmsDto;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.type.StopModeV2;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OrderStartRequestV2 extends OrderReportRequest {

    @Schema(description = "订单启动结果 0表示成功，其他都表示失败, 失败码同充电结束码")
    private Integer startResult;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "开启方式:0x01: 紧急充电卡订单 0x02: 无卡启动 0x11: 在线卡启动 0x12: VIN码启动 0x21: 管理后台 0x22: 批量启动 0x31: 微信公众号 0x32: 微信小程序 0x33: iOS APP 0x34: 安卓 APP")
    private OrderStartType startType;
    @Schema(description = "账号")
    private String accountNo;
    @Schema(description = "车架号", required = false)
    private String vin;
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    @Schema(description = "开始充电时间, unix时间戳", example = "*************")
    private Date startTime;
    @Schema(description = "剩余电量", required = false)
    private Integer soc;
    @Schema(description = "停止方式")
    private StopModeV2 stopMode;
    @Schema(description = "费用抵扣方式")
    private String calcType;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Insulation insulation;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDto bms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDto battery;

    @Data
    @Schema(description = "绝缘检测信息")
    public static class Insulation {

        @Schema(description = "绝缘检测结果 0正常 1故障 2告警")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer result;

        @Schema(description = "DC+绝缘检测值. 单位1Ω/V", example = "123")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer positive;

        @Schema(description = "DC-绝缘检测值. 单位1Ω/V", example = "123")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer negative;

        @Schema(description = "绝缘检测电压. 单位V")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private BigDecimal voltage;
    }


//    public String getCalcType() {
//        return calcType;
//    }
//
//    public OrderStartRequestV2 setCalcType(String calcType) {
//        this.calcType = calcType;
//        return this;
//    }
//
//    public OrderStartType getStartType() {
//        return startType;
//    }
//
//    public OrderStartRequestV2 setStartType(OrderStartType startType) {
//        this.startType = startType;
//        return this;
//    }
//
//    public String getAccountNo() {
//        return accountNo;
//    }
//
//    public OrderStartRequestV2 setAccountNo(String accountNo) {
//        this.accountNo = accountNo;
//        return this;
//    }
//
//    public String getVin() {
//        return vin;
//    }
//
//    public OrderStartRequestV2 setVin(String vin) {
//        this.vin = vin;
//        return this;
//    }
//
//    public Date getStartTime() {
//        return startTime;
//    }
//
//    public OrderStartRequestV2 setStartTime(Date startTime) {
//        this.startTime = startTime;
//        return this;
//    }
//
//    public Integer getSoc() {
//        return soc;
//    }
//
//    public OrderStartRequestV2 setSoc(Integer soc) {
//        this.soc = soc;
//        return this;
//    }
//
//    public StopModeV2 getStopMode() {
//        return stopMode;
//    }
//
//    public OrderStartRequestV2 setStopMode(StopModeV2 stopMode) {
//        this.stopMode = stopMode;
//        return this;
//    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
