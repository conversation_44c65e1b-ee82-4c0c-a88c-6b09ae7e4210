package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.meter.dto.Meter485Cfg;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * @Classname MqModifyMeterInfo
 * @Description
 * @Date 11/3/2021 10:20 AM
 * @Created by Rafael
 */
@Data
public class MqModifyMeterInfo {

    @Schema(description = "电表配置", example = "{}")
    private Meter485Cfg meter485Cfg;

    public static class REQ extends BaseMqttMsg<MqModifyMeterInfo> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.MODIFY_METER_INFO;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private MqModifyMeterInfo.REQ req;

        public builder(String gwno) {
            this.req = new MqModifyMeterInfo.REQ();
            this.req.setGwno(gwno);
            this.req.setData(new MqModifyMeterInfo());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public MqModifyMeterInfo.builder setCfg(Meter485Cfg cfg) {
            this.req.getData().setMeter485Cfg(cfg);
            return this;
        }

        public MqModifyMeterInfo.REQ build() {
            return this.req;
        }
    }

}
