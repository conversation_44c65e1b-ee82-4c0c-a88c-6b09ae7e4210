package com.cdz360.iot.model.parts.po;


import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "配件规格类型")

public class PartsTypePo {



	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "配件类型编码(物料编码)")

	@NotNull(message = "code 不能为 null")

	@Size(max = 32, message = "code 长度不能超过 32")

	private String code;



	@Schema(description = "配件名(大类别名)")

	@NotNull(message = "name 不能为 null")

	@Size(max = 64, message = "name 长度不能超过 64")

	private String name;



	@Schema(description = "规格型号(显示)")

	@NotNull(message = "fullModel 不能为 null")

	@Size(max = 128, message = "fullModel 长度不能超过 128")

	private String fullModel;





}

