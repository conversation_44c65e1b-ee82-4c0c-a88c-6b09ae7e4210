package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkJsCouponReqVo
 * @Description
 * @Date 6/18/2024 9:45 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统-捷顺3C类接口额外参数")
public class ParkJs3CReqVo<T> {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cid;// 商户号

    @JsonProperty(value = "v")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String version = "2";

    @JsonProperty(value = "p")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T params;

}