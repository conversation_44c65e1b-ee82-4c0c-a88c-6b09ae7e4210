package com.cdz360.iot.model.pv.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.pv.dto.huawei.ActiveAdjustmentInstruction;
import com.cdz360.iot.model.pv.dto.huawei.DeviceStatus;
import com.cdz360.iot.model.pv.dto.huawei.InverterStatus1;
import com.cdz360.iot.model.pv.dto.huawei.InverterStatus2;
import com.cdz360.iot.model.pv.dto.huawei.InverterStatus3;
import com.cdz360.iot.model.pv.dto.huawei.ReactiveAdjustmentInstruction;
import com.cdz360.iot.model.pv.dto.huawei.ReactiveAdjustmentMode;
import com.cdz360.iot.model.pv.type.PvMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "逆变器运行数据")
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class PvRtDataDto {

    @JsonProperty("id")
    @Schema(description = "设备modbus id", example = "123")
    private Integer deviceId;

    @JsonProperty("dno")
    @Schema(description = "设备编号", example = "ABC123")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @JsonProperty("sno")
    @Schema(description = "设备铭牌编号")
    @JsonInclude(Include.NON_EMPTY)
    private String serialNo;

    // 错误代码
    @JsonProperty("err")
    private List<Long> errorCodeList;

    @JsonProperty("ua")
    @Schema(description = "交流侧-A相电压, 单位: 1V", example = "123.4")
    private BigDecimal voltageA;

    @JsonProperty("ia")
    @Schema(description = "交流侧-A相电流, 单位: 1A", example = "123.4")
    private BigDecimal currentA;

    @JsonProperty("fa")
    @Schema(description = "交流侧-A相频率, 单位: 1Hz", example = "123.4")
    private BigDecimal frequencyA;

    @JsonProperty("ub")
    @Schema(description = "交流侧-B相电压, 单位: 1V", example = "123.4")
    private BigDecimal voltageB;

    @JsonProperty("ib")
    @Schema(description = "交流侧-B相电流, 单位: 1A", example = "123.4")
    private BigDecimal currentB;

    @JsonProperty("fb")
    @Schema(description = "交流侧-B相频率, 单位: 1Hz", example = "123.4")
    private BigDecimal frequencyB;

    @JsonProperty("uc")
    @Schema(description = "交流侧-C相电压, 单位: 1V", example = "123.4")
    private BigDecimal voltageC;

    @JsonProperty("ic")
    @Schema(description = "交流侧-C相电流, 单位: 1A", example = "123.4")
    private BigDecimal currentC;

    @JsonProperty("fc")
    @Schema(description = "交流侧-C相频率, 单位: 1Hz", example = "123.4")
    private BigDecimal frequencyC;

    @JsonProperty("uab")
    @Schema(description = "交流侧-AB线电压, 单位: 1V")
    private BigDecimal voltageAb;

    @JsonProperty("ubc")
    @Schema(description = "交流侧-BC线电压, 单位: 1V")
    private BigDecimal voltageBc;

    @JsonProperty("uca")
    @Schema(description = "交流侧-CA线电压, 单位: 1V")
    private BigDecimal voltageCa;

    @JsonProperty("p")
    @Schema(description = "交流侧-总有功功率, 单位: 1kW")
    private BigDecimal activePower;

    @JsonProperty("q")
    @Schema(description = "交流侧-总无功功率, 单位: 1kVar")
    private BigDecimal reactivePower;

    @JsonProperty("s")
    @Schema(description = "交流侧-总视在功率, 单位: 1kVA")
    private BigDecimal apparentPower;

    @JsonProperty("pf")
    @Schema(description = "交流侧-总功率因数, 单位: --")
    private BigDecimal powerFactor;

    @JsonProperty("f")
    @Schema(description = "交流侧-频率, 单位: 1Hz")
    private BigDecimal frequency;

    // @JsonProperty("p")
    // @Schema(description = "交流侧-有功功率, 单位: 1kW", example = "123.4")
    // @Deprecated(since = "用activePower代替")
    // private BigDecimal outPower;

    @JsonProperty("ip")
    @Schema(description = "直流侧-有功功率, 单位: 1kW")
    private BigDecimal inPower;

    @JsonProperty("iii")
    @Schema(description = "直流侧-绝缘阻抗, 单位: 1MΩ（1MΩ=1000000Ω）")
    private BigDecimal inInsulationImpedance;

    @JsonProperty("e")
    @Schema(description = "逆变器-转换效率, 单位: 1%")
    private BigDecimal efficiency;

    @JsonProperty("m")
    @Schema(description = "逆变器-运行状态")
    private PvMode rtMode;

    @JsonProperty("ak")
    @Schema(description = "逆变器-总发电量, 单位: 1KW·h", example = "123.4")
    private BigDecimal totalKwh;

    @JsonProperty("ah")
    @Schema(description = "逆变器-总发电时间, 单位: 1H", example = "567")
    private Long totalHour;

    @JsonProperty("tk")
    @Schema(description = "逆变器-当日发电量, 单位: 1kW·h", example = "123.4")
    private BigDecimal todayKwh;

    @JsonProperty("it")
    @Schema(description = "逆变器-内部温度, 单位: 1℃")
    private BigDecimal innerTemp;

    @JsonProperty("t")
    @Schema(description = "逆变器-散热片温度, 单位: 1℃", example = "23.4")
    private BigDecimal deviceTemp;

    @JsonProperty("rp")
    @Schema(description = "逆变器-额定功率（只读一次）, 单位: 1kW")
    private BigDecimal ratedPower;

    @JsonProperty("v1")
    @Schema(description = "PV组串1-电压, 单位: 1V", example = "123.4")
    private BigDecimal pv1Voltage;

    @JsonProperty("c1")
    @Schema(description = "PV组串1-电流, 单位: 1A", example = "123.4")
    private BigDecimal pv1Current;

    @JsonProperty("v2")
    @Schema(description = "PV组串2-电压, 单位: 1V", example = "123.4")
    private BigDecimal pv2Voltage;

    @JsonProperty("c2")
    @Schema(description = "PV组串2-电流, 单位: 1A", example = "123.4")
    private BigDecimal pv2Current;

      // RTC 时间
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @JsonProperty("ts")
//    private Date rtcTime;

    /**
     * 以下为华为逆变器相关字段 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
     */

    @JsonProperty("pno")
    @Schema(description = "设备零件号（只读一次）")
    @JsonInclude(Include.NON_EMPTY)
    private String partNo;

    @JsonProperty("dm")
    @Schema(description = "设备型号（只读一次）")
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;

    @JsonProperty("s1")
    @Schema(description = "状态1")
    private List<InverterStatus1> status1;

    @JsonProperty("s2")
    @Schema(description = "状态2")
    private InverterStatus2 status2;

    @JsonProperty("s3")
    @Schema(description = "状态3")
    private InverterStatus3 status3;

    @JsonProperty("ds")
    @Schema(description = "设备状态")
    private DeviceStatus deviceStatus;

    @JsonProperty("ftc")
    @Schema(description = "故障码")
    private Integer faultCode;

    @JsonProperty("pot")
    @Schema(description = "开机时间")
    private Long powerOnTime;

    @JsonProperty("pft")
    @Schema(description = "关机时间")
    private Long powerOffTime;

    @JsonProperty("st")
    @Schema(description = "系统时间")
    private Long systemTime;

    @JsonProperty("gn")
    @Schema(description = "组串个数（只读一次）")
    private Integer groupNum;

    @JsonProperty("mn")
    @Schema(description = "MPPT个数（只读一次）")
    private Integer mpptNum;

    @JsonProperty("v3")
    @Schema(description = "PV组串3-电压, 单位: 1A")
    private BigDecimal pv3Voltage;

    @JsonProperty("c3")
    @Schema(description = "PV组串3-电流, 单位: 1V")
    private BigDecimal pv3Current;

    @JsonProperty("atpm")
    @Schema(description = "最大有功, 单位: 1kW")
    private BigDecimal activePowerMax;

    @JsonProperty("apm")
    @Schema(description = "最大视在, 单位: 1kVA")
    private BigDecimal apparentPowerMax;

    @JsonProperty("orpm")
    @Schema(description = "最大无功（向电网馈入）, 单位: 1kVar")
    private BigDecimal outReactivePowerMax;

    @JsonProperty("irpm")
    @Schema(description = "最大无功（从电网吸收）, 单位: 1kVar")
    private BigDecimal inReactivePowerMax;

    @JsonProperty("gabv")
    @Schema(description = "电网AB线电压, 单位: 1V")
    private BigDecimal gridAbVoltage;

    @JsonProperty("gbcv")
    @Schema(description = "电网BC线电压, 单位: 1V")
    private BigDecimal gridBcVoltage;

    @JsonProperty("gcav")
    @Schema(description = "电网CA线电压, 单位: 1V")
    private BigDecimal gridCaVoltage;

    @JsonProperty("gv")
    @Schema(description = "电网A/B/C相电压, 单位: 1V")
    private GtiAbcItem gridVoltage;

    @JsonProperty("gc")
    @Schema(description = "电网A/B/C相电流, 单位: 1A")
    private GtiAbcItem gridCurrent;

    @JsonProperty("papt")
    @Schema(description = "当天峰值有功功率, 单位: 1kW")
    private BigDecimal peakActivePowerToday;

    @JsonProperty("gf")
    @Schema(description = "电网频率, 单位: 1Hz")
    private BigDecimal gridFrequency;

    @JsonProperty("aam")
    @Schema(description = "有功调节模式, 0：百分比；1：固定值；")
    private Integer activeAdjustmentMode;

    @JsonProperty("aav")
    @Schema(description = "有功调节值（随有功调节模式而变化）\n"
        + "百分比：1%\n"
        + "固定值：1kW")
    private BigDecimal activeAdjustmentValue;

    @JsonProperty("aai")
    @Schema(description = "有功调节指令")
    private ActiveAdjustmentInstruction activeAdjustmentInstruction;

    @JsonProperty("ram")
    @Schema(description = "无功调节模式")
    private ReactiveAdjustmentMode reactiveAdjustmentMode;

    @JsonProperty("rav")
    @Schema(description = "无功调节值（随无功调节模式而变化）\n"
        + "功率因数：0.001\n"
        + "绝对值：0.001kVar\n"
        + "Q/S：0.001\n"
        + "Q-U特征曲线：固定填0\n"
        + "cosϕ-P/Pn特征曲线：固定填0\n"
        + "PF-U特征曲线：固定填0\n"
        + "Q-P特征曲线：固定填0")
    private BigDecimal reactiveAdjustmentValue;

    @JsonProperty("rai")
    @Schema(description = "无功调节指令")
    private ReactiveAdjustmentInstruction reactiveAdjustmentInstruction;

    @JsonProperty("es")
    @Schema(description = "储能运行状态")
    private Integer essStatus;

    @JsonProperty("ecadp")
    @Schema(description = "储能充放电功率, 单位: 1W")
    private BigDecimal essChargeAndDischargePower;

    @JsonProperty("eckt")
    @Schema(description = "储能当日充电量, 单位: 1kWh")
    private BigDecimal essChargeKwhToday;

    @JsonProperty("edkt")
    @Schema(description = "储能当日放电量, 单位: 1kWh")
    private BigDecimal essDischargeKwhToday;

    @JsonProperty("map")
    @Schema(description = "电表有功功率, 单位: 1W")
    private BigDecimal meterActivePower;

    @JsonProperty("on")
    @Schema(description = "优化器个数")
    private Integer optimizerNum;

    @JsonProperty("oon")
    @Schema(description = "优化器在线个数")
    private Integer optimizerOnlineNum;

    /**
     * ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 以上为华为逆变器相关字段
     */

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class GtiAbcItem {

        private BigDecimal v1;// A 相电压/流

        private BigDecimal v2;// B 相电压/流

        private BigDecimal v3;// C 相电压/流
    }

}
