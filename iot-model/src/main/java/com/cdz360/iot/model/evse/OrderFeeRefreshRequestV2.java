package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 在线订单续费 V2
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderFeeRefreshRequestV2 extends BaseObject {
    @Schema(description = "唯一的订单号")
    private String orderNo;

    @Schema(description = "桩在云平台的唯一ID")
    private String evseNo;

    @Schema(description = "充电枪ID")
    private Integer plugId;

    @Schema(description = "金额抵扣时必传, 剩余金额, 单位'元'")
    private BigDecimal remainAmount;

    @Schema(description = "电量抵扣时必传, 剩余电量, 单位'kwh'")
    private BigDecimal remainPower;

//    @Schema(description = "已充电量, 单位'kwh'")
//    private BigDecimal kwh;
}
