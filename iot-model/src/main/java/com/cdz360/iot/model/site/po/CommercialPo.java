package com.cdz360.iot.model.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "商户信息")
public class CommercialPo {

    private Long id;

    private Long pid;

    private Integer commLevel;

    private Long topCommId;

    private String commName;

    private String shortName;

    private String idChain;

    private Boolean enable;
}
