package com.cdz360.iot.model.evse.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.po.PgPcItem;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备升级传输数据信息")
@Data
@Accessors(chain = true)
public class UpgradeTaskDto {

    @Schema(description = "设备编号", requiredMode = RequiredMode.REQUIRED)
    private String essDno;

    @Schema(description = "升级包ID", requiredMode = RequiredMode.REQUIRED)
    private Long upgradePgId;

    @Schema(description = "升级记录ID", requiredMode = RequiredMode.REQUIRED)
    private Long upgradeLogId;

    @Schema(description = "升级模块信息", requiredMode = RequiredMode.REQUIRED)
    private List<PgPcItem> pcItemList;

    @Schema(description = "获取升级包账号")
    private String fetchAcc;

    @Schema(description = "获取升级包密码")
    private String fetchPassw;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
