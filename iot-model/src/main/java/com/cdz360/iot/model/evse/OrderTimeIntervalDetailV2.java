package com.cdz360.iot.model.evse;

import com.cdz360.base.model.charge.dto.BatteryDynamicDto;
import com.cdz360.base.model.charge.dto.BmsDynamicDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderTimeIntervalDetailV2 {
    @Schema(description = "unix时间戳")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long timestamp;

    @Schema(description = "已充时长, 单位'分钟'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long duration;

    @Schema(description = "计剩余充电时间, 单位'分钟'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long remainingTime;

    @Schema(description = "消费电量, 单位'KWH'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;

    @Schema(description = "当前累计电费金额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "当前累计服务费金额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servFee;

    @Schema(description = "订单可用余额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal balance;

    //    @Schema(description = "电池温度, 单位'摄氏度'")
//    private Long batteryTemp;
    @Schema(description = "桩温度, 单位'摄氏度'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long evseTemp;

    @Schema(description = "枪温度, 单位'摄氏度'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long plugTemp;

    @Schema(description = "单体最高电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "单体最低电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minVoltage;

    @Schema(description = "当前实时电量. 百分比")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long soc;

    @Schema(description = "功率, 单位'KW'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal power;

    @Schema(description = "直流输出电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageO;

    @Schema(description = "直流输出电流, 单位'安'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcCurrentO;

    @Schema(description = "直流A相输入电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageA;

    @Schema(description = "直流B相输入电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageB;

    @Schema(description = "直流C相输入电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageC;

    @Schema(description = "交流A相电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageA;

    @Schema(description = "交流A相电流, 单位'安'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentA;

    @Schema(description = "交流B相电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageB;

    @Schema(description = "交流B相电流, 单位'安'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentB;

    @Schema(description = "交流C相电压, 单位'伏'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageC;

    @Schema(description = "交流C相电流, 单位'安'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentC;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDynamicDto bms;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDynamicDto battery;
}
