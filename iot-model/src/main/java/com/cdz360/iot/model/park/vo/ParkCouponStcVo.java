package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-苏停车")
public class ParkCouponStcVo {
    @JsonProperty(value = "appId")
    private String appId; // 苏停车分配给开发者的应用ID 2017122600085412

    @JsonProperty(value = "method")
    private String method; // 接口名称 coupon.send

    @JsonProperty(value = "sign")
    private String sign; // 签名数据

    @JsonProperty(value = "timestamp")
    private String timestamp; // 送请求的时间，格式"yyyy-MM-dd HH:mm:ss" 2017-12-26 08:35:36

    @JsonProperty(value = "parkId")
    private String parkId; // 停车场编号 000026

    @JsonProperty(value = "plateNumber")
    private String plateNumber; // 车牌号

    @JsonProperty(value = "couponType")
    private int couponType; // 优惠类型 2：时长免费

    @JsonProperty(value = "disHours")
    private int disHours; // 优惠小时数 2：两小时 3：三小时

    @JsonProperty(value = "startTime")
    private String startTime; // 优惠券有效开始时间，格式"yyyy-MM-dd HH:mm:ss" 2017-12-26 08:35:36

    @JsonProperty(value = "endTime")
    private String endTime; // 优惠券有效结束时间，格式"yyyy-MM-dd HH:mm:ss" 2017-12-26 08:35:36

//    code（返回码） Msg（返回描述）
//    000 成功
//    101 校验签名失败
//    102 权限不足
//    103 缺少必填参数
//    104 非法参数
//    105 业务处理失败
//    106 没有数据
    @Data
    public static class Response {
        @Schema(description = "返回码")
        private int code;

        @Schema(description = "描述")
        private String message;

        @Schema(description = "签名")
        private String sign;

        @Schema(description = "优惠券编号")
        private String data;
    }
}