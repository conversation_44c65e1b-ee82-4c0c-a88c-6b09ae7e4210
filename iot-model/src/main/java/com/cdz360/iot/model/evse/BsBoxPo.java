package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname BsBoxPo
 * @Description
 * @Date 6/10/2020 6:03 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class BsBoxPo {

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "场站编号")
    private String siteId;

    @Schema(description = "是否使用场站默认设置", example = "true")
    private Boolean useSiteSetting;

    @Schema(description = "桩额定功率", example = "123")
    private Integer power;

    @Schema(description = "价格模板编号", example = "123")
    private Long priceCode;


    private Date updateTime;
}
