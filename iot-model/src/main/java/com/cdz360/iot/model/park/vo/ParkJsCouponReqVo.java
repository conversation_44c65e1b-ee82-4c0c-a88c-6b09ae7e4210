package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkJsCouponReqVo
 * @Description
 * @Date 6/18/2024 9:49 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统-捷顺3C类接口参数")
public class ParkJsCouponReqVo {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String serviceId = "3c.order.discount";

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String requestType = "DATA";

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParkJsCouponCarReqVo attributes;
}