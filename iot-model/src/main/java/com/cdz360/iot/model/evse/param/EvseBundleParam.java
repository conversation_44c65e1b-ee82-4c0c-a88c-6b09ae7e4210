package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.type.BundleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "升级包参数")

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EvseBundleParam extends BaseListParam {

    @Schema(description = "是否分页查询")
    private Boolean pageFlag;

    @Schema(description = "升级包类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<BundleType> typeList;

    @Schema(description = "状态（0已停用；1正常；）")
    private List<Integer> statusList;

    private List<String> evseNoList;

    @Schema(description = "根据设备约束查询需要的升级包")
    private String restraintDno;

    @Schema(description = "关注升级包版本")
    private Integer protocol;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
