package com.cdz360.iot.model.meter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Classname MeterInfo
 * @Description
 * @Date 11/3/2021 10:16 AM
 * @Created by Rafael
 */
@Data
public class MeterInfo {
    //    @Schema(description = "modbus id", example = "3", required = true)
    private Integer id;

    private String dno;

    private String no;

    //    @Schema(description = "采样周期,5的倍数,单位秒", example = "60")
    @JsonProperty("st")
    private Integer samplingTime;
}
