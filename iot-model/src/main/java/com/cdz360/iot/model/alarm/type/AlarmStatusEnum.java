package com.cdz360.iot.model.alarm.type;

import lombok.Getter;

/**
 * 告警数据状态枚举值
 *
 * <AUTHOR>
 * @date Create on 2018/08/08 14:43
 */
@Getter
public enum AlarmStatusEnum {
    UNKONWN(999, "未知"),
    ALARM_STATUS_NOT_END(0, "未结束"),//未结束，进行中
    ALARM_STATUS_END_AUTO(1, "自动结束"),
    ALARM_STATUS_END_HAND(2, "手动结束");

    private int value;
    private String label;

    AlarmStatusEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

}
