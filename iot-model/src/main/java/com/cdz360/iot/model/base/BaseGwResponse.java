package com.cdz360.iot.model.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "响应消息")
public class BaseGwResponse extends BaseGwPackage {

    @Schema(description = "错误描述")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String error = null;
    @Schema(description = "状态码, 0表示成功, 其他都为错误码")
    private int status = 0;

    public BaseGwResponse() {
        super(IotPackageType.RES);
    }

    public BaseGwResponse(String seq) {
        super(IotPackageType.RES, seq);
    }

    public BaseGwResponse(int status, String seq, String error) {
        super(IotPackageType.RES, seq);
        this.status = status;
        this.error = error;
    }

    public static BaseGwResponse newInstance() {
        return new BaseGwResponse();
    }

    public int getStatus() {
        return status;
    }

    public BaseGwResponse setStatus(int status) {
        this.status = status;
        return this;
    }



    public String getError() {
        return error;
    }

    public BaseGwResponse setError(String error) {
        this.error = error;
        return this;
    }
}
