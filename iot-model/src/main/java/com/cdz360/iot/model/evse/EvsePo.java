package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.iot.type.ODMType;
import com.cdz360.iot.model.base.DbObject;
import com.cdz360.iot.model.evse.type.EvseBizType;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

//import com.cdz360.base.model.iot.type.NetType;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvsePo extends DbObject {

    //private String gwno;
    private String evseId;
    private String name;
    private Integer power; // 额定功率
    private EvseBizStatus bizStatus;
    private EvseStatus evseStatus = EvseStatus.UNKNOWN;
    private SupplyType supply = SupplyType.UNKNOWN;
    private NetType net = NetType.UNKNOWN;

    private DtuType dtuType;

    private EvseBizType bizType;

    private String ip;

    private String iccid;

    private String imsi;

    private String imei;
    /**
     * 桩型号
     */
    private String model;

    private Long modelId;

    // 铭牌编号
    private String physicalNo;

    private Integer plugNum;
    /**
     * 场站ID
     */
    private String siteId;
    /**
     * 场站对应的商户ID
     */
    private Long commId;

    /**
     * 价格模板ID
     */
    private Long priceCode;

    /**
     * 桩协议版本
     */
    private Integer protocolVer;
    /**
     * 桩固件(软件)版本
     */
    private String firmwareVer;

    /**
     * 枪口编号   0+桩号+0+枪口号
     */
    private String plugNo;

    /**
     * PC01版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc01Ver;
    /**
     * PC02版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc02Ver;
    /**
     * PC03版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc03Ver;

    private EvseProtocolType protocol;//桩协议类型 DC/CCTIA
    /**
     * 电桩型号, 如: G4-001 请用model,暂时没用到这个字段
     */
    private String modelName;

    /**
     * 额定电压,单位"V"
     */
    private BigDecimal voltage;

    /**
     * 额定电流,单位"A"
     */
    private BigDecimal current;

    @Schema(description = "是否支持插枪状态")
    private Boolean connSupport;

    @Schema(description = "当前最新的密钥版本号")
    private Long passcodeVer;
    @Schema(description = "是否开启debug")
    private Boolean debugTag;

    @Schema(description = "是否使用场站默认配置", example = "true")
    private Boolean useSiteCfg;

    @Schema(description = "最近一次升级状态 null表示没升级过")
    private UpdateTaskStatusEnum upgradeStatus;

    @Schema(description = "生产单号(出厂编号)")
    private String produceNo;

    @Schema(description = "生产日期(出厂日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    @Schema(description = "质保到期日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

    @Schema(description = "投运日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openForBusinessDate;

    @Schema(description = "户号")
    private String accountNumber;

    @Schema(description = "ODM供应商")
    private ODMType odm;
}
