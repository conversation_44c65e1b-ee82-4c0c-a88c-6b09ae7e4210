package com.cdz360.iot.model.register;

import com.cdz360.iot.model.base.BaseObject;

import java.util.List;

public class GwLoginResult extends BaseObject {

    private int hb = 5;

    private String gwno;

    private List<Integer> t;

    public int getHb() {
        return hb;
    }

    public GwLoginResult setHb(int hb) {
        this.hb = hb;
        return this;
    }

    public String getGwno() {
        return gwno;
    }

    public GwLoginResult setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public List<Integer> getT() {
        return t;
    }

    public GwLoginResult setT(List<Integer> t) {
        this.t = t;
        return this;
    }
}
