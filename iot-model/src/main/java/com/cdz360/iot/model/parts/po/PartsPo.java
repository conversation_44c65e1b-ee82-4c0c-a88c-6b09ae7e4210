package com.cdz360.iot.model.parts.po;

import com.cdz360.iot.model.parts.type.PartsLocationStatus;
import com.cdz360.iot.model.parts.type.PartsStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "配件")
public class PartsPo {

    @Schema(description = "配件唯一编码")
    @NotNull(message = "code 不能为 null")
    @Size(max = 16, message = "code 长度不能超过 16")
    private String code;

    @Schema(description = "配件状态:USABLE(可用);BROKEN(损坏);DISCARD(报废)")
    private PartsStatus status;

    @Schema(description = "流转状态:STORAGE(库存中);APPLY(申领中);TRANSFER(调拨中);"
        + "POST(邮寄中);OUT_STORAGE(已出库);ROLLBACK(回退中);"
        + "DISCARD(报废中-和可用状态中报废一样)")
    private PartsLocationStatus locationStatus;

    @Schema(description = "配件类型编码(t_parts_type.id)")
    @NotNull(message = "typeCode 不能为 null")
    private Long typeId;

    @Schema(description = "仓库编码(t_storage.code)")
    @Size(max = 32, message = "storeCode 长度不能超过 32")
    private String storeCode;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "创建人")
    private Long createBy;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @Schema(description = "申领人")
    private Long applyBy;

    @Schema(description = "备注信息")
    @Size(max = 128, message = "remark 长度不能超过 128")
    private String remark;
}

