package com.cdz360.iot.model.parts.po;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "快递单")

public class ExpressPo {



	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "快递单号")

	@NotNull(message = "expressNo 不能为 null")
	@Size(max = 64, message = "expressNo 长度不能超过 64")
	private String expressNo;

	@Schema(description = "快递名称")
	@JsonInclude(Include.NON_EMPTY)
	@Size(max = 64, message = "expressName 长度不能超过 64")
	private String expressName;

	@Schema(description = "发货地址")

	@NotNull(message = "fromAddress 不能为 null")

	@Size(max = 128, message = "fromAddress 长度不能超过 128")

	private String fromAddress;



	@Schema(description = "发货地址")

	@NotNull(message = "toAddress 不能为 null")

	@Size(max = 128, message = "toAddress 长度不能超过 128")

	private String toAddress;



	@Schema(description = "调拨单号(t_trans_order.orderNo)")

	@NotNull(message = "transOrderNo 不能为 null")

	@Size(max = 64, message = "transOrderNo 长度不能超过 64")

	private String transOrderNo;





}

