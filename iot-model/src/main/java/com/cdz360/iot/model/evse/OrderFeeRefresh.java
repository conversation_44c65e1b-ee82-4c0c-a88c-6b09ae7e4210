package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 *
 * @Author: <PERSON>
 * @Date: 2019/7/4 15:00
 */
public class OrderFeeRefresh extends BaseObject {
    @Schema(description = "账户总余额, 单位'分'")
    private int balance;

    @Schema(description = "增量余额, 单位'分'")
    private int amount;

    public int getBalance() {
        return balance;
    }

    public void setBalance(int balance) {
        this.balance = balance;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }
}
