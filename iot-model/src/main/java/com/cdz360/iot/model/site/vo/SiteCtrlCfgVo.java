package com.cdz360.iot.model.site.vo;

import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Schema(description = "场站配置模板")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class SiteCtrlCfgVo extends SiteCtrlCfgPo {
    @Schema(description = "控制器名")
    private String ctrlName;
}
