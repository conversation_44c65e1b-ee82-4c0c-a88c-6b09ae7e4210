package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListPlugParam extends BaseListParam{

    @Nullable
    private List<String> siteIdList;


    @Nullable
    private List<String> evseNoList;

    @Nullable
    private SupplyType supplyType;

    @Nullable
    @Schema(description = "桩运营状态")
    private List<EvseBizStatus> bizStatusList;

    @Nullable
    private List<PlugStatus> plugStatusList;

    private List<String> gidList;

    private String commIdChain;
    @Nullable
    @Schema(description = "枪头编号, 桩号+2位枪号")
    private List<String> plugNoList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站状态, 不传表示查询所有的. 0, 不可用; 1, 待上线; 2, 已上线; 3, 已隐藏; 4, 维护中")
    private List<Integer> statusList;
}
