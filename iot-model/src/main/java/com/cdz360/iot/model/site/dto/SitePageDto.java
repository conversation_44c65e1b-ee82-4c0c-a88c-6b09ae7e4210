package com.cdz360.iot.model.site.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

/**
 * @Classname SitePageDto
 * @Description TODO
 * @Date 2019/5/30
 * @Created by wangzheng
 */
@Schema(description = "场站信息（带总条数）")
public class SitePageDto {
    @Schema(description = "场站列表")
    private List<SiteDto> siteList;

    @Schema(description = "总条数")
    private Integer total;

    public List<SiteDto> getSiteList() {
        return siteList;
    }

    public SitePageDto setSiteList(List<SiteDto> siteList) {
        this.siteList = siteList;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public SitePageDto setTotal(Integer total) {
        this.total = total;
        return this;
    }
}