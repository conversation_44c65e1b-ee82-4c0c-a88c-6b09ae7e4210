package com.cdz360.iot.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EditEvseParam extends AddEvseParam {

    @Schema(description = "桩ID", example = "1")
    private Long id;

}
