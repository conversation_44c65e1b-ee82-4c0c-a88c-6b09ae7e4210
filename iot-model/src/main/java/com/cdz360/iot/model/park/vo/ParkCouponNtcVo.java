package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-宁停车")
public class ParkCouponNtcVo {
    @JsonProperty("appkey")
    private String appkey; // Appkey

    @JsonProperty("time")
    private String time; // 时间（当前时间戳，用于生成 sign）

    @JsonProperty("secret")
    private String secret; // 第三方账号（道闸停车方提供）

    @JsonProperty("parkCode")
    private String parkCode; // 停车场 ID（道闸停车方提供）

    @JsonProperty("orderNo")
    private String orderNo; // 订单号(充电桩订单号)

    @JsonProperty("plateNo")
    private String plateNo; // 车牌号(全车牌)，不允许为空

    @JsonProperty("startTime")
    private String startTime; // 开始时间，不允许为空 yyyy-MM-dd HH:mm:ss

    @JsonProperty("endTime")
    private String endTime; // 结束时间，不允许为空 yyyy-MM-dd HH:mm:ss

    @JsonProperty("stationId")
    private String stationId; // 电站 ID，停车场对接接口文档

    @JsonProperty("stationName")
    private String stationName; // 电站名称

    @JsonProperty("deviceNo")
    private String deviceNo; // 设备编号（唯一）

    @JsonProperty("deviceName")
    private String deviceName; // 设备名称

    @JsonProperty("spaceNo")
    private String spaceNo; // 车位号,停放位置没有的请传空

    @JsonProperty("power")
    private String power; // 充电电量单位：度，小数点后 2 位，没有的请传空

    @JsonProperty("elecMoney")
    private String elecMoney; // 电费单位：元，没有的请传空，支持两位小数

    @JsonProperty("seviceMoney")
    private String seviceMoney; // 服务费/附加费 单位：元，没有的请传空，支持两位小数

    @JsonProperty("totalMoney")
    private String totalMoney; // 总费用单位：元，没有的请传空，支持两位小数

    @JsonProperty("freeType")
    private String freeType; // 免费类型：1-免费时长 2-免费金额，当前默认请填 1，2 暂时不支持

    @JsonProperty("freeDuration")
    private String freeDuration; // 停车优惠时长，当 freeType 为 1 时不能为空 单位小时，保留小数点后一位，当前默认请填2.0

    @JsonProperty("freeAmount")
    private String freeAmount; // 停车优惠时长，当 freeType 为 2 时不能为空 单位元，保留小数点后两位

    @JsonProperty("ISP")
    private String ISP; // 充电桩企业名称

    @Data
    public static class Response {

        @Schema(description = "返回状态（0 下发优惠券成功，其他失败）")
        private int resCode;

        @Schema(description = "返回信息")
        private String resMsg;
    }
}