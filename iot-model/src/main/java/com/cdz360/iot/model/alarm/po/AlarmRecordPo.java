package com.cdz360.iot.model.alarm.po;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmRecordPo {

    /**
     * 告警记录id-告警报表中‘告警编号’
     */
    private Long warningId;
    /**
     * 控制器编码
     */
    private String ctrlNo;
    /**
     * 电桩型号, 如: G4-001
     */
    private String modelName;
    /**
     *     桩固件(软件)版本
     */
    private String firmwareVer;

    /**
     * 告警报表中‘上报设备/编号’
     * 为空时，取boxOutFactoryCode值
     */
    private String sourceNo;

    /**
     * 告警编码-告警报表中‘告警代码’
     */
    private String warningCode;
    /**
     * 告警名-告警报表中‘告警备注’
     */
    private String warningName;
    /**
     * 告警处理说明-告警报表中‘补充说明’
     */
    private String warningInstructions;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 桩名称
     */
    private String evseName;


    /**
     * 设备序列号/桩号-告警报表中‘告警对象/编号’
     */
    private String boxOutFactoryCode;

    /**
     * 充电接口(枪头)序号
     */
    private Integer connectorId;

    /**
     * 设备类型
     * EssEquipType
     */
    private Integer equipType;

    /**
     * 光储ESS设备ID
     */
    private Long  equipId;

    /**
     * 设备名称
     */
    private String essEquipName;

    /**
     * 站点id-告警报表中‘告警所属场站’
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 商户id
     */
    private String businessId;

    /**
     * 告警等级
     */
    private Integer level;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     * {@link com.cdz360.iot.monitor.constant.AlarmEventTypeEnum}
     * -告警报表中‘告警类型’
     */
    private Integer warningType;

    /**
     * 开始时间-告警报表中‘发生时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间-告警报表中‘结束时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(title = "告警开始时间", description = "设备本地时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lst;

    @Schema(title = "告警结束时间", description = "设备本地时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime let;

    @Schema(title = "时区", example = "+8")
    private String tz;

    /**
     * 告警最后上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warningUpdateTime;

    /**
     * 告警状态（0未结束   1自动结束   2手动结束){@link com.cdz360.iot.monitor.constant.AlarmStatusEnum}
     * -告警报表中‘告警状态’
     */
    private Integer status;

    /**
     * 持续时长单位秒（暂时由计算得出）
     */
    private Long duration;

    /**
     * 备注信息（用于存储说明信息）
     */
    private String remark;
    /**
     * 错误描述
     */
    private String error;
    /**
     * 温度 温度异常时有值
     */
    private Integer temp;

    /**
     * 设备最后心跳时间
     */
    private Long heartBeatTime;

    /**
     * 操作人id
     */
    private Long updateBy;

    private String gwno;

    private String appName;

    /**
     * 控制器负载率
     */
    private Integer loadRatio;

    /**
     * 配电柜温度
     */
    private Integer pwrTemp;

    /**
     * 将相关的控制器上报告警记录连接起来
     * 使用UUID生成
     * 留作备用
     */
    private String linkId;

    private String ctrlName;

    private String orderNo;

    private String plugName;

    /**
     * 异常问题编码
     */
    private Long errorCode;
    /**
     * 运行状态:未知(0);正常(1);异常(2);待机(3)
     */
    private Integer rtStatus;

    /**
     * 储能ESS唯一编号
     */
    private String dno;

    /**
     * 储能设备sn
     */
    private String sn;

    private String  ywOrderNo;

}
