package com.cdz360.iot.model.base;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

public class ListRpcResponse<T> extends BaseRpcResponse {
    private List<T> data;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long total;

    public ListRpcResponse() {
        this.data = new ArrayList<>();
    }

    public ListRpcResponse(List<T> data) {
        this.data = data;
    }

    public ListRpcResponse(List<T> data, Long total) {
        this.data = data;
        this.total = total;
    }

    public List<T> getData() {
        return data;
    }

    public ListRpcResponse<T> setData(List<T> data) {
        this.data = data;
        return this;
    }

    public Long getTotal() {
        return total;
    }

    public ListRpcResponse<T> setTotal(Long total) {
        this.total = total;
        return this;
    }
}
