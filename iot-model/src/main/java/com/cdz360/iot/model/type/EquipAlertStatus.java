package com.cdz360.iot.model.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EquipAlertStatus implements DcEnum {

    UNKNOWN(0, "未知"),
    OK(1, "正常"),
    ABNORMAL(2, "异常") // 异常
    ;


    @JsonValue
    private final int code;
    private final String desc;

    EquipAlertStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static EquipAlertStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return EquipAlertStatus.ABNORMAL;
        }
        int code = 0;
        if (codeIn instanceof EquipAlertStatus) {
            return (EquipAlertStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EquipAlertStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EquipAlertStatus.ABNORMAL;
    }
}
