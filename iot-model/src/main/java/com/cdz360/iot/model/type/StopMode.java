package com.cdz360.iot.model.type;

import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(name = "stopMode", description = "停充方式")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StopMode extends BaseObject {
    @Schema(description = "停止方式：FULL: 充满AMOUNT: 按金额充KWH: 按电量充 TIME: 按时间充")
    private StopModeType type;
    @Schema(description = "按金额充时的金额, 单位'元'")
    private BigDecimal amount;
    @Schema(description = "按电量充的电量, 单位'1KWH'")
    private BigDecimal kwh;
    @Schema(description = "按时间充的时间, 单位'分钟'")
    private Integer time;
}
