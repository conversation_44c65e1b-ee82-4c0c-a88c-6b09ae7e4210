package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseGwRequest;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OrderReportRequest extends BaseGwRequest {
    @Schema(description = "唯一的订单号")
    private String orderNo;
//    private GwOrderEvent event;
    @Schema(description = "桩在云平台的唯一ID")
    @Deprecated
    private String evseId;
    @Schema(description = "桩在云平台的唯一ID")
    private String evseNo;

    @Schema(description = "枪头编号")
    private String plugNo;
//    @Schema(description = "枪编号", required = true)
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "充电枪ID")
    private Integer plugId;

//
//    public String getEvseNo() {
//        return evseNo;
//    }
//
//    public OrderReportRequest setEvseNo(String evseNo) {
//        this.evseNo = evseNo;
//        return this;
//    }
//
//    public String getOrderNo() {
//        return orderNo;
//    }
//
//    public OrderReportRequest setOrderNo(String orderNo) {
//        this.orderNo = orderNo;
//        return this;
//    }
//
//
//
//    public String getEvseId() {
//        return evseId;
//    }
//
//    public OrderReportRequest setEvseId(String evseId) {
//        this.evseId = evseId;
//        return this;
//    }
//
//
//
//    public Integer getPlugId() {
//        return plugId;
//    }
//
//    public OrderReportRequest setPlugId(Integer plugId) {
//        this.plugId = plugId;
//        return this;
//    }

    public static class CustomDateDeserializer extends StdDeserializer<Date> {

        private final SimpleDateFormat formatter =
                new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");

        public CustomDateDeserializer() {
            this(null);
        }

        public CustomDateDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public Date deserialize(JsonParser jsonparser, DeserializationContext context)
                throws IOException {
            String unixTimestamp = jsonparser.getText();
            Long milliseconds = Long.valueOf(unixTimestamp + "000");
            Date date = new Date();
            date.setTime(milliseconds);
            return date;
        }
    }

    public static class CustomDateSerializer extends StdSerializer<Date> {

        public CustomDateSerializer() {
            this(null);
        }

        public CustomDateSerializer(Class<Date> t) {
            super(t);
        }

        @Override
        public void serialize(Date value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeNumber(value.getTime() / 1000);
        }
    }


}
