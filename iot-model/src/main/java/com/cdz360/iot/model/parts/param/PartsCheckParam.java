package com.cdz360.iot.model.parts.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PartsCheckParam {

    @Schema(description = "物料名称")
    @JsonInclude(Include.NON_EMPTY)
    private String typeName;

    @Schema(description = "物料规格型号")
    @JsonInclude(Include.NON_EMPTY)
    private String typeFullModel;

    @Schema(description = "物料编码")
    @JsonInclude(Include.NON_EMPTY)
    private String typeCode;

    @Schema(description = "库中是否存在 true: 存在; false: 不存在")
    @JsonInclude(Include.NON_NULL)
    private Boolean isSubsistent;

    @JsonInclude(Include.NON_NULL)
    private Long partsTypeId;

}
