package com.cdz360.iot.model.parts.vo;

import com.cdz360.iot.model.parts.po.PartsPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "物料信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsVo extends PartsPo {

    //    物料规格 👇
    @Schema(description = "物料规格编码")
    @JsonInclude(Include.NON_NULL)
    private String typeCode;

    @Schema(description = "物料规格名称")
    @JsonInclude(Include.NON_NULL)
    private String typeName;

    @Schema(description = "物料规格型号")
    @JsonInclude(Include.NON_EMPTY)
    private String typeFullModel;

    //    库存信息 👇
    @Schema(description = "仓库唯一编码")
    @JsonInclude(Include.NON_EMPTY)
    private String storageCode;

    @Schema(description = "仓库名称")
    @JsonInclude(Include.NON_EMPTY)
    private String storageName;

    @Schema(description = "仓库类型:PERSONAL(个人);NORMAL(普通)")
    @JsonInclude(Include.NON_EMPTY)
    private String storageType;

    @Schema(description = "个人仓关联的运维人员ID(普通仓置空)")
    @JsonInclude(Include.NON_EMPTY)
    private Long storageUid;
}
