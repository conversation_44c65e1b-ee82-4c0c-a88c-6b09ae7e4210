package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.ess.dto.EssCfgDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 修改储能ESS配置信息
 */
@Data
@Accessors(chain = true)
public class ModifyEssCfgReq {

    @Schema(description = "配置信息版本号", example = "123")
    @JsonProperty("cv")
    private Long cfgVer;

    @Schema(description = "是否全部替换")
    @JsonProperty("full")
    private Boolean full;

    private List<EssCfgDto> essCfgList;

    public static class REQ extends BaseMqttMsg<ModifyEssCfgReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.MODIFY_ESS_CFG;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {

        private REQ req;

        public builder() {
            this.req = new REQ();
            this.req.setData(new ModifyEssCfgReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setCfgVer(Long cfgVer) {
            this.req.getData().setCfgVer(cfgVer);
            return this;
        }

        public builder setFull(boolean full) {
            this.req.getData().setFull(full);
            return this;
        }

        public builder setEssCfgDtoList(List<EssCfgDto> essCfgDtoList) {
            req.getData().setEssCfgList(essCfgDtoList);
            return this;
        }

        public builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }
}
