package com.cdz360.iot.model.evse.type;

import lombok.Getter;

@Getter
public enum EvseDeviceType {

    JTGB("急停盖板"),
    GLW("过滤网"),
    GYZLCDMK("高压直流充电模块"),
    SKDLQ("塑壳断路器"),
    JLJCQ("交流接触器"),
    ZLJCQSC("直流接触器.输出"),
    ZLJCQGLFP("直流接触器.功率分配"),
    RDQ("熔断器"),
    SRFS("散热风扇"),
    FLQ("分流器"),
    DNB("电能表"),
    ZLCDQ("直流充电枪"),
    KGDYKZDY("开关电源.控制电源"),
    KGDYBMS24V("开关电源.BMS.24V"),
    KGDYBMS12V("开关电源.BMS.12V"),
    CMP("触摸屏"),
    DKQB("读卡器板"),
    MODULE4G("4G模块"),
    CCK("存储卡"),
    WLWK("物联网卡"),
    JYJCMK("绝缘检测模块"),
    AQGLB("安全管理板"),
    CDZKZB("充电桩控制板"),
    BILLING_UI_BOARD("计费UI板"),
    GLFPKZB("功率分配控制板"),
    CDKZMZ("充电控制模组"),
    ;

    private final String desc;

    EvseDeviceType(String desc) {
        this.desc = desc;
    }

}
