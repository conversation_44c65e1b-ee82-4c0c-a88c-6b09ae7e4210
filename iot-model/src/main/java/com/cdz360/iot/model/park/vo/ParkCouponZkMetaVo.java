package com.cdz360.iot.model.park.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkCouponZkMetaVo
 * @Description
 * @Date 12/24/2021 4:27 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-中控meta")
public class ParkCouponZkMetaVo {
    private String name;
    private Integer value;// 优惠券个数：1个为0.5小时
}