package com.cdz360.iot.model.dongzheng.request;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

public class BaseReq implements Serializable {

    private String seq;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String toJsonString() {
        String ret = "";
        ObjectMapper om = new ObjectMapper();
        try {
            ret = om.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            LoggerFactory.getLogger(BaseReq.class).trace(e.getMessage(), e);

        }
        return ret;
    }

    @Override
    public String toString() {
        return this.toJsonString();
    }
}
