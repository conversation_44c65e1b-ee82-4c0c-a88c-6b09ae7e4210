package com.cdz360.iot.model.site.po;

import com.cdz360.iot.model.order.type.SiteCtrlCfgStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname SiteCtrlCfgLogPo
 * @Description
 * @Date 4/23/2020 1:33 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "场站控制器配置记录")
public class SiteCtrlCfgLogPo {
    private Long id;
    private String ctrlNum;
    private SiteCtrlCfgStatusType status;
    private Integer triggerRst;
    private Integer pwrCtrlRst;
    private Integer infoUpRst;
    private Integer loadAlmRst;
    private Integer pwrTempRst;
    private Date createTime;
    private Date updateTime;
}