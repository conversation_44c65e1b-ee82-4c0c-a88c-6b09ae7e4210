package com.cdz360.iot.model.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

public abstract class DbObject extends BaseObject {

    private Long id;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String gwno;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据创建时间, UNIX时间戳", format = "java.lang.Long")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据最后更新时间, UNIX时间戳", format = "java.lang.Long")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public DbObject setId(Long id) {
        this.id = id;
        return this;
    }

    public String getGwno() {
        return gwno;
    }

    public DbObject setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public DbObject setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public DbObject setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}
