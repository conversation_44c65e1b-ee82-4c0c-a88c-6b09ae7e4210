package com.cdz360.iot.model.meter.po;

import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import com.cdz360.iot.model.pv.type.GtiVendor;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "电表")
public class MeterPo {

	@Schema(description = "主键id")
	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "电表唯一编号")
	@NotNull(message = "dno 不能为 null")
	@Size(max = 16, message = "dno 长度不能超过 16")
	private String dno;

	@Schema(description = "电表编号")
	@NotNull(message = "no 不能为 null")
	@Size(max = 20, message = "no 长度不能超过 20")
	private String no;

	@Schema(description = "场站ID")
	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "名称")
	@NotNull(message = "name 不能为 null")
	@Size(max = 100, message = "name 长度不能超过 100")
	private String name;

	@NotNull(message = "status 不能为 null")
	private MeterStatusType status;

	@Schema(description = "网络类型")
	private NetType net;

	@Schema(description = "品牌名称")
	private GtiVendor vendor;

	@Schema(description = "电流变比")
	private Integer ctr;

	@Schema(description = "电压变比")
	private Integer vtr;

	@Schema(description = "名称")
	@NotNull(message = "name 不能为 null")
	@Size(max = 100, message = "name 长度不能超过 100")
	private String area;

	@Schema(description = "是否计量其他大功率用电设备")
	@NotNull(message = "otherDevice 不能为 null")
	private Boolean otherDevice;

	@Schema(description = "注释")
	@NotNull(message = "comment 不能为 null")
	@Size(max = 255, message = "comment 长度不能超过 255")
	private String comment;

	@Schema(description = "最近活动时间")
	private Date lastActiveTime;

	@Schema(description = "网关编号")
	@Size(max = 32, message = "gwno 长度不能超过 32")
	private String gwno;

	@Schema(description = "通信协议")
	@Size(max = 100, message = "protocol 长度不能超过 100")
	private String protocol;

	@Schema(description = "串口通信(485/modbus) ID 1~247")
	private Integer sid;

	@Schema(description = "是否计算电损，1启用 0禁用")
	private Boolean powerLoss;

	@Schema(description = "记录创建时间")
	private Date createTime;

	@Schema(description = "记录最后修改时间")
	private Date updateTime;


}
