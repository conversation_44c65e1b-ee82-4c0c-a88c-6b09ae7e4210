package com.cdz360.iot.model.evse.cfg;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PriceSchema {
    private Integer code;//价格编码, 取值范围 0 ~ 255, 不能重复
    private Integer elecPrice;//电价, 单位为万分之一元每度. 如 12345 表示 1.2345元/度

    public Integer getCode() {
        return code;
    }

    public PriceSchema setCode(Integer code) {
        this.code = code;
        return this;
    }

}
