package com.cdz360.iot.model.dongzheng.response;

import com.cdz360.base.utils.JsonUtils;

import java.io.Serializable;

public class BoxInfoVo implements Serializable {

    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备序列号/桩号
     */
    private String serialNumber;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 设备名称
     */
    private String boxName;
    /**
     * 代理商ID
     */
    private String businessId;
    /**
     * 站点编号
     */
    private String siteId;
    /**
     * 设备状态{@link com.cdz360.iot.common.constant.DeviceStatusEnum}
     */
    private Integer status;
    /**
     * 有效枪头数量
     */
    private Integer validateConnectorCount;
    /**
     * 交直流类型**0-交流 1-直流 2-交直流**
     */
    private Integer currentType;
    /**
     * 所属产品系列名称
     */
    private String productName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 最后心跳时间**毫秒时间戳**
     */
    private Long lastHeartbeatTime;

    /**
     * 是否关联场站默认计费(0：是 1：否)
     */
    private Integer isAssociateSiteTemplate;

    /**
     * 计费模板Id
     */
    private Long templateId;

    /**
     * 计费模板名称
     */
    private String templateName;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getBoxName() {
        return boxName;
    }

    public void setBoxName(String boxName) {
        this.boxName = boxName;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getValidateConnectorCount() {
        return validateConnectorCount;
    }

    public void setValidateConnectorCount(Integer validateConnectorCount) {
        this.validateConnectorCount = validateConnectorCount;
    }

    public Integer getCurrentType() {
        return currentType;
    }

    public void setCurrentType(Integer currentType) {
        this.currentType = currentType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getLastHeartbeatTime() {
        return lastHeartbeatTime;
    }

    public void setLastHeartbeatTime(Long lastHeartbeatTime) {
        this.lastHeartbeatTime = lastHeartbeatTime;
    }

    public Integer getIsAssociateSiteTemplate() {
        return isAssociateSiteTemplate;
    }

    public void setIsAssociateSiteTemplate(Integer isAssociateSiteTemplate) {
        this.isAssociateSiteTemplate = isAssociateSiteTemplate;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
}
