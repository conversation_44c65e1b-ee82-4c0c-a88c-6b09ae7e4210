package com.cdz360.iot.model.evse.po;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.model.evse.type.EvseModelFlag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电桩型号")
public class EvseModelPo  {

    private Long id;

    @NotNull(message = "设备型号不能为空")
    @Schema(description = "型号名称")
    private String model;

    @NotNull(message = "品牌不能为空")
    @Schema(description = "品牌")
    private String brand;

    @NotNull(message = "系列不能为空")
    @Schema(description = "系列")
    private String series;

    @NotNull(message = "电桩类型不能为空")
    @Schema(description = "电桩类型")
    private SupplyType supply;

    @NotNull(message = "额定功率不能为空")
    @Schema(description = "额定功率")
    private Integer power;

    @NotNull(message = "枪头数量不能为空")
    @Schema(description = "枪头数量")
    private Integer plugNum;

    /**
     * {@link EvseModelFlag}
     */
    @Schema(description = "标志位")
    private List<Integer> flags;

    @Schema(description = "0：非分体机，1：250A终端单枪，2：250A终端双枪")
    private Integer splitFlag;

    @Schema(description = "状态")
    private Boolean status;

    private Boolean enable;

    public boolean isConnSupport() {
        if (CollectionUtils.isNotEmpty(this.getFlags())
                && this.getFlags().contains(EvseModelFlag.NONSUPPORT_CONNECT_STATUS.getCode())) {
            return false;
        }
        return true;
    }

    public boolean isConstantCharge() {
        if (CollectionUtils.isNotEmpty(this.getFlags())
                && this.getFlags().contains(EvseModelFlag.SUPPORT_CONSTANT_CHARGE.getCode())) {
            return true;
        }
        return false;
    }

}
