package com.cdz360.iot.model.parts.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "物料在运维工单上的使用")
@Data
@Accessors(chain = true)
public class PartsYwOrderRefParam {

    @Schema(description = "用户ID sys_user.id")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "用户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String userName;

    @Schema(description = "运维工单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

    @Schema(description = "工单操作使用物料")
    @JsonInclude(Include.NON_NULL)
    private List<AssociationOp> associationOps;

    @Schema(description = "物料在运维工单上操作")
    @Data
    @Accessors(chain = true)
    public static class AssociationOp {

        @Schema(description = "操作方式", required = true)
        @JsonInclude(Include.NON_NULL)
        private AssociationOpType opType;

        @Schema(description = "使用新的物料ID")
        @JsonInclude(Include.NON_EMPTY)
        private String newPartsCode;

        @Schema(description = "旧的物料规格名称 设备使用，可能没有")
        @JsonInclude(Include.NON_EMPTY)
        private String typeName;

        @Schema(description = "物料编码ID 旧物料ID不存在则提供物料规格编码ID")
        @JsonInclude(Include.NON_NULL)
        private Long typeId;

        @Schema(description = "是否为回滚操作")
        @JsonInclude(Include.NON_NULL)
        private Boolean rollback;

        @Schema(description = "物料入库返回最新的物料Id")
        @JsonInclude(Include.NON_NULL)
        private String inNewCode;


    }

    public static enum AssociationOpType {
        UNINSTALL("拆卸"), // 拆除
        INSTALL("加装"), // 装新
        REPLACE("更换"); // 更换

        private final String desc;

        AssociationOpType(String desc) {
            this.desc = desc;
        }
    }

    public static void checkField(PartsYwOrderRefParam param) {
        param.getAssociationOps().forEach(x -> {
            switch (x.getOpType()) {
                case INSTALL:
                    if (StringUtils.isBlank(x.getNewPartsCode())) {
                        throw new DcArgumentException("请提供加装的物料ID");
                    }
                    break;
                case UNINSTALL:
                    if (StringUtils.isBlank(x.getTypeName()) && null == x.getTypeId()) {
                        throw new DcArgumentException("请提供拆卸物料规格名称或物料规格编码ID");
                    }
                    break;
                case REPLACE:
                    if (StringUtils.isBlank(x.getNewPartsCode())) {
                        throw new DcArgumentException("请提供更换的新物料ID");
                    }

                    if (StringUtils.isBlank(x.getTypeName()) && null == x.getTypeId()) {
                        throw new DcArgumentException("请提供更换的旧物料规格名称或物料规格编码ID");
                    }
                    break;
            }
        });
    }
}
