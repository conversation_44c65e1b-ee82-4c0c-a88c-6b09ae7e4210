package com.cdz360.iot.model.param;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OfflineEvseParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "是否已存在库中")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isExist;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String model;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String brand;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer power;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugNum;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String iccid;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String msisdn;

    @Schema(description = "SIM是否已存在库中")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isSimExist;

    @Schema(description = "SIM是否已被绑定")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isSimBind;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long simId;

    @Schema(description = "校验设备型号+品牌+额定功率+枪头数量库中是否存在，匹配到多个时取最近那条数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isModelBrandUniqueKey;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modelId;

}