package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModbusLongTv extends ModbusNumberTv {
    @JsonIgnore
    public static List<Integer> TYPES = List.of(ModbusDataType.UINT32.getCode(),
        ModbusDataType.INT64.getCode(),
        ModbusDataType.UINT64.getCode());

    private long v;

}
