package com.cdz360.iot.model.evse;


import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseReportRequestV2 {
    private String evseNo;
    private EvseStatus evseStatus;//桩协议类型, DC/CCTIA

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SupplyType supply;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer temp;//桩温度

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer errorCode;//桩异常问题编码

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String error; //桩异常问题描述

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseReportRequestV2.Plug> plugs;//充电枪信息数组

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    public static class Plug extends BaseObject {
        private Integer plugId;
        private PlugStatus plugStatus;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer errorCode;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String error;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer alertCode;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer temp;

        @Schema(description = "充电订单号")
        private String orderNo;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Schema(description = "车辆VIN码, 无法获取时传 0x00")
        private String vin;
    }

    @JsonIgnore
    private String linkId;

    @JsonIgnore
    private String ctrlNo;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
