package com.cdz360.iot.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DeviceParam {

    private String evseNo;

    @Schema(description = "器件列表")
    private List<Device> list;

    @Data
    @Accessors(chain = true)
    public static class Device {
        @Schema(description = "器件名称")
        private String deviceName;

        @Schema(description = "槽位数量")
        private Integer number;

        @Schema(description = "型号")
        private String moduleType;

        private List<DeviceDetail> detailList;
    }

    @Data
    @Accessors(chain = true)
    public static class DeviceDetail {

        @Schema(description = "id")
        private Long id;

        @Schema(description = "序列")
        private Integer idx;

        @Schema(description = "现原材编号")
        private String deviceNo;

        @Schema(description = "录入时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date time;
    }
}
