package com.cdz360.iot.model.site.dto;


import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.GwStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "场站信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteDto extends SitePo {

    @Schema(description = "场站类型: 0, 全部; 1, 公共; 2, 个人; 3, 营运")
    private Integer dzType;


    @Schema(description = "所在城市名字")
    private String cityName;

    @Schema(description = "网关ID")
    private Long gwId;

    @Schema(description = "网关编号")
    private String gwno;

    @Schema(description = "网关状态")
    private GwStatus gwStatus;

    @Schema(description = "场站与网关绑定关系修改的时间")
    private Date gwSiteRefUpdateTime;

    @Schema(description = "图片列表，站点图片，多个图片以英文逗号,分隔")
    private String images;

    @Schema(description = "图片列表 List形式，站点图片")
    private List<String> imagesList;



}
