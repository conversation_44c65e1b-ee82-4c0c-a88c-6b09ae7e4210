package com.cdz360.iot.model.pv.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "通信模块")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PvComVo {

    @Schema(description = "品牌名称")
    private String vendor;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "编号")
    private String number;

}
