package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;

public class OrderTimeIntervalDetail {
    @Schema(description = "unix时间戳")
    private Long timestamp;
    @Schema(description = "已充时长, 单位'分钟'")
    private Long duration;
    @Schema(description = "计剩余充电时间, 单位'分钟'")
    private Long remainingTime;
    @Schema(description = "消费电量, 单位'0.01 KWH'")
    private Long kwh;
    @Schema(description = "当前累计电费金额, 单位'分'")
    private Long elecFee;
    @Schema(description = "当前累计服务费金额, 单位'分'")
    private Long servFee;
    @Schema(description = "订单可用余额, 单位'分'")
    private Long balance;
    @Schema(description = "电池温度, 单位'摄氏度'")
    private Long batteryTemp;
    @Schema(description = "桩温度, 单位'摄氏度'")
    private Long evseTemp;
    @Schema(description = "枪温度, 单位'摄氏度'")
    private Long plugTemp;
    @Schema(description = "单体最高电压, 单位'0.01伏'")
    private Long maxVoltage;
    @Schema(description = "单体最低电压, 单位'0.01伏'")
    private Long minVoltage;

    @Schema(description = "当前实时电量. 百分比")
    private Long soc;
    @Schema(description = "直流输出电压, 单位'0.01伏'")
    private Long dcVoltageO;
    @Schema(description = "直流输出电流, 单位'0.01安'")
    private Long dcCurrentO;
    @Schema(description = "直流A相输入电压, 单位'0.01伏'")
    private Long dcVoltageA;
    @Schema(description = "直流B相输入电压, 单位'0.01伏'")
    private Long dcVoltageB;
    @Schema(description = "直流C相输入电压, 单位'0.01伏'")
    private Long dcVoltageC;
    @Schema(description = "交流A相电压, 单位'0.01伏'")
    private Long acVoltageA;
    @Schema(description = "交流A相电流, 单位'0.01安'")
    private Long acCurrentA;
    @Schema(description = "交流B相电压, 单位'0.01伏'")
    private Long acVoltageB;
    @Schema(description = "交流B相电流, 单位'0.01安'")
    private Long acCurrentB;
    @Schema(description = "交流C相电压, 单位'0.01伏'")
    private Long acVoltageC;
    @Schema(description = "交流C相电流, 单位'0.01安'")
    private Long acCurrentC;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getRemainingTime() {
        return remainingTime;
    }

    public void setRemainingTime(Long remainingTime) {
        this.remainingTime = remainingTime;
    }

    public Long getKwh() {
        return kwh;
    }

    public void setKwh(Long kwh) {
        this.kwh = kwh;
    }

    public Long getElecFee() {
        return elecFee;
    }

    public void setElecFee(Long elecFee) {
        this.elecFee = elecFee;
    }

    public Long getServFee() {
        return servFee;
    }

    public void setServFee(Long servFee) {
        this.servFee = servFee;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Long getBatteryTemp() {
        return batteryTemp;
    }

    public void setBatteryTemp(Long batteryTemp) {
        this.batteryTemp = batteryTemp;
    }

    public Long getEvseTemp() {
        return evseTemp;
    }

    public void setEvseTemp(Long evseTemp) {
        this.evseTemp = evseTemp;
    }

    public Long getPlugTemp() {
        return plugTemp;
    }

    public void setPlugTemp(Long plugTemp) {
        this.plugTemp = plugTemp;
    }

    public Long getMaxVoltage() {
        return maxVoltage;
    }

    public void setMaxVoltage(Long maxVoltage) {
        this.maxVoltage = maxVoltage;
    }

    public Long getMinVoltage() {
        return minVoltage;
    }

    public void setMinVoltage(Long minVoltage) {
        this.minVoltage = minVoltage;
    }

    public Long getSoc() {
        return soc;
    }

    public void setSoc(Long soc) {
        this.soc = soc;
    }

    public Long getDcVoltageO() {
        return dcVoltageO;
    }

    public void setDcVoltageO(Long dcVoltageO) {
        this.dcVoltageO = dcVoltageO;
    }

    public Long getDcCurrentO() {
        return dcCurrentO;
    }

    public void setDcCurrentO(Long dcCurrentO) {
        this.dcCurrentO = dcCurrentO;
    }

    public Long getDcVoltageA() {
        return dcVoltageA;
    }

    public void setDcVoltageA(Long dcVoltageA) {
        this.dcVoltageA = dcVoltageA;
    }

    public Long getDcVoltageB() {
        return dcVoltageB;
    }

    public void setDcVoltageB(Long dcVoltageB) {
        this.dcVoltageB = dcVoltageB;
    }

    public Long getDcVoltageC() {
        return dcVoltageC;
    }

    public void setDcVoltageC(Long dcVoltageC) {
        this.dcVoltageC = dcVoltageC;
    }

    public Long getAcVoltageA() {
        return acVoltageA;
    }

    public void setAcVoltageA(Long acVoltageA) {
        this.acVoltageA = acVoltageA;
    }

    public Long getAcCurrentA() {
        return acCurrentA;
    }

    public void setAcCurrentA(Long acCurrentA) {
        this.acCurrentA = acCurrentA;
    }

    public Long getAcVoltageB() {
        return acVoltageB;
    }

    public void setAcVoltageB(Long acVoltageB) {
        this.acVoltageB = acVoltageB;
    }

    public Long getAcCurrentB() {
        return acCurrentB;
    }

    public void setAcCurrentB(Long acCurrentB) {
        this.acCurrentB = acCurrentB;
    }

    public Long getAcVoltageC() {
        return acVoltageC;
    }

    public void setAcVoltageC(Long acVoltageC) {
        this.acVoltageC = acVoltageC;
    }

    public Long getAcCurrentC() {
        return acCurrentC;
    }

    public void setAcCurrentC(Long acCurrentC) {
        this.acCurrentC = acCurrentC;
    }
}
