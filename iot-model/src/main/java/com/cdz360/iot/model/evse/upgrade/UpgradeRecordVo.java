package com.cdz360.iot.model.evse.upgrade;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "快速查桩-充电桩档案-桩升级记录")
public class UpgradeRecordVo {

    @Schema(description = "升级序号")
    private Long id;

    @Schema(description = "升级时间")
    private Date createTime;

    @Schema(description = "升级包编码")
    private Long bundleVersion;

    @Schema(description = "升级包名称")
    private String fileName;

    @Schema(description = "PC01版本号")
    private String pc01Ver;

    @Schema(description = "PC02版本号")
    private String pc02Ver;

    @Schema(description = "PC03版本号")
    private String pc03Ver;

    @Schema(description = "升级状态")
    private UpdateTaskStatusEnum status;

    @Schema(description = "操作人")
    private String opName;

    private String siteId;
    private String siteName;

}
