package com.cdz360.iot.model.ess.po;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.ess.vo.EquipNameplateInfo;
import com.cdz360.iot.model.net.vo.Rs485Cfg;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "储能ESS挂载设备信息")
public class EssEquipPo {

    @Schema(description = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "储能ESS唯一编号")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 16, message = "essDno 长度不能超过 16")
    private String essDno;

    @Schema(description = "设备唯一编号")
    @Size(max = 16, message = "dno 长度不能超过 16")
    private String dno;

    @Schema(description = "设备名称(根据ESS设备名称自动生成)")
    @Size(max = 32, message = "name 长度不能超过 32")
    private String name;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    private EquipStatus status;

    @Schema(description = "告警状态: 0,未知;1,正常;2,异常")
    private EquipAlertStatus alertStatus;

    @Schema(description = "设备ID(ess内唯一)")
    @NotNull(message = "equipId 不能为 null")
    private Long equipId;

    @Schema(description = "设备类型ID")
    private Integer equipTypeId;

    @Schema(description = "设备类型")
    @NotNull(message = "equipType 不能为 null")
    private EssEquipType equipType;

    @Schema(description = "设备型号")
    @Size(max = 16, message = "equipModel 长度不能超过 16")
    private String equipModel;

    @Schema(description = "铭牌信息")
    @JsonInclude(Include.NON_NULL)
    private EquipNameplateInfo nameplateInfo;

    @Schema(description = "品牌名称")
    @Size(max = 16, message = "vendor 长度不能超过 16")
    private String vendor;

    @Schema(description = "软件版本")
    @Size(max = 16, message = "swVer 长度不能超过 16")
    private String swVer;

    @Schema(description = "网络类型,ETHENET,S232,S485")
    private NetType netType;

    @Schema(description = "网络配置参数")
    private JsonNode netCfg;

    @Schema(description = "需要读取的Modbus协议的字段地址")
    private JsonNode modbusAddrCfg;

    @Schema(description = "Modbus协议字段解析配置")
    private JsonNode modbusTvCfg;

    @Schema(description = "日志设置")
    private JsonNode logCfg;

    @Schema(title = "自动校时", description = "true自动校时")
    private Boolean syncTime;

    @Schema(description = "设备名称(中文)")
    @NotNull(message = "equipNameCn 不能为 null")
    @Size(max = 64, message = "equipNameCn 长度不能超过 64")
    private String equipNameCn;

    @Schema(description = "设备名称(英文)")
    @NotNull(message = "equipNameEn 不能为 null")
    @Size(max = 64, message = "equipNameEn 长度不能超过 64")
    private String equipNameEn;

    @Schema(description = "是否有效")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

    @Schema(description = "记录创建时间")
    private Date createTime;

    @Schema(description = "记录最后修改时间")
    private Date updateTime;

    @JsonIgnore
    public Rs485Cfg getRs485Cfg() {
        if (NetType.S485 == this.netType) {
            return JsonUtils.fromJson(this.netCfg, Rs485Cfg.class);
        }
        return null;
    }

}

