package com.cdz360.iot.model.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "获取场站列表参数")
public class ListSiteParam extends BaseListParam {

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "经度")
    private Double lon;

    @Schema(description = "纬度")
    private Double lat;

    private Double delta = 0.0;

    public Double getDelta() {
        return delta;
    }

    public ListSiteParam setDelta(Double delta) {
        this.delta = delta;
        return this;
    }

    public String getCityCode() {
        return cityCode;
    }

    public ListSiteParam setCityCode(String cityCode) {
        this.cityCode = cityCode;
        return this;
    }

    public Double getLon() {
        return lon;
    }

    public ListSiteParam setLon(Double lon) {
        this.lon = lon;
        return this;
    }

    public Double getLat() {
        return lat;
    }

    public ListSiteParam setLat(Double lat) {
        this.lat = lat;
        return this;
    }
}
