package com.cdz360.iot.model.type;
//
//import com.cdz360.base.model.base.type.DcEnum;
//import com.fasterxml.jackson.annotation.JsonCreator;
//import com.fasterxml.jackson.annotation.JsonValue;
//import lombok.Getter;
//
//@Getter
//public enum EquipStatus implements DcEnum {
//
//    UNKNOWN(0, "未知"),
//    NORMAL(1, "正常"),
////    FAULT(2, "异常"),
////    WAIT(3, "待机"),
//    OFFLINE(2, "离线"),
//    OFF(99, "下线"),
//    ;
//
//    @JsonValue
//    private final int code;
//    private final String desc;
//
//    EquipStatus(int code, String desc) {
//        this.code = code;
//        this.desc = desc;
//    }
//
//    @JsonCreator
//    public static EquipStatus valueOf(Object codeIn) {
//        if (codeIn == null) {
//            return EquipStatus.UNKNOWN;
//        }
//        int code = 0;
//        if (codeIn instanceof EquipStatus) {
//            return (EquipStatus) codeIn;
//        } else if (codeIn instanceof Integer) {
//            code = ((Integer) codeIn).intValue();
//        } else if (codeIn instanceof Long) {
//            code = ((Long) codeIn).intValue();
//        } else if (codeIn instanceof String) {
//            code = Integer.parseInt((String) codeIn);
//        }
//        for (EquipStatus type : values()) {
//            if (type.code == code) {
//                return type;
//            }
//        }
//        return EquipStatus.UNKNOWN;
//    }
//}
