package com.cdz360.iot.model.evse.po;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.iot.model.base.DbObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlugPo extends DbObject {
    private String evseId;
    private int plugId;
    private String name;
    private PlugStatus plugStatus;
    private String orderNo;
    private List<Long> ids;
    private BigDecimal voltageMin;
    private BigDecimal voltageMax;
    private BigDecimal currentMin;
    private BigDecimal currentMax;
    private BigDecimal power;
    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 错误内容
     */
    private String errorMsg;

    /**
     * 告警码
     */
    private Integer alertCode;
}
