package com.cdz360.iot.model.evse.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.PlugDto;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EvseModelVo extends EvsePo {

    private String brand;

    private String series;

    private List<Integer> flags;

    private List<EvseModulePo> evseModulePoList;

    @Schema(description = "枪头信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PlugDto> plugList;

    private Long simId;

    @Schema(description = "SIM卡卡号, 20位数字")
    private String simIccid;

    @Schema(description = "移动台国际ISDN号码")
    private String simMsisdn;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
