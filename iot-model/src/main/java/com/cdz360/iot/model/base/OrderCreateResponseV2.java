package com.cdz360.iot.model.base;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.iot.model.type.StopMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "桩端发起充电反馈数据")
public class OrderCreateResponseV2 //extends BaseObject
{
    @Schema(description = "订单号. 长度不得超过16位")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;//订单号. 长度不得超过16位.

    @Schema(description = "费用抵扣方式  MONEY: 仅金额抵扣  POWER: 仅电量抵扣  BOTH: 金额抵扣服务费, 电量抵扣电费")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String calcType;//费用抵扣方式  MONEY: 仅金额抵扣  POWER: 仅电量抵扣  BOTH: 金额抵扣服务费, 电量抵扣电费

    @Schema(description = "账户总余额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;//账户总余额, 单位'元'

    @Schema(description = "可实时扣费金额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frozenAmount;//可实时扣费金额, 单位'元'

    @Schema(description = "电量账户总余额, 单位'kwh'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalPower;//电量账户总余额, 单位'kwh'

    @Schema(description = "当前订单, 电量账户冻结余额(可实时扣费部分), 单位'kwh'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frozenPower;//当前订单, 电量账户冻结余额(可实时扣费部分), 单位'kwh'

    @Schema(description = "停止方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private StopMode stopMode;//停止方式

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "soc限制")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer limitSoc;

    @Schema(description = "协议价优惠后的电价信息，下发到桩 鉴权用户使用协议价时存在")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargePriceVo priceVo;

}
