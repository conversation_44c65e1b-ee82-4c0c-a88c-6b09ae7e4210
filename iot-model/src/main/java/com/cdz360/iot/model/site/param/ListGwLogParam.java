package com.cdz360.iot.model.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "获取网关状态临时日志列表参数")
public class ListGwLogParam extends BaseListParam {
    @Schema(description = "时间")
    private Integer sec;

    public Integer getSec() {
        return sec;
    }

    public ListGwLogParam setSec(Integer sec) {
        this.sec = sec;
        return this;
    }
}
