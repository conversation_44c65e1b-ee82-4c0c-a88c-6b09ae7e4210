package com.cdz360.iot.model.type;

import com.cdz360.base.model.base.type.DcEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运营属性")
@Getter
public enum BizType implements DcEnum {

    UNKNOWN(0, "未知"),

    SELF(1, "自营"),

    NON_SELF(2, "非自营"),

    HLHT(3, "互联互通"),

    OFFLINE_SELF(4, "脱机自营"),

    OFFLINE_NON_SELF(5, "脱机非自营"),
    ;

    private final int code;
    private final String desc;

    BizType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BizType valueOf(int code) {
        for (BizType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BizType.UNKNOWN;
    }

    public static boolean isOffline(Integer code) {
        if (code == null) {
            return false;
        }
        return OFFLINE_SELF.getCode() == code || OFFLINE_NON_SELF.getCode() == code;
    }

}