package com.cdz360.iot.model.park.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取地锁列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListParkingLockParam extends BaseListParam {

    @Schema(description = "场站ID 精确匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "地锁ID(供应商)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serialNumber;

    @Schema(description = "地锁状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParkingLockStatus status;

    @Schema(description = "地锁状态列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ParkingLockStatus> statusList;

    @Schema(description = "时间范围")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter date;
}
