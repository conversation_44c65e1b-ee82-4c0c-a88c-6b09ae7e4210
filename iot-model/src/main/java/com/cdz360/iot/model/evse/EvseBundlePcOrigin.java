package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * @ClassName： EvseBundlePcOrigin
 * @Description: 升级包支持的源版本信息
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:44
 */
@Schema(description = "com.cdz360.iot.model.evse.EvseBundlePcOrigin")
@Data
@Accessors(chain = true)
public class EvseBundlePcOrigin implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 关联的包id.  t_evse_bundle.id
     */
    @Schema(description = "关联的包id.t_evse_bundle.id")
    private Long bundleId;
    /**
     * 关联的包id. t_evse_bundle_pc.id
     */
    @Schema(description = "关联的包id.t_evse_bundle_pc.id")
    private Long pcId;
    /**
     * 模块类型,如PC01,PC02,PC01-1
     */
    @Schema(description = "模块类型,如PC01,PC02,PC01-1")
    private String pcName;
    /**
     * 源版本信息, 格式为: 硬件版本号-软件版本号-定制号
     */
    @Schema(description = "源版本信息, 格式为: 硬件版本号-软件版本号-定制号")
    private String origin;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}