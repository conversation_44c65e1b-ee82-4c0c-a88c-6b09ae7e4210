package com.cdz360.iot.model.evse.dto;

import com.cdz360.iot.model.evse.EvseBundle;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * @ClassName： EvseBundleDto
 * @Description: 桩升级包数据传输DTO
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:13
 */
@Schema(description = "com.cdz360.iot.model.evse.dto.EvseBundleDto")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EvseBundleDto extends EvseBundle {
    private static final long serialVersionUID = 1L;

    private List<PC0XDto> pc0XList;
}