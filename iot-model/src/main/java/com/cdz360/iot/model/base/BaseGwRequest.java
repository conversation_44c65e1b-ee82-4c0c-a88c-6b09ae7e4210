package com.cdz360.iot.model.base;

import com.cdz360.iot.model.type.GwRequestMethod;

public class BaseGwRequest extends BaseGwPackage {




    private GwRequestMethod method;
    private String gwno;

    public BaseGwRequest() {
        super(IotPackageType.REQ);
    }

    public BaseGwRequest(String seq) {
        super(IotPackageType.REQ, seq);
    }

    public BaseGwRequest(GwRequestMethod method, String seq) {
        super(IotPackageType.REQ, seq);
        this.method = method;
    }

    public BaseGwRequest(GwRequestMethod method) {
        super(IotPackageType.REQ);
        this.method = method;
    }

    public String getGwno() {
        return gwno;
    }

    public BaseGwRequest setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public GwRequestMethod getMethod() {
        return method;
    }

    public BaseGwRequest setMethod(GwRequestMethod method) {
        this.method = method;
        return this;
    }
}
