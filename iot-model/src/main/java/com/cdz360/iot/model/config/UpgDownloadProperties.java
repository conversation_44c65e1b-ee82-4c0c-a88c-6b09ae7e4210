package com.cdz360.iot.model.config;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.evse.EvseBundleRemoteFileConf;
import com.cdz360.iot.model.evse.type.EvseVendor;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * @ProjectName iotServer
 * <AUTHOR>
 * @CreateDate 2019/9/20 9:18
 */
@Data
@Component
@Slf4j
@ConfigurationProperties(prefix = "upgrade.download")
public class UpgDownloadProperties {

    private String schema;
    private String host;
    private String port;
    private String basepath;
    private List<Vendor> vendors;

    /**
     * @Description: 获取文件下载路径
     * @Param filePath 文件路径
     * @Author: JLei
     * @CreateDate: 16:13 2019/10/11
     */
    public String getDownloadPath(String filePath) {
        if (filePath == null) {
            filePath = "";
        }
        filePath = "/".concat(filePath);
        URL url = null;
        try {
            url = StringUtils.isBlank(this.port) ? new URL(this.schema, this.host, filePath)
                : new URL(this.schema, this.host, Integer.parseInt(this.port), filePath);
        } catch (MalformedURLException e) {
            log.error("获取文件下载路径错误。errorMsg = {}", e.getMessage());
            throw new DcServiceException("获取文件下载路径错误");
        }
        return url.toString();
    }

    public String getDownloadPath(EvseBundleRemoteFileConf conf, String filePath) {
        try {
            return (new URL(conf.getProtocol(), conf.getHost(), conf.getPort(), filePath)).toString();
        } catch (MalformedURLException e) {
            log.error("获取远程文件下载路径错误。errorMsg = {}", e.getMessage());
            throw new DcServiceException("获取远程文件下载路径错误");
        }
    }

    public String getUsername() {
        return getUsername(null);
    }

    public String getUsername(@Nullable EvseVendor req) {
        Vendor temp = filterVendor(req);
        return temp.getUsername();
    }

    public String getPassword() {
        return getPassword(null);
    }

    public String getPassword(@Nullable EvseVendor req) {
        Vendor temp = filterVendor(req);
        return temp.getPassword();
    }

    public String getBasepath() {
        return getBasepath(null);
    }

    public String getBasepath(@Nullable EvseVendor req) {
        Vendor temp = filterVendor(req);
        return basepath + temp.getPath();
    }

    private Vendor filterVendor(@Nullable EvseVendor req) {
        Assert.isTrue(CollectionUtils.isNotEmpty(vendors), "桩升级信息未配置");
        EvseVendor vendor = req != null ? req : EvseVendor.TOPOWER;
        AtomicReference<Vendor> strRef = new AtomicReference<>();
        vendors.stream().filter(e -> vendor.name().equals(e.getName()))
            .findFirst()
            .ifPresentOrElse(strRef::set, () -> {
                throw new DcServiceException("桩升级信息未配置");
            });
        return strRef.get();
    }

    @Data
    public static class Vendor {

        private String name;
        private String username;
        private String password;
        private String path;

    }

}
