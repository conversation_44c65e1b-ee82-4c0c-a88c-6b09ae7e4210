package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * ModbusDataType.U_SHORT(3), ModbusDataType.INTEGER(4)
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModbusIntegerTv extends ModbusNumberTv {

    @JsonIgnore
    public static List<Integer> TYPES = List.of(
        ModbusDataType.UINT16.getCode());

    private int v;

}
