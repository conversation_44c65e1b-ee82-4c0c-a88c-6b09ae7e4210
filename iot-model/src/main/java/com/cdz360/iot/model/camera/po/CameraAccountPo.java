package com.cdz360.iot.model.camera.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "视频接入账号信息")
public class CameraAccountPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "商户id")
	private Long commId;

	@Schema(description = "帐户类型：1萤石云，2云眸")
	private Integer type;

	@Schema(description = "客户端ID")
	@Size(max = 64, message = "clientId 长度不能超过 64")
	private String clientId;

	@Schema(description = "访问密钥")
	@Size(max = 64, message = "clientSecret 长度不能超过 64")
	private String clientSecret;

	@Schema(description = "认证模式")
	@Size(max = 64, message = "grantType 长度不能超过 64")
	private String grantType;

	@Schema(description = "权限范围")
	@Size(max = 64, message = "scope 长度不能超过 64")
	private String scope;

	@Schema(description = "访问令牌")
	@Size(max = 64, message = "accessToken 长度不能超过 64")
	private String accessToken;

	@Schema(description = "令牌类型")
	@Size(max = 64, message = "tokenType 长度不能超过 64")
	private String tokenType;

	@Schema(description = "过期时间")
	private Date expiresIn;

	@Schema(description = "取流认证信息,有效期为7天")
	@Size(max = 64, message = "accountToken 长度不能超过 64")
	private String accountToken;

	@Schema(description = "取流认证信息")
	@Size(max = 64, message = "accountAppKey 长度不能超过 64")
	private String accountAppKey;

	@Schema(description = "取流认证过期时间")
	private Date accountExpireTime;

	@Schema(description = "1有效 0无效")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
