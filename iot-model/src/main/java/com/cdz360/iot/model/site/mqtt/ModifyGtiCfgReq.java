package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.pv.dto.PvCfgDto;
import com.cdz360.iot.model.srs.dto.SrsCfgDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 修改光伏逆变器配置信息
 */
@Data
@Accessors(chain = true)
public class ModifyGtiCfgReq {

    @Schema(description = "配置信息版本号", example = "123")
    @JsonProperty("cv")
    private Long cfgVer;

    @Schema(description = "是否全部替换")
    @JsonProperty("full")
    private Boolean full;

    private List<PvCfgDto> pvCfgList;

    @Schema(description = "辐射仪配置")
    private SrsCfgDto srsCfg;

    public static class REQ extends BaseMqttMsg<ModifyGtiCfgReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.MODIFY_GTI_CFG;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private REQ req;

        public builder() {
            this.req = new REQ();
            this.req.setData(new ModifyGtiCfgReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setCfgVer(Long cfgVer) {
            this.req.getData().setCfgVer(cfgVer);
            return this;
        }

        public builder setFull(boolean full) {
            this.req.getData().setFull(full);
            return this;
        }

        public builder setPvCfgList(List<PvCfgDto> pvCfgList) {
            req.getData().setPvCfgList(pvCfgList);
            return this;
        }

        public builder setSrsCfg(SrsCfgDto srsCfg) {
            req.getData().setSrsCfg(srsCfg);
            return this;
        }

        public builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }
}
