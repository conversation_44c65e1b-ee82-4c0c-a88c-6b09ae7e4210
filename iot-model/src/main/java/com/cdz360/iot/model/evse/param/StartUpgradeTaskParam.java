package com.cdz360.iot.model.evse.param;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备选择升级包升级")
@Data
@Accessors(chain = true)
public class StartUpgradeTaskParam {

    @Schema(description = "设备编号", requiredMode = RequiredMode.REQUIRED)
    private String essDno;

    @Schema(description = "升级包ID", requiredMode = RequiredMode.REQUIRED)
    private Long upgradePgId;

    @Schema(description = "操作人ID", hidden = true)
    private Long opId;
    @Schema(description = "操作人姓名", hidden = true)
    private String opName;

//    @Schema(description = "升级模块信息", hidden = true)
//    private List<PgPcItem> pcItemList;
//
//    @Schema(description = "获取升级包账号", hidden = true)
//    private String fetchAcc;
//
//    @Schema(description = "获取升级包密码", hidden = true)
//    private String fetchPassw;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
