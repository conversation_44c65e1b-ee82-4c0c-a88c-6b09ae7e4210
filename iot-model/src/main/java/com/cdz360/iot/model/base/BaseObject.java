package com.cdz360.iot.model.base;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.LoggerFactory;

public class BaseObject  {

    public String toJsonString() {
        String ret = "";
        ObjectMapper om = new ObjectMapper();
        try {
            ret = om.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            LoggerFactory.getLogger(BaseObject.class).trace(e.getMessage(), e);

        }
        return ret;
    }

    @Override
    public String toString() {
        return this.toJsonString();
    }
}
