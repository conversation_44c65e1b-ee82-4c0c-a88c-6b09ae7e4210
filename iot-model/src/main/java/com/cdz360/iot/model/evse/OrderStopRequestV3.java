package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderStopRequestV3 extends OrderStopRequestV2 {

    // 充电完成原因
    private Long completeCode;

    // 订单总电量: 单位，0.0001kwh，合充时为主枪的总电量
    private BigDecimal kwh;

    // 合充的辅枪枪号
    private Integer secondPlugIdx;

    // 辅枪充电前电表读数
    private BigDecimal secondStartMeter;

    // 辅枪充电后电表读数
    private BigDecimal secondStopMeter;

    // 辅枪订单总电量
    private BigDecimal secondKwh;

    // 电池单体最低电压
    private BigDecimal minBatteryVoltage;

    @Schema(description = "是否需要使用协议价", required = false)
    private Boolean isNeedDiscount;

}
