package com.cdz360.iot.model.evse.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "桩配置信息")
@Data
@Accessors(chain = true)
public class EvseCfgPo {

    private Long id;

    @Schema(description = "桩号")
    private String evseNo;

    @Schema(description = "管理员密码")
    private String adminPassword;


    @Schema(description = "二级管理员密码")
    private String level2Password;


    @Schema(description = "白天音量", example = "5")
    private Integer dayVolume;


    @Schema(description = "夜晚音量", example = "5")
    private Integer nightVolume;

    @Schema(description = "二维码url")
    private String qrUrl;

    @Schema(description = "紧急充电卡逻辑卡号列表，用,分隔")
    private String whiteCardList;

    @Schema(description = "国际协议")
    private String internationalAgreement;

    @Schema(description = "辅电电压设置")
    private Integer heatingVoltage;

    @Schema(description = "是否支持辅电手动切换")
    private Boolean heating;

    @Schema(description = "均/轮充设置 0均充 1轮充")
    private Integer avgOrTurnCharge;

    @Schema(description = "是否支持电池反接检测")
    private Boolean batteryCheck;

    @Schema(description = "是否支持主动安全检测")
    private Boolean securityCheck;

    @Schema(description = "是否支持插枪获取VIN")
    private Boolean vinDiscover;

    @Schema(description = "订单账号显示类型")
    private Integer accountDisplayType;

    @Schema(description = "合充开关 （1开0关）")
    private Integer isCombineCharge;

    @Schema(description = "是否支持充电记录查询", example = "true")
    private Boolean isQueryChargeRecord;

    @Schema(description = "是否支持不拔枪充电", example = "true")
    private Boolean constantCharge;

    @Schema(description = "是否可见订单信息隐私", example = "true")
    private Boolean orderPrivacySetting;

    @Schema(description = "是否支持定时充电", example = "true")
    private Boolean isTimedCharge;

    @Schema(description = "是否支持无卡充电", example = "true")
    private Boolean isNoCardCharge;

    @Schema(description = "是否支持扫码充电", example = "true")
    private Boolean isScanCharge;

    @Schema(description = "是否支持Vin码充电", example = "true")
    private Boolean isVinCharge;

    @Schema(description = "是否支持刷卡充电", example = "true")
    private Boolean isCardCharge;


    @Schema(description = "是否支持定额电量充电", example = "true")
    private Boolean isQuotaEleCharge;

    @Schema(description = "是否支持固定金额充电", example = "true")
    private Boolean isQuotaMoneyCharge;

    @Schema(description = "是否支持固定时长充电", example = "true")
    private Boolean isQuotaTimeCharge;

    @Schema(description = "操作人id", example = "123")
    private Long opUid;

    @Schema(description = "是否有效", example = "true")
    private Boolean enable;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


}
