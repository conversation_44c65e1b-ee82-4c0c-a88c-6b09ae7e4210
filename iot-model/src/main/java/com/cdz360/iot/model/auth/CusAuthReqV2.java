package com.cdz360.iot.model.auth;

import com.cdz360.iot.model.base.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CusAuthReqV2 extends BaseObject {
    private String evseNo;
    private String plugId;
    private String vin;
    private Integer authType;//鉴权类型:0x11: 在线卡, 0x12: 车架号
    private String accountNo;//账号, 此处为卡号 (逻辑卡号) 或 17位 VIN 码
}
