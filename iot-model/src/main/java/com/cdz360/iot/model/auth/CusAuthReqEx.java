package com.cdz360.iot.model.auth;

import com.cdz360.base.model.base.type.EvseProtocolType;

/**
 * @Classname CusAuthReqEx
 * @Description 增加了gwid，为了把网关编号传给work，以实现[账号+网关号+桩号+枪号]为key
 * @Date 2019/5/31 16:18
 * @Created by Rafael
 */
public class CusAuthReqEx extends CusAuthReq {
    private String gwno;
    private EvseProtocolType evseProtocolType;

    public String getGwno() {
        return gwno;
    }

    public CusAuthReqEx setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    @Override
    public EvseProtocolType getEvseProtocolType() {
        return evseProtocolType;
    }

    @Override
    public void setEvseProtocolType(EvseProtocolType evseProtocolType) {
        this.evseProtocolType = evseProtocolType;
    }
}