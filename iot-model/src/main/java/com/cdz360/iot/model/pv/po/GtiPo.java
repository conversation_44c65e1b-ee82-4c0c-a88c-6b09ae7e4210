package com.cdz360.iot.model.pv.po;

import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.PvComVo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "光伏逆变器信息(grid-tie")
public class GtiPo {

    @Schema(description = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "逆变器唯一编号")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 16, message = "dno 长度不能超过 16")
    private String dno;

    @Schema(description = "设备名称")
    @Size(max = 32, message = "name 长度不能超过 32")
    private String name;

    @Schema(description = "串口通信(485/modbus) ID")
    @NotNull(message = "sid 不能为 null")
    private Integer sid;

    @Schema(description = "设备铭牌编号")
    @NotNull(message = "serialNo 不能为 null")
    @Size(max = 32, message = "serialNo 长度不能超过 32")
    private String serialNo;

    @Schema(description = "品牌名称")
    @NotNull(message = "vendor 不能为 null")
    @Size(max = 16, message = "vendor 长度不能超过 16")
    private GtiVendor vendor;

    @Schema(description = "通信模块")
    private PvComVo com;

    @Schema(description = "额定功率")
    private Long power;

    @Schema(description = "输出电压")
    private BigDecimal outputVoltage;

    @Schema(description = "最大输出电流")
    private BigDecimal outputCurrent;

    @Schema(description = "最大视在功率")
    private BigDecimal apparentPower;

    @Schema(description = "网关编号")
    @NotNull(message = "gwno 不能为 null")
    @Size(max = 16, message = "gwno 长度不能超过 16")
    private String gwno;

    @Schema(description = "场站ID")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "设备型号")
    @NotNull(message = "deviceModel 不能为 null")
    @Size(max = 32, message = "deviceModel 长度不能超过 32")
    private String deviceModel;

    @Schema(description = "设备零件号")
    @Size(max = 32, message = "partNo长度不能超过 32")
    private String partNo;

    @Schema(description = "组串个数")
    private Integer groupNum;

    @Schema(description = "MPPT个数")
    private Integer mpptNum;

    @Schema(description = "MPPT电压min")
    private BigDecimal mpptVoltageMin;

    @Schema(description = "MPPT电压max")
    private BigDecimal mpptVoltageMax;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @NotNull(message = "status 不能为 null")
    private EquipStatus status;

    @Schema(description = "告警状态: 0,未知;1,正常;2,异常")
    private EquipAlertStatus alertStatus;


    @Schema(description = "储能设备ID(t_ess_equip.id) 数据通过储能透传上来")
    private Long essEquipId;

    @Schema(description = "当前配置模板ID")
    @NotNull(message = "cfgId 不能为 null")
    private Long cfgId;

    @Schema(description = "配置信息状态: 0,未知;1,已下发;2,下发中;3,下发失败")
    @NotNull(message = "cfgStatus 不能为 null")
    private EquipCfgStatus cfgStatus;

    @Schema(description = "上次成功下发配置模板ID")
    @NotNull(message = "cfgSuccessId 不能为 null")
    private Long cfgSuccessId;

    @Schema(description = "最后一次下发成功时间")
    private Date cfgSuccessTime;

    @Schema(description = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "记录最后修改时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

}

