package com.cdz360.iot.model.alarm.dto;

import com.cdz360.base.model.es.type.EquipWarnLevel;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class EssEquipAlarmLangDto {

    @Schema(description = "设备类型 EssEquipType, 如 PCS BMS")
    @NotNull(message = "equipType 不能为 null")
    @Size(max = 16, message = "equipType 长度不能超过 16")
    private String equipType;


    @Schema(description = "语言,使用缩写如cn,en")
    @NotNull(message = "lang 不能为 null")
    @Size(max = 6, message = "lang 长度不能超过 6")
    private String lang;


    @Schema(description = "字段编码")
    @NotNull(message = "code 不能为 null")
    @Size(max = 64, message = "code 长度不能超过 64")
    private String code;


    @Schema(description = "显示名称")
    @Size(max = 128, message = "name 长度不能超过 128")
    private String name;


    @Schema(description = "EMERGENCY 7,CRITICAL 6,FAULT 5,ERROR 4,WARNING 3,NOTIFICATION 2,INFORMATION 1")
    @NotNull(message = "level 不能为 null")
    private EquipWarnLevel level;


    @Schema(description = "处理建议")
    @JsonInclude(Include.NON_EMPTY)
    private String prompt;
}
