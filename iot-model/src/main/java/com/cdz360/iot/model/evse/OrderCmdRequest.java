package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.base.BaseGwRequest;
import com.cdz360.iot.model.type.GwOrderCmd;


public class OrderCmdRequest extends BaseGwRequest {
    private String orderNo;
    private GwOrderCmd cmd;
    private SupplyType supply;
    private String evseId;
    private Integer plugId;
    private Long balance;
    // TODO: 还有其他字段


    public String getOrderNo() {
        return orderNo;
    }

    public OrderCmdRequest setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public GwOrderCmd getCmd() {
        return cmd;
    }

    public OrderCmdRequest setCmd(GwOrderCmd cmd) {
        this.cmd = cmd;
        return this;
    }

    public SupplyType getSupply() {
        return supply;
    }

    public OrderCmdRequest setSupply(SupplyType supply) {
        this.supply = supply;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public OrderCmdRequest setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public Integer getPlugId() {
        return plugId;
    }

    public OrderCmdRequest setPlugId(Integer plugId) {
        this.plugId = plugId;
        return this;
    }

    public Long getBalance() {
        return balance;
    }

    public OrderCmdRequest setBalance(Long balance) {
        this.balance = balance;
        return this;
    }
}
