package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseGwRequest;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 在线订单续费
 *
 * @Author: <PERSON>
 * @Date: 2019/7/4 14:29
 */
public class OrderFeeRefreshRequest extends BaseGwRequest {
    @Schema(description = "唯一的订单号")
    private String orderNo;

    @Schema(description = "桩在云平台的唯一ID")
    private String evseId;

    @Schema(description = "充电枪ID")
    private Integer plugId;

    @Schema(description = "剩余金额,单位'分'")
    private Integer available;

    @Schema(description = "已充电量,单位'0.0001KWH'. 如 34567表示 3.4567kwh")
    private Integer kwh;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getEvseId() {
        return evseId;
    }

    public void setEvseId(String evseId) {
        this.evseId = evseId;
    }

    public Integer getPlugId() {
        return plugId;
    }

    public void setPlugId(Integer plugId) {
        this.plugId = plugId;
    }

    public Integer getAvailable() {
        return available;
    }

    public void setAvailable(Integer available) {
        this.available = available;
    }

    public Integer getKwh() {
        return kwh;
    }

    public void setKwh(Integer kwh) {
        this.kwh = kwh;
    }
}
