package com.cdz360.iot.model.parts.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "处理物料退回参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsRollbackParam extends PartsOpBaseParam {

    @Schema(description = "快递单号")
    @JsonInclude(Include.NON_EMPTY)
    private String expressNo;

    @Schema(description = "快递名称")
    @JsonInclude(Include.NON_EMPTY)
    private String expressName;

}
