package com.cdz360.iot.model.evse.po;

import com.cdz360.iot.model.evse.type.PackageType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "海外平台升级包")
public class EvsePackagePo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "设备类型")
    private PackageType type;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "状态,1-正常，0-停用")
    private Long status;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "升级包信息")
    private List<PackageInfoItem> packageInfo;

    @Schema(description = "账号")
    private String account;

    @Schema(description = "密码")
    private String password;

    private String code;

    @Schema(description = "删除与否1-已删除，0-未删除")
    private Boolean enable;

    @Schema(description = "操作人ID")
    private Long opId;

    @Schema(description = "操作人")
    private String opName;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "最后更新时间")
    private Date updateTime;


}
