package com.cdz360.iot.model.evse.type;

import com.cdz360.iot.model.type.CfgEvseResultType;

public interface EvseCfgResult {

    int SUCCESS = 0;

    int FAIL = 99;

    int SENDING = 300;

    int TIMEOUT = 301;

    int EVSE_OFFLINE = 302;

    static Integer parse(CfgEvseResultType type) {
        Integer res = null;
        switch (type) {
            case SUCCESS:
                res = SUCCESS;
                break;
            case FAIL:
                res = FAIL;
                break;
            case TIMEOUT:
                res = TIMEOUT;
                break;
        }
        return res;
    }

}
