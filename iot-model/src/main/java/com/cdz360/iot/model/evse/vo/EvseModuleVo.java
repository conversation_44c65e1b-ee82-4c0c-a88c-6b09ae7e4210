package com.cdz360.iot.model.evse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseModuleVo {

    @Schema(description = "序号")
    private Integer idx;

    @Schema(description = "型号")
    private String moduleType;

    @Schema(description = "关联枪头")
    private Integer plugId;

    @Schema(description = "模块序列号")
    private String deviceNo;

    @Schema(description = "进风口温度")
    private Integer intakeTemp;

    @Schema(description = "设定电压")
    private BigDecimal voltage;

    @Schema(description = "实际电压")
    private BigDecimal actualVoltage;

    @Schema(description = "设定电流")
    private BigDecimal current;

    @Schema(description = "实际电流")
    private BigDecimal actualCurrent;

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "状态码描述")
    private List<String> statusDesc;

}
