package com.cdz360.iot.model.evse.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseModulePo {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "器件名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceName;

    @Schema(description = "模块类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String moduleType;

    @Schema(description = "槽位数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer number;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

}
