package com.cdz360.iot.model.modbus.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 485线承载的上传通信协议
 */
@Getter
public enum Rs485Protocol implements DcEnum {

    UNKNOWN(0),

    MODBUS(1),      // MODBUS RTU
    MODBUS_RTU_TCP(2),  // ModbusRtu over TCP
    MODBUS_TCP(4),  // MODBUS TCP
    DLT645(11),     // 电表DLT645
    ;

    @JsonValue
    final int code;

    Rs485Protocol(int code) {
        this.code = code;
    }


    @JsonCreator
    public static Rs485Protocol valueOf(Object codeIn) {
        if (codeIn == null) {
            return Rs485Protocol.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof Rs485Protocol) {
            return (Rs485Protocol) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (Rs485Protocol type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return Rs485Protocol.UNKNOWN;
    }
}
