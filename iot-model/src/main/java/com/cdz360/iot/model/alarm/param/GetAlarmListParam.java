package com.cdz360.iot.model.alarm.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "获取设备告警列表的请求参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GetAlarmListParam extends BaseListParam {

    @Schema(description = "查询设备编号列表(不传则认为汇总当前用户所有设备)")
    @JsonInclude(Include.NON_NULL)
    private List<String> dno;

    @Schema(hidden = true)
    private List<String> gids;

    @Schema(hidden = true)
    private String commIdChain;

    @Schema(hidden = true)
    private List<String> siteIds;

    @Schema(hidden = true)
    private List<EssEquipType> equipTypes;

    @Schema(hidden = true)
    private List<AlarmStatusEnum> alarmStatusList;
}
