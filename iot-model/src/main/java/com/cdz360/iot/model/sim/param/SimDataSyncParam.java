package com.cdz360.iot.model.sim.param;

import com.cdz360.iot.model.sim.po.SimPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class SimDataSyncParam {

    private List<SimPo> reqData;

    @Schema(description = "PB-usage是否为全量数据（true or null：PB-usage为当月用量；false：PB-usage为昨日用量；）")
    private Boolean isTotalPBUsage;

    @Override
    public String toString() {
        String str = "reqData.size: %d, isTotalPBUsage: %b";
        return String.format(str, reqData.size(), isTotalPBUsage);
    }

}
