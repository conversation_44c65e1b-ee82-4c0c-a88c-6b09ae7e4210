package com.cdz360.iot.model.meter.dto;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Classname Meter485Cfg
 * @Description
 * @Date 11/3/2021 10:14 AM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Meter485Cfg extends P485Port {


    //    @Value("${cfgVer:#{null}}")
    private Long cfgVer;

    //        @Value("${pvCfgList:#{null}}")
    private List<MeterInfo> meterList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
