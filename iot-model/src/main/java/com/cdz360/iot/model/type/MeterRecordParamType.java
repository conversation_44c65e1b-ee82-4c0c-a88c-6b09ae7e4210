package com.cdz360.iot.model.type;

/**
 * @Classname MeterRecordParamType
 * @Description 电表统计参数
 * @Date 11/8/2021 10:05 AM
 * @Created by Rafael
 */
public enum MeterRecordParamType {
    combineTotal("组合有功总电能"),
    positiveTotal("正向有功总电能"),
    negativeTotal("反向有功总电能"),
    positiveIdleTotal("组合无功1总电能"),
    negativeIdleTotal("组合无功2总电能"),
    positiveApparentTotal("正向视在总电能"),
    negativeApparentTotal("反向视在总电能");

    private String desc;
    MeterRecordParamType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {return this.desc;}
}