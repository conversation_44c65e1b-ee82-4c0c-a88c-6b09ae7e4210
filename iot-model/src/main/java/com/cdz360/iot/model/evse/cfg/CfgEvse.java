package com.cdz360.iot.model.evse.cfg;

import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
public class CfgEvse extends BaseObject {
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private String adminCodeA;//桩端管理员登录密码, 数字型, 6~8位. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private String adminCodeB;//桩端二级管理员登录密码,数字型, 6~8位. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean vin;//是否支持 VIN 码充电. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private String qrUrl;//桩端显示的二维码 URL. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private List<WhiteCard> whiteCards;//紧急充电卡列表. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean queryChargeRecord;//是否支持充电记录查询. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean qrCharge;//是否支持扫码充电. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean cardCharge;//是否支持刷卡充电. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean noCardCharge;//是否支持无卡充电. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Boolean timedCharge;//是否支持定时充电. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Integer dayVolume;//白天音量. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Integer nightVolume;//夜间音量. 不传表示不做修改

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private ChargeStopMode stopMode;//充电停止方式, 不传表示不做变更

    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Charge charge;//计费方案. 不传表示不做修改

    private String cfgVer;
}