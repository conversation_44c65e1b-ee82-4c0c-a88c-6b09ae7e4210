package com.cdz360.iot.model.parts.po;

import com.cdz360.iot.model.parts.type.PartsOpType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "配件操作日志")
public class PartsOpLogPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "操作类型:STORE_2_SEND(总库发货);RECEIVE_4_SEND(签收总库发货物料);"
        + "APPLY_4_STORE(申请调拨);APPROVE_AND_SEND(同意调拨);"
        + "RECEIVE_4_APPLY(签收调拨物料);STORE_2_EVSE(物料被使用);"
        + "EVSE_2_STORE(物料被拆卸);STATUS_MODIFY(状态修改);STORE_ROLLBACK(物料退回总部);"
        + "RECEIVE_4_ROLLBACK(签收退回物料);STORE_BROKEN(报废)")
    private PartsOpType opType;

    @Schema(description = "操作人员ID(sys_user.id)")
    private Long opUid;

    @Schema(description = "操作记录内容:{name,status,orderNo}")
    private OpDetail detail;

    @Schema(description = "配件唯一编码(t_parts.code)")
    @NotNull(message = "partsCode 不能为 null")
    @Size(max = 16, message = "partsCode 长度不能超过 16")
    private String partsCode;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Data
    @Accessors(chain = true)
    public static class OpDetail {

        @Schema(description = "不做说明，根据操作类型来决定")
        @JsonInclude(Include.NON_EMPTY)
        private String name;

        @Schema(description = "不做说明，根据操作类型来决定")
        @JsonInclude(Include.NON_EMPTY)
        private String lastStatus;

        @Schema(description = "不做说明，根据操作类型来决定")
        @JsonInclude(Include.NON_EMPTY)
        private String status;

        @Schema(description = "不做说明，根据操作类型来决定")
        @JsonInclude(Include.NON_EMPTY)
        private String orderNo;
    }

    public PartsOpLogPo detailStatus(String status) {
        this.detail = new OpDetail().setStatus(status);
        return this;
    }

    public PartsOpLogPo detailStatus(String lastStatus, String status) {
        this.detail = new OpDetail().setLastStatus(lastStatus).setStatus(status);
        return this;
    }

    public PartsOpLogPo detail(String name, String orderNo) {
        this.detail = new OpDetail().setName(name).setOrderNo(orderNo);
        return this;
    }

    public PartsOpLogPo detailOrder(String orderNo) {
        this.detail = new OpDetail().setOrderNo(orderNo);
        return this;
    }
}

