package com.cdz360.iot.model.site.po;

import com.cdz360.iot.model.base.BaseObject;

import java.util.Date;

/**
 * 为初始化的网关
 */
public class IdleGwPo extends BaseObject {

    private long id;

    private String gwno;

    private String passcode;

    private String ip;

    /**
     * 经度
     */
    private double lon;

    /**
     * 纬度
     */
    private double lat;

    private String cityCode;

    private boolean enable;

    private Date createTime;

    private Date updateTime;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getGwno() {
        return gwno;
    }

    public void setGwno(String gwno) {
        this.gwno = gwno;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public double getLon() {
        return lon;
    }

    public void setLon(double lon) {
        this.lon = lon;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPasscode() {
        return passcode;
    }

    public void setPasscode(String passcode) {
        this.passcode = passcode;
    }
}
