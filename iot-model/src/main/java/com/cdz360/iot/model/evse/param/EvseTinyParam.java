package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EvseTinyParam extends BaseListParam {

    private String commIdChain;

    private String siteId;

    @Schema(description = "运营状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseBizStatus> bizStatusList;

    @Schema(required = false)
    private List<String> evseNoList;

}
