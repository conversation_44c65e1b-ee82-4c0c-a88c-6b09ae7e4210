package com.cdz360.iot.model.dongzheng.request;

public class PassReq extends BaseReq{
    private String username;
    private String oldPassword;
    private String newPassword;

    public String getUsername() {
        return username;
    }

    public PassReq setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public PassReq setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
        return this;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public PassReq setNewPassword(String newPassword) {
        this.newPassword = newPassword;
        return this;
    }
}
