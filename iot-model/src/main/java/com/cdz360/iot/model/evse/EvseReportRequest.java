package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.base.BaseGwRequest;
import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.base.model.iot.type.NetType;

import java.util.List;

public class EvseReportRequest extends BaseGwRequest {
    private String evseId;
    private EvseStatus evseStatus = EvseStatus.UNKNOWN;
    private EvseProtocolType protocol;//桩协议类型, DC/CCTIA
    private Integer evseVer;//桩协议版本。如：303
    private String evseSoftware;//firmwareVer, 值类似：127-213-213, pc01-pc02-pc03
    private SupplyType supply = SupplyType.UNKNOWN;
    private NetType net = NetType.UNKNOWN;
    private Integer plugNum;
    private Integer temp;
    private List<Plug> plugs;

    private Boolean vin;
    private Integer errorCode;
    private String error;

    public String getEvseId() {
        return evseId;
    }

    public EvseReportRequest setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public EvseStatus getEvseStatus() {
        return evseStatus;
    }

    public EvseReportRequest setEvseStatus(EvseStatus evseStatus) {
        this.evseStatus = evseStatus;
        return this;
    }

    public SupplyType getSupply() {
        return supply;
    }

    public EvseReportRequest setSupply(SupplyType supply) {
        this.supply = supply;
        return this;
    }

    public NetType getNet() {
        return net;
    }

    public EvseReportRequest setNet(NetType net) {
        this.net = net;
        return this;
    }

    public Integer getPlugNum() {
        return plugNum;
    }

    public EvseReportRequest setPlugNum(Integer plugNum) {
        this.plugNum = plugNum;
        return this;
    }

    public Integer getTemp() {
        return temp;
    }

    public EvseReportRequest setTemp(Integer temp) {
        this.temp = temp;
        return this;
    }

    public List<Plug> getPlugs() {
        return plugs;
    }

    public EvseReportRequest setPlugs(List<Plug> plugs) {
        this.plugs = plugs;
        return this;
    }

    public Boolean getVin() {
        return vin;
    }

    public EvseReportRequest setVin(Boolean vin) {
        this.vin = vin;
        return this;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public EvseReportRequest setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
        return this;
    }

    public String getError() {
        return error;
    }

    public EvseReportRequest setError(String error) {
        this.error = error;
        return this;
    }

    public Integer getEvseVer() {
        return evseVer;
    }

    public EvseReportRequest setEvseVer(Integer evseVer) {
        this.evseVer = evseVer;
        return this;
    }

    public String getEvseSoftware() {
        return evseSoftware;
    }

    public EvseReportRequest setEvseSoftware(String evseSoftware) {
        this.evseSoftware = evseSoftware;
        return this;
    }

    public EvseProtocolType getProtocol() {
        return protocol;
    }

    public EvseReportRequest setProtocol(EvseProtocolType protocol) {
        this.protocol = protocol;
        return this;
    }

    public static class Plug extends BaseObject {
        private Integer plugId;
        private PlugStatus plugStatus;
        private Integer errorCode;
        private String error;
        private Integer alertCode;
        private Integer temp;

        public int getPlugId() {
            return plugId;
        }

        public Plug setPlugId(int plugId) {
            this.plugId = plugId;
            return this;
        }

        public Plug setPlugId(Integer plugId) {
            this.plugId = plugId;
            return this;
        }

        public PlugStatus getPlugStatus() {
            return plugStatus;
        }

        public Plug setPlugStatus(PlugStatus plugStatus) {
            this.plugStatus = plugStatus;
            return this;
        }

        public Integer getTemp() {
            return temp;
        }

        public Plug setTemp(Integer temp) {
            this.temp = temp;
            return this;
        }

        public String getError() {
            return error;
        }

        public Plug setError(String error) {
            this.error = error;
            return this;
        }

        public Integer getErrorCode() {
            return errorCode;
        }

        public Plug setErrorCode(Integer errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public Integer getAlertCode() {
            return alertCode;
        }

        public Plug setAlertCode(Integer alertCode) {
            this.alertCode = alertCode;
            return this;
        }
    }
}
