package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.EvseVendor;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * @ClassName： EvseBundle
 * @Description: 桩升级包信息
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:13
 */
@Schema(description = "com.cdz360.iot.model.evse.EvseBundle")
@Data
@Accessors(chain = true)
public class EvseBundle implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "升级包类型")
    private BundleType type;

    @Schema(description = "状态（0已停用；1正常；）")
    private Integer status;

    @Schema(description = "供应商（桩软件必填，控制器软件不填）")
    private EvseVendor vendor;

    @Schema(description = "升级包大小")
    private Long bundleSize;

    /**
     * 升级包唯一编号
     */
    @Schema(description = "升级包唯一编号")
    private Long version;
    /**
     * 上传升级压缩包的文件名
     */
    @Schema(description = "上传升级压缩包的文件名")
    private String fileName;
    /**
     * 升级要点
     */
    @Schema(description = "升级要点")
    private String releaseNote;
    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    private Long opId;
    /**
     * 操作人姓名
     */
    @Schema(description = "操作人姓名")
    private String opName;
    /**
     * 自描述文件版本
     */
    @Schema(description = "自描述文件版本")
    private Integer protocol;
    /**
     * 自描述文件内容
     */
    @Schema(description = "自描述文件内容")
    private String context;
    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    private Date createTime;
    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date updateTime;
    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean enable;
    /**
     * 上传进度
     */
    @Schema(description = "上传进度")
    private Integer progress;

}