package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.type.EvseBizType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEvseParam extends BaseListParam {

    @Schema(description = "集团商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String brand;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

    @Schema(description = "桩编号, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "桩名称, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseName;

    @Schema(description = "电流类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private SupplyType supplyType;

    @Schema(description = "桩型号, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String modelName;

    @Schema(description = "桩协议, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String protocol;

    @Schema(description = "软件版本, 支持模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String firmwareVer;


    @Schema(description = "桩编号列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;

    @Schema(description = "桩Id列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> evseIdList;


    @Schema(description = "sim卡号模糊查找")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String iccid;

    @Schema(description = "运营模式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseBizType bizType;

    @Schema(description = "运营状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseBizStatus> bizStatusList;


    @Schema(description = "桩状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseStatus> evseStatusList;

    @Schema(description = "是否支持本地VIN鉴权")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isVinAuth;

    @Schema(description = "软件版本")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> firmwareVerList;

    @Schema(description = "模糊查询关键词")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String keyword;

    /**
     * 查询flags列表中存在的桩型号记录（匹配存在交集的项目）
     */
    @Schema(description = "标志位(1, 不支持空占状态; 2, 不支持计费下发;3-支持本地VIN鉴权)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Integer> flags;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
