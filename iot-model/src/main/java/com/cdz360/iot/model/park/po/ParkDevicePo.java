package com.cdz360.iot.model.park.po;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "停车设备信息")

public class ParkDevicePo {



	@Schema(description = "记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "车场id")

	@NotNull(message = "parkId 不能为 null")

	private Long parkId;



	@Schema(description = "设备号")

	@NotNull(message = "deviceId 不能为 null")

	@Size(max = 200, message = "deviceId 长度不能超过 200")

	private String deviceId;



	@Schema(description = "设备ip信息")

	private ParkDeviceIp ip;



	@Schema(description = "消息")

	private String eventMsg;



	@Schema(description = "设备掉线/上线时间(时间戳)")

	private Long eventTime;



	@Schema(description = "推送状态值")

	private Integer status;



	private Date createTime;



	private Date updateTime;





}

