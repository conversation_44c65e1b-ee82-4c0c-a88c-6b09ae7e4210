package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseGwRequest;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class OrderReportRequestV2 {

    public static class CustomDateDeserializer extends StdDeserializer<Date> {

        private SimpleDateFormat formatter =
                new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");

        public CustomDateDeserializer() {
            this(null);
        }

        public CustomDateDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public Date deserialize(JsonParser jsonparser, DeserializationContext context)
                throws IOException {
            String unixTimestamp = jsonparser.getText();
            Long milliseconds = Long.valueOf(unixTimestamp + "000");
            Date date = new Date();
            date.setTime(milliseconds);
            return date;
        }
    }

    public static class CustomDateSerializer extends StdSerializer<Date> {

        public CustomDateSerializer() {
            this(null);
        }

        public CustomDateSerializer(Class<Date> t) {
            super(t);
        }

        @Override
        public void serialize(Date value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeNumber(value.getTime() / 1000);
        }
    }

}
