package com.cdz360.iot.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

@Schema(description = "桩配置更新通知请求")
public class EvseCfgRequest extends BaseObject {
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String type = "REQ";

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String seq;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String method = "EVSE_CFG";

    @Schema(description = "需要更新配置的桩ID数组, 桩ID在云平台的唯一")
    private List<String> evseIds;

    @Schema(description = "配置内容版本号. 该信息用于统计配置生效的数量")
    private String cfgVer;

    public String getType() {
        return type;
    }

    public EvseCfgRequest setType(String type) {
        this.type = type;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public EvseCfgRequest setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public EvseCfgRequest setMethod(String method) {
        this.method = method;
        return this;
    }

    public List<String> getEvseIds() {
        return evseIds;
    }

    public EvseCfgRequest setEvseIds(List<String> evseIds) {
        this.evseIds = evseIds;
        return this;
    }

    public String getCfgVer() {
        return cfgVer;
    }

    public EvseCfgRequest setCfgVer(String cfgVer) {
        this.cfgVer = cfgVer;
        return this;
    }
}
