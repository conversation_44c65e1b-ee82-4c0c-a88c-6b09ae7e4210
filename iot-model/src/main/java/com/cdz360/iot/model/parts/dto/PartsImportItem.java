package com.cdz360.iot.model.parts.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PartsImportItem {

    @Schema(description = "物料名称")
    @JsonInclude(Include.NON_NULL)
    private String typeName;

    @Schema(description = "物料规格型号")
    @JsonInclude(Include.NON_EMPTY)
    private String typeFullModel;

    @Schema(description = "物料编码ID")
    @JsonInclude(Include.NON_NULL)
    private Long typeId;

    @Schema(description = "物料编码")
    @JsonInclude(Include.NON_NULL)
    private String typeCode;

    @Schema(description = "所属运维人ID")
    @JsonInclude(Include.NON_EMPTY)
    private Long opId;

    @Schema(description = "所属运维人")
    @JsonInclude(Include.NON_EMPTY)
    private String opName;

    @Schema(description = "所属帐号")
    @JsonInclude(Include.NON_EMPTY)
    private String opUsername;

    @Schema(description = "是否可用")
    @JsonInclude(Include.NON_EMPTY)
    private Boolean available;

    @Schema(description = "错误信息")
    @JsonInclude(Include.NON_EMPTY)
    private String detail;

    // ==== 快递信息 ====
    @Schema(description = "快递单号")
    @JsonInclude(Include.NON_EMPTY)
    private String expressNo;

    @Schema(description = "快递名称")
    @JsonInclude(Include.NON_EMPTY)
    private String expressName;

    // ==== 操作人信息 ====
    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Long opUid;

    @Schema(description = "申请人ID")
    @JsonInclude(Include.NON_NULL)
    private Long applyId;
    @Schema(description = "申请人名称")
    @JsonInclude(Include.NON_NULL)
    private String applyName;

    @Schema(description = "申请数量")
    @JsonInclude(Include.NON_NULL)
    private Integer applyNum;
}
