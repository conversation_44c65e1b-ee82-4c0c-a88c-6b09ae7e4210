package com.cdz360.iot.model.evse.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;

@Getter
@Schema(description = "充电桩型号-标志位")
public enum EvseModelFlag implements DcEnum {

    UNKNOWN(0, "未知"),
    NONSUPPORT_CONNECT_STATUS(1, "不支持空占状态"),
    NONSUPPORT_PRICE_DELIVER(2, "不支持计费下发"),
    SUPPORT_VIN_AUTH(3, "支持本地VIN下发"),
    SUPPORT_CONSTANT_CHARGE(4, "支持不拔枪重连"),
    SUPPORT_LOCAL_CARD_AUTH(5, "支持本地认证卡下发"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    EvseModelFlag(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static EvseModelFlag valueOf(Object codeIn) {
        if (codeIn == null) {
            return EvseModelFlag.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EvseModelFlag) {
            return (EvseModelFlag) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (EvseModelFlag flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return EvseModelFlag.UNKNOWN;
    }

    public static List<EvseModelFlag> valueOf(List<Integer> integerList) {
        if (CollectionUtils.isEmpty(integerList)) {
            return null;
        }
        return integerList.stream().map(code -> {
            EvseModelFlag temp = UNKNOWN;
            for (EvseModelFlag flag : values()) {
                if (flag.code == code) {
                    temp = flag;
                }
            }
            return temp;
        }).collect(Collectors.toList());
    }

}
