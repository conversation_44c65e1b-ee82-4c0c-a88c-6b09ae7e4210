package com.cdz360.iot.model.parts.po;

import com.cdz360.iot.model.parts.type.TransOrderStatus;
import com.cdz360.iot.model.parts.type.TransOrderType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "调拨单")
public class TransOrderPo {

    @Schema(description = "调拨单号")
    @NotNull(message = "orderNo 不能为 null")
    @Size(max = 64, message = "orderNo 长度不能超过 64")
    private String orderNo;

    @Schema(description = "类型:SEND(发货);TRANSFER(调拨);REVERT(退回)")
    private TransOrderType type;

    @Schema(description = "调拨单状态:TRANSFERRING(调拨中);RECEIVED(已接收)")
    private TransOrderStatus status;

    @Schema(description = "仓库唯一编码")
    @Size(max = 16, message = "fromCode 长度不能超过 16")
    private String fromCode;

    @Schema(description = "仓库唯一编码")
    @Size(max = 16, message = "toCode 长度不能超过 16")
    private String toCode;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "创建人")
    private Long createBy;
}

