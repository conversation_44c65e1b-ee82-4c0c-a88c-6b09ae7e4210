package com.cdz360.iot.model.topology.po;

import com.cdz360.iot.model.type.TopologyType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "拓扑上下行关系")
public class SiteTopologyRefPo {

//	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "上级对象类型")
//	@NotNull(message = "upType 不能为 null")
	private TopologyType upType;

	@Schema(description = "上级设备对象id")
//	@NotNull(message = "upId 不能为 null")
	private Long upId;

	@Schema(description = "下级对象类型")
//	@NotNull(message = "downType 不能为 null")
	private TopologyType downType;

	@Schema(description = "上级设备对象id")
//	@NotNull(message = "downId 不能为 null")
	private Long downId;

//	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	private Date updateTime;


}
