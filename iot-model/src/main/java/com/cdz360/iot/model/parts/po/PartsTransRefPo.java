package com.cdz360.iot.model.parts.po;


import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "配件-调拨单关联")

public class PartsTransRefPo {



	@Schema(description = "配件唯一编码(t_parts.code)")

	@NotNull(message = "partsCode 不能为 null")

	@Size(max = 16, message = "partsCode 长度不能超过 16")

	private String partsCode;



	@Schema(description = "调拨单号(t_trans_order.orderNo)")

	@NotNull(message = "transOrderNo 不能为 null")

	@Size(max = 64, message = "transOrderNo 长度不能超过 64")

	private String transOrderNo;





}

