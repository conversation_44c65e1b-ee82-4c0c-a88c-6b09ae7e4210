package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusRwType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "t", // 使用字段 t 作为类型标识符
    visible = true // 允许反序列化时读取该字段
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = ModbusDecimalTv.class, names = {"8", "4", "2", }),      // DECIMAL
    @JsonSubTypes.Type(value = ModbusIeee754Tv.class, name = "9"),      // IEEE754
    @JsonSubTypes.Type(value = ModbusIntegerTv.class, names = {"3"}), // U_SHORT/INTEGER
    @JsonSubTypes.Type(value = ModbusLongTv.class, names = {"5", "6", "7"}),         // UINT32
    @JsonSubTypes.Type(value = ModbusBcdTv.class, names = "10"),         // BCD
    @JsonSubTypes.Type(value = ModbusStringTv.class, name = "11")        // STRING
})

@Data
@Accessors(chain = true)
public abstract class ModbusAbstractTv {

    /**
     * 寄存器地址
     */
    private int addr;


    /**
     * 名称
     * <p>PCS: PcsKvCode</p>
     */
    private String name;


    /**
     * 数据类型 ModbusDataType
     */
    private int t;


    /**
     * 寄存器个数
     */
    private int num;

    /**
     * 寄存器原始byte数据
     */
    private byte[] bytes;

    private Boolean isPushData;

    /**
     * 显示的名称,一般为通讯文档里的寄存器名字
     */
    @JsonProperty(value = "dn")
    @JsonInclude(Include.NON_EMPTY)
    private String displayName;

    @JsonInclude(Include.NON_EMPTY)
    private String unit;

    @Nullable
    @JsonInclude(Include.NON_NULL)
    private ModbusRwType rw;

    @JsonInclude(Include.NON_EMPTY)
    private String desc;
}
