package com.cdz360.iot.model.park.vo;

import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "地锁事件日志查看")
@EqualsAndHashCode(callSuper = true)
public class ParkingLockEventLogVo extends ParkingLockEventLogPo {

}
