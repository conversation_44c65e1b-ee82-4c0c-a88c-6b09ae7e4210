package com.cdz360.iot.model.base;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.site.mqtt.BaseMqttMsg;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CollectEvseStatusRequest //extends BaseGwPackage
{


//    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
//    private String method = "COLLECT_EVSE_STATUS";


//    public CollectEvseStatusRequest() {
//        super(IotPackageType.REQ);
//    }

    public static class REQ extends BaseMqttMsg<CollectEvseStatusRequest> {

        @Schema(accessMode = Schema.AccessMode.READ_ONLY)
        @Getter
        @JsonProperty("cmd")
        private String method = "COLLECT_EVSE_STATUS";  // TODO： 兼容旧版网关代码，2021.11版本后移除

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.CE_COLLECT_STATUS;
        }

        public REQ(String gwno) {
            super(gwno, new CollectEvseStatusRequest());
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }
}
