package com.cdz360.iot.model.evse.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;

@Getter
public enum PackageType implements DcEnum {

    UNKNOWN(0, "未知"),
    CHARGE_PILE(10, "充电桩");

    @JsonValue
    private final int code;
    private final String desc;

    PackageType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static PackageType valueOf(Object codeIn) {
        if (codeIn == null) {
            return PackageType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PackageType) {
            return (PackageType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (PackageType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return PackageType.UNKNOWN;
    }

}
