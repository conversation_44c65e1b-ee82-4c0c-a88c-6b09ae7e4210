package com.cdz360.iot.model.site.ctrl;

import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname SiteCtrlMonitor
 * @Description
 * @Date 4/21/2020 7:54 PM
 * @Created by Rafael
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(description = "控制器监测信息上报")
public class SiteCtrlMonitor extends BaseObject {
    @Schema(description = "负载率")
    private List<Integer> loadRatio;

    @Schema(description = "配电柜温度")
    private List<Integer> pwrTemp;
}