package com.cdz360.iot.model.parts.param;

import com.cdz360.iot.model.parts.type.PartsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "处理物料报废参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsEditParam extends PartsOpBaseParam {

    @Schema(description = "物料规格ID 精确匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private Long typeId;

    @Schema(description = "需要变更的目标状态")
    @JsonInclude(Include.NON_NULL)
    private PartsStatus toStatus;
}
