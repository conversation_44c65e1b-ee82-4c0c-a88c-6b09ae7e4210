package com.cdz360.iot.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.iot.type.EquipStatus;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname MeterListParam
 * @Description
 * @Date 9/21/2020 9:42 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterListParam extends BaseListParam {

    private String siteId;
    private List<String> siteIdList;
    private String gwno;// 关联的控制器
    private Boolean powerLoss;// 是否计算电损，1启用 0禁用
    private String idChain;
    private List<Long> meterIdList; // 指定显示电表列表
    private List<String> dnoList;   // 设备 dno 列表

    private String siteNameLike; // 场站名称模糊查询
    private String meterNoLike; // 电表编号模糊查询

    private String meterNameLike; // 电表名称模糊查询

    private String verdorLike; // 品牌模糊查询

    private EquipStatus status; // 设备状态

    private String deviceModelLike; // 型号模糊查询

}