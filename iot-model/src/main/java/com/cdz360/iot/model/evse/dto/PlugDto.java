package com.cdz360.iot.model.evse.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PlugDto {
    @Schema(description = "序号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugId;

    @Schema(description = "桩号+抢号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String plugNo;

    @Schema(description = "枪头名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String plugName;
}
