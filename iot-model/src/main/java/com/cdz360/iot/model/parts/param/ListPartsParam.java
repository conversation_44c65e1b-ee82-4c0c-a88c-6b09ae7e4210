package com.cdz360.iot.model.parts.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.iot.model.parts.type.PartsLocationStatus;
import com.cdz360.iot.model.parts.type.PartsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取物料列表查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListPartsParam extends BaseListParam {

    @Schema(description = "物料ID 模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String partsCodeLike;

    @Schema(description = "物料规格编号 模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String typeCodeLike;

    @Schema(description = "物料规格ID 精确匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private Long typeId;

    @Schema(description = "物料规格ID列表 精确匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private List<Long> typeIdList;

    @Schema(description = "物料规格名称 精确匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private String typeName;

    @Schema(description = "物料规格名称 模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String typeNameLike;

    @Schema(description = "物料规格 模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String typeModelLike;

    @Schema(description = "用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "不包含用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> exUidList;

    @Schema(description = "所属运维组 中间转换处理，通过场站组获取用户ID")
    @JsonInclude(Include.NON_NULL)
    private List<String> gidList;

    @Schema(description = "流转状态列表")
    @JsonInclude(Include.NON_NULL)
    private List<PartsLocationStatus> locationStatusList;

    @Schema(description = "物料状态(配件状态) 是否可用转换")
    @JsonInclude(Include.NON_NULL)
    private List<PartsStatus> statusList;

    @Schema(description = "快递单号 模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String expressNoLike;

    @Schema(description = "是否指查询总部库 不做sql查询条件")
    @JsonInclude(Include.NON_NULL)
    private Boolean defaultTopStorage;

    @Schema(description = "仓库编码列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> storageCodeList;
}
