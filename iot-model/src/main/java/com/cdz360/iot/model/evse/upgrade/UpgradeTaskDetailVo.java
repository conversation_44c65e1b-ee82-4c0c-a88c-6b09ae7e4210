package com.cdz360.iot.model.evse.upgrade;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * @Classname UpgradeTaskDetailVo
 * @Description TODO
 * @Date 9/18/2019 3:08 PM
 * @Created by Rafael
 */
@Data
@Schema(description = "桩升级详情")
public class UpgradeTaskDetailVo {
    private Long id;
    private Long taskId;
    private String evseId;
    private String evseName;
    private String pc01Ver;
    private String pc02Ver;
    private String pc03Ver;
    private UpdateTaskStatusEnum status;
    private String failReason;
    private Date updateTime;
}