package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.base.IotPackageType;
import com.cdz360.iot.model.type.StopMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;

/**
 * 桩端发起充电响应
 *
 * <AUTHOR>
 * @date Create on 2019/04/19
 */
public class ChgEvseResonse extends BaseObject {
    @Schema(description = "消息方法, 请求/响应")
    private IotPackageType type = IotPackageType.RES;

    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "可实时扣费金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal amount;

    @Schema(description = "停止方式")
    private StopMode stopMode;

    @Schema(description = "鉴权介质绑定的车牌号")
    private String carNo;

    public String getOrderNo() {
        return orderNo;
    }

    public ChgEvseResonse setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public StopMode getStopMode() {
        return stopMode;
    }

    public ChgEvseResonse setStopMode(StopMode stopMode) {
        this.stopMode = stopMode;
        return this;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public ChgEvseResonse setBalance(BigDecimal balance) {
        this.balance = balance;
        return this;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public ChgEvseResonse setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }
}
