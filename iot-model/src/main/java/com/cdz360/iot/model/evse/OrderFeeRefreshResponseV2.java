package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 * @Author: Nathan
 * @Date: 2019/7/4 15:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderFeeRefreshResponseV2 extends BaseObject {

    @Schema(description = "金额抵扣时必传, 账户总余额, 单位'元'")
    private BigDecimal totalAmount;

    @Schema(description = "金额抵扣时必传, 增加的冻结金额, 单位'元'")
    private BigDecimal frozenAmount;

    @Schema(description = "金额抵扣时必传, 增加后订单冻结金额, 单位'元'")
    private BigDecimal afterOrderFrozenAmount;

    @Schema(description = "电量抵扣时必传, 电量账户总余额, 单位'kwh'")
    private BigDecimal totalPower;

    @Schema(description = "电量抵扣时必传, 增加的冻结电量, 单位'kwh'")
    private BigDecimal frozenPower;
}
