package com.cdz360.iot.model.modbus.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.List;

@Getter
public enum ModbusDataType implements DcEnum {

    UNKNOWN(0),

    BYTE(1),
    INT16(2),
    UINT16(3),
    INT32(4),
    UINT32(5),
    INT64(6),
    UINT64(7),
    DECIMAL(8),
    IEEE754(9),    // IEEE 754 单/双精度浮点数
    BCD(10),    // BCD 编码，转数字
    String(11);

    @JsonValue
    final int code;

    ModbusDataType(int code) {
        this.code = code;
    }

    public static List<Integer> withSignType() {
        return List.of(INT16.getCode(), INT32.getCode(), INT64.getCode(), DECIMAL.getCode());
    }


    @JsonCreator
    public static ModbusDataType valueOf(Object codeIn) {
        if (codeIn == null) {
            return ModbusDataType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ModbusDataType) {
            return (ModbusDataType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ModbusDataType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ModbusDataType.UNKNOWN;
    }


}
