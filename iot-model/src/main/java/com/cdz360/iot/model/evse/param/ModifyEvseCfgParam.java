package com.cdz360.iot.model.evse.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.cfg.ChargeV2;
import com.cdz360.iot.model.evse.cfg.WhiteCardV2;
import com.cdz360.iot.model.po.SiteAuthCardLogPo;
import com.cdz360.iot.model.vin.po.SiteAuthVinLogPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class ModifyEvseCfgParam {

    // 批量配置 批量修改桩配置传多个桩号
    @Schema(description = "桩编号列表", example = "010203040506")
    private List<String> evseNoList;

    @Schema(description = "桩端长效秘钥版本号")
    private Integer evsePasscodeVer;

    @Schema(description = "桩端长效秘钥")
    private String evsePasscode;

    //
    @Schema(description = "管理员密码", example = "123456")
    private String adminPassword;
    //
    @Schema(description = "二级管理员密码", example = "123456")
    private String level2Password;
    //
    @Schema(description = "白天音量", example = "5")
    private Integer dayVolume;
    //
    @Schema(description = "夜晚音量", example = "5")
    private Integer nightVolume;
    //
    @Schema(description = "二维码url", example = "http://aa.bb.cc/")
    private String qrUrl;

    @Schema(description = "国际协议")
    private String internationalAgreement;

    @Schema(description = "辅电电压设置")
    private Integer heatingVoltage;

    @Schema(description = "是否支持辅电手动切换")
    private Boolean heating;

    @Schema(description = "均/轮充设置 0均充 1轮充")
    private Integer avgOrTurnCharge;

    @Schema(description = "是否支持电池反接检测")
    private Boolean batteryCheck;

    @Schema(description = "是否支持主动安全检测")
    private Boolean securityCheck;

    @Schema(description = "是否支持插枪获取VIN")
    private Boolean vinDiscover;

    /**
     * null 不做更改 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    @Schema(description = "订单账号显示类型")
    private Integer accountDisplayType;

    @Schema(description = "合充开关 （1开0关）")
    private Integer isCombineCharge;
    //  （1是0否）
    @Schema(description = "是否支持充电记录查询", example = "true")
    private Boolean isQueryChargeRecord;
    //  （1是0否）
    @Schema(description = "是否支持不拔枪充电", example = "true")
    private Boolean constantCharge;
    //  （1是0否）
    @Schema(description = "是否可见订单信息隐私", example = "true")
    private Boolean orderPrivacySetting;
    //  （1是0否）
    @Schema(description = "是否支持定时充电", example = "true")
    private Boolean isTimedCharge;
    //  （1是0否）
    @Schema(description = "是否支持无卡充电", example = "true")
    private Boolean isNoCardCharge;
    //  （1是0否）
    @Schema(description = "是否支持扫码充电", example = "true")
    private Boolean isScanCharge;
    //  （1是0否）
    @Schema(description = "是否支持Vin码充电", example = "true")
    private Boolean isVinCharge;
    //  （1是0否）
    @Schema(description = "是否支持刷卡充电", example = "true")
    private Boolean isCardCharge;
    //  （1是0否）
    @Schema(description = "是否支持定额电量充电", example = "true")
    private Boolean isQuotaEleCharge;
    //  （1是0否）
    @Schema(description = "是否支持固定金额充电", example = "true")
    private Boolean isQuotaMoneyCharge;
    //  （1是0否）
    @Schema(description = "是否支持固定时长充电", example = "true")
    private Boolean isQuotaTimeCharge;

    @Schema(description = "计费模板Id，需要修改计费模板时赋值，否则为空null")
    private Long priceSchemeId;

    @Schema(description = "分时段计费")
    private List<ChargeV2> priceSchemeList;

    @Schema(description = "需要下发的紧急充电卡列表")
    private List<WhiteCardV2> whiteCards;

    @Schema(description = "桩本地vin鉴权")
    private List<SiteAuthVinLogPo> siteAuthVinList;

    @Schema(description = "桩本地card鉴权")
    private List<SiteAuthCardLogPo> siteAuthCardList;

//    //
//    @Schema(description = "紧急充电卡列表，用,分隔", example = "123,456,789")
//    private String whiteCardList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
