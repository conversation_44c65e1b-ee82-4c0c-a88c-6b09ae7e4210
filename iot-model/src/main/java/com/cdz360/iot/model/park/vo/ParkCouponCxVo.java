package com.cdz360.iot.model.park.vo;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-深圳创享智能开发有限公司")
public class ParkCouponCxVo {
    @JsonProperty(value = "parkingId")
    private String parkingId;// 车场编号

    @JsonProperty(value = "plateNumber")
    private String plateNumber;// 需要减免的车牌号码

    @JsonProperty(value = "favourableDuration")
    private Integer favourableDuration;// 优惠时长（单位：分）

    @JsonProperty(value = "timestamp")
    private Long timestamp;// 时间戳用于防止DoS攻击,时间戳相差超过2分钟，认定为非法操作

    @JsonProperty(value = "appKey")
    private String appKey;// 由平台编码统一分配，请求都要MD5加密32大写

    @JsonProperty(value = "sign")
    private String sign;// 商户名称

    @Data
    public static class Response {

        @Schema(description = "状态值 非1全部失败")
        private String status;

        @Schema(description = "message")
        private String message;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }
}