package com.cdz360.iot.model.base;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "微服务API调用响应消息的基础数据结构")
public class BaseRpcResponse extends BaseObject {

    @Schema(description = "错误描述")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String error = null;
    @Schema(description = "状态码, 0表示成功, 其他都为错误码")
    private int status = 0;

    public static BaseRpcResponse newInstance() {
        return new BaseRpcResponse();
    }

    public int getStatus() {
        return status;
    }

    public BaseRpcResponse setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getError() {
        return error;
    }

    public BaseRpcResponse setError(String error) {
        this.error = error;
        return this;
    }
}
