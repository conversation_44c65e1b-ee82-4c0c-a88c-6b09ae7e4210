package com.cdz360.iot.model.base;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.site.mqtt.BaseMqttMsg;
import com.cdz360.iot.model.type.GwRequestMethod;
import com.cdz360.iot.model.type.SshStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.RandomStringUtils;

@Schema(description = "ssh反向通道")
public class BaseSshRequest extends BaseObject{
    @Schema(description = "TUNNEL")
    private Enum method;
    @Schema(description = "指令类型: START, STOP")
    private Enum cmd;
    @Schema(description = "远端地址, IP或域名")
    private String remoteAddress;
    @Schema(description = "远端端口号")
    private int remotePort;
    @Schema(description = "反向通道端口")
    private int remoteTunnelPort;
    @Schema(description = "本地端口号, 默认为 22", required = false)
    private int localPort;
    @Schema(description = "有效期, 单位分钟, 默认60分钟. 过期后网关自动关闭通道", required = false)
    private int expire;

    public static class REQ extends BaseMqttMsg<BaseSshRequest> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.MGC_TUNNEL_START;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private REQ req;

        public builder() {
            this.req = new BaseSshRequest.REQ();
            this.req.setData(new BaseSshRequest());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setData(GwRequestMethod method, SshStatus cmd, String remoteAddress,
                               int remotePort, int remoteTunnelPort,
                               int localPort, int expire) {
            req.getData().setMethod(method);
            req.getData().setCmd(cmd);
            req.getData().setRemoteAddress(remoteAddress);
            req.getData().setRemotePort(remotePort);
            req.getData().setRemoteTunnelPort(remoteTunnelPort);
            req.getData().setLocalPort(localPort);
            req.getData().setExpire(expire);
            return this;
        }

        public builder setData(GwRequestMethod method, SshStatus cmd) {
            req.getData().setMethod(method);
            req.getData().setCmd(cmd);
            return this;
        }

        public BaseSshRequest.builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }

        public BaseSshRequest.REQ build() {
            return this.req;
        }
    }

    public BaseSshRequest() {
    }

    public Enum getMethod() {
        return method;
    }

    public void setMethod(Enum method) {
        this.method = method;
    }

    public Enum getCmd() {
        return cmd;
    }

    public void setCmd(Enum cmd) {
        this.cmd = cmd;
    }

    public String getRemoteAddress() {
        return remoteAddress;
    }

    public void setRemoteAddress(String remoteAddress) {
        this.remoteAddress = remoteAddress;
    }

    public int getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(int remotePort) {
        this.remotePort = remotePort;
    }

    public int getRemoteTunnelPort() {
        return remoteTunnelPort;
    }

    public void setRemoteTunnelPort(int remoteTunnelPort) {
        this.remoteTunnelPort = remoteTunnelPort;
    }

    public int getLocalPort() {
        return localPort;
    }

    public void setLocalPort(int localPort) {
        this.localPort = localPort;
    }

    public int getExpire() {
        return expire;
    }

    public void setExpire(int expire) {
        this.expire = expire;
    }
}
