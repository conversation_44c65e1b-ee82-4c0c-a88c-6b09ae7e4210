package com.cdz360.iot.model.dongzheng.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CardDto implements Serializable {
    /**
     * t_card
     */
    private static final long serialVersionUID = 1L;
    /**
     * 自动增长列
     */
    private Long id;
    /**
     * 卡片号码
     */
    private String cardNo;
    /**
     * 卡片类型0在线卡1离线卡
     */
    private Integer cardType;
    /**
     * 卡片芯片号
     */
    private String cardChipNo;
    /**
     * 卡片渠道-发卡方(1:商户自添加 2:平台制卡 3:虚拟卡生成）
     */
    private String cardChannel;
    /**
     * 卡片面值
     */
    private BigDecimal cardDenomination;
    /**
     * 卡片激活码
     */
    private String cardActivationCode;
    /**
     * 卡状态（10000未激活，10001已激活，10002卡锁定，10005已失效，10006已过期，20000离线卡已删除，20001离线卡正常）
     */
    private String cardStatus;
    /**
     * 卡片激活日期
     */
    private Date cardActivationDate;
    /**
     * 创建时间
     */
    private Date cardCreateDate;
    /**
     * 修改时间
     */
    private Date cardUpdateDate;
    /**
     * 商户Id
     */
    private Long commId;
    /**
     * 站点id(废弃)
     */
    private String stationId;
    /**
     * 站点名称(废弃)
     */
    private String stationName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 状态（1活跃，0休眠）
     */
    private Integer status;
    /**
     * 卡类别（0虚拟卡，1实体卡）(废弃)
     */
    private Integer type;
    /**
     * 0:其他,1:套餐卡, 2:月卡,3:离线卡;4-鉴权卡
     */
    private Integer isPackage;
    /**
     *
     */
    private Long merchantId;
    /**
     * 卡片赠送金余额
     */
    private BigDecimal cardGiftAmount;
    /**
     * 月卡或年卡的生效时间
     */
    private Date takeEffectTime;
    /**
     * 月卡或年卡的失效时间
     */
    private Date pastDueTime;
    /**
     * 月卡或年卡充电次数
     */
    private Integer chargerTimes;
    /**
     * 有效标志1有效0无效
     */
    private String yxBz;
    /**
     * 鉴权卡所属站点（多个站点用‘，’分割）
     */
    private String stations;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getCardChipNo() {
        return cardChipNo;
    }

    public void setCardChipNo(String cardChipNo) {
        this.cardChipNo = cardChipNo;
    }

    public String getCardChannel() {
        return cardChannel;
    }

    public void setCardChannel(String cardChannel) {
        this.cardChannel = cardChannel;
    }

    public BigDecimal getCardDenomination() {
        return cardDenomination;
    }

    public void setCardDenomination(BigDecimal cardDenomination) {
        this.cardDenomination = cardDenomination;
    }

    public String getCardActivationCode() {
        return cardActivationCode;
    }

    public void setCardActivationCode(String cardActivationCode) {
        this.cardActivationCode = cardActivationCode;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Date getCardActivationDate() {
        return cardActivationDate;
    }

    public void setCardActivationDate(Date cardActivationDate) {
        this.cardActivationDate = cardActivationDate;
    }

    public Date getCardCreateDate() {
        return cardCreateDate;
    }

    public void setCardCreateDate(Date cardCreateDate) {
        this.cardCreateDate = cardCreateDate;
    }

    public Date getCardUpdateDate() {
        return cardUpdateDate;
    }

    public void setCardUpdateDate(Date cardUpdateDate) {
        this.cardUpdateDate = cardUpdateDate;
    }

    public Long getCommId() {
        return commId;
    }

    public void setCommId(Long commId) {
        this.commId = commId;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsPackage() {
        return isPackage;
    }

    public void setIsPackage(Integer isPackage) {
        this.isPackage = isPackage;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public BigDecimal getCardGiftAmount() {
        return cardGiftAmount;
    }

    public void setCardGiftAmount(BigDecimal cardGiftAmount) {
        this.cardGiftAmount = cardGiftAmount;
    }

    public Date getTakeEffectTime() {
        return takeEffectTime;
    }

    public void setTakeEffectTime(Date takeEffectTime) {
        this.takeEffectTime = takeEffectTime;
    }

    public Date getPastDueTime() {
        return pastDueTime;
    }

    public void setPastDueTime(Date pastDueTime) {
        this.pastDueTime = pastDueTime;
    }

    public Integer getChargerTimes() {
        return chargerTimes;
    }

    public void setChargerTimes(Integer chargerTimes) {
        this.chargerTimes = chargerTimes;
    }

    public String getYxBz() {
        return yxBz;
    }

    public void setYxBz(String yxBz) {
        this.yxBz = yxBz;
    }

    public String getStations() {
        return stations;
    }

    public void setStations(String stations) {
        this.stations = stations;
    }
}
