package com.cdz360.iot.model.site.param;

import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

@Schema(description = "场站参数")
public class SiteParam extends BaseObject {


    @Schema(description = "场站ID")
    private String id;

//    @Schema(description = "城市编码")
//    private String cityCode;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "图片列表，站点图片，多个图片以英文逗号,分隔")
    private String images;

    @Schema(description = "图片列表 List形式，站点图片")
    private List<String> imagesList;

    @Schema(description = "城市编号")
    private String cityCode;

    @Schema(description = "当前页数")
    private Integer page;

    @Schema(description = "每页行数")
    private Integer rows;

    @Schema(description = "搜索关键词")
    private String keywords;

    public String getCityCode() {
        return cityCode;
    }

    public SiteParam setCityCode(String cityCode) {
        this.cityCode = cityCode;return this;
    }

    public Integer getPage() {
        return page;
    }

    public SiteParam setPage(Integer page) {
        this.page = page;return this;
    }

    public Integer getRows() {
        return rows;
    }

    public SiteParam setRows(Integer rows) {
        this.rows = rows;return this;
    }

    public String getKeywords() {
        return keywords;
    }

    public SiteParam setKeywords(String keywords) {
        this.keywords = keywords;return this;
    }

    public String getImages() {
        return images;
    }

    public SiteParam setImages(String images) {
        this.images = images;
        return this;
    }

    public List<String> getImagesList() {
        return imagesList;
    }

    public SiteParam setImagesList(List<String> imagesList) {
        this.imagesList = imagesList;
        return this;
    }
//    private String dzToken;
//
//    public String getDzToken() {
//        return dzToken;
//    }
//
//    public SiteParam setDzToken(String dzToken) {
//        this.dzToken = dzToken;
//        return this;
//    }
    //    private String siteName;
//
//    public String getSiteName() {
//        return siteName;
//    }
//
//    public SiteParam setSiteName(String siteName) {
//        this.siteName = siteName;
//        return this;
//    }

    public String getId() {
        return id;
    }

    public SiteParam setId(String id) {
        this.id = id;
        return this;
    }

//    public String getCityCode() {
//        return cityCode;
//    }
//
//    public SiteParam setCityCode(String cityCode) {
//        this.cityCode = cityCode;
//        return this;

    public Double getLongitude() {
        return longitude;
    }

    public SiteParam setLongitude(Double longitude) {
        this.longitude = longitude;
        return this;
    }

    public Double getLatitude() {
        return latitude;
    }

    public SiteParam setLatitude(Double latitude) {
        this.latitude = latitude;
        return this;
    }
//    }

}
