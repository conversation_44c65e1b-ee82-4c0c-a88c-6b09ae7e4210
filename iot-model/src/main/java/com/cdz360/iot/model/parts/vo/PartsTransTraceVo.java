package com.cdz360.iot.model.parts.vo;

import com.cdz360.iot.model.parts.po.PartsPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "物料调拨追溯信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsTransTraceVo extends PartsPo {

    @Schema(description = "无聊操作日志")
    @JsonInclude(Include.NON_NULL)
    private List<PartsOpLogVo> opLogVoList;

    //    物料规格 👇
    @Schema(description = "物料规格编码")
    @JsonInclude(Include.NON_NULL)
    private String typeCode;

    @Schema(description = "物料规格名称")
    @JsonInclude(Include.NON_NULL)
    private String typeName;

    @Schema(description = "物料规格型号")
    @JsonInclude(Include.NON_EMPTY)
    private String typeFullModel;

}
