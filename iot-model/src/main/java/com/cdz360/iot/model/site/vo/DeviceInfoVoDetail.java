package com.cdz360.iot.model.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DeviceInfoVoDetail {

    @Schema(description = "控制器编号 网关编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "设备个数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long num;

}
