package com.cdz360.iot.model.site.ctrl;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname SiteCtrlLinkRes
 * @Description
 * @Date 4/21/2020 3:46 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class SiteCtrlRegisterResult {
    private String n;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String password;
    private String mqUrl;
    private String mqClientId;
    private String mqUsername;
    private String mqPassword;
    private String mqTopic;
    private String mqttLwt; // last will topic
}