package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListEvseCfgParam extends BaseListParam {

    private String siteId;

    //private

    @Schema(description = "桩编号列表")
    private List<String> evseNoList;
}
