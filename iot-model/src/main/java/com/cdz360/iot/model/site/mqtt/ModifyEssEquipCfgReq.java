package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.modbus.dto.ModbusDecimalTv;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 修改储能ESS配置信息
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ModifyEssEquipCfgReq {

    /**
     * 上行参数,错误描述
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String error = null;

    /**
     * 上行参数。状态码，0表示成功，其他部表示失败
     */
    @JsonInclude(Include.NON_NULL)
    private Integer status = 0;

    /**
     * 上下行参数
     */
    @JsonInclude(Include.NON_NULL)
    private EssEquipType equipType;

    /**
     * 上下行参数
     */
    @JsonInclude(Include.NON_NULL)
    private String equipDno;

    /**
     * 下行参数
     */
    @JsonInclude(Include.NON_NULL)
    private List<ModbusDecimalTv> tvs;

    public ModifyEssEquipCfgReq(String equipDno, EssEquipType equipType) {
        this.equipDno = equipDno;
        this.equipType = equipType;
    }

    public static class REQ extends BaseMqttMsg<ModifyEssEquipCfgReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.EMU_MODIFY_EQUIP_CFG_REQ;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {

        private REQ req;

        public builder(String equipDno, EssEquipType equipType) {
            this.req = new REQ();
            this.req.setData(new ModifyEssEquipCfgReq(equipDno, equipType));
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setTvs(List<ModbusDecimalTv> tvs) {
            this.req.getData().setTvs(tvs);
            return this;
        }

        public builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }
}
