package com.cdz360.iot.model.comm.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
public class CommercialManageVo {

    private Long comId;

    private String smsOperatorType;

    /**
     * 短信apikey(云片平台apikey或动动客平台UID)
     */
    private String smsApiKey;

    /**
     * 短信平台用户密码(运营商为云片时不填，动动客时必填)
     */
    private String smsApiPwd;
}
