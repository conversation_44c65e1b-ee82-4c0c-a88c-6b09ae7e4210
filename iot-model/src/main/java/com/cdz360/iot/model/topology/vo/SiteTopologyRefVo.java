package com.cdz360.iot.model.topology.vo;

import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname SiteTopologyRefVo
 * @Description
 * @Date 1/20/2021 11:05 AM
 * @Created by Rafael
 */
@Schema(description = "场站拓扑图")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class SiteTopologyRefVo extends SiteTopologyRefPo {
    private String downName;
    private String downNo;
    private Integer power;//设备额定功率

    private List<SiteTopologyRefVo> downList;
}