package com.cdz360.iot.model.parts.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取物料列表查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListPartsTypeParam extends BaseListParam {

    @Schema(description = "物料编号 模糊匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private String codeLike;

    @Schema(description = "物料规格名称 模糊匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private String nameLike;

    @Schema(description = "物料规格 模糊匹配查询")
    @JsonInclude(Include.NON_EMPTY)
    private String modelLike;

}
