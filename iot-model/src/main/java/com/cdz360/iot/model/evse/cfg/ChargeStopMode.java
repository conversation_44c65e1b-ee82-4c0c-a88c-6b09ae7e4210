package com.cdz360.iot.model.evse.cfg;

import com.cdz360.iot.model.base.BaseObject;

import java.util.Objects;

/**
 * @Classname StopMode
 * @Description
 * @Date 2019/6/13 14:59
 * @Created by tangziyu
 */
public class ChargeStopMode extends BaseObject {
    private boolean amount;//是否支持按金额充
    private boolean kwh;//是否支持按电量充
    private boolean time;//是否支持按时间充

    public boolean isAmount() {
        return amount;
    }

    public void setAmount(boolean amount) {
        this.amount = amount;
    }

    public boolean isKwh() {
        return kwh;
    }

    public void setKwh(boolean kwh) {
        this.kwh = kwh;
    }

    public boolean isTime() {
        return time;
    }

    public void setTime(boolean time) {
        this.time = time;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) return false;
        ChargeStopMode that = (ChargeStopMode) obj;
        return Objects.equals(amount, that.amount)
                && Objects.equals(kwh, that.kwh)
                && Objects.equals(time, that.time);
    }
}
