package com.cdz360.iot.model.meter.po;

import com.cdz360.iot.model.meter.type.MeterEstimateType;
import com.cdz360.iot.model.type.MeterRecordParamType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "设备(桩或PCS)-电表绑定关系")
public class DeviceMeterPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	private MeterEstimateType estimateType;

	@NotNull(message = "deviceId 不能为 null")
	@Size(max = 32, message = "deviceId 长度不能超过 32")
	private String deviceId;

	@NotNull(message = "meterId 不能为 null")
	private Long meterId;

	// PCS交流输入对应电表参数
	private MeterRecordParamType pcsAcInput;

	// PCS交流输出对应电表参数
	private MeterRecordParamType pcsAcOutput;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;


}
