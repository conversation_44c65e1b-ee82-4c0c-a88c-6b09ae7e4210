package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

@Data
@Accessors(chain = true)
public class UploadGtiDataFileReq {

    @Schema(description = "需要上传文件的日期,不传值时默认为前一日", example = "2021-09-10")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;

    @Schema(description = "需要上传的设备编号列表,默认上传全部", example = "[\"aaa\"]")
    private List<String> dnoList;

    public static class REQ extends BaseMqttMsg<UploadGtiDataFileReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.UPLOAD_GTI_DATA_FILE;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private REQ req;

        public builder() {
            this.req = new REQ();
            this.req.setData(new UploadGtiDataFileReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public builder setDate(Date date) {
            this.req.getData().setDate(date);
            return this;
        }

        public builder setDnoList(List<String> dnoList) {
            this.req.getData().setDnoList(dnoList);
            return this;
        }

        public REQ build() {
            return this.req;
        }
    }
}
