package com.cdz360.iot.model.evse.param;

import com.cdz360.base.utils.StringUtils;
import lombok.Data;
import org.springframework.util.Assert;

@Data
public class UpgradeStatusParam {

    private Long id;

    private Integer status;

    private Long opId;

    private String opName;

    public void entryCheck() {
        Assert.isTrue(
            id != null && status != null && opId != null && StringUtils.isNotBlank(opName),
            "入参缺失");
    }

}
