package com.cdz360.iot.model.evse;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * @ClassName： EvseBundleDescRequest
 * @Description: 桩升级包自描述
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/16 8:51
 */
@Data
@Accessors(chain = true)
public class EvseBundleContext {
    /**
     * protocol : 1
     * ver : 1
     * pc0xList: [{"type":"PC01","hw":"1","sw":"325","oder":"11","adaptiveList":{"hw":[1,2,3,4],"sw":[321,323,324],"order":[11]}} ,
     * {"type":"PC02","hw":1,"sw":"5","order":"11","adaptiveList":{"hw":[1,2,3,4],"sw":[1,3,4],"order":[11]}} ,
     * {"type":"PC03","hw":104,"sw":"15","order":"11","adaptiveList":{"hw":[101,102],"sw":[9,13,14],"order":[13]}}],
     * releaseNote : "1、新增紧急充电卡功能","2、修复订单异常bug"
     */
    /**
     * 升级包协议
     */
    private Integer protocol;
    /**
     * 升级包版本
     */
    private Long ver;
    /**
     * 硬件版本支持列表
     */
    private List<PC0X> pcList;
    /**
     * 定制信息
     */
    private OrderMsg orderMsg;
    /**
     * 升级要点
     */
    private String releaseNote;

    private String fileName;
    private Long opId;
    private String opName;
    private String context;

    /**
     * 远程文件配置，用于指定升级包下载地址和访问信息
     */
    private EvseBundleRemoteFileConf remoteFileConf;
}
