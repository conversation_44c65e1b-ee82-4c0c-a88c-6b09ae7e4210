package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 获取光伏逆变器信息(MQTT)
 */
@Data
@Accessors(chain = true)
public class GetGtiInfoReq {


    @Schema(description = "逆变器设备编号", example = "ABCD123")
    private List<String> gtiDnoList;


    public static class REQ extends BaseMqttMsg<GetGtiInfoReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.GET_GTI_INFO;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {
        private GetGtiInfoReq.REQ req;

        public builder() {
            this.req = new GetGtiInfoReq.REQ();
            this.req.setData(new GetGtiInfoReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public GetGtiInfoReq.builder setGtiNos(List<String> gtiNos) {
            this.req.getData().setGtiDnoList(gtiNos);
            return this;
        }

        public GetGtiInfoReq.builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }

        public GetGtiInfoReq.REQ build() {
            return this.req;
        }
    }
}
