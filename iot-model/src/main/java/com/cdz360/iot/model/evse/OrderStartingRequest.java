package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
public class OrderStartingRequest extends OrderReportRequest {
    /**
     * 0: 成功
     * 2001: 枪未连接
     * 2002: 枪口已有进行中的订单;
     * 2003: 已经有预约的线下充电
     * 2004: 桩故障
     * 2256: 桩离线
     * 2257: 桩未响应
     * 以网关协议为准
     */
    @Schema(description = "启动充电的返回结果")
    private int startResult;

    public int getStartResult() {
        return startResult;
    }

    public OrderStartingRequest setStartResult(int startResult) {
        this.startResult = startResult;
        return this;
    }
}
