package com.cdz360.iot.model.site.ctrl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname SiteCtrlRes
 * @Description
 * @Date 4/21/2020 3:30 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteCtrlRes<T> extends BaseResponse {
    private String seq;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    T data;

    public SiteCtrlRes(String seq) {
        this.seq = seq;
    }

    public SiteCtrlRes(String seq, T data) {
        this.data = data;
        this.seq = seq;
    }

    public SiteCtrlRes(T data) {
        this.data = data;
    }

    public static <T> SiteCtrlRes<T> success(String seq) {
        return new SiteCtrlRes<>(seq);
    }

    public static <T> SiteCtrlRes<T> success(T data) {
        return new SiteCtrlRes<>(data);
    }

    public static <T> SiteCtrlRes<T> success(String seq, T data) {
        return new SiteCtrlRes<>(seq, data);
    }
}