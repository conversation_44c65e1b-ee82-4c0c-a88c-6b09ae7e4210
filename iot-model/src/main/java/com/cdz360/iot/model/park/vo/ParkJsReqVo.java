package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkJsReqVo
 * @Description
 * @Date 6/18/2024 8:58 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统-捷顺")
public class ParkJsReqVo<T> {
    @JsonProperty(value = "app_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String appId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String method;

    @JsonProperty(value = "sign_type")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String signType;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long timestamp;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String charset;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String format;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String projectCode;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String abilityCode;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sign;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty(value = "biz_content")
    private T bizContent;

}