package com.cdz360.iot.model.site.ctrl;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * @Classname TimeDuring
 * @Description
 * @Date 4/21/2020 8:04 PM
 * @Created by <PERSON>
 */
@Data
public class TimeDuring {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startTime;//时段变化开始时间，格式为 HH:MM
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TempRange> tempRange;//温度范围
}