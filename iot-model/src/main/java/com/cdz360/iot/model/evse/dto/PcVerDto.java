package com.cdz360.iot.model.evse.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "PC板版本号")
@Data
public class PcVerDto {
    @Schema(description = "PC板名称, 如: PC01, PC02-2", example = "PC01")
    private String name;

    @Schema(description = "PC板的软件定制号", example = "32")
    private String vendorCode;

    @Schema(description = "PC板的软件版本号", example = "45")
    private String swVer;

    @Schema(description = "PC板的硬件版本号", example = "67")
    private String hwVer;
}
