package com.cdz360.iot.model.auth;

import com.cdz360.iot.model.base.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 *
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CusAuthResBaseV2 extends BaseObject {
    private BigDecimal amount;
    private String carNo;
    private BigDecimal power;
}