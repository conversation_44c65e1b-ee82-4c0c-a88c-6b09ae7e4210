package com.cdz360.iot.model.base;

import com.cdz360.iot.model.type.StopMode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderCreateResponse extends BaseObject {
    private String orderNo;//订单号. 长度不得超过16位.
    private BigDecimal balance;//账户总可用余额, 单位'元'
    private BigDecimal amount;//可实时扣费金额, 单位'元'

    private StopMode stopMode;//停止方式
    private String carNo;

}
