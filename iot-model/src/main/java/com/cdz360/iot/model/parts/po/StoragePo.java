package com.cdz360.iot.model.parts.po;


import com.cdz360.iot.model.parts.type.StorageType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "仓库")

public class StoragePo {



	@Schema(description = "仓库唯一编码")

	@NotNull(message = "code 不能为 null")

	@Size(max = 16, message = "code 长度不能超过 16")

	private String code;



	@Schema(description = "仓库名称")

	@Size(max = 64, message = "name 长度不能超过 64")

	private String name;



	@Schema(description = "仓库类型:PERSONAL(个人);NORMAL(普通)")

	private StorageType type;



	@Schema(description = "个人仓关联的运维人员ID(普通仓置空)")

	private Long uid;





}

