package com.cdz360.iot.model.meter.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Classname MeterEvseVo
 * @Description
 * @Date 9/21/2020 1:35 PM
 * @Created by Rafael
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterEvseVo extends MeterVo {

    private List<DeviceMeterVo> deviceMeterPoList;
}