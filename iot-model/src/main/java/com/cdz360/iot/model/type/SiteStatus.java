package com.cdz360.iot.model.type;

/**
 * 场站状态
 */
public enum SiteStatus {

    CLOSE(0),       // 已删除(关闭)

    OPENING(1),    // 即将开放, 待上线

    NORMAL(2),     // 正常运营

    HIDE(3),       // 隐藏

    OFF(4),       // 临时关闭, 维护中

    UNKNOWN(99),    // 未知状态
    ;

    private final int value;

    SiteStatus(final int value) {
        this.value = value;
    }

    public static SiteStatus fromValue(int value) {
        for (SiteStatus voStatus : values()) {
            if (voStatus.value == value) {
                return voStatus;
            }
        }
        return null;
    }

    public int getValue() {
        return this.value;
    }
}
