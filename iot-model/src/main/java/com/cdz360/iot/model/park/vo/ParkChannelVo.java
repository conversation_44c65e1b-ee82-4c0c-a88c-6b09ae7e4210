package com.cdz360.iot.model.park.vo;

import com.cdz360.iot.model.park.type.ParkChannelType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站道闸信息查看")
public class ParkChannelVo {

    @Schema(description = "记录ID 本系统内")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "通道编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long channelId;

    @Schema(description = "设备号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceId;

    @Schema(description = "通道类型: 0:入口，1：出口，2出入口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParkChannelType passType;

    @Schema(description = "通道名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelName;
}
