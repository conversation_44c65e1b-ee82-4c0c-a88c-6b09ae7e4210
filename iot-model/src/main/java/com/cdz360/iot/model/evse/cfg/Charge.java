package com.cdz360.iot.model.evse.cfg;

import java.util.List;

public class Charge {
    private Long chargeId;//计费模板ID
    private Long servPrice;//服务费单价
    private List<PriceSchema> priceSchemaList;//价格配置项
    private Integer defaultCode;//默认电价编码. 默认电价编码的值必须要在 priceSchemaList 里存在
    private List<CfgTime> timeList;//时间段. 不传表示全天都使用默认电价

    public Long getChargeId() {
        return chargeId;
    }

    public Charge setChargeId(Long chargeId) {
        this.chargeId = chargeId;
        return this;
    }

    public Long getServPrice() {
        return servPrice;
    }

    public Charge setServPrice(Long servPrice) {
        this.servPrice = servPrice;
        return this;
    }

    public List<PriceSchema> getPriceSchemaList() {
        return priceSchemaList;
    }

    public Charge setPriceSchemaList(List<PriceSchema> priceSchemaList) {
        this.priceSchemaList = priceSchemaList;
        return this;
    }

    public Integer getDefaultCode() {
        return defaultCode;
    }

    public Charge setDefaultCode(Integer defaultCode) {
        this.defaultCode = defaultCode;
        return this;
    }

    public List<CfgTime> getTimeList() {
        return timeList;
    }

    public Charge setTimeList(List<CfgTime> timeList) {
        this.timeList = timeList;
        return this;
    }
}
