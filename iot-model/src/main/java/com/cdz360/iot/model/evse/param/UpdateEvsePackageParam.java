package com.cdz360.iot.model.evse.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.cfg.ChargeV2;
import com.cdz360.iot.model.evse.cfg.WhiteCardV2;
import com.cdz360.iot.model.evse.po.EvsePackagePo;
import com.cdz360.iot.model.vin.po.SiteAuthVinLogPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateEvsePackageParam extends EvsePackagePo {

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
