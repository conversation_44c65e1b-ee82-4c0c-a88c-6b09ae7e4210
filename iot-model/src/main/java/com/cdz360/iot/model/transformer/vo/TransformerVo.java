package com.cdz360.iot.model.transformer.vo;

import com.cdz360.iot.model.topology.vo.SiteTopologyRef;
import com.cdz360.iot.model.topology.vo.SiteTopologyRefVo;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname TransformerVo
 * @Description
 * @Date 1/20/2021 9:09 AM
 * @Created by Rafaelmgm/transformer/list
 */
@Schema(description = "变压器")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class TransformerVo extends TransformerPo implements SiteTopologyRef {
    private String siteName = "";
    private List<SiteTopologyRefVo> downList = List.of();
}