package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusValueOrder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModbusNumberTv extends ModbusAbstractTv {

    /**
     * 有符号/无符号,是否有负数值
     */
    private Boolean sign;

    /**
     * 偏移量
     */
    private Integer shift;

    /**
     * 倍数
     */
    private BigDecimal multiple;

//    /**
//     * big endian
//     */
//    private Boolean bigEndian;

    /**
     * 数值的编码排序
     */
    private ModbusValueOrder order;

//    /**
//     * 寄存器的“高位-低位”对换
//     * <p>部分设备寄存器使用大端编码，但低位的寄存器在前面，高位在后，需要做对换后才能做解析</p>
//     */
//    private Boolean registerSwitch;

    /**
     * 小数位数
     */
    @JsonInclude(Include.NON_NULL)
    private Integer decimal;

    @JsonProperty(value = "min")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal minVal;

    @JsonProperty(value = "max")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal maxVal;
}
