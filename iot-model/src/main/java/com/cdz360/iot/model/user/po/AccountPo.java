package com.cdz360.iot.model.user.po;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "账户信息")
public class AccountPo {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "用户id")
    private long id;

    @Schema(description = "雇员id")
    private long employee_id;

    @Schema(description = "密码")
    private String password;

    public void setPassword(String password) {
        this.password = password;
    }

    public long getId() {
        return id;
    }
}
