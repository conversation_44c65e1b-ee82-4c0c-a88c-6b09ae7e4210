package com.cdz360.iot.model.evse.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电桩密钥")
public class EvsePasscodePo {

    private Long id;

    @Schema(description = "充电桩编号")
    private String evseNo;


    @Schema(description = "密钥版本号")
    private Long ver;


    @Schema(description = "HEX编码的密钥")
    private String passcode;


    @Schema(description = "是否有效")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "最后更新时间")
    private Date updateTime;


}
