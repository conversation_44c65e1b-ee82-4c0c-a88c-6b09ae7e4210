package com.cdz360.iot.model.evse;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;

public class OrderStopRequestV1 extends OrderReportRequest {
    @Schema(description = "充电完成原因. 0 为正常完成, 其他都为非正常完成")
    private long stopCode;//
    @Schema(description = "账号")
    private String accountNo;
    @Schema(description = "开启方式:0x01: 紧急充电卡订单 0x02: 无卡启动 0x11: 在线卡启动 0x12: VIN码启动 0x21: 管理后台 0x22: 批量启动 0x31: 微信公众号 0x32: 微信小程序 0x33: iOS APP 0x34: 安卓 APP")
    private OrderStartType startType;
    @Schema(description = "当前累计电量, 单位'万分之千瓦时'. 如 34567表示 3.4567kwh")
    private Integer kwh;//
    @Schema(description = "当前累计电费金额, 单位'分'")
    private Integer elecFee;//
    @Schema(description = "当前累计服务费金额, 单位'分'")
    private Integer servFee;//
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    @Schema(description = "分段计费开始时间, unix时间戳")
    private Date startTime;//
    //@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "s")
    @JsonDeserialize(using = CustomDateDeserializer.class)
    @JsonSerialize(using = CustomDateSerializer.class)
    @Schema(description = "分段计费结束时间, unix时间戳")
    private Date stopTime;//
    @Schema(description = "车架号")
    private String vin;//
    @Schema(description = "充电结束时的soc")
    private Integer soc;//
    @Schema(description = "订单可用余额, 单位'分'")
    private long balance;//
    @Schema(description = "电价模板编号")
    private Integer priceCode;//
    @Schema(description = "账单详情")
    private List<OrderDetail> detail;
    @Schema(description = "充电开始前电表读数, 单位'0.01 KWH'")
    private Integer startMeter;
    @Schema(description = "充电完成后电表读数, 单位'0.01 KWH'")
    private Integer stopMeter;

    public Integer getStartMeter() {
        return startMeter;
    }

    public void setStartMeter(Integer startMeter) {
        this.startMeter = startMeter;
    }

    public Integer getStopMeter() {
        return stopMeter;
    }

    public void setStopMeter(Integer stopMeter) {
        this.stopMeter = stopMeter;
    }

    public long getStopCode() {
        return stopCode;
    }

    public OrderStopRequestV1 setStopCode(long stopCode) {
        this.stopCode = stopCode;
        return this;
    }

    public Integer getKwh() {
        return kwh;
    }

    public OrderStopRequestV1 setKwh(Integer kwh) {
        this.kwh = kwh;
        return this;
    }

    public Integer getElecFee() {
        return elecFee;
    }

    public OrderStopRequestV1 setElecFee(Integer elecFee) {
        this.elecFee = elecFee;
        return this;
    }

    public Integer getServFee() {
        return servFee;
    }

    public OrderStopRequestV1 setServFee(Integer servFee) {
        this.servFee = servFee;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public OrderStopRequestV1 setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getStopTime() {
        return stopTime;
    }

    public OrderStopRequestV1 setStopTime(Date stopTime) {
        this.stopTime = stopTime;
        return this;
    }

    public String getVin() {
        return vin;
    }

    public OrderStopRequestV1 setVin(String vin) {
        this.vin = vin;
        return this;
    }

    public Integer getSoc() {
        return soc;
    }

    public OrderStopRequestV1 setSoc(Integer soc) {
        this.soc = soc;
        return this;
    }

    public long getBalance() {
        return balance;
    }

    public OrderStopRequestV1 setBalance(long balance) {
        this.balance = balance;
        return this;
    }

    public Integer getPriceCode() {
        return priceCode;
    }

    public OrderStopRequestV1 setPriceCode(Integer priceCode) {
        this.priceCode = priceCode;
        return this;
    }

    public List<OrderDetail> getDetail() {
        return detail;
    }

    public OrderStopRequestV1 setDetail(List<OrderDetail> detail) {
        this.detail = detail;
        return this;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public OrderStartType getStartType() {
        return startType;
    }

    public void setStartType(OrderStartType startType) {
        this.startType = startType;
    }

}
