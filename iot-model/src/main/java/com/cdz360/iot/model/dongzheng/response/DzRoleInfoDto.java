package com.cdz360.iot.model.dongzheng.response;


import java.io.Serializable;
import java.util.List;

public class DzRoleInfoDto implements Serializable {
    private Long id;
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private String createTime;
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private String updateTime;
    private Long createBy;
    private String email;
    private String name;
    private String phone;
    private List<Long> roleIdList;
    private List<DzRoleDto> roleList;
    private Long status;
    private String username;
    private String lastLoginTime;
    private List<DzPositionDto> positionList;
    private DzOrgDto org;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<Long> getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(List<Long> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public List<DzRoleDto> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<DzRoleDto> roleList) {
        this.roleList = roleList;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public List<DzPositionDto> getPositionList() {
        return positionList;
    }

    public void setPositionList(List<DzPositionDto> positionList) {
        this.positionList = positionList;
    }

    public DzOrgDto getOrg() {
        return org;
    }

    public void setOrg(DzOrgDto org) {
        this.org = org;
    }
}
