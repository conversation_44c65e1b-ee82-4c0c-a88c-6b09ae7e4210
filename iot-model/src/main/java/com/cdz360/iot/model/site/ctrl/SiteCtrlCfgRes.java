package com.cdz360.iot.model.site.ctrl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class SiteCtrlCfgRes {
    @JsonIgnore
    private String ctrlNum;
    private Boolean pwrCtrl;//功率分配功能开关
    private Boolean infoUp;//监测信息上报开关
    private Boolean pwrLoadAlm;//功率负荷报警开关
    private Boolean pwrTempAlm;//配电柜温度报警开关
    private Boolean chgFireAlm;//充电桩烟雾报警开关
    private Boolean chgDoorAlm;//充电桩门禁报警开关
    private Integer pwrCap;//'配电容量功率分配功能为开时必须发送
    private List<PowerCtrlLmt> pwrCtrlLmt;//JSON功率限定策略功率分配功能为开时必须发送
    private Integer tempSample;//配电柜温度信息采样周期（秒钟）监测信息上报为开时必须发送
    private Integer pwrSample;//功率负载采样周期（秒钟）监测信息上报为开时必须发送
    private Integer infoUpLoop;//监测信息上报时间间隔（秒钟）监测信息上报为开时必须发送
    private Integer pwrLoadLmt;//负载率报警阈值功率负荷报警为开时必须发送
    private Integer pwrTempLmt;//温度报警阈值配电柜温度报警为开时必须发送
//    private Integer loadRatio;//最新负载率
//    private Integer pwrTemp;//最新配电柜温度
}
