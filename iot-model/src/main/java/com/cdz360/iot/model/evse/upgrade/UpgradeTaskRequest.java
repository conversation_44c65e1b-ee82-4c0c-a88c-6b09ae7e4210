package com.cdz360.iot.model.evse.upgrade;

import com.cdz360.iot.model.evse.po.PackageInfoItem;
import lombok.Data;

import java.util.List;

/**
 * @Classname UpgradeTaskRequest
 * @Description TODO
 * @Date 9/21/2019 10:33 AM
 * @Created by <PERSON>
 */
@Data
public class UpgradeTaskRequest {
    String siteId;//场站id
    List<String> evseIds;//桩编号列表
    Long bundleId;//升级包id
    Long opId;//操作者id(不要传，传了也没用)
    String OpName;//操作者名字(不要传，传了也没用)
    Long taskId;//这个参数用于再升级失败的桩

    private List<PackageInfoItem> packageInfo;//升级包详细链接等信息(海外版专属)
}