package com.cdz360.iot.model.modbus.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * modbus数据编码排序方式
 */
@Getter
public enum ModbusValueOrder implements DcEnum {

    UNKNOWN(0),

    AABB(1),    // 2字节大端, AA*256 + BB
    BBAA(2),    // 2字节小端, AA*256 + BB
    AABBCC(3),  // 3字节
    CCBBAA(4),  // 3字节
    AABBCCDD(10),      // 4字节
    BBAADDCC(11),    // 4字节
    CCDDAABB(12),     // 4字节
    DDCCBBAA(13),    // 4字节
    AABBCCDDEEFFGGHH(14),   // 8字节

    ;

    @JsonValue
    final int code;

    ModbusValueOrder(int code) {
        this.code = code;
    }


    @JsonCreator
    public static ModbusValueOrder valueOf(Object codeIn) {
        if (codeIn == null) {
            return ModbusValueOrder.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ModbusValueOrder) {
            return (ModbusValueOrder) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ModbusValueOrder type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ModbusValueOrder.UNKNOWN;
    }
}
