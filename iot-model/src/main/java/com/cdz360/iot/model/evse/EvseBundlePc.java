package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName： EvseBundlePc
 * @Description: 升级包上的各个PC板详情
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:25
 */
@Schema(description = "com.cdz360.iot.model.evse.EvseBundlePc")
@Data
@Accessors(chain = true)
public class EvseBundlePc implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 关联的包id. t_evse_bundle.id
     */
    @Schema(description = "关联的包id. t_evse_bundle.id")
    private Long bundleId;
    /**
     * 模块类型,如PC01,PC02,PC01-1
     */
    @Schema(description = "模块类型,如PC01,PC02,PC01-1")
    private String pcName;
    /**
     * 升级后的硬件版本号
     */
    @Schema(description = "升级后的硬件版本号")
    private Integer hwVer;
    /**
     * 升级后的软件版本号
     */
    @Schema(description = "升级后的软件版本号")
    private Integer swVer;
    /**
     * 升级后的定制版本号
     */
    @Schema(description = "升级后的定制版本号")
    private Integer vendorCode;
    /**
     * PC01下载地址, 不含协议和域名部分
     */
    @Schema(description = "PC01下载地址, 不含协议和域名部分")
    private String path;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "升级适配版本")
    private PC0X.Adaptive adaptive;
}