package com.cdz360.iot.model.meter.vo;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.meter.po.MeterPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname MeterVo
 * @Description
 * @Date 9/21/2020 9:36 AM
 * @Created by Rafael
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterVo extends MeterPo {
    @Schema(description = "绑定桩个数")
    private Integer bindEvseCount;

    private String siteName;

    private String deviceModel;

    private EssEquipType type;

}