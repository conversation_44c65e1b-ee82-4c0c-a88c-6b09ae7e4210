package com.cdz360.iot.model.park.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkCouponVo
 * @Description
 * @Date 4/23/2021 3:22 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券")
public class ParkCouponVo {
    private int parkOrderId;
    private String siteId;
    private String carNo;
    private Long parkId;//宜泊-ParkCode、宁停车-parkCode
    private String parkSignKey;
    private String parkOrderNo;
    private int duration;//单位：分钟
    private String ISP; // 宁停车-充电桩企业名称

    private String parkAppId;// 用于中控、(宜泊-partnerid)接口、(宁停车-Appkey)接口
    private String parkAppSecret;// 用于中控、(宜泊-签名Key)接口、(宁停车-secret)接口
    private String parkType;// IST艾视特，ZK中控，YB宜泊，JS捷顺，NTC宁停车

    private String parkConfig;// 停车、道闸自定义配置：json

}