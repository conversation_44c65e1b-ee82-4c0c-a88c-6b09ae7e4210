package com.cdz360.iot.model.park.param;

import com.cdz360.base.model.base.type.PlugStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "枪头状态变更事件参数")
@Data
@Accessors(chain = true)
public class PlugStatusEventLogParam {

    @Schema(description = "桩在云平台的唯一ID", required = true)
    @JsonInclude(Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "充电枪ID", required = true)
    @JsonInclude(Include.NON_NULL)
    private Integer plugId;

    @Schema(description = "枪头状态", required = true)
    @JsonInclude(Include.NON_NULL)
    private PlugStatus status;

    @Schema(description = "充电订单号 充电时需要提供充电订单号")
    @JsonInclude(Include.NON_EMPTY)
    private String orderNo;
}
