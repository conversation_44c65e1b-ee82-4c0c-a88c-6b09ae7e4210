package com.cdz360.iot.model.park.po;


import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "地锁")

public class ParkingLockPo {



	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "设备供应商: 未知(UNKNOWN)，安效停(ANNEFFI)")

	private ParkingLockPartner partner;



	@Schema(description = "供应商地锁ID(交互使用字段)")

	@NotNull(message = "serialNumber 不能为 null")

	@Size(max = 32, message = "serialNumber 长度不能超过 32")

	private String serialNumber;


	@Schema(description = "车位编号")
	@Size(max = 32, message = "positionCode 长度不能超过 32")
	private String positionCode;

	@Schema(description = "地锁状态: 未知(UNKNOWN)，平台离线(PLATFORM_OFFLINE)，" +
			"正常空闲(NORMAL)，有车开锁状态(OPEN_CAR_IN)，无车开锁状态(OPEN_NOT_CAR)，故障(ERROR)，被远程断电(CUT_POWER)")

	private ParkingLockStatus status;



	@Schema(description = "供应商地锁uuid")

	@Size(max = 32, message = "devUuid 长度不能超过 32")

	private String devUuid;



	@Schema(description = "地锁类型")

	@Size(max = 32, message = "type 长度不能超过 32")

	private String type;



	@Schema(description = "地锁电量(%)-直接供电是100%")

	private BigDecimal electricQuantity;



	@Schema(description = "地锁所在场所ID")
	@Size(max = 64, message = "parkingLotId 长度不能超过 32")

	private String parkingLotId;



	@Schema(description = "地锁所在场所名称")

	@Size(max = 128, message = "parkingLotName 长度不能超过 128")

	private String parkingLotName;



	@Schema(description = "地锁在场所的编号")

	@Size(max = 32, message = "parkingSpaceCode 长度不能超过 32")

	private String parkingSpaceCode;



	@Schema(description = "占用车牌号")

	@Size(max = 32, message = "carNo 长度不能超过 32")

	private String carNo;

	@Schema(description = "桩编号")
	private String evseNo;

	@Schema(description = "充电枪ID")
	private Integer plugId;

	@Schema(description = "供应商地锁元数据")
	private String srcDetail;

	@Schema(description = "异常信息")
	private String errorMsg;

	private Date createTime;



	private Date updateTime;





}

