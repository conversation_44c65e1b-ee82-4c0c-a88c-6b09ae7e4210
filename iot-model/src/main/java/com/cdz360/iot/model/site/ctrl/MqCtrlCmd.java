package com.cdz360.iot.model.site.ctrl;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.iot.model.type.SiteCtrlCmdType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname MqCtrlCmd
 * @Description
 * @Date 4/21/2020 11:17 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MqCtrlCmd<T> extends BaseObject {
    private int v;//网关协议版本号
    private String seq;//请求消息中的 seq
    private SiteCtrlCmdType cmd;
    private String n;//控制器编号
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private T data;

    public SiteCtrlCmdType getCmd() {
        if(this.cmd != null) {
            return this.cmd;
        } else {
            String[] pkgList = this.getClass().getName().split("\\.");
            String className = pkgList[pkgList.length - 1];
            final String PrefixRe = "^Mq";
            final String SuffixRe = "Cmd$";
            String cmdCore = className.replaceFirst(PrefixRe, "").replaceFirst(SuffixRe, "");
            String cmdRet = String.join("_", cmdCore.split("(?=\\p{Upper})"));
            this.setCmd(SiteCtrlCmdType.valueOf(cmdRet.toUpperCase()));
            return this.cmd;
        }
    }
}