package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * IEEE 754 标准的浮点数
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModbusIeee754Tv extends ModbusDecimalTv {

    @JsonIgnore
    public static List<Integer> TYPES = List.of(ModbusDataType.IEEE754.getCode());

    private BigDecimal v;
}
