package com.cdz360.iot.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

@Schema(description = "云端获取桩配置")
public class EvseGetCfgRequest extends BaseObject {
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String type = "REQ";

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String seq;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String method = "EVSE_GET_CFG";

    @Schema(description = "需要获取配置的桩ID数组, 桩ID在云平台的唯一")
    private List<String> evseIds;


    public String getType() {
        return type;
    }

    public EvseGetCfgRequest setType(String type) {
        this.type = type;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public EvseGetCfgRequest setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public EvseGetCfgRequest setMethod(String method) {
        this.method = method;
        return this;
    }

    public List<String> getEvseIds() {
        return evseIds;
    }

    public EvseGetCfgRequest setEvseIds(List<String> evseIds) {
        this.evseIds = evseIds;
        return this;
    }


}
