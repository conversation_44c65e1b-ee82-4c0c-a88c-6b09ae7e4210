package com.cdz360.iot.model.parts.type;

public enum PartsOpType {
    STORE_2_SEND("总库发货"),
    RECEIVE_("签收物料"),
    APPLY_4_STORE("申请调拨"),
    APPROVE_AND_SEND("同意调拨"),
    STORE_2_EVSE("物料被使用"),
    EVSE_2_STORE("物料被拆卸"),
    STATUS_MODIFY("状态修改"),
    STORE_ROLLBACK("物料退回总部"),
    STORE_BROKEN("报废");

    private final String desc;

    PartsOpType(String desc) {
        this.desc = desc;
    }
}
