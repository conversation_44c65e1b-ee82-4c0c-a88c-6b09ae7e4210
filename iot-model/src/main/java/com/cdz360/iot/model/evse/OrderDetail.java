package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;

public class OrderDetail {
    @Schema(description = "分段计费开始时间, unix时间戳",example = "1557111811")
    private Long startTime;
    @Schema(description = "分段计费结束时间, unix时间戳",example = "1557111811")
    private Long stopTime;
    @Schema(description = "价格分段编号")
    private Integer priceCode;
    @Schema(description = "当前累计电量, 单位'万分之千瓦时'. 如 34567表示 3.4567kwh",example = "34567")
    private BigDecimal kwh;
    @Schema(description = "当前累计电费金额, 单位'分'")
    private BigDecimal elecFee;
    @Schema(description = "当前累计服务费金额, 单位'分'")
    private BigDecimal servFee;

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getStopTime() {
        return stopTime;
    }

    public void setStopTime(Long stopTime) {
        this.stopTime = stopTime;
    }

    public Integer getPriceCode() {
        return priceCode;
    }

    public void setPriceCode(Integer priceCode) {
        this.priceCode = priceCode;
    }

    public BigDecimal getKwh() {
        return kwh;
    }

    public void setKwh(BigDecimal kwh) {
        this.kwh = kwh;
    }

    public BigDecimal getElecFee() {
        return elecFee;
    }

    public void setElecFee(BigDecimal elecFee) {
        this.elecFee = elecFee;
    }

    public BigDecimal getServFee() {
        return servFee;
    }

    public void setServFee(BigDecimal servFee) {
        this.servFee = servFee;
    }
}
