package com.cdz360.iot.model.user.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(description = "员工信息")
public class EmployeePo {
    @Schema(description = "员工信息记录ID")
    private long id = 0L;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "email地址")
    private String email;


    @Schema(description = "状态. 0:离职;1:在职")
    private int status;

    @Schema(description = "最后更新时间")
    private Date updateTime;

    public long getId() {
        return id;
    }

    public EmployeePo setId(long id) {
        this.id = id;
        return this;
    }

    public String getPhone() {
        return phone;
    }

    public EmployeePo setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    public String getName() {
        return name;
    }

    public EmployeePo setName(String name) {
        this.name = name;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public EmployeePo setEmail(String email) {
        this.email = email;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public EmployeePo setStatus(int status) {
        this.status = status;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public EmployeePo setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}
