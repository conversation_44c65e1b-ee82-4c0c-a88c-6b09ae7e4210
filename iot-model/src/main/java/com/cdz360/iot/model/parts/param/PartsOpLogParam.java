package com.cdz360.iot.model.parts.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "物料操作日志参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsOpLogParam extends PartsOpBaseParam {

    @Schema(description = "入库或出库标识 true(入库);false(出库)")
    @JsonInclude(Include.NON_NULL)
    private Boolean inStorage;

    @Schema(description = "运维工单单号")
    @JsonInclude(Include.NON_EMPTY)
    private String ywOrderNo;
}
