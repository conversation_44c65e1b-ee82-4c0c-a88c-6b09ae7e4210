package com.cdz360.iot.model.site.po;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.site.ctrl.PowerCtrlLmt;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname SiteCtrlPo
 * @Description
 * @Date 4/22/2020 3:54 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场站控制器配置模板")
public class SiteCtrlCfgPo extends BaseObject {
    @Schema(description = "记录Id值", hidden = true)
    private Long id;

    @Schema(description = "场站控制器编号")
    private String ctrlNum;

    @Schema(description = "功率分配功能开关")
    private Boolean pwrCtrl;//功率分配功能开关

    @Schema(description = "监测信息上报开关")
    private Boolean infoUp;//监测信息上报开关

    @Schema(description = "功率负荷报警开关")
    private Boolean pwrLoadAlm;//功率负荷报警开关

    @Schema(description = "配电柜温度报警开关")
    private Boolean pwrTempAlm;//配电柜温度报警开关

    @Schema(description = "充电桩烟雾报警开关")
    private Boolean chgFireAlm;//充电桩烟雾报警开关

    @Schema(description = "充电桩门禁报警开关")
    private Boolean chgDoorAlm;//充电桩门禁报警开关

    @Schema(description = "配电容量功率分配功能为开时必须发送")
    private Integer pwrCap;//'配电容量功率分配功能为开时必须发送

    @Schema(description = "JSON功率限定策略功率分配功能为开时必须发送")
    private List<PowerCtrlLmt> pwrCtrlLmt;//JSON功率限定策略功率分配功能为开时必须发送

    @Schema(description = "配电柜温度信息采样周期（秒钟）监测信息上报为开时必须发送")
    private Integer tempSample;//配电柜温度信息采样周期（秒钟）监测信息上报为开时必须发送

    @Schema(description = "功率负载采样周期（秒钟）监测信息上报为开时必须发送")
    private Integer pwrSample;//功率负载采样周期（秒钟）监测信息上报为开时必须发送

    @Schema(description = "监测信息上报时间间隔（秒钟）监测信息上报为开时必须发送")
    private Integer infoUpLoop;//监测信息上报时间间隔（秒钟）监测信息上报为开时必须发送

    @Schema(description = "负载率报警阈值功率负荷报警为开时必须发送")
    private Integer pwrLoadLmt;//负载率报警阈值功率负荷报警为开时必须发送

    @Schema(description = "温度报警阈值配电柜温度报警为开时必须发送")
    private Integer pwrTempLmt;//温度报警阈值配电柜温度报警为开时必须发送

//    @Schema(description = "最新负载率")
//    private BigDecimal loadRatio;//最新负载率
//
//    @Schema(description = "最新配电柜温度")
//    private BigDecimal pwrTemp;//最新配电柜温度

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

//    private PowerCtrlLmt powerCtrlLmt;
//
//    private String getPwrCtrlLmt() {
//        if(powerCtrlLmt == null) return null;
//        return powerCtrlLmt.toJsonString();
//    }
//
//    private void setPwrCtrlLmt(String startJon) {
//        pwrCtrlLmt = startJon;
//        powerCtrlLmt = JsonUtils.fromJson(startJon, PowerCtrlLmt.class);
//    }
}