package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "升级包上下文信息")
@Data
@Accessors(chain = true)
public class UpgradePgContext {

    @Schema(description = "协议")
    private Integer protocol;

    @Schema(description = "版本")
    private Long ver;

    @Schema(description = "升级包子项信息")
    private List<SubItem> subItems;

    @Schema(description = "支持版本信息")
    private String support;

    @Data
    @Accessors(chain = true)
    public static class SubItem {

        @Schema(description = "命名")
        private String name;

        @Schema(description = "当前升级包版本")
        private String ver;

        @Schema(description = "升级支持MTU大小，单位: 字节")
        private Integer mtuSize;

        @Schema(description = "升级包中文件路径")
        private String binName;
    }
}
