package com.cdz360.iot.model.site.po;

import com.cdz360.base.model.charge.type.SiteDynamicPowerType;
import com.cdz360.iot.model.type.SiteStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 对应数据库表 chargerlink_charger.t_site
 */
@Schema(description = "场站信息")
@Data
@Accessors(chain = true)
public class SitePo {

//private String idno;

    @Schema(description = "场站ID")
    private Long id;

    @Schema(description = "场站名字")
    private String name;

    @Schema(description = "场站自编号")
    private String siteNo;

    @Schema(description = "场站的集团商户ID")
    private Long topCommId;


    @Schema(description = "场站的商户ID")
    private Long commId;

    @Schema(description = "场站类型: 0-未知 1-公共 2-公交 3-物流 4-混合")
    private Integer type;

    @Schema(title = "运营属性. 0,未知; 1,自营; 2,非自营; 3,互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private Integer bizType;

    @Schema(description = "经度")
    private BigDecimal lon;

    @Schema(description = "纬度")
    private BigDecimal lat;

    @Schema(title = "时区", description = "如: +8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String timeZone;

    @Schema(description = "场站地址")
    private String address;

    @Schema(description = "服务号码")
    private String phone;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "地区编码")
    private String areaCode;

    @Schema(description = "站点状态. 0、已删除， 1、待上线  2：已上线  3、已隐藏 4：维护中")
    private SiteStatus status;

    @Schema(description = "价格模板ID")
    private Long priceCode;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据创建时间, UNIX时间戳", format = "java.lang.Long")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据最后更新时间, UNIX时间戳", format = "java.lang.Long")
    private Date updateTime;


    @Schema(description = "东正数据库对应场站的ID")
    private String siteId;

    //TODO 先这么改
    @Deprecated
    @Schema(description = "东正数据库对应场站的ID-待修改")
    private String dzId;

    @Schema(description = "交流桩数量")
    private Integer acEvseNum;

    @Schema(description = "直流桩数量")
    private Integer dcEvseNum;

    @Schema(description = "交流枪头数量")
    private Integer acPlugNum;

    @Schema(description = "直流枪头数量")
    private Integer dcPlugNum;

    @Schema(description = "交流总功率,单位kw")
    private Integer acPower;

    @Schema(description = "直流总功率,单位kw")
    private Integer dcPower;

    @Schema(description = "车场编号 道闸")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long parkId;

    @Schema(description = "场站功率分配模式")
    private SiteDynamicPowerType dyPow;
}
