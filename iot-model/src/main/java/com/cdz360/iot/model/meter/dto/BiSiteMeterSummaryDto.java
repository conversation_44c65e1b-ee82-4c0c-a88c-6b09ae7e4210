package com.cdz360.iot.model.meter.dto;

import com.cdz360.iot.model.meter.po.BiMeterSumPo;
import com.cdz360.iot.model.type.MeterRecordParamType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Classname BiSiteMeterSummaryDto
 * @Description
 * @Date 11/8/2021 9:41 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class BiSiteMeterSummaryDto {
    private Long meterId;
    private String meterName;
    private MeterRecordParamType paramType;
    private List<BiMeterSumPo> biSummaryList;
}