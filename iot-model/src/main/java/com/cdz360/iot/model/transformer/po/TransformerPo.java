package com.cdz360.iot.model.transformer.po;

import com.cdz360.iot.model.base.Update;
import com.cdz360.iot.model.transformer.vo.OrderlyCapVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "变压器")
public class TransformerPo {

	@NotNull(groups = {Update.class}, message = "变压器ID不能为空")
	private Long id;

	@Schema(description = "编号")
	@NotNull(message = "场站编号 不能为 null")
	@Size(max = 20, message = "场站编号 长度不能超过 20")
	private String no;

	@Schema(description = "设备名称")
	@Size(max = 20, message = "设备名称 长度不能超过 20")
	private String name;

	@Schema(description = "场站id")
	@NotNull(message = "场站id 不能为 null")
	private String siteId;

	@Schema(description = "容量（kVA）")
	private BigDecimal capacity;

	@Schema(description = "有序充电可分配容量（kW）")
	private List<OrderlyCapVo> orderlyCaps;

	@Schema(description = "可分配容量（kW）")
	private BigDecimal assignableCap;

	@Schema(description = "输入电压（kV）")
	private BigDecimal inputVoltage;

	@Schema(description = "输出电压（kV）")
	private BigDecimal outputVoltage;

//	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	private Date updateTime;


}
