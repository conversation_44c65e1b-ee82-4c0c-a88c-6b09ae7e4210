package com.cdz360.iot.model.evse;

import lombok.Data;

import java.util.List;


/**
 * @ClassName： PC0X
 * @Description: 版本支持描述
 * @Email: <EMAIL>
 * @Author: JLEI 
 * @CreateDate: 2019/9/16 8:52
 */
@Data
public class PC0X {
    /**
     * type: PC01,PC01_1,PC01_2,PC02,PC03
     * hw : 104
     * sw : 15
     * oder : 11
     * adaptiveList : {"hw":[101,102],"sw":[9,13,14],"oder":[13]}
     */
    private String binName;
    private String type;
    private Integer hw;
    private Integer sw;
    private Integer order;
    private Adaptive adaptive;

    // 文件远程实际地址，一般不用填，当自描述文件json存在remoteFileConf的字段时，应填写
    private String remoteFilePath;

    @Data
    public class Adaptive {
        private List<Integer> hw;
        private List<Integer> sw;
        private List<Integer> order;
    }
}
