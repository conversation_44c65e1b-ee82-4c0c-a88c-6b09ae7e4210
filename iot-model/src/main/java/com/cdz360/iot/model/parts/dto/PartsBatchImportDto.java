package com.cdz360.iot.model.parts.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "无聊批量导入参数")
@Data
@Accessors(chain = true)
public class PartsBatchImportDto {

    // ==== 操作人信息 ====
    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Long opUid;

    @Schema(description = "物料项列表")
    @JsonInclude(Include.NON_NULL)
    private List<PartsImportItem> items;
}
