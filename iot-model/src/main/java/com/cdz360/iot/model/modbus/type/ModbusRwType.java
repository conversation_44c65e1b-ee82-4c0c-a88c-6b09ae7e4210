package com.cdz360.iot.model.modbus.type;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum ModbusRwType {
    R,  // 只读
    W,  // 只写
    RW  // 读写
    ;

    @JsonCreator
    public static ModbusRwType valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        if (codeIn instanceof ModbusRwType) {
            return (ModbusRwType) codeIn;
        } else if (codeIn instanceof String) {
            String str = (String) codeIn;
            if ("R".equalsIgnoreCase(str)) {
                return ModbusRwType.R;
            } else if ("W".equalsIgnoreCase(str)) {
                return ModbusRwType.W;
            } else if ("RW".equalsIgnoreCase(str)) {
                return ModbusRwType.RW;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
}
