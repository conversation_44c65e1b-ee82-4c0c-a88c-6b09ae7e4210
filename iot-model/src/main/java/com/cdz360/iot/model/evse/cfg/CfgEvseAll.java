package com.cdz360.iot.model.evse.cfg;

import com.cdz360.iot.model.base.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Classname CfgEvseAll
 * @Description
 * @Date 2019/6/17 14:43
 * @Created by tangziyu
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CfgEvseAll extends BaseObject {
    private String evseId;//桩在云平台的唯一ID
    private String adminCodeA;//桩端管理员登录密码, 数字型, 6~8位. 不传表示不做修改
    private String adminCodeB;//桩端二级管理员登录密码,数字型, 6~8位. 不传表示不做修改
    private Boolean vin;//是否支持 VIN 码充电. 不传表示不做修改
    private String qrUrl;//桩端显示的二维码 URL. 不传表示不做修改
    private List<WhiteCard> whiteCards;//紧急充电卡列表. 不传表示不做修改
    private EvseCfgEnum bmsVer;//BMS协议.
    private Boolean autoStop;//是否自动停充.
    private EvseCfgEnum balanceMode;//均/轮充模式
    private Boolean combination;//合充开关
    private Boolean heating;//辅电手动切换开关
    private Boolean batteryCheck;//电池反接检测开关
    private EvseCfgEnum isolation;//绝缘检测类型
    private Boolean manualMode;//手动充电开关
    private Boolean queryChargeRecord;//是否支持充电记录查询. 不传表示不做修改
    private Boolean qrCharge;//是否支持扫码充电. 不传表示不做修改
    private Boolean cardCharge;//是否支持刷卡充电. 不传表示不做修改
    private Boolean noCardCharge;//是否支持无卡充电. 不传表示不做修改
    private Boolean timedCharge;//是否支持定时充电. 不传表示不做修改
    private Integer dayVolume;//白天音量. 不传表示不做修改
    private Integer nightVolume;//夜间音量. 不传表示不做修改
    private ChargeStopMode stopMode;//充电停止方式, 不传表示不做变更
    private Charge charge;//计费方案. 不传表示不做修改
}
