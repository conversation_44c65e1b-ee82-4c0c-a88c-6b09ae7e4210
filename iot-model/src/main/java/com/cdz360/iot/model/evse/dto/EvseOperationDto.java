package com.cdz360.iot.model.evse.dto;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.model.evse.type.AvailabilityType;
import com.cdz360.iot.model.evse.type.MessageTrigger;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(Include.NON_NULL)
public class EvseOperationDto {

    private Integer plugId;

    /**
     * 获取配置
     */
    private List<String> getConfigKeyList;

    /**
     * 更改配置
     */
    private List<ConfigurationVo> changeConfigVoList;

    /**
     * 获取诊断日志
     */
    private GetDiagnosticsVo getDiagnosticsVo;

    /**
     * 变更桩可用性
     */
    private AvailabilityType availability;

    /**
     * 清除缓存
     */
    private Boolean clearCache;

    /**
     * 触发消息
     */
    private MessageTrigger messageTrigger;

    /**
     * 解锁连接器
     */
    private Boolean unlockConnector;

    public boolean validate() {
        return CollectionUtils.isNotEmpty(getConfigKeyList) || CollectionUtils.isNotEmpty(
            changeConfigVoList) || getDiagnosticsVo != null || availability != null
            || clearCache != null || messageTrigger != null || unlockConnector != null;
    }

    @Data
    public static class ConfigurationVo {

        private String key;

        private String value;
    }

    @Data
    public static class GetDiagnosticsVo {

        /**
         * 例如：https://test01-commercial-manage.iot.renwochong.com/ant/api/ocpp/diagnostics/upload/{evseNo}
         */
        private String location;

        private Integer retries = 3;

        private Integer retryInterval = 10;

        private Date startTime;

        private Date stopTime;
    }

}
