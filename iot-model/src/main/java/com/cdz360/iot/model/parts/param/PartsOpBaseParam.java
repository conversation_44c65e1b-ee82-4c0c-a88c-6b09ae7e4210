package com.cdz360.iot.model.parts.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "物料操作基础类")
@Data
@Accessors(chain = true)
public class PartsOpBaseParam {

    @Schema(description = "操作人ID")
    @JsonInclude(Include.NON_NULL)
    private Long opUid;

    @Schema(description = "物料ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> codeList;

    @Schema(description = "物料ID")
    @JsonInclude(Include.NON_EMPTY)
    private String code;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
