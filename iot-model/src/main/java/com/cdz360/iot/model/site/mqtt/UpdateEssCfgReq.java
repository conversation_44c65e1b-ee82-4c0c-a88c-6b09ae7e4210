package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.site.mqtt.ModifyEssCfgReq.builder;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

@Data
@Accessors(chain = true)
public class UpdateEssCfgReq {


    private Long cfgId;

    private String cfgNo;


    public static class REQ extends BaseMqttMsg<UpdateEssCfgReq> {

        @JsonProperty("c")
        @Override
        public IotGwCmdType2 getCmd() {
            return IotGwCmdType2.MODIFY_ESS_CFG;
        }

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    public static class builder {

        private UpdateEssCfgReq.REQ req;

        public builder() {
            this.req = new UpdateEssCfgReq.REQ();
            this.req.setData(new UpdateEssCfgReq());
            this.req.setSeq(RandomStringUtils.randomAlphanumeric(16));
        }

        public UpdateEssCfgReq.builder setCfgId(Long cfgId) {
            this.req.getData().setCfgId(cfgId);
            return this;
        }

        public UpdateEssCfgReq.builder setCfgNo(String cfgNo) {
            this.req.getData().setCfgNo(cfgNo);
            return this;
        }

        public UpdateEssCfgReq.builder setGwno(String gwno) {
            req.setGwno(gwno);
            return this;
        }
        public UpdateEssCfgReq.REQ build() {
            return this.req;
        }
    }
}
