package com.cdz360.iot.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.type.SiteBiSampleType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname SiteBiParam
 * @Description
 * @Date 11/8/2021 9:44 AM
 * @Created by Rafael
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(description = "场站数据统计请求参数")
public class SiteBiParam extends BaseListParam {

    @Schema(description = "场站id")
    private String siteId;

//    @Schema(description = "商户ID链")
//    private String commIdChain;

    @Schema(description = "采样时间，按小时、天、月（故障统计时，不需要传小时）", required = true)
    private SiteBiSampleType sampleType;

    @Schema(description = "开始时间（毫秒）", required = true)
    private Long startTime;

    @Schema(description = "结束时间（毫秒）", required = true)
    private Long endTime;

//    @Schema(description = "统计节点，创建时间、充电开始时间、充电结束时间、支付时间", required = true)
//    private SiteBiDependOnOrderTimeType dependOnTimeType;


    @Schema(description = "用于mybatis中查询，等同于 startTime")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fromTime;

    @Schema(description = "用于mybatis中查询，等同于 endTime")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date toTime;

    @Schema(description = "场站列表,用于报表统计")
    private List<String> siteIdList;

    @Schema(description = "企业ID列表,用于报表统计")
    private List<Long> corpIdList;

//    @Schema(description = "统计内容:总电量,尖、峰、平、谷、电量,总费用，电费,服务费", required = true)
//    private ReportBiType biContent;

    @Schema(description = "开始页码", required = true)
    private Long start;

//    @Schema(description = "excel位置")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private ExcelBiPosition excelPosition;

    @Schema(description = "sheet名称", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sheetName;

    @Schema(description = "是否包含互联互通场站 null or true 表示包含; false 表示不包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean includedHlhtSite;

    private Integer current;

    private  Integer size;

    /**
     * 排序规则
     */
    private  String sort = "DESC" ;


    /**
     * 根据入参采样类型调整开始/结束时间
     */
    public void resetTime() {
        Date from = new Date(this.startTime);
        Date to = new Date(this.endTime);

        // 分秒都为0
        LocalDateTime fromTime = from.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);
        LocalDateTime toTime = to.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
                sampleType == SiteBiSampleType.MONTH) {
            fromTime = fromTime.withHour(0);
            toTime = toTime.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            fromTime = fromTime.with(TemporalAdjusters.firstDayOfMonth());
            toTime = toTime.with(TemporalAdjusters.firstDayOfMonth());
        }

        startTime = fromTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        endTime = toTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        this.fromTime = new Date(startTime);
        this.toTime = new Date(endTime);
    }

    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    public static List<LocalDateTime> reviseResult(SiteBiParam param) {
        if (null == param.getFromTime() || null == param.getToTime()) {
            param.resetTime();
        }

        LocalDateTime start = param.getFromTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if(param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        return timeList;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}