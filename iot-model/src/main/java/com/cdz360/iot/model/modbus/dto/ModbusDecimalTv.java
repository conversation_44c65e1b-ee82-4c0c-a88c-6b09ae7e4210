package com.cdz360.iot.model.modbus.dto;

import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * type = ModbusDataType.DECIMAL(8)
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModbusDecimalTv extends ModbusNumberTv {
    @JsonIgnore
    public static List<Integer> TYPES = List.of(
            ModbusDataType.DECIMAL.getCode(),
            ModbusDataType.INT32.getCode(),
            ModbusDataType.INT16.getCode()
    );

    private BigDecimal v;
}
