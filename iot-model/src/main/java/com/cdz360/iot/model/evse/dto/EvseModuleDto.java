package com.cdz360.iot.model.evse.dto;

import com.cdz360.iot.model.evse.vo.EvseModuleVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class EvseModuleDto {

    @Schema(description = "比较结果 1:两边一致;" +
            "2:桩上报的数量 > 平台录入的模块数量;" +
            "3:平台录入的模块数量 > 桩上报的模块数量;" +
            "4:桩上报的模块型号与平台设置的不一致;" +
            "5:桩上报的数量 > 平台录入的模块数量，且模块型号两边不一致;" +
            "6:平台录入的模块数量 > 桩上报的模块数量，且模块型号两边不一致;")
    private Integer compareResult;

    @Schema(description = "桩上报的模块型号(compareResult为4、5、6时有值)")
    private String reportModuleType;

    @Schema(description = "模块信息")
    private List<EvseModuleVo> moduleVoList;

}
