package com.cdz360.iot.model.camera.vo;

import com.cdz360.iot.model.camera.po.CameraPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname CameraVo
 * @Description
 * @Date 7/29/2021 3:04 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "摄像设备")
@EqualsAndHashCode(callSuper = true)
public class CameraVo extends CameraPo {
    @Schema(description = "云眸账号访问token")
    private String accountToken;

    @Schema(description = "取流认证过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accountExpireTime;

    @Schema(description = "萤石云子账号访问token")
    private String SubAccessToken;

    @Schema(description = "萤石云主账号访问token")
    private String accessToken;

    @Schema(description = "相机所属场站id")
    private String siteId;

    @Schema(description = "相机所属场站名")
    private String siteName;

    @Schema(description = "对应录像机的序列号")
    private String recordDeviceSerial;

    @Schema(description = "摄像头序列号")
    private String ipcSerial;
}