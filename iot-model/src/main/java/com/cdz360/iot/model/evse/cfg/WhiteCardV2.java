package com.cdz360.iot.model.evse.cfg;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WhiteCardV2 {

    @Schema(description = "物理卡号, 对用户可见")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String visibleNumber;

    @Schema(description = "逻辑卡号, 对用户不可见, 仅用于鉴权")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardNumber;

    @Schema(description = "充电密码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passcode;
}
