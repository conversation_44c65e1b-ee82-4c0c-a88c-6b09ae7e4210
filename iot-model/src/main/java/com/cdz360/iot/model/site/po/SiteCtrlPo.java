package com.cdz360.iot.model.site.po;

import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Classname SiteCtrlPo
 * @Description
 * @Date 4/22/2020 3:54 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class SiteCtrlPo {
    private Long id;
    private String num;
    private String siteId;
    private String name;
    private SiteCtrlStatusType status;
    private String passcode;
    private Integer protocolVer;
    private String swVer;
    private String lanIp;
    private String mac;
    private Boolean enable;
    private Integer loadRatio;//最新负载率
    private Integer pwrTemp;//最新配电柜温度
    private Date loginTime;
    private Date createTime;
    private Date updateTime;
}