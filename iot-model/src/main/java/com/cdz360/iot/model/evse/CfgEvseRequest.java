package com.cdz360.iot.model.evse;

import com.cdz360.iot.model.base.BaseGwPackage;
import com.cdz360.iot.model.type.GwRequestMethod;

public class CfgEvseRequest extends BaseGwPackage {
    private GwRequestMethod method;
    private String evseId;
    private String cfgVer;

    public GwRequestMethod getMethod() {
        return method;
    }

    public void setMethod(GwRequestMethod method) {
        this.method = method;
    }

    public String getEvseId() {
        return evseId;
    }

    public void setEvseId(String evseId) {
        this.evseId = evseId;
    }

    public String getCfgVer() {
        return cfgVer;
    }

    public void setCfgVer(String cfgVer) {
        this.cfgVer = cfgVer;
    }
}
