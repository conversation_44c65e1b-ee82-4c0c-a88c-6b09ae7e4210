package com.cdz360.iot.model.evse;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.iot.model.type.StopMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 桩端发起充电请求
 *
 * <AUTHOR>
 * @date Create on 2019/04/19
 */
@Schema(description = "桩端发起充电请求")
@Data
@Accessors(chain = true)
public class ChgEvseRequestV2 {
    // 桩编号
    private String evseNo;
    // 卡号 (逻辑卡号)或17位VIN 码
    private String accountNo;
    // 停止方式
    @Schema(description = "停充方式", example = "")
    private StopMode stopMode;
    //充电枪Id
    private Integer plugId;
    //开启方式:
    //0x11: 在线卡
    //0x12: 车架号
    private OrderStartType startType;

}
