package com.cdz360.iot.model.evse.vo;

import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseCfgResultVo extends EvseCfgResultPo {

    @Schema(description = "场站Id")
    private String siteId;

    @Schema(description = "场站名称")
    private String siteName;
}
