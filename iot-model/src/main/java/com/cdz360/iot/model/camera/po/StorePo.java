package com.cdz360.iot.model.camera.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Deprecated
@Schema(description = "海康云眸门店信息")
@Data
@Accessors(chain = true)
public class StorePo {
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "门店ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeId;

    @Schema(description = "门店名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeName;

    @Schema(description = "门店面积")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeMeasure;

    @Schema(description = "门店详细地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeDetailAddress;

    @Schema(description = "省")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String addressProvince;

    @Schema(description = "市")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String addressCity;

    @Schema(description = "区")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String addressCounty;

    @Schema(description = "街道地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String addressDetail;

    @Schema(description = "店长姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mangerName;

    @Schema(description = "门店电话")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeTel;

    @Schema(description = "店长手机号码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mangerTel;

    @Schema(description = "门店编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeNo;

    @Schema(description = "门店经度")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeLng;

    @Schema(description = "门店纬度")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeLat;

    @Schema(description = "门店描述")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String storeRemark;

    @Schema(description = "门店区域组织路径")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String areaPath;

    @Schema(description = "数据入库时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String insertTime;

    @Schema(description = "数据更新时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String updateTime;
}
