package com.cdz360.iot.model.param;

import com.cdz360.iot.model.type.MeterRecordParamType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Classname MeterRecordBiParam
 * @Description  @Date 11/8/2021 11:25 AM
 * @Created by Rafael
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(description = "电表数据统计请求参数")
public class MeterRecordBiParam extends SiteBiParam  {
    private List<Long> meterIdList;
    private List<MeterRecordParamType> paramTypeList;
}