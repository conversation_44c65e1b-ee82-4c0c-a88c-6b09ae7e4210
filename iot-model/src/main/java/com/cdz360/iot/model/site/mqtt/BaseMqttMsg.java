package com.cdz360.iot.model.site.mqtt;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;

@Data
@Accessors(chain = true)
public abstract class BaseMqttMsg<T> {
    @JsonProperty("n")
    private String gwno;
    @JsonProperty("s")
    private String seq;//请求消息中的 seq


    @JsonProperty("d")
    private T data;

    @JsonProperty("c")
    public abstract IotGwCmdType2 getCmd();


    // TODO： 兼容旧版网关代码，2021.11版本后移除
    @JsonProperty("seq")
    public String getSeqX() {
        return this.seq;
    }

    public BaseMqttMsg() {
        // default
        this.seq = RandomStringUtils.randomAlphanumeric(16);
    }

//    public BaseMqttMsg(T data) {
//        this.data = data;
//        this.seq = RandomStringUtils.randomAlphanumeric(16);
//    }

    public BaseMqttMsg(String gwno, T data) {
        this.gwno = gwno;
        this.data = data;
        this.seq = RandomStringUtils.randomAlphanumeric(16);
    }
}
