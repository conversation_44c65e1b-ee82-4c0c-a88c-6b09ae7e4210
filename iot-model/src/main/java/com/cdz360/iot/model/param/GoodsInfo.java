package com.cdz360.iot.model.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单使用配件(物品)")
@Data
@Accessors(chain = true)
public class GoodsInfo {

    @Schema(description = "其它器件")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OtherDevice> otherDeviceList;

    @Schema(description = "充电模块")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<chargingModule> chargingModuleList;

    @Data
    @Accessors(chain = true)
    public static class OtherDevice {

        @Schema(description = "配件名称(物品名称)")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String name;

        @Schema(description = "配件数量(物品数量)")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer num;
    }


    @Data
    @Accessors(chain = true)
    public static class chargingModule {

        @Schema(description = "序列")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer idx;

        @Schema(description = "退回到上一次")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Boolean rollback;

        @Schema(description = "现原材编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String newDeviceNo;

        @Schema(description = "旧原材编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String oldDeviceNo;

        @Schema(description = "桩编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String evseNo;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}