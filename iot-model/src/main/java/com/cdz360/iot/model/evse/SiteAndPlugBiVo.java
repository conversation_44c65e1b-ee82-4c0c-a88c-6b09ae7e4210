package com.cdz360.iot.model.evse;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname SiteAndPlugBiVo
 * @Description
 * @Date 6/23/2020 2:12 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain = true)
@Schema(description = "首页-充电站和枪头监控信息")
public class SiteAndPlugBiVo {

    @Schema(description = "充电站数量")
    private Integer siteCount = 0;

    @Schema(description = "总功率")
    private BigDecimal power = BigDecimal.ZERO;

    @Schema(description = "枪头数量")
    private Integer plugCount = 0;

    @Schema(description = "直流枪头数")
    private Integer dcPlugCount = 0;

    @Schema(description = "交流枪头数")
    private Integer acPlugCount = 0;

    @Schema(description = "电桩数量")
    private Integer evseCount = 0;

    @Schema(description = "枪头充电中数量")
    private Integer plugChargingCount = 0;

    @Schema(description = "枪头完成数量")
    private Integer plugChargeEndCount = 0;

    @Schema(description = "枪头空占数量")
    private Integer plugConnectedCount = 0;

    @Schema(description = "枪头空闲数量")
    private Integer plugIdleCount = 0;

    @Schema(description = "枪头离线数量")
    private Integer plugOfflineCount = 0;

    @Schema(description = "枪头故障数量")
    private Integer plugErrorCount = 0;

}