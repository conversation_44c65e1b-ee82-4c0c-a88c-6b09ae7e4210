package com.cdz360.iot.model.modbus.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ModbusAddrRange {

    private int addr;
    private String addr16;
    private String name;
    private int num;
    private int rate;

    private Byte readCmd;
    private Byte writeSingleCmd;
    private Byte writeMultipleCmd;
    private String desc;

    /**
     * 是否强制通知
     */
    private Boolean forceNotify;
}
