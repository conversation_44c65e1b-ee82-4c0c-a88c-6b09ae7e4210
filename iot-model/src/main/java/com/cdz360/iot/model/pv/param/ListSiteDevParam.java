package com.cdz360.iot.model.pv.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListSiteDevParam extends BaseListParam {
    @Schema(description = "地址或编号 指控制器编号，或是逆变器ID，或是逆变器地址，或是储能SN，或是储能下设备ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String devNo;

    @Schema(description = "设备名称 可以是控制器名称，也可以是逆变器名称，或是储能名称，或是储能下设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String devName;

    @Schema(description = "配置模板名称 光伏逆变器模板，或是储能ESS模板")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tempName;

    @Schema(description = "品牌名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vendor;

    @Schema(description = "型号名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceModel;

}
