package com.cdz360.iot.model.evse.dto;

import com.cdz360.iot.model.evse.PC0X;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @ClassName： PC0XDto
 * @Description: 版本支持描述
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/16 8:52
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PC0XDto extends PC0X {

    private String adaptiveHws;
    private String adaptiveSws;
    private String adaptiveOders;
    private String path;
}
