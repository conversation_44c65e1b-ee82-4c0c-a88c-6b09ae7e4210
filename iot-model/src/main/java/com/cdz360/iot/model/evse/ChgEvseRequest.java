package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.iot.model.type.StopMode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;

/**
 * 桩端发起充电请求
 *
 * <AUTHOR>
 * @date Create on 2019/04/19
 */
@Schema(description = "桩端发起充电请求")
public class ChgEvseRequest extends OrderReportRequest {
    // 开启方式
    private OrderStartType startType;

    @Schema(description = "桩名称(9月版本新增字段)")
    private String evseName;

    @Schema(description = "枪头名称(9月版本新增字段)")
    private String plugName;

    @Schema(description = "场站ID(9月版本新增字段)")
    private String siteId;

    @Schema(description = "场站直属商户ID(9月版本新增字段)")
    private Long siteCommId;

    @Schema(description = "当前绑定的计费模板ID(9月版本新增字段)")
    private Long priceCode;

    @Schema(description = "电流类型(9月版本新增字段)")
    private SupplyType supplyType;

     // 卡号 (逻辑卡号)或17位VIN 码
    private String accountNo;
    // 停止方式
    @Schema(description = "停充方式", example = "")
    private StopMode stopMode;
    // 可用金额
    private BigDecimal balance;
    private Long payAccountId;
    private Long userId;
    private Integer defaultPayType;
    private BigDecimal frozenAmount;
    private EvseProtocolType evseProtocolType;

    @Schema(description = "是否为后付费标识: true/false")
    private Boolean postPaid; // 是否为后付费标识: true/false

    public OrderStartType getStartType() {
        return startType;
    }

    public ChgEvseRequest setStartType(OrderStartType startType) {
        this.startType = startType;
        return this;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public ChgEvseRequest setAccountNo(String accountNo) {
        this.accountNo = accountNo;
        return this;
    }

    public StopMode getStopMode() {
        return stopMode;
    }

    public ChgEvseRequest setStopMode(StopMode stopMode) {
        this.stopMode = stopMode;
        return this;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public ChgEvseRequest setBalance(BigDecimal balance) {
        this.balance = balance;
        return this;
    }

    public Long getPayAccountId() {
        return payAccountId;
    }

    public ChgEvseRequest setPayAccountId(Long payAccountId) {
        this.payAccountId = payAccountId;
        return this;
    }

    public Integer getDefaultPayType() {
        return defaultPayType;
    }

    public ChgEvseRequest setDefaultPayType(Integer defaultPayType) {
        this.defaultPayType = defaultPayType;
        return this;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public ChgEvseRequest setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public ChgEvseRequest setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public EvseProtocolType getEvseProtocolType() {
        return evseProtocolType;
    }

    public void setEvseProtocolType(EvseProtocolType evseProtocolType) {
        this.evseProtocolType = evseProtocolType;
    }

    public Boolean getPostPaid() {
        return postPaid;
    }

    public void setPostPaid(Boolean postPaid) {
        this.postPaid = postPaid;
    }


    public String getEvseName() {
        return evseName;
    }

    public ChgEvseRequest setEvseName(String evseName) {
        this.evseName = evseName;
        return this;
    }

    public String getPlugName() {
        return plugName;
    }

    public ChgEvseRequest setPlugName(String plugName) {
        this.plugName = plugName;
        return this;
    }

    public String getSiteId() {
        return siteId;
    }

    public ChgEvseRequest setSiteId(String siteId) {
        this.siteId = siteId;
        return this;
    }

    public Long getSiteCommId() {
        return siteCommId;
    }

    public ChgEvseRequest setSiteCommId(Long siteCommId) {
        this.siteCommId = siteCommId;
        return this;
    }

    public Long getPriceCode() {
        return priceCode;
    }

    public ChgEvseRequest setPriceCode(Long priceCode) {
        this.priceCode = priceCode;
        return this;
    }

    public SupplyType getSupplyType() {
        return supplyType;
    }

    public ChgEvseRequest setSupplyType(SupplyType supplyType) {
        this.supplyType = supplyType;
        return this;
    }
}
