package com.cdz360.iot.model.evse.po;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PlugStatus;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 充电枪状态变更日志
 */
@Data
@Accessors(chain = true)
public class PlugLogPo {

    private Long id;
    private String siteId;
    private String evseNo;
    private String plugNo;
    private IotEvent eventType;
    private PlugStatus plugStatus;
    private Integer errorCode;
    private LocalDateTime createTime;

}
