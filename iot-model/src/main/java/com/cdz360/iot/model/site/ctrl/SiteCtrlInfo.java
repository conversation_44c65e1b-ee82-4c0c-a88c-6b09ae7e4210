package com.cdz360.iot.model.site.ctrl;

import com.cdz360.iot.model.site.vo.SiteCtrlCfgVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * @Classname SiteCtrlInfo
 * @Description
 * @Date 4/21/2020 7:57 PM
 * @Created by Rafael
 */
@Data
@Schema(description = "控制器上报配置")
public class SiteCtrlInfo {
    // 上报控制器配置，redis中存放key值
    public static int REDIS_DDL = 10; // 分钟
    private static String REDIS_PRE = "iot:ctrl:cfg:";
    @Schema(description = "功率分配功能开关")
    private boolean pwrCtrl;//功率分配功能开关
    @Schema(description = "监测信息上报开关")
    private boolean infoUp;//监测信息上报开关
    @Schema(description = "功率负荷报警开关")
    private boolean pwrLoadAlm;//功率负荷报警开关
    @Schema(description = "配电柜温度报警开关")
    private boolean pwrTempAlm;//配电柜温度报警开关
    @Schema(description = "充电桩烟雾报警开关")
    private boolean chgFireAlm;//充电桩烟雾报警开关
    @Schema(description = "充电桩门禁报警开关")
    private boolean chgDoorAlm;//充电桩门禁报警开关
    @Schema(description = "配电容量,功率分配功能为开时必须发送")
    private Integer pwrCap;//配电容量,功率分配功能为开时必须发送
    @Schema(description = "功率限定策略,功率分配功能为开时必须发送")
    private List<PowerCtrlLmt> pwrCtrlLmt;//功率限定策略,功率分配功能为开时必须发送
    @Schema(description = "配电柜温度信息采样周期（秒钟）,监测信息上报为开时必须发送")
    private Integer tempSample;//配电柜温度信息采样周期（秒钟）,监测信息上报为开时必须发送
    @Schema(description = "/功率负载采样周期（秒钟）,监测信息上报为开时必须发送")
    private Integer pwrSample;//功率负载采样周期（秒钟）,监测信息上报为开时必须发送
    @Schema(description = "监测信息上报时间间隔（秒钟）,监测信息上报为开时必须发送")
    private Integer infoUpLoop;//监测信息上报时间间隔（秒钟）,监测信息上报为开时必须发送
    @Schema(description = "负载率报警阈值,功率负荷报警为开时必须发送")
    private Integer pwrLoadLmt;//负载率报警阈值,功率负荷报警为开时必须发送
    @Schema(description = "温度报警阈值,配电柜温度报警为开时必须发送")
    private Integer pwrTempLmt;//温度报警阈值,配电柜温度报警为开时必须发送

    public static String redisKey(String ctrlNo) {
        return REDIS_PRE + ctrlNo;
    }

    // 转换使用
    public static SiteCtrlCfgVo map2Vo(SiteCtrlInfo info) {
        SiteCtrlCfgVo result = new SiteCtrlCfgVo();
        BeanUtils.copyProperties(info, result);
        return result;
    }
}