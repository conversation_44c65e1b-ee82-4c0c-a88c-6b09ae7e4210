package com.cdz360.iot.model.evse.vo;

import com.cdz360.iot.model.evse.po.EvsePackagePo;
import com.cdz360.iot.model.evse.po.PackageInfoItem;
import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.EvseVendor;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 海外平台升级包
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=true)
public class EvsePackageVo extends EvsePackagePo {

}