package com.cdz360.iot.model.site.dto;

import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.iot.model.order.type.SiteCtrlCfgStatusType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SiteCtrlDto {

    private Long id;

    private String num;

    private String name;

    private SiteCtrlStatusType status;

    private String passcode;

    private Date loginTime;

    private Date createTime;

    private String swVer;

    private String protocolVer;

    private Integer loadRatio;//最新负载率

    private Integer pwrTemp;//最新配电柜温度

    private Integer pwrCap;//配电容量

    private BigDecimal pwrLoadLmt;//负载率报警阈值

    private BigDecimal pwrTempLmt;//温度报警阈值

    private SiteCtrlCfgStatusType cfgStatus;//配置下发状态
}
