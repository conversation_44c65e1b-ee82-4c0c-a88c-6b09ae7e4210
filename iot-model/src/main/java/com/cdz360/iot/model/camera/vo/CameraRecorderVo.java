package com.cdz360.iot.model.camera.vo;

import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname CameraRecorderVo
 * @Description
 * @Date 9/22/2021 1:26 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "硬盘录像机设备")
@EqualsAndHashCode(callSuper = true)
public class CameraRecorderVo extends CameraRecorderPo {
    private String siteId;
    private String siteName;
    private List<CameraVo> cameraList;
}