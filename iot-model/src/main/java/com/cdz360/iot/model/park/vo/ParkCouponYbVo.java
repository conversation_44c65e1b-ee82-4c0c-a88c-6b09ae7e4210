package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkCouponYbVo
 * @Description
 * @Date 1/10/2022 11:02 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-宜泊科技平台")
public class ParkCouponYbVo {
    @JsonProperty(value = "parkcode")
    private String parkCode;// 停车场编号

    @JsonProperty(value = "carno")
    private String carNo;// 车牌号

    @JsonProperty(value = "appId")
    private String appId;// 卡号或者软件id

    @JsonProperty(value = "isAddUseFlage")
    private Integer isAddUseFlage;// 是否可叠加使用的标志, 0：不可叠加, 1：可叠加

    @JsonProperty(value = "freehours")
    private Integer freehours;// 免费时间（单位:小时）, 30,如果抵扣类型是金额，这个字段传0

    @JsonProperty(value = "freeMoney")
    private BigDecimal freeMoney;// 免费金额（单位:元）, 10.00，如果抵扣类型是时间，这个字段传0

    @JsonProperty(value = "type")
    private Integer type;// 抵扣类型, 0：金额 1:时间 2：全免

    @JsonProperty(value = "storeName")
    private String storeName;// 商户名称
}