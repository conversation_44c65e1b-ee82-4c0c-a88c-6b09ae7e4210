package com.cdz360.iot.model.site.ctrl;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @Classname TempRange
 * @Description
 * @Date 4/21/2020 8:05 PM
 * @Created by Rafael
 */
@Data
public class TempRange {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer startTemp;//温度范围变化开始温度
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer limitPower;//限定功率
}