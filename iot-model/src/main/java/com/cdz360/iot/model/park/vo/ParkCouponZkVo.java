package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkCouponZkVo
 * @Description
 * @Date 12/24/2021 4:25 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-中控")
public class ParkCouponZkVo {

    @JsonProperty(value = "grant:mcoupon:quantity")
    private ParkCouponZkMetaVo meta;
//	"metadata": {
//        "grant:mcoupon:quantity": {
//            "name": "string",
//                    "value": 3
//        }
//    },
    @JsonProperty(value = "api_store_code")
    private String apiStoreCode; // 62626601
    @JsonProperty(value = "event_id")
    private String eventId; // DC000001
    @JsonProperty(value = "api_type")
    private String apiType; // DINGCHONG
    @JsonProperty(value = "subject")
    private String subject; // 123
    @JsonProperty(value = "plate")
    private String plate; // 粤BXXXXX
    @JsonProperty(value = "app_id")
    private String appId; // op1b4ee929b109a4
}