package com.cdz360.iot.model.dongzheng.request;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname SysLeveAlarmMsgRequest
 * @Description 系统级别告警请求（微服务DOWN掉 网关登陆超时）
 * <AUTHOR>
 * @Date 2019/8/20 17:23
 * @Email <EMAIL>
 */
@Data
public class SysLeveAlarmReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 告警码
     */
    String warningCode;

    /**
     * 微服务异常告警
     */
    List<AppAlert> alerts;

    @Data
    @AllArgsConstructor
    public static class AppAlert {
        private String appName;
        private String instanceId;
        private String message;
    }
}

