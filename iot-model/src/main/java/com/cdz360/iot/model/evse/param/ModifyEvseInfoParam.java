package com.cdz360.iot.model.evse.param;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.evse.po.PlugPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ModifyEvseInfoParam {

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "桩编号")
    private String evseNo;

    private List<String> evseNoList;

    @Schema(description = "桩名称")
    private String name;

    @Schema(description = "设备型号ID")
    private Long modelId;

    @Schema(description = "桩额定功率", example = "123")
    private Integer power;

    // 海外版使用时，用的是带后缀的比如-A
    @Schema(description = "桩型号", example = "TN-QCZ02-A10")
    private String model;

    @Schema(description = "电流类型", example = "AC")
    private SupplyType supplyType;

    @Schema(description = "桩协议")
    private String protocolVer;

    @Schema(description = "软件版本")
    private String firmwareVer;

    @Schema(description = "桩出厂日期", example = "2020-01-01")
    private Date produceDate;

    @Schema(description = "质保到期日", example = "2020-01-01")
    private Date expireDate;

    @Schema(description = "桩出厂编号", example = "T17223584")
    private String produceNo;

    @Schema(description = "桩铭牌编号", example = "T17223584")
    private String physicalNo;

    @Schema(description = "SIM卡号,找到t_sim记录填补到t_evse中")
    private String iccid;

    @Schema(description = "imei，自动填补")
    private String imei;

    @Schema(description = "imsi，自动填补")
    private String imsi;

    @Schema(description = "模块类型")
    private String moduleType;

//    @Schema(description = "模块数量")
//    private Integer moduleNum;

    @Schema(description = "槽位数")
    private Integer slotNum;

    @Schema(description = "是否使用场站默认设置", example = "true")
    private Boolean useSiteSetting;

    private List<PlugPo> plugVoList;

    // 目前仅海外版使用
    @Schema(description = "品牌")
    private String brand;

    // 目前仅海外版使用
    @Schema(description = "电桩类型")
    private SupplyType supply;
}
