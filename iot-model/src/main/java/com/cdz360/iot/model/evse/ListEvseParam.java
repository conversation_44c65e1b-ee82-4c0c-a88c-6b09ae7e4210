package com.cdz360.iot.model.evse;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.lang.Nullable;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListEvseParam extends BaseListParam {

    @Nullable
    private List<String> siteIdList;
    @Schema(description = "桩名称或桩编号")
    private String keywords;
    @Schema(description = "桩状态列表")
    private List<EvseStatus> evseStatusList;
    @Schema(description = "桩类型")
    private SupplyType supplyType;
    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "是否获取枪头列表", example = "false")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean withPlugList;

    @Schema(description = "变压器ID", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tfmId;

    @Schema(description = "运营状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseBizStatus> bizStatusList;

}
