package com.cdz360.iot.model.dongzheng.response;

public class DzSiteDto extends DzBaseDto {
    private String address;
    private String brandIds;
    private Integer city;
    private String cityName;
    private Integer countOfBox;
    private Integer countOfCharger;
    private Integer distance;
    private Integer feeMax;
    private Integer feeMin;
    private String images;
    private String location;
    private Integer operateId;
    private String operateName;
    private Integer park;
    private String parkFee;
    private Integer province;
    private String provinceName;
    private String remark;
    private Integer scope;
    private String serviceHolidayTime;
    private String serviceWorkdayTime;
    private String siteId;
    private String siteName;
    private Integer status;
    private Integer type;


    // Getter Methods

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getCountOfBox() {
        return countOfBox;
    }

    public void setCountOfBox(Integer countOfBox) {
        this.countOfBox = countOfBox;
    }

    public Integer getCountOfCharger() {
        return countOfCharger;
    }

    public void setCountOfCharger(Integer countOfCharger) {
        this.countOfCharger = countOfCharger;
    }

    public Integer getDistance() {
        return distance;
    }

    public void setDistance(Integer distance) {
        this.distance = distance;
    }

    public Integer getFeeMax() {
        return feeMax;
    }

    public void setFeeMax(Integer feeMax) {
        this.feeMax = feeMax;
    }

    public Integer getFeeMin() {
        return feeMin;
    }

    public void setFeeMin(Integer feeMin) {
        this.feeMin = feeMin;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getOperateId() {
        return operateId;
    }

    public void setOperateId(Integer operateId) {
        this.operateId = operateId;
    }

    public String getOperateName() {
        return operateName;
    }

    // Setter Methods

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Integer getPark() {
        return park;
    }

    public void setPark(Integer park) {
        this.park = park;
    }

    public String getParkFee() {
        return parkFee;
    }

    public void setParkFee(String parkFee) {
        this.parkFee = parkFee;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    public String getServiceHolidayTime() {
        return serviceHolidayTime;
    }

    public void setServiceHolidayTime(String serviceHolidayTime) {
        this.serviceHolidayTime = serviceHolidayTime;
    }

    public String getServiceWorkdayTime() {
        return serviceWorkdayTime;
    }

    public void setServiceWorkdayTime(String serviceWorkdayTime) {
        this.serviceWorkdayTime = serviceWorkdayTime;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
