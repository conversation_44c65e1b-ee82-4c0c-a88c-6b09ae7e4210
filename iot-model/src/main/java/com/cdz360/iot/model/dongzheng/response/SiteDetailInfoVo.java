package com.cdz360.iot.model.dongzheng.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class SiteDetailInfoVo implements Serializable {
    /**
     * 站点ID
     */
    private String id;
    /**
     * 站点编号
     */
    private String idNo;
    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 站点地址
     */
    private String address;
    /**
     * 省份编码
     */
    private Integer province;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市编码
     */
    private Integer city;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区域编码
     */
    private Integer area;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 服务号码
     */
    private String phone;
    /**
     * 站点状态{@link com.cdz360.iot.business.constant.SiteStatusEnum}
     */
    private Integer status;
    /**
     * 运营商ID
     */
    private Long operateId;
    /**
     * 运营商名称
     */
    private String operateName;
    /**
     * 物业ID或代理商账号ID
     */
    private Long merchantId;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系人号码
     */
    private String contactsPhone;
    /**
     * 备注
     */
    private String remark;
    /**
     * 站点类型**0-未知 1-公共 2-个人 3-运营**
     */
    private Integer type;
    /**
     * 工作日服务时间
     */
    private String serviceWorkdayTime;
    /**
     * 节假日服务时间
     */
    private String serviceHolidayTime;
    /**
     * 停车是否收费 **0-未知 1-收费 2-免费**
     */
    private Integer park;
    /**
     * 停车费
     */
    private String parkFee;
    /**
     * 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
     */
    private Integer appoint;
    /**
     * 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
     */
    private Integer scope;
    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onlineDate;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 站点图片
     */
    private String images;
    /**
     * 支持品牌
     */
    private String brandIds;
    /**
     * 支付方式
     */
    private String payMode;
    /**
     * 充电接口状态信息统计: key-充电接口状态{@link com.cdz360.iot.common.constant.ConnectorStatusEnum} value-充电接口数量
     */
    private Map<String, Integer> chargerStatusMap;
    /**
     * 收费说明
     */
    private String feeDescription;
    /**
     * 站点收费范围 最低
     */
    private Integer feeMin;
    /**
     * 站点收费范围 最高
     */
    private Integer feeMax;
    /**
     * 距离,单位：米
     */
    private Double distance;
    /**
     * 时长，单位：秒
     */
    private Double duration;

    /**
     * 站点默认计费模板Id
     */
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    private String templateName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getOperateId() {
        return operateId;
    }

    public void setOperateId(Long operateId) {
        this.operateId = operateId;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getServiceWorkdayTime() {
        return serviceWorkdayTime;
    }

    public void setServiceWorkdayTime(String serviceWorkdayTime) {
        this.serviceWorkdayTime = serviceWorkdayTime;
    }

    public String getServiceHolidayTime() {
        return serviceHolidayTime;
    }

    public void setServiceHolidayTime(String serviceHolidayTime) {
        this.serviceHolidayTime = serviceHolidayTime;
    }

    public Integer getPark() {
        return park;
    }

    public void setPark(Integer park) {
        this.park = park;
    }

    public String getParkFee() {
        return parkFee;
    }

    public void setParkFee(String parkFee) {
        this.parkFee = parkFee;
    }

    public Integer getAppoint() {
        return appoint;
    }

    public void setAppoint(Integer appoint) {
        this.appoint = appoint;
    }

    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    public Date getOnlineDate() {
        return onlineDate;
    }

    public void setOnlineDate(Date onlineDate) {
        this.onlineDate = onlineDate;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public Map<String, Integer> getChargerStatusMap() {
        return chargerStatusMap;
    }

    public void setChargerStatusMap(Map<String, Integer> chargerStatusMap) {
        this.chargerStatusMap = chargerStatusMap;
    }

    public String getFeeDescription() {
        return feeDescription;
    }

    public void setFeeDescription(String feeDescription) {
        this.feeDescription = feeDescription;
    }

    public Integer getFeeMin() {
        return feeMin;
    }

    public void setFeeMin(Integer feeMin) {
        this.feeMin = feeMin;
    }

    public Integer getFeeMax() {
        return feeMax;
    }

    public void setFeeMax(Integer feeMax) {
        this.feeMax = feeMax;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
}
