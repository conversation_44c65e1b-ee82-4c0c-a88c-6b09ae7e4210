package com.cdz360.iot.model.evse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "计费模板下发场站信息")
public class PriceSchemeSiteVo {
    @Schema(description = "场站Id")
    private String siteId;

    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "计费模板下发状态: 0, 成功; 300, 下发中; 301, 超时;")
    private Integer status;
}
