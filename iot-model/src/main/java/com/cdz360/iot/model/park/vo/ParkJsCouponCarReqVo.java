package com.cdz360.iot.model.park.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname ParkJsCouponCarReqVo
 * @Description
 * @Date 6/18/2024 9:55 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统-停车系统-捷顺3C类接口属性")
public class ParkJsCouponCarReqVo {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String carNo;
    private String parkCode;
    private Integer couponType;//优惠类型，0：减免金额，1：减免时间，2：全免
    private int couponValue;//这里传2.0签名会有问题，简便处理固定直接传整数2小时
    private Integer isRepeat;//不填:默认车辆在同一次入场，可以进行多次打折减免 1: 车辆在同一次入场，可以进行多次打折减免 0: 车辆在同一次入场，只能减免一次
}