package com.cdz360.iot.model.evse.vo;

import com.cdz360.iot.model.evse.po.EvseCfgPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseCfgVo extends EvseCfgPo {

    @Schema(description = "桩名称")
    private String evseName;

    /**
     * 见 EvseCfgResult
     */
    @Schema(description = "价格模板下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码", example = "0")
    private Integer priceCodeResult;

    @Schema(description = "期望的价格模板ID", example = "123")
    private Long expectPriceCode;

    @Schema(description = "当前生效的价格模板ID", example = "123")
    private Long actualPriceCode;

    @Schema(description = "计费模板生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date priceCodeEffectiveTime;

    /**
     * 见 EvseCfgResult
     */
    @Schema(description = "桩配置下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码", example = "0")
    private Integer cfgResult;

    @Schema(description = "期望的桩配置ID", example = "123")
    private Long expectCfgCode;

    @Schema(description = "当前生效的桩配置ID", example = "123")
    private Long actualCfgCode;

    /**
     * 见 EvseCfgResult
     */
    @Schema(description = "白名单列表下发结果. 0, 成功; 300, 下发中; 301, 超时; 302, 桩离线; 其他都为桩端返回失败码", example = "0")
    private Integer whiteCardResult;

    @Schema(description = "期望的白名单配置ID", example = "123")
    private Long expectWhiteCardCode;

    @Schema(description = "当前生效的白名单配置ID", example = "123")
    private Long actualWhiteCardCode;
}
