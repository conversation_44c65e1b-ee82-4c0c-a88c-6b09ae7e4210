package com.cdz360.iot.model.camera.po;

import com.cdz360.iot.model.camera.type.CameraRecordType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "直连设备设备")
public class CameraRecorderPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "门店id")
	@NotNull(message = "cameraSiteId 不能为 null")
	private Long cameraSiteId;

	@Schema(description = "录像机设备序列号")
	@Size(max = 64, message = "deviceSerial 长度不能超过 64")
	private String deviceSerial;

	@Schema(description = "设备验证码")
	@Size(max = 64, message = "validateCode 长度不能超过 64")
	private String validateCode;

	@Schema(description = "设备名称")
	@Size(max = 64, message = "deviceName 长度不能超过 64")
	private String deviceName;

	@Schema(description = "设备型号，如CS-C2S-21WPFR-WX")
	private String model;

	@Schema(description = "在线状态：0-不在线，1-在线")
	private Integer status;

	@Schema(description = "设备密码")
	@Size(max = 255, message = "password 长度不能超过 255")
	private String password;

	@Schema(description = "IP地址")
	@Size(max = 255, message = "ip 长度不能超过 64")
	private String ip;

	@Schema(description = "设备类型")
	private CameraRecordType type;

	@Schema(description = "1有效 0无效")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	@Schema(description = "摄像头长宽比")
	private String aspectRatio;
}
