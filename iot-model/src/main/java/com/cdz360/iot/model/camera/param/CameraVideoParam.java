package com.cdz360.iot.model.camera.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname CameraVideoParam
 * @Description
 * @Date 7/29/2021 3:14 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "录像回放")
public class CameraVideoParam {

    @Schema(description = "相机id")
    private Long cameraId;//相机id
    @Schema(description = "本地录像回放开始时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;//本地录像回放开始时间
    @Schema(description = "本地录像回放结束时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopTime;//本地录像回放结束时间

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long expireTime = 86400L;//过期时间
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Integer quality = 1;//视频清晰度，1-高清，2-标清
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Integer type = 2;//ezopen协议地址的类型，1-预览，2-本地录像回放，3-云存储录像回放，非必选，默认为1
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Integer protocol = 3;//流播放协议，1-ezopen、2-hls、3-rtmp、4-flv，默认为1

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String deviceSerial;//设备序列号
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Integer channelNo;//通道号

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String startTimeStr;//本地录像回放开始时间
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String stopTimeStr;//本地录像回放结束时间

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String accessToken;// 用于萤石云取回放流的token

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public String getStartTimeStr() {
        return format.format(this.startTime);
    }
    public String getStopTimeStr() {
        return format.format(this.stopTime);
    }

}