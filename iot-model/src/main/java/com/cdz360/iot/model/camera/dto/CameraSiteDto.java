package com.cdz360.iot.model.camera.dto;

import com.cdz360.iot.model.camera.po.CameraSitePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "摄像机所在场站信息")
@EqualsAndHashCode(callSuper = true)
public class CameraSiteDto extends CameraSitePo {

    @Schema(description = "场站对应的区县编号")
    private String siteDistrictCode;
}
