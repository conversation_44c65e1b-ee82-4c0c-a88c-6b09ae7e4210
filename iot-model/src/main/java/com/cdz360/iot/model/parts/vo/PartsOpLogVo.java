package com.cdz360.iot.model.parts.vo;

import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "物料操作日志信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PartsOpLogVo extends PartsOpLogPo {

    @Schema(description = "操作人员姓名")
    @JsonInclude(Include.NON_EMPTY)
    private String opName;

}
