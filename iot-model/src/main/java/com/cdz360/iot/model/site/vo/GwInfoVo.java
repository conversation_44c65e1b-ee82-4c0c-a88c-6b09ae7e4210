package com.cdz360.iot.model.site.vo;

import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.gw.MgcAlertInfo;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.type.GwStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站控制器信息")
@Data
@Accessors(chain = true)
public class GwInfoVo {

    @Schema(description = "控制器编号 网关编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "设备类型 控制器固定为'光储充控制器'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "设备名称 网关名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "设备状态 网关状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GwStatus status;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "网管密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passcode;

    @Schema(description = "挂载逆变器列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<GtiVo> gtiVoList;

    @Schema(description = "控制器下挂载的设备")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<UpdateCtrlDeviceDto> gtiEssVoList;

    @Schema(description = "从Redis中读取，磁盘占用率，内存等信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private MgcAlertInfo mgcAlertInfo;

    @Schema(description = "软件版本号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer swVerCode;

    @Schema(description = "升级状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UpgradeStatus upgradeStatus;
}
