package com.cdz360.iot.model.site.ctrl;

import com.cdz360.iot.model.order.type.SiteCtrlCfgResultType;
import lombok.Data;

/**
 * @Classname SiteCtrlCfgResultReq
 * @Description
 * @Date 4/21/2020 6:49 PM
 * @Created by Rafael
 */
@Data
public class SiteCtrlCfgResultReq {
    private SiteCtrlCfgResultType result;
    private Integer triggerRst;//各种开关项配置结果, 不传表示不做变更,0x00：成功
    private Integer pwrCtrlRst;//功率限定配置结果, 不传表示不做变更,0x00：成功
    private Integer infoUpRst;//监测信息上报配置结果, 不传表示不做变更,0x00：成功
    private Integer loadAlmRst;//功率负载预警配置结果, 不传表示不做变更,0x00：成功
    private Integer pwrTempRst;//温度预警配置结果,不传表示不做变更,0x00：成功
}