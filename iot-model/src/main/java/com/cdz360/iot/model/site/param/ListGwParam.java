package com.cdz360.iot.model.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.iot.model.type.GwStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

@Schema(description = "获取网关列表参数")
public class ListGwParam extends BaseListParam {

    @Schema(description = "城市编码")
    private List<GwStatus> statusList;

    @Schema(description = "经度")
    private Double lon;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "误差范围")
    private Double delta = 0.0;

    public Double getDelta() {
        return delta;
    }

    public ListGwParam setDelta(Double delta) {
        this.delta = delta;
        return this;
    }

    public List<GwStatus> getStatusList() {
        return statusList;
    }

    public ListGwParam setStatusList(List<GwStatus> statusList) {
        this.statusList = statusList;
        return this;
    }

    public Double getLon() {
        return lon;
    }

    public ListGwParam setLon(Double lon) {
        this.lon = lon;
        return this;
    }

    public Double getLat() {
        return lat;
    }

    public ListGwParam setLat(Double lat) {
        this.lat = lat;
        return this;
    }
}
