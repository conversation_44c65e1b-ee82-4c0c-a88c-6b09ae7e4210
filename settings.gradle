
pluginManagement {
    repositories {

        // maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        gradlePluginPortal()

    }
}

rootProject.name = 'iot-server'
include 'iot-common'
include 'iot-model'
include 'iot-ds'
include 'iot-feign-biz'
include 'iot-device-mgm-client'
include 'iot-worker-client'
include 'iot-task'
include 'iot-device-mgm'
include 'iot-worker'
include 'iot-pv-api'
include 'iot-ess-api'
include 'iot-monitor'
include 'iot-collection'
include 'iot-park-api'
include 'iot-meter-api'
include 'iot-camera-api'
include 'iot-common-biz'
