package com.cdz360.iot.common.utils;

import java.io.ByteArrayOutputStream;

public class ByteArrayUtil {

    public static byte[] transInt(int value) {
        byte[] bytes = new byte[4];
        bytes[3] = (byte) (value >> 24);
        bytes[2] = (byte) (value >> 16);
        bytes[1] = (byte) (value >> 8);
        bytes[0] = (byte) value;
        return bytes;
    }

    public static void writeInt(ByteArrayOutputStream os, int value) {
        byte[] bytes = new byte[4];
        bytes[3] = (byte) (value >> 24);
        bytes[2] = (byte) (value >> 16);
        bytes[1] = (byte) (value >> 8);
        bytes[0] = (byte) value;
        os.writeBytes(bytes);
    }

    public static void writeShort(ByteArrayOutputStream os, int shortValue) {
        byte[] bytes = new byte[2];
        bytes[1] = (byte) (shortValue >> 8);
        bytes[0] = (byte) shortValue;
        os.writeBytes(bytes);
    }
}
