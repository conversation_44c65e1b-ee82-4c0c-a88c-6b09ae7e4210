package com.cdz360.iot.common.utils;

public class RedisIdGenerator {
    private static final String PREFFIX = "evseCfg-";
//    private static final String PREFFIX_V2 = "evseCfgV2-";
    public static String getCfgId(String gwno, String evseId) {
        IotAssert.isNotBlank(gwno, "网关编号不能为空白");
        IotAssert.isNotBlank(evseId, "桩编号不能为空白");
        return String.format("%s%s%s", PREFFIX, gwno, evseId);
    }

//    public static String getCfgIdV2(String gwno, String evseId) {
//        IotAssert.isNotBlank(gwno, "网关编号不能为空白");
//        IotAssert.isNotBlank(evseId, "桩编号不能为空白");
//        return String.format("%s%s%s", PREFFIX_V2, gwno, evseId);
//    }
}
