package com.cdz360.iot.common.utils;

//
//public class JsonUtils {
//
//    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);
//
//    public static <T> T fromJson(String jsonString, Class<T> clazz) {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        try {
//            return mapper.readValue(jsonString, clazz);
//        } catch (IOException e) {
//            logger.error(e.getMessage(), e);
//            throw new RuntimeException(" result can not converto to Object");
//        }
//
//    }
//
//    public static <T> T fromJson(String json, TypeReference<T> typereference) {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        try {
//            return mapper.readValue(json, typereference);
//        } catch (IOException e) {
//            logger.error(e.getMessage(), e);
//            throw new RuntimeException(" result can not converto to Object");
//        }
//    }
//
//
//    public static JsonNode fromJson(String json) {
//
//        ObjectMapper mapper = new ObjectMapper();
//        JsonFactory factory = mapper.getFactory();
//        try {
//            JsonParser jp = factory.createParser(json);
//            JsonNode jsonNode = mapper.readTree(jp);
//            return jsonNode;
//        } catch (IOException e) {
//
//            logger.error(e.getMessage(), e);
//            throw new RuntimeException(" result can not converto to Object");
//        }
//
//    }
//
//
//    private static ObjectMapper getObjectMapper() {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        return mapper;
//    }
//
//    public static String toJsonString(Object obj) {
//        ObjectMapper mapper = getObjectMapper();
//
//        String jsonString = "";
//        try {
//            jsonString = mapper.writeValueAsString(obj);
//        } catch (JsonProcessingException e) {
//            //logger.err
//            logger.error(e.getMessage(), e);
//        }
//        return jsonString;
//    }
//
//}
