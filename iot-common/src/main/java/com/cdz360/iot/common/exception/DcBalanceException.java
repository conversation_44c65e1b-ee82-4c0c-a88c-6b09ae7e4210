package com.cdz360.iot.common.exception;

import com.cdz360.base.model.base.exception.DcException;
import org.slf4j.event.Level;

/**
 * @Classname DcBalanceException
 * @Description 用于抛出余额不足异常
 * @Date 2019/6/27
 * @Created by wangzheng
 */
public class DcBalanceException extends DcException {
    private static final int STATUS = 2101;

    public DcBalanceException(String msg) {
        super(STATUS, msg, Level.WARN);
    }

    public DcBalanceException(int status, String msg) {
        super(status, msg, Level.WARN);
    }

    public DcBalanceException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }
}