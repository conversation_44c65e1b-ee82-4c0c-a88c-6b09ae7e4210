package com.cdz360.iot.common.utils;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MqttUtils {

    private static final String HMAC_SHA1 = "HmacSHA1";
    private static final Logger logger = LoggerFactory.getLogger(MqttUtils.class);

    /**
     * 云端MQTT客户端的clientId
     */
    public static String formatCloudClientId(String env, String name, String hostIp) {
        return "cloud@@@" + env + "@@@" + name + "@@@" + hostIp.replace("\\.", "_");
    }

    /**
     * 获取 mqtt 的 clientId
     */
    public static String getMqttClientId(String gwno, String mqttGroup, Boolean isSupervisor) {
        if (Boolean.TRUE.equals(isSupervisor)) {
            return mqttGroup + "@@@" + gwno + "@@@supervisor";
        } else {
            return mqttGroup + "@@@" + gwno;
        }
    }

    /**
     * 获取 mqtt 的 username
     */
    public static String getMqttUsername(String mqttId, String mqttAccessKey) {
        return "Signature|" + mqttAccessKey + "|" + mqttId;
    }

    /**
     * 获取 mqtt 的 password
     */
    public static String getMqttPasscode(String clientId, String mqttAccessSecret) {
        String passcode = "";
        try {
            byte[] keyBytes = mqttAccessSecret.getBytes();
            SecretKeySpec signingKey = new SecretKeySpec(keyBytes, HMAC_SHA1);
            Mac mac = Mac.getInstance(HMAC_SHA1);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(clientId.getBytes());
            passcode = Base64.getEncoder().encodeToString(rawHmac);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            logger.error(e.getMessage(), e);
        }
        return passcode;
    }

    /**
     * 获取 mqtt 的 topic
     */
    public static String getMqTopic(String gwno, String env, String topicPrefix,
        Boolean isSupervisor) {
        if (Boolean.TRUE.equals(isSupervisor)) {
            return topicPrefix + "/" + env + "/gw/" + gwno + "/supervisor/";

        } else {
            return topicPrefix + "/" + env + "/gw/" + gwno + "/";
        }
    }

    public static String getMqLastWillTopic(String env, String topicPrefix, String lastWillTopic,
        Boolean isSupervisor) {
        if (Boolean.TRUE.equals(isSupervisor)) {
            return topicPrefix + "/" + env + "/gw/" + lastWillTopic + "/supervisor/";

        } else {
            return topicPrefix + "/" + env + "/gw/" + lastWillTopic + "/";
        }
    }
}