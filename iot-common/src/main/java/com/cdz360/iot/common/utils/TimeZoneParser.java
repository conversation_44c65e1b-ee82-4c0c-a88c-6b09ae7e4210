package com.cdz360.iot.common.utils;

import com.cdz360.base.utils.StringUtils;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TimeZoneParser {

    private static final Logger log = LoggerFactory.getLogger(TimeZoneParser.class);

    public static boolean isValidTimeZone(String timezoneString) {
        if (StringUtils.isEmpty(timezoneString)) {
            return false;
        }

        try {
            timezoneString = timezoneString.trim().toUpperCase();
            // 尝试解析为 ZoneOffset (适用于 GMT/UTC+偏移量格式)
            if (timezoneString.startsWith("GMT") || timezoneString.startsWith("UTC")) {
                String offsetString = timezoneString.substring(3); // 去掉 GMT/UTC 前缀
                ZoneOffset offset = ZoneOffset.of(offsetString);
                return offset != null; // 解析成功
            } else {
                /*
                // 尝试解析为 ZoneId (适用于 IANA 时区 ID 格式)
                ZoneId zoneId = ZoneId.of(timezoneString);
                return true; // 解析成功
                */
                return false;
            }
        } catch (Exception e) {
            log.error("isValidTimeZone error: {}", e.getMessage(), e);
            return false; // 解析失败
        }
    }

    /**
     * 获取当前时间（含时区）
     *
     * @param tz 时区（例如：GMT+08:00/UTC+08:00）
     * @return
     */
    public static LocalDateTime nowByTz(@Nonnull String tz) {
        return switchByTz(LocalDateTime.now(), tz);
    }

    /**
     * 通过时区转换时间
     *
     * @param source 可为null 原时间（系统默认时区）
     * @param tz     可为null 目标时区（例如：GMT+08:00/UTC+08:00）
     * @return
     */
    public static LocalDateTime switchByTz(@Nonnull LocalDateTime source, @Nullable String tz) {
        LocalDateTime ldt = source;
        if (StringUtils.isNotBlank(tz)) {
            String offsetString = tz.substring(3); // 去掉 GMT/UTC 前缀
            ldt = source
                .atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneOffset.of(offsetString))
                .toLocalDateTime();
        }
        return ldt;
    }

}
