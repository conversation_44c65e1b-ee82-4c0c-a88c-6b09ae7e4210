app:
  name: iotDeviceMgm

server:
  address: 0.0.0.0
  port: 8089
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-device-mgm-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01


management:
  context-path: /admin
  security:
    enabled: false



eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/
#      defaultZone: http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000

springdoc:
  packagesToScan: com.cdz360.iot.device.mgm.rest
  swagger-ui:
    path: /swagger-ui.html

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

logging:
  level:
    com.cdz360.iot: 'DEBUG'
    org.springframework: 'WARN'
    org.springframework.cloud.config: 'INFO'
    org.mybatis: 'DEBUG'
#    org.eclipse.paho: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"