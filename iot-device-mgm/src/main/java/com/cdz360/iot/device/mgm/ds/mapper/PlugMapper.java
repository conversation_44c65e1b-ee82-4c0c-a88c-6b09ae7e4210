package com.cdz360.iot.device.mgm.ds.mapper;

import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.iot.device.mgm.model.bi.vo.PlugInfoDto;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PlugMapper {

    List<PlugInfoDto> getPlugsOfSite(
        @Param(value = "start") long start,
        @Param(value = "size") int size,
        @Param(value = "siteIdList") List<String> siteIdList,
        @Param(value = "bizStatusList") List<EvseBizStatus> bizStatusList);

    int refreshPlugName(
        @Param(value = "evseId") String evseId,
        @Param(value = "plugId") Integer plugId,
        @Param(value = "name") String name);
}
