package com.cdz360.iot.device.mgm.dzds.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.dongzheng.request.TokenRequest;
import com.cdz360.iot.model.dongzheng.response.SysUserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Interfacename AuthCoreClient
 * @Description 用于调用auth-core服务
 * @Date 2019/6/5
 * @Created by wangzheng
 */
@FeignClient(value = DcConstants.KEY_FEIGN_DC_BIZ_AUTH)
public interface IotDzAuthCoreApiClient {

    @RequestMapping(value = "/api/info/token", method = RequestMethod.POST)
    ObjectResponse<SysUserVo> getLoginUserByToken(@RequestBody TokenRequest tokenRequest);
}
