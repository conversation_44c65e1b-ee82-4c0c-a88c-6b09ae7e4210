package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.type.ModuleType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class EvseModuleStatusConvertFactory {

    private Map<String, EvseModuleStatusConvert> evseModuleStatusConvertMap = new HashMap<>();

    public synchronized void addEvseModuleStatusConvertMap(String moduleType,
        EvseModuleStatusConvert convert) {
        evseModuleStatusConvertMap.put(moduleType, convert);
    }

    public List<String> formatStatus(String moduleType, Integer status) {
        if (StringUtils.isBlank(moduleType)) {
            return null;
        }
        // 英可瑞
        List<String> YKR = List.of(ModuleType.DCM_TYPE_YKR_EVR700_15000.name(),
            ModuleType.YKR_EVR600_15000.name(),
            ModuleType.YKR_EVR500_15000.name(),
            ModuleType.EVR330_15000C.name(),
            ModuleType.EVR700_20000C.name(),
            ModuleType.YKR_EVR430_20000C.name(),
            ModuleType.EVR330_15000_DEGRADE.name());
        // 英飞源
        List<String> INFY = List.of(ModuleType.INFY_REG75030.name(),
            ModuleType.INFY_REG50040.name(),
            ModuleType.INFY_REG75035.name(),
            ModuleType.INFY_REG75050.name(),
            ModuleType.WL_NXR75030H.name(), // 永联品牌的模块，使用英飞源模块状态码，主要用在鼎充品牌的桩上
            ModuleType.REG75040.name(),
            ModuleType.INFY_REG1K070.name());
        // 华为
        List<String> HUAWEI = List.of(ModuleType.HUAWEI_R75020G1.name(),
            ModuleType.HUAWEI_R95021G1.name());
        // 中兴
        List<String> ZTE = List.of(ModuleType.ZTE_ZXD030_T751.name());

        // 优优绿能
        List<String> UUGP = List.of(ModuleType.UUGP_UR100030SW.name(),
            ModuleType.UUGP_UR100040SW.name());

        // 永联
        List<String> WL = List.of(ModuleType.WL_NXR100020.name(),
            ModuleType.WL_UXR100040.name());

        EvseModuleStatusConvert convert = null;
        if (YKR.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("YKR");
        } else if (INFY.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("INFY");
        } else if (HUAWEI.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("HUAWEI");
        } else if (ZTE.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("ZTE");
        } else if (UUGP.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("UUGP");
        } else if (WL.contains(moduleType)) {
            convert = evseModuleStatusConvertMap.get("WL");
        }
        return this.convertToList(convert, status);
    }

    private List<String> convertToList(EvseModuleStatusConvert convert, Integer status) {
        List<String> res = new ArrayList<>();
        if (status == null || convert == null) {
            return null;
        }

        byte bitIdx = 0;
        while (bitIdx < 32) {
            int temp = status & (1 << bitIdx);
            if (temp > 0) {
                res.add(convert.formatStatus(bitIdx));
            }
            bitIdx++;
        }
        return res;
    }

}
