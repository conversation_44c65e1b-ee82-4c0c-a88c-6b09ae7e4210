package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_SIM, fallbackFactory = OpenSimFeignHystrix.class)
public interface OpenSimFeignClient {

    // 获取SIM卡信息
    @GetMapping(value = "/open/sim/getSimInfo")
    Mono<ObjectResponse<SimPo>> getSimInfo(@RequestParam(value = "iccid", required = false) String iccid,
                                           @RequestParam(value = "msisdn", required = false) String msisdn,
                                           @RequestParam("vendor") NetworkVendor vendor);

    // 同步SIM卡信息（只含ICCID或MSISDN）
    @GetMapping(value = "/open/sim/refreshTinyData")
    Mono<BaseResponse> refreshTinyData();

}
