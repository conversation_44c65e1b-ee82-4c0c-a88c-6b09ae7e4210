package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.srs.vo.RedisSrsRtData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;

@Slf4j
@Service
public class RedisSrsRtDataService {
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final static Calendar CALENDAR = Calendar.getInstance();

    private static final String PRE_REDIS_KEY = "srs:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取最近一条运行时数据
     *
     * @param dno
     * @param date
     * @return
     */
    public RedisSrsRtData latestRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, RedisSrsRtData.class);
    }

    private static String formatKey(String dno, LocalDate date) {
        return PRE_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatKey(String dno, String date) {
        return PRE_REDIS_KEY + dno + ":" + date;
    }
}
