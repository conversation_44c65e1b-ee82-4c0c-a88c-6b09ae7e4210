package com.cdz360.iot.device.mgm.listener;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.event.DcBaseQEvent;
import com.cdz360.data.sync.event.MqEventSubType;
import com.cdz360.data.sync.event.PlugInfoEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.device.mgm.feign.ParkFeignClient;
import com.cdz360.iot.ds.ro.ParkingLockRoDs;
import com.cdz360.iot.ds.rw.PlugLogRwDs;
import com.cdz360.iot.model.evse.po.PlugLogPo;
import com.cdz360.iot.model.park.param.PlugStatusEventLogParam;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.fasterxml.jackson.databind.JsonNode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = IotConstants.DM_IOT_QUEUE_NAME)
public class IotEventListener {

    private static final List<PlugStatus> plugStatusList = List.of(
        PlugStatus.CONNECT, PlugStatus.BUSY, PlugStatus.JOIN,
        PlugStatus.RECHARGE_END, PlugStatus.IDLE);

    @Autowired
    private PlugLogRwDs plugLogRwDs;
//    private AlarmService alarmService;
//    private AlarmServiceImpl alarmService;

    @Autowired
    private ParkingLockRoDs parkingLockRoDs;

    @Autowired
    private ParkFeignClient parkFeignClient;

    private Map<String, PlugLogPo> plugs = new ConcurrentHashMap<>();

    @RabbitHandler
    public void plugInfoListener(String msg) {
//        log.info("msg = {}", msg);
        try {
            JsonNode json = JsonUtils.fromJson(msg);
            String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
            if (MqEventSubType.MQ_PLUG.name().equals(subMqType)) {

                PlugInfoEvent event = JsonUtils.fromJson(msg, PlugInfoEvent.class);
                PlugVo plugVo = event.getData();

                // 还没有绑定场站的桩，忽略
                if (StringUtils.isBlank(plugVo.getSiteId())) {
                    return;
                }

                LocalDateTime today = LocalDateTime.now();

                PlugLogPo last = plugs.get(plugVo.getPlugNo());
                if (last == null // 缓存不存在
                    || last.getPlugStatus() != plugVo.getStatus()   // 枪头状态有变更
                    || !NumberUtils.equals(last.getErrorCode(), plugVo.getErrorCode())  // 故障编号有变更
                    || last.getCreateTime().getDayOfMonth()
                    != today.getDayOfMonth()// 日期有变更(每天至少一条记录)
                ) {
                    PlugLogPo plugLog = new PlugLogPo();
                    plugLog.setSiteId(plugVo.getSiteId())
                        .setEvseNo(plugVo.getEvseNo())
                        .setPlugNo(plugVo.getPlugNo())
                        .setEventType(event.getEventType())
                        .setPlugStatus(plugVo.getStatus())
                        .setErrorCode(plugVo.getErrorCode())
                        .setCreateTime(today);

                    log.info("新增枪头状态变更日志 {}", plugLog);
                    plugLogRwDs.addPlugLog(plugLog);
                    plugs.put(plugLog.getPlugNo(), plugLog);

                    try {
                        final ParkingLockPo lock = parkingLockRoDs.getByEvseNoAndPlugId(
                            plugVo.getEvseNo(), plugVo.getIdx()); // FIXME: 数据量增加要考虑索引
                        if (null != lock && plugStatusList.contains(plugVo.getStatus())) {
                            this.parkFeignClient.plugStatusEventLog(new PlugStatusEventLogParam()
                                    .setEvseNo(plugVo.getEvseNo())
                                    .setPlugId(plugVo.getIdx())
                                    .setStatus(plugVo.getStatus())
                                    .setOrderNo(plugVo.getOrderNo()))
                                .subscribe();
                        }
                    } catch (Exception e) {
                        log.error("推送枪头状态变更事件: {}", e.getMessage(), e);
                    }
                }
            }
//            else if (MqEventSubType.MQ_CTRL.name().equals(subMqType)) {
//                log.info("处理告警-场站控制器");
//
//                IotCtrlInfoEvent iotCtrlInfoEvent = JsonUtils.fromJson(msg, IotCtrlInfoEvent.class);
//                SiteCtrlVo siteCtrlVo = iotCtrlInfoEvent.getData();
//
//
//
//            } else if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {
//                log.info("处理告警-桩");
//
//                EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
//                EvseVo evseVo = evseInfoEvent.getData();
//
//                if (StringUtils.isNotBlank(evseInfoEvent.getLinkId())) {
//
//                }
//
//            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.warn("收到未识别的消息. msg = {}", msg);
        }

//        log.info("<<");
    }
}
