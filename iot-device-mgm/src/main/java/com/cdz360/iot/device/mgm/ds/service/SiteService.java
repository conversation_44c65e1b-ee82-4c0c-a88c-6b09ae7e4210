package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.sync.model.Site;
import com.cdz360.data.sync.model.SiteGroup;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper;
import com.cdz360.iot.device.mgm.ds.mapper.PlugMapper;
import com.cdz360.iot.device.mgm.model.bi.vo.PlugInfoDto;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.SiteGroupSiteRefRwDs;
import com.cdz360.iot.ds.rw.SiteRwDs;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.BizType;
import com.cdz360.iot.model.type.SiteStatus;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

//import com.cdz360.iot.device.mgm.dzds.service.CityService;

/**
 * 充电场站相关的ds服务
 */
@Service
public class SiteService {

    private final Logger logger = LoggerFactory.getLogger(SiteService.class);

//
//    @Autowired
//    private SiteFeignClient siteFeignClient;


    @Autowired
    private GwInfoMapper gwInfoMapper;

    @Autowired
    private PlugMapper plugMapper;

    @Autowired
    private SiteRwDs siteRwDs;

    @Autowired
    private SiteGroupSiteRefRwDs siteGroupSiteRefRwDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private PlugRoDs plugRoDs;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private RedisIotRwService redisIotRwService;

//    /**
//     * 获取充电场站列表
//     * TODO 目前使用东正接口作为临时的数据源方案，今后将做修改
//     *
//     * @param param
//     * @return
//     */
//    public SitePageDto listSiteDto(ListSiteParam param) {
//
//        //使用东正提供的接口
//        //不直接查询东正的DB
//
//        Integer start = null;
//        try {
//            start = param.getStart().intValue();
//        } catch (Exception ex) {
//            logger.warn(ex.getMessage(), ex);
//        }
//        SiteParam req = new SiteParam();
//        req.setLatitude(param.getLat())
//                .setLongitude(param.getLon())
//                .setCityCode(param.getCityCode())
//                .setPage(start)
//                .setRows(param.getSize())
//                .setKeywords(param.getSk());
//        ListResponse<DzSiteDto> res = siteFeignClient.getPagedSiteGeoList(req);
//
//        List<SiteDto> apiRes = res.getData().stream().map(e -> {
//            Double lat = null;
//            Double lon = null;
//            try {
//                if (!StringUtils.isEmpty(e.getLocation())) {
//                    String[] latLon = e.getLocation().split(",");
//                    lat = Double.valueOf(latLon[0]);
//                    lon = Double.valueOf(latLon[1]);
//                }
//            } catch (Exception ex) {
//                logger.error(ex.getMessage(), ex);
//            }
//            SiteDto siteDto = new SiteDto();
//            SiteDto dto = gwInfoMapper.getGwInfoByDzSiteId(e.getSiteId());//从iot库中查询相对应的场站与网关的绑定关系
//            if (dto != null) {
//                siteDto.setGwId(dto.getGwId())
//                        .setGwno(dto.getGwno())
//                        .setGwStatus(dto.getGwStatus())
//                        .setGwSiteRefUpdateTime(dto.getGwSiteRefUpdateTime())
//                        .setId(dto.getId());
//            }
//            siteDto.setCityName(e.getCityName())
//                    .setImages(e.getImages()).setName(e.getSiteName())
//                    .setDzId(e.getSiteId())
//                    .setAddress(e.getAddress())
//                    .setProvinceCode(e.getProvince().toString())
//                    .setCityCode(e.getCity().toString())
//                    .setLat(lat)
//                    .setLon(lon)
//                    .setType(e.getType())
//                    .setStatus(SiteStatus.fromValue(e.getStatus()));
//            //                    .setId(e.getSiteId());
//            return siteDto;
//        }).collect(Collectors.toList());
//
//        SitePageDto sitePageDto = new SitePageDto();
//        sitePageDto.setSiteList(apiRes).setTotal(res.getTotal().intValue());
//
//
//        return sitePageDto;
//    }

//    /**
//     * 修改场站信息
//     * TODO 目前使用东正接口作为临时的数据源方案，今后将做修改
//     *
//     * @param param
//     * @return
//     */
//    public CommonRpcResponse<Integer> modifySiteInfo(SiteParam param) {
//        BaseResponse dzBaseRes = siteFeignClient.updateSiteInfo(param);
//        CommonRpcResponse<Integer> ret = new CommonRpcResponse<>(0);
//        //        ret.setError(dzBaseRes.getMessage());
//        //
//        //        if(dzBaseRes.getSuccess() == null || !dzBaseRes.getSuccess()) {
//        //            ret.setStatus(dzBaseRes.getErrcode());
//        //            ret.setData(dzBaseRes.getErrcode());
//        //        }
//        if (dzBaseRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
//            ret.setStatus(dzBaseRes.getStatus());
//            ret.setError(dzBaseRes.getError());
//        }
//        return ret;
//    }

    /**
     * 同步场站数据到 t_site表. 如果已经有相同dzId的场站数据, 执行更新操作 否则新增一条新的数据
     *
     * @param site
     */
    @Transactional
    public synchronized void syncSiteInfo(Site site) {
        if (NumberUtils.equals(site.getBizType(), BizType.HLHT.getCode())) {
            return; //互联场站无需同步
        }
        logger.info("site:{}", JsonUtils.toJsonString(site));
        int i = siteRwDs.insertOrUpdate(this.siteConvertToSitePo(site));
        IotAssert.isTrue(i > 0, "同步场站数据到t_site表失败");

        // 删除场站同时删除组信息
        if (SiteStatus.CLOSE.equals(site.getStatus())) {
            siteGroupSiteRefRwDs.deleteBySiteId(site.getSiteId());
        }

        this.refreshReidsSiteName(site);
    }

    /**
     * 同步场站数据到 t_r_site_group_site_ref 表.
     *
     * @param siteGroup
     */
    @Transactional
    public synchronized void syncSiteGroupInfo(SiteGroup siteGroup) {
        logger.info("site group site ref: {}", JsonUtils.toJsonString(siteGroup));
        if (null != siteGroup &&
            StringUtils.isNotBlank(siteGroup.getGid())) {
            // 删除旧数据
            int i;
            if (CollectionUtils.isNotEmpty(siteGroup.getSiteIdList())) {
                i = siteGroupSiteRefRwDs.batchDelete(
                    siteGroup.getGid(), siteGroup.getSiteIdList());

                // 增加新数据
                i = siteGroupSiteRefRwDs.batchInsert(
                    siteGroup.getGid(), siteGroup.getSiteIdList());
            } else {
                i = siteGroupSiteRefRwDs.deleteByGid(siteGroup.getGid());
            }
        }
    }

    /**
     * 刷新redis中桩、枪的site信息
     *
     * @param site
     */
    public void refreshReidsSiteName(Site site) {
        Set<String> evseNos = redisIotReadService.getSiteEvses(site.getSiteId());
        if (CollectionUtils.isNotEmpty(evseNos)) {
            evseNos.forEach(e -> {
                EvseVo update = new EvseVo();
                update.setEvseNo(e)
                    .setSiteCommId(site.getCommId())
                    .setSiteName(site.getName());
                redisIotRwService.updateEvseRedisCache(update);
            });
        }

        Set<String> plugNos = redisIotReadService.getSitePlugs(site.getSiteId());
        if (CollectionUtils.isNotEmpty(plugNos)) {
            plugNos.forEach(e -> {
                PlugVo update = new PlugVo();
                update.setPlugNo(e)
                    .setSiteCommId(site.getCommId())
                    .setSiteName(site.getName());
                redisIotRwService.updatePlugRedisCache(update);
            });
        }
    }


    public List<PlugInfoDto> getPlugsOfSite(ListPlugParam param
        //Integer index, Integer size, String siteId
    ) {
        if (param == null || param.getStart() == null || param.getSize() == null) {
            throw new DcArgumentException("请提供分页参数信息");
        } else if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("场站ID不能为空");
        }

//        if (index <= 0) {
//            throw new DcArgumentException("分页索引值从1开始");
//        }

        return plugMapper.getPlugsOfSite(param.getStart(), param.getSize(), param.getSiteIdList(),
            param.getBizStatusList());
    }

    public SitePo siteConvertToSitePo(Site site) {
        SitePo sitePo = new SitePo();
        sitePo.setSiteId(site.getSiteId())
            .setName(site.getName())
            .setTopCommId(site.getTopCommId())
            .setCommId(site.getCommId())
            .setStatus(SiteStatus.fromValue(site.getStatus()))
            .setProvinceCode(String.valueOf(site.getProvince()))
            .setCityCode(String.valueOf(site.getCity()))
            .setAreaCode(String.valueOf(site.getArea()))
            .setAddress(site.getAddress())
            .setLon(site.getLon())
            .setLat(site.getLat())
            .setTimeZone(site.getTimeZone())
            .setPriceCode(site.getPriceCode())
            .setDyPow(site.getDyPow())
            .setType(site.getSiteType())
            .setBizType(site.getBizType())
            .setPhone(site.getPhone())
            .setSiteNo(site.getSiteNo());
        return sitePo;
    }

    public Boolean refreshPlugName(String evseId, Integer plugId, String name) {
        return plugMapper.refreshPlugName(evseId, plugId, name) > 0;
    }

    /**
     * 获取有空闲枪头的场站ID列表
     */
    public List<String> getIdleSiteIdList(Long topCommId) {
        return siteRoDs.getIdleSiteIdList(topCommId);
    }

    public SitePo getRecordEvsePlugInfo(String siteId) {
        SitePo po = new SitePo();
        po.setSiteId(siteId)
            .setAcEvseNum(0).setAcPower(0).setDcEvseNum(0).setDcPower(0).setAcPlugNum(0)
            .setDcPlugNum(0);
        List<EvsePlugRecordPo> evseRecordPoList = evseRoDs.getEvseRecordInfo(siteId);
        evseRecordPoList.forEach(e -> {
            if (e.getSupply() == SupplyType.AC) {
                po.setAcEvseNum(e.getEvseTotalNum())
                    .setAcPower(e.getTotalPower());
            } else if (e.getSupply() == SupplyType.DC) {
                po.setDcEvseNum(e.getEvseTotalNum())
                    .setDcPower(e.getTotalPower());
            }
        });
        List<EvsePlugRecordPo> plugRecordPos = plugRoDs.getPlugRecordInfo(siteId);
        plugRecordPos.forEach(e -> {
            if (e.getSupply() == SupplyType.AC) {
                po.setAcPlugNum(e.getPlugTotalNum());
            } else if (e.getSupply() == SupplyType.DC) {
                po.setDcPlugNum(e.getPlugTotalNum());
            }
        });
        return po;
    }

    public List<SitePo> getUpgradeCleaningEvsePlugInfo() {
        List<SitePo> res = new ArrayList<>();

        List<EvsePlugRecordPo> evseRecordPoList = evseRoDs.getUpgradeCleaningEvseInfo();
        List<EvsePlugRecordPo> plugRecordPos = plugRoDs.getUpgradeCleaningPlugInfo();
        evseRecordPoList.forEach(e -> {
            SitePo po = new SitePo();
            po.setSiteId(e.getSiteId());

            if (e.getSupply() == SupplyType.AC) {
                po.setAcEvseNum(e.getEvseTotalNum())
                    .setAcPower(e.getTotalPower());
            } else if (e.getSupply() == SupplyType.DC) {
                po.setDcEvseNum(e.getEvseTotalNum())
                    .setDcPower(e.getTotalPower());
            }

            for (EvsePlugRecordPo plug : plugRecordPos) {
                int tmp = 0;
                int max = 2;
                if (e.getSiteId().equals(plug.getSiteId())) {
                    if (plug.getSupply() == SupplyType.AC) {
                        po.setAcPlugNum(plug.getPlugTotalNum());
                    } else if (plug.getSupply() == SupplyType.DC) {
                        po.setDcPlugNum(plug.getPlugTotalNum());
                    }
                    tmp += 1;
                }
                if (tmp == max) {
                    break;
                }
            }
            res.add(po);
        });

        return res;
    }

    public Integer getSiteCount(SiteAndPlugBiParam param) {
        return siteRoDs.getSiteCount(param);
    }

    public ObjectResponse<Date> getExpireDate(String siteId) {
        return RestUtils.buildObjectResponse(siteRoDs.getExpireDate(siteId));
    }

}
