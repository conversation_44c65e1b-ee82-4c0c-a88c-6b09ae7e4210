package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvsePackageRoDs;
import com.cdz360.iot.ds.rw.EvsePackageRwDs;
import com.cdz360.iot.model.config.UpgDownloadProperties;
import com.cdz360.iot.model.evse.param.ListPackageParam;
import com.cdz360.iot.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.iot.model.evse.vo.EvsePackageVo;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 海外平台升级包管理
 */
@Service
@Slf4j
@EnableConfigurationProperties(UpgDownloadProperties.class)
public class EvsePackageService {

    @Autowired
    private EvsePackageRwDs evsePackageRwDs;

    @Autowired
    private EvsePackageRoDs evsePackageRoDs;

    public BaseResponse deleteById(UpdateEvsePackageParam param) {
        evsePackageRwDs.updatePackage(param);
        return RestUtils.success();
    }

    public BaseResponse updateStatus(UpdateEvsePackageParam param) {
        evsePackageRwDs.updatePackage(param);
        return RestUtils.success();
    }

    public ListResponse<EvsePackageVo> getList(ListPackageParam param) {
        if (param.getSize() == null) {
            param.setSize(10);
        }
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        List<EvsePackageVo> list = evsePackageRoDs.getList(param);
        return RestUtils.buildListResponse(list, evsePackageRoDs.getCount(param));
    }

    public BaseResponse  createPackage(UpdateEvsePackageParam params) {
        params.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
        evsePackageRwDs.create(params);
        return RestUtils.success();
    }

    public ListResponse<String>  getBrandList() {
        return RestUtils.buildListResponse(evsePackageRoDs.getBrandList());
    }

    @Transactional
    public BaseResponse editPackage(UpdateEvsePackageParam param) {
        IotAssert.isNotBlank(param.getCode(),"code不能为空");
        evsePackageRwDs.disablePackageByCode(param.getCode());
        evsePackageRwDs.create(param);
        return RestUtils.success();
    }

    public EvsePackageVo selectByPrimaryKey(Long id) {
        EvsePackageVo evsePackageVo = evsePackageRoDs.selectByPrimaryKey(id);
        IotAssert.isNotNull(evsePackageVo, "所选定的升级包有问题，请先确认升级包是否正常，然后再尝试");
        return evsePackageVo;
    }
}
