package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.device.mgm.ds.service.EvseModuleService;
import com.cdz360.iot.model.evse.dto.EvseModuleDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/device/mgm/evseModule")
public class EvseModuleRest {

    @Autowired
    private EvseModuleService service;

    @GetMapping("/list")
    public ObjectResponse<EvseModuleDto> getList(@RequestParam("evseNo") String evseNo) {
        log.info("getEvseModuleList。 evseNo: {}", evseNo);
        return service.getList(evseNo);
    }

}
