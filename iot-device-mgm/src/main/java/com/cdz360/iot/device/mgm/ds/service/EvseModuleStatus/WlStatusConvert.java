package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 永联电源模块状态码解析器
 */
@Service
public class WlStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("WL", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "模块故障（红指示灯亮）";
                break;
            case 1:
                res = "模块保护（黄指示灯亮）";
                break;
            case 2:
                res = "NA";
                break;
            case 3:
                res = "模块内部SCI通信故障";
                break;
            case 4:
                res = "输入模式检测错误（或输入接线错误）";
                break;
            case 5:
                res = "监控下发输入模式与实际工作模式不匹配";
                break;
            case 6:
                res = "NA";
                break;
            case 7:
                res = "DCDC过压";
                break;
            case 8:
                res = "PFC电压异常（不平衡或过压或欠压）";
                break;
            case 9:
                res = "交流过压";
                break;
            case 10:
            case 11:
            case 12:
            case 13:
                res = "NA";
                break;
            case 14:
                res = "交流欠压";
                break;
            case 15:
                res = "NA";
                break;
            case 16:
                res = "CAN通信故障";
                break;
            case 17:
                res = "模块不均流";
                break;
            case 18:
            case 19:
            case 20:
            case 21:
                res = "NA";
                break;
            case 22:
                res = "DCDC开关机状态 0:开机，1:关机";
                break;
            case 23:
                res = "模块限功率";
                break;
            case 24:
                res = "温度限功率";
                break;
            case 25:
                res = "交流限功率";
                break;
            case 26:
                res = "NA";
                break;
            case 27:
                res = "风扇故障";
                break;
            case 28:
                res = "DCDC短路";
                break;
            case 29:
                res = "NA";
                break;
            case 30:
                res = "DCDC过温";
                break;
            case 31:
                res = "DCDC输出过压";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
