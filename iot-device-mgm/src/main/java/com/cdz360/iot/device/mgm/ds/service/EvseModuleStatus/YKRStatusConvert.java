package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class YKRStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("YKR", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "模块关机";
                break;
            case 1:
                res = "模块故障";
                break;
            case 2:
                res = "模块限流";
                break;
            case 3:
                res = "风扇故障";
                break;
            case 4:
                res = "输入过压";
                break;
            case 5:
                res = "输入欠压";
                break;
            case 6:
                res = "输出过压";
                break;
            case 7:
                res = "输出欠压";
                break;
            case 8:
                res = "过流保护";
                break;
            case 9:
                res = "过温保护";
                break;
            case 10:
                res = "设置关机";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
