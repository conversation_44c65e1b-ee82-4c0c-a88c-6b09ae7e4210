package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.parts.PartsOpLogService;
import com.cdz360.iot.model.parts.param.PartsOpLogParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "物料操作日志相关接口", description = "物料操作日志")
public class PartsOpLogRest {

    @Autowired
    private PartsOpLogService partsOpLogService;

    @Operation(summary = "物料关联运维工单操作")
    @PostMapping(value = "/device/mgm/partsLog/ywOrder")
    public Mono<BaseResponse> partsYwOrder(@RequestBody PartsOpLogParam param) {
        log.info("物料关联运维工单操作: param = {}", JsonUtils.toJsonString(param));
        partsOpLogService.partsYwOrder(param);
        return Mono.just(RestUtils.success());
    }
}
