package com.cdz360.iot.device.mgm.model.basic.dto;

import com.cdz360.iot.device.mgm.model.basic.po.PriceItemPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站收益计算信息")
public class SiteProfitInfo {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "收益计算分段信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PriceItemPo> priceItemPoList;

    @Schema(description = "储能充电支付计算分段信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PriceItemPo> inPriceItemPoList;

    @Schema(description = "储能充电支付计算分段信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PriceItemPo> outPriceItemPoList;
}
