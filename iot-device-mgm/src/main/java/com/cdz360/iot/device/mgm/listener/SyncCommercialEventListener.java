package com.cdz360.iot.device.mgm.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.SyncCommercialEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.ds.rw.CommercialRwDs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = IotConstants.MQ_QUEUE_COMMERCIAL_DEVICE_MGM)
public class SyncCommercialEventListener {

    @Autowired
    private CommercialRwDs syncCommercialService;

    @RabbitHandler
    public void commercialInfoListener(String msg) {
        log.info(">> msg = {}", msg);
        try {
            SyncCommercialEvent event = JsonUtils.fromJson(msg, SyncCommercialEvent.class);

            syncCommercialService.syncCommercial(event.getData());
        } catch (Exception e) {
            log.warn("收到未识别的消息. msg = {}", msg);
        }

        log.info("<<");
    }
}
