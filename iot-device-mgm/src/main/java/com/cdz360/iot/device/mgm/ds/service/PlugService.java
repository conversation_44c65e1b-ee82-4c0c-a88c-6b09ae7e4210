package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.SiteAndPlugBiVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PlugService {

    @Autowired
    private PlugRoDs plugRoDs;

    public PlugVo getPlugByPlugNo(String plugNo) {
        return plugRoDs.getPlugByPlugNo(plugNo);
    }

    public ListResponse<PlugVo> getPlugList(ListPlugParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }

        return plugRoDs.getPlugList(param);
    }

    public SiteAndPlugBiVo getSiteAndPlugStatus(SiteAndPlugBiParam param) {
        return plugRoDs.getSiteAndPlugStatus(param);
    }
}
