package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.MgcService;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "微网控制器相关接口", description = "微网控制器")
@RequestMapping("/device/mgm/mgc")
public class MgcRest {

    @Autowired
    private MgcService mgcService;

    @Operation(summary = "新增控制器信息")
    @PostMapping(value = "/addCtrl")
    public Mono<BaseResponse> addCtrl(@RequestBody UpdateCtrlDto param) {
        log.info("新增控制器信息: param = {}", JsonUtils.toJsonString(param));
        return mgcService.addCtrl(param)
                .map(res -> RestUtils.success());
    }
    @Operation(summary = "更新控制器信息")
    @PostMapping(value = "/updateCtrl")
    public Mono<BaseResponse> updateCtrl(@RequestBody UpdateCtrlDto param) {
        log.info("更新控制器信息: param = {}", JsonUtils.toJsonString(param));
        return mgcService.updateCtrl(param)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "获取控制器信息")
    @GetMapping(value = "/getCtrl")
    public Mono<ObjectResponse<GwInfoVo>> getCtrl(
            @Parameter(name = "微网控制器编号") @RequestParam(value = "gwno") String gwno) {
        log.info("获取控制器信息: gwno = {}", gwno);
        return mgcService.getCtrl(gwno)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除场站控制器")
    @PostMapping(value = "/removeCtrl")
    public Mono<BaseResponse> removeCtrl(
            @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId,
            @Parameter(name = "控制器编号(网关编号)", required = true) @RequestParam(value = "gwno") String gwno) {
        log.info("删除场站控制器: siteId = {}, gwno = {}", siteId, gwno);
        return mgcService.removeCtrl(siteId, gwno)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "获取场站控制器列表")
    @PostMapping(value = "/findCtrlList")
    public Mono<ListResponse<GwInfoVo>> findCtrlList(@RequestBody ListCtrlParam param) {
        log.info("获取场站控制器列表: param = {}", JsonUtils.toJsonString(param));
        return mgcService.findCtrlList(param);
    }

    @Operation(summary = "控制器升级记录")
    @PostMapping(value = "/upgradeLogList")
    public Mono<ListResponse<UpgradeLogVo>> upgradeLogList(@RequestBody ListUpgradeLogParam param) {
        log.info("获取升级记录: {}", JsonUtils.toJsonString(param));
        return mgcService.upgradeLogList(param);
    }
}
