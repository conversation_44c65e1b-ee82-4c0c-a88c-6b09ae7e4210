package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class OpenHlhtFeignHystrix implements FallbackFactory<OpenHlhtFeignClient> {

    @Override
    public OpenHlhtFeignClient apply(Throwable throwable) {
        log.error("[{}]服务调用异常: {}", DcConstants.KEY_FEIGN_OPEN_HLHT,
            throwable.getMessage(), throwable);
        return new OpenHlhtFeignClient() {

            @Override
            public Mono<ObjectResponse<Pair<Boolean, List<EssStatusBi>>>> getEssStatusBi(
                String siteId) {
                log.error("【服务熔断】 getEssStatusBi. Service = {} siteId = {}",
                    DcConstants.KEY_FEIGN_OPEN_HLHT, siteId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Pair<Boolean, EssDataBi>>> getEssDayBi(String siteId) {
                log.error("【服务熔断】 getEssDayBi. Service = {} siteId = {}",
                    DcConstants.KEY_FEIGN_OPEN_HLHT, siteId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

        };
    }

    @Override
    public <V> Function<V, OpenHlhtFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super OpenHlhtFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }
}
