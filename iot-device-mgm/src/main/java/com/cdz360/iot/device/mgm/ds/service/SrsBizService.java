package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.device.mgm.feign.BizDataCoreFeignClient;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiDailyRoDs;
import com.cdz360.iot.ds.rw.GtiDailyRwDs;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.model.srs.vo.RedisSrsRtData;
import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SrsBizService {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GtiDailyRoDs gtiDailyRoDs;

    @Autowired
    private GtiDailyRwDs gtiDailyRwDs;

    @Autowired
    private RedisSrsRtDataService redisSrsRtDataService;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    /**
     * 逆变器列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<SrsPo>> findSrsList(ListCtrlParam param) {
        return Mono.just(param)
                .map(srsRoDs::getByGwno)
                .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DeviceInfoVo>> findSrsVoList(List<String> siteIdList) {
        return Mono.just(siteIdList)
                .map(gwSiteRefRoDs::findSrsVoList)
                .map(RestUtils::buildListResponse);
    }

    public Mono<ObjectResponse<RedisSrsRtData>> srsInfoInTime(String dno) {
        if (StringUtils.isBlank(dno)) {
            throw new DcArgumentException("辐射仪设备编号无效");
        }

        SrsPo gti = srsRoDs.getByDno(dno);
        if (null == gti) {
            throw new DcArgumentException("辐射仪不存在");
        }

        return Mono.just(dno)
                .map(e -> RestUtils.buildObjectResponse(redisSrsRtDataService.latestRtData(e, LocalDate.now())));
    }

//    public Mono<Long> syncRtData2Sql(LocalDate destDate) {
//        List<GtiVo> gtiList = this.gtiRoDs.findGtiList(new ListGtiParam());
//
//        List<String> siteIdList = gtiList.stream().map(GtiVo::getSiteId)
//                .distinct()
//                .collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(siteIdList)) {
//            return Mono.just(0L);
//        }
//
//        final LocalDate yesterday = null == destDate ? LocalDate.now().plusDays(-1) : destDate;
//        final Date date = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
//        AtomicReference<Map<String, List<PriceItemPo>>> siteProfitMap = new AtomicReference<>();
//        ListSiteProfitParam profitParam = new ListSiteProfitParam();
//        return bizDataCoreFeignClient.getSiteProfitList(profitParam.setSiteIdList(siteIdList))
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .doOnNext(profitList -> siteProfitMap.set(profitList.stream()
//                        .collect(Collectors.toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getPriceItemPoList))))
//                .map(i -> gtiList)
//                .flatMapMany(Flux::fromIterable)
//                .flatMap(gti -> {
//                    // 获取redis数据
//                    RedisPvRtData data = this.redisPvRtDataService.latestRtData(gti.getDno(), yesterday);
//                    if (null == data) {
//                        log.debug("redis 中数据不存在: {} / {}", gti.getGwno(), gti.getSid());
//                        return Mono.just(false);
//                    }
//
//                    List<PriceItemPo> priceItems = siteProfitMap.get().get(gti.getSiteId());
//                    return this.redisPvRtDataService.gtiDayProfit(gti.getDno(), yesterday, priceItems)
//                            .map(profit -> {
//                                GtiDailyPo dailyPo = new GtiDailyPo();
//                                dailyPo.setDno(gti.getDno())
//                                        .setDate(date)
//                                        .setGtiId(gti.getId())
//                                        .setSiteId(gti.getSiteId())
//                                        .setEnable(true)
//                                        .setTodayKwh(data.getTodayKwh())
//                                        .setTodayProfit(profit)
//                                        .setProfitTempId(priceItems.stream().findFirst()
//                                                .orElse(new PriceItemPo().setTemplateId(0L))
//                                                .getTemplateId())
//                                        .setTotalKwh(data.getTotalKwh())
//                                        .setTotalHour(data.getTotalHour());
//                                log.info("daily: {}", JsonUtils.toJsonString(dailyPo));
//                                return this.gtiDailyRwDs.upsetGtiDaily(dailyPo);
//                            });
//                })
//                .count();
//    }

}
