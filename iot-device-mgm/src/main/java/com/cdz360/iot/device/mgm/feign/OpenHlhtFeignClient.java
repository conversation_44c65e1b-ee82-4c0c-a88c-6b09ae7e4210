package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import java.util.List;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_HLHT, fallbackFactory = OpenHlhtFeignHystrix.class)
public interface OpenHlhtFeignClient {

    // 判断场站是否互联互通数据 如果互联互通则返回相应数据
    @GetMapping(value = "/open/site/getEssStatusBi")
    Mono<ObjectResponse<Pair<Boolean, List<EssStatusBi>>>> getEssStatusBi(@RequestParam(value = "siteId") String siteId);

    // 判断场站是否互联互通数据 如果互联互通则返回相应数据
    @GetMapping(value = "/taizhouEms/getEssDayBi")
    Mono<ObjectResponse<Pair<Boolean, EssDataBi>>> getEssDayBi(@RequestParam(value = "siteId") String siteId);

}
