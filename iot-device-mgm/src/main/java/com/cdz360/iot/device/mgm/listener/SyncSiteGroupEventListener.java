package com.cdz360.iot.device.mgm.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.SyncSiteGroupEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.device.mgm.ds.service.SiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = IotConstants.MQ_QUEUE_SITE_GROUP_DEVICE_MGM)
public class SyncSiteGroupEventListener {

    @Autowired
    private SiteService siteService;

    @RabbitHandler
    public void siteGroupListener(String msg) {
        log.info(">> msg = {}", msg);
        try {
            SyncSiteGroupEvent event = JsonUtils.fromJson(msg, SyncSiteGroupEvent.class);

            this.siteService.syncSiteGroupInfo(event.getData());
        } catch (Exception e) {
            log.warn("message:{}", e.getMessage());
            log.warn("收到未识别的消息. msg = {}", msg);
        }

        log.info("<<");
    }
}
