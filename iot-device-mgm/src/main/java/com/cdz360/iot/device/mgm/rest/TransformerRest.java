package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.TransformerService;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.model.transformer.vo.TransformerPlugsVo;
import com.cdz360.iot.model.transformer.vo.TransformerVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.cdz360.iot.model.base.Update;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @Classname TransformerRest
 * @Description
 * @Date 1/20/2021 9:01 AM
 * @Created by Rafael
 */
@Slf4j
@RestController
@Tag(name = "变压器制器相关接口", description = "场站变压器")
@RequestMapping("/device/mgm/transformer")
public class TransformerRest {

    @Autowired
    private TransformerService transformerService;

    @PostMapping("/addTransformer")
    public BaseResponse addTransformer(@Validated @RequestBody TransformerVo req) {
        log.info("add req: {}", JsonUtils.toJsonString(req));
        if (CollectionUtils.isNotEmpty(req.getOrderlyCaps())) {
            req.setAssignableCap(null);
        }
        transformerService.addWrap(req);
        return RestUtils.success();
    }

    @PostMapping("/editTransformer")
    public BaseResponse editTransformer(@Validated(Update.class) @RequestBody TransformerVo req) {
        log.info("req: {}", JsonUtils.toJsonString(req));
        if (CollectionUtils.isNotEmpty(req.getOrderlyCaps())) {
            req.setAssignableCap(null);
        }
        transformerService.editWrapper(req);
        return RestUtils.success();
    }

    @PostMapping("/sendMsgByTransformerId")
    public BaseResponse sendMsgByTransformerId(@RequestParam("transformerId") Long transformerId) {
        log.info("sendMsgByTransformerId transformerId: {}", transformerId);
        transformerService.sendMsgByTransformerId(transformerId);
        return RestUtils.success();
    }

    @GetMapping("/listTransformer")
    public Mono<ListResponse<TransformerVo>> listTransformer(@RequestParam(value = "keyword", required = false) String keyword,
                                                  @RequestParam(value = "transformerId", required = false) Long transformerId,
                                                  @RequestParam(value = "siteId") String siteId,
                                                  @RequestParam(value = "start") long start,
                                                  @RequestParam(value = "size") long size) {
        log.info("keyword: {}, transformerId: {}, siteId: {}, start: {}, size: {}",
                keyword, transformerId, siteId, start, size);
        return transformerService.list(keyword, transformerId, siteId, start, size)
                .map(e -> RestUtils.buildListResponse(e,
                        transformerService.listCount(keyword, transformerId, siteId, start, size)));
    }

    @GetMapping("/disableTransformer")
    public BaseResponse disableTransformer(@RequestParam(value = "id") long id) {
        log.info("id: {}", id);
        return transformerService.disable(id);
    }

    @GetMapping("/getPlugListByTransformerId")
    ListResponse<TransformerPlugsVo> getPlugListByTransformerId(@RequestParam(value = "siteId") String siteId,
                                                                @RequestParam(value = "transformerId", required = false) Long transformerId) {
        log.info("siteId: {}， transformerId： {}", siteId, transformerId);
        return transformerService.getPlugListByTransformerId(siteId, transformerId);
    }

    @GetMapping("/getByNo")
    public ObjectResponse<TransformerPo> getByNo(@RequestParam(value = "no") String no) {
        log.info("no: {}", no);
        return transformerService.getByNo(no);
    }
}