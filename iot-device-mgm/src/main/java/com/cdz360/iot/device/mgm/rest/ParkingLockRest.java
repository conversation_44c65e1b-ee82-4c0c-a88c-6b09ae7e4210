package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.ParkingLockService;
import com.cdz360.iot.model.park.dto.ParkingLockDto;
import com.cdz360.iot.model.park.param.ListParkingLockParam;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "地锁相关接口")
@RestController
@Slf4j
@RequestMapping("/device/mgm/parkingLock")
public class ParkingLockRest {

    @Autowired
    private ParkingLockService parkingLockService;

    @Operation(summary = "获取地锁列表")
    @PostMapping(value = "/parkingLockList")
    public Mono<ListResponse<ParkingLockVo>> parkingLockList(
        @RequestBody ListParkingLockParam param) {
        log.info("获取地锁列表: param = {}", param);
        return parkingLockService.parkingLockList(param);
    }

    @Operation(summary = "移除地锁")
    @PostMapping(value = "/removeParkingLock")
    public Mono<ObjectResponse<ParkingLockVo>> removeParkingLock(
        @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("移除地锁: lockId = {}", lockId);
        return parkingLockService.removeParkingLock(lockId)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "添加地锁")
    @PostMapping(value = "/addParkingLock")
    public Mono<ObjectResponse<ParkingLockVo>> addParkingLock(@RequestBody ParkingLockDto dto) {
        log.info("添加地锁: dto = {}", dto);
        return parkingLockService.addParkingLock(dto)
            .map(RestUtils::buildObjectResponse);
    }
}
