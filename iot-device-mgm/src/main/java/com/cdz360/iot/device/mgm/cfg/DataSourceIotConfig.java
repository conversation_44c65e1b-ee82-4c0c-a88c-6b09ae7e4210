package com.cdz360.iot.device.mgm.cfg;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 访问iot库的datasource
 */
@Configuration
@MapperScan(basePackages = "com.cdz360.iot.device.mgm.ds.mapper", sqlSessionFactoryRef = "iotSqlSessionFactory")
public class DataSourceIotConfig {

    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;
    @Value("${spring.datasource.driver-class-name}")
    private String driverclass;


    @Bean(name = "iotDataSource")
    @Primary
    public DataSource iotDataSource() {
        return DataSourceBuilder
                .create().url(url).username(username).password(password).driverClassName(driverclass)
                .build();
    }


    @Bean(name = "iotTransactionManager")
    @Primary
    public DataSourceTransactionManager iotTransactionManager() {
        return new DataSourceTransactionManager(iotDataSource());
    }


    @Bean(name = "iotSqlSessionFactory")
    @Primary
    public SqlSessionFactory iotSqlSessionFactory(@Qualifier("iotDataSource") DataSource iotDataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(iotDataSource);
        return sessionFactory.getObject();
    }
}
