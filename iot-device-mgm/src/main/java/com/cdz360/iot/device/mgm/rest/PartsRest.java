package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.parts.PartsOpLogService;
import com.cdz360.iot.device.mgm.ds.service.parts.PartsService;
import com.cdz360.iot.model.parts.dto.PartsBatchImportDto;
import com.cdz360.iot.model.parts.dto.PartsImportItem;
import com.cdz360.iot.model.parts.param.ListPartsParam;
import com.cdz360.iot.model.parts.param.PartsBrokenParam;
import com.cdz360.iot.model.parts.param.PartsEditParam;
import com.cdz360.iot.model.parts.param.PartsOpBaseParam;
import com.cdz360.iot.model.parts.param.PartsRollbackParam;
import com.cdz360.iot.model.parts.param.PartsTransReviewParam;
import com.cdz360.iot.model.parts.param.PartsYwOrderRefParam;
import com.cdz360.iot.model.parts.vo.PartsTransTraceVo;
import com.cdz360.iot.model.parts.vo.PartsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "物料相关接口", description = "物料")
public class PartsRest {

    @Autowired
    private PartsService partsService;

    @Autowired
    private PartsOpLogService partsOpLogService;

    @Operation(summary = "物料发货")
    @PostMapping(value = "/device/mgm/parts/transport")
    public Mono<ListResponse<PartsVo>> partsTransport(@RequestBody PartsImportItem param) {
        log.debug("物料发货: param = {}", JsonUtils.toJsonString(param));
        return partsService.partsTransport(param)
            .doOnNext(x -> partsOpLogService.partsTransport(x, param))
            .collectList()
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "物料签收")
    @PostMapping(value = "/device/mgm/parts/receive")
    public Mono<ListResponse<PartsVo>> partsReceive(@RequestBody PartsOpBaseParam param) {
        log.debug("物料签收: param = {}", param);
        return partsService.partsReceive(param)
            .doOnNext(x -> partsOpLogService.partsReceive(x, param.getOpUid()))
            .collectList()
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "物料报废")
    @PostMapping(value = "/device/mgm/parts/broken")
    public Mono<BaseResponse> partsBroken(@RequestBody PartsBrokenParam param) {
        log.debug("物料报废: {}", param);
        return partsService.partsBroken(param)
            .doOnNext(x -> partsOpLogService.partsBroken(x, param))
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "物料退回")
    @PostMapping(value = "/device/mgm/parts/rollback")
    public Mono<BaseResponse> partsRollback(@RequestBody PartsRollbackParam param) {
        log.debug("物料退回: {}", param);
        return partsService.partsRollback(param)
            .doOnNext(x -> partsOpLogService.partsRollback(x, param))
            .collectList()
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "物料编辑", description = "编辑")
    @PostMapping(value = "/device/mgm/parts/edit")
    public Mono<BaseResponse> partsEdit(
        @RequestBody PartsEditParam param) {
        log.debug("物料状态变更: {}", param);
        return partsService.partsEdit(param)
            .doOnNext(x -> partsOpLogService.partsChangeStatus(x, param))
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "物料调拨审批(同意/拒绝)")
    @PostMapping(value = "/device/mgm/parts/transReview")
    public Mono<ListResponse<PartsVo>> partsTransReview(
        @RequestBody PartsTransReviewParam param) {
        log.debug("物料调拨审批(同意/拒绝): {}", param);
        return partsService.partsTransReview(param)
            .doOnNext(x -> partsOpLogService.partsTransReview(x, param))
            .collectList()
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "物料调拨申请")
    @PostMapping(value = "/device/mgm/parts/transApply")
    public Mono<ListResponse<PartsVo>> partsTransApply(
        @RequestBody PartsOpBaseParam param) {
        log.debug("物料调拨申请: {}", param);
        return partsService.partsTransApply(param)
            .doOnNext(x -> partsOpLogService.partsTransApply(x, param))
            .collectList()
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "物料调拨申请取消")
    @PostMapping(value = "/device/mgm/parts/transApplyCancel")
    public Mono<ObjectResponse<PartsVo>> partsTransApplyCancel(
        @RequestBody PartsOpBaseParam param) {
        log.debug("物料调拨申请取消: {}", param);
        return partsService.partsTransApplyCancel(param)
//            .doOnNext(x -> partsOpLogService.partsTransApplyCancel(x, param))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "物料调拨追溯")
    @GetMapping(value = "/device/mgm/parts/transTrace")
    public Mono<ObjectResponse<PartsTransTraceVo>> partsTransTrace(
        @RequestParam("code") String code) {
        log.debug("物料调拨追溯: code = {}", code);
        return partsService.partsTransTrace(code)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "物料批量导入")
    @PostMapping(value = "/device/mgm/parts/batchImport")
    public Mono<BaseResponse> partsBatchImport(@RequestBody PartsBatchImportDto dto) {
        log.debug("物料批量导入: opUid = {}, items.size = {}", dto.getOpUid(), dto.getItems().size());
        return partsService.partsBatchImport(dto);
    }

    @Operation(summary = "个人物料库数量")
    @GetMapping(value = "/device/mgm/parts/count")
    public Mono<ObjectResponse<Long>> partsCount(
        @Parameter(name = "用户ID") @RequestParam Long uid) {
        log.info("个人物料库数量: uid = {}", uid);
        return partsService.partsCount(uid)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "物料列表")
    @PostMapping(value = "/device/mgm/parts/findParts")
    public Mono<ListResponse<PartsVo>> findParts(@RequestBody ListPartsParam param) {
        log.info("获取物料列表: param = {}", JsonUtils.toJsonString(param));
        return partsService.findParts(param);
    }

    @Operation(summary = "物料关联运维工单")
    @PostMapping(value = "/device/mgm/parts/ywOrderRef")
    public Mono<ObjectResponse<PartsYwOrderRefParam>> partsYwOrderRef(@RequestBody PartsYwOrderRefParam param) {
        log.info("物料关联运维工单: param = {}", JsonUtils.toJsonString(param));
        return partsService.partsYwOrderRef(param);
    }
}
