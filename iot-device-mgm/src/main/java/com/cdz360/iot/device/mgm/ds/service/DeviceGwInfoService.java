package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.cfg.IotProperties;
import com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper;
import com.cdz360.iot.device.mgm.dzds.client.CityFeignClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

//import com.cdz360.iot.device.mgm.dzds.service.CityService;

@Service
@EnableConfigurationProperties({IotProperties.class})
@Configuration
public class DeviceGwInfoService {
    private final Logger logger = LoggerFactory.getLogger(DeviceGwInfoService.class);

    @Autowired
    private GwInfoMapper gwInfoMapper;

    //    @Autowired
    //    private CityService cityService;

    // @Autowired
    // private IotProperties iotProperties;

    @Autowired
    private CityFeignClient cityFeignClient;

    @Value("${iot.scan-distance.max}")
    private Double locationDelta;

//    public List<GwInfoPo> listGwBySiteIds(List<Long> siteIds) {
//        if (CollectionUtils.isEmpty(siteIds)) {
//            return List.of();
//        }
//        return this.gwInfoMapper.listGwBySiteIds(siteIds);
//    }


    @Transactional
    public void configGwRef(String oldGwno, String currGwno, String siteId) {
        IotAssert.isTrue(gwInfoMapper.getCountByGwnoAndStatus(currGwno) > 0, "网关不存在或状态非正常，编号:" + currGwno);
        IotAssert.isTrue(gwInfoMapper.getCountBySiteId(siteId) > 0, "场站不存在或场站状态为已删除，id:" + siteId);
        //若siteId对应的gwno与currGwno相等，则认为重复请求，直接更新updateTime
        if (gwInfoMapper.findGwnoBySiteIdAndTrue(siteId) != null && gwInfoMapper.findGwnoBySiteIdAndTrue(siteId).equals(currGwno.trim())) {
            this.gwInfoMapper.insertOrUpdate(currGwno, siteId);
            return;
        }
        IotAssert.isTrue(gwInfoMapper.getCountByGwnoAndTrue(currGwno) == 0, "网关已被其他场站绑定(无权修改)，编号:" + currGwno);

        //判断t_gw_site_ref中是否已存在对应记录
        if (oldGwno != null && oldGwno.length() > 0) {
            //逻辑删除
            this.gwInfoMapper.logicDelete(oldGwno, siteId);
        }
        if (gwInfoMapper.getCountBySiteIdAndTrue(siteId) > 0) {
            logger.info("修改绑定网关时，已存在有效的网关与场站绑定关系，但请求时未上传参数oldCwno或oldGwno不正确，将自动令其关系失效");
            this.gwInfoMapper.updateFalseBySiteId(siteId);
        }
        this.gwInfoMapper.insertOrUpdate(currGwno, siteId);
    }

    public String getCityCode(String cityName) {
        //        return cityService.getCityCode(cityName);
        if (!(cityName.length() > 0)) {
            throw new DcArgumentException("参数cityName为空");
        }
        ObjectResponse<String> result = cityFeignClient.getCityCode(cityName);
        if (result.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            logger.warn("获取城市信息失败. result = {}", result);
            throw new DcArgumentException("iotDzCityApiClient调用失败");
        }
        if (result.getData() == null) {
            logger.warn("根据cityName：“{}” 查询cityCode返回结果为空", cityName);
        }
        return result.getData();
    }

    public List<String> getNotBoundGwno(String cityCode) {
        return this.gwInfoMapper.getNotBoundGwno(cityCode);
    }
}
