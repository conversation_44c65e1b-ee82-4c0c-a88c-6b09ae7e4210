package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.device.mgm.ds.service.SimService;
import com.cdz360.iot.model.sim.param.ListSimParam;
import com.cdz360.iot.model.sim.param.ModifyRelationParam;
import com.cdz360.iot.model.sim.param.SimDataSyncParam;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.vo.CheckResult;
import com.cdz360.iot.model.sim.vo.SimImportItem;
import com.cdz360.iot.model.sim.vo.SimTinyVo;
import com.cdz360.iot.model.sim.vo.SimVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "SIM卡管理相关接口", description = "SIM卡管理")
public class SimRest {

    @Autowired
    private SimService simService;

    @Operation(summary = "接收外部运营商SIM数据")
    @PostMapping(value = "/device/mgm/sim/dataSync")
    public BaseResponse dataSync(@RequestBody SimDataSyncParam param) {
        log.info("dataSync {}", param.toString());
        return simService.dataSync(param);
    }

    @Operation(summary = "SIM卡管理列表")
    @PostMapping(value = "/device/mgm/sim/getList")
    public ListResponse<SimVo> getSimList(@RequestBody ListSimParam param) {
        log.info("getSimList param = {}", param);
        return simService.getSimList(param);
    }

    @Operation(summary = "SIM卡下拉列表（极简）")
    @PostMapping(value = "/device/mgm/sim/getSimTinyList")
    public ListResponse<SimTinyVo> getSimTinyList(@RequestBody ListSimParam param) {
        log.info("getSimTinyList param = {}", param);
        return simService.getSimTinyList(param);
    }

    @Operation(summary = "同步增量卡")
    @GetMapping(value = "/device/mgm/sim/syncAll")
    public Mono<BaseResponse> syncAll() {
        log.info("syncAll start");
        return simService.syncAll();
    }

    @Operation(summary = "同步单个SIM信息")
    @GetMapping(value = "/device/mgm/sim/sync")
    public Mono<ObjectResponse<SimPo>> syncSim(@RequestParam(value = "simId") Long simId) {
        log.info("syncSim simId = {}", simId);
        return simService.syncSim(simId);
    }

    @Operation(summary = "修改SIM和桩的绑定关系")
    @PostMapping(value = "/device/mgm/sim/modifyRelation")
    public BaseResponse modifyRelation(@RequestBody ModifyRelationParam param) {
        log.info("modifyRelation param = {}", param);
        return simService.modifyRelation(param);
    }

    @Operation(summary = "批量修改SIM和桩的绑定关系")
    @PostMapping(value = "/device/mgm/sim/batchModifyRelation")
    public BaseResponse batchModifyRelation(@RequestBody List<SimImportItem> list) {
        log.info("batchModifyRelation list.size = {}", list.size());
        return simService.batchModifyRelation(list);
    }

    @Operation(summary = "SIM导入数据检查")
    @PostMapping(value = "/device/mgm/sim/checkInDB")
    public ListResponse<CheckResult> checkInDB(@RequestBody List<String> codeList) {
        return simService.checkInDB(codeList);
    }

}
