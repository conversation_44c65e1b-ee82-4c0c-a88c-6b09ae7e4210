package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.ds.ro.mapper.CommercialRoMapper;
import com.cdz360.iot.model.site.po.CommercialPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname CommercialService
 * @Description
 * @Date 6/29/2020 1:46 PM
 * @Created by Rafael
 */
@Service
public class CommercialService {
    @Autowired
    private CommercialRoMapper commercialRoMapper;

    List<CommercialPo> getCommByPid(Long commId) {
        return commercialRoMapper.getCommByPid(commId);
    }

    CommercialPo getCommById(Long commId) {
        return commercialRoMapper.getCommById(commId);
    }
}