package com.cdz360.iot.device.mgm.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.*;

/**
 * 文件操作工具类
 *
 * @Author: <PERSON><PERSON><PERSON><EMAIL>
 * @Date: Created on 14:51 2019/1/22.
 */
@Slf4j
public class FileUtils {
    /**
     * 文件或文件夹重命名
     *
     * @param oldPath 被删除文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean renameFile(String oldPath, String newPath) {
        File oldfile = new File(oldPath);
        File newfile = new File(newPath);
        oldfile.renameTo(newfile);
        return true;
    }

    /**
     * @Description: 复制文件（夹）到一个目标文件夹
     * @Author: JLei
     * @CreateDate: 14:34 2019/9/23
     */
    public static void copy(File sourceFile, File targetFile) throws IOException {
        if (!sourceFile.exists()) {
            return;
        }
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        if (sourceFile.isFile()) {
            File objFile = new File(targetFile.getPath() + File.separator + sourceFile.getName());
            //复制文件到目标地
            BufferedInputStream bis = new BufferedInputStream(new FileInputStream(sourceFile));
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(objFile));
            IOUtils.copy(bis, bos);
            IOUtils.closeQuietly(bis);
            IOUtils.closeQuietly(bos);
        } else {
            String targetFolder = targetFile.getPath() + File.separator + sourceFile.getName();
            File _targetFolderFile = new File(targetFolder);
            _targetFolderFile.mkdirs();
            for (File sf : sourceFile.listFiles()) {
                copy(sf, new File(targetFolder));
            }
        }
    }

    /**
     * @Description: 删除文件（夹）
     * @Author: JLei
     * @CreateDate: 14:29 2019/9/23
     */
    public static void delete(File file) {
        if (!file.exists()) {
            return;
        }
        if (file.isFile()) {
            file.delete();
        } else {
            for (File f : file.listFiles()) {
                delete(f);
            }
            file.delete();
        }
    }

    /**
     * @Description: 将文件（夹）移动到令一个文件夹
     * @Author: JLei
     * @CreateDate: 14:28 2019/9/23
     */
    public static void move(File sourceFolderFile, File targetFolderFile) throws IOException {
        copy(sourceFolderFile, targetFolderFile);
        delete(sourceFolderFile);
    }
}
