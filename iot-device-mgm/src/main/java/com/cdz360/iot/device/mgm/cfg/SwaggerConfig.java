package com.cdz360.iot.device.mgm.cfg;

import org.springframework.context.annotation.Configuration;

@Configuration
//@EnableSwagger2
public class SwaggerConfig {

//    @Bean
//    public Docket petApi() {
//
//        final String desc = "物联网场站,设备管理服务. ";
//        final ApiInfo apiInfo = new ApiInfo("iot-device-mgm", desc,
//                "0.0.1", "", null,
//                "", "", Collections.emptyList());
//
//        return new Docket(DocumentationType.SWAGGER_2).select()
//                .apis(RequestHandlerSelectors.basePackage("com.cdz360.iot.device.mgm.rest"))
//                .build()//.globalOperationParameters(List.of(paramUid, paramToken, paramVersion))
//         .apiInfo(apiInfo);
//    }


}
