package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.GtiBizService;
import com.cdz360.iot.model.pv.dto.CntCtrlGtiDto;
import com.cdz360.iot.model.pv.dto.PvRtDataPowerProfit;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.param.PvProfitTrendParam;
import com.cdz360.iot.model.pv.vo.GtiDataInTimeVo;
import com.cdz360.iot.model.pv.vo.GtiSampleData;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@Tag(name = "光伏相关接口", description = "光伏服务")
@RequestMapping("/device/mgm/gti")
public class PvGtiRest {

    @Autowired
    private GtiBizService gtiBizService;

    @Operation(summary = "统计光伏站控制器/逆变器数量")
    @GetMapping(value = "/countCtrlGti")
    public Mono<ObjectResponse<CntCtrlGtiDto>> countCtrlGti(
            @Parameter(name = "场站ID", required = true) @RequestParam String siteId) {
        log.info("统计光伏站控制器/逆变器数量: siteId = {}", siteId);
        return gtiBizService.countCtrlGti(siteId)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取场站控制器及挂载逆变器个数")
    @PostMapping(value = "/findCtrlVoList")
    public Mono<ListResponse<DeviceInfoVo>> findCtrlVoList(@RequestBody(required = false) List<String> siteIdList) {
        log.info("获取场站控制器及挂载逆变器个数: siteId.size = {}", siteIdList.size());
        return gtiBizService.findCtrlVoList(siteIdList);
    }

    @Operation(summary = "逆变器运行数据采样")
    @PostMapping(value = "/gtiRtDataSample")
    public Mono<ListResponse<GtiSampleData>> gtiRtDataSample(@RequestBody DataBiParam param) {
        log.info("逆变器运行数据采样: param = {}", JsonUtils.toJsonString(param));
        return gtiBizService.gtiRtDataSample(param)
                .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "电量收益趋势")
    @PostMapping(value = "/powerProfitTrend")
    public Mono<ListResponse<PvRtDataPowerProfit>> powerProfitTrend(@RequestBody PvProfitTrendParam param) {
        log.info("电量收益趋势: param = {}", JsonUtils.toJsonString(param));
        return gtiBizService.powerProfitTrend(param);
    }

    @Operation(summary = "获取控制器下的逆变器列表")
    @PostMapping(value = "/findGtiList")
    public Mono<ListResponse<GtiVo>> findGtiList(@RequestBody ListGtiParam param) {
        log.info("获取控制器下的逆变器列表: param = {}", JsonUtils.toJsonString(param));
        return gtiBizService.findGtiList(param);
    }

    @Operation(summary = "光伏逆变器实时数据")
    @GetMapping(value = "/gtiInfoInTime")
    public Mono<ObjectResponse<GtiDataInTimeVo>> gtiInfoInTime(
            @Parameter(name = "逆变器设备编号", required = true) @RequestParam String dno) {
        log.info("光伏逆变器实时数据: dno = {}", dno);
        return gtiBizService.gtiInfoInTime(dno)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "同步光伏站日数据到数据库", description = "定时任务凌晨3:00")
    @GetMapping(value = "/syncRtData2Sql")
    public Mono<BaseResponse> syncRtData2Sql(
            @Parameter(name = "指定同步数据日期")
            @RequestParam(value = "destDate", required = false)
            @JsonDeserialize(using = LocalDateDeserializer.class)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate destDate) {
        log.info("同步光伏站日数据到数据库: {}", destDate);
        return gtiBizService.syncRtData2Sql(destDate)
                .doOnNext(i -> log.info("数据同步完成: i = {}", i))
                .map(res -> RestUtils.success());
    }
}
