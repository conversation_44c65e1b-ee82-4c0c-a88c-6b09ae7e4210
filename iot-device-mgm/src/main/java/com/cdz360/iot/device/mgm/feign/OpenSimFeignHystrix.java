package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class OpenSimFeignHystrix implements FallbackFactory<OpenSimFeignClient> {
    @Override
    public OpenSimFeignClient apply(Throwable throwable) {
        return new OpenSimFeignClient() {

            @Override
            public Mono<ObjectResponse<SimPo>> getSimInfo(String iccid, String msisdn, NetworkVendor vendor) {
                log.error("【服务熔断】 获取SIM卡信息. Service = {}, api = getSimInfo. iccid = {}, iccid = {}, vendor = {}",
                        DcConstants.KEY_FEIGN_OPEN_SIM, iccid, msisdn, vendor);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> refreshTinyData() {
                log.error("【服务熔断】 同步增量卡. Service = {}, api = refreshTinyData.",
                        DcConstants.KEY_FEIGN_OPEN_SIM);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, OpenSimFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_SIM);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super OpenSimFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_SIM);
        return null;
    }
}
