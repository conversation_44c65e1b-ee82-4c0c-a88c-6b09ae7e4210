package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.EssBizService;
import com.cdz360.iot.device.mgm.ds.service.RedisEssEquipRtDataService;
import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.ess.dto.EssDto;
import com.cdz360.iot.model.ess.dto.EssEquipTinyDto;
import com.cdz360.iot.model.ess.dto.EssTinyDto;
import com.cdz360.iot.model.ess.param.BmsRelevantParam;
import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import com.cdz360.iot.model.ess.param.ListBmsParam;
import com.cdz360.iot.model.ess.param.ListEssBatteryClusterParam;
import com.cdz360.iot.model.ess.param.ListEssBatteryPackParam;
import com.cdz360.iot.model.ess.param.ListEssEquipLangParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.param.ListPcsParam;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.BmsRelevantVo;
import com.cdz360.iot.model.ess.vo.EquipPowerBiVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryClusterVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryStackVo;
import com.cdz360.iot.model.ess.vo.EssEquipPCSVo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.TotalEssDataBi;
import com.cdz360.iot.model.pcs.vo.PcsVo;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.pv.vo.EssDataInTimeVo;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDate;
import java.util.List;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能相关接口", description = "储能服务")
@RequestMapping("/device/mgm/ess")
public class EssRest {

    @Autowired
    private EssBizService essBizService;

    @Autowired
    private RedisEssEquipRtDataService redisEssEquipRtDataService;

    @Operation(summary = "获取光储Ess信息")
    @GetMapping(value = "/getById")
    public Mono<ObjectResponse<EssPo>> getById(
        @RequestParam(value = "id", required = true) Long id) {
        log.info("获取光储ESS信息。id = {}", id);
        return essBizService.getById(id)
            .map(op -> RestUtils.buildObjectResponse(op.orElse(null)));
    }

    @Operation(summary = "根据gwno获取光储Ess列表")
    @PostMapping(value = "/getByGwno")
    public Mono<ListResponse<EssDto>> getByGwno(@RequestBody ListCtrlParam param) {
        log.info("根据gwno获取光储Ess列表。param = {}", param);
        return essBizService.getByGwno(param)
            .map(op -> RestUtils.buildListResponse(op.orElse(null)));
    }


    @Operation(summary = "获取储能ESS列表", description = "缩略信息")
    @PostMapping(value = "/getEssTinyList")
    public Mono<ListResponse<EssTinyDto>> getEssTinyList(@RequestBody ListEssParam param) {
        log.info("获取储能ESS列表。param = {}", param);
        param.setStartIfNull(0L).setSizeIfNull(10, 1000);
        return Mono.just(RestUtils.buildListResponse(essBizService.getEssTinyList(param)));
    }

    @Operation(summary = "获取储能ESS下属的设备列表", description = "缩略信息")
    @PostMapping(value = "/getEssSubEquipTinyList")
    public Mono<ListResponse<EssEquipTinyDto>> getEssSubEquipTinyList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取储能ESS下属的设备列表。param = {}", param);
        return Mono.just(RestUtils.buildListResponse(essBizService.getEssSubEquipTinyList(param)));
    }

    @Operation(summary = "获取光储Ess列表")
    @PostMapping(value = "/findEssList")
    public Mono<ListResponse<EssVo>> findEssList(@RequestBody ListEssParam param) {
        log.info("获取光储ESS列表。param = {}", param);
        return essBizService.findEssList(param);
    }

    @Operation(summary = "获取光储Ess下挂载的所有设备")
    @PostMapping(value = "/findEquipList")
    public Mono<ListResponse<EssEquipPo>> findEquipList(@RequestBody ListCtrlParam param) {
        log.info("获取光储Ess下挂载的所有设备。param = {}", param);
        return essBizService.findEquipList(param);
    }

    @Operation(summary = "查询站点下获取光储Ess下挂载的所有设备")
    @GetMapping(value = "/findEquipListBySiteId")
    public Mono<ListResponse<EssEquipPo>> findEquipListBySiteId(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "equipType", required = false) EssEquipType equipType) {
        log.info("查询站点下获取光储Ess下挂载的所有设备。siteId = {}, equipType = {}", siteId,
            equipType);
        return essBizService.findEquipListBySiteId(siteId, equipType);
    }

    @SneakyThrows
    @Operation(summary = "获取光储Ess挂载设备(PCS)信息列表")
    @PostMapping(value = "/findEquipPCSList")
    public Mono<ListResponse<EssEquipPCSVo>> findEquipPCSList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取光储Ess挂载设备(PCS)信息列表。param = {}", param);
        return essBizService.findEquipPCSList(param);
    }

    @Operation(summary = "获取电池堆/电池簇与BMS设备编号关系")
    @PostMapping(value = "/bms/relevant")
    public Mono<ObjectResponse<BmsRelevantVo>> bmsRelevant(
        @RequestBody BmsRelevantParam param) {
        log.info("获取电池堆/电池簇与BMS设备编号关系: param = {}", param);
        return Mono.just(essBizService.bmsRelevant(param))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取BMS信息列表")
    @PostMapping(value = "/getBmsList")
    public Mono<ListResponse<BmsPo>> getBmsList(@RequestBody ListBmsParam param) {
        log.info("获取BMS信息列表。param = {}", param);
        return Mono.just(RestUtils.buildListResponse(essBizService.getBmsList(param)));
    }

    @Operation(summary = "获取PCS信息列表")
    @PostMapping(value = "/getPcsList")
    public Mono<ListResponse<PcsVo>> getPcsList(@RequestBody ListPcsParam param) {
        log.info("获取PCS信息列表。param = {}", param);
        return Mono.just(RestUtils.buildListResponse(essBizService.getPcsList(param)));
    }

    @Operation(summary = "获取光储Ess挂载设备(电池堆)信息列表")
    @PostMapping(value = "/findEquipBatteryStackList")
    public Mono<ListResponse<EssEquipBatteryStackVo>> findEquipBatteryStackList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取光储Ess挂载设备(电池堆)信息列表。param = {}", param);
        return essBizService.findEquipBatteryStackList(param);
    }

    @Operation(summary = "获取电池簇列表")
    @PostMapping(value = "/findEquipBatteryClusterList")
    public Mono<ListResponse<EssEquipBatteryClusterVo>> findEquipBatteryClusterList(
        @RequestBody ListEssBatteryClusterParam param) {
        log.info("获取电池簇列表: param = {}", JsonUtils.toJsonString(param));
        return essBizService.findEquipBatteryClusterList(param);
    }

    @Operation(summary = "根据电池堆获取电池簇列表")
    @GetMapping(value = "/findBatteryClusterNosByBatteryStack")
    public Mono<ListResponse<Long>> findBatteryClusterNosByBatteryStack(
        @RequestParam(value = "essDno") String essDno,
        @RequestParam(value = "batteryStackId") Long batteryStackId) {
        log.info("根据电池堆获取电池簇列表。essDno = {},batteryStackId = {}", essDno,
            batteryStackId);
        return redisEssEquipRtDataService.findBatteryClusterNosByBatteryStack(essDno,
            batteryStackId);
    }

    @Operation(summary = "获取电池簇列表")
    @PostMapping(value = "/getBatteryClusterList")
    public Mono<ListResponse<EssBatteryBundlePo>> getBatteryClusterList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取电池簇列表。param = {}", param);
        return essBizService.getBatteryClusterList(param);
    }

    @Operation(summary = "根据电池簇号获取电池组列表")
    @GetMapping(value = "/findBatteryPackNosByBatteryClusterNo")
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackNosByBatteryClusterNo(
        @RequestParam(value = "essDno") String essDno,
        @RequestParam(value = "batteryStackId") Long batteryStackId,
        @RequestParam(value = "batteryClusterNo") Long batteryClusterNo) {
        log.info("根据电池簇号获取电池组列表。essDno = {},batteryStackId = {},batteryClusterNo = {}",
            essDno, batteryStackId, batteryClusterNo);
        return redisEssEquipRtDataService.findBatteryPackNosByBatteryClusterNo(essDno,
            batteryStackId, batteryClusterNo);
    }

    @Operation(summary = "根据essDno获取电池组列表")
    @PostMapping(value = "/findBatteryPackSimpleVoList")
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackSimpleVoList(
        @RequestBody ListEssBatteryPackParam param) {
        log.info("findBatteryPackSimpleVoList。param = {}", param);
        return essBizService.findBatteryPackSimpleVoList(param);
    }

    @Operation(summary = "获取光储Ess挂载设备(电池组)信息列表")
    @PostMapping(value = "/findEquipBatteryPackList")
    public Mono<ListResponse<EssEquipBatteryPackVo>> findEquipBatteryPackList(
        @RequestBody ListEssBatteryPackParam param) {
        log.info("获取光储Ess挂载设备(电池组)信息列表: param = {}", JsonUtils.toJsonString(param));
        return essBizService.findEquipBatteryPackList(param);
    }

    @Operation(summary = "储能统计数据", description = "运行数据统计和ESS统计")
    @PostMapping(value = "/totalBi")
    public Mono<ObjectResponse<TotalEssDataBi>> essTotalBi(@RequestBody DayKwhParam param) {
        log.info("储能统计数据: param = {}", JsonUtils.toJsonString(param));
        return this.essBizService.essTotalBi(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取有效挂载设备的EquipId")
    @GetMapping(value = "/getEquipPowerBiVo")
    public Mono<ObjectResponse<EquipPowerBiVo>> getEquipPowerBiVo(
        @RequestParam(value = "essDno") String essDno) {
        log.info("获取有效挂载设备的EquipId: essDno = {}", essDno);
        return this.essBizService.getEquipPowerBiVo(essDno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "ESS实时数据获取")
    @GetMapping(value = "/essInfoInTime")
    public Mono<ObjectResponse<EssDataInTimeVo>> essInfoInTime(
        @RequestParam(value = "dno") String dno) {
        log.info("ESS实时数据获取 dno: {}", dno);
        return essBizService.essInfoInTime(dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "同步储能站日数据到数据库", description = "定时任务凌晨3:00")
    @GetMapping(value = "/syncRtData2Sql")
    public Mono<BaseResponse> syncRtData2Sql(
        @Parameter(name = "指定同步数据日期")
        @RequestParam(value = "destDate", required = false)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate destDate) {
        log.info("同步储能站日数据到数据库: {}", destDate);
        return essBizService.syncRtData2Sql(destDate)
            .doOnNext(i -> log.info("数据同步完成: i = {}", i))
            .map(res -> RestUtils.success());
    }

    @Operation(summary = "台州储能站日数据update")
    @PostMapping(value = "/taizhouDailyUpdate")
    public Mono<ObjectResponse<Boolean>> taizhouDailyUpdate(
        @RequestBody EssDailyPo dailyPo) { // 只能存储到一个pcs中 其他pcs为0
        log.info("台州储能站日数据update: {}", JsonUtils.toJsonString(dailyPo));
        return essBizService.upsetEssDaily(dailyPo)
            .map(res -> RestUtils.buildObjectResponse(res));
    }


    @Operation(summary = "获取户储设备字段的多语言文案", description = "获取户储设备字段的多语言文案")
    @PostMapping(value = "/getEssLangList")
    public Mono<ListResponse<EssEquipLangPo>> getEssLangList(
        @RequestBody ListEssEquipLangParam param) {
        log.debug("获取设备字段的多语言文案 param= {}", param);
        Assert.notNull(param.getDno(), "参数错误, dno不能为空");
        Assert.notNull(param.getLang(), "参数错误, lang不能为空");
        List<EssEquipLangPo> result = essBizService.getEssLangList(param);
        return Mono.just(RestUtils.buildListResponse(result));
    }

    @Operation(summary = "获取设备字段的多语言文案", description = "获取设备字段的多语言文案")
    @PostMapping(value = "/getEquipLangList")
    public Mono<ListResponse<EssEquipLangPo>> getEquipLangList(
        @RequestBody ListEssEquipLangParam param) {
        log.debug("获取设备字段的多语言文案 param= {}", param);
        Assert.notNull(param.getDno(), "参数错误, dno不能为空");
//        Assert.notNull(param.getEquipType(), "参数错误, equipType不能为空");

        Assert.notNull(param.getLang(), "参数错误, lang不能为空");
        List<EssEquipLangPo> result = essBizService.getEquipLangList(param);
        return Mono.just(RestUtils.buildListResponse(result));
    }

    @Operation(summary = "获取设备故障告警的多语言文案", description = "获取设备故障告警的多语言文案")
    @PostMapping(value = "/getAlarmLangList")
    public Mono<ListResponse<EssEquipAlarmLangDto>> getAlarmLangList(
        @RequestBody ListAlarmLangParam param) {
        log.debug("获取设备故障告警的多语言文案 param= {}", param);
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getDnos()), "参数错误, dnos不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getCodes()), "参数错误, codes不能为空");
        List<EssEquipAlarmLangDto> result = essBizService.getAlarmLangList(param);
        return Mono.just(RestUtils.buildListResponse(result));
    }
}
