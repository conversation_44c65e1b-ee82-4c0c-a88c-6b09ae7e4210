package com.cdz360.iot.device.mgm.cfg;

import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.iot.common.base.IotConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {

    /**
     * 同步桩/枪信息的队列配置
     */
    @Bean
    public Queue iotDeviceQueue() {
        return new Queue(IotConstants.DM_IOT_QUEUE_NAME, true, false, true);
    }

    @Bean
    DirectExchange exchangeIotDevice() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT, true, true);
    }

    @Bean
    Binding bindingExchangeIotDevice(Queue iotDeviceQueue, DirectExchange exchangeIotDevice) {

        return BindingBuilder.bind(iotDeviceQueue).to(exchangeIotDevice)
            .with(DcMqConstants.MQ_ROUTING_KEY_IOT);
    }

    /**
     * 同步场站信息的队列配置
     */
    @Bean
    public Queue siteQueue() {
        return new Queue(IotConstants.SITE_SYNC_QUEUE_NAME, true, false, true);
    }

    /**
     * 同步场站组信息
     */
    @Bean
    public Queue siteGroupQueue() {
        return new Queue(IotConstants.MQ_QUEUE_SITE_GROUP_DEVICE_MGM, true, false, true);
    }

    @Bean
    DirectExchange exchangeSite() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_INFRASTRUCTURE, true, true);
    }

    @Bean
    Binding bindingExchangeSite(Queue siteQueue, DirectExchange exchangeSite) {

        return BindingBuilder.bind(siteQueue).to(exchangeSite)
            .with(DcMqConstants.MQ_ROUTING_KEY_SITE);
    }

    @Bean
    Binding bindingExchangeSiteGroup(Queue siteGroupQueue, DirectExchange exchangeSite) {

        return BindingBuilder.bind(siteGroupQueue).to(exchangeSite)
            .with(DcMqConstants.MQ_ROUTING_KEY_SITE_GROUP);
    }


    /**
     * 同步商户信息的队列配置
     */
    @Bean
    public Queue commQueue() {
        return new Queue(IotConstants.MQ_QUEUE_COMMERCIAL_DEVICE_MGM, true, false, true);
    }

    @Bean
    DirectExchange exchangeComm() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_INFRASTRUCTURE, true, true);
    }

    @Bean
    Binding bindingExchangeComm(Queue commQueue, DirectExchange exchangeComm) {

        return BindingBuilder.bind(commQueue).to(exchangeComm)
            .with(DcMqConstants.MQ_ROUTING_KEY_COMMERCIAL);
    }
}
