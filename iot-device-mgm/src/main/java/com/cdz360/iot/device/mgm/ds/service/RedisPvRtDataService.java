package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.device.mgm.model.basic.po.PriceItemPo;
import com.cdz360.iot.model.pv.dto.PvRtDataDto;
import com.cdz360.iot.model.pv.vo.RedisPvRtData;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RedisPvRtDataService {
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final static Calendar CALENDAR = Calendar.getInstance();

    private static final String PRE_REDIS_KEY = "pv:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 逆变器编号列表某一天发电收益
     *
     * @param dnoList 逆变器编号列表
     * @param date 统计查询日期
     * @param tempList 收益计算
     * @return
     */
    public Mono<BigDecimal> gtiDayProfit(List<String> dnoList, LocalDate date, List<PriceItemPo> tempList) {
        if (CollectionUtils.isEmpty(dnoList)) {
            return Mono.just(BigDecimal.ZERO);
        }
        return Flux.fromIterable(dnoList)
                .flatMap(dno -> this.gtiDayProfit4RedisKey(formatKey(dno, date), tempList))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 微网控制器某一天发电收益
     *
     * @param dno 逆变器编号
     * @param date 统计查询日期
     * @param tempList 收益计算
     * @return
     */
    public Mono<BigDecimal> gtiDayProfit(String dno, LocalDate date, List<PriceItemPo> tempList) {
        String key = formatKey(dno, date);
        return this.gtiDayProfit4RedisKey(key, tempList);
    }

    /**
     * 微网控制器某一天发电收益
     *
     * @param key redis 中key值
     * @param tempList 收益计算
     * @return
     */
    public Mono<BigDecimal> gtiDayProfit4RedisKey(String key, List<PriceItemPo> tempList) {
        BigDecimal profit = BigDecimal.ZERO; // 收益
        RedisPvRtData last = null;
        try {
            int start = 0;
            List<RedisPvRtData> dataList = this.findGtiRtData(key, start, start + 1000);
            if (!CollectionUtils.isEmpty(dataList)) {
                int size = dataList.size();
                int idx = 0;
                while (true) {
                    if (idx == size) {
                        dataList = this.findGtiRtData(key, start, start + 1000);
                        if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
                            break;
                        }

                        size = dataList.size();
                        idx = 0;
                    }

                    start++;
                    RedisPvRtData rtData = dataList.get(idx++);
                    if (null != rtData) {
                        BigDecimal todayKwh = null;
                        if (null == last) {
                            todayKwh = rtData.getTodayKwh();
                        } else {
                            todayKwh = rtData.getTodayKwh()
                                    .subtract(last.getTodayKwh());
                        }

                        if (null == todayKwh) {
                            continue;
                        } else if (DecimalUtils.lteZero(todayKwh)) {
                            if (DecimalUtils.ltZero(todayKwh)) {
                                // 若当日发电量趋势变成递减，则重置收益
                                profit = BigDecimal.ZERO;
                            }
                            last = rtData;
                            continue;
                        }

                        profit = profit.add(todayKwh.multiply(getYuanPerKwh(rtData.getTime(), tempList)));
                        last = rtData;
                        rtData = null;
                        todayKwh = null;
                    }
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Mono.just(profit);
    }

    // FIXME: 这里可以返回找到的时段信息
    private static BigDecimal getYuanPerKwh(Date time, List<PriceItemPo> temp) {
        CALENDAR.setTime(time);
        int minute = CALENDAR.get(Calendar.HOUR_OF_DAY) * 60 + CALENDAR.get(Calendar.MINUTE);
        Optional<PriceItemPo> target = temp.stream()
                .filter(i -> i.getStartTime() < minute && minute <= i.getStopTime())
                .findFirst();
        return target.isPresent() ? target.get().getPrice() : BigDecimal.ZERO;
    }

    /**
     * 获取逆变器运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisPvRtData> findGtiRtData(String key, long start, long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        List<String> range = redisTemplate.opsForList()
                .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }
        return range.stream()
                .map(value -> JsonUtils.fromJson(value, RedisPvRtData.class))
                .collect(Collectors.toList());
    }

    /**
     * 逆变器今天的发电量
     *
     * @param dnoList 逆变器编号
     * @return
     */
    public Mono<BigDecimal> gtiDayKwh(List<String> dnoList, LocalDate date) {
        if (CollectionUtils.isEmpty(dnoList)) {
            return Mono.just(BigDecimal.ZERO);
        }

        return Flux.fromIterable(dnoList)
                .map(dno -> {
                    RedisPvRtData data = this.latestRtData(dno, date);
                    return null == data ? BigDecimal.ZERO : data.getTodayKwh();
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 逆变器今天的输出功率
     * @param dnoList
     * @param date
     * @return
     */
    public Mono<BigDecimal> gtiDayPower(List<String> dnoList, LocalDate date) {
        if (CollectionUtils.isEmpty(dnoList)) {
            return Mono.just(BigDecimal.ZERO);
        }

        return Flux.fromIterable(dnoList)
                .map(dno -> {
                    RedisPvRtData data = this.latestRtData(dno, date);
                    return null == data ? BigDecimal.ZERO : data.getActivePower();
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 逆变器今天的发电量
     *
     * @param dnoList 逆变器编号
     * @return
     */
    public Mono<BigDecimal> gtiTodayKwh(List<String> dnoList) {
        final LocalDate date = LocalDate.now();
        return Flux.fromIterable(dnoList)
                .map(dno -> {
                    RedisPvRtData data = this.latestRtData(dno, date);
                    return null == data ? BigDecimal.ZERO : data.getTodayKwh();
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 逆变器今天的发电量
     *
     * @param dno 逆变器编号
     * @return
     */
    public BigDecimal gtiTodayKwh(String dno) {
        RedisPvRtData data = this.latestRtData(dno, LocalDate.now());
        return null == data ? BigDecimal.ZERO : data.getTodayKwh();
    }

    /**
     * 今天光伏运行时数据压入
     *
     * @param dno 逆变器编号
     * @param data 数据
     */
    public void pushRtData(String dno, PvRtDataDto data) {
        String key = formatKey(dno, LocalDate.now());

        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(RedisPvRtData.convert(data)));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时数据压入
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @param data 数据
     */
    public void pushRtData(String dno, LocalDate date, PvRtDataDto data) {
        String key = formatKey(dno, date);
        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(RedisPvRtData.convert(data)));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时数据批量压入
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @param dataList 数据
     */
    public void pushRtData(String dno, LocalDate date, List<PvRtDataDto> dataList) {
        String key = formatKey(dno, date);
        redisTemplate.opsForList()
                .rightPushAll(key, dataList.stream()
                        .filter(data -> StringUtils.isNotBlank(data.getDno()))
                        .map(RedisPvRtData::convert)
                        .map(JsonUtils::toJsonString)
                        .collect(Collectors.toList()));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时先拆分数据，再将数据批量压入
     *
     * @param date 日期
     * @param dataList 数据
     */
    public void splitPushRtData(LocalDate date, List<PvRtDataDto> dataList) {
        Map<String, List<PvRtDataDto>> collect = dataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.getDno()))
                .collect(Collectors.groupingBy(PvRtDataDto::getDno));
        collect.keySet().forEach(dno -> this.pushRtData(dno, date, collect.get(dno)));
    }

    public RedisPvRtData pvSrcInfo(String dno) {
        return this.latestRtData(dno, LocalDate.now());
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param dno
     * @param date
     * @return
     */
    public RedisPvRtData latestRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, RedisPvRtData.class);
    }

    private static String formatKey(String dno, LocalDate date) {
        return PRE_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatKey(String dno, String date) {
        return PRE_REDIS_KEY + dno + ":" + date;
    }
}
