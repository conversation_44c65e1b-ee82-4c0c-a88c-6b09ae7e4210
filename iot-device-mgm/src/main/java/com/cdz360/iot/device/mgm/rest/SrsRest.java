package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.ds.service.SrsBizService;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.model.srs.vo.RedisSrsRtData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "辐射仪相关接口", description = "辐射仪服务")
@RequestMapping("/device/mgm/srs")
public class SrsRest {

    @Autowired
    private SrsBizService srsBizService;

    @Operation(summary = "获取控制器下的辐射仪列表")
    @PostMapping(value = "/findSrsList")
    public Mono<ListResponse<SrsPo>> findSrsList(@RequestBody ListCtrlParam param) {
        log.info("获取控制器下的辐射仪列表: param = {}", JsonUtils.toJsonString(param));
        return srsBizService.findSrsList(param);
    }

    @Operation(summary = "获取场站控制器及挂载辐射仪个数")
    @PostMapping(value = "/findSrsVoList")
    public Mono<ListResponse<DeviceInfoVo>> findSrsVoList(@RequestBody(required = false) List<String> siteIdList) {
        log.info("获取场站控制器及挂载辐射仪个数: siteId.size = {}", siteIdList.size());
        return srsBizService.findSrsVoList(siteIdList);
    }

    @Operation(summary = "辐射仪实时数据")
    @GetMapping(value = "/srsInfoInTime")
    public Mono<ObjectResponse<RedisSrsRtData>> srsInfoInTime(
            @Parameter(name = "辐射仪设备编号", required = true) @RequestParam(value = "dno") String dno) {
        log.info("辐射仪实时数据: dno = {}", dno);
        return srsBizService.srsInfoInTime(dno);
    }

//    @Operation(summary = "同步光伏站日数据到数据库", description = "定时任务凌晨3:00")
//    @GetMapping(value = "/syncRtData2Sql")
//    public Mono<BaseResponse> syncRtData2Sql(
//            @Parameter(name = "指定同步数据日期")
//            @RequestParam(value = "destDate", required = false)
//            @JsonDeserialize(using = LocalDateDeserializer.class)
//            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate destDate) {
//        log.info("同步光伏站日数据到数据库: {}", destDate);
//        return srsBizService.syncRtData2Sql(destDate)
//                .doOnNext(i -> log.info("数据同步完成: i = {}", i))
//                .map(res -> RestUtils.success());
//    }

}
