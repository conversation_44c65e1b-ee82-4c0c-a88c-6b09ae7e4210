package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.parts.PartsTypeService;
import com.cdz360.iot.model.parts.dto.PartsTypeDto;
import com.cdz360.iot.model.parts.param.ListPartsTypeParam;
import com.cdz360.iot.model.parts.param.PartsCheckParam;
import com.cdz360.iot.model.parts.vo.PartsTypeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "物料相关接口", description = "物料")
public class PartsTypeRest {

    @Autowired
    private PartsTypeService partsTypeService;

    @Operation(summary = "物料规格列表")
    @PostMapping(value = "/device/mgm/parts/findPartsType")
    public Mono<ListResponse<PartsTypeVo>> findPartsType(@RequestBody ListPartsTypeParam param) {
        log.info("物料规格列表: param = {}", JsonUtils.toJsonString(param));
        return partsTypeService.findPartsType(param);
    }

    @Operation(summary = "新增物料规格")
    @PostMapping(value = "/device/mgm/parts/addPartsType")
    public Mono<ObjectResponse<PartsTypeVo>> addPartsType(@RequestBody PartsTypeDto dto) {
        log.info("新增物料规格: dto = {}", JsonUtils.toJsonString(dto));
        return partsTypeService.addPartsType(dto)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "物料导入前检查")
    @PostMapping(value = "/device/mgm/parts/checkInDB")
    public Mono<ListResponse<PartsCheckParam>> checkInDB(@RequestBody List<PartsCheckParam> items) {
        log.info("parts checkInDB items.size = {}", items.size());
        return partsTypeService.checkInDB(items)
            .map(RestUtils::buildListResponse);
    }

}
