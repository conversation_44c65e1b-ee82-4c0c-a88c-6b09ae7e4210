package com.cdz360.iot.device.mgm.model.basic.param;

import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.EvseVendor;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.codec.multipart.FilePart;

@Schema(description = "升级包上传参数")
@Data
@Accessors(chain = true)
public class UploadBundleParam {
    @Schema(description = "升级包文件", required = true)
    private FilePart file;

    @Schema(description = "包类型", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BundleType type;

    @Schema(description = "供应商（桩软件必填，控制器软件不填）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseVendor vendor;

    @Schema(description = "软件版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long swVerCode;

    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opId;

    @Schema(description = "上传大小", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long fileSize;

    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;
}
