package com.cdz360.iot.device.mgm.utils;

public class GeoUtils {

    private static final Double R = 6378.137;

    /**
     * 2个点的距离，单位米
     * @param latStart
     * @param lonStart
     * @param latEnd
     * @param lonEnd
     * @return
     */
    public static Double getDistance(Double latStart, Double lonStart, Double latEnd, Double lonEnd) {
        double lon1 = (Math.PI / 180) * lonStart;
        double lon2 = (Math.PI / 180) * lonEnd;
        double lat1 = (Math.PI / 180) * latStart;
        double lat2 = (Math.PI / 180) * latEnd;

        double d = Math.acos(Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1)) * R;
        return d * 1000;
    }

    /**
     * 同getDistance，从高德js复制来的算法
     * @param latStart
     * @param lonStart
     * @param latEnd
     * @param lonEnd
     * @return
     */
    public static Double getDistance1(Double latStart, Double lonStart, Double latEnd, Double lonEnd) {
        double b = Math.PI / 180;
        double c = Math.sin((latEnd - latStart) * b / 2);
        double d = Math.sin((lonEnd - lonStart) * b / 2);
        double a = c * c + d * d * Math.cos(latStart * b) * Math.cos(latEnd * b);

        return 12756274 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    }
}
