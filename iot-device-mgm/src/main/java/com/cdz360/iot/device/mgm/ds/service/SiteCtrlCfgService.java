package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.dzds.client.IotWorkFeign;
import com.cdz360.iot.ds.ro.SiteCtrlCfgRoDs;
import com.cdz360.iot.ds.ro.SiteCtrlRoDs;
import com.cdz360.iot.ds.rw.SiteCtrlCfgRwDs;
import com.cdz360.iot.model.site.ctrl.SiteCtrlInfo;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import com.cdz360.iot.model.site.vo.SiteCtrlCfgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteCtrlCfgService {

    @Autowired
    private SiteCtrlCfgRoDs siteCtrlCfgRo;

    @Autowired
    private SiteCtrlCfgRwDs siteCtrlCfgRwDs;

    @Autowired
    private SiteCtrlRoDs siteCtrlRoDs;

    @Autowired
    private IotWorkFeign iotWorkFeign;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public int addOrUpdate(SiteCtrlCfgPo po) {
        return siteCtrlCfgRwDs.insertOrUpdate(po);
    }

    public SiteCtrlCfgVo findByCtrlNo(String ctrlNum) {
        if (StringUtils.isBlank(ctrlNum)) {
            throw new DcArgumentException("请提供场站控制器编号", Level.WARN);
        }

        SiteCtrlCfgPo po = siteCtrlCfgRo.selectByNum(ctrlNum);
        if (null == po) {
            throw new DcArgumentException("场站控制器编号" + ctrlNum + "对应配置不存在，请添加", Level.WARN);
        }

        return this.po2Vo(po);
    }

    private SiteCtrlCfgVo po2Vo(SiteCtrlCfgPo po) {
        SiteCtrlCfgVo vo = new SiteCtrlCfgVo();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    public void send2GetCfg(String ctrlNum) {
        // iot worker 下发
        iotWorkFeign.cfgGet(ctrlNum);
    }

    public SiteCtrlCfgVo getBySiteCtrl(String ctrlNum) {
        // 前面已经下发获取指令，所以这个接口才能获取到配置配置
        // 从Redis 中获取，没有则返回空对象
        if (StringUtils.isBlank(ctrlNum)) {
            throw new DcArgumentException("场站控制器编号不能为空", Level.WARN);
        }

        // 直接存入redis
        String key = SiteCtrlInfo.redisKey(ctrlNum);
        String data = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(data)) {
            return null;
        }

        SiteCtrlInfo info = JsonUtils.fromJson(data, SiteCtrlInfo.class);
        if (null == info) {
            return null;
        }

        SiteCtrlCfgVo vo = SiteCtrlInfo.map2Vo(info);
        vo.setCtrlNum(ctrlNum);

        SiteCtrlPo siteCtrlPo = siteCtrlRoDs.selectByNum(ctrlNum);
        if(siteCtrlPo == null) {
            log.error("装配配置信息时，DB不存在可用的控制器: {}", ctrlNum);
        } else {
            vo.setCtrlName(siteCtrlPo.getName());
        }

        log.info("控制器上报配置: vo = {}", vo.toString());
        return vo;
    }
}
