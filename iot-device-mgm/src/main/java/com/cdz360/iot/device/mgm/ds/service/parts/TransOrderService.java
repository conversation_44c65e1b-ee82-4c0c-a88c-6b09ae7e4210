package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.iot.ds.ro.TransOrderRoDs;
import com.cdz360.iot.ds.rw.PartsTransRefRwDs;
import com.cdz360.iot.ds.rw.TransOrderRwDs;
import com.cdz360.iot.model.parts.dto.PartsImportItem;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.po.StoragePo;
import com.cdz360.iot.model.parts.po.TransOrderPo;
import com.cdz360.iot.model.parts.type.TransOrderStatus;
import com.cdz360.iot.model.parts.type.TransOrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class TransOrderService {

    @Autowired
    private PartsTransRefRwDs partsTransRefRwDs;

    @Autowired
    private TransOrderRoDs transOrderRoDs;

    @Autowired
    private TransOrderRwDs transOrderRwDs;

    @Autowired
    private StorageService storageService;

    @Autowired
    private ExpressService expressService;

    public void createTransOrder(String partsCode, TransOrderPo transOrder) {
        int time = 10;
        String orderNo;
        do {
            orderNo = "T" + RandomStringUtils.randomNumeric(7);
        } while (time-- > 0 && null != transOrderRoDs.getByOrderNo(orderNo));
        transOrder.setOrderNo(orderNo);
        boolean b = transOrderRwDs.insertTransOrder(transOrder);
        b = partsTransRefRwDs.insertPartsTransRef(partsCode, orderNo);
        if (!b) {
            log.warn("调拨单创建不成功");
        }
    }

    @Transactional
    public void partsTransPersonal(
        Long opUid, Long toUid, String toUserName,
        String expressNo, String expressName, PartsPo parts) {
        // 指向仓库(个人)
        final StoragePo storage = storageService.getPersonalStorage(toUid, toUserName);
        this.partsTransPersonal(opUid, expressNo, expressName, storage, parts);
    }

    @Transactional
    public void partsTransPersonal(PartsImportItem param, PartsPo parts) {
        final StoragePo storage = storageService.getPersonalStorage(
            param.getApplyId(), param.getApplyName());

        for (int i = 0; i < param.getApplyNum(); i++) {
            this.partsTransPersonal(
                param.getOpUid(), param.getExpressNo(), param.getExpressName(), storage, parts);
        }
    }

    @Transactional
    public void partsTransPersonal(
        Long opUid, String expressNo, String expressName,
        final StoragePo toStorage, PartsPo parts) {
        TransOrderPo transOrder = new TransOrderPo()
            .setType(TransOrderType.TRANSFER)
            .setCreateBy(opUid)
            .setStatus(TransOrderStatus.TRANSFERRING)
            .setFromCode(parts.getStoreCode())
            .setToCode(toStorage.getCode());
        this.createTransOrder(parts.getCode(), transOrder);

        // 快递单创建
        this.expressService.createExpress(
            transOrder.getOrderNo(), expressNo, expressName);
    }

    /**
     * 物料签收后工单状态调整
     *
     * @param partsCode 物料ID
     */
    public void postPartsReceive(String partsCode) {
        transOrderRwDs.postPartsReceive(partsCode); // 物料签收后工单状态调整
    }
}
