package com.cdz360.iot.device.mgm.dzds.client;

//@FeignClient(name = "device-business-rest")
//@RequestMapping("/api/site")
//public interface SiteFeignClient {
//    @PostMapping(value = "/getPagedSiteGeoList", consumes = MediaType.APPLICATION_JSON_VALUE,
//            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    ListResponse<DzSiteDto> getPagedSiteGeoList(@RequestBody SiteParam siteParam);
//
////    @PostMapping(value = "/updateSiteInfo", consumes = MediaType.APPLICATION_JSON_VALUE,
////            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
////    BaseResponse updateSiteInfo(@RequestBody SiteParam siteParam);
//}