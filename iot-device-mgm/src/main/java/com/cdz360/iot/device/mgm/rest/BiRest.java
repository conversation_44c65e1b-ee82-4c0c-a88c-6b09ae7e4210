package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.BiService;
import com.cdz360.iot.device.mgm.model.bi.vo.DeviceBi;
import com.cdz360.iot.device.mgm.model.bi.vo.ListDeviceParam;
import com.cdz360.iot.model.evse.dto.SiteDeviceBiDto;
import com.cdz360.iot.model.evse.vo.EvseStatusPowerBiVo;
import com.cdz360.iot.model.evse.vo.PlugStatusBiVo;
import com.cdz360.iot.model.evse.vo.PlugSupplyBiVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "数据统计相关接口", description = "统计")
@RequestMapping(value = "/iot/bi", produces = MediaType.APPLICATION_JSON_VALUE)
public class BiRest {

    @Autowired
    private BiService biService;

    @Operation(summary = "设备统计数据")
    @GetMapping(value = "/device")
    public ObjectResponse<DeviceBi> getDeviceBi(
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.info("siteId = {}", siteId);
        DeviceBi bi = this.biService.getDeviceBi(siteId, null);
        return new ObjectResponse<>(bi);
    }

    @Operation(summary = "场站列表设备统计数据")
    @PostMapping(value = "/getSitesDevice")
    public ListResponse<DeviceBi> getSitesDevice(@RequestBody ListDeviceParam param) {
        log.info("site size = {}", param.getSiteIdList().size());
        List<DeviceBi> bi = this.biService.getSitesDevice(param);
        return new ListResponse<>(bi);
    }


    @Operation(summary = "统计场站桩/枪数量")
    @PostMapping(value = "/getSiteDeviceBiList")
    public ListResponse<SiteDeviceBiDto> getSiteDeviceBiList(
        @RequestParam(value = "siteIdList", required = false) List<String> siteIdList) {
        var list = this.biService.getSiteDeviceBiList(siteIdList);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "根据电流类型统计桩/枪数量")
    @PostMapping(value = "/getPlugSupplyBi")
    public ListResponse<PlugSupplyBiVo> getPlugSupplyBi(
        @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        var list = this.biService.getPlugSupplyBi(commIdChain);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "根据状态统计桩/枪数量")
    @PostMapping(value = "/getPlugStatusBi")
    public ListResponse<PlugStatusBiVo> getPlugStatusBi(
        @RequestParam(value = "provinceCode", required = false) String provinceCode,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        var list = this.biService.getPlugStatusBi(provinceCode, cityCode, siteId, commIdChain);
        return RestUtils.buildListResponse(list);
    }


    @Operation(summary = "根据状态统计桩功率")
    @PostMapping(value = "/getEvseStatusPowerBi")
    public ListResponse<EvseStatusPowerBiVo> getEvseStatusPowerBi(
        @RequestParam(value = "provinceCode", required = false) String provinceCode,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        var list = this.biService.getEvseStatusPowerBi(provinceCode, cityCode, siteId, commIdChain);
        return RestUtils.buildListResponse(list);
    }


}
