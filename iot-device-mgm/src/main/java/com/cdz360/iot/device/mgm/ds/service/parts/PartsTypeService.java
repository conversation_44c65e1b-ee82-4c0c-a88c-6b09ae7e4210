package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.PartsTypeRoDs;
import com.cdz360.iot.ds.rw.PartsTypeRwDs;
import com.cdz360.iot.model.parts.dto.PartsTypeDto;
import com.cdz360.iot.model.parts.param.ListPartsTypeParam;
import com.cdz360.iot.model.parts.param.PartsCheckParam;
import com.cdz360.iot.model.parts.po.PartsTypePo;
import com.cdz360.iot.model.parts.vo.PartsTypeVo;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PartsTypeService {

    @Autowired
    private PartsTypeRwDs partsTypeRwDs;

    @Autowired
    private PartsTypeRoDs partsTypeRoDs;

    public Mono<ListResponse<PartsTypeVo>> findPartsType(ListPartsTypeParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 1000) {
                    param.setSize(999);
                }
            })
            .map(this.partsTypeRoDs::findPartsType)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.partsTypeRoDs.countPartsType(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<PartsTypeVo> addPartsType(PartsTypeDto dto) {
        IotAssert.isNotBlank(dto.getCode(), "请输入物料编码");
        IotAssert.isNotBlank(dto.getName(), "请输入物料名称");
        IotAssert.isNotBlank(dto.getFullModel(), "请输入物料规格型号");

        final PartsTypePo old = partsTypeRoDs.getOneByFields(
            dto.getName(), dto.getCode(), dto.getFullModel());
        IotAssert.isNull(old, "物料规格已经存在");

        return Mono.just(dto)
            .map(PartsTypeService::dto2Po)
            .doOnNext(partsTypeRwDs::insertPartsType)
            .map(PartsTypeService::po2Vo);
    }

    private static PartsTypeVo po2Vo(PartsTypePo po) {
        final PartsTypeVo result = new PartsTypeVo();
        result.setId(po.getId())
            .setName(po.getName())
            .setCode(po.getCode())
            .setFullModel(po.getFullModel());
        return result;
    }

    private static PartsTypePo dto2Po(PartsTypeDto dto) {
        final PartsTypePo result = new PartsTypePo();
        result.setName(dto.getName())
            .setCode(dto.getCode())
            .setFullModel(dto.getFullModel());
        return result;
    }

    public Mono<List<PartsCheckParam>> checkInDB(List<PartsCheckParam> items) {
        List<PartsTypePo> poList = partsTypeRoDs.getByFields(items.stream().distinct().collect(
            Collectors.toList()));
        Map<String, PartsTypePo> map = poList.stream().collect(Collectors.toMap(
            PartsTypePo::getCode, e -> e, (v1, v2) -> v1));

        return Mono.just(items).map(t -> t.stream().peek(e -> {
            PartsTypePo po = map.get(e.getTypeCode());
            if (po != null) {
                e.setIsSubsistent(true);
                e.setPartsTypeId(po.getId());
            } else {
                e.setIsSubsistent(false);
            }
        }).collect(Collectors.toList()));
    }

    public PartsTypePo getOneByFieldsNotExit(String typeName) {
        return this.getOneByFieldsNotExit(typeName, "", "");
    }

    public PartsTypePo getOneByFieldsNotExit(
        String typeName, String typeCode, String typeFullModel) {
        PartsTypePo type = partsTypeRoDs.getOneByFields(
            typeName, typeCode, typeFullModel);
        if (null == type) {
            type = new PartsTypePo()
                .setName(typeName)
                .setCode(typeCode)
                .setFullModel(typeFullModel);
            final boolean b = partsTypeRwDs.insertPartsType(type);
            if (!b) {
                log.error("创建物料规格类型失败: {}", type);
                throw new DcServiceException("创建物料规格类型失败");
            }
        }
        return type;
    }
}
