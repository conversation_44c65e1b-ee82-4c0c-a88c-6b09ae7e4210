package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class HUAWEIStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("HUAWEI", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "输出过压锁死故障";
                break;
            case 1:
                res = "环温过温关机";
                break;
            case 2:
                res = "模块故障关机";
                break;
            case 3:
                res = "模块保护关机";
                break;
            case 4:
                res = "模块风扇故障";
                break;
            case 5:
                res = "EEPROM读写错误";
                break;
            case 6:
                res = "输出过流保护";
                break;
            case 7:
                res = "输出欠压故障";
                break;
            case 8:
                res = "环温低温关机";
                break;
            case 9:
                res = "模块关机";
                break;
            case 10:
                res = "风扇固定全速";
                break;
            case 11:
                res = "NA";
                break;
            case 12:
                res = "模块内部过温（故障）";
                break;
            case 13:
                res = "软地址重排";
                break;
            case 14:
                res = "输出模式自动切换使能";
                break;
            case 15:
                res = "CAN 通讯质量差";
                break;
            case 16:
                res = "模块顺序起机功能使能";
                break;
            case 17:
                res = "模块输入欠压";
                break;
            case 18:
                res = "模块交流不平衡";
                break;
            case 19:
                res = "模块交流缺相";
                break;
            case 20:
                res = "模块严重不均流";
                break;
            case 21:
                res = "模块序列号重复关机";
                break;
            case 22:
                res = "模块输入过压";
                break;
            case 23:
                res = "模块PFC故障";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
