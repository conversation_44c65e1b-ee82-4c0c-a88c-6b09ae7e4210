package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 优优绿能电源模块状态码解析器
 */
@Service
public class UugpStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("UUGP", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
//        return String.valueOf(bitIdx);  // 暂时缺少优优绿能模块的状态码信息，先返回原始数据
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "交流过压";
                break;
            case 1:
                res = "交流欠压";
                break;
            case 2:
                res = "交流过压脱离(交流过压关机)";
                break;
            case 3:
                res = "PFC 母线过压";
                break;
            case 4:
                res = "PFC 母线欠压";
                break;
            case 5:
                res = "PFC 母线不平衡";
                    break;
            case 6:
                res = "直流输出过压";
                break;
            case 7:
                res = "直流过压关机";
                break;
            case 8:
                res = "直流输出欠压";
                break;
            case 9:
                res = "风扇不运行";
                break;
            case 10:
                res = "预留";
                break;
            case 11:
                res = "风扇驱动电路损坏";
                break;
            case 12:
                res = "环境过温";
                break;
            case 13:
                res = "环境温度过低";
                break;
            case 14:
                res = "PFC 过温保护 1";
                break;
            case 15:
                res = "输出继电器故障";
                break;
            case 16:
                res = "DC 过温保护 1";
                break;
            case 17:
                res = "预留";
                break;
            case 18:
                res = "PFC 与 DCDC 通信故障";
                break;
            case 19:
                res = "预留";
                break;
            case 20:
                res = "PFC 故障";
                break;
            case 21:
                res = "DCDC 故障";
                break;
            case 22:
                res = "预留";
                break;
            case 23:
                res = "预留";
                break;
            case 24:
                res = "预留";
                break;
            case 25:
                res = "DCDC 不运行";
                break;
            case 26:
            case 27:
                res = "输出环路状态";
                break;
            case 28:
                res = "DC 输出电压不平衡";
                break;
            case 29:
                res = "发现相同序列号的模块";
                break;
            case 30:
                res = "预留";
                break;
            case 31:
                res = "泄放电路异常";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
