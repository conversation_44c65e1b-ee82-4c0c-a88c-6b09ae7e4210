package com.cdz360.iot.device.mgm.dzds.client;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.param.TransformerUpdateParam;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class IotWorkFeignHystrix implements FallbackFactory<IotWorkFeign> {
    @Override
    public IotWorkFeign create(Throwable cause) {
        return new IotWorkFeign() {
            @Override
            public BaseResponse cfgGet(String ctrlNum) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse sendUpdateMsg(TransformerUpdateParam req) {
                return RestUtils.serverBusy();
            }
        };
    }
}
