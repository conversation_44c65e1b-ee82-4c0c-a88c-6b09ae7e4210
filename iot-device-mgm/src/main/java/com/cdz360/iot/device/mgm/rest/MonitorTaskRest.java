package com.cdz360.iot.device.mgm.rest;

import com.cdz360.iot.device.mgm.ds.service.GwTmpLogService;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.ListRpcResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Tag(name = "设备监控相关接口", description = "监控")
@RequestMapping(value = "/device/mgm/monitor", produces = MediaType.APPLICATION_JSON_VALUE)
public class MonitorTaskRest {
    private final Logger logger = LoggerFactory.getLogger(MonitorTaskRest.class);

    @Autowired
    private GwTmpLogService gwTmpLogService;

    @Operation(summary = "执行网关监控任务")
    @PostMapping(value = "/gw")
    @Deprecated
    public BaseRpcResponse monitorGw() {
        logger.trace(">>执行网关监控任务");
        BaseRpcResponse res = BaseRpcResponse.newInstance();
        // TODO: 逻辑待实现
        logger.trace("<< 执行网关监控任务");
        return res;
    }

    @Operation(summary = "执行下行指令监控任务")
    @PostMapping(value = "/downstreamRequest")
    @Deprecated
    public BaseRpcResponse monitorDownstreamRequest() {
        logger.trace(">>执行下行指令监控任务");
        BaseRpcResponse res = BaseRpcResponse.newInstance();
        // TODO: 逻辑待实现
        logger.trace("<< ");
        return res;
    }

    @Operation(summary = "执行下行指令监控任务")
    @PostMapping(value = "/listGwno")
    public BaseRpcResponse listGwno() {
        logger.trace(">>查询网关列表");
        List<String> list = gwTmpLogService.getGroupGwno();
        ListRpcResponse<String> ret = new ListRpcResponse<>(list);
        logger.trace("<< ");
        return ret;
    }
}
