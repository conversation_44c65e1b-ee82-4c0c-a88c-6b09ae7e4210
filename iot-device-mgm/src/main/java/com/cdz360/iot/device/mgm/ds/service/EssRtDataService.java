package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.feign.BizDataCoreFeignClient;
import com.cdz360.iot.device.mgm.feign.OpenHlhtFeignClient;
import com.cdz360.iot.device.mgm.model.basic.dto.SiteProfitInfo;
import com.cdz360.iot.device.mgm.model.basic.param.ListSiteProfitParam;
import com.cdz360.iot.device.mgm.model.basic.po.PriceItemPo;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDailyRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.vo.DayEssDataBi;
import com.cdz360.iot.model.ess.vo.DaySiteEssRtDataBi;
import com.cdz360.iot.model.ess.vo.EquipSampleData;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssEquipVo;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.ess.vo.SiteEssRtDataBi;
import com.cdz360.iot.model.ess.vo.TotalEssRtDataBi;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssRtDataService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private EssDailyRoDs essDailyRoDs;

    @Autowired
    private RedisEssEquipRtDataService redisEssEquipRtDataService;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    public Mono<List<DayEssDataBi>> siteRtData7Day(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID不能为空");
        }

        Date toDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DAY_OF_MONTH, -8);
        Date fromDate = calendar.getTime();
        return Mono.just(essDailyRoDs.siteRtDataOfDay(siteId, fromDate, toDate));
    }

    public Mono<TotalEssRtDataBi> rtDataTotal(DayKwhParam param) {
        return Mono.zip(this.todayRtData(param).collectList().map(list -> {
                    BigDecimal inKwh = list.stream().map(SiteEssRtDataBi::getInKwh)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal inExpend = list.stream().map(SiteEssRtDataBi::getInExpend)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal outKwh = list.stream().map(SiteEssRtDataBi::getOutKwh)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal outIncome = list.stream().map(SiteEssRtDataBi::getOutIncome)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal profit = list.stream().map(SiteEssRtDataBi::getProfit)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return new EssDataBi().setProfit(profit)
                        .setInKwh(inKwh)
                        .setInExpend(inExpend)
                        .setOutKwh(outKwh)
                        .setOutIncome(outIncome);
                }), // 当天
                Mono.just(essDailyRoDs.rtDataOfYesterday(param)), // 昨天
                Mono.just(param).doOnNext(p -> {
                    if (p.getYear() == null || null == p.getMonth()) {
                        LocalDate date = LocalDate.now();
                        if (null == p.getYear()) {
                            p.setYear(date.getYear());
                        }
                        if (null == p.getMonth()) {
                            p.setMonth(date.getMonthValue());
                        }
                    }
                }).map(essDailyRoDs::rtDataOfMonth), // 当月
                Mono.just(essDailyRoDs.rtDataOfTotal(param))) // 累计
            .map(tuple -> {
                EssDataBi today = tuple.getT1();
                EssDataBi yesterday = tuple.getT2();
                EssDataBi month = tuple.getT3();
                EssDataBi total = tuple.getT4();

                month.setProfit(month.getProfit().add(today.getProfit()));
                month.setInExpend(month.getInExpend().add(today.getInExpend()));
                month.setInKwh(month.getInKwh().add(today.getInKwh()));
                month.setOutIncome(month.getOutIncome().add(today.getOutIncome()));
                month.setOutKwh(month.getOutKwh().add(today.getOutKwh()));

                total.setProfit(total.getProfit().add(today.getProfit()));
                total.setInExpend(total.getInExpend().add(today.getInExpend()));
                total.setInKwh(total.getInKwh().add(today.getInKwh()));
                total.setOutIncome(total.getOutIncome().add(today.getOutIncome()));
                total.setOutKwh(total.getOutKwh().add(today.getOutKwh()));
                return new TotalEssRtDataBi()
                    .setToday(today)
                    .setYesterday(yesterday)
                    .setMonth(month)
                    .setTotal(total);
            });
    }

    private Flux<SiteEssRtDataBi> todayRtData(DayKwhParam param) {
        // 获取微网控制器列表
        ListCtrlParam ctrlParam = new ListCtrlParam()
            .setCommIdChain(param.getCommIdChain())
            .setSiteIdList(param.getSiteIdList());
        ctrlParam.setSize(100);
        List<GwInfoVo> ctrlList = this.gwSiteRefRoDs.findCtrlList(ctrlParam);
        List<String> siteIdList = ctrlList.stream().map(GwInfoVo::getSiteId)
            .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(siteIdList)) {
            return Flux.fromIterable(List.of());
        }

        final LocalDate date = LocalDate.now();
        AtomicReference<Map<String, List<PriceItemPo>>> siteInMap = new AtomicReference<>();
        AtomicReference<Map<String, List<PriceItemPo>>> siteOutMap = new AtomicReference<>();
        ListSiteProfitParam profitParam = new ListSiteProfitParam();
        return bizDataCoreFeignClient.getSiteProfitList(profitParam.setSiteIdList(siteIdList))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .doOnNext(profitList -> {
                siteInMap.set(profitList.stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getInPriceItemPoList()))
                    .collect(Collectors
                        .toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getInPriceItemPoList)));
                siteOutMap.set(profitList.stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getOutPriceItemPoList()))
                    .collect(Collectors
                        .toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getOutPriceItemPoList)));
            })
            .map(i -> ctrlList)
            .flatMapMany(Flux::fromIterable)
            .filter(ctrl -> CollectionUtils.isNotEmpty(siteOutMap.get().get(ctrl.getSiteId())))
            .flatMap(ctrl -> {
                // 统计所有设备列表的数据(PCS, DCAC, DCDC)
                ListEssEquipParam essEquipParam = new ListEssEquipParam()
                    .setGwno(ctrl.getGwno())
                    .setEquipTypes(List.of(EssEquipType.PCS));
                List<EssEquipVo> equipList = essEquipRoDs.findEssEquipList(essEquipParam);

                List<PriceItemPo> inPriceItems = siteInMap.get().get(ctrl.getSiteId());
                List<PriceItemPo> outPriceItems = siteOutMap.get().get(ctrl.getSiteId());
                return openHlhtFeignClient.getEssDayBi(ctrl.getSiteId())
                        .doOnNext(FeignResponseValidate::check)
                        .flatMap( e -> {
                            Pair<Boolean, EssDataBi> pair = e.getData();
                            if (pair.getFirst()) {
                                SiteEssRtDataBi result = new SiteEssRtDataBi();
                                BeanUtils.copyProperties(pair.getSecond(), result);
                                return Mono.just(result.setSiteId(ctrl.getSiteId()));
                            }
                            return this.gwAllPCSDayBi(equipList, date, inPriceItems, outPriceItems)
                                .map(data -> {
                                    SiteEssRtDataBi result = new SiteEssRtDataBi();
                                    BeanUtils.copyProperties(data, result);
                                    return result.setSiteId(ctrl.getSiteId());
                                });
                        });
            })
            .collect(Collectors.toMap(SiteEssRtDataBi::getSiteId, o -> o, (o1, o2) -> {
                o1.setProfit(o1.getProfit().add(o2.getProfit()));
                o1.setInExpend(o1.getInExpend().add(o2.getInExpend()));
                o1.setInKwh(o1.getInKwh().add(o2.getInKwh()));
                o1.setOutIncome(o1.getOutIncome().add(o2.getOutIncome()));
                o1.setOutKwh(o1.getOutKwh().add(o2.getOutKwh()));
                return o1;
            }))
            .map(Map::values)
            .flatMapMany(Flux::fromIterable);
    }

    private Mono<EssDataBi> gwAllPCSDayBi(
        List<EssEquipVo> equipVoList, LocalDate date,
        List<PriceItemPo> inPriceItems, List<PriceItemPo> outPriceItems) {
        return redisEssEquipRtDataService.gwAllPCSDayBi(equipVoList, date, inPriceItems,
            outPriceItems);
    }

    public Mono<List<EquipSampleData>> equipRtDataSample(DataBiParam param) {
        IotAssert.isNotNull(param.getSampleType(), "数据汇总单位不能为空(sampleType)");

        switch (param.getSampleType()) {
//            case HOUR:
//                DataBiParam.rangeDate(param);
            case DAY:
            case MONTH:
            case YEAR:
                DataBiParam.rangeDate(param);
                return Mono.just(param)
                    .map(essDailyRoDs::equipRtDataSample)
                    .map(data -> {
                        LocalDate now = LocalDate.now();
                        LocalDate endDate = param.getToDate().toLocalDate();

                        boolean needToday = false;
                        if (null != endDate && endDate.getYear() == now.getYear()) {
                            switch (param.getSampleType()) {
                                case YEAR:
                                    needToday = true;
                                    break;
                                case MONTH:
                                    needToday = endDate.getMonth() == now.getMonth();
                                    break;
                                case DAY:
                                    needToday = now.isEqual(endDate);
                                    break;
                            }
                        }

                        if (needToday) {
                            // FIXME: 获取当天数据
//                                todayRtData()
                        }
                        return data;
                    });
            default:
                log.error("数据汇总单位不支持: {}", param.getSampleType());
                throw new DcArgumentException("数据汇总单位不支持");
        }
    }

    public Flux<DaySiteEssRtDataBi> siteDayOfRangeKwh(DayKwhParam param) {
//        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
//            return Flux.fromIterable(List.of());
//        }

        return Mono.zip(Mono.just(essDailyRoDs.siteDayOfRangeKwh(param)),
                this.todayRtData(new DayKwhParam()
                        .setCommIdChain(param.getCommIdChain())
                        .setSiteIdList(param.getSiteIdList()))
                    .map(p -> {
                        DaySiteEssRtDataBi result = new DaySiteEssRtDataBi();
                        result.setDate(LocalDate.now())
                            .setSiteId(p.getSiteId());
                        BeanUtils.copyProperties(p, result);
                        return result;
                    })
                    .collectList())
            .map(tuple -> {
                List<DaySiteEssRtDataBi> result = tuple.getT1();
                result.addAll(tuple.getT2());
                return result;
            })
            .flatMapMany(Flux::fromIterable);
    }

    public Mono<List<EssStatusBi>> getEssStatusBi(String commIdChain, String siteId) {
        if (StringUtils.isNotBlank(siteId)) {
            return openHlhtFeignClient.getEssStatusBi(siteId)
                .doOnNext(FeignResponseValidate::check)
                .map( e -> {
                    Pair<Boolean, List<EssStatusBi>> pair = e.getData();
                    if (pair.getFirst()) {
                        return pair.getSecond();
                    }
                    return essRoDs.getEssStatusBi(commIdChain, siteId);
                });
        }
        return Mono.just(essRoDs.getEssStatusBi(commIdChain, siteId));
    }

}
