package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.ro.EvseModuleDetailRoDs;
import com.cdz360.iot.ds.ro.EvseModuleRoDs;
import com.cdz360.iot.ds.ro.EvseOfflineRoDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.ro.SimRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipAlarmLangRoDs;
import com.cdz360.iot.ds.rw.EvseCfgResultRwDs;
import com.cdz360.iot.ds.rw.EvseCfgRwDs;
import com.cdz360.iot.ds.rw.EvsePasscodeRwDs;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseCfgResultParam;
import com.cdz360.iot.model.evse.param.ListEvseModelParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import com.cdz360.iot.model.evse.vo.EvseCfgResultVo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.param.OfflineEvseParam;
import com.cdz360.iot.model.sim.vo.SimUnionVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import zipkin2.Call.Base;


/**
 * @ClassName： EvseService
 * @Description: 充电桩相关的ds服务
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/10/31 10:32
 */
@Service
public class EvseService {
    private final Logger logger = LoggerFactory.getLogger(EvseService.class);

    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private EvseOfflineRoDs evseOfflineRoDs;
    @Autowired
    private PlugRoDs plugRoDs;
    @Autowired
    private EvsePasscodeRwDs evsePasscodeRwDs;

    @Autowired
    private EvseCfgRwDs evseCfgRwDs;

    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;
    @Autowired
    private EvseModelRoDs evseModelRoDs;
    @Autowired
    private EvseModuleRoDs evseModuleRoDs;
    @Autowired
    private EvseModuleDetailRoDs evseModuleDetailRoDs;
    @Autowired
    private SimRoDs simRoDs;

    @Autowired
    private EssEquipAlarmLangRoDs essEquipAlarmLangRoDs;

    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;

    @Autowired
    private RedisIotReadService  redisIotReadService;
    @Autowired
    private RedisIotRwService  redisIotRwService;


    public EvseInfoDto getEvseInfo(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("参数错误");
        }
        EvseInfoDto res = evseRoDs.getEvseInfo(evseNo);
        if (res == null) {
            return res;
        }
        res.setPlugNoList(plugRoDs.getPlugNoList(evseNo))
                .setPlugPoList(plugRoDs.getPlugPoList(evseNo));
        EvseModulePo modulePo = evseModuleRoDs.findChargingModule(evseNo);
        if (modulePo != null) {
            res.setModuleType(modulePo.getModuleType())
                    .setSlotNum(modulePo.getNumber())
                    .setModuleNum(evseModuleDetailRoDs.getValidModuleNum(modulePo.getId()).intValue());
        }
        return res;
    }

    public ListResponse<EvseInfoDto> getEvseInfoList(ListEvseParam param) {
        return evseRoDs.getEvseInfoList(param);
    }

    public List<EvsePo> getEvseList(ListEvseParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        return evseRoDs.getEvseList(param);
    }

    public List<EvseTinyDto> getEvseTinyList(EvseTinyParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        return evseRoDs.getEvseTinyList(param);
    }

    public List<EvseModelVo> getEvseModelVoList(ListEvseParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        return evseRoDs.getEvseModelVoList(param);
    }

    public ListResponse<EvseModelPo> getModelList(Long start, Integer size,String keyword) {
        if (start == null) {
            start = 0L;
        }
        if (size == null) {
            size = 999;
        }
        ListEvseModelParam param = new ListEvseModelParam();
        param.setStatus(Boolean.TRUE)
            .setModel(keyword)
            .setStart(start)
            .setSize(size);
        return evseModelRoDs.getList(param);
    }

    public List<EvsePo> getEvseListForTopology(ListEvseParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        return evseRoDs.getEvseListForTopology(param);
    }

    /**
     * @Description: 根据evseIds获取桩信息列表
     * @Author: JLei
     * @CreateDate: 10:33 2019/10/31
     */
    public ListResponse<EvsePo> selectByEvseIds(List<String> evseIds) {
        Assert.isTrue(CollectionUtils.isNotEmpty(evseIds), "桩号列表不能为空");
        List<EvsePo> list = evseRoDs.selectByEvseIds(evseIds);
        return new ListResponse<>(list, (long) list.size());
    }


    /**
     * @Description: 更新桩的长效密钥
     * @Author: JLei
     * @CreateDate: 10:33 2019/10/31
     */
    public ObjectResponse<Long> updateEvsePasscode(String evseNo, String passcode) {
        Assert.isTrue(StringUtils.isNotBlank(evseNo), "桩号不能为空");
//        Assert.isTrue(StringUtils.isNotBlank(passcode), "长效密钥不能为空"); // 海外平台允许为空
        Long lastVer = evsePasscodeRwDs.updateEvsePasscode(evseNo, passcode);
        return new ObjectResponse(lastVer);
    }


    public BaseResponse updateBizStatus(String evseNo, Long bizStatus) {
        Assert.isTrue(StringUtils.isNotBlank(evseNo), "桩号不能为空");
        Assert.isTrue(bizStatus != null, "运营状态不能为空");
        EvsePo evsePo = new EvsePo()
            .setEvseId(evseNo)
            .setBizStatus(EvseBizStatus.valueOf(bizStatus));
        int i = evseRwQueryMapper.updateByEvseId(evsePo);
        if (i > 0) {
            EvseVo evseRedisCache = redisIotReadService.getEvseRedisCache(evseNo);
            evseRedisCache.setBizStatus(EvseBizStatus.valueOf(bizStatus));
            redisIotRwService.updateEvseRedisCache(evseRedisCache);
        }
        return RestUtils.success();
    }

    /**
     * @Description: 更新桩的长效密钥
     * @Author: JLei
     * @CreateDate: 10:33 2019/10/31
     */
    public ObjectResponse<EvsePasscodePo> getEvsePasscode(String evseNo, Long ver) {
        Assert.isTrue(StringUtils.isNotBlank(evseNo), "桩号不能为空");
        EvsePasscodePo evsePasscode = evsePasscodeRwDs.getEvsePasscode(evseNo, ver, false);
        return new ObjectResponse(evsePasscode);
    }

//    /**
//     * 获取桩计费模板下发的最新时间
//     *
//     * @param param
//     * @return
//     */
//    public List<EvseCfgVo> getPriceSchemeDownTime(ListEvseCfgParam param) {
//        com.cdz360.iot.model.base.ListResponse<EvseCfgVo> evseCfgVoList = this.evseCfgRwDs.getEvseCfgVoList(param);
//        List<EvseCfgVo> voList = evseCfgVoList.getData();
//        return voList;
//    }

    public List<EvseCfgResultVo> getEvseCfgResultList(ListEvseCfgResultParam param) {
        return this.evseCfgResultRwDs.getEvseCfgResultList(param);
    }

    public List<String> getEvseModelOrFirm(String type ) {
        return evseRoDs.getEvseModelOrFirm(type);
    }

    public Long getTotalPower(SiteAndPlugBiParam param) {
        return this.evseRoDs.getTotalPower(param);
    }

    public Integer getEvseCount(SiteAndPlugBiParam param) {
        return this.evseRoDs.getEvseCount(param);
    }

    public ListResponse<OfflineEvseParam> checkOfflineEvseInDB(List<OfflineEvseParam> params) {

        return RestUtils.buildListResponse(params.stream().map(e -> {
            Long count = evseOfflineRoDs.count(e.getEvseNo(), e.getSiteId());
            e.setIsExist(count > 0);
            return e;
        }).collect(Collectors.toList()));
    }

    public ListResponse<OfflineEvseParam> checkEvseInDB(List<OfflineEvseParam> params) {

        EvseTinyParam req = new EvseTinyParam();
        req.setEvseNoList(params.stream().map(OfflineEvseParam::getEvseNo).collect(Collectors.toList()));
        List<EvseTinyDto> list = evseRoDs.getEvseTinyList(req);
        Map<String, String> evseMap = list.stream().collect(Collectors.toMap(EvseTinyDto::getEvseNo, EvseTinyDto::getName));

        List<String> codeList = params.stream().map(e -> {
            List<String> temp = new ArrayList<>();
            if (StringUtils.isNotBlank(e.getIccid())) {
                temp.add(e.getIccid());
            } else if (StringUtils.isNotBlank(e.getMsisdn())) {
                temp.add(e.getMsisdn());
            }
            return temp;
        }).filter(e -> e.size() > 0).flatMap(Collection::stream).collect(Collectors.toList());
        List<SimUnionVo> codeUnion = simRoDs.getUnionVo(codeList);
        Map<String, SimUnionVo> codeMap = codeUnion.stream().collect(Collectors.toMap(e -> e.getCodeType() + "-" + e.getCode(), o -> o));

        return RestUtils.buildListResponse(params.stream().map(e -> {
            if (StringUtils.isNotBlank(evseMap.get(e.getEvseNo()))) {
                e.setIsExist(true);
            }

            if (StringUtils.isNotBlank(e.getIccid()) || StringUtils.isNotBlank(e.getMsisdn())) {
                SimUnionVo temp = null;
                if (codeMap.get("1-" + e.getIccid()) != null) {
                    temp = codeMap.get("1-" + e.getIccid());
                } else if (codeMap.get("2-" + e.getMsisdn()) != null) {
                    temp = codeMap.get("2-" + e.getMsisdn());
                }
                boolean isSimExist = temp != null;
                e.setIsSimExist(isSimExist);

                if (isSimExist) {
                    e.setSimId(temp.getSimId());
                    e.setIsSimBind(StringUtils.isNotBlank(temp.getSiteId()) || StringUtils.isNotBlank(temp.getEvseNo()));
                } else {
                    e.setIsSimBind(Boolean.FALSE);
                }
            }

            EvseModelPo modelPo = evseModelRoDs.findByExactFields(e.getModel(), e.getBrand(),
                    e.getPower(), e.getPlugNum());
            if (modelPo != null) {
                e.setIsModelBrandUniqueKey(true);
                e.setModelId(modelPo.getId());
            } else {
                e.setIsModelBrandUniqueKey(false);
            }
            return e;
        }).collect(Collectors.toList()));
    }

    public ListResponse<String> getFirmwareVerList(BaseListParam param) {
        return RestUtils.buildListResponse(evseRoDs.getFirmwareVerList(param));
    }

    public List<EssEquipAlarmLangDto>  getLangList(ListAlarmLangParam params) {
        return essEquipAlarmLangRoDs.getAlarmLangDtoList(params);
    }

}
