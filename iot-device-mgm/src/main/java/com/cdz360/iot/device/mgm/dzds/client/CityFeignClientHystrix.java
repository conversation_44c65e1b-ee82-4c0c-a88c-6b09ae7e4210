package com.cdz360.iot.device.mgm.dzds.client;

import com.cdz360.base.model.base.dto.ObjectResponse;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Classname IotDzCityFallback
 * @Description TODO
 * @Date 2019/5/27
 * @Created by wangzheng
 */
@Component
public class CityFeignClientHystrix implements FallbackFactory<CityFeignClient> {
    private final Logger logger = LoggerFactory.getLogger(CityFeignClientHystrix.class);

    @Override
    public CityFeignClient create(Throwable cause) {
        return new CityFeignClient() {
            @Override
            public ObjectResponse<String> getCityCode(String cityName) {
                logger.error(cause.getMessage(), cause);
                return null;
            }
        };
    }
}