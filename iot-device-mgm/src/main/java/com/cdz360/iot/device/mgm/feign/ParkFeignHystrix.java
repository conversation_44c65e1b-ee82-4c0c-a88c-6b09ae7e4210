package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.park.param.PlugStatusEventLogParam;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ParkFeignHystrix implements FallbackFactory<ParkFeignClient> {

    @Override
    public ParkFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new ParkFeignClient() {
            @Override
            public Mono<ObjectResponse<ParkingLockVo>> lookForLock(String siteId,
                String remoteLockId) {
                log.error(
                    "【服务熔断】 向地锁云查询地锁. Service = {}, api = lookForLock. siteId = {}, remoteLockId = {}",
                    DcConstants.KEY_FEIGN_IOT_PARK, siteId, remoteLockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> plugStatusEventLog(PlugStatusEventLogParam param) {
                log.error("服务[{}]接口熔断 - 枪头状态变更事件日志, param = {}",
                    DcConstants.KEY_FEIGN_IOT_PARK, param);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, ParkFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ParkFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }
}
