package com.cdz360.iot.device.mgm.rest;

import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.ds.service.DeviceGwInfoService;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.CommonRpcResponse;
import com.cdz360.iot.model.base.ListRpcResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "网关相关接口", description = "网关")
@RequestMapping(value = "/device/mgm/gw", produces = MediaType.APPLICATION_JSON_VALUE)
public class GwInfoRest {
    private final Logger logger = LoggerFactory.getLogger(GwInfoRest.class);

    @Autowired
    private DeviceGwInfoService deviceGwInfoService;


    @Operation(summary = "配置网关信息")
    @PostMapping(value = "/configGwRef")
    public BaseRpcResponse configGwRef(@Parameter(name = "old网关编号") @RequestParam String oldGwno,
                                    @Parameter(name = "curr网关编号") @RequestParam String currGwno,
                                    @Parameter(name = "场站ID") @RequestParam String siteId){
        logger.info("配置网关信息。oldGwno = {}, currGwno = {}, siteId= {}", oldGwno, currGwno, siteId);
        BaseRpcResponse res = BaseRpcResponse.newInstance();
        IotAssert.isNotBlank(currGwno,"currGwno不能为空");
        IotAssert.isNotBlank(String.valueOf(siteId),"siteId不能为空");
        this.deviceGwInfoService.configGwRef(oldGwno,currGwno,siteId);
        res.setStatus(0);
        logger.info("<<");
        return res;
    }

    @Operation(summary = "获取城市编号")
    @PostMapping(value = "/dzCityCode")
    public CommonRpcResponse<String> getCityCode(@Parameter(name = "城市名") @RequestParam String cityName) {
        logger.info("获取城市编号。cityName = {}", cityName);
        String cityCode = deviceGwInfoService.getCityCode(cityName);
        CommonRpcResponse<String> res = new CommonRpcResponse<>(cityCode);
        logger.info("<<");
        return res;
    }

    @Operation(summary = "获取当前城市未绑定场站的网关编号")
    @GetMapping(value = "/getNotBoundGwno")
    public ListRpcResponse<String> getNotBoundGwno(@Parameter(name = "城市编号") @RequestParam String cityCode) {
        logger.info("获取当前城市未绑定场站的网关编号。cityCode = {}", cityCode);
        IotAssert.isNotBlank(cityCode,"cityCode不能为空");
        ListRpcResponse<String> res =new ListRpcResponse<String>(this.deviceGwInfoService.getNotBoundGwno(cityCode));
        logger.info("<<");
        return res;
    }
}
