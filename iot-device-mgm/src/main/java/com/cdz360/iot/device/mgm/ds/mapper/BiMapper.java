package com.cdz360.iot.device.mgm.ds.mapper;

import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.iot.device.mgm.model.bi.vo.EvseStatusBi;
import com.cdz360.iot.device.mgm.model.bi.vo.EvseSupplyBi;
import com.cdz360.iot.device.mgm.model.bi.vo.PlugStatusBi;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

@Mapper
public interface BiMapper {

    List<EvseStatusBi> getEvseStatusBi(@Nullable @Param("siteId") String siteId,
        @Param("bizStatus") EvseBizStatus bizStatus);

    List<PlugStatusBi> getPlugStatusBi(@Nullable @Param("siteId") String siteId,
        @Param("bizStatus") EvseBizStatus bizStatus);


    List<EvseSupplyBi> getEvseSupplyBi(@Nullable @Param("siteId") String siteId,
        @Param("bizStatus") EvseBizStatus bizStatus);

    List<EvseSupplyBi> getPlugSupplyBi(@Nullable @Param("siteId") String siteId,
        @Param("bizStatus") EvseBizStatus bizStatus);
}
