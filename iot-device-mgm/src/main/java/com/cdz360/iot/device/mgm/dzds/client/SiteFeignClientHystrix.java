package com.cdz360.iot.device.mgm.dzds.client;

//@Component
//public class SiteFeignClientHystrix implements FallbackFactory<SiteFeignClient> {
//
//    private final Logger logger = LoggerFactory.getLogger(SiteFeignClientHystrix.class);
//
//    @Override
//    public SiteFeignClient create(Throwable cause) {
//        return new SiteFeignClient() {
//            @Override
//            public ListResponse<DzSiteDto> getPagedSiteGeoList(SiteParam siteParam) {
//                logger.error(cause.getMessage(),cause);
//                return null;
//            }
//
////            @Override
////            public BaseResponse updateSiteInfo(SiteParam siteParam) {
////                logger.error(cause.getMessage(),cause);
////                return null;
////            }
//        };
//    }
//}
