package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.DevCfgRoDs;
import com.cdz360.iot.ds.ro.EssSmoothOutPutCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDrySpotCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssFmCtrlCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssGeneratorCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssInOutCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssPeakShareCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.DevCfgRwDs;
import com.cdz360.iot.ds.rw.EssCfgRwDs;
import com.cdz360.iot.ds.rw.EssDrySpotCfgRwDs;
import com.cdz360.iot.ds.rw.EssFmCtrlCfgRwDs;
import com.cdz360.iot.ds.rw.EssGeneratorCfgRwDs;
import com.cdz360.iot.ds.rw.EssInOutCfgRwDs;
import com.cdz360.iot.ds.rw.EssPeakShareCfgRwDs;
import com.cdz360.iot.ds.rw.EssSmoothOutPutRwDs;
import com.cdz360.iot.ds.rw.GtiCfgRwDs;
import com.cdz360.iot.ds.rw.GtiGridDispatchCfgRwDs;
import com.cdz360.iot.model.ess.param.AddEssCfgParam;
import com.cdz360.iot.model.ess.param.AddGtiCfgParam;
import com.cdz360.iot.model.ess.param.ListDevCfgParam;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssDrySpotCfgPo;
import com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo;
import com.cdz360.iot.model.ess.po.EssGeneratorCfgPo;
import com.cdz360.iot.model.ess.po.EssInOutCfgPo;
import com.cdz360.iot.model.ess.po.EssPeakShareCfgPo;
import com.cdz360.iot.model.ess.po.SmoothOutPutCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.ess.type.DrySpotCtrlMode;
import com.cdz360.iot.model.ess.type.EssBootMode;
import com.cdz360.iot.model.ess.type.EssSwitchCommand;
import com.cdz360.iot.model.ess.type.InOutCoupleMode;
import com.cdz360.iot.model.ess.vo.DevCfgVo;
import com.cdz360.iot.model.ess.vo.EssDetailVo;
import com.cdz360.iot.model.ess.vo.GtiCfgVo;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;


/**
 * 光储配置模板
 */
@Service
public class EssService {
    static enum ESS_EQUIP_CDF_TYPE {
        IN_OUT_CFG,
        PEAK_SHARE_CFG,
        ESS_CFG,
        SMOOTH_CFG,
        FM_CTRL_CFG,
        GENERAL_CFG,
        DRY_SPOT_CFG
    }

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";
    @Autowired
    private DevCfgRwDs devCfgRwDs;

    @Autowired
    private DevCfgRoDs devCfgRoDs;

    @Autowired
    private EssInOutCfgRwDs essInOutCfgRwDs;

    @Autowired
    private EssPeakShareCfgRwDs essPeakShareCfgRwDs;

    @Autowired
    private EssCfgRwDs essCfgRwDs;

    @Autowired
    private EssFmCtrlCfgRwDs essFmCtrlCfgRwDs;

    @Autowired
    private EssGeneratorCfgRwDs essGeneratorCfgRwDs;

    @Autowired
    private EssDrySpotCfgRwDs essDrySpotCfgRwDs;

    @Autowired
    private EssSmoothOutPutRwDs essSmoothOutPutRwDs;

    @Autowired
    private GtiCfgRwDs gtiCfgRwDs;

    @Autowired
    private GtiGridDispatchCfgRwDs gtiGridDispatchCfgRwDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private GtiCfgRoDs gtiCfgRoDs;

    @Autowired
    private EssInOutCfgRoDs essInOutCfgRoDs;

    @Autowired
    private EssPeakShareCfgRoDs essPeakShareCfgRoDs;

    @Autowired
    private EssFmCtrlCfgRoDs essFmCtrlCfgRoDs;

    @Autowired
    private EssDrySpotCfgRoDs essDrySpotCfgRoDs;

    @Autowired
    private EssSmoothOutPutCfgRoDs essSmoothOutPutCfgRoDs;

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssGeneratorCfgRoDs essGeneratorCfgRoDs;

    /**
     * 新增光储模板
     *
     * @param param
     * @return
     */
    @Transactional
    public Mono<ObjectResponse<Long>> addEss(AddEssCfgParam param) {
        return Mono.just(param)
                .map(p -> {
                    Long devId = null;
                    //初次新增
                    List<Long> idList = devCfgRoDs.getByName(param.getName(), param.getSiteId());
                    if (null == param.getId()) {
                        IotAssert.isTrue(CollectionUtils.isEmpty(idList), "模板名称重复");
                        DevCfgPo po = new DevCfgPo();
                        BeanUtils.copyProperties(param, po);
                        po.setCode(RandomStringUtils.random(10, RANDOM_CHAR))
                                .setVer(1L)
                                .setEnable(Boolean.TRUE);
                        devCfgRwDs.insertDevCfg(po);
                        devId = po.getId();
                    } else {
                        List<Long> idList1 = idList.stream().filter(i -> !i.equals(param.getId())).collect(Collectors.toList());
                        IotAssert.isTrue(CollectionUtils.isEmpty(idList1), "模板名称重复");

                        DevCfgPo devCfgPo = devCfgRoDs.getById(param.getId(),param.getCommIdChain());
                        IotAssert.isNotNull(devCfgPo, "数据为空");
                        devId = param.getId();
                    }

                    //充放电时段参数
                    if (null != param.getInOutCfg()) {
                        EssInOutCfgPo inOutCfgPo = new EssInOutCfgPo();
                        BeanUtils.copyProperties(param.getInOutCfg(), inOutCfgPo);
                        inOutCfgPo.setCfgId(devId);
                        if (null == inOutCfgPo.getCoupleMode()) {
                            inOutCfgPo.setCoupleMode(InOutCoupleMode.UNKNOWN);
                        }
                        this.addInOutCfg(inOutCfgPo);
                    }

                    //削峰填谷
                    if (null != param.getPeakShareCfg()) {
                        EssPeakShareCfgPo essPeakShareCfgPo = new EssPeakShareCfgPo();
                        BeanUtils.copyProperties(param.getPeakShareCfg(), essPeakShareCfgPo);
                        essPeakShareCfgPo.setCfgId(devId);
                        this.addPeakShareCfg(essPeakShareCfgPo);
                    }

                    //储能开关机设置
                    if (null != param.getEssCfg()) {
                        EssCfgPo essCfgPo = new EssCfgPo();
                        BeanUtils.copyProperties(param.getEssCfg(), essCfgPo);
                        essCfgPo.setStrategy(param.getStrategy());
                        essCfgPo.setSamplingTime(param.getSamplingTime());
                        essCfgPo.setCfgId(devId);
                        this.addEssCfg(essCfgPo);
                    }

                    //平滑输出
                    if (null != param.getSmoothOutCfg()) {
                        SmoothOutPutCfgPo smoothOutPutCfgPo = new SmoothOutPutCfgPo();
                        BeanUtils.copyProperties(param.getSmoothOutCfg(), smoothOutPutCfgPo);
                        smoothOutPutCfgPo.setCfgId(devId);
                        this.addSmoothOutPutCfg(smoothOutPutCfgPo);
                    }

                    // 调频控制
                    if (null != param.getFmCtrlCfg()) {
                        EssFmCtrlCfgPo essFmCtrlCfgPo = new EssFmCtrlCfgPo();
                        BeanUtils.copyProperties(param.getFmCtrlCfg(), essFmCtrlCfgPo);
                        essFmCtrlCfgPo.setCfgId(devId);
                        this.addFmCtrlCfg(essFmCtrlCfgPo);
                    }

                    //柴油机控制
                    if (null != param.getGeneratorCfg()) {
                        EssGeneratorCfgPo essGeneratorCfgPo = new EssGeneratorCfgPo();
                        BeanUtils.copyProperties(param.getGeneratorCfg(), essGeneratorCfgPo);
                        essGeneratorCfgPo.setCfgId(devId);
                        this.addGeneratorCfg(essGeneratorCfgPo);
                    }
                    //干节点控制
                    if (null != param.getDrySpotCfg()) {
                        EssDrySpotCfgPo essDrySpotCfgPo = new EssDrySpotCfgPo();
                        BeanUtils.copyProperties(param.getDrySpotCfg(), essDrySpotCfgPo);
                        essDrySpotCfgPo.setCfgId(devId);
                        if (null == essDrySpotCfgPo.getCtrlMode()) {
                            essDrySpotCfgPo.setCtrlMode(DrySpotCtrlMode.UNKNOWN);
                        }
                        this.addDrySpotCfg(essDrySpotCfgPo);
                    }

                    //采集周期，策略类型每次保存都要做更改
                    EssCfgPo essPo = essCfgRoDs.getByCfgId(devId);
                    if (null == essPo) {
                        EssCfgPo po = new EssCfgPo();
                        po.setCfgId(devId)
                                .setSamplingTime(param.getSamplingTime())
                                .setBootMode(EssBootMode.UNKNOWN)
                                .setSwitchCommand(EssSwitchCommand.UNKNOWN)
                                .setStrategy(param.getStrategy());
                        essCfgRwDs.insertEssCfg(po);
                    } else {
                        essPo.setStrategy(param.getStrategy())
                                .setSamplingTime(param.getSamplingTime());
                        essCfgRwDs.insertEssCfg(essPo);
                    }
                    return RestUtils.buildObjectResponse(devId);
                });
    }

    @Transactional
    public Mono<ObjectResponse<Long>> editEss(AddEssCfgParam param) {
        return Mono.just(param)
                .doOnNext(e -> {
                    IotAssert.isNotNull(e.getId(), "ID不能为空");
                }).map(e -> {
                    List<Long> idList = devCfgRoDs.getByName(param.getName(), param.getSiteId()).stream().filter(i -> !i.equals(param.getId())).collect(Collectors.toList());
                    IotAssert.isTrue(CollectionUtils.isEmpty(idList), "模板名称重复");

                    DevCfgPo devCfgPo = devCfgRoDs.getById(e.getId(),param.getCommIdChain());
                    IotAssert.isTrue(null != devCfgPo && CfgType.HT_ESS.equals(devCfgPo.getType()), "数据为空");

                    //重新写进 t_dev_cfg
                    DevCfgPo devCfgPo1 = new DevCfgPo();
                    BeanUtils.copyProperties(param, devCfgPo1);
                    devCfgPo1.setEnable(Boolean.TRUE)
                            .setId(null)
                            .setCode(devCfgPo.getCode())
                            .setVer(devCfgPo.getVer() + 1L);
                    devCfgRwDs.insertDevCfg(devCfgPo1);
                    //原来的 enable = false
                    devCfgPo.setEnable(Boolean.FALSE);
                    devCfgRwDs.updateDevCfg(devCfgPo);
                    /**以下各个配置表调整**/
                    Long devId = devCfgPo1.getId();
                    //充放电时段参数
                    if (null != param.getInOutCfg()) {
                        EssInOutCfgPo inOutCfgPo = new EssInOutCfgPo();
                        BeanUtils.copyProperties(param.getInOutCfg(), inOutCfgPo);
                        inOutCfgPo.setCfgId(devId);
                        if (null == inOutCfgPo.getCoupleMode()) {
                            inOutCfgPo.setCoupleMode(InOutCoupleMode.UNKNOWN);
                        }
                        this.addInOutCfg(inOutCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.IN_OUT_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    //削峰填谷
                    if (null != param.getPeakShareCfg()) {
                        EssPeakShareCfgPo essPeakShareCfgPo = new EssPeakShareCfgPo();
                        BeanUtils.copyProperties(param.getPeakShareCfg(), essPeakShareCfgPo);
                        essPeakShareCfgPo.setCfgId(devId);
                        this.addPeakShareCfg(essPeakShareCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.PEAK_SHARE_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    //储能开关机设置
                    if (null != param.getEssCfg()) {
                        EssCfgPo essCfgPo = new EssCfgPo();
                        BeanUtils.copyProperties(param.getEssCfg(), essCfgPo);
                        essCfgPo.setStrategy(param.getStrategy());
                        essCfgPo.setSamplingTime(param.getSamplingTime());
                        essCfgPo.setCfgId(devId);
                        this.addEssCfg(essCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.ESS_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    //平滑输出
                    if (null != param.getSmoothOutCfg()) {
                        SmoothOutPutCfgPo smoothOutPutCfgPo = new SmoothOutPutCfgPo();
                        BeanUtils.copyProperties(param.getSmoothOutCfg(), smoothOutPutCfgPo);
                        smoothOutPutCfgPo.setCfgId(devId);
                        this.addSmoothOutPutCfg(smoothOutPutCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.SMOOTH_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    // 调频控制
                    if (null != param.getFmCtrlCfg()) {
                        EssFmCtrlCfgPo essFmCtrlCfgPo = new EssFmCtrlCfgPo();
                        BeanUtils.copyProperties(param.getFmCtrlCfg(), essFmCtrlCfgPo);
                        essFmCtrlCfgPo.setCfgId(devId);
                        this.addFmCtrlCfg(essFmCtrlCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.FM_CTRL_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    //柴油机控制
                    if (null != param.getGeneratorCfg()) {
                        EssGeneratorCfgPo essGeneratorCfgPo = new EssGeneratorCfgPo();
                        BeanUtils.copyProperties(param.getGeneratorCfg(), essGeneratorCfgPo);
                        essGeneratorCfgPo.setCfgId(devId);
                        this.addGeneratorCfg(essGeneratorCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.GENERAL_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }
                    //干节点控制
                    if (null != param.getDrySpotCfg()) {
                        EssDrySpotCfgPo essDrySpotCfgPo = new EssDrySpotCfgPo();
                        BeanUtils.copyProperties(param.getDrySpotCfg(), essDrySpotCfgPo);
                        essDrySpotCfgPo.setCfgId(devId);
                        if (null == essDrySpotCfgPo.getCtrlMode()) {
                            essDrySpotCfgPo.setCtrlMode(DrySpotCtrlMode.UNKNOWN);
                        }
                        this.addDrySpotCfg(essDrySpotCfgPo);
                        this.insertBeforeSelect(List.of(ESS_EQUIP_CDF_TYPE.DRY_SPOT_CFG), devCfgPo.getId(), devCfgPo1.getId());
                    }

                    //采集周期，策略类型每次保存都要做更改
                    EssCfgPo essPo = essCfgRoDs.getByCfgId(devId);
                    if (null == essPo) {
                        EssCfgPo po = new EssCfgPo();
                        po.setCfgId(devId)
                                .setSamplingTime(param.getSamplingTime())
                                .setBootMode(EssBootMode.UNKNOWN)
                                .setSwitchCommand(EssSwitchCommand.UNKNOWN)
                                .setStrategy(param.getStrategy());
                        essCfgRwDs.insertEssCfg(po);
                    } else {
                        essPo.setStrategy(param.getStrategy())
                                .setSamplingTime(param.getSamplingTime());
                        essCfgRwDs.insertEssCfg(essPo);
                    }
                    return RestUtils.buildObjectResponse(devCfgPo1.getId());
                });
    }

    @FunctionalInterface
    interface InsertFunc {
        void process(Long oldId, Long newId);
    }

    private Map<ESS_EQUIP_CDF_TYPE, InsertFunc> mapH = new HashMap<>() {{
        put(ESS_EQUIP_CDF_TYPE.IN_OUT_CFG, (oldId, newId) -> {
            essInOutCfgRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.PEAK_SHARE_CFG, (oldId, newId) -> {
            essPeakShareCfgRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.ESS_CFG, (oldId, newId) -> {
            essCfgRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.SMOOTH_CFG, (oldId, newId) -> {
            essSmoothOutPutRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.FM_CTRL_CFG, (oldId, newId) -> {
            essFmCtrlCfgRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.GENERAL_CFG, (oldId, newId) -> {
            essGeneratorCfgRwDs.insertBeforeSelect(oldId, newId);
        });
        put(ESS_EQUIP_CDF_TYPE.DRY_SPOT_CFG, (oldId, newId) -> {
            essDrySpotCfgRwDs.insertBeforeSelect(oldId, newId);
        });
    }};

    void insertBeforeSelect(List<ESS_EQUIP_CDF_TYPE> filterList, Long oldId, Long newId) {
        Stream.of(ESS_EQUIP_CDF_TYPE.values())
                .filter(item -> !filterList.contains(item))
                .forEach(item -> {
                    InsertFunc testFunc = mapH.get(item);
                    if (null != testFunc) testFunc.process(oldId, newId);
                });
    }

    /**
     * 新增逆变器模板
     *
     * @param param
     * @return
     */
    @Transactional
    public Mono<BaseResponse> addGtiCfg(AddGtiCfgParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                List<Long> idList = devCfgRoDs.getByName(param.getName(), param.getSiteId());
                IotAssert.isTrue(CollectionUtils.isEmpty(idList), "模板名称重复");
                DevCfgPo po = new DevCfgPo();
                BeanUtils.copyProperties(p, po);
                po.setCode(RandomStringUtils.random(10, RANDOM_CHAR))
                    .setVer(1L)
                    .setEnable(Boolean.TRUE);
                devCfgRwDs.insertDevCfg(po);

                if (CfgType.GOOD_WE_GTI == p.getType()) {
                    GtiCfgPo gtiCfgPo = new GtiCfgPo();
                    BeanUtils.copyProperties(p, gtiCfgPo);
                    gtiCfgPo.setCfgId(po.getId());
                    gtiCfgRwDs.insertGtiCfg(gtiCfgPo);
                } else if (CfgType.HUAWEI_GTI == p.getType()) {
                    if (p.getGridDispatchCfgPo() != null) {
                        p.getGridDispatchCfgPo().setCfgId(po.getId());
                        p.getGridDispatchCfgPo().setSamplingTime(p.getSamplingTime());
                        gtiGridDispatchCfgRwDs.insertGtiCfg(p.getGridDispatchCfgPo());
                    }
                } else {
                    throw new DcServiceException("不支持的品牌");
                }
            })
            .map(p -> RestUtils.success());
    }

    /**
     * 逆变器模板编辑
     *
     * @param param
     * @return
     */
    @Transactional
    public Mono<BaseResponse> editGtiCfg(AddGtiCfgParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                DevCfgPo devCfgPo = devCfgRoDs.getById(param.getId(), param.getCommIdChain());
                IotAssert.isNotNull(devCfgPo, "数据为空");
                List<Long> idList = devCfgRoDs.getByName(param.getName(), param.getSiteId())
                    .stream().filter(e -> !e.equals(param.getId())).collect(Collectors.toList());
                IotAssert.isTrue(CollectionUtils.isEmpty(idList), "模板名称重复");
                //新增

                DevCfgPo po = new DevCfgPo();
                BeanUtils.copyProperties(p, po);
                po.setCode(devCfgPo.getCode())
                    .setVer(devCfgPo.getVer() + 1L)
                    .setCommId(devCfgPo.getCommId())
                    .setSiteId(devCfgPo.getSiteId())
                    .setEnable(Boolean.TRUE)
                    .setId(null);
                devCfgRwDs.insertDevCfg(po);

                //上一个版本false
                devCfgPo.setEnable(Boolean.FALSE);
                devCfgRwDs.updateDevCfg(devCfgPo);

                if (CfgType.GOOD_WE_GTI == p.getType()) {
                    GtiCfgPo gtiCfgPo = new GtiCfgPo();
                    BeanUtils.copyProperties(p, gtiCfgPo);
                    gtiCfgPo.setCfgId(po.getId());
                    gtiCfgRwDs.insertGtiCfg(gtiCfgPo);
                } else if (CfgType.HUAWEI_GTI == p.getType()) {
                    if (p.getGridDispatchCfgPo() != null) {
                        p.getGridDispatchCfgPo().setCfgId(po.getId());
                        p.getGridDispatchCfgPo().setSamplingTime(p.getSamplingTime());
                        gtiGridDispatchCfgRwDs.insertGtiCfg(p.getGridDispatchCfgPo());
                    }
                } else {
                    throw new DcServiceException("不支持的品牌");
                }
            })
            .map(p -> RestUtils.success());
    }

    public Mono<BaseResponse> delDevCfg(AddGtiCfgParam param) {
        return Mono.just(param)
                .doOnNext(p -> {
                    DevCfgPo devCfgPo = devCfgRoDs.getById(p.getId(),p.getCommIdChain());
                    IotAssert.isTrue(null != devCfgPo,"无法获取模板信息");
                    List<CfgType> gtiList = List.of(CfgType.GOOD_WE_GTI, CfgType.HUAWEI_GTI);
                    if (gtiList.contains(devCfgPo.getType())) {
                        gtiCfgRoDs.delPrecheck(p.getId());
                    } else if (CfgType.HT_ESS == devCfgPo.getType()) {
                        essCfgRoDs.delPrecheck(p.getId());
                    }
                    devCfgPo.setEnable(Boolean.FALSE)
                            .setOpName(p.getOpName())
                            .setOpUid(p.getOpUid());
                    devCfgRwDs.updateDevCfg(devCfgPo);
                })
                .map(p -> RestUtils.success());
    }

    public Mono<ListResponse<DevCfgVo>> getDevCfgList(ListDevCfgParam param) {
        return Mono.just(param).map(p -> {
            if (null == p.getStart()) {
                p.setStart(0L);
            }
            if (null == p.getSize()) {
                p.setSize(10);
            }
            Long total = devCfgRoDs.getDevCfgCount(p);
            if (null == total) {
                return RestUtils.buildListResponse(null);
            }
            List<DevCfgVo> list = devCfgRoDs.getDevCfgList(p);

            list.stream().forEach(e -> {
                if (CfgType.GOOD_WE_GTI.equals(e.getType()) || CfgType.HUAWEI_GTI.equals(e.getType())) {
                    e.setAmount(gtiRoDs.getCountByCfgId(e.getId()));
                } else if (CfgType.HT_ESS.equals(e.getType())) {
                    e.setAmount(essRoDs.getEssCountByCfgId(e.getId()));
                }
            });
            return RestUtils.buildListResponse(list, total);
        });
    }

    public Mono<ObjectResponse<EssDetailVo>> getDevCfgDetail(Long id, String commIdChain) {
        return Mono.just(id)
                .map(p -> {
                    IotAssert.isNotNull(p, "请求参数不正确");
                    DevCfgPo devCfgPo = devCfgRoDs.getById(p, commIdChain);
                    IotAssert.isTrue(null != devCfgPo && CfgType.HT_ESS.equals(devCfgPo.getType()), "数据信息不存在");

                    EssDetailVo detailVo = new EssDetailVo();
                    BeanUtils.copyProperties(devCfgPo, detailVo);
                    detailVo.setInOutCfg(essInOutCfgRoDs.getById(devCfgPo.getId()) == null ? new EssInOutCfgPo() : essInOutCfgRoDs.getById(devCfgPo.getId()));
                    detailVo.setPeakShareCfg(essPeakShareCfgRoDs.getById(devCfgPo.getId()) == null ? new EssPeakShareCfgPo() : essPeakShareCfgRoDs.getById(devCfgPo.getId()));
                    detailVo.setSmoothOutCfg(essSmoothOutPutCfgRoDs.getById(devCfgPo.getId()) == null ? new SmoothOutPutCfgPo() : essSmoothOutPutCfgRoDs.getById(devCfgPo.getId()));
                    detailVo.setFmCtrlCfg(essFmCtrlCfgRoDs.getById(devCfgPo.getId()) == null ? new EssFmCtrlCfgPo() : essFmCtrlCfgRoDs.getById(devCfgPo.getId()));
                    detailVo.setGeneratorCfg(essGeneratorCfgRoDs.getById(devCfgPo.getId()) == null ? new EssGeneratorCfgPo() : essGeneratorCfgRoDs.getById(devCfgPo.getId()));
                    detailVo.setDrySpotCfg(essDrySpotCfgRoDs.getById(devCfgPo.getId()) == null ? new EssDrySpotCfgPo() : essDrySpotCfgRoDs.getById(devCfgPo.getId()));

                    EssCfgPo essCfgPo = essCfgRoDs.getByCfgId(devCfgPo.getId());
                    detailVo.setEssCfg(essCfgPo == null ? new EssCfgPo() : essCfgPo);
                    if (null != essCfgPo) {
                        detailVo.setSamplingTime(essCfgPo.getSamplingTime());
                        detailVo.setStrategy(essCfgPo.getStrategy());
                    }
                    return RestUtils.buildObjectResponse(detailVo);
                });
    }

    public Mono<ObjectResponse<GtiCfgVo>> getGtiCfgDetail(Long id, String commIdChain) {
        return Mono.just(id)
            .map(p -> {
                DevCfgPo devCfgPo = devCfgRoDs.getById(id, commIdChain);
                IotAssert.isNotNull(devCfgPo, "模板不存在");
                GtiCfgVo gtiCfgVo = devCfgRoDs.getGtiDetailById(id, commIdChain,
                    devCfgPo.getType());
                return RestUtils.buildObjectResponse(gtiCfgVo);
            });
    }

    /**
     * 新增充放电时段参数
     *
     * @param po
     */
    public void addInOutCfg(EssInOutCfgPo po) {
        essInOutCfgRwDs.insertEssInOutCfg(po);
    }

    /**
     * 新增削峰填谷参数
     *
     * @param po
     */
    public void addPeakShareCfg(EssPeakShareCfgPo po) {
        essPeakShareCfgRwDs.insertEssPeakShareCfg(po);
    }

    /**
     * 新增储能开关机设置
     *
     * @param po
     */
    public void addEssCfg(EssCfgPo po) {
        essCfgRwDs.insertEssCfg(po);
    }

    /**
     * 新增平滑输出
     *
     * @param po
     */
    public void addSmoothOutPutCfg(SmoothOutPutCfgPo po) {
        essSmoothOutPutRwDs.insertEssSmoothOutPutCfg(po);
    }

    /**
     * 新增调频控制
     *
     * @param po
     */
    public void addFmCtrlCfg(EssFmCtrlCfgPo po) {
        essFmCtrlCfgRwDs.insertEssFmCtrlCfg(po);
    }

    /**
     * 新增柴油机控制
     *
     * @param po
     */
    public void addGeneratorCfg(EssGeneratorCfgPo po) {
        essGeneratorCfgRwDs.insertEssGeneratorCfg(po);
    }

    /**
     * 新增干接点控制
     *
     * @param po
     */
    public void addDrySpotCfg(EssDrySpotCfgPo po) {
        essDrySpotCfgRwDs.insertEssDrySpotCfg(po);
    }


}
