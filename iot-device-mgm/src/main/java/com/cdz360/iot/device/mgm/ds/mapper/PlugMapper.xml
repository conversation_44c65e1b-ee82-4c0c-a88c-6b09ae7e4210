<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.device.mgm.ds.mapper.PlugMapper">

  <!--获取场站的枪列表-->
  <select id="getPlugsOfSite" resultType="com.cdz360.iot.device.mgm.model.bi.vo.PlugInfoDto">
    select p.evseId, p.plugId, p.orderNo, ev.supply as supplyType,
    ev.name as evseName, p.name as plugName, p.plugStatus as status,
    p.voltageMin minV,
    p.voltageMax maxV,
    p.currentMin minA,
    p.currentMax maxA,
    p.power,
    ev.power as evsePower
    from t_plug as p
    left join t_evse ev on p.evseId = ev.evseId
    where 1=1
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      <foreach item="siteId" collection="siteIdList"
        open="and ev.siteId in (" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
      <foreach item="bizStatus" collection="bizStatusList"
        open="and ev.bizStatus in (" close=")" separator=",">
        #{bizStatus.code}
      </foreach>
    </if>
    ORDER BY FIELD(p.plugStatus, 'IDLE', 'CONNECT', 'BUSY', 'RECHARGE_END', 'OFFLINE', 'ERROR',
    'OFF',
    'UNKNOWN')
    limit #{start}, #{size}
  </select>

  <!-- 更新枪名称 -->
  <update id="refreshPlugName">
    update t_plug set `name` = #{name} where evseId = #{evseId} and plugId = #{plugId}
  </update>
</mapper>