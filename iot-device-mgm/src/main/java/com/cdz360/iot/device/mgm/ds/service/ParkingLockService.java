package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.feign.ParkFeignClient;
import com.cdz360.iot.ds.ro.ParkingLockEventLogRoDs;
import com.cdz360.iot.ds.ro.ParkingLockRoDs;
import com.cdz360.iot.ds.rw.ParkingLockRwDs;
import com.cdz360.iot.model.park.dto.ParkingLockDto;
import com.cdz360.iot.model.park.param.ListParkingLockParam;
import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ParkingLockService {

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private ParkingLockRoDs parkingLockRoDs;

    @Autowired
    private ParkingLockRwDs parkingLockRwDs;

    @Autowired
    private ParkingLockEventLogRoDs parkingLockEventLogRoDs;

    @Autowired
    private ParkFeignClient parkFeignClient;

    public Mono<ListResponse<ParkingLockVo>> parkingLockList(ListParkingLockParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.parkingLockRoDs::parkingLockList)
            .map(x -> {
                // 状态更新时间
                final List<Long> recIdList = x.stream().map(ParkingLockVo::getId)
                    .distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(recIdList)) {
                    final Map<Long, ParkingLockEventLogPo> m =
                        parkingLockEventLogRoDs.latestStatusUpdateTime(recIdList).stream()
                            .collect(
                                Collectors.toMap(ParkingLockEventLogPo::getParkingLockId, o -> o));

                    if (CollectionUtils.isNotEmpty(m.keySet())) {
                        x.forEach(k -> {
                            if (m.containsKey(k.getId())) {
                                k.setStatusUpdateTime(m.get(k.getId()).getCreateTime());
                            }
                        });
                    }
                }
                return x;
            })
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.parkingLockRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ParkingLockVo> removeParkingLock(Long lockId) {
        return Mono.justOrEmpty(parkingLockRoDs.getVoById(lockId))
            .switchIfEmpty(Mono.error(new DcArgumentException("地锁ID无效")))
            .doOnNext(vo -> {
                ParkingLockPo lockPo = new ParkingLockPo()
                    .setPartner(vo.getPartner())
                    .setSerialNumber(vo.getSerialNumber())
                    .setEvseNo("")
                    .setPlugId(0);
                parkingLockRwDs.upsetParkingLock(lockPo);
            });
    }

    public Mono<ParkingLockVo> addParkingLock(ParkingLockDto dto) {
        return Mono.just(dto)
            .doOnNext(d -> {
                if (StringUtils.isBlank(dto.getSerialNumber())) {
                    throw new DcArgumentException("车位锁ID不能为空");
                }

                if (StringUtils.isBlank(dto.getEvseNo())) {
                    throw new DcArgumentException("桩编号不能为空");
                }
            })
            .flatMap(d -> {
                EvseVo evse = redisIotReadService.getEvseRedisCache(dto.getEvseNo());
                IotAssert.isNotNull(evse, "锁还没有绑定到桩");
                IotAssert.isNotBlank(evse.getSiteId(), "请先将桩绑定到场站，再操作");
                return parkFeignClient.lookForLock(evse.getSiteId(), dto.getSerialNumber())
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData)
                    .map(x -> x.setSiteId(evse.getSiteId()).setSiteName(evse.getSiteName()));
            })
            .doOnNext(d -> {
                ParkingLockPo lock = parkingLockRoDs.getByEvseNoAndPlugId(
                    dto.getEvseNo(), dto.getPlugId());
                IotAssert.isNull(lock, "该枪头已经绑定对应的地锁，请删除后再操作");

                ParkingLockPo result = new ParkingLockPo();
                BeanUtils.copyProperties(d, result);
                result.setEvseNo(dto.getEvseNo())
                    .setPlugId(dto.getPlugId())
                    .setPositionCode(dto.getPositionCode())
                    .setDevUuid(dto.getSerialNumber());
                parkingLockRwDs.upsetParkingLock(result);
            });
    }

    public Mono<List<ParkingLockEventLogVo>> eventLogRecent20(Long parkingLockId) {
        IotAssert.isNotNull(parkingLockId, "地锁ID不能为空");
        return Mono.just(parkingLockEventLogRoDs.eventLogRecent(parkingLockId, 20));
    }

}
