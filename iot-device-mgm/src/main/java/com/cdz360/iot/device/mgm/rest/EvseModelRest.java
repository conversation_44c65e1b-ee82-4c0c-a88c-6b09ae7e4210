package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.EvseModelService;
import com.cdz360.iot.model.evse.param.ListEvseModelParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/device/mgm/evseModel")
public class EvseModelRest {

    @Autowired
    private EvseModelService service;

    @PostMapping("/list")
    public ListResponse<EvseModelPo> listEvseBundlePage(@RequestBody ListEvseModelParam param) {
        log.info("listEvseBundlePage param: {}", param);
        return service.listEvseBundlePage(param);
    }

    @PostMapping("/add")
    public BaseResponse addEvseModel(@Validated @RequestBody EvseModelPo po) {
        log.info("addEvseModel po: {}", po);
        return service.addEvseModel(po);
    }

    @PostMapping("/edit")
    public BaseResponse editEvseModel(@Validated @RequestBody EvseModelPo po) {
        log.info("editEvseModel param: {}", po);
        Pair<Boolean, Boolean> res = service.editEvseModel(po);
        if (res.getLeft()) {
            if (res.getRight()) service.afterEditEvseModel(po);
            return RestUtils.success();
        } else {
            return RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败");
        }
    }

    @PostMapping("/changeStatus")
    public BaseResponse changeStatus(@RequestParam("id") Long id,
                                     @RequestParam("enable") Boolean enable) {
        log.info("changeStatus id: {} enable: {}", id, enable);
        return service.changeStatus(id, enable);
    }

    @PostMapping("/remove")
    public BaseResponse remove(@RequestParam("id") Long id) {
        log.info("remove id: {}", id);
        return service.remove(id);
    }

    @GetMapping("/getBrandList")
    public ListResponse<String> getBrandList() {
        return RestUtils.buildListResponse(service.getBrandList());
    }

    @GetMapping("/findById")
    public ObjectResponse<EvseModelPo> findById(@RequestParam("id") Long id) {
        return service.findById(id);
    }

}
