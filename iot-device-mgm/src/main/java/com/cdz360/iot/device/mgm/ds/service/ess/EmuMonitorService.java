package com.cdz360.iot.device.mgm.ds.service.ess;

import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.BmsStackRtData;
import com.cdz360.base.model.es.vo.EmuMonitorMetricsVo;
import com.cdz360.base.model.es.vo.EssChargeTinySummary;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.meter.vo.MeterAbcData;
import com.cdz360.data.cache.RedisEmuRoService;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.device.mgm.feign.OpenHlhtFeignClient;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EmuMonitorService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private RedisEmuRoService redisEmuRoService;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    public Mono<EmuMonitorMetricsVo> siteEssDeviceMetrics(String siteId) {
        List<EssStatusBi> essStatus = essRoDs.getEssStatusBi(null, siteId);
        List<EssStatusBi> equipStatus = essEquipRoDs.getEquipStatusBi(siteId);
        List<EssStatusBi> collect = Stream.concat(essStatus.stream(), equipStatus.stream())
            .filter(x -> null != x.getStatus() && x.getNum() != null)
            .collect(Collectors.toList());

        // 设备状态统计
        EmuMonitorMetricsVo result = new EmuMonitorMetricsVo()
            .setNormalCount(collect.stream()
                .filter(x -> x.getStatus() == 1).mapToLong(EssStatusBi::getNum).sum())
            .setOfflineCount(collect.stream()
                .filter(x -> x.getStatus() == 2).mapToLong(EssStatusBi::getNum).sum())
            .setErrorCount(collect.stream()
                .filter(x -> x.getStatus() == 3).mapToLong(EssStatusBi::getNum).sum());

        // 获取PCS有功功率/无功功率
        List<EssEquipPo> pcsList = essEquipRoDs.findEquipListBySiteId(siteId, EssEquipType.PCS);
        if (CollectionUtils.isNotEmpty(pcsList)) {
            // redis 中获取数据
            List<MeterAbcData> data = pcsList.stream().map(x -> {
                PcsRtData pcsRtData = redisEmuRoService.getPcsRtData(x.getDno());
                if (null != pcsRtData && null != pcsRtData.getAcData()) {
                    return pcsRtData.getAcData();
                }
                return new MeterAbcData();
            }).collect(Collectors.toList());

            result.setActivePower(data.stream()
                    .filter(x -> x.getActivePower() != null && null != x.getActivePower().getTotal())
                    .map(x -> x.getActivePower().getTotal())
                    .reduce(BigDecimal.ZERO, BigDecimal::add))
                .setReactivePower(data.stream()
                    .filter(x -> x.getReactivePower() != null && null != x.getReactivePower()
                        .getTotal())
                    .map(x -> x.getReactivePower().getTotal())
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            // 充放电量
            List<EssChargeTinySummary> summaries = pcsList.stream().map(x -> {
                PcsRtData pcsRtData = redisEmuRoService.getPcsRtData(x.getDno());
                if (null != pcsRtData && null != pcsRtData.getChargeData()) {
                    return pcsRtData.getChargeData();
                }
                return new EssChargeTinySummary();
            }).collect(Collectors.toList());
            result.setChargeData(
                new EssChargeTinySummary()
                    .setInKwh(summaries.stream().map(EssChargeTinySummary::getInKwh)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .setOutKwh(summaries.stream().map(EssChargeTinySummary::getOutKwh)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .setTotalInKwh(summaries.stream().map(EssChargeTinySummary::getTotalInKwh)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .setTotalOutKwh(summaries.stream().map(EssChargeTinySummary::getTotalOutKwh)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)));
        } else {
            result.setActivePower(BigDecimal.ZERO)
                .setReactivePower(BigDecimal.ZERO);
        }

        // 电池SOC合计
        List<EssEquipPo> bmsList = essEquipRoDs.findEquipListBySiteId(siteId, EssEquipType.BMS);
        if (CollectionUtils.isNotEmpty(bmsList)) {
            // FIXME: 这里的计算需要结合容量来调整，暂时采用平均
            BigDecimal soc = bmsList.stream().map(x -> {
                    BmsRtData bmsRtData = redisEmuRoService.getBmsRtData(x.getDno());
                    if (null != bmsRtData && CollectionUtils.isNotEmpty(bmsRtData.getStackDataList())) {
                        return bmsRtData.getStackDataList().stream()
                            .map(BmsStackRtData::getSoc)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(bmsRtData.getStackDataList().size()), 2,
                                RoundingMode.HALF_UP);
                    }
                    return BigDecimal.ZERO;
                }).reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(bmsList.size()), 2, RoundingMode.HALF_UP);
            result.setSoc(soc);

            BigDecimal soh = bmsList.stream().map(x -> {
                    BmsRtData bmsRtData = redisEmuRoService.getBmsRtData(x.getDno());
                    if (null != bmsRtData && CollectionUtils.isNotEmpty(bmsRtData.getStackDataList())) {
                        return bmsRtData.getStackDataList().stream()
                            .map(BmsStackRtData::getSoh)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(bmsRtData.getStackDataList().size()), 2,
                                RoundingMode.HALF_UP);
                    }
                    return BigDecimal.ZERO;
                }).reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(bmsList.size()), 2, RoundingMode.HALF_UP);
            result.setSoh(soh);
        } else {
            result.setSoc(BigDecimal.ZERO)
                .setSoh(BigDecimal.ZERO);
        }
        // 增加互联互通的ess场站信息数据
        return openHlhtFeignClient.getEssDayBi(siteId)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(e -> {
                Pair<Boolean, EssDataBi> pair = e.getData();
                if (pair.getFirst()) {
                    result.setChargeData(new EssChargeTinySummary()
                        .setInKwh(pair.getSecond().getInKwh())
                        .setOutKwh(pair.getSecond().getOutKwh()));
//                        .setTotalInKwh(pair.getSecond().getInKwh())
//                        .setTotalOutKwh(pair.getSecond().getOutKwh()));
                }
                return Mono.just(result);
            });
    }
}
