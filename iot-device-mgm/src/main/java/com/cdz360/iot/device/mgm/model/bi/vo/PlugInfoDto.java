package com.cdz360.iot.device.mgm.model.bi.vo;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 枪具体信息
 *
 * @Author: Nathan
 * @Date: 2019/8/16 12:19
 */
@Data
@Accessors(chain = true)
public class PlugInfoDto {
    // 桩编号
    @Schema(description = "桩编号")
    private String evseId;

    // 桩名称
    @Schema(description = "桩名称")
    private String evseName;

//    @Schema(description = "枪编号")
//    private String plugNo;

    // 枪编号
    @Schema(description = "枪序号")
    private Integer plugId;

    // 枪名称
    @Schema(description = "枪名称")
    private String plugName;

    // 枪状态
    @Schema(description = "枪状态")
    private PlugStatus status;

    // 电流形式
    @Schema(description = "电流形式")
    private SupplyType supplyType;

    // 功率
    @Schema(description = "枪头功率")
    private Long power;

    @Schema(description = "整桩功率")
    private Long evsePower;

    // 输出电压
    @Schema(description = "输出电压")
    private BigDecimal maxV;

    // 输出最小电压
    @Schema(description = "输出最小电压")
    private BigDecimal minV;


    // 枪头最大电流
    @Schema(description = "枪头最大电流")
    private BigDecimal maxA;

    // 枪头最低电流
    @Schema(description = "枪头最低电流")
    private BigDecimal minA;


    // 当前进行中的订单号
    @Schema(description = "当前进行中的订单号")
    private String orderNo;

    // 当前进行中订单的SOC
//    @Schema(description = "当前进行中订单的SOC(百分比)")
//    private Integer soc;

}
