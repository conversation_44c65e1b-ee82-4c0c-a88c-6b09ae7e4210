package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class ZTEStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("ZTE", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "输入欠压";
                break;
            case 1:
                res = "输入缺相";
                break;
            case 2:
                res = "输入过压";
                break;
            case 3:
                res = "输出过压";
                break;
            case 4:
                res = "输出过流";
                break;
            case 5:
                res = "过温";
                break;
            case 6:
                res = "风扇故障";
                break;
            case 7:
                res = "BUS不平衡";
                break;
            case 8:
                res = "输出欠压";
                break;
            case 9:
                res = "主变原边过流";
                break;
            case 10:
                res = "SCI通信异常";
                break;
            case 11:
                res = "PFC BUS过压";
                break;
            case 12:
                res = "PFC BUS欠压";
                break;
            case 13:
                res = "缓启动异常";
                break;
            case 14:
                res = "均流异常";
                break;
            case 15:
                res = "PFC输入过流";
                break;
            case 16:
                res = "EEPROM故障";
                break;
            case 17:
                res = "交流频率异常";
                break;
            case 18:
                res = "三相输入不平衡";
                break;
            case 19:
                res = "交流输入端";
                break;
            case 20:
                res = "校准失败";
                break;
            case 21:
                res = "输出过载";
                break;
            case 22:
                res = "can通信异常";
                break;
            case 23:
                res = "输出泄放损坏";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
