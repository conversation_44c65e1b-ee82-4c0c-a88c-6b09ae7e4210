package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.SiteCtrlService;
import com.cdz360.iot.device.mgm.model.req.SiteCtrlReq;
import com.cdz360.iot.model.base.Update;
import com.cdz360.iot.model.site.dto.SiteCtrlDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Classname SiteCtrlController
 * @Description
 * @Date 4/22/2020 7:15 PM
 * @Created by <PERSON>
 */
@Slf4j
@RestController
@Tag(name = "场站控制器相关接口", description = "场站控制器")
@RequestMapping("/device/mgm/siteCtrl")
public class SiteCtrlRest {

    @Autowired
    private SiteCtrlService siteCtrlService;

    @PostMapping("/add")
    public BaseResponse add(@Validated @RequestBody SiteCtrlReq req) {
        log.info("req: {}", req);
        return siteCtrlService.add(req);
    }

    @PostMapping("/edit")
    public BaseResponse edit(@Validated(Update.class) @RequestBody SiteCtrlReq req) {
        log.info("req: {}", req);
        return siteCtrlService.edit(req);
    }

    @GetMapping("/list")
    public ListResponse<SiteCtrlDto> list(@RequestParam(value = "keyword", required = false) String keyword,
                                          @RequestParam(value = "siteId") String siteId,
                                          @RequestParam(value = "start") long start,
                                          @RequestParam(value = "size") long size) {
        log.info("keyword: {}, siteId: {}, start: {}, size: {}", keyword, siteId, start, size);
        return siteCtrlService.list(keyword, siteId, start, size);
    }

    @GetMapping("/disable")
    public BaseResponse disable(@RequestParam(value = "num") String num) {
        log.info("num: {}", num);
        siteCtrlService.disable(num);
        return RestUtils.success();
    }
}