package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.feign.BizDataCoreFeignClient;
import com.cdz360.iot.device.mgm.model.basic.dto.SiteProfitInfo;
import com.cdz360.iot.device.mgm.model.basic.param.ListSiteProfitParam;
import com.cdz360.iot.device.mgm.model.basic.po.PriceItemPo;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiDailyRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.GtiDailyRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ds.rw.GwInfoRwDs;
import com.cdz360.iot.ds.rw.GwSiteRefRwDs;
import com.cdz360.iot.model.pv.dto.CntCtrlGtiDto;
import com.cdz360.iot.model.pv.dto.PvRtDataPowerProfit;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.param.PvProfitTrendParam;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.vo.DayPvDataBi;
import com.cdz360.iot.model.pv.vo.GtiDataInTimeVo;
import com.cdz360.iot.model.pv.vo.GtiSampleData;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.pv.vo.RedisPvRtData;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.cdz360.iot.model.type.SiteBiSampleType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class GtiBizService {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";

    @Autowired
    private GwInfoRwDs gwInfoRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwSiteRefRwDs gwSiteRefRwDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private GtiCfgRoDs gtiCfgRoDs;

    @Autowired
    private GtiDailyRoDs gtiDailyRoDs;

    @Autowired
    private GtiDailyRwDs gtiDailyRwDs;

    @Autowired
    private RedisPvRtDataService redisPvRtDataService;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private RedisEquipRtDataService redisEquipRtDataService;

    private GwInfoPo getGwInfo(String siteId) {
        GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
        if (gwInfo == null) {
            log.error("can't find gwno for siteId = {}", siteId);
            throw new DcArgumentException("请配置场站对应的网关");
        }
        return gwInfo;
    }

    // 微网控制器数量、逆变器数量
    public Mono<CntCtrlGtiDto> countCtrlGti(String siteId) {
        return Mono.just(siteId)
                .map(gwSiteRefRoDs::countCtrlGti);
    }

    public Mono<ListResponse<DeviceInfoVo>> findCtrlVoList(List<String> siteIdList) {
        return Mono.just(siteIdList)
                .map(gwSiteRefRoDs::findCtrlVoList)
                .map(RestUtils::buildListResponse);
    }

    private static PvRtDataPowerProfit convert2PowerProfit(DayPvDataBi in) {
        PvRtDataPowerProfit ret = new PvRtDataPowerProfit();
        return ret.setTotalProfit(in.getTotalProfit())
                .setTotalKwh(in.getTotalKwh())
                .setTime(in.getDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
    }

    private List<LocalDateTime> reviseResult(PvProfitTrendParam param) {
//        if (param.getStartTime() == null || param.getEndTime() == null) {
//            param.resetTime();
//        }

        LocalDateTime start = param.getStartTime();
        LocalDateTime end = param.getEndTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }

    public Mono<List<GtiSampleData>> gtiRtDataSample(DataBiParam param) {
        IotAssert.isNotNull(param.getSampleType(), "数据汇总单位不能为空(sampleType)");

        switch (param.getSampleType()) {
            case DAY:
            case MONTH:
            case YEAR:
                DataBiParam.rangeDate(param);
                return Mono.just(param)
                        .map(gtiDailyRoDs::gtiRtDataSample)
                        .map(data -> {
                            LocalDate now = LocalDate.now();
                            LocalDate endDate = param.getToDate().toLocalDate();

                            boolean needToday = false;
                            if (null != endDate && endDate.getYear() == now.getYear()) {
                                switch (param.getSampleType()) {
                                    case YEAR:
                                        needToday = true;
                                        break;
                                    case MONTH:
                                        needToday = endDate.getMonth() == now.getMonth();
                                        break;
                                    case DAY:
                                        needToday = now.isEqual(endDate);
                                        break;
                                }
                            }

                            if (needToday) {
                                // FIXME: 获取当天数据
                            }
                            return data;
                        });
            default:
                log.error("数据汇总单位不支持: {}", param.getSampleType());
                throw new DcArgumentException("数据汇总单位不支持");
        }
    }

    public Mono<ListResponse<PvRtDataPowerProfit>> powerProfitTrend(PvProfitTrendParam param) {

        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
        IotAssert.isNotNull(param.getStartTime(), "请传入开始时间");
        IotAssert.isNotNull(param.getEndTime(), "请传入结束时间");

        //重置开始结束时间，保证区间右闭
        if(SiteBiSampleType.DAY.equals(param.getSampleType())) {
            param.setEndTime(param.getEndTime().plusDays(1));
        } else if(SiteBiSampleType.MONTH.equals(param.getSampleType())) {
            param.setEndTime(param.getEndTime().plusMonths(1));
        }
        // 重置开始结束时间，保证区间右闭
        param.resetTime();

        return this.powerProfitTrendGap(param).map(e -> {
            Map<LocalDateTime, PvRtDataPowerProfit> profitMap = e.getData()
                    .stream()
                    .collect(Collectors.toMap(PvRtDataPowerProfit::getTime, o -> o, (o, n) -> n));
//            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(e.getData())) {
                List<LocalDateTime> localDateTimes = this.reviseResult(param);
                List<PvRtDataPowerProfit> resData = localDateTimes.stream()
                        .map(item -> {
                            if (profitMap.get(item) != null) {
                                return profitMap.get(item);
                            } else {
                                PvRtDataPowerProfit ret = new PvRtDataPowerProfit();
                                return ret.setTime(item)
                                        .setTotalKwh(BigDecimal.ZERO)
                                        .setTotalProfit(BigDecimal.ZERO);
                            }
                        })
                        .collect(Collectors.toList());
                e.setData(resData);
//            } else {
//                List<LocalDateTime> localDateTimes = this.reviseResult(param);
//                List<PvRtDataPowerProfit> resData = localDateTimes.stream()
//                        .map(item -> {
//                            PvRtDataPowerProfit ret = new PvRtDataPowerProfit();
//                            return ret.setTime(item)
//                                    .setTotalKwh(BigDecimal.ZERO)
//                                    .setTotalProfit(BigDecimal.ZERO);
//                        })
//                        .collect(Collectors.toList());
//                e.setData(resData);
//            }
            return e;
        });
    }


    private Mono<ListResponse<PvRtDataPowerProfit>> powerProfitTrendGap(PvProfitTrendParam param) {

        ZoneId defaultZoneId = ZoneId.systemDefault();
        if(SiteBiSampleType.DAY.equals(param.getSampleType())) {
            return Mono.just(gtiDailyRoDs.siteRtDataOfDay(param.getSiteId(),
                                    Date.from(param.getStartTime().atZone(defaultZoneId).toInstant()),
                                    Date.from(param.getEndTime().atZone(defaultZoneId).toInstant()))
                            .stream()
                            .map(GtiBizService::convert2PowerProfit)
                            .collect(Collectors.toList()))
                    .map(RestUtils::buildListResponse);
        } else if(SiteBiSampleType.MONTH.equals(param.getSampleType())) {
            return Mono.just(gtiDailyRoDs.siteRtDataOfMonthGroup(param.getSiteId(),
                                    Date.from(param.getStartTime().atZone(defaultZoneId).toInstant()),
                                    Date.from(param.getEndTime().atZone(defaultZoneId).toInstant()))
                            .stream()
                            .map(GtiBizService::convert2PowerProfit)
                            .collect(Collectors.toList()))
                    .map(RestUtils::buildListResponse);
        } else {
            IotAssert.isTrue(false, "不支持的时间单位，仅支持天、月");
            List<PvRtDataPowerProfit> ret = List.of();
            return Mono.just(ret)
                    .map(RestUtils::buildListResponse);
        }
    }

    /**
     * 逆变器列表
     * @param param
     * @return
     */
    public Mono<ListResponse<GtiVo>> findGtiList(ListGtiParam param) {
        return Mono.just(param)
                .map(gtiRoDs::findGtiList)
                .map(list->{
                    if (CollectionUtils.isNotEmpty(list)) {
                        list.forEach(e->{
                            e.setPvData(redisPvRtDataService.pvSrcInfo(e.getDno()));
                        });
                    }
                    Long total = 0L;
                    if (null != param.getTotal() && param.getTotal() && list.size()>0) {
                       total = gtiRoDs.count(param);
                    }
                    return RestUtils.buildListResponse(list,total);
                });
    }

    public Mono<GtiDataInTimeVo> gtiInfoInTime(String dno) {
        if (StringUtils.isBlank(dno)) {
            throw new DcArgumentException("逆变器设备编号无效");
        }

        GtiPo gti = gtiRoDs.getByDno(dno);
        if (null == gti) {
            throw new DcArgumentException("逆变器不存在");
        }

        return Mono.just(dno)
                .map(gtiDailyRoDs::gtiInfoInTime)
                .map(res -> {
                    RedisPvRtData data = redisPvRtDataService.latestRtData(dno, LocalDate.now());
                    if (null != data) {
                        res.setRealtimeData(data);
                        res.getRealtimeData()
                            .setRatedPower(gti.getPower() != null
                                ? BigDecimal.valueOf(gti.getPower()) : null)
                            .setPartNo(gti.getPartNo())
                            .setDeviceModel(gti.getDeviceModel())
                            .setGroupNum(gti.getGroupNum())
                            .setMpptNum(gti.getMpptNum());
                    }
                    res.setStatus(gti.getStatus());
                    return res;
                });
    }

    public Mono<Long> syncRtData2Sql(LocalDate destDate) {
        List<GtiVo> gtiList = this.gtiRoDs.findGtiList(new ListGtiParam());

        List<String> siteIdList = gtiList.stream().map(GtiVo::getSiteId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(siteIdList)) {
            return Mono.just(0L);
        }

        final LocalDate yesterday = null == destDate ? LocalDate.now().plusDays(-1) : destDate;
        final Date date = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        AtomicReference<Map<String, List<PriceItemPo>>> siteProfitMap = new AtomicReference<>();
        ListSiteProfitParam profitParam = new ListSiteProfitParam();
        return bizDataCoreFeignClient.getSiteProfitList(profitParam.setSiteIdList(siteIdList))
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .doOnNext(profitList -> siteProfitMap.set(profitList.stream()
                        .collect(Collectors.toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getPriceItemPoList))))
                .map(i -> gtiList)
                .flatMapMany(Flux::fromIterable)
                .flatMap(gti -> {
                    // 获取redis数据
                    RedisPvRtData data = this.redisPvRtDataService.latestRtData(gti.getDno(), yesterday);
                    if (null == data) {
                        log.debug("redis 中数据不存在: {} / {}", gti.getGwno(), gti.getSid());
                        return Mono.just(false);
                    }

                    List<PriceItemPo> priceItems = siteProfitMap.get().get(gti.getSiteId());
                    return this.redisPvRtDataService.gtiDayProfit(gti.getDno(), yesterday, priceItems)
                            .map(profit -> {
                                GtiDailyPo dailyPo = new GtiDailyPo();
                                dailyPo.setDno(gti.getDno())
                                        .setDate(date)
                                        .setGtiId(gti.getId())
                                        .setSiteId(gti.getSiteId())
                                        .setEnable(true)
                                        .setTodayKwh(data.getTodayKwh())
                                        .setTodayProfit(profit)
                                        .setProfitTempId(priceItems.stream().findFirst()
                                                .orElse(new PriceItemPo().setTemplateId(0L))
                                                .getTemplateId())
                                        .setTotalKwh(data.getTotalKwh())
                                        .setTotalHour(data.getTotalHour());
                                log.info("daily: {}", JsonUtils.toJsonString(dailyPo));
                                return this.gtiDailyRwDs.upsetGtiDaily(dailyPo);
                            });
                })
                .count();
    }
}
