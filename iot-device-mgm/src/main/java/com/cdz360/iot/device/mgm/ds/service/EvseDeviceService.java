package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseModuleDetailRoDs;
import com.cdz360.iot.ds.ro.EvseModuleRoDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.EvseModuleDetailRwDs;
import com.cdz360.iot.ds.rw.EvseModuleRwDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.type.EvseDeviceType;
import com.cdz360.iot.model.evse.vo.DeviceVo;
import com.cdz360.iot.model.evse.vo.EvseDeviceVo;
import com.cdz360.iot.model.param.DeviceParam;
import com.cdz360.iot.model.param.FindDeviceParam;
import com.cdz360.iot.model.param.GoodsInfo;
import com.cdz360.iot.model.param.ReplaceDeviceParam;
import com.cdz360.iot.model.site.po.SitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EvseDeviceService {

    private static final Integer DEFAULT_NUMBER_OF_SLOTS = 24; // 默认槽位数

    @Autowired
    private EvseModuleRoDs evseModuleRoDs;

    @Autowired
    private EvseModuleRwDs evseModuleRwDs;

    @Autowired
    private EvseModuleDetailRoDs evseModuleDetailRoDs;

    @Autowired
    private EvseModuleDetailRwDs evseModuleDetailRwDs;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    public Mono<ListResponse<DeviceVo>> getByEvseNo(String evseNo,
        String deviceName,
        Boolean siteAllEvse,
        Long start,
        Long size) {

        return Mono.just(evseNo)
                .map(e -> {
                    if(Boolean.TRUE.equals(siteAllEvse)) {
                        final EvsePo evse = evseRoDs.getEvse(evseNo);
                        if(evse == null) {
                            List<DeviceVo> empty = List.of();
                            return RestUtils.buildListResponse(empty);
                        }
                        if(StringUtils.isNotBlank(evse.getSiteId())) {
                            List<DeviceVo> list = evseModuleRoDs.getBySiteId(evse.getSiteId(),
                                start,
                                size);
                            return RestUtils.buildListResponse(list);
                        }
                    }
                    List<DeviceVo> list = evseModuleRoDs.getByEvseNo(evseNo, deviceName, start,
                        size);
                    Long count = evseModuleRoDs.getByEvseNoCount(evseNo, deviceName);
                    return RestUtils.buildListResponse(list, count);
                }).doOnNext(e -> {
                    if(e != null && CollectionUtils.isNotEmpty(e.getData())) {
                        EvsePo evse = evseRoDs.getEvse(evseNo);
                        if (evse != null && StringUtils.isNotBlank(evse.getSiteId())) {
                            SitePo site = siteRoDs.getSite(evse.getSiteId());
                            if (site != null) {
                                e.getData().forEach(ex -> {
                                    ex.setSiteId(site.getSiteId()).setSiteName(site.getName());
                                });
                            }
                        }
                    }
                });
    }

    public Mono<ObjectResponse<EvseDeviceVo>> getByDeviceNo(FindDeviceParam param) {
        return Mono.just(param)
                .map(e -> RestUtils.buildObjectResponse(evseModuleRoDs.getByDeviceNo(e)));
    }

    @Transactional
    public Mono<BaseResponse> addDevice(DeviceParam param) {
        List<EvseModulePo> modulePoList = evseModuleRoDs.queryByEvseNo(param.getEvseNo());
        IotAssert.isTrue(CollectionUtils.isEmpty(modulePoList), "桩器件数据异常，新增失败");

        List<DeviceParam.Device> deviceList = param.getList();
        for (int i = 0; i < deviceList.size(); i++) {
            DeviceParam.Device device = deviceList.get(i);
            EvseModulePo evseModulePo = new EvseModulePo();
            evseModulePo.setEvseNo(param.getEvseNo());
            evseModulePo.setDeviceName(device.getDeviceName());
            evseModulePo.setModuleType(device.getModuleType());
            evseModulePo.setNumber(device.getNumber());
            evseModuleRwDs.insert(evseModulePo);
            List<DeviceParam.DeviceDetail> deviceDetailList = device.getDetailList();
            List<EvseModuleDetailPo> poList = new ArrayList<EvseModuleDetailPo>();
            for (int j = 0; j < deviceDetailList.size(); j++) {
                DeviceParam.DeviceDetail deviceDetail = deviceDetailList.get(j);
                EvseModuleDetailPo evseModuleDetailPo = new EvseModuleDetailPo();
                evseModuleDetailPo.setModuleId(evseModulePo.getId());
                evseModuleDetailPo.setIdx(deviceDetail.getIdx());
                evseModuleDetailPo.setDeviceNo(deviceDetail.getDeviceNo());
                evseModuleDetailPo.setUpdateTime(deviceDetail.getTime());
                poList.add(evseModuleDetailPo);
            }
            evseModuleDetailRwDs.insert(poList);
        }
        return Mono.just(RestUtils.success());
    }

    @Transactional
    public Mono<BaseResponse> editDevice(DeviceParam param) {
        param.getList().forEach(e -> {
            IotAssert.isTrue(NumberUtils.equals(e.getNumber(), e.getDetailList().size()), "参数错误：槽位数与模块数不匹配");
        });

        List<EvseModulePo> modulePoList = evseModuleRoDs.queryByEvseNo(param.getEvseNo());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(modulePoList), "此桩无器件信息");
        Map<String, EvseModulePo> map = modulePoList.stream().collect(Collectors.toMap(EvseModulePo::getDeviceName, o -> o));

        for (DeviceParam.Device device : param.getList()) {

            EvseModulePo dataDB = map.get(device.getDeviceName());
            long moduleId;
            boolean isDelete = false;

            if (dataDB == null) {
                // 器件不存在则新增
                EvseModulePo evseModulePo = new EvseModulePo();
                evseModulePo.setEvseNo(param.getEvseNo());
                evseModulePo.setDeviceName(device.getDeviceName());
                evseModulePo.setModuleType(device.getModuleType());
                evseModulePo.setNumber(device.getNumber());
                evseModuleRwDs.insert(evseModulePo);
                moduleId = evseModulePo.getId();

            } else {
                moduleId = dataDB.getId();
                isDelete = dataDB.getNumber() > device.getNumber();

                // 器件存在则修改
                if (!device.getModuleType().equals(dataDB.getModuleType())
                        || !device.getNumber().equals(dataDB.getNumber())) {

                    EvseModulePo update = new EvseModulePo();
                    update.setId(dataDB.getId())
                            .setModuleType(device.getModuleType())
                            .setNumber(device.getNumber());
                    evseModuleRwDs.updateById(update);
                }
            }

            for (DeviceParam.DeviceDetail deviceDetail : device.getDetailList()) {

                EvseModuleDetailPo evseModuleDetailPo = new EvseModuleDetailPo();
                evseModuleDetailPo.setId(deviceDetail.getId());
                evseModuleDetailPo.setModuleId(moduleId);
                evseModuleDetailPo.setIdx(deviceDetail.getIdx());
                evseModuleDetailPo.setDeviceNo(deviceDetail.getDeviceNo());
                evseModuleDetailPo.setUpdateTime(deviceDetail.getTime());
                evseModuleDetailRwDs.insertOrUpdate(evseModuleDetailPo);
            }

            // 删除多余的数据
            if (isDelete) {
                evseModuleDetailRwDs.deleteByNumber(dataDB.getId(), device.getNumber());
            }

        }
        return Mono.just(RestUtils.success());
    }

    @Transactional
    public Mono<BaseResponse> replaceDevice(ReplaceDeviceParam param) {
        String ywOrderNo = param.getYwOrderNo();
        GoodsInfo goodsInfo = param.getGoodsInfo();

        return Mono.just(goodsInfo)
                .doOnNext(e -> {
                    IotAssert.isNotNull(ywOrderNo, "运维工单编号不能为空");
                    e.getChargingModuleList().forEach(t -> {
                        IotAssert.isNotNull(t.getIdx(), "序列不能为空");
                    });
                })
                .map(GoodsInfo::getChargingModuleList)
                .filterWhen(e -> Mono.just(CollectionUtils.isNotEmpty(e)))
                .map(t -> {
                    t.forEach(e -> {
                        List<DeviceVo> deviceVoList = evseModuleRoDs.getByEvseNo(e.getEvseNo(), EvseDeviceType.GYZLCDMK.getDesc(), null, null);
                        List<EvseModuleDetailPo> detailPoList = new ArrayList<>();
                        deviceVoList.forEach(volist -> {
                            if (CollectionUtils.isNotEmpty(volist.getDetailList())) {
                                // workaround... 数据库返回的 List<OldDevicePo> 数据结构为 LinkedHashMap , 需要转换为 java 对象 OldDevicePo
                                volist.getDetailList().stream()
                                        .forEach(d1 -> detailPoList.add(JsonUtils.fromJson(JsonUtils.toJsonString(d1), EvseModuleDetailPo.class)));
//                                detailPoList.addAll(volist.getDetailList());
                            }
                        });
                        boolean isExist = CollectionUtils.isNotEmpty(detailPoList);
                        Map<Integer, EvseModuleDetailPo> map = detailPoList.stream().collect(Collectors.toMap(EvseModuleDetailPo::getIdx, o -> o));


                        EvseModuleDetailPo dataDB = map.get(e.getIdx());
                        if (isExist) {
                            log.info("桩存在模块信息");

                            if (dataDB == null) {
                                log.info("查不到槽位信息，则补上该槽位信息。evseNo: {}, idx: {}", e.getEvseNo(), e.getIdx());
                                EvseModuleDetailPo detailPo = new EvseModuleDetailPo();

                                detailPo.setModuleId(deviceVoList.get(0).getId())
                                        .setIdx(e.getIdx())
                                        .setDeviceNo(e.getNewDeviceNo());

                                if (StringUtils.isNotBlank(e.getOldDeviceNo())) {
                                    EvseModuleDetailPo.OldDevicePo oldDevicePo = new EvseModuleDetailPo.OldDevicePo();
                                    oldDevicePo.setNo(e.getOldDeviceNo());
                                    oldDevicePo.setDate(new Date());
                                    oldDevicePo.setYwOrderNo(ywOrderNo);
                                    detailPo.setOldDeviceNo(List.of(oldDevicePo));
                                }

                                evseModuleDetailRwDs.insertOrUpdate(detailPo);

                                if (e.getIdx() > detailPoList.size()) {
                                    EvseModulePo update = new EvseModulePo();
                                    update.setEvseNo(e.getEvseNo())
                                            .setDeviceName(EvseDeviceType.GYZLCDMK.getDesc())
                                            .setNumber(e.getIdx());
                                    evseModuleRwDs.updateModuleTypeAndNumber(update);
                                }

                                // 不往下执行
                                return;

                            } else {
                                log.info("获取到槽位信息. dataDB: {}", JsonUtils.toJsonString(dataDB));
//                                dataDB.setOldDeviceNo(JSON.parseArray(JSON.toJSONString(dataDB.getOldDeviceNo()), EvseModuleDetailPo.OldDevicePo.class)); // 避免java.util.LinkedHashMap cannot be cast to xxx问题

                            }

                        } else {
                            log.info("桩无模块信息，开始生成默认模块信息。evseNo: {}, idx: {}", e.getEvseNo(), e.getIdx());
                            EvseModulePo evseModulePo = new EvseModulePo();
                            evseModulePo.setEvseNo(e.getEvseNo());
                            evseModulePo.setDeviceName(EvseDeviceType.GYZLCDMK.getDesc());
                            evseModulePo.setNumber(DEFAULT_NUMBER_OF_SLOTS);
                            evseModuleRwDs.insert(evseModulePo);

                            int idx = 1;
                            List<EvseModuleDetailPo> poList = new ArrayList<>();
                            while (idx <= DEFAULT_NUMBER_OF_SLOTS) {
                                EvseModuleDetailPo evseModuleDetailPo = new EvseModuleDetailPo();
                                evseModuleDetailPo.setModuleId(evseModulePo.getId());
                                evseModuleDetailPo.setIdx(idx);
                                if (idx == e.getIdx()) {
                                    evseModuleDetailPo.setDeviceNo(e.getNewDeviceNo());

                                    if (StringUtils.isNotBlank(e.getOldDeviceNo())) {
                                        EvseModuleDetailPo.OldDevicePo oldDevicePo = new EvseModuleDetailPo.OldDevicePo();
                                        oldDevicePo.setNo(e.getOldDeviceNo());
                                        oldDevicePo.setDate(new Date());
                                        oldDevicePo.setYwOrderNo(ywOrderNo);
                                        evseModuleDetailPo.setOldDeviceNo(List.of(oldDevicePo));
                                    }

                                }
                                poList.add(evseModuleDetailPo);
                                idx++;
                            }
                            evseModuleDetailRwDs.insert(poList);

                            // 不往下执行
                            return;
                        }


                        EvseModuleDetailPo update = new EvseModuleDetailPo();
                        if (Boolean.TRUE.equals(e.getRollback())) {

                            List<EvseModuleDetailPo.OldDevicePo> temp = dataDB.getOldDeviceNo().stream()
                                    .sorted(Comparator.comparing(EvseModuleDetailPo.OldDevicePo::getDate).reversed()).collect(Collectors.toList());

                            if (StringUtils.isNotBlank(e.getOldDeviceNo()) && StringUtils.isNotBlank(e.getNewDeviceNo())) {
                                // 回退上一次的替换操作

                                if (CollectionUtils.isNotEmpty(temp)) {
                                    update.setId(dataDB.getId())
                                            .setDeviceNo(temp.get(0).getNo())
                                            .setUpdateTime(temp.get(0).getDate())
                                            .setOldDeviceNo(temp.stream().skip(1).collect(Collectors.toList()));
                                } else {
                                    update.setId(dataDB.getId())
                                            .setDeviceNo(null)
                                            .setOldDeviceNo(null)
                                            .setUpdateTime(null);
                                }

                            } else if (StringUtils.isNotBlank(e.getOldDeviceNo()) && StringUtils.isBlank(e.getNewDeviceNo())) {
                                // 回退上一次的拆卸操作

                                if (CollectionUtils.isNotEmpty(temp)) {
                                    update.setId(dataDB.getId())
                                            .setDeviceNo(temp.get(0).getNo())
                                            .setUpdateTime(temp.get(0).getDate())
                                            .setOldDeviceNo(temp.stream().skip(1).collect(Collectors.toList()));
                                } else {
                                    update.setId(dataDB.getId())
                                            .setDeviceNo(null)
                                            .setOldDeviceNo(null)
                                            .setUpdateTime(null);
                                }

                            } else if (StringUtils.isBlank(e.getOldDeviceNo()) && StringUtils.isNotBlank(e.getNewDeviceNo())) {
                                // 回退上一次的添加操作

                                update.setId(dataDB.getId())
                                        .setDeviceNo(null)
                                        .setOldDeviceNo(dataDB.getOldDeviceNo())
                                        .setUpdateTime(null);

                            } else {
                                return;
                            }


                        } else {
                            Optional<EvseModuleDetailPo.OldDevicePo> latestOldDevice = CollectionUtils.isEmpty(dataDB.getOldDeviceNo()) ?
                                    Optional.empty()
                                    : dataDB.getOldDeviceNo().stream().max(Comparator.comparing(EvseModuleDetailPo.OldDevicePo::getDate));


                            if (StringUtils.isNotBlank(e.getOldDeviceNo()) && StringUtils.isBlank(e.getNewDeviceNo())) {

                                if (StringUtils.isBlank(dataDB.getDeviceNo())
                                        && latestOldDevice.isPresent()
                                        && latestOldDevice.get().getNo().equals(e.getOldDeviceNo())) {
                                    // 重复提交
                                    return;
                                }

                            } else if (StringUtils.isNotBlank(e.getOldDeviceNo()) && StringUtils.isNotBlank(e.getNewDeviceNo())) {

                                if (StringUtils.equals(dataDB.getDeviceNo(), e.getNewDeviceNo())
                                        && latestOldDevice.isPresent()
                                        && StringUtils.equals(latestOldDevice.get().getNo(), e.getOldDeviceNo())) {
                                    // 现原材编号、旧原材编号都与库中值一致，无需往下处理
                                    return;
                                }

                            } else if (StringUtils.isBlank(e.getOldDeviceNo()) && StringUtils.isNotBlank(e.getNewDeviceNo())) {

                                if (StringUtils.isNotBlank(dataDB.getDeviceNo())) {
                                    if (e.getNewDeviceNo().equals(dataDB.getDeviceNo())) {
                                        // 重复提交
                                        return;
                                    } else {
                                        throw new DcServiceException("当前槽位已有充电模块，请确认后重新输入");
                                    }
                                }

                            }


                            /**
                             * 此处兼容三种情况
                             * 1.仅拆下旧器件（oldDeviceNo有值，newDeviceNo为空）
                             * 2.替换器件（oldDeviceNo有值，newDeviceNo有值）
                             * 3.仅添加新器件（oldDeviceNo为空，newDeviceNo有值）
                             */
                            {
                                update.setId(dataDB.getId())
                                        .setDeviceNo(e.getNewDeviceNo())
                                        .setUpdateTime(new Date());

                                List<EvseModuleDetailPo.OldDevicePo> temp = new ArrayList<>();
                                if (CollectionUtils.isNotEmpty(dataDB.getOldDeviceNo())) {
                                    temp = dataDB.getOldDeviceNo();
                                }
                                if (StringUtils.isNotBlank(e.getOldDeviceNo())) {
                                    EvseModuleDetailPo.OldDevicePo oldDevicePo = new EvseModuleDetailPo.OldDevicePo();
                                    oldDevicePo.setNo(e.getOldDeviceNo());
                                    oldDevicePo.setDate(dataDB.getUpdateTime() == null ? new Date() : dataDB.getUpdateTime());
                                    oldDevicePo.setYwOrderNo(ywOrderNo);
                                    temp.add(oldDevicePo);
                                }
                                update.setOldDeviceNo(temp);
                            }

                        }

                        evseModuleDetailRwDs.updateById(update);
                    });
                    return RestUtils.success();
                })
                .switchIfEmpty(Mono.just("充电模块为空，直接返回成功").map(t -> {
                    log.info(t);
                    return RestUtils.success();
                }));

    }

}
