package com.cdz360.iot.device.mgm.model.bi.vo;

import com.cdz360.base.model.base.type.EvseBizStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class ListDeviceParam {

    @Schema(description = "场站ID列表", example = "[\"123456789\", \"987654321\"]")
    private List<String> siteIdList;

    @Schema(description = "运营状态")
    @JsonInclude(Include.NON_NULL)
    private EvseBizStatus bizStatus;


}
