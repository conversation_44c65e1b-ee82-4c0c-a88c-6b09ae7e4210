package com.cdz360.iot.device.mgm.rest.ess;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.vo.EssDeviceBiVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.ds.service.ess.EssDeviceDataInfoService;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.vo.SiteEssVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能设备数据信息", description = "储能设备数据信息相关接口合集")
@Slf4j
@RestController
@RequestMapping("/ess/device/di")
public class EssDeviceDataInfoRest {

    @Autowired
    private EssDeviceDataInfoService essDeviceDataInfoService;

    // /device/mgm/gti/countCtrlGti
    @Operation(summary = "储能设备数据统计")
    @GetMapping("/count")
    public Mono<ListResponse<EssDeviceBiVo>> essDeviceDataInfo(
        @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId) {
        log.info("储能设备数据统计: siteId = {}", siteId);
        return essDeviceDataInfoService.essDeviceDataInfo(siteId);
    }


    @Operation(summary = "获取场站EMS数量")
    @PostMapping("/countEms")
    public Mono<ListResponse<SiteEssVo>> essDeviceCount(@RequestBody ListEssParam param) {
        log.info("获取场站EMS数量: param = {}", JsonUtils.toJsonString(param));
        return essDeviceDataInfoService.essDeviceCount(param);
    }
}
