package com.cdz360.iot.device.mgm.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.device.mgm.ds.service.EvseDeviceService;
import com.cdz360.iot.model.evse.vo.DeviceVo;
import com.cdz360.iot.model.evse.vo.EvseDeviceVo;
import com.cdz360.iot.model.param.DeviceParam;
import com.cdz360.iot.model.param.FindDeviceParam;
import com.cdz360.iot.model.param.ReplaceDeviceParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "桩器件相关接口", description = "桩器件")
public class EvseDeviceRest {

    @Autowired
    private EvseDeviceService service;

    /**
     *
     * @param evseNo
     * @param deviceName
     * @param siteAllEvse 是否尝试获取桩的场站下，对应的所有器件信息
     * @param start
     * @param size
     * @return
     */
    @GetMapping("/iot/evseDevice/getByEvseNo")
    public Mono<ListResponse<DeviceVo>> getByEvseNo(@RequestParam(value = "evseNo") String evseNo,
        @RequestParam(value = "deviceName", required = false) String deviceName,
        @RequestParam(value = "siteAllEvse",
            required = false,
            defaultValue = "false") Boolean siteAllEvse,
        @RequestParam(value = "start", defaultValue = "0") Long start,
        @RequestParam(value = "size", defaultValue = "10") Long size) {
        log.info("getByEvseNo evseNo: {}, deviceName: {}, siteAllEvse: {}, start: {}, size: {}",
            evseNo, deviceName, siteAllEvse, start, size);
        return service.getByEvseNo(evseNo, deviceName, siteAllEvse, start, size);
    }

    @PostMapping("/iot/evseDevice/getByDeviceNo")
    public Mono<ObjectResponse<EvseDeviceVo>> getByDeviceNo(@RequestBody FindDeviceParam param) {
        log.info("getByDeviceNo deviceNo: {}, evseNoList.size: {}", param.getDeviceNo(), param.getEvseNoList().size());
        return service.getByDeviceNo(param);
    }

    @PostMapping("/iot/evseDevice/addDevice")
    public Mono<BaseResponse> addDevice(@RequestBody DeviceParam param) {
        log.info("addDevice param: {}", param);
        return service.addDevice(param);
    }

    @PostMapping("/iot/evseDevice/editDevice")
    public Mono<BaseResponse> editDevice(@RequestBody DeviceParam param) {
        log.info("editDevice param: {}", param);
        return service.editDevice(param);
    }

    @Operation(summary = "充电模块更换")
    @PostMapping("/iot/evseDevice/replaceDevice")
    public Mono<BaseResponse> replaceDevice(@RequestBody ReplaceDeviceParam param) {
        log.info("replaceDevice param: {}", param);
        return service.replaceDevice(param);
    }
}
