package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.model.basic.dto.SiteProfitInfo;
import com.cdz360.iot.device.mgm.model.basic.param.ListSiteProfitParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class BizDataCoreFeignHystrix implements FallbackFactory<BizDataCoreFeignClient> {
    @Override
    public BizDataCoreFeignClient apply(Throwable throwable) {
        return new BizDataCoreFeignClient() {
//            @Override
//            public Mono<ObjectResponse<OssStsDto>> getArchiveSts() {
//                log.error("【服务熔断】。Service = {}, 获取文件上传的STS信息",
//                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
//                return Mono.just(RestUtils.serverBusy4ObjectResponse());
//            }

            @Override
            public Mono<ListResponse<SiteProfitInfo>> getSiteProfitList(ListSiteProfitParam param) {
                log.error("【服务熔断】 获取场站光伏收益计算规则. Service = {}, api = getPvProfitList. param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, BizDataCoreFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super BizDataCoreFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
