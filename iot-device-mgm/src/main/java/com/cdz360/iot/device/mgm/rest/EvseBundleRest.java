package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.device.mgm.ds.service.EvseBundleService;
import com.cdz360.iot.device.mgm.model.basic.param.UploadBundleParam;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.dto.EvseBundleDto;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.param.UpgradeStatusParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


/**
 * @ClassName： EvseBundleController
 * @Description: 桩升级信息控制层
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:56
 */
@Slf4j
@RestController
@RequestMapping("/device/mgm/evsebundle")
public class EvseBundleRest {

    @Autowired
    private EvseBundleService evseBundleService;

    /**
     * @Description: 删除升级包
     * @Author: JLei
     * @CreateDate: 16:44 2019/9/16
     */
    @Operation(summary = "桩升级包删除")
    @GetMapping("/deleteEvseBundle")
    public BaseResponse deleteEvseBundle(
        @Parameter(name = "桩升级包主键") @RequestParam("id") Long id) {
        log.info("【桩升级包】删除开始。id = {}", id);
        Assert.notNull(id, "参数id不能为空");
        EvseBundle bundle = evseBundleService.deleteEvseBundle(id);
        evseBundleService.deleteEvseBundleFtpDir(id, bundle.getVendor(), "DELETE");
        log.info("【桩升级包】删除结束。id = {}", id);
        return new BaseResponse();
    }

    @Operation(summary = "桩升级包-修改状态")
    @PostMapping("/changeStatus")
    public BaseResponse changeStatus(@RequestBody UpgradeStatusParam param) {
        log.info("changeStatus. param = {}", param);
        return evseBundleService.changeStatus(param);
    }

    /**
     * @Description: 查询升级包列表
     * @Author: JLei
     * @CreateDate: 16:44 2019/9/16
     */
    @Operation(summary = "桩升级包分页查询")
    @PostMapping("/listEvseBundlePage")
    public ListResponse<EvseBundleDto> listEvseBundlePage(
        @Parameter(name = "桩升级包主键") @RequestBody EvseBundleParam evseBundleParam) {
        log.info("【桩升级包】分页查询开始。evseBundleParam = {}", evseBundleParam.toString());
        ListResponse<EvseBundleDto> listRes = evseBundleService.listEvseBundlePage(evseBundleParam);
        log.info("【桩升级包】分页查询结束。evseBundleIdList = {}",
            CollectionUtils.isNotEmpty(listRes.getData()) ?
                listRes.getData().stream().map(EvseBundle::getId).collect(Collectors.toList())
                : null);
        return listRes;
    }

    @PostMapping(value = "/uploadEvseBundle")
    public Mono<ObjectResponse<Long>> uploadEvseBundle(
        @RequestHeader("Content-Length") long bundleSize,
//            @Parameter(name = "桩升级包文件(ZIP格式)") @RequestPart("file") FilePart file,
        @ModelAttribute UploadBundleParam bundleParam) {
        log.info("升级包上传: size = {}, {}", bundleSize, JsonUtils.toJsonString(bundleParam));
        FilePart file = bundleParam.getFile();

        Assert.notNull(file, "请选中桩升级包文件上传");
//        Assert.isTrue(StringUtils.isNotBlank(token), "token不能为空");

        // 升级包-zip压缩文件名
        String zipName = file.filename();
        String zipPath = getTempZipName(zipName);

        return file.transferTo(Paths.get(zipPath))
            .then(Mono.just(RestUtils.buildObjectResponse(1L)))
            .doOnNext(res -> {
                try {
                    // 桩升级包压缩文件复制到临时目录
//                        file.transferTo(Paths.get(zipPath));
                    switch (bundleParam.getType()) {
                        case MGC_SOFT:
                            evseBundleService.uploadMgcBundle(bundleParam, zipPath, zipName,
                                bundleSize);
                            break;
                        case EVSE_SOFT:
                            // 桩升级包上传
                            evseBundleService.uploadEvseBundle(bundleParam, zipPath, zipName,
                                bundleSize);
                            break;
                        case USER_ESS:
                            evseBundleService.uploadUserEssPg(
                                bundleParam, zipPath, zipName, bundleSize);
                            break;
                        default:
                            log.error("暂不支持包类型: {}", bundleParam.getType());
                            throw new DcArgumentException("暂不支持包类型");
                    }
                } catch (DcServiceException | IllegalArgumentException | ZipException e) {
                    throw new DcServiceException(e.getMessage());
                } catch (Exception e) {
                    log.error("桩升级包上传失败。errMsg = {},e = {}",
                        e.getMessage() == null ? "未知异常" : e.getMessage(), e);
                    throw new DcServiceException("桩升级包上传失败");
                } finally {
                    try {
                        Files.deleteIfExists(Paths.get(zipPath));
                    } catch (IOException e) {
                        log.error("桩升级包临时文件删除失败。errMsg = {},e = {}",
                            e.getMessage() == null ? "未知异常" : e.getMessage(), e);
                    }
                }
            });
    }

    private String getTempZipName(String zipName) {
        String[] pointSplitArray = zipName.split("\\.");
        String tempZipName = UUID.randomUUID().toString()
            .concat("." + pointSplitArray[pointSplitArray.length - 1]);
        return System.getProperty(IotConstants.IOT_SYSTEM_TEMP_DIR_KEY)
            .concat(File.separator + tempZipName);
    }
}