package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.PartsRoDs;
import com.cdz360.iot.ds.ro.PartsTypeRoDs;
import com.cdz360.iot.ds.rw.PartsRwDs;
import com.cdz360.iot.ds.rw.TransOrderRwDs;
import com.cdz360.iot.model.parts.dto.PartsBatchImportDto;
import com.cdz360.iot.model.parts.dto.PartsImportItem;
import com.cdz360.iot.model.parts.param.ListPartsParam;
import com.cdz360.iot.model.parts.param.PartsBrokenParam;
import com.cdz360.iot.model.parts.param.PartsEditParam;
import com.cdz360.iot.model.parts.param.PartsOpBaseParam;
import com.cdz360.iot.model.parts.param.PartsRollbackParam;
import com.cdz360.iot.model.parts.param.PartsTransReviewParam;
import com.cdz360.iot.model.parts.param.PartsYwOrderRefParam;
import com.cdz360.iot.model.parts.param.PartsYwOrderRefParam.AssociationOp;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.po.PartsTypePo;
import com.cdz360.iot.model.parts.po.StoragePo;
import com.cdz360.iot.model.parts.type.PartsLocationStatus;
import com.cdz360.iot.model.parts.type.PartsStatus;
import com.cdz360.iot.model.parts.vo.PartsTransTraceVo;
import com.cdz360.iot.model.parts.vo.PartsVo;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PartsService {

    private static final List<PartsLocationStatus> CAN_RECEIVE_STATUS = List.of(
        PartsLocationStatus.POST, PartsLocationStatus.ROLLBACK);

    @Autowired
    private PartsTypeService partsTypeService;

    @Autowired
    private PartsTypeRoDs partsTypeRoDs;

    @Autowired
    private PartsRoDs partsRoDs;

    @Autowired
    private PartsRwDs partsRwDs;

    @Autowired
    private TransOrderRwDs transOrderRwDs;

    @Autowired
    private StorageService storageService;

    @Autowired
    private TransOrderService transOrderService;

    @Autowired
    private PartsOpLogService partsOpLogService;

    public Mono<Long> partsCount(@NotNull Long uid) {
        IotAssert.isNotNull(uid, "用户ID不能为空");
        return Mono.just(partsRoDs.countParts(
            new ListPartsParam().setUidList(List.of(uid))));
    }

    public Mono<ListResponse<PartsVo>> findParts(ListPartsParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 1000) {
                    param.setSize(999);
                }

                // uid 转成仓库
                List<String> storageCodeList = storageService.findStorageCodeList(
                    param.getDefaultTopStorage(), param.getUidList(), param.getExUidList());
                param.setStorageCodeList(storageCodeList);
            })
            .map(this.partsRoDs::findParts)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.partsRoDs.countParts(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    @Transactional
    public Flux<PartsVo> partsReceive(PartsOpBaseParam param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        // 状态校验
        final HashMap<String, PartsPo> map = new HashMap<>();
        param.getCodeList()
            .forEach(code -> {
                final PartsPo parts = partsRoDs.getByCode(code);
                IotAssert.isNotNull(parts, String.format("物料ID[%s]无效", code));
                IotAssert.isTrue(
                    CAN_RECEIVE_STATUS.contains(parts.getLocationStatus()),
                    String.format("物料[%s]状态不允许操作签收", code));
                map.put(code, parts);
            });

        return Flux.fromIterable(param.getCodeList())
            .map(code -> {
                final PartsPo parts = map.get(code);
                final boolean b = partsRwDs.updatePartsLocationStatus(
                    code, null,
                    parts.getLocationStatus(), PartsLocationStatus.STORAGE);
                if (!b) {
                    log.warn("变更物料流转状态为库存中失败");
                }

                // 调拨单状态调整
                transOrderService.postPartsReceive(code);

                PartsVo result = new PartsVo();
                BeanUtils.copyProperties(parts, result);
                return result;
            });
    }

    public Mono<List<PartsPo>> partsBroken(PartsBrokenParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        // 日志使用
        final List<PartsPo> oldStatus = partsRoDs.findPartsStatus(param.getCodeList());
        IotAssert.isTrue(oldStatus.size() == param.getCodeList().size(),
            "库中数据和入参物料ID列表数量不匹配");
        if (oldStatus.size() != param.getCodeList().size()) {
            log.warn("库中数据和入参物料ID列表数量不匹配: in = {}, src = {}",
                param.getCodeList().size(), oldStatus.size());
            throw new DcArgumentException("库中数据和入参物料ID列表数量不匹配");
        }

        final int i = partsRwDs.updatePartsBroken(param.getCodeList());
        if (i != param.getCodeList().size()) {
            log.warn("物料报废数量与入参物料ID数量不一致");
        }

        return Mono.just(oldStatus);
    }

    public Flux<PartsPo> partsRollback(PartsRollbackParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
//        final String code = param.getCode();
//        IotAssert.isNotBlank(code, "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotBlank(param.getExpressName(), "请填写快递名称");
        IotAssert.isNotBlank(param.getExpressNo(), "请填写快递单号");

        return Flux.fromIterable(param.getCodeList())
            .map(code -> {
                final PartsPo parts = partsRwDs.getByCode(code, true);
                IotAssert.isNotNull(parts, "物料ID无效");
                IotAssert.isTrue(
                    PartsStatus.BROKEN.equals(parts.getStatus()), "当前物料状态不允许该操作");

                // 调拨单
                final StoragePo storage = storageService.getTopStorage();
                transOrderService.partsTransPersonal(
                    param.getOpUid(), param.getExpressNo(), param.getExpressName(),
                    storage, parts);

                final boolean b = partsRwDs.updatePartsLocationStatus(
                    code, storage.getCode(), parts.getLocationStatus(),
                    PartsLocationStatus.ROLLBACK);
                if (!b) {
                    log.warn("变更物料流转状态为库存中失败");
                    throw new DcServiceException("变更物料流转状态为库存中失败");
                }
                partsRwDs.updateParts(new PartsPo().setCode(code)
                    .setRemark(param.getExpressName() + "|" + param.getExpressNo()));

                PartsVo result = new PartsVo();
                BeanUtils.copyProperties(parts, result);
                return result;
            });
    }

    public Mono<List<PartsPo>> partsEdit(PartsEditParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotNull(param.getToStatus(), "目标状态不能为空");
        IotAssert.isTrue(!PartsStatus.DISCARD.equals(param.getToStatus()),
            "当前接口不允许进行报废操作"); // 请使用报废接口

        // 日志使用
        final List<PartsPo> oldStatus = partsRoDs.findPartsStatus(param.getCodeList());
        IotAssert.isTrue(oldStatus.size() == param.getCodeList().size(),
            "库中数据和入参物料ID列表数量不匹配");
        if (oldStatus.size() != param.getCodeList().size()) {
            log.warn("库中数据和入参物料ID列表数量不匹配: in = {}, src = {}",
                param.getCodeList().size(), oldStatus.size());
            throw new DcArgumentException("库中数据和入参物料ID列表数量不匹配");
        }

        // 批量变更所属类型
        if (null != param.getTypeId()) {
            final PartsTypePo partsType = partsTypeRoDs.getById(param.getTypeId());
            IotAssert.isNotNull(partsType, "物料规格无效");
        }

        final int i = partsRwDs.batchUpdateParts(
            param.getCodeList(), param.getTypeId(), param.getToStatus());
        if (i != param.getCodeList().size()) {
            log.warn("物料报废数量与入参物料ID数量不一致");
        }

        return Mono.just(oldStatus);
    }

    @Transactional
    public Flux<PartsVo> partsTransReview(PartsTransReviewParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
        final Boolean agree = param.getAgree();
        IotAssert.isNotNull(agree, "同意/拒绝标识需要指明");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotBlank(param.getApplyName(), "请传入申请人名称");

        return Flux.fromIterable(param.getCodeList())
            .map(code -> {
                final PartsPo parts = partsRwDs.getByCode(code, true);
                IotAssert.isNotNull(parts, "物料ID无效");
                IotAssert.isTrue(
                    PartsLocationStatus.APPLY.equals(parts.getLocationStatus()),
                    "当前物料状态不允许该操作");

                // 变更物料所属库(申请人)
                if (null == parts.getApplyBy()) {
                    log.warn("物料申请人无效: {}", parts);
                    throw new DcArgumentException("物料申请人无效");
                }

                String storageCode = null; // 指向库存编码
                if (agree) { // 调拨单
                    IotAssert.isNotBlank(param.getExpressName(), "请填写快递名称");
                    IotAssert.isNotBlank(param.getExpressNo(), "请填写快递单号");
                    final StoragePo storage = storageService.getPersonalStorage(parts.getApplyBy(),
                        param.getApplyName());
                    storageCode = storage.getCode();
                    transOrderService.partsTransPersonal(
                        param.getOpUid(), param.getExpressNo(), param.getExpressName(),
                        storage, parts);
                }

                final boolean b = partsRwDs.updatePartsTransReview(
                    agree, code, storageCode,
                    agree ? (param.getExpressName() + "|" + param.getExpressNo()) : null);
                if (!b) {
                    log.warn("变更物料流转状态为库存中失败");
                }

                PartsVo result = new PartsVo();
                BeanUtils.copyProperties(parts, result);
                return result;
            });
    }

    @Transactional
    public Flux<PartsVo> partsTransApply(PartsOpBaseParam param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        // 状态校验
        final HashMap<String, PartsPo> map = new HashMap<>();
        param.getCodeList()
            .forEach(code -> {
                final PartsPo parts = partsRwDs.getByCode(code, true);
                IotAssert.isNotNull(parts, String.format("物料ID[%s]无效", code));
                IotAssert.isTrue(
                    PartsLocationStatus.STORAGE.equals(parts.getLocationStatus()),
                    String.format("物料[%s]不在库中，不允许该操作", code));
                IotAssert.isTrue(PartsStatus.USABLE.equals(parts.getStatus()),
                    String.format("物料[%s]不可用，不允许该操作", code));
                map.put(code, parts);
            });

        return Flux.fromIterable(param.getCodeList())
            .map(code -> {
                final boolean b = partsRwDs.updatePartsTransApply(code, param.getOpUid());
                if (!b) {
                    log.warn("变更物料流转状态为库存中失败");
                }

                PartsVo result = new PartsVo();
                BeanUtils.copyProperties(map.get(code), result);
                return result;
            });
    }

    @Transactional
    public Mono<PartsVo> partsTransApplyCancel(PartsOpBaseParam param) {
        final String code = param.getCode();
        IotAssert.isNotBlank(code, "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        final PartsPo parts = partsRwDs.getByCode(code, true);
        IotAssert.isNotNull(parts, "物料ID无效");
        IotAssert.isTrue(
            PartsLocationStatus.APPLY.equals(parts.getLocationStatus()),
            "当前物料不是申领中，不允许该操作");
        IotAssert.isNotNull(parts.getApplyBy(), "物料申请人无效");
        IotAssert.isTrue(parts.getApplyBy().equals(param.getOpUid()), "当前用户不能操作取消");

        final boolean b = partsRwDs.updatePartsTransReview(false, code, null, null);
        if (!b) {
            log.warn("变更物料流转状态为库存中失败");
        }

        PartsVo result = new PartsVo();
        BeanUtils.copyProperties(parts, result);
        return Mono.just(result);
    }

    public Mono<PartsTransTraceVo> partsTransTrace(String code) {
        IotAssert.isNotBlank(code, "物料ID不能为空");

        final PartsPo parts = partsRoDs.getByCode(code);
        IotAssert.isNotNull(parts, "物料ID无效");

        PartsTransTraceVo result = new PartsTransTraceVo();
        BeanUtils.copyProperties(parts, result);
        result.setOpLogVoList(partsOpLogService.getPartsOpLog(code));

        final PartsTypePo partsType = partsTypeRoDs.getById(parts.getTypeId());
        if (null != partsType) {
            result.setTypeCode(partsType.getCode())
                .setTypeName(partsType.getName())
                .setTypeFullModel(partsType.getFullModel());
        }
        return Mono.just(result);
    }

    @Transactional
    public Flux<PartsVo> partsTransport(PartsImportItem param) {
        IotAssert.isNotNull(param.getTypeId(), "物料规格信息请选择");
        IotAssert.isNotNull(param.getApplyId(), "请选择申请人");
        IotAssert.isNotBlank(param.getApplyName(), "请选择申请人");
        IotAssert.isNotBlank(param.getExpressName(), "请填写快递名称");
        IotAssert.isNotBlank(param.getExpressNo(), "请填写快递单号");
        IotAssert.isNotNull(param.getApplyNum(), "请输入申请数量");
        IotAssert.isTrue(param.getApplyNum() > 0, "申请数量需要大于0");

        param.setAvailable(true);
        param.setOpId(param.getApplyId())
            .setOpName(param.getApplyName());

        return Flux.range(0, param.getApplyNum())
            .map(x -> {
                final PartsPo partsPo = this.dto2Po(param);
                partsPo.setLocationStatus(PartsLocationStatus.POST)
                    .setApplyBy(param.getApplyId());
                partsPo.setRemark(param.getExpressName() + "|" + param.getExpressNo());
                return partsPo;
            })
            .map(partsPo -> {
                partsPo.setLocationStatus(PartsLocationStatus.POST)
                    .setApplyBy(param.getApplyId());
                partsPo.setRemark(param.getExpressName() + "|" + param.getExpressNo());
                final boolean b = partsRwDs.insertParts(partsPo);

                // 调拨单
                transOrderService.partsTransPersonal(
                    param.getOpUid(), param.getApplyId(), param.getApplyName(),
                    param.getExpressNo(), param.getExpressName(), partsPo);

                PartsVo result = new PartsVo();
                BeanUtils.copyProperties(partsPo, result);
                return result;
            });
    }

    /**
     * 获取有效的物料ID
     *
     * @return
     */
    private String nextPartsCode() {
        int time = 10;
        String code;
        do {
            code = "P" + RandomStringUtils.randomNumeric(7);
        } while (time-- > 0 && null != partsRoDs.getByCode(code));
        return time == 0 ? code + "X" : code;
    }

    private PartsPo dto2Po(PartsImportItem param) {
        // 仓库不存在则创建
        StoragePo storage = null == param.getOpId() ? storageService.getTopStorage() :
            storageService.getPersonalStorage(param.getOpId(), param.getOpName());

        return new PartsPo()
            .setCode(this.nextPartsCode())
            .setTypeId(param.getTypeId())
            .setStoreCode(storage.getCode())
            .setCreateTime(new Date())
            .setCreateBy(param.getOpUid())
            // 默认状态，若需要可根据需要调整 👇
            .setStatus(Boolean.TRUE.equals(param.getAvailable())
                ? PartsStatus.USABLE : PartsStatus.BROKEN)
            .setLocationStatus(PartsLocationStatus.STORAGE);
    }

    private List<PartsPo> dto2Po(List<PartsImportItem> param) {
        return param.stream().map(this::dto2Po).collect(Collectors.toList());
    }

    public Mono<BaseResponse> partsBatchImport(PartsBatchImportDto dto) {
        IotAssert.isNotNull(dto.getOpUid(), "操作人ID不能为空");

        final List<PartsPo> poList = this.dto2Po(dto.getItems());
        poList.forEach(x -> x.setCreateBy(dto.getOpUid()));
        this.partsBatchInsert(poList);
        return Mono.just(RestUtils.success());
    }

    public void partsBatchInsert(final List<PartsPo> poList) {
        final int size = Math.min(poList.size(), 100);
        final int max = poList.size();
        int fromIndex = 0;
        int toIndex = size;
        while (fromIndex < max) { // 分批插入数据
            final int i = partsRwDs.batchInsert(poList.subList(fromIndex, toIndex));
            fromIndex = toIndex;
            toIndex = Math.min((toIndex + size), max);
        }
    }

    @Transactional
    public Mono<ObjectResponse<PartsYwOrderRefParam>> partsYwOrderRef(PartsYwOrderRefParam param) {
        if (CollectionUtils.isEmpty(param.getAssociationOps())) {
            return Mono.just(new ObjectResponse<>());
        }

        // 数据校验
        IotAssert.isNotBlank(param.getYwOrderNo(), "工单编号不能为空");
        IotAssert.isNotBlank(param.getUserName(), "用户名称不能为空");
        IotAssert.isNotNull(param.getUid(), "用户ID不能为空");
        PartsYwOrderRefParam.checkField(param);

        // 物料状态调整及物料操作日志
        param.getAssociationOps().forEach(x -> {
            switch (x.getOpType()) {
                case REPLACE:
                    this.partsOutStorage(param, x);
                    x.setInNewCode(this.partsInStorage(param, x)); // 入库物料返回最新的物料ID
                    break;
                case UNINSTALL:
                    x.setInNewCode(this.partsInStorage(param, x)); // 入库物料返回最新的物料ID
                    break;
                case INSTALL:
                    this.partsOutStorage(param, x);
                    break;
            }
        });

        return Mono.just(RestUtils.buildObjectResponse(param));
    }
    // 物料入库返回最新的物料ID
    private String partsInStorage(final PartsYwOrderRefParam param, final AssociationOp x) {
        // 物料入库
        final PartsTypePo partsType = (null == x.getTypeId()) ?
            partsTypeService.getOneByFieldsNotExit(x.getTypeName()) :
            partsTypeRoDs.getById(x.getTypeId());
        IotAssert.isNotNull(partsType, "物料规格信息无效");

        final StoragePo storage = storageService.getPersonalStorage(
            param.getUid(), param.getUserName());

        PartsPo parts = new PartsPo().setCode(this.nextPartsCode())
            .setTypeId(partsType.getId())
            .setStoreCode(storage.getCode())
            .setCreateBy(param.getUid())
            .setStatus(PartsStatus.BROKEN)
            .setLocationStatus(PartsLocationStatus.STORAGE);
        final boolean b = partsRwDs.insertParts(parts);
        if (!b) {
            log.warn("新增物料失败: {}", parts);
        }

        // 物料入库日志
        partsOpLogService.partsYwOrder(
            parts.getCode(), param.getUid(), param.getYwOrderNo(), true);
        return parts.getCode();
    }

    private void partsOutStorage(final PartsYwOrderRefParam param, final AssociationOp x) {
        // 物料出库
        PartsPo parts = partsRoDs.getByCode(x.getNewPartsCode());
        IotAssert.isNotNull(parts, "物料ID无效");
        IotAssert.isTrue(
            PartsLocationStatus.STORAGE.equals(parts.getLocationStatus()),
            "物料流转状态不符合该操作");
        // 物料回滚操作
        if (Boolean.TRUE.equals(x.getRollback())) {
            IotAssert.isTrue(
                PartsStatus.USABLE.equals(parts.getStatus()) || PartsStatus.BROKEN.equals(parts.getStatus()),
                "物料状态不符合该操作");
        } else {
            IotAssert.isTrue(
                PartsStatus.USABLE.equals(parts.getStatus()),
                "物料状态不符合该操作");
        }

        final boolean b = partsRwDs.updatePartsLocationStatus(
            parts.getCode(), null,
            PartsLocationStatus.STORAGE, PartsLocationStatus.OUT_STORAGE);
        if (!b) {
            throw new DcArgumentException("物料出库操作失败");
        }
        partsRwDs.updateParts(new PartsPo().setCode(parts.getCode())
            .setRemark(param.getYwOrderNo()));

        // 物料出库日志
        partsOpLogService.partsYwOrder(
            parts.getCode(), param.getUid(), param.getYwOrderNo(), false);
    }
}
