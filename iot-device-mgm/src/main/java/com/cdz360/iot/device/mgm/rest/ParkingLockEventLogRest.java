package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.ParkingLockService;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "地锁事件日志相关接口")
@RestController
@Slf4j
@RequestMapping("/device/mgm/parkingLock/eventLog")
public class ParkingLockEventLogRest {

    @Autowired
    private ParkingLockService parkingLockService;

    @Operation(summary = "获取地锁近20天事件日志")
    @GetMapping(value = "/recent20")
    public Mono<ListResponse<ParkingLockEventLogVo>> eventLogRecent20(
        @Parameter(name = "地锁ID", required = true) @RequestParam Long parkingLockId) {
        log.info("获取地锁近20天事件日志: parkingLockId = {}", parkingLockId);
        return parkingLockService.eventLogRecent20(parkingLockId)
            .map(RestUtils::buildListResponse);
    }
}
