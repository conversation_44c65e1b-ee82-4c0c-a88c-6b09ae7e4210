package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.ds.service.EssService;
import com.cdz360.iot.model.ess.param.AddEssCfgParam;
import com.cdz360.iot.model.ess.param.AddGtiCfgParam;
import com.cdz360.iot.model.ess.param.ListDevCfgParam;
import com.cdz360.iot.model.ess.vo.DevCfgVo;
import com.cdz360.iot.model.ess.vo.EssDetailVo;
import com.cdz360.iot.model.ess.vo.GtiCfgVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;


@Slf4j
@RestController
@Tag(name = "光储配置模板相关接口", description = "光储配置模板")
@RequestMapping(value = "/device/mgm/ess")
public class DeviceRest {
    @Autowired
    private EssService essService;

    @Operation(summary = "新增光储配置模板")
    @PostMapping(value = "/addEss")
    public Mono<ObjectResponse<Long>> addEss(@RequestBody AddEssCfgParam param) {
        log.info("光储模板新增参数,param={}", JsonUtils.toJsonString(param));
        return essService.addEss(param);
    }

    @Operation(summary = "光储配置模板编辑")
    @PostMapping(value = "/editEss")
    public Mono<ObjectResponse<Long>> editEss(@RequestBody AddEssCfgParam param) {
        log.info("光储模板编辑参数,param={}", JsonUtils.toJsonString(param));
        return essService.editEss(param);
    }

    @Operation(summary = "新增逆变器模板")
    @PostMapping(value = "/addGtiCfg")
    public Mono<BaseResponse> addGtiCfg(@RequestBody AddGtiCfgParam param) {
        log.info("光储模板新增参数,param={}", JsonUtils.toJsonString(param));
        return essService.addGtiCfg(param);
    }

    @Operation(summary = "编辑逆变器模板")
    @PostMapping(value = "/editGtiCfg")
    public Mono<BaseResponse> editGtiCfg(@RequestBody AddGtiCfgParam param) {
        log.info("光储模板编辑参数,param={}", JsonUtils.toJsonString(param));
        return essService.editGtiCfg(param);
    }

    @Operation(summary = "模板删除")
    @PostMapping(value = "/delDevCfg")
    public Mono<BaseResponse> delDevCfg(@RequestBody AddGtiCfgParam param) {
        return essService.delDevCfg(param);
    }

    @Operation(summary = "模板列表")
    @PostMapping(value = "/getDevCfgList")
    public Mono<ListResponse<DevCfgVo>> getDevCfgList(@RequestBody ListDevCfgParam param) {
        log.info("光储配置模板列表,param={}", JsonUtils.toJsonString(param));
        return essService.getDevCfgList(param);
    }

    @Operation(summary = "光储ESS配置模板详情")
    @GetMapping(value = "/getDevCfgDetail")
    public Mono<ObjectResponse<EssDetailVo>> getDevCfgDetail(@RequestParam(value = "id") Long id,
                                                             @RequestParam(value = "commIdChain") String commIdChain) {
        log.info("逆变器详情,id={},commIdChain={}", id, commIdChain);
        return essService.getDevCfgDetail(id, commIdChain);
    }

    @Operation(summary = "逆变器模板详情")
    @GetMapping(value = "/getGtiCfgDetail")
    public Mono<ObjectResponse<GtiCfgVo>> getGtiCfgDetail(@RequestParam(value = "id") Long id,
                                                          @RequestParam(value = "commIdChain") String commIdChain) {
        log.info("逆变器详情,id={},commIdChain={}", id, commIdChain);
        return essService.getGtiCfgDetail(id, commIdChain);
    }
}
