package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.es.vo.EmuRtInfo;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisEmuRoService;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.feign.BizDataCoreFeignClient;
import com.cdz360.iot.device.mgm.model.basic.dto.SiteProfitInfo;
import com.cdz360.iot.device.mgm.model.basic.param.ListSiteProfitParam;
import com.cdz360.iot.device.mgm.model.basic.po.PriceItemPo;
import com.cdz360.iot.ds.ro.DevCfgRoDs;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.BmsRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssBatteryBundleRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssBatteryPackRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipAlarmLangRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipLangRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ds.rw.EssDailyRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.ess.dto.BatteryCluster;
import com.cdz360.iot.model.ess.dto.BatteryPack;
import com.cdz360.iot.model.ess.dto.BatteryStack;
import com.cdz360.iot.model.ess.dto.Ems;
import com.cdz360.iot.model.ess.dto.EssDto;
import com.cdz360.iot.model.ess.dto.EssEquipTinyDto;
import com.cdz360.iot.model.ess.dto.EssTinyDto;
import com.cdz360.iot.model.ess.dto.Pcs;
import com.cdz360.iot.model.ess.dto.RedisEquipRtData;
import com.cdz360.iot.model.ess.param.BmsRelevantParam;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import com.cdz360.iot.model.ess.param.ListBmsParam;
import com.cdz360.iot.model.ess.param.ListEssBatteryClusterParam;
import com.cdz360.iot.model.ess.param.ListEssBatteryPackParam;
import com.cdz360.iot.model.ess.param.ListEssEquipLangParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.param.ListPcsParam;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.BmsRelevantVo;
import com.cdz360.iot.model.ess.vo.EquipPowerBiVo;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryClusterVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryStackVo;
import com.cdz360.iot.model.ess.vo.EssEquipPCSVo;
import com.cdz360.iot.model.ess.vo.EssEquipVo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.TotalEssDataBi;
import com.cdz360.iot.model.ess.vo.TotalEssRtDataBi;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import com.cdz360.iot.model.pcs.vo.PcsVo;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.pv.type.BatteryModel;
import com.cdz360.iot.model.pv.type.EssModel;
import com.cdz360.iot.model.pv.type.PcsModel;
import com.cdz360.iot.model.pv.vo.EssDataInTimeVo;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssBizService {

    @Autowired
    private DevCfgRoDs devCfgRoDs;

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private BmsRoDs bmsRoDs;


    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private EssDailyRwDs essDailyRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private RedisEmuRoService redisEmuRoService;

    @Autowired
    private EssBatteryPackRoDs essBatteryPackRoDs;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    @Autowired
    private RedisEssEquipRtDataService essEquipRtDataService;

    @Autowired
    private EssRtDataService essRtDataService;

    @Autowired
    private EssBatteryBundleRoDs batteryBundleRoDs;
    @Autowired
    private EssEquipLangRoDs essEquipLangRoDs;

    @Autowired
    private EssEquipAlarmLangRoDs essEquipAlarmLangRoDs;

    private static EssEquipPCSVo EssEquipVoToEssEquipPCSVo(EssEquipVo essEquipVo) {
        EssEquipPCSVo essEquipPCSVo = new EssEquipPCSVo();
        BeanUtils.copyProperties(essEquipVo, essEquipPCSVo);
        return essEquipPCSVo;
    }

    private static EssEquipBatteryPackVo EssEquipVoToEssEquipBatteryPackVo(EssEquipVo essEquipVo) {
        EssEquipBatteryPackVo essEquipBatteryPackVo = new EssEquipBatteryPackVo();
        BeanUtils.copyProperties(essEquipVo, essEquipBatteryPackVo);
        return essEquipBatteryPackVo;
    }

    private static EssEquipBatteryStackVo EssEquipVoToEssEquipBatteryStackVo(
        EssEquipVo essEquipVo) {
        EssEquipBatteryStackVo essEquipBatteryStackVo = new EssEquipBatteryStackVo();
        BeanUtils.copyProperties(essEquipVo, essEquipBatteryStackVo);
        return essEquipBatteryStackVo;
    }

    public Mono<Optional<EssPo>> getById(Long id) {
        return Mono.just(id)
            .map(i -> Optional.ofNullable(essRoDs.getById(i)));
    }

    public Mono<Optional<List<EssDto>>> getByGwno(ListCtrlParam param) {
        return Mono.just(param)
            .map(i -> Optional.ofNullable(essRoDs.getByGwno(param)));
    }

    @Transactional(readOnly = true)
    public List<EssTinyDto> getEssTinyList(ListEssParam param) {

        return essRoDs.getEssTinyList(param);
    }

    /**
     * 获取储能设备的下属子设备(缩略信息)
     */
    @Transactional(readOnly = true)
    public List<EssEquipTinyDto> getEssSubEquipTinyList(ListEssEquipParam paramIn) {
        log.info("获取储能设备的下属子设备(缩略信息) paramIn = {}", paramIn);
        return essEquipRoDs.getEssEquipTinyList(paramIn);
    }

    private EssTinyDto toEssTinyDto(EssVo essIn) {
        EssTinyDto dto = new EssTinyDto();
        dto.setDno(essIn.getDno())
            .setName(essIn.getName())
            .setSiteId(essIn.getSiteId())
            .setStatus(EquipStatus.valueOf(essIn.getStatus()));
        return dto;
    }

    /**
     * 光出ESS列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssVo>> findEssList(ListEssParam param) {
        return Mono.just(param)
            .map(essRoDs::findEssList)
            .map(list -> {
                Long total = 0L;
                if (param.getTotal() != null && param.getTotal()) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        total = essRoDs.getEssCount(param);
                        List<String> dnoList = list.stream().map(EssVo::getDno)
                            .collect(Collectors.toList());
                        List<EssEquipVo> equipList = essEquipRoDs.getEssEquipListByDno(dnoList);
                        if (CollectionUtils.isNotEmpty(equipList)) {
                            Map<String, List<EssEquipVo>> map = equipList.stream()
                                .collect(Collectors.groupingBy(EssEquipVo::getEssDno));
                            list.stream().forEach(e -> {
                                e.setEssEquipVoList(map.get(e.getDno()));
                            });
                        }
                    }
                    return RestUtils.buildListResponse(list, total);
                }
                return RestUtils.buildListResponse(list);
            });

    }

    /**
     * 获取光储Ess下挂载的所有设备
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipPo>> findEquipList(ListCtrlParam param) {
        return Mono.just(param)
            .map(essEquipRoDs::findEquipList)
            .map(RestUtils::buildListResponse);
    }

    /**
     * 获取光储Ess下挂载的所有设备
     *
     * @param siteId
     * @return
     */
    public Mono<ListResponse<EssEquipPo>> findEquipListBySiteId(String siteId,
        EssEquipType equipType) {
        return Mono.just(siteId)
            .map(e -> essEquipRoDs.findEquipListBySiteId(e, equipType))
            .map(RestUtils::buildListResponse);
    }

    /**
     * 光储ESS PCS列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipPCSVo>> findEquipPCSList(ListEssEquipParam param) {
        param.setEquipTypes(List.of(EssEquipType.PCS));
        return Mono.just(param)
            .map(essEquipRoDs::findEssEquipList)
            .map(list -> {
                List<EssEquipPCSVo> essEquipPCSVoList = list.stream()
                    .map(EssBizService::EssEquipVoToEssEquipPCSVo).collect(Collectors.toList());
                Long total = 0L;
                if (param.getTotal() != null && param.getTotal()) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        total = essEquipRoDs.getEssEquipCount(param);
                    }
                    essEquipPCSVoList.forEach(e -> {
                        String str = essEquipRtDataService.latestRtData(e.getEssDno(),
                            e.getEquipId());
                        if (StringUtils.isNotBlank(str)) {
                            RedisEquipRtData<Pcs> pcsData = JsonUtils.fromJson(str,
                                new TypeReference<>() {
                                });
                            if (pcsData != null && pcsData.getData() != null) {
                                Pcs pcs = pcsData.getData();
                                e.setCabinetNo(pcs.getSystemSn());
                                e.setDcacSwitchState(pcs.getDcacSwitchState());
                                e.setDcacOnGridState(pcs.getDcacOnGridState());
                                e.setModuleTemp(pcs.getModuleTemperature());
                                e.setEnvironmentTemp(pcs.getAmbientTemperature());
                                e.setAcVoltageAB(pcs.getAcLineVoltageAb());
                                e.setAcVoltageBC(pcs.getAcLineVoltageBc());
                                e.setAcVoltageCA(pcs.getAcLineVoltageCa());
                                e.setAcCurrentA(pcs.getAcCurrentA());
                                e.setAcCurrentB(pcs.getAcCurrentB());
                                e.setAcCurrentC(pcs.getAcCurrentA());
                                e.setAcPowerFactor(pcs.getAcPfTotal());
                                e.setAcActivePower(pcs.getAcActivePowerTotal());
                                e.setAcReactivePower(pcs.getAcReactivePowerTotal());
                                e.setAcApparentPower(pcs.getAcApparentPowerTotal());
                                e.setAcTotalChargeKwh(pcs.getAccumulativeChargedEnergy());
                                e.setAcTotalDisChargeKwh(pcs.getAccumulativeDischargedEnergy());
                                e.setAcTodayChargeKwh(pcs.getAcChargingElecToday());
                                e.setAcTodayDisChargeKwh(pcs.getAcDischargeElecToday());
                                e.setAcFrequency(pcs.getAcFrequency());
                                e.setDcVoltage(pcs.getDcVoltage());
                                e.setDcCurrent(pcs.getDcCurrent());
                                e.setControlModel(pcs.getDcControlMode());
                                e.setDcPower(pcs.getDcPower());
                                e.setDcTotalChargeKwh(pcs.getDcAccumulativeChargedEnergy());
                                e.setDcTotalDisChargeKwh(pcs.getDcAccumulativeDischargedEnergy());
                            }
                        }
                    });

                    return RestUtils.buildListResponse(essEquipPCSVoList, total);
                }
                return RestUtils.buildListResponse(essEquipPCSVoList);
            });
    }

    /**
     * 光储ESS 电池堆列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryStackVo>> findEquipBatteryStackList(
        ListEssEquipParam param) {
        param.setEquipTypes(List.of(EssEquipType.BATTERY_STACK));
        return Mono.just(param)
            .map(essEquipRoDs::findEssEquipList)
            .map(list -> {
                List<EssEquipBatteryStackVo> essEquipBatteryStackVoList = list.stream()
                    .map(EssBizService::EssEquipVoToEssEquipBatteryStackVo)
                    .collect(Collectors.toList());
                Long total = 0L;
                if (param.getTotal() != null && param.getTotal()) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        total = essEquipRoDs.getEssEquipCount(param);
                    }
                    essEquipBatteryStackVoList.forEach(e -> {
                        String str = essEquipRtDataService.latestRtData(e.getEssDno(),
                            e.getEquipId());
                        if (StringUtils.isNotBlank(str)) {
                            RedisEquipRtData<BatteryStack> batteryStackData = JsonUtils.fromJson(
                                str, new TypeReference<>() {
                                });
                            if (batteryStackData != null && batteryStackData.getData() != null) {
                                BatteryStack batteryStack = batteryStackData.getData();

                                e.setVoltage(batteryStack.getBatteryVoltageTotal());
                                e.setCurrent(batteryStack.getBatteryCurrentTotal());
                                e.setSoc(batteryStack.getSoc());
                                e.setSoh(batteryStack.getSoh());
                                e.setMinVoltage(batteryStack.getMinMonomerVoltage());
                                e.setMinVoltageClusterNo(
                                    batteryStack.getMinMonomerVoltageClusterNo());
                                e.setMinVoltageBatteryNo(
                                    batteryStack.getMinMonomerVoltageBatteryNo());
                                e.setMinVoltageCellNo(batteryStack.getMinMonomerVoltageCellNo());
                                e.setMaxVoltage(batteryStack.getMaxMonomerVoltage());
                                e.setMaxVoltageClusterNo(
                                    batteryStack.getMaxMonomerVoltageClusterNo());
                                e.setMaxVoltageBatteryNo(
                                    batteryStack.getMaxMonomerVoltageBatteryNo());
                                e.setMaxVoltageCellNo(batteryStack.getMaxMonomerVoltageCellNo());
                                e.setMinTemp(batteryStack.getMinBatteryTemperature());
                                e.setMinTempClusterNo(batteryStack.getMinTemperatureClusterNo());
                                e.setMinTempBatteryNo(batteryStack.getMinTemperatureBatteryNo());
                                e.setMinTempCellNo(batteryStack.getMinTemperatureCellNo());
                                e.setMaxTemp(batteryStack.getMaxBatteryTemperature());
                                e.setMaxTempClusterNo(batteryStack.getMaxTemperatureClusterNo());
                                e.setMaxTempBatteryNo(batteryStack.getMaxTemperatureBatteryNo());
                                e.setMaxTempCellNo(batteryStack.getMaxTemperatureCellNo());
                                e.setAllowChargeAndDisCharge(batteryStack.getAllowsInOutMarker());
                                e.setInsulationImpedance(batteryStack.getInsulatedResistance());
                                e.setClusterNumber(batteryStack.getSysParallelClustersNum());
                                e.setWorkClusterNumber(batteryStack.getWorkClusterNum());
                                e.setAllowMaxChargeCurrent(batteryStack.getMaxInCurrentAllowed());
                                e.setAllowMaxDisChargeCurrent(
                                    batteryStack.getMaxOutCurrentAllowed());
                                e.setClusterLMUNumber(batteryStack.getClustersLmuNum());
                                e.setSingleBatteryCapacity(batteryStack.getOneBatteryCapacity());
                            }
                        }
                    });
                    return RestUtils.buildListResponse(essEquipBatteryStackVoList, total);
                }
                return RestUtils.buildListResponse(essEquipBatteryStackVoList);
            });
    }

    public List<BmsPo> getBmsList(ListBmsParam paramIn) {
        if (CollectionUtils.isEmpty(paramIn.getSiteIdList())
            && CollectionUtils.isEmpty(paramIn.getEssDnoList())
            && CollectionUtils.isEmpty(paramIn.getBmsDnoList())) {
            log.warn("参数错误, siteIdList/essDnoList/bmsDnoList 不能全为空");
            return List.of();
        }
        return bmsRoDs.getBmsList(paramIn);
    }

    public List<PcsVo> getPcsList(ListPcsParam paramIn) {
        if (CollectionUtils.isEmpty(paramIn.getSiteIdList())
            && CollectionUtils.isEmpty(paramIn.getEssDnoList())
            && CollectionUtils.isEmpty(paramIn.getPcsDnoList())) {
            log.warn("参数错误, siteIdList/essDnoList/pcsDnoList 不能全为空");
            return List.of();
        }
        return pcsRoDs.getPcsList(paramIn);
    }

    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackSimpleVoList(
        @RequestBody ListEssBatteryPackParam param) {
        IotAssert.isTrue(
            StringUtils.isNotBlank(param.getEssDno()),
            "缺失入参");

        List<EssEquipBatteryPackSimpleVo> list = essBatteryPackRoDs.findBatteryPackSimpleVoList(
            param.getEssDno(),
            param.getStackEquipId(),
            param.getClusterEquipId(),
            param.getStart(),
            param.getSize());

        return Mono.just(list)
            .map(RestUtils::buildListResponse);
    }

    /**
     * 光储ESS 电池组列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryPackVo>> findEquipBatteryPackList(
        ListEssBatteryPackParam param) {
        return Mono.just(param)
            .map(essBatteryPackRoDs::findBatteryPack)
            .map(data -> data.stream().peek(equip -> {
                RedisEquipRtData<BatteryPack> cache = essEquipRtDataService.latestBatteryPackRtData(
                    equip.getEssDno(), equip.getClusterEquipId(), equip.getLmuSn(),
                    new TypeReference<>() {
                    });
                if (null != cache && cache.getData() != null) {
                    BatteryPack pack = cache.getData();
                    equip.setTempPoint1(pack.getTempPoint1())
                        .setTempPoint2(pack.getTempPoint2())
                        .setTempPoint3(pack.getTempPoint3())
                        .setTempPoint4(pack.getTempPoint4())
                        .setPositiveColumnTempSamplingPoint(
                            pack.getPositiveColumnTempSamplingPoint())
                        .setNegativeColumnTempSamplingPoint(
                            pack.getNegativeColumnTempSamplingPoint())
                        .setCellVoltage1(pack.getCellVoltage1())
                        .setCellVoltage2(pack.getCellVoltage2())
                        .setCellVoltage3(pack.getCellVoltage3())
                        .setCellVoltage4(pack.getCellVoltage4())
                        .setCellVoltage5(pack.getCellVoltage5())
                        .setCellVoltage6(pack.getCellVoltage6())
                        .setCellVoltage7(pack.getCellVoltage7())
                        .setCellVoltage8(pack.getCellVoltage8())
                        .setCellVoltage9(pack.getCellVoltage9())
                        .setCellVoltage10(pack.getCellVoltage10())
                        .setCellVoltage11(pack.getCellVoltage11())
                        .setCellVoltage12(pack.getCellVoltage12())
                        .setCellVoltage13(pack.getCellVoltage13())
                        .setCellVoltage14(pack.getCellVoltage14())
                        .setCellVoltage15(pack.getCellVoltage15())
                        .setCellVoltage16(pack.getCellVoltage16())
                        .setInternalResistance1(pack.getInternalResistance1())
                        .setInternalResistance2(pack.getInternalResistance2())
                        .setInternalResistance3(pack.getInternalResistance3())
                        .setInternalResistance4(pack.getInternalResistance4())
                        .setInternalResistance5(pack.getInternalResistance5())
                        .setInternalResistance6(pack.getInternalResistance6())
                        .setInternalResistance7(pack.getInternalResistance7())
                        .setInternalResistance8(pack.getInternalResistance8())
                        .setInternalResistance9(pack.getInternalResistance9())
                        .setInternalResistance10(pack.getInternalResistance10())
                        .setInternalResistance11(pack.getInternalResistance11())
                        .setInternalResistance12(pack.getInternalResistance12())
                        .setInternalResistance13(pack.getInternalResistance13())
                        .setInternalResistance14(pack.getInternalResistance14())
                        .setInternalResistance15(pack.getInternalResistance15())
                        .setInternalResistance16(pack.getInternalResistance16());
                }
            }).collect(Collectors.toList()))
            .map(RestUtils::buildListResponse)
            .map(list -> {
                if (null != param.getTotal() && param.getTotal()) {
                    if (null == param.getSize()) {
                        param.setSize(10);
                    }
                    list.setTotal(essBatteryPackRoDs.countBatteryPack(param));
                }
                return list;
            });
    }

    public Mono<EssDataInTimeVo> essInfoInTime(String dno) {
        return Mono.just(dno)
            .map(siteId -> {
                EssPo essPo = essRoDs.getByDno(dno);
                if (essPo == null) {
                    throw new DcArgumentException("无法找到设备信息");
                }
                EssDataInTimeVo res = new EssDataInTimeVo();
                res.setDno(dno)
                    .setType("储能ESS")
                    .setName(essPo.getName())
                    .setVendor(essPo.getVendor());

                String emsJson = essEquipRtDataService.latestRtData(dno,
                    (long) EssEquipType.EMS.getCode());
                if (StringUtils.isNotBlank(emsJson)) {
                    try {
                        RedisEquipRtData<Ems> emsRedisEquipRtData = JsonUtils.fromJson(emsJson,
                            new TypeReference<RedisEquipRtData<Ems>>() {
                            });
                        if (emsRedisEquipRtData != null && emsRedisEquipRtData.getData() != null) {
                            Ems ems = emsRedisEquipRtData.getData();
                            res.setSystemSn(ems.getSystemSn())
                                .setCommunicationProtocolVersion(
                                    ems.getCommunicationProtocolVersion())
                                .setHardwareVersionNum(ems.getHardwareVersionNum())
                                .setSoftwareVersionNum(ems.getSoftwareVersionNum())
                                .setEmsModel(EssModel.codeOf(ems.getEmsModel()))
                                .setPcsModel(PcsModel.codeOf(ems.getPcsModel()))
                                .setBatteryModel(BatteryModel.codeOf(ems.getBatteryModel()))
                                .setInstalledBatteryCapacity(ems.getInstalledBatteryCapacity())
                                .setInternallyConsumesElec(ems.getInternallyConsumesElec())
                                .setInternallyConsumesPower(ems.getInternallyConsumesPower())
                                .setSystemModel(ems.getSystemModel());

                            var ecs = Ems.parseCommunicationStatus(ems.getEmsCommunicationStatus());
                            if (CollectionUtils.isNotEmpty(ecs)) {
                                res.setEmsOnlineNum(
                                    ecs.stream().filter(e -> Boolean.TRUE == e).count());
                            }
                            var pcs = Ems.parseCommunicationStatus(ems.getPcsCommunicationStatus());
                            if (CollectionUtils.isNotEmpty(pcs)) {
                                res.setPcsOnlineNum(
                                    pcs.stream().filter(e -> Boolean.TRUE == e).count());
                            }
                            var bcs = Ems.parseCommunicationStatus(ems.getBmsCommunicationStatus());
                            if (CollectionUtils.isNotEmpty(bcs)) {
                                res.setBmsOnlineNum(
                                    bcs.stream().filter(e -> Boolean.TRUE == e).count());
                            }
                        }
                    } catch (Exception ex) {
                        log.warn("转换为EMS失败 emsJson: {}, msg: {}", emsJson, ex.getMessage(),
                            ex);
                    }
                }
                return res;
            });
    }

    public Mono<Long> syncRtData2Sql(LocalDate destDate) {
        final LocalDate yesterday = null == destDate ? LocalDate.now().plusDays(-1) : destDate;
        FetchUserEssParam fetchUserEssParam = new FetchUserEssParam();
        fetchUserEssParam.setSize(100);
        // 户用储能ESS
        List<UserEssVo> userEssList = this.essRoDs.userEssList(fetchUserEssParam);
        if (CollectionUtils.isNotEmpty(userEssList)) { // 户储处理逻辑调整
            this.dealWithUserEss(yesterday, userEssList);
        }

        Set<String> collect = userEssList.stream().map(UserEssVo::getDno)
            .collect(Collectors.toSet());
        List<EssVo> essList = this.essRoDs.findEssList(new ListEssParam());

        List<String> siteIdList = essList.stream()
            .filter(x -> !collect.contains(x.getDno()))
            .map(EssVo::getSiteId).distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(siteIdList)) {
            return Mono.just(0L);
        }

//        final Date date = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        AtomicReference<Map<String, List<PriceItemPo>>> siteInMap = new AtomicReference<>();
        AtomicReference<Map<String, List<PriceItemPo>>> siteOutMap = new AtomicReference<>();
        ListSiteProfitParam profitParam = new ListSiteProfitParam();
        return bizDataCoreFeignClient.getSiteProfitList(profitParam.setSiteIdList(siteIdList))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .doOnNext(profitList -> {
                siteInMap.set(profitList.stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getInPriceItemPoList()))
                    .collect(Collectors
                        .toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getInPriceItemPoList)));
                siteOutMap.set(profitList.stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getOutPriceItemPoList()))
                    .collect(Collectors
                        .toMap(SiteProfitInfo::getSiteId, SiteProfitInfo::getOutPriceItemPoList)));
            })
            .map(i -> essList)
            .flatMapMany(Flux::fromIterable)
            .flatMap(ess -> {
                EmuRtInfo emuRtInfo = redisEmuRoService.getEmuRtInfo(ess.getDno());
                if (null != emuRtInfo) {
                    return Mono.just(false); // 工商储不需要处理
//                    List<String> pcsDnos = emuRtInfo.getPcsDnos();
//                    if (CollectionUtils.isNotEmpty(pcsDnos)) {
//                        return Mono.zip(
//                                this.essEquipRtDataService.gwAllPCSDayBi(pcsDnos, yesterday),
//                                this.essEquipRtDataService.gwAllPCSTotalDetail2(pcsDnos, yesterday))
//                            .map(tuple -> {
//                                EssDataBi data = tuple.getT1();
//                                EssDataBi total = tuple.getT2();
//
//                                EssDailyPo dailyPo = new EssDailyPo();
//                                dailyPo.setDno(ess.getDno())
//                                    .setEssId(ess.getId())
//                                    .setDate(yesterday)
//                                    .setSiteId(ess.getSiteId())
//                                    .setEnable(true)
//
//                                    .setTotalInKwh(total.getInKwh())
//                                    .setTotalAcInKwh(total.getInKwh())
//                                    .setTotalOutKwh(total.getOutKwh())
//                                    .setTotalAcOutKwh(total.getOutKwh())
//
//                                    .setTodayAcInKwh(data.getInKwh())
//                                    .setTodayInKwh(data.getInKwh())
//                                    .setTodayOutKwh(data.getOutKwh())
//                                    .setTodayAcOutKwh(data.getOutKwh())
//
//                                    .setTodayProfit(data.getProfit())
//                                    .setTodayOutIncome(data.getOutIncome())
//                                    .setTodayInExpend(data.getInExpend());
//
//                                log.info("ess daily: {}", JsonUtils.toJsonString(dailyPo));
//                                return this.essDailyRwDs.upsetEssDaily(dailyPo);
//                            });
//                    } else {
//                        return Mono.just(false);
//                    }
                } else {
                    // 统计所有设备列表的数据(PCS, DCAC, DCDC)
                    ListEssEquipParam essEquipParam = new ListEssEquipParam()
                        .setEssDno(ess.getDno())
                        .setGwno(ess.getGwno())
                        .setEquipTypes(List.of(EssEquipType.PCS));
                    List<EssEquipVo> equipList = essEquipRoDs.findEssEquipList(essEquipParam);

                    List<PriceItemPo> inPriceItems = siteInMap.get().get(ess.getSiteId());
                    List<PriceItemPo> outPriceItems = siteOutMap.get().get(ess.getSiteId());
                    return this.essEquipRtDataService.gwAllPCSDayBi(
                            equipList, yesterday, inPriceItems, outPriceItems)
                        .zipWith(this.essEquipRtDataService.gwAllPCSTotalDetail(
                            equipList, yesterday))
                        .map(tuple -> {
                            EssDataBi data = tuple.getT1();
                            EssDataBi total = tuple.getT2();

                            EssDailyPo dailyPo = new EssDailyPo();
                            dailyPo.setDno(ess.getDno())
                                .setEssId(ess.getId())
                                .setDate(yesterday)
                                .setSiteId(ess.getSiteId())
                                .setEnable(true)

                                .setTotalInKwh(total.getInKwh())
                                .setTotalAcInKwh(total.getInKwh())
                                .setTotalOutKwh(total.getOutKwh())
                                .setTotalAcOutKwh(total.getOutKwh())

                                .setTodayAcInKwh(data.getInKwh())
                                .setTodayInKwh(data.getInKwh())
                                .setTodayOutKwh(data.getOutKwh())
                                .setTodayAcOutKwh(data.getOutKwh())

                                .setTodayProfit(data.getProfit())
                                .setTodayOutIncome(data.getOutIncome())
                                .setTodayInExpend(data.getInExpend());

                            if (null != inPriceItems) {
                                dailyPo.setInPriceId(inPriceItems.stream().findFirst()
                                    .orElse(new PriceItemPo().setTemplateId(0L))
                                    .getTemplateId());
                            }

                            if (null != outPriceItems) {
                                dailyPo.setOutPriceId(outPriceItems.stream().findFirst()
                                    .orElse(new PriceItemPo().setTemplateId(0L))
                                    .getTemplateId());
                            }

                            log.info("ess daily: {}", JsonUtils.toJsonString(dailyPo));
                            return this.essDailyRwDs.upsetEssDaily(dailyPo);
                        });
                }
            })
            .count();
    }

    public Mono<Boolean> upsetEssDaily(EssDailyPo dailyPo) {
        return Mono.just(this.essDailyRwDs.upsetEssDaily(dailyPo));
    }

    // 户用储能暂时不考虑收益计算
    private void dealWithUserEss(final LocalDate yesterday, List<UserEssVo> userEssList) {
        Mono.just(userEssList)
            .flatMapMany(Flux::fromIterable)
            .flatMap(ess -> this.essEquipRtDataService.userEssRtDataDayBi(ess.getDno(), yesterday)
                .map(total -> {
                    EssDailyPo dailyPo = new EssDailyPo();
                    dailyPo.setDno(ess.getDno())
                        .setEssId(ess.getId())
                        .setDate(yesterday)
                        .setSiteId(ess.getSiteId())
                        .setEnable(true)

                        .setTotalInKwh(total.getInKwh())
                        .setTotalAcInKwh(total.getInKwh())
                        .setTotalOutKwh(total.getOutKwh())
                        .setTotalAcOutKwh(total.getOutKwh());

                    log.info("ess daily: {}", JsonUtils.toJsonString(dailyPo));
                    return this.essDailyRwDs.upsetEssDaily(dailyPo);
                }))
            .count()
            .subscribe(i -> log.info("户储处理数据: i = {}", i));
    }

    public Mono<TotalEssDataBi> essTotalBi(DayKwhParam param) {
//        if (StringUtils.isBlank(param.getCommIdChain()) && CollectionUtils.isEmpty(param.getSiteIdList())) {
//            throw new DcArgumentException("需要提供其中一个条件，商户链或场站列表");
//        }

        ListEssParam essParam = new ListEssParam()
            .setCommIdChain(param.getCommIdChain())
            .setSiteIdList(param.getSiteIdList());
        return Mono.zip(Mono.just(essRoDs.getEssCount(essParam)),
                essRtDataService.rtDataTotal(param))
            .map(tuple -> {
                Long essCnt = tuple.getT1();
                TotalEssRtDataBi rtData = tuple.getT2();

                TotalEssDataBi result = new TotalEssDataBi();
                result.setEssCnt(essCnt)
                    .setToday(rtData.getToday())
                    .setYesterday(rtData.getYesterday())
                    .setMonth(rtData.getMonth())
                    .setTotal(rtData.getTotal());
                return result;
            });
    }

    public Mono<ListResponse<EssEquipBatteryClusterVo>> findEquipBatteryClusterList(
        ListEssBatteryClusterParam param) {
        return Mono.just(param)
            .map(essEquipRoDs::findBatteryCluster)
            .map(data -> data.stream().peek(equip -> {
                RedisEquipRtData<BatteryCluster> cache = essEquipRtDataService.latestRtData(
                    equip.getEssDno(), equip.getEquipId(), new TypeReference<>() {
                    });
                if (null != cache && cache.getData() != null) {
                    BatteryCluster cluster = cache.getData();
                    equip.setClusterVoltage(cluster.getClusterVoltage())
                        .setClusterCurrent(cluster.getClusterCurrent())
                        .setSoc(cluster.getSoc())
                        .setSoh(cluster.getSoh())
                        .setInsulatedResistance(cluster.getInsulatedResistance())
                        .setMinMonomerVoltage(cluster.getMinMonomerVoltage())
                        .setMinMonomerVoltageClusterNo(cluster.getMinMonomerVoltageClusterNo())
                        .setMinMonomerVoltageBatteryNo(cluster.getMinMonomerVoltageBatteryNo())
                        .setMinMonomerVoltageCellNo(cluster.getMinMonomerVoltageCellNo())
                        .setMaxMonomerVoltage(cluster.getMaxMonomerVoltage())
                        .setMaxMonomerVoltageClusterNo(cluster.getMaxMonomerVoltageClusterNo())
                        .setMaxMonomerVoltageBatteryNo(cluster.getMaxMonomerVoltageBatteryNo())
                        .setMaxMonomerVoltageCellNo(cluster.getMaxMonomerVoltageCellNo())
                        .setMinBatteryTemp(cluster.getMinBatteryTemp())
                        .setMinBatteryTempClusterNo(cluster.getMinBatteryTempClusterNo())
                        .setMinBatteryTempBatteryNo(cluster.getMinBatteryTempBatteryNo())
                        .setMinBatteryTempCellNo(cluster.getMinBatteryTempCellNo())
                        .setMaxBatteryTemp(cluster.getMaxBatteryTemp())
                        .setMaxBatteryTempClusterNo(cluster.getMaxBatteryTempClusterNo())
                        .setMaxBatteryTempBatteryNo(cluster.getMaxBatteryTempBatteryNo())
                        .setMaxBatteryTempCellNo(cluster.getMaxBatteryTempCellNo());
                }
            }).collect(Collectors.toList()))
            .map(RestUtils::buildListResponse)
            .map(list -> {
                if (null != param.getTotal() && param.getTotal()) {
                    if (null == param.getSize()) {
                        param.setSize(10);
                    }
                    list.setTotal(essEquipRoDs.countBatteryCluster(param));
                }
                return list;
            });
    }

    public Mono<EquipPowerBiVo> getEquipPowerBiVo(String essDno) {
        return Mono.just(essRoDs.getEquipPowerBiVo(essDno));
    }

    public Mono<ListResponse<EssBatteryBundlePo>> getBatteryClusterList(ListEssEquipParam param) {
        List<EssBatteryBundlePo> bundleList = batteryBundleRoDs.getBatteryBundleList(param);
        return Mono.just(RestUtils.buildListResponse(bundleList));
    }

    /**
     * 获取设备字段的多语言文案
     */
    @Transactional
    public List<EssEquipLangPo> getEquipLangList(ListEssEquipLangParam paramIn) {
        EssEquipPo equip = essEquipRoDs.getByDno(paramIn.getDno());
        if (equip == null) {
            log.warn("设备 {} 不存在.", paramIn.getDno());
            return null;
        } else if (equip.getVendor() == null || equip.getEquipType() == null) {
            log.error("设备信息不完整. equip= {}", JsonUtils.toJsonString(equip));
            return null;
        }
        EssEquipType equipType = equip.getEquipType();
        if (equipType.name().endsWith("_METER")) {    // 各种电表
            equipType = EssEquipType.METER;
        }
//        log.debug("");
        return essEquipLangRoDs.getEquipLangList(equip.getVendor(),
            equipType, paramIn.getLang());
    }

    /**
     * 获取设备字段的多语言文案
     */
    @Transactional
    public List<EssEquipLangPo> getEssLangList(ListEssEquipLangParam paramIn) {
        EssPo equip = essRoDs.getByDno(paramIn.getDno());
        if (equip == null) {
            log.warn("设备 {} 不存在.", paramIn.getDno());
            return null;
        } else if (equip.getVendor() == null) {
            log.error("设备信息不完整. equip= {}", JsonUtils.toJsonString(equip));
            return null;
        }
        return essEquipLangRoDs.getEquipLangList(equip.getVendor().name(),
            EssEquipType.HYBRID_INVERTER, paramIn.getLang());
    }

    /**
     * 获取设备故障/告警的多语言翻译文案
     */
    @Transactional
    public List<EssEquipAlarmLangDto> getAlarmLangList(ListAlarmLangParam paramIn) {
        ListEssEquipParam listEssEquipParam = new ListEssEquipParam();
        listEssEquipParam.setDnoList(paramIn.getDnos());
        List<EssEquipPo> equips = essEquipRoDs.getEquipList(listEssEquipParam);
        if (CollectionUtils.isEmpty(equips) && CollectionUtils.isEmpty(paramIn.getEquipTypes())) {
            log.warn("查询设备列表失败. paramIn= {}", paramIn);
            return List.of();
        }
        List<EssEquipType> equipTypes = new ArrayList<>();
        for (var equip : equips) {
            if (equip.getEquipType().name().endsWith("_METER")) {    // 各种电表
                equipTypes.add(EssEquipType.METER);
            } else if (equip.getEquipType() == EssEquipType.AIR_CONDITION) {    // 空调按除湿处理
                equipTypes.add(EssEquipType.DEHUMIDIFIER);
            } else if (equip.getEquipType().name().startsWith("BATTERY_")) {    // 电池堆、电池簇都按BMS处理
                equipTypes.add(EssEquipType.BMS);
            } else {
                equipTypes.add(equip.getEquipType());
            }
        }

        // 将对端传过来的设备类型和本地通过dno查询到的设备类型全部放到查询列表里
        Set<EssEquipType> equipTypeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(paramIn.getEquipTypes())) {
            equipTypeSet.addAll(paramIn.getEquipTypes());
        }
        if (CollectionUtils.isNotEmpty(equipTypes)) {
            equipTypeSet.addAll(equipTypes);
        }
        paramIn.setEquipTypes(equipTypeSet.stream().collect(Collectors.toList()));
        log.info("查询设备告警故障语言信息参数 param= {}", paramIn);
        return essEquipAlarmLangRoDs.getAlarmLangDtoList(paramIn);
    }

    public BmsRelevantVo bmsRelevant(BmsRelevantParam param) {
        IotAssert.isTrue(List.of(EssEquipType.BATTERY_STACK, EssEquipType.BATTERY_PACK)
            .contains(param.getEquipType()), "设备类型无效");
        BmsRelevantVo result = batteryBundleRoDs.bmsRelevant(param);
        IotAssert.isNotNull(result, "设备编号无效");
        return result;
    }
}
