package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.ds.mapper.GwLogTmpMapper;
import com.cdz360.iot.device.mgm.model.basic.po.GwLogPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GwTmpLogService {
    private final Logger logger = LoggerFactory.getLogger(GwTmpLogService.class);

    @Autowired
    private GwLogTmpMapper gwLogTmpMapper;

    public void addGwLog(String string, String clientPrefixFilter) {

        logger.debug("mqtt gw log: {}", string);

        //        JsonNode json = JsonUtils.fromJson(string);
        GwLogPo gwLogPo = JsonUtils.fromJson(string, GwLogPo.class);

        String gwnoOrClient;//可能时网关编号，也可能是云端client的编号
        try {
            String[] gidClient = gwLogPo.getClientId().split("@@@");
            gwnoOrClient = gidClient[gidClient.length - 1];
            gwLogPo.setGwno(gwnoOrClient);
        } catch (Exception e) {
            throw new DcArgumentException(String.format("无效的clientId: %s", gwLogPo.getClientId()));
        }

        if (gwnoOrClient.indexOf(clientPrefixFilter) == 0) {
            // 云端client的编号，进行过滤
            logger.info("skip client prefix: " + clientPrefixFilter);
            return;
        }

        //        String channelId = json.get("channelId").textValue();
        //        String clientId = json.get("clientId").textValue();
        //        String clientIp = json.get("clientIp").textValue();
        //        Long eventIndex = json.get("eventIndex").asLong();
        //        String eventType = json.get("eventType").textValue();
        //        Long time = json.get("time").asLong();
        List list = new ArrayList();
        list.add(gwLogPo);
        gwLogTmpMapper.batchInsert(list);
    }

    public List<String> getGroupGwno() {
        List<String> ret = gwLogTmpMapper.getGroupGwno();
        logger.debug("从log_tmp表获取gwno组: {}", ret);
        return ret;
    }
}
