package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.feign.OpenSimFeignClient;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.SimRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.SimRwDs;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.sim.param.ListSimParam;
import com.cdz360.iot.model.sim.param.ModifyRelationParam;
import com.cdz360.iot.model.sim.param.SimDataSyncParam;
import com.cdz360.iot.model.sim.param.SimQueryCondition;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.CuSimStatus;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import com.cdz360.iot.model.sim.type.SimDeviceType;
import com.cdz360.iot.model.sim.vo.CheckResult;
import com.cdz360.iot.model.sim.vo.SimImportItem;
import com.cdz360.iot.model.sim.vo.SimTinyVo;
import com.cdz360.iot.model.sim.vo.SimVo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.DeviceStatusCodeType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Service
public class SimService {

    @Autowired
    private SimRoDs simRoDs;
    @Autowired
    private SimRwDs simRwDs;
    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private OpenSimFeignClient openSimFeignClient;
    @Autowired
    private DcEventPublisher dcEventPublish;

    public BaseResponse dataSync(SimDataSyncParam param) {
        if (CollectionUtils.isEmpty(param.getReqData())) throw new DcServiceException("参数不能为空");

        AtomicReference<NetworkVendor> vendor = new AtomicReference<>(null);
        param.getReqData().stream().map(SimPo::getVendor).findFirst()
                .ifPresentOrElse(vendor::set, () -> {
                    throw new DcServiceException("外部运营商SIM数据异常");
                });

        AtomicReference<Map<Boolean, List<SimPo>>> mapRef = new AtomicReference<>(new HashMap<>());
        AtomicReference<List<SimVo>> existRef = new AtomicReference<>(new ArrayList<>());
        this.simDataProcess(param.getReqData(), vendor.get(), mapRef, existRef, param.getIsTotalPBUsage());

        boolean res = true;
        if (CollectionUtils.isNotEmpty(mapRef.get().get(true))) {
            this.alarmByStatus(param.getReqData(), existRef.get());
            res = simRwDs.batchUpdate(mapRef.get().get(true), vendor.get());
        }

        if (CollectionUtils.isNotEmpty(mapRef.get().get(false))) {
            res = res && simRwDs.batchInsert(mapRef.get().get(false));
        }
        if (!res) {
            throw new DcServiceException("同步数据失败");
        }
        return RestUtils.success();
    }

    private void simDataProcess(List<SimPo> reqData, NetworkVendor vendor,
                                AtomicReference<Map<Boolean, List<SimPo>>> mapRef,
                                AtomicReference<List<SimVo>> existRef,
                                Boolean isTotalPBUsage) {
        List<SortParam> sorts = new ArrayList<>();
        sorts.add(new SortParam().setColumns(List.of("s.id")).setOrder(OrderType.asc));
        sorts.add(new SortParam().setColumns(List.of("e.updateTime")).setOrder(OrderType.desc));

        if (NetworkVendor.CM_CT == vendor) {
            SimQueryCondition req = new SimQueryCondition();
            req.setMsisdnList(reqData.stream().map(SimPo::getMsisdn).collect(Collectors.toList()));
            req.setSorts(sorts);
            existRef.set(simRoDs.queryByCondition(req));
            List<String> temp = existRef.get().stream().map(SimPo::getMsisdn).collect(Collectors.toList());
            mapRef.set(reqData.stream().collect(Collectors.groupingBy(e -> temp.contains(e.getMsisdn()))));
        } else if (NetworkVendor.CM_PB == vendor) {
            SimQueryCondition req = new SimQueryCondition();
            req.setMsisdnList(reqData.stream().map(SimPo::getMsisdn).collect(Collectors.toList()));
            req.setSorts(sorts);
            existRef.set(simRoDs.queryByCondition(req));
            List<String> temp = existRef.get().stream().map(SimPo::getMsisdn).collect(Collectors.toList());
            mapRef.set(reqData.stream().collect(Collectors.groupingBy(e -> temp.contains(e.getMsisdn()))));
            if (Boolean.FALSE.equals(isTotalPBUsage)) {
                Map<String, BigDecimal> simPoMap = existRef.get().stream()
                    .collect(Collectors.toMap(SimPo::getMsisdn, SimPo::getUsage, (l1, l2) -> l1));
                mapRef.get().get(Boolean.TRUE).forEach(
                    e -> e.setUsage(DecimalUtils.add(e.getUsage(), simPoMap.get(e.getMsisdn()))));
            }
        } else if (NetworkVendor.CU == vendor) {
            SimQueryCondition req = new SimQueryCondition();
            req.setIccidList(reqData.stream().map(SimPo::getIccid).collect(Collectors.toList()));
            req.setSorts(sorts);
            existRef.set(simRoDs.queryByCondition(req));
            List<String> temp = existRef.get().stream().map(SimPo::getIccid).collect(Collectors.toList());
            mapRef.set(reqData.stream().collect(Collectors.groupingBy(e -> temp.contains(e.getIccid()))));
        } else {
            throw new DcServiceException("接收外部运营商SIM数据出错");
        }
    }

    private void alarmByStatus(List<SimPo> reqData, List<SimVo> exist) {
        boolean answer = reqData.stream().allMatch(e -> e.getIccid() != null && e.getStatus() != null);
        answer = answer && exist.stream().allMatch(e -> e.getIccid() != null && e.getStatus() != null && StringUtils.isNotBlank(e.getSiteId()));
        if (!answer) return;

        Map<String, SimVo> map = exist.stream()
            .collect(Collectors.toMap(SimPo::getIccid, o -> o, (l1, l2) -> l1));
        reqData.stream().forEach(e -> {
            SimVo orign = map.get(e.getIccid());
            if (e.getStatus() != orign.getStatus() && orign.getStatus() == CuSimStatus.ACTIVATED) {
                // 推送数据到device
                PvGtiVo simVo = new PvGtiVo();

                SitePo sitePo = siteRoDs.getSite(orign.getSiteId());
                simVo.setSiteId(sitePo.getSiteId())
                        .setSiteCommId(sitePo.getCommId())
                        .setSiteName(sitePo.getName());

                simVo.setRecId(orign.getId().toString())
                        .setDno(orign.getIccid())
                        .setErrorCodeList(List.of((long) DeviceStatusCodeType.C30000.getCode())); // 生成告警
                this.dcEventPublish.publishSimInfo(IotEvent.STATE_CHANGE, simVo);
            } else if (e.getStatus() != orign.getStatus() && e.getStatus() == CuSimStatus.ACTIVATED) {
                // 推送数据到device
                PvGtiVo simVo = new PvGtiVo();
                simVo.setRecId(orign.getId().toString())
                        .setDno(orign.getIccid())
                        .setErrorCodeList(null); // 结束告警
                this.dcEventPublish.publishSimInfo(IotEvent.STATE_CHANGE, simVo);
            }
        });

    }

    public ListResponse<SimVo> getSimList(ListSimParam param) {

        List<String> evseNoIccids = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getEvseNo())) {
            ListEvseParam req = new ListEvseParam();
            req.setEvseNo(param.getEvseNo());
            ListResponse<EvseInfoDto> evseInfoDtoListResponse = evseRoDs.getEvseInfoList(req);
            List<String> tempIccids = evseInfoDtoListResponse.getData().stream()
                .map(EvseInfoDto::getIccid)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempIccids)) {
                return RestUtils.buildListResponse(List.of(), 0);
            }
            evseNoIccids = tempIccids;
        }

        List<String> siteIdIccids = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getSiteId())) {
            ListEvseParam req = new ListEvseParam();
            req.setSiteId(param.getSiteId());
            List<EvsePo> evsePos = evseRoDs.getEvseList(req);
            siteIdIccids = evsePos.stream().map(EvsePo::getIccid)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        }

        List<SimVo> resData = simRoDs.getList(param, evseNoIccids, siteIdIccids);
        if (CollectionUtils.isEmpty(resData)) {
            return RestUtils.buildListResponse(List.of(), 0);
        }

        Map<Boolean, List<SimVo>> deviceTypeListMap = resData.stream()
            .filter(e -> e.getDeviceType() != null)
            .collect(Collectors.groupingBy(s -> SimDeviceType.EVSE.equals(s.getDeviceType()),
                Collectors.toList()));

        if (deviceTypeListMap.size() > 0 && deviceTypeListMap.containsKey(Boolean.FALSE)) {
            Map<String, String> map = siteRoDs.queryByCondition(
                    deviceTypeListMap.get(Boolean.FALSE).stream().map(SimPo::getSiteId)
                        .collect(Collectors.toList()))
                .stream().filter(e -> StringUtils.isNotBlank(e.getSiteId()))
                .collect(Collectors.toMap(SitePo::getSiteId, SitePo::getName));
            if (map.size() > 0) {
                resData = resData.stream().peek(e -> {
                    String siteName = map.get(e.getSiteId());
                    if (siteName != null) {
                        e.setSiteName(siteName);
                    }
                }).collect(Collectors.toList());
            }
        }

        if (deviceTypeListMap.size() > 0 && deviceTypeListMap.containsKey(Boolean.TRUE)) {
            List<EvseInfoDto> dtoList = evseRoDs.getEvseSimVo(
                deviceTypeListMap.get(Boolean.TRUE).stream().map(SimPo::getIccid)
                    .collect(Collectors.toList()));
            Map<String, EvseInfoDto> map = dtoList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getIccid()))
                .collect(Collectors.toMap(EvseInfoDto::getIccid, o -> o,
                    (EvseInfoDto dto1, EvseInfoDto dto2) -> dto1));
            if (map.size() > 0) {
                resData = resData.stream().peek(e -> {
                    EvseInfoDto temp = map.get(e.getIccid());
                    if (temp != null) {
                        e.setSiteName(temp.getSiteName())
                            .setEvseNo(temp.getEvseNo())
                            .setSiteId(temp.getSiteId());
                    }
                }).collect(Collectors.toList());
            }
        }

        if (BooleanUtils.isFalse(param.getTotal())) {
            return RestUtils.buildListResponse(resData);
        }
        Long count = simRoDs.getListCount(param, evseNoIccids, siteIdIccids);
        return RestUtils.buildListResponse(resData, count);
    }

    public ListResponse<SimTinyVo> getSimTinyList(ListSimParam param) {
        IotAssert.isNotNull(param, "参数不能为空");
        List<SimTinyVo> res = simRoDs.getTinyList(param);
        return RestUtils.buildListResponse(res);
    }

    public Mono<BaseResponse> syncAll() {
        return openSimFeignClient.refreshTinyData();
    }

    public Mono<ObjectResponse<SimPo>> syncSim(Long simId) {
        SimPo simPo = simRoDs.getById(simId);
        IotAssert.isNotNull(simPo, "SIM卡不存在");

        return openSimFeignClient.getSimInfo(simPo.getIccid(), simPo.getMsisdn(), simPo.getVendor())
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .doOnNext(simPoSyncData -> {
                SimDataSyncParam req = new SimDataSyncParam();
                req.setReqData(List.of(simPoSyncData));
                req.setIsTotalPBUsage(true);
                this.dataSync(req);
            })
            .map(simPoSyncData -> {
                if (simPoSyncData.getActivatedDate() != null) {
                    simPo.setActivatedDate(simPoSyncData.getActivatedDate());
                }
                simPo.setSlotStatus(simPoSyncData.getSlotStatus())
                    .setStatus(simPoSyncData.getStatus())
                    .setUpdateTime(new Date())
                    .setUsage(simPoSyncData.getUsage());
                return RestUtils.buildObjectResponse(simPo);
            });
    }

    @Transactional
    public BaseResponse modifyRelation(ModifyRelationParam param) {
        IotAssert.isTrue(StringUtils.isBlank(param.getRemark()) || param.getRemark().length() < 51,
            "备注超出50字长度限制");

        SimVo simVo = simRoDs.getVoBySimId(param.getSimId());
        IotAssert.isNotNull(simVo, "SIM卡不存在");

        SimDeviceType deviceType = null;
        if (StringUtils.isNotBlank(param.getSiteId())) {
            SitePo sitePo = siteRoDs.getSite(param.getSiteId());
            IotAssert.isNotNull(sitePo, "站点不存在");
            deviceType = SimDeviceType.UNKNOWN;
        }

        if (param.getEvseNo() != null &&
                !StringUtils.equals(simVo.getEvseNo(), param.getEvseNo())) {
            // 先把旧的关系解绑（桩、备注清除，场站不清除）
            if (StringUtils.isNotBlank(simVo.getEvseNo())) {
                int num = evseRwQueryMapper.clearSimConfig(simVo.getEvseNo());
                IotAssert.isTrue(num > 0, "修改失败");
                IotAssert.isTrue(simRwDs.resetRemark(simVo.getId()), "修改失败");
            }

            // 建立新的关系
            if (StringUtils.isNotBlank(param.getEvseNo())) {
                EvsePo evsePo = evseRoDs.getEvse(param.getEvseNo());
                IotAssert.isNotNull(evsePo, "桩编号不存在");

                EvsePo update = new EvsePo();
                update.setEvseId(param.getEvseNo())
                        .setIccid(simVo.getIccid())
                        .setImsi(simVo.getImsi())
                        .setImei(simVo.getImei());
                int num = evseRwQueryMapper.updateByEvseId(update);
                IotAssert.isTrue(num > 0, "修改失败");
            }
        }
        deviceType = StringUtils.isNotBlank(param.getEvseNo()) ? SimDeviceType.EVSE : deviceType;

        if (param.getSiteId() != null || param.getRemark() != null) {
            SimPo temp = new SimPo();
            temp.setIccid(simVo.getIccid())
                    .setSiteId(param.getSiteId() != null ? param.getSiteId() : null)
                    .setRemark(param.getRemark() != null ? param.getRemark() : null);
            boolean res = simRwDs.updateByIccid(temp);
            IotAssert.isTrue(res, "修改失败");
        }

        boolean res = simRwDs.resetDeviceTypeById(param.getSimId(), deviceType);
        IotAssert.isTrue(res, "修改失败");
        return RestUtils.success();
    }

    public BaseResponse batchModifyRelation(List<SimImportItem> list) {
        list.forEach(e -> {
            SimPo simPo = simRoDs.getOneByCode(e.getCode());
            IotAssert.isNotNull(simPo, "批量处理失败");

            ModifyRelationParam param = new ModifyRelationParam();
            param.setSimId(simPo.getId())
                    .setSiteId(e.getSiteId())
                    .setEvseNo(e.getEvseNo())
                    .setRemark(e.getRemark());
            IotAssert.isNotNull(this.modifyRelation(param), "批量处理失败");
        });
        return RestUtils.success();
    }

    public ListResponse<CheckResult> checkInDB(List<String> codeList) {
        List<String> existList = simRoDs.getUnion(codeList);
        List<SimPo> simPoList = simRoDs.getUnionAll(codeList);
        List<CheckResult> results = codeList.stream().map(e -> {
            CheckResult data = new CheckResult();
            data.setCode(e);
            data.setIsSubsistent(existList.contains(e));
            boolean answer = simPoList.stream().anyMatch(t -> StringUtils.equals(e, t.getIccid()) || StringUtils.equals(e, t.getMsisdn()));
            data.setIsUnique(answer);
            return data;
        }).collect(Collectors.toList());
        return RestUtils.buildListResponse(results);
    }

}
