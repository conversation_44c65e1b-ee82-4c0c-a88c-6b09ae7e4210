package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.device.mgm.ds.mapper.BiMapper;
import com.cdz360.iot.device.mgm.model.bi.vo.DeviceBi;
import com.cdz360.iot.device.mgm.model.bi.vo.ListDeviceParam;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.model.evse.dto.SiteDeviceBiDto;
import com.cdz360.iot.model.evse.vo.EvseStatusPowerBiVo;
import com.cdz360.iot.model.evse.vo.PlugStatusBiVo;
import com.cdz360.iot.model.evse.vo.PlugSupplyBiVo;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
public class BiService {

    @Autowired
    private BiMapper biMapper;

    @Autowired
    private PlugRoDs plugRoDs;

    @Autowired
    private EvseRoDs evseRoDs;

    public DeviceBi getDeviceBi(@Nullable String siteId, EvseBizStatus bizStatus) {
        DeviceBi result = new DeviceBi();
        result.setEvseStatusBi(biMapper.getEvseStatusBi(siteId, bizStatus));
        result.setPlugStatusBi(biMapper.getPlugStatusBi(siteId, bizStatus));
        result.setEvseSupplyBi(biMapper.getEvseSupplyBi(siteId, bizStatus));
        result.setPlugSupplyBi(biMapper.getPlugSupplyBi(siteId, bizStatus));
        return result;
    }

    public List<DeviceBi> getSitesDevice(ListDeviceParam param) {
        List<DeviceBi> result = new ArrayList<>();
        param.getSiteIdList().forEach(siteId -> {
            result.add(this.getDeviceBi(siteId, param.getBizStatus()));
        });
        return result;
    }

    public List<SiteDeviceBiDto> getSiteDeviceBiList(List<String> siteIdList) {
        if (CollectionUtils.isEmpty(siteIdList)) {
            return new ArrayList<>();
        }
        return this.plugRoDs.getSitePlugBiList(siteIdList);
    }

    /**
     * 根据电流类型统计桩/枪数量
     *
     * @return
     */
    public List<PlugSupplyBiVo> getPlugSupplyBi(@Nullable String commIdChain) {
        return this.plugRoDs.getPlugSupplyBi(commIdChain);
    }

    /**
     * 根据状态统计桩/枪数量
     *
     * @return
     */
    public List<PlugStatusBiVo> getPlugStatusBi(@Nullable String provinceCode,
        @Nullable String cityCode,
        @Nullable String siteId,
        @Nullable String commIdChain) {
        return this.plugRoDs.getPlugStatusBi(provinceCode, cityCode, siteId, commIdChain);
    }

    /**
     * 根据状态统计桩功率
     *
     * @param commIdChain
     * @return
     */
    public List<EvseStatusPowerBiVo> getEvseStatusPowerBi(@Nullable String provinceCode,
        @Nullable String cityCode,
        @Nullable String siteId,
        @Nullable String commIdChain) {
        return this.evseRoDs.getEvseStatusPowerBi(provinceCode, cityCode, siteId, commIdChain);
    }
}
