package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.ds.service.DeviceBiService;
import com.cdz360.iot.device.mgm.ds.service.PlugService;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.SiteAndPlugBiVo;
import com.cdz360.iot.model.evse.SiteAndPlugBiVoEx;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.cdz360.iot.model.site.po.CommercialPo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "充电枪相关接口", description = "充电枪")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public class PlugRest {

    @Autowired
    private PlugService plugService;

    @Autowired
    private DeviceBiService deviceBiService;

    @Operation(summary = "获取枪头信息")
    @PostMapping(value = "/device/mgm/plug/getPlugInfo")
    public ObjectResponse<PlugVo> getPlugInfo(@RequestParam String plugNo) {
        log.debug(">>获取枪头信息。 plugNo = {}", plugNo);
        PlugVo plug = plugService.getPlugByPlugNo(plugNo);
        return RestUtils.buildObjectResponse(plug);
    }


    @Operation(summary = "获取枪头列表")
    @PostMapping(value = "/device/mgm/plug/getPlugList")
    public ListResponse<PlugVo> getPlugList(@RequestBody ListPlugParam param) {
        log.debug(">>获取枪头列表。 param = {}", param);
        ListResponse<PlugVo> res = plugService.getPlugList(param);
        log.debug(">>获取枪头信息列表。 size = {}", res.getData().size());
        return res;
    }


    @Operation(summary = "获取商户下桩/枪状态统计数据")
    @PostMapping(value = "/device/mgm/plug/getSiteAndPlugStatus")
    public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugStatus(@RequestParam(value = "commId", required = false) Long commId,
                                                                @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        log.debug(">>获取枪头状态统计。 commId = {}", commId);
        if (commId != null) {
            CommercialPo commercialPo = deviceBiService.getCommById(commId);
            IotAssert.isNotNull(commercialPo, "找不到对应的商户:" + commId);
            IotAssert.isNotBlank(commercialPo.getIdChain(), "商户idChain不能为空:" + commId);
            commIdChain = commercialPo.getIdChain();
        }
        SiteAndPlugBiVo res = deviceBiService.getSiteAndPlugStatus(null, commIdChain);
        log.debug(">>获取枪头状态统计。 res = {}", res);
        return new ObjectResponse<>(res);
    }

    @Operation(summary = "枪状态统计数据")
    @PostMapping(value = "/device/mgm/plug/getSiteAndPlugBiVo")
    public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugBiVo(
        @RequestBody SiteAndPlugBiParam param) {
        log.debug(">>获取枪头状态统计。 param = {}", param);
        return RestUtils.buildObjectResponse(deviceBiService.getSiteAndPlugStatus(param));
    }


    @Operation(summary = "获取商户和商户下枪头状态统计列表")
    @PostMapping(value = "/device/mgm/plug/getSiteAndPlugStatusSubComm")
    public ListResponse<SiteAndPlugBiVoEx> getSiteAndPlugStatusSubComm(@RequestParam("commId") Long commId) {
        log.debug(">>获取枪头状态统计。 commList = {}", commId);
        List<SiteAndPlugBiVoEx> res = deviceBiService.getSiteAndPlugStatusSubComm(commId);
        log.debug(">>获取枪头状态统计。 res = {}", res);
        return new ListResponse<>(res);
    }
}
