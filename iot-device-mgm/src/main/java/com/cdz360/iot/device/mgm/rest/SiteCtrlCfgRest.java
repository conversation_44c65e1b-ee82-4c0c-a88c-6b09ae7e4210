package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.SiteCtrlCfgService;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import com.cdz360.iot.model.site.vo.SiteCtrlCfgVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "场站控制器配置模板相关接口")
@Slf4j
@RestController
@RequestMapping("/device/mgm/siteCtrlCfg")
public class SiteCtrlCfgRest {

    @Autowired
    private SiteCtrlCfgService siteCtrlCfgService;

    @Operation(summary = "新增场站配置模板或更新")
    @PostMapping(value = "/addOrUpdate")
    public BaseResponse addOrUpdate(@RequestBody SiteCtrlCfgPo po) {
        log.info("新增/更新配置模板: {}", JsonUtils.toJsonString(po));
        int i = siteCtrlCfgService.addOrUpdate(po);
        log.info("新增/更新配置模板结果: i = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站最新的配置模板信息", description = "保存数据最新")
    @GetMapping(value = "/findByCtrlNo")
    public ObjectResponse<SiteCtrlCfgVo> findByCtrlNo(
            @Parameter(name = "场站控制器编号", required = true) @RequestParam(value = "ctrlNum") String ctrlNum) {
        return RestUtils.buildObjectResponse(siteCtrlCfgService.findByCtrlNo(ctrlNum));
    }

    @Operation(summary = "查看场站控制器配置", description = "从redis中获取场站控制器上报配置信息")
    @GetMapping(value = "/getBySiteCtrl")
    public ObjectResponse<SiteCtrlCfgVo> getBySiteCtrl(@RequestParam(value = "ctrlNum") String ctrlNum) {
        return RestUtils.buildObjectResponse(siteCtrlCfgService.getBySiteCtrl(ctrlNum));
    }

    @Operation(summary = "下发获取控制器配置指令", description = "配置查看控制器配置接口使用，该接口仅做下发")
    @GetMapping(value = "/send2GetCfg")
    public BaseResponse send2GetCfg(@RequestParam(value = "ctrlNum") String ctrlNum) {
        siteCtrlCfgService.send2GetCfg(ctrlNum);
        return RestUtils.success();
    }
}
