package com.cdz360.iot.device.mgm.model.req;

import com.cdz360.iot.model.base.Update;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
@Accessors(chain = true)
public class SiteCtrlReq {
    @NotNull(groups = {Update.class}, message = "控制器ID不能为空")
    private Long Id;

    //    @NotBlank(groups = {Insert.class}, message = "场站ID不能为空")
    private String siteId;

    @NotBlank(message = "控制器编号不能为空")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "控制器编号只支持数字或字母")
    @Size(min = 10, max = 10, message = "控制器编号只能为10位")
    private String num; //10位数字或字母

    @NotBlank(message = "设备名称不能为空")
    @Pattern(regexp = "^[\\u4E00-\\u9FA5A-Za-z0-9]+$", message = "设备名称只支持字母、数字、汉字")
    @Size(max = 40, message = "设备名称最大长度40个字符")
    private String name; //最大长度40个字符，只支持字母、数字、汉字

    @NotBlank(message = "密钥不能为空")
    @Pattern(regexp = "^[a-zA-Z].{5,17}$", message = "密钥字母开头，6-18字符之间，只支持字母、数字、常规字符")
    private String passcode; //字母开头，6-18字符之间，只支持字母、数字、常规字符
}
