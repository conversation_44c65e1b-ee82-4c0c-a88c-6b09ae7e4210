package com.cdz360.iot.device.mgm.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.SyncSiteEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.device.mgm.ds.service.SiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = IotConstants.SITE_SYNC_QUEUE_NAME)
public class SyncSiteEventListener {

    @Autowired
    private SiteService siteService;

    @RabbitHandler
    public void siteInfoListener(String msg) {
        log.info(">> msg = {}", msg);
        try {
            SyncSiteEvent event = JsonUtils.fromJson(msg, SyncSiteEvent.class);

            this.siteService.syncSiteInfo(event.getData());
        } catch (Exception e) {
            log.warn("message:{}",e.getMessage());
            log.warn("收到未识别的消息. msg = {}", msg);
        }

        log.info("<<");
    }
}
