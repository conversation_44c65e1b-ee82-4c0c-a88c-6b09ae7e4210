package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.iot.ds.rw.ExpressRwDs;
import com.cdz360.iot.model.parts.po.ExpressPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExpressService {

    @Autowired
    private ExpressRwDs expressRwDs;

    public void createExpress(String orderNo, String expressNo, String expressName) {
        ExpressPo expressPo = new ExpressPo()
            .setTransOrderNo(orderNo)
            .setExpressName(expressName)
            .setExpressNo(expressNo);
        final boolean b = expressRwDs.insertExpress(expressPo);
        if (!b) {
            log.warn("物流单号创建不成功: {}", expressPo);
        }
    }
}
