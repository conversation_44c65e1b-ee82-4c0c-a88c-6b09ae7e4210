package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.UpgradeLogRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.BmsRwDs;
import com.cdz360.iot.ds.rw.EssBatteryBundleRwDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ds.rw.GwInfoRwDs;
import com.cdz360.iot.ds.rw.GwSiteRefRwDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.ds.rw.ess.ds.PcsRwDs;
import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.bms.type.BmsVendor;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.pcs.po.PcsPo;
import com.cdz360.iot.model.pcs.type.PcsVendor;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.po.GwSiteRefPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EmuService {

    @Autowired
    private GwInfoRwDs gwInfoRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwSiteRefRwDs gwSiteRefRwDs;

    @Autowired
    private UpgradeLogRoDs upgradeLogRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private BmsRwDs bmsRwDs;

//    @Autowired
//    private BmsRoDs bmsRoDs;

    @Autowired
    private PcsRwDs pcsRwDs;

//    @Autowired
//    private PcsRoDs pcsRoDs;

//    @Autowired
//    private RedisEquipRtDataService redisEquipRtDataService;

    @Autowired
    private DnoGenerator dnoGenerator;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    EssBatteryBundleRwDs essBatteryBundleRwDs;

    @Autowired
    MeterRwDs meterRwDs;

//    @Autowired
//    MeterRoDs meterRoDs;

//    @Autowired
//    EssBatteryBundleRoDs essBatteryBundleRoDs;


    /**
     * 新增EMU
     *
     * @param param
     * @return
     */
    @Transactional
    public Mono<Boolean> addEmu(UpdateCtrlDto param) {
        IotAssert.isNotBlank(param.getGwno(), "控制器编号无效");

        IotAssert.isNull(gwSiteRefRoDs.getGwInfoByGwno(param.getGwno(), false),
            "该控制器编号已存在或已被删除");

        IotAssert.isNull(gwSiteRefRoDs.getGwInfoByName(null, param.getName()),
            "该控制器名称已存在");

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceDtoList()), "管理网元不能为空");

        long count = param.getDeviceDtoList().stream()
            .map(UpdateCtrlDeviceDto::getName).distinct().count();
        if (count != param.getDeviceDtoList().size()) {
            throw new DcArgumentException("提交设备名称重复");
        }

        GtiPo gti = gtiRoDs.getByName(false, param.getDeviceDtoList());
        if (null != gti) {
            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
        }

        this.updateCtrlInfo(param);

        // 更新各个设备信息   以及t_ess_equip中内容
        if (CollectionUtils.isNotEmpty(param.getDeviceDtoList())) {
            updateEquip(param.getGwno(), param.getSiteId(), param.getDeviceDtoList(),
                null);
        }
        return Mono.just(true);
    }

    private void updateCtrlInfo(UpdateCtrlDto param) {
        GwInfoPo gw = new GwInfoPo();
        gw.setGwno(param.getGwno())
            .setName(param.getName())
            .setPasscode(param.getPasscode())
            .setMqType(GwMqType.MQ_TYPE_MQTT)
            .setVer(3);
        gwInfoRwDs.upset(gw);

        // t_ess
        EssPo essPo = new EssPo();
        essPo.setGwno(param.getGwno())
            .setName(param.getName())
            .setDno(param.getGwno())
            .setStatus(EquipStatus.NORMAL)
            .setVendor(GtiVendor.UNKNOWN)
            .setSiteId(param.getSiteId());
        essRwDs.upsetEss(essPo);

        GwSiteRefPo refPo = new GwSiteRefPo();
        refPo.setGwno(param.getGwno())
            .setSiteId(param.getSiteId())
            .setEnable(true);
        gwSiteRefRwDs.upset(refPo);
    }

//    private void upsetGti(UpdateCtrlDto param) {
//        var data = param.getDeviceDtoList().stream()
//            .filter(e -> NetType.PV.equals(e.getDeviceType()))
//            .collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(data)) {
//            List<String> remainDnoList = data.stream()
//                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
//                .map(UpdateCtrlDeviceDto::getDno)
//                .collect(Collectors.toList());
//
//            gtiRwDs.offlineGti(param.getGwno(), remainDnoList);
//
//            gtiRwDs.batchUpsetGti(this.gtiDto2Po(param.getSiteId(), param.getGwno(), data));
//        } else {
//            gtiRwDs.offlineGti(param.getGwno(), null);
//        }
//    }

//    private void upsetEss(UpdateCtrlDto param) {
//        var data = param.getDeviceDtoList().stream()
//            .filter(e -> NetType.ESS.equals(e.getDeviceType()))
//            .collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(data)) {
//            List<String> remainDnoList = data.stream()
//                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
//                .map(UpdateCtrlDeviceDto::getDno)
//                .collect(Collectors.toList());
//
//            essRwDs.offlineEss(param.getGwno(), remainDnoList);
//
//            essRwDs.batchUpsetEss(this.essDto2Po(param.getSiteId(), param.getGwno(), data));
//        } else {
//            essRwDs.offlineEss(param.getGwno(), null);
//        }
//    }

//    private void upsetSrs(UpdateCtrlDto param) {
//        var data = param.getDeviceDtoList().stream()
//            .filter(e -> NetType.SRS.equals(e.getDeviceType()))
//            .collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(data)) {
//            List<String> remainDnoList = data.stream()
//                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
//                .map(UpdateCtrlDeviceDto::getDno)
//                .collect(Collectors.toList());
//
//            srsRwDs.offlineEss(param.getGwno(), remainDnoList);
//
//            srsRwDs.batchUpsetSrs(this.srsDto2Po(param.getSiteId(), param.getGwno(), data));
//        } else {
//            srsRwDs.offlineEss(param.getGwno(), null);
//        }
//    }

    private List<GtiPo> gtiDto2Po(String siteId, String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> this.gtiDto2Po(siteId, gwno, dto))
            .collect(Collectors.toList());
    }

    private List<MeterPo> meterDto2Po(String siteId, String gwno,
        List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> this.meterDto2Po(siteId, gwno, dto))
            .collect(Collectors.toList());
    }


    private List<BmsPo> bmsDto2Po(String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> {
            BmsPo essPo = new BmsPo();

            if (StringUtils.isNotEmpty(dto.getDno())) {
                essPo.setDno(dto.getDno());
            } else {
                essPo.setDno(dnoGenerator.genDno(EssEquipType.BMS));
            }
            return essPo.
                setEssDno(gwno)
                .setName(dto.getName())
                .setVendor(BmsVendor.UNKNOWN);

        }).collect(Collectors.toList());
    }

    private List<PcsPo> pcsDto2Po(String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> {
            PcsPo srsPo = new PcsPo();

            if (StringUtils.isNotEmpty(dto.getDno())) {
                srsPo.setDno(dto.getDno());
            } else {
                srsPo.setDno(dnoGenerator.genDno(EssEquipType.PCS));
            }
            return srsPo.setEssDno(gwno)
                .setName(dto.getName())
                .setVendor(PcsVendor.UNKNOWN);

        }).collect(Collectors.toList());
    }

    private GtiPo gtiDto2Po(String siteId, String gwno, UpdateCtrlDeviceDto dto) {
        GtiPo po = new GtiPo();
        if (StringUtils.isBlank(dto.getDno())) {
            po.setDno(dnoGenerator.genDno(EssEquipType.PV_INV));
        } else {
            po.setDno(dto.getDno());
        }
        return po.setGwno(gwno)
            .setSiteId(siteId)
            .setSid(dto.getSid() == null ? 0 : dto.getSid())
            .setName(dto.getName())
            .setVendor(GtiVendor.UNKNOWN);
    }


    /**
     * 关口电表
     *
     * @param siteId
     * @param gwno
     * @param dto
     * @return
     */
    private MeterPo meterDto2Po(String siteId, String gwno, UpdateCtrlDeviceDto dto) {
        MeterPo po = new MeterPo();
        String dno =
            StringUtils.isEmpty(dto.getDno()) ? dnoGenerator.genDno(EssEquipType.GRID_GATEWAY_METER)
                : dto.getDno();

        return po.setGwno(gwno)
            .setSiteId(siteId)
            .setDno(dno)
            .setNo(dno)
            .setNet(dto.getNetType() != null ? dto.getNetType() : NetType.UNKNOWN)
            .setSid(dto.getSid() == null ? 0 : dto.getSid())
            .setName(dto.getName())
            .setVendor(GtiVendor.UNKNOWN);
    }

    @Transactional
    public Mono<Boolean> updateEmu(UpdateCtrlDto param) {
        IotAssert.isNotBlank(param.getGwno(), "控制器编号无效");

        IotAssert.isNotNull(gwSiteRefRoDs.getGwInfoByGwno(param.getGwno(), true),
            "该控制器不存在。");

        IotAssert.isNull(gwSiteRefRoDs.getGwInfoByName(param.getGwno(), param.getName()),
            String.format("该控制器名称[%s]已被占用", param.getName()));

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceDtoList()), "管理网元不能为空");

        long count = param.getDeviceDtoList().stream()
            .map(UpdateCtrlDeviceDto::getName).distinct().count();
        if (count != param.getDeviceDtoList().size()) {
            throw new DcArgumentException("提交设备名称重复");
        }

        // 更新EMU本身信息
        this.updateCtrlInfo(param);

        // 挂载设备信息调整
        this.updateEmuEquipList(param);

//        GtiPo gti = gtiRoDs.getByName(false, param.getDeviceDtoList().stream()
//            .filter(dto -> StringUtils.isBlank(dto.getDno()))
//            .collect(Collectors.toList()));
//        if (null != gti) {
//            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
//        }
//
//        gti = gtiRoDs.getByName(true, param.getDeviceDtoList().stream()
//            .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
//            .collect(Collectors.toList()));
//        if (null != gti) {
//            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
//        }
//
//        this.updateCtrlInfo(param);
//
//        // 更新各设备信息  以及t_ess_equip中内容
//        if (CollectionUtils.isNotEmpty(param.getDeviceDtoList())) {
//            updateEquip(param.getGwno(), param.getSiteId(), param.getDeviceDtoList(),
//                "EDIT");
//        }
        return Mono.just(true);
    }

    private void updateEmuEquipList(UpdateCtrlDto param) {
        if (CollectionUtils.isNotEmpty(param.getDeviceDtoList())) {
            // 编辑更新内容
            List<UpdateCtrlDeviceDto> updateDevList = param.getDeviceDtoList().stream()
                .filter(x -> StringUtils.isNotBlank(x.getDno())).collect(
                    Collectors.toList());
            updateDevList.forEach(dto -> {
                essEquipRwDs.updateEssEquip(new EssEquipPo()
                    .setDno(dto.getDno())
                    .setName(dto.getName())
                    .setVendor(dto.getEmuVendor())
                    .setNetType(dto.getNetType())
                    .setNetCfg(dto.getNetCfg()));

                // 关联表数据
                switch (dto.getEmuDeviceType()) {
                    case BMS:
                        bmsRwDs.updateBms(new BmsPo().setDno(dto.getDno())
                            .setName(dto.getName())
                            .setVendor(BmsVendor.valueOf(dto.getEmuVendor())));
                        break;
                    case PCS:
                        pcsRwDs.updatePcs(new PcsPo().setDno(dto.getDno())
                            .setName(dto.getName())
                            .setVendor(PcsVendor.valueOf(dto.getEmuVendor())));
                        break;
                    case PV_INV:
                        gtiRwDs.updateGtiByDno(new GtiPo().setDno(dto.getDno())
                            .setName(dto.getName())
                            .setVendor(GtiVendor.valueOf(dto.getEmuVendor())));
                        break;
                }
            });

            // 已删除的设备
            if (CollectionUtils.isNotEmpty(updateDevList)) {
                essEquipRwDs.enableByEssDno(
                    param.getGwno(), Boolean.FALSE,
                    updateDevList.stream().map(UpdateCtrlDeviceDto::getDno).distinct()
                        .collect(Collectors.toList()));
            } else {
                essEquipRwDs.enableByEssDno(param.getGwno(), Boolean.FALSE);
            }

            // 新添加的设备
            List<UpdateCtrlDeviceDto> newDevList = param.getDeviceDtoList().stream()
                .filter(x -> StringUtils.isBlank(x.getDno()))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(newDevList)) {
                int maxEquipId = essEquipRoDs.getMaxEquipIdByDno(param.getGwno());
                AtomicInteger ordinal = new AtomicInteger(maxEquipId);
                List<EssEquipPo> equipList = newDevList.stream()
                    .map(dto -> new EssEquipPo()
                        .setDno(dnoGenerator.genDno(dto.getEmuDeviceType()))
                        .setName(dto.getName())
                        .setEssDno(param.getGwno())
                        .setStatus(EquipStatus.NORMAL)
                        .setEquipId(
                            dto.getEquipId() == null ? Long.valueOf(ordinal.incrementAndGet())
                                : dto.getEquipId())
                        .setEquipTypeId(dto.getEmuDeviceType().getCode())
                        .setEquipType(dto.getEmuDeviceType())
                        .setVendor(dto.getEmuVendor())
                        .setNetType(dto.getNetType())
                        .setNetCfg(dto.getNetCfg())
                    )
                    .collect(Collectors.toList());
                essEquipRwDs.batchUpset(equipList);

                // 关联表数据
                equipList.forEach(equip -> {
                    switch (equip.getEquipType()) {
                        case BMS:
                            bmsRwDs.upsetBms(new BmsPo().setDno(equip.getDno())
                                .setName(equip.getName())
                                .setVendor(BmsVendor.valueOf(equip.getVendor()))
                                .setEssDno(param.getGwno()));
                            break;
                        case PCS:
                            pcsRwDs.batchUpsetPcs(List.of(new PcsPo().setDno(equip.getDno())
                                .setName(equip.getName())
                                .setVendor(PcsVendor.valueOf(equip.getVendor()))
                                .setEssDno(param.getGwno())));
                            break;
                        case PV_INV:
                            gtiRwDs.upsetGti(new GtiPo().setDno(equip.getDno())
                                .setName(equip.getName())
                                .setVendor(GtiVendor.valueOf(equip.getVendor()))
                                .setGwno(param.getGwno()));
                            break;
                    }
                });
            }
        } else {
            // 删除所有设备列表
            essEquipRwDs.enableByEssDno(param.getGwno(), Boolean.FALSE);
        }
    }

    /**
     * 更新各设备信息
     *
     * @param essDno
     * @param siteId
     * @param deviceList
     */
    public void updateEquip(String essDno, String siteId,
        List<UpdateCtrlDeviceDto> deviceList, String uType) {
        if (StringUtils.isEmpty(essDno) || StringUtils.isEmpty(siteId)
            || CollectionUtils.isEmpty(
            deviceList)) {
            return;
        }
        // 分组获取设备信息
        Map<EssEquipType, List<UpdateCtrlDeviceDto>> groupedDevices = deviceList.stream()
            .collect(Collectors.groupingBy(
                UpdateCtrlDeviceDto::getEmuDeviceType,
                Collectors.mapping(e -> {
                    if (StringUtils.isEmpty(e.getDno())) {
                        e.setDno(dnoGenerator.genDno(e.getEmuDeviceType()));
                    }
                    return e;
                }, Collectors.toList())
            ));

        if ("EDIT".equals(uType)) {
            // 下线设备
            gtiRwDs.offlineGti(essDno, null);
            bmsRwDs.offlineBms(essDno, null);
            pcsRwDs.offlinePcs(essDno, null);
            essEquipRwDs.enableByEssDno(essDno, Boolean.FALSE);
        }

        // 批量更新设备
        groupedDevices.forEach((type, list) -> {
            if (CollectionUtils.isNotEmpty(list)) {
                switch (type) {
                    case PV_INV: // 光伏逆变器
                        gtiRwDs.batchUpsetGti(this.gtiDto2Po(siteId, essDno, list));
                        break;
                    case BMS:
                        bmsRwDs.batchUpsetBms(this.bmsDto2Po(essDno, list));
                        break;
                    case BATTERY_PACK: // 电池簇  t_ess_battery_bundle
                        List<EssBatteryBundlePo> bundleList = list.stream().map(x -> {
                            EssBatteryBundlePo po = new EssBatteryBundlePo();
                            po.setDno(x.getDno())
                                .setEssDno(essDno)
                                .setStackEquipId(0L)
                                .setEquipId(0L);
                            // 暂时拿到第一个bms
                            List<UpdateCtrlDeviceDto> bmsList = groupedDevices.get(
                                EssEquipType.BMS);
                            po.setBmsDno(
                                Optional.ofNullable(bmsList).flatMap(vv -> vv.stream().findFirst())
                                    .map(UpdateCtrlDeviceDto::getDno).orElse(null));

                            return po;
                        }).collect(Collectors.toList());
                        essBatteryBundleRwDs.batchInsertBundle(bundleList);
                        break;

                    case BATTERY_STACK: // 电池堆 t_ess_battery_cluster
                        break;
                    case GRID_GATEWAY_METER: // 电网关口电表 t_meter
                        meterRwDs.batchInsertMeter(this.meterDto2Po(siteId, essDno, list));
                        break;
                    case PCS:
                        pcsRwDs.batchUpsetPcs(this.pcsDto2Po(essDno, list));
                        break;
                }
            }
        });

        // 更新t_ess_equip中数据
        int maxEquipId = essEquipRoDs.getMaxEquipIdByDno(essDno);
        AtomicInteger ordinal = new AtomicInteger(maxEquipId);
        List<EssEquipPo> equipList = groupedDevices.values().stream()
            .flatMap(List::stream)
            .map(dto -> new EssEquipPo()
                .setDno(dto.getDno())
                .setName(dto.getName())
                .setEssDno(essDno)
                .setStatus(EquipStatus.NORMAL)
                .setEquipId(dto.getEquipId() == null ? Long.valueOf(ordinal.incrementAndGet())
                    : dto.getEquipId())
                .setEquipTypeId(dto.getEmuDeviceType().getCode())
                .setEquipType(dto.getEmuDeviceType())
                .setVendor(dto.getEmuVendor())
                .setNetType(dto.getNetType())
                .setNetCfg(dto.getNetCfg())
            )
            .collect(Collectors.toList());
        essEquipRwDs.batchUpset(equipList);
    }

    public Mono<GwInfoVo> getEmu(String gwno) {
        if (StringUtils.isBlank(gwno)) {
            throw new DcArgumentException("微网控制器编号无效");
        }

        GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
        if (null == gw) {
            throw new DcArgumentException("微网控制器不存在");
        }

        GwInfoVo vo = new GwInfoVo();
        BeanUtils.copyProperties(gw, vo);
        vo.setGtiEssVoList(essEquipRoDs.getEquipListByEssDnoList(List.of(gwno)));
        return Mono.just(vo);
    }

    @Transactional
    public Mono<Boolean> removeEmu(String siteId, String gwno) {
        if (StringUtils.isBlank(gwno)) {
            throw new DcArgumentException("微网控制器编号无效");
        }

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        GwSiteRefPo refPo = new GwSiteRefPo();
        refPo.setSiteId(siteId)
            .setGwno(gwno)
            .setEnable(false);
        return Mono.just(refPo)
            .map(gwSiteRefRwDs::upset)
            .doOnNext(i -> gtiRwDs.offlineGti(gwno, null));
    }

    public Mono<ListResponse<GwInfoVo>> findEmuList(ListCtrlParam param) {
        return Mono.just(param)
            .map(p -> {
                List<GwInfoVo> gwInfoList = gwSiteRefRoDs.findCtrlList(p);
                Long total = 0L;

                if (CollectionUtils.isNotEmpty(gwInfoList)) {
                    List<String> gwnoList = gwInfoList.stream().map(GwInfoVo::getGwno).collect(
                        Collectors.toList());
                    List<UpdateCtrlDeviceDto> dnoList = essEquipRoDs.getEquipListByEssDnoList(
                        gwnoList);
                    if (CollectionUtils.isNotEmpty(dnoList)) {
                        Map<String, List<UpdateCtrlDeviceDto>> dnoMap = dnoList.stream()
                            .collect(Collectors.groupingBy(UpdateCtrlDeviceDto::getEssDno));
                        gwInfoList.forEach(x -> x.setGtiEssVoList(dnoMap.get(x.getGwno())));
                    }
                }
                if (param.getTotal() != null && param.getTotal()) {
                    if (CollectionUtils.isNotEmpty(gwInfoList)) {
                        total = gwSiteRefRoDs.findCtrlListCount(param);
                    }
                    return RestUtils.buildListResponse(gwInfoList, total);
                }
                return RestUtils.buildListResponse(gwInfoList);
            });
    }

    public Mono<ListResponse<UpgradeLogVo>> upgradeLogList(ListUpgradeLogParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.upgradeLogRoDs::upgradeLogList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.upgradeLogRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }
}
