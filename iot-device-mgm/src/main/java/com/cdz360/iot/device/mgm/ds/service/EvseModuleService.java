package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus.EvseModuleStatusConvertFactory;
import com.cdz360.iot.ds.ro.EvseModuleRoDs;
import com.cdz360.iot.ds.ro.EvseReportModuleRoDs;
import com.cdz360.iot.model.evse.dto.EvseModuleDto;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.vo.EvseModuleVo;
import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EvseModuleService {

    @Autowired
    private EvseModuleRoDs evseModuleRoDs;
    @Autowired
    private EvseReportModuleRoDs evseReportModuleRoDs;
    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    public ObjectResponse<EvseModuleDto> getList(String evseNo) {

        EvseModulePo modulePo = evseModuleRoDs.findChargingModule(evseNo);

        List<EvseModuleVo> evseModuleVoList = evseReportModuleRoDs.getEvseModuleVoList(evseNo);

        EvseModuleDto res = new EvseModuleDto();
        res.setModuleVoList(evseModuleVoList);

        if (modulePo != null && CollectionUtils.isNotEmpty(evseModuleVoList)) {
            String moduleType = modulePo.getModuleType();
            int number = modulePo.getNumber();
            String reportModuleType = evseModuleVoList.get(0).getModuleType();
            int reportModuleNumber = evseModuleVoList.size();

            if (StringUtils.equals(moduleType, reportModuleType)) {
                if (NumberUtils.equals(number, reportModuleNumber)) {
                    res.setCompareResult(1);
                } else if (NumberUtils.compareInteger(reportModuleNumber, number) > 0) {
                    res.setCompareResult(2);
                } else if (NumberUtils.compareInteger(number, reportModuleNumber) > 0) {
                    res.setCompareResult(3);

                    int diff = number - reportModuleNumber;
                    int maxIdx = evseModuleVoList.stream().map(EvseModuleVo::getIdx)
                        .filter(Objects::nonNull).max(Comparator.comparing(x -> x))
                        .orElse(reportModuleNumber);
                    for (int i = 1; i <= diff; i++) {
                        EvseModuleVo temp = new EvseModuleVo();
                        temp.setIdx(maxIdx + i)
                            .setModuleType(moduleType);
                        evseModuleVoList.add(temp);
                    }

                }
            } else {
                res.setCompareResult(4);
                res.setReportModuleType(reportModuleType);
                evseModuleVoList.forEach(e -> {
                    e.setModuleType(moduleType);
                });

                if (NumberUtils.compareInteger(reportModuleNumber, number) > 0) {
                    res.setCompareResult(5);
                } else if (NumberUtils.compareInteger(number, reportModuleNumber) > 0) {
                    res.setCompareResult(6);

                    int diff = number - reportModuleNumber;
                    int maxIdx = evseModuleVoList.stream().map(EvseModuleVo::getIdx)
                        .filter(Objects::nonNull).max(Comparator.comparing(x -> x))
                        .orElse(reportModuleNumber);
                    for (int i = 1; i <= diff; i++) {
                        EvseModuleVo temp = new EvseModuleVo();
                        temp.setIdx(maxIdx + i)
                            .setModuleType(moduleType);
                        evseModuleVoList.add(temp);
                    }
                }
            }

        } else if (modulePo == null && CollectionUtils.isNotEmpty(evseModuleVoList)) {
            // nothing to do
        } else if (modulePo != null && CollectionUtils.isEmpty(evseModuleVoList)) {
            for (int i = 1; i <= modulePo.getNumber(); i++) {
                EvseModuleVo temp = new EvseModuleVo();
                temp.setIdx(i)
                    .setModuleType(modulePo.getModuleType());
                evseModuleVoList.add(temp);
            }
        } else if (modulePo == null && CollectionUtils.isEmpty(evseModuleVoList)) {
            // nothing to do
        }

        if (CollectionUtils.isNotEmpty(evseModuleVoList)) {
            evseModuleVoList.forEach(e -> {
                e.setStatusDesc(e.getStatus() == null ? null
                    : convertFactory.formatStatus(e.getModuleType(), e.getStatus()));
            });
        }
        return RestUtils.buildObjectResponse(res);
    }

}
