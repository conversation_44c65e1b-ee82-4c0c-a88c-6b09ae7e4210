package com.cdz360.iot.device.mgm.ds.service.EvseModuleStatus;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class INFYStatusConvert implements EvseModuleStatusConvert {

    @Autowired
    private EvseModuleStatusConvertFactory convertFactory;

    @PostConstruct
    public void init() {
        convertFactory.addEvseModuleStatusConvertMap("INFY", this);
    }

    @Override
    public String formatStatus(int bitIdx) {
        String res = null;
        switch (bitIdx) {
            case 0:
                res = "输出短路";
                break;
            case 1:
            case 2:
                res = "NA";
                break;
            case 3:
                res = "休眠";
                break;
            case 4:
                res = "模块泄放异常";
                break;
            case 5:
            case 6:
            case 7:
                res = "NA";
                break;
            case 8:
                res = "DC关机";
                break;
            case 9:
                res = "故障告警";
                break;
            case 10:
                res = "保护告警";
                break;
            case 11:
                res = "风扇故障";
                break;
            case 12:
                res = "过温";
                break;
            case 13:
                res = "输出过压";
                break;
            case 14:
                res = "WalkIn打开";
                break;
            case 15:
                res = "通讯中断";
                break;
            case 16:
                res = "限功率状态";
                break;
            case 17:
                res = "模块ID重复";
                break;
            case 18:
                res = "不均流";
                break;
            case 19:
                res = "输入缺相";
                break;
            case 20:
                res = "输入不平衡";
                break;
            case 21:
                res = "输入欠压";
                break;
            case 22:
                res = "输入过压";
                break;
            case 23:
                res = "PFC关机";
                break;
            default:
                res = "NA";
                break;
        }
        return res;
    }
}
