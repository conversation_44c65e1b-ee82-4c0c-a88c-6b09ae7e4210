package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.iot.device.mgm.model.basic.dto.SiteProfitInfo;
import com.cdz360.iot.device.mgm.model.basic.param.ListSiteProfitParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
        fallbackFactory = BizDataCoreFeignHystrix.class)
public interface BizDataCoreFeignClient {

//    // 获取文件上传的STS信息
//    @GetMapping(value = "/dataCore/oss/getArchiveSts")
//    Mono<ObjectResponse<OssStsDto>> getArchiveSts();

    // 获取场站光伏收益计算规则
    @PostMapping("/dataCore/site/getSiteProfitList")
    Mono<ListResponse<SiteProfitInfo>> getSiteProfitList(@RequestBody ListSiteProfitParam param);
}
