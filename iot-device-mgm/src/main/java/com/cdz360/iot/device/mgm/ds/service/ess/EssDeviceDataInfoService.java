package com.cdz360.iot.device.mgm.ds.service.ess;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.vo.EssDeviceBiVo;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisEmuRoService;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.SiteEssVo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssDeviceDataInfoService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private RedisEmuRoService redisEmuRoService;

    public Mono<ListResponse<EssDeviceBiVo>> essDeviceDataInfo(String siteId) {

        // 获取emu列表
        List<EssVo> emuList = essRoDs.findEssList(new ListEssParam().setSiteId(siteId));
        if (CollectionUtils.isEmpty(emuList)) {
            return Mono.just(RestUtils.buildListResponse(List.of()));
        }

        List<String> dnoList = emuList.stream().map(EssVo::getDno)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());

        List<EssDeviceBiVo> result = essEquipRoDs.equipCountByEssDno(dnoList);
        if (CollectionUtils.isNotEmpty(result)) {
            result.add(0,
                new EssDeviceBiVo().setEquipType(EssEquipType.EMS).setCount((long) emuList.size()));
            return Mono.just(RestUtils.buildListResponse(result));
        }
        return Mono.just(RestUtils.buildListResponse(List.of(
            new EssDeviceBiVo().setEquipType(EssEquipType.EMS).setCount((long) emuList.size()))));
    }

    public Mono<ListResponse<SiteEssVo>> essDeviceCount( ListEssParam param) {
        return Mono.just(RestUtils.buildListResponse(essRoDs.findSiteEssAmount(param)));
    }
}
