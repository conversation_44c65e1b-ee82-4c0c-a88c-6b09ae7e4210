package com.cdz360.iot.device.mgm;

import com.netflix.discovery.EurekaClient;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;

@SpringBootApplication
@ComponentScan(basePackages = {"com.cdz360.iot", "com.cdz360.data"})
@MapperScan(basePackages = {"com.cdz360.iot.**.mapper"})
@EnableDiscoveryClient(autoRegister = true)
@EnableFeignClients(basePackages = { "com.cdz360.iot.*.feign", "com.cdz360.iot.device.mgm.dzds.client"})
@EnableReactiveFeignClients(basePackages = { "com.cdz360.iot.device.mgm.feign"})
@EnableAsync
@EnableScheduling
public class IotDeviceMgmMain {
    private final Logger logger = LoggerFactory.getLogger(IotDeviceMgmMain.class);


    @Autowired
    private EurekaClient discoveryClient;

    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(IotDeviceMgmMain.class).web(WebApplicationType.REACTIVE).run(args);
    }

    @PreDestroy
    public void destroy() {
        logger.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        logger.info(".....");
    }
}
