package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.device.mgm.ds.service.SiteService;
import com.cdz360.iot.device.mgm.model.bi.vo.PlugInfoDto;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.site.po.SitePo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "场站相关接口", description = "场站")
@RequestMapping(value = "/device/mgm/site", produces = MediaType.APPLICATION_JSON_VALUE)
public class SiteRest {

    private final Logger logger = LoggerFactory.getLogger(SiteRest.class);

    @Autowired
    private SiteService siteService;

//    @Operation(summary = "获取场站列表接口")
//    @PostMapping(value = "/listSite")
//    public CommonRpcResponse<SiteDto> listSite(
//            @Parameter(name = "获取场站列表参数") @RequestBody ListSiteParam param) {
//        logger.info(">>获取场站列表接口。 param = {}", param);
//        SitePageDto sitePageDto = this.siteService.listSiteDto(param);
//        CommonRpcResponse<SiteDto> res = new CommonRpcResponse(sitePageDto);
//        logger.info("<< siteList.size = {}", sitePageDto.getSiteList().size());
//        return res;
//    }

//    @Operation(summary = "修改场站信息接口")
//    @PostMapping(value = "/modifySite")
//    public CommonRpcResponse<Integer> modifySite(
//            @Parameter(name = "修改场站信息参数") @RequestBody SiteParam param) {
//        logger.info(">>修改场站信息接口。 param = {}", param);
//        CommonRpcResponse<Integer> res = this.siteService.modifySiteInfo(param);
//        logger.info("<< modify result = {}", res);
//        return res;
//    }

    @Operation(summary = "获取场站下的枪列表")
    @PostMapping(value = "/plugs")
    public ListResponse<PlugInfoDto> plugs(
        @RequestBody ListPlugParam param
//            @Parameter(name = "当前页数") @RequestParam("index") Integer index,
//            @Parameter(name = "每页行数") @RequestParam("size") Integer size,
//            @Parameter(name = "场站Id") @RequestParam String siteId
    ) {
        logger.info(">> 获取场站枪口列表。 param = {}", param);
        if (param == null || param.getStart() == null || param.getSize() == null) {
            logger.info("<< 分页参数需要提供");
            throw new DcArgumentException("分页参数需要提供");
        }

        List<PlugInfoDto> res = this.siteService.getPlugsOfSite(param);
//        logger.info("<< query result = {}", res);
        return new ListResponse<>(res);
    }

    @Operation(summary = "更新枪名称")
    @GetMapping(value = "/refreshPlugName")
    @Deprecated
    public ObjectResponse<Boolean> plugs(
        @Parameter(name = "桩编号") @RequestParam("evseId") String evseId,
        @Parameter(name = "枪编号") @RequestParam("plugId") Integer plugId,
        @Parameter(name = "枪名称") @RequestParam("name") String name) {
        throw new DcServerException("deprecated");
//        logger.info(">> 更新枪名称, evseId={}, plugId={}, name={}", evseId, plugId, name);
//        Boolean res = this.siteService.refreshPlugName(evseId, plugId, name);
//        logger.info("<< update result = {}", res);
//        return new ObjectResponse<>(res);
    }

    @Operation(summary = "获取有空闲枪头的场站ID列表")
    @GetMapping(value = "/getIdleSiteIdList")
    public ListResponse<String> getIdleSiteIdList(
        @Parameter(name = "集团商户ID") @RequestParam("topCommId") Long topCommId) {
        logger.debug(">> 获取有空闲枪头的场站ID列表, topCommId = {}", topCommId);
        var list = this.siteService.getIdleSiteIdList(topCommId);
        logger.info("<< list.size = {}", list.size());
        return new ListResponse<>(list);
    }

    @Operation(summary = "获取场站桩枪相关数据(仅考虑正常运营状态)")
    @GetMapping(value = "/getRecordEvsePlugInfo")
    public ObjectResponse<SitePo> getRecordEvsePlugInfo(
        @RequestParam(value = "siteId") String siteId) {
        return RestUtils.buildObjectResponse(siteService.getRecordEvsePlugInfo(siteId));
    }

    @Operation(summary = "批量获取2020-06版本升级清洗场站桩枪相关数据")
    @GetMapping(value = "/getUpgradeCleaningEvsePlugInfo")
    public ListResponse<SitePo> getRecordEvsePlugInfoList() {
        return new ListResponse<>(siteService.getUpgradeCleaningEvsePlugInfo());
    }

    @Operation(summary = "获取场站质保到期日")
    @GetMapping(value = "/getExpireDate")
    public ObjectResponse<Date> getExpireDate(@RequestParam(value = "siteId") String siteId) {
        logger.info("getExpireDate siteId: {}", siteId);
        return siteService.getExpireDate(siteId);
    }

}
