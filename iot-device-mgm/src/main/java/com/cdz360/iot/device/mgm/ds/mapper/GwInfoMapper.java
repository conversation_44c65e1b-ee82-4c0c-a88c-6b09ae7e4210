package com.cdz360.iot.device.mgm.ds.mapper;

import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.dto.SiteDto;
import com.cdz360.iot.model.site.param.ListGwParam;
import com.cdz360.iot.model.site.po.GwInfoPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GwInfoMapper {
//    List<GwInfoPo> listGwBySiteIds(@Param(value = "siteIds") List<Long> siteIds);

//    List<GwInfoDto> listGwIdle(ListGwParam param);

    //    List<GwInfoDto> listGwCommon(ListGwParam param);
    int logicDelete(@Param(value = "oldGwno") String oldGwno, @Param(value = "siteId") String siteId);

    int insertOrUpdate(@Param(value = "currGwno") String currGwno, @Param(value = "siteId") String siteId);

    SiteDto getGwInfoByDzSiteId(@Param(value = "siteId") String siteId);

    List<String> getNotBoundGwno(@Param(value = "cityCode") String cityCode);

    int getCountBySiteId(@Param(value = "siteId") String siteId);

    int getCountByGwnoAndStatus(@Param(value = "gwno") String gwno);

    int getCountBySiteIdAndTrue(@Param(value = "siteId") String siteId);

    int getCountByGwnoAndTrue(@Param(value = "gwno") String gwno);

    void updateFalseBySiteId(@Param(value = "siteId") String siteId);

    String findGwnoBySiteIdAndTrue(@Param(value = "siteId") String siteId);
}
