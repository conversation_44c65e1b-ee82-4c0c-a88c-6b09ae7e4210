package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.UpgradeLogRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ds.rw.GwInfoRwDs;
import com.cdz360.iot.ds.rw.GwSiteRefRwDs;
import com.cdz360.iot.ds.rw.SrsRwDs;
import com.cdz360.iot.model.ess.dto.RedisMgcRtData;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.gw.MgcAlertInfo;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.EquipUnitType;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.po.GwSiteRefPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import com.cdz360.iot.model.srs.po.SrsPo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class MgcService {

    @Autowired
    private GwInfoRwDs gwInfoRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwSiteRefRwDs gwSiteRefRwDs;

    @Autowired
    private UpgradeLogRoDs upgradeLogRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private SrsRwDs srsRwDs;

//    @Autowired
//    private EssRoDs essRoDs;
//
//    @Autowired
//    private SrsRoDs srsRoDs;

    @Autowired
    private RedisEquipRtDataService redisEquipRtDataService;

    @Autowired
    private DnoGenerator dnoGenerator;

    @Transactional
    public Mono<Boolean> addCtrl(UpdateCtrlDto param) {
        if (StringUtils.isBlank(param.getGwno())) {
            throw new DcArgumentException("控制器编号无效");
        }

        GwInfoDto gwInfoByGwno = gwSiteRefRoDs.getGwInfoByGwno(param.getGwno(), false);
        if (null != gwInfoByGwno) {
            throw new DcArgumentException("该控制器编号已存在或已被删除");
        }

        GwInfoDto gw = gwSiteRefRoDs.getGwInfoByName(null, param.getName());
        if (null != gw) {
            throw new DcArgumentException("该控制器名称已存在");
        }

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceDtoList()), "管理网元不能为空");

        long count = param.getDeviceDtoList().stream()
            .map(UpdateCtrlDeviceDto::getName).distinct().count();
        if (count != param.getDeviceDtoList().size()) {
            throw new DcArgumentException("提交设备名称重复");
        }

        count = param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
            .map(UpdateCtrlDeviceDto::getSid).distinct().count();
        if (count != param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
            .toArray().length) {
            throw new DcArgumentException("提交通讯地址重复");
        }

        GtiPo gti = gtiRoDs.getByName(false, param.getDeviceDtoList());
        if (null != gti) {
            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
        }

        this.updateCtrlInfo(param);

        // 注意更新逆变器,储能ESS逻辑
        if (CollectionUtils.isNotEmpty(param.getDeviceDtoList())) {
            List<UpdateCtrlDeviceDto> pvList = param.getDeviceDtoList().stream()
                .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
                .collect(Collectors.toList());
            List<UpdateCtrlDeviceDto> essList = param.getDeviceDtoList().stream()
                .filter(e -> EquipUnitType.ESS.equals(e.getDeviceType()))
                .collect(Collectors.toList());
            List<UpdateCtrlDeviceDto> srsList = param.getDeviceDtoList().stream()
                .filter(e -> EquipUnitType.SRS.equals(e.getDeviceType()))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pvList)) {
                gtiRwDs.batchUpsetGti(this.gtiDto2Po(param.getSiteId(), param.getGwno(), pvList));
            }
            if (CollectionUtils.isNotEmpty(essList)) {
                essRwDs.batchUpsetEss(this.essDto2Po(param.getSiteId(), param.getGwno(), essList));
            }
            if (CollectionUtils.isNotEmpty(srsList)) {
                srsRwDs.batchUpsetSrs(this.srsDto2Po(param.getSiteId(), param.getGwno(), srsList));
            }
        }
        return Mono.just(true);
    }

    private void updateCtrlInfo(UpdateCtrlDto param) {
        GwInfoPo gw = new GwInfoPo();
        gw.setGwno(param.getGwno())
            .setName(param.getName())
            .setPasscode(param.getPasscode())
            .setMqType(GwMqType.MQ_TYPE_MQTT)
            .setVer(3);
        gwInfoRwDs.upset(gw);
        GwSiteRefPo refPo = new GwSiteRefPo();
        refPo.setGwno(param.getGwno())
            .setSiteId(param.getSiteId())
            .setEnable(true);
        gwSiteRefRwDs.upset(refPo);
    }

    private void upsetGti(UpdateCtrlDto param) {
        var data = param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(data)) {
            List<String> remainDnoList = data.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
                .map(UpdateCtrlDeviceDto::getDno)
                .collect(Collectors.toList());

            gtiRwDs.offlineGti(param.getGwno(), remainDnoList);

            gtiRwDs.batchUpsetGti(this.gtiDto2Po(param.getSiteId(), param.getGwno(), data));
        } else {
            gtiRwDs.offlineGti(param.getGwno(), null);
        }
    }

    private void upsetEss(UpdateCtrlDto param) {
        var data = param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.ESS.equals(e.getDeviceType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(data)) {
            List<String> remainDnoList = data.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
                .map(UpdateCtrlDeviceDto::getDno)
                .collect(Collectors.toList());

            essRwDs.offlineEss(param.getGwno(), remainDnoList);

            essRwDs.batchUpsetEss(this.essDto2Po(param.getSiteId(), param.getGwno(), data));
        } else {
            essRwDs.offlineEss(param.getGwno(), null);
        }
    }

    private void upsetSrs(UpdateCtrlDto param) {
        var data = param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.SRS.equals(e.getDeviceType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(data)) {
            List<String> remainDnoList = data.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
                .map(UpdateCtrlDeviceDto::getDno)
                .collect(Collectors.toList());

            srsRwDs.offlineEss(param.getGwno(), remainDnoList);

            srsRwDs.batchUpsetSrs(this.srsDto2Po(param.getSiteId(), param.getGwno(), data));
        } else {
            srsRwDs.offlineEss(param.getGwno(), null);
        }
    }

    private List<GtiPo> gtiDto2Po(String siteId, String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> this.gtiDto2Po(siteId, gwno, dto))
            .collect(Collectors.toList());
    }

    private List<EssPo> essDto2Po(String siteId, String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> {
            EssPo essPo = new EssPo();

            if (StringUtils.isNotEmpty(dto.getDno())) {
                essPo.setDno(dto.getDno());
            } else {
                essPo.setDno(dnoGenerator.genDno(EssEquipType.ESS));
            }
            return essPo.setSiteId(siteId)
                .setGwno(gwno)
                .setName(dto.getName())
                .setVendor(dto.getVendor());

        }).collect(Collectors.toList());
    }

    private List<SrsPo> srsDto2Po(String siteId, String gwno, List<UpdateCtrlDeviceDto> dtoList) {
        return dtoList.stream().map(dto -> {
            SrsPo srsPo = new SrsPo();

            if (StringUtils.isNotEmpty(dto.getDno())) {
                srsPo.setDno(dto.getDno());
            } else {
                srsPo.setDno(dnoGenerator.genDno(EssEquipType.SRS));
            }
            return srsPo.setSiteId(siteId)
                .setGwno(gwno)
                .setName(dto.getName())
                .setSid(dto.getSid())
                .setVendor(dto.getVendor());

        }).collect(Collectors.toList());
    }

    private GtiPo gtiDto2Po(String siteId, String gwno, UpdateCtrlDeviceDto dto) {
        GtiPo po = new GtiPo();
        if (StringUtils.isBlank(dto.getDno())) {
            po.setDno(dnoGenerator.genDno(EssEquipType.PV_INV));
        } else {
            po.setDno(dto.getDno());
        }
        return po.setGwno(gwno)
            .setSiteId(siteId)
            .setSid(dto.getSid())
            .setName(dto.getName())
            .setVendor(dto.getVendor());
    }

    @Transactional
    public Mono<Boolean> updateCtrl(UpdateCtrlDto param) {

        if (StringUtils.isBlank(param.getGwno())) {
            throw new DcArgumentException("控制器编号无效");
        }

        GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(param.getGwno(), true);
        if (null == gw) {
            throw new DcArgumentException("该控制器不存在。");
        }

        gw = gwSiteRefRoDs.getGwInfoByName(param.getGwno(), param.getName());
        if (null != gw) {
            throw new DcArgumentException(
                String.format("该控制器名称[%s]已被占用", param.getName()));
        }

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceDtoList()), "管理网元不能为空");

        long count = param.getDeviceDtoList().stream()
            .map(UpdateCtrlDeviceDto::getName).distinct().count();
        if (count != param.getDeviceDtoList().size()) {
            throw new DcArgumentException("提交设备名称重复");
        }

        count = param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
            .map(UpdateCtrlDeviceDto::getSid).distinct().count();
        if (count != param.getDeviceDtoList().stream()
            .filter(e -> EquipUnitType.PV.equals(e.getDeviceType()))
            .toArray().length) {
            throw new DcArgumentException("提交通讯地址重复");
        }

        GtiPo gti = gtiRoDs.getByName(false, param.getDeviceDtoList().stream()
            .filter(dto -> StringUtils.isBlank(dto.getDno()))
            .collect(Collectors.toList()));
        if (null != gti) {
            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
        }

        gti = gtiRoDs.getByName(true, param.getDeviceDtoList().stream()
            .filter(dto -> StringUtils.isNotBlank(dto.getDno()))
            .collect(Collectors.toList()));
        if (null != gti) {
            throw new DcArgumentException(String.format("设备名称[%s]已被占用", gti.getName()));
        }

        this.updateCtrlInfo(param);

        // 注意更新逆变器,储能ESS,辐射仪SRS逻辑
        if (CollectionUtils.isNotEmpty(param.getDeviceDtoList())) {
            this.upsetGti(param);
            this.upsetEss(param);
            this.upsetSrs(param);
        }
        return Mono.just(true);
    }

    public Mono<GwInfoVo> getCtrl(String gwno) {
        if (StringUtils.isBlank(gwno)) {
            throw new DcArgumentException("微网控制器编号无效");
        }

        GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
        if (null == gw) {
            throw new DcArgumentException("微网控制器不存在");
        }

        GwInfoVo vo = new GwInfoVo();
        BeanUtils.copyProperties(gw, vo);
        vo.setGtiEssVoList(this.gwSiteRefRoDs.findGtiAndEssListByGwno(List.of(gwno)));
        return Mono.just(vo);
    }

    @Transactional
    public Mono<Boolean> removeCtrl(String siteId, String gwno) {
        if (StringUtils.isBlank(gwno)) {
            throw new DcArgumentException("微网控制器编号无效");
        }

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        GwSiteRefPo refPo = new GwSiteRefPo();
        refPo.setSiteId(siteId)
            .setGwno(gwno)
            .setEnable(false);
        return Mono.just(refPo)
            .map(gwSiteRefRwDs::upset)
            .doOnNext(i -> gtiRwDs.offlineGti(gwno, null));
    }

    public Mono<ListResponse<GwInfoVo>> findCtrlList(ListCtrlParam param) {
        return Mono.just(param)
            .map(p -> {
                List<GwInfoVo> gwInfoList = gwSiteRefRoDs.findCtrlList(p);
                Long total = 0L;

                if (CollectionUtils.isNotEmpty(gwInfoList)) {
                    gwInfoList.forEach(e -> {
                        RedisMgcRtData<MgcAlertInfo> ret = redisEquipRtDataService.gwSrcInfo(
                            e.getGwno());
                        e.setMgcAlertInfo(ret != null ? ret.getData() : new MgcAlertInfo());
                        e.setGtiEssVoList(
                            gwSiteRefRoDs.findGtiAndEssListByGwno(List.of(e.getGwno())));
                    });
                }

                if (param.getTotal() != null && param.getTotal()) {
                    if (CollectionUtils.isNotEmpty(gwInfoList)) {
                        total = gwSiteRefRoDs.findCtrlListCount(param);
                    }
                    return RestUtils.buildListResponse(gwInfoList, total);
                }
                return RestUtils.buildListResponse(gwInfoList);
            });
    }

    public Mono<ListResponse<UpgradeLogVo>> upgradeLogList(ListUpgradeLogParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.upgradeLogRoDs::upgradeLogList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.upgradeLogRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }
}
