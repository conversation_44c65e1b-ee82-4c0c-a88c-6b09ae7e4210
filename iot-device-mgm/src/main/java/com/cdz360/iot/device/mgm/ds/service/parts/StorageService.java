package com.cdz360.iot.device.mgm.ds.service.parts;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.StorageRoDs;
import com.cdz360.iot.ds.rw.StorageRwDs;
import com.cdz360.iot.model.parts.po.StoragePo;
import com.cdz360.iot.model.parts.type.StorageType;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StorageService {

    private static final String DEFAULT_STORAGE_NAME = "总部库";

    @Autowired
    private StorageRoDs storageRoDs;

    @Autowired
    private StorageRwDs storageRwDs;

    private String nextStorageCode() {
        int time = 10;
        String code;
        do {
            code = "D" + RandomStringUtils.randomNumeric(7);
        } while (time-- > 0 && null != storageRoDs.getByCode(code));
        return time == 0 ? code + "X" : code;
    }

    public StoragePo createStorage(String name, Long uid) {
        StoragePo storage = new StoragePo()
            .setCode(this.nextStorageCode())
            .setName(name);

        if (null != uid) {
            storage.setUid(uid)
                .setType(StorageType.PERSONAL);
        } else {
            storage.setType(StorageType.PERSONAL);
        }

        final boolean b = storageRwDs.insertStorage(storage);
        if (!b) {
            log.warn("普通库创建不成功");
        }
        return storage;
    }

    public StoragePo getPersonalStorage(Long uid, String name) {
        StoragePo storage = storageRwDs.getByUid(uid, true);
        if (null == storage) {
            storage = this.createStorage(name, uid);
        }
        return storage;
    }

    // 总部库(默认库)
    public StoragePo getTopStorage() {
        StoragePo storage = storageRwDs.getOneByName(DEFAULT_STORAGE_NAME, true);
        if (null == storage) {
            storage = this.createStorage(DEFAULT_STORAGE_NAME, null);
        }
        return storage;
    }

    public List<String> findStorageCodeList(
        Boolean defaultTopStorage, List<Long> uidList, List<Long> exUidList) {
        List<String> result = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(uidList)) {
            List<Long> collect;
            if (CollectionUtils.isNotEmpty(exUidList)) {
                collect = uidList.stream().filter(x -> !exUidList.contains(x))
                    .collect(Collectors.toList());
            } else {
                collect = uidList;
            }
            result = storageRoDs.getByUid(collect).stream()
                .map(StoragePo::getCode)
                .collect(Collectors.toList());
        }

        // 是否只查总部库物料
        if (Boolean.TRUE.equals(defaultTopStorage)) {
            final StoragePo storage = this.getTopStorage();
            result.add(storage.getCode());
        }

        return result;
    }
}
