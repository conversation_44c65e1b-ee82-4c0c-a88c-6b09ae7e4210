package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.model.req.SiteCtrlReq;
import com.cdz360.iot.ds.ro.SiteCtrlRoDs;
import com.cdz360.iot.ds.rw.SiteCtrlRwDs;
import com.cdz360.iot.model.site.dto.SiteCtrlDto;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteCtrlService {

    @Autowired
    private SiteCtrlRoDs siteCtrlRoDs;
    @Autowired
    private SiteCtrlRwDs siteCtrlRwDs;

    public BaseResponse add(SiteCtrlReq req) {
        SiteCtrlPo db = siteCtrlRoDs.selectByNum(req.getNum());
        IotAssert.isTrue(db == null || !db.getEnable(), "控制器编号已存在");

        SiteCtrlPo po = new SiteCtrlPo();
        po.setNum(req.getNum())
                .setSiteId(req.getSiteId())
                .setName(req.getName())
                .setStatus(SiteCtrlStatusType.STARTUP)
                .setPasscode(req.getPasscode())
                .setEnable(true);

        siteCtrlRwDs.insertOrUpdate(po);
        return RestUtils.success();
    }

    public BaseResponse edit(SiteCtrlReq req) {
        SiteCtrlPo po = new SiteCtrlPo();
        po.setId(req.getId())
                .setNum(req.getNum())
                .setSiteId(req.getSiteId())
                .setName(req.getName())
                .setPasscode(req.getPasscode());

        siteCtrlRwDs.insertOrUpdate(po);
        return RestUtils.success();
    }

    public ListResponse<SiteCtrlDto> list(String keyword, String siteId, long start, long size) {
        return siteCtrlRoDs.list(keyword, siteId, start, size);
    }

    public Boolean disable(String num) {
        // TODO: 2020/4/24 还需清除token
        return siteCtrlRwDs.disable(num);
    }
}
