package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.dzds.client.IotWorkFeign;
import com.cdz360.iot.ds.ro.*;
import com.cdz360.iot.ds.rw.SiteTopologyRefRwDs;
import com.cdz360.iot.ds.rw.TransformerRwDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.param.TransformerUpdateParam;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.topology.vo.SiteTopologyRefVo;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.model.transformer.vo.TransformerPlugsVo;
import com.cdz360.iot.model.transformer.vo.TransformerVo;
import com.cdz360.iot.model.type.TopologyType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname TransformerService
 * @Description
 * @Date 1/20/2021 9:12 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class TransformerService {

    @Autowired
    private TransformerRoDs transformerRoDs;

    @Autowired
    private TransformerRwDs transformerRwDs;

    @Autowired
    private SiteTopologyRefRoDs siteTopologyRefRoDs;

    @Autowired
    private SiteTopologyRefRwDs siteTopologyRefRwDs;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private PlugRoDs plugRoDs;

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private EvseMeterRoDs evseMeterRoDs;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotWorkFeign iotWorkFeign;

    private static final int MAX_QUERY_THREAD_COUNT = 10;

    public int listCount(String keyword, Long transformerId, String siteId, long start, long size) {
        return transformerRoDs.listCount(keyword, transformerId, siteId, start, size);
    }

    public Mono<List<TransformerVo>> list(String keyword, Long transformerId, String siteId, long start, long size) {
        List<TransformerVo> ret = transformerRoDs.list(keyword, transformerId, siteId, start, size);

        if(CollectionUtils.isEmpty(ret)) {
            return Mono.just(List.of());
        }

        Mono<List<TransformerVo>> listMono = Flux.fromIterable(ret)
                .parallel(ret.size() > MAX_QUERY_THREAD_COUNT ? MAX_QUERY_THREAD_COUNT : ret.size())
                .runOn(Schedulers.parallel())
                .map(e -> {
                    List<SiteTopologyRefVo> collect = this.getDownList(e.getId());
                    // 设置变压器下游对象
                    e.setDownList(collect);
                    return e;
                }).sequential()
                .sort(Comparator.comparing(TransformerVo::getUpdateTime).reversed())
                .collectList();




        return listMono;

//        if(CollectionUtils.isNotEmpty(ret)) {
//            ret.forEach(e -> {
//                List<SiteTopologyRefPo> topologyVos = siteTopologyRefRoDs.getByUpId(e.getId());
//                e.setTopologyVos(topologyVos);
//            });
//        }
//        return new ListResponse<>(ret, (long)ret.size());
    }

    /**
     * 获取下游设备信息
     * @param id
     * @return
     */
    private List<SiteTopologyRefVo> getDownList(Long id) {

        List<SiteTopologyRefPo> topologyVos = siteTopologyRefRoDs.getByUpId(id);

        return topologyVos.stream()
                .map(ie -> {
                    SiteTopologyRefVo vo = new SiteTopologyRefVo();
                    BeanUtils.copyProperties(ie, vo);
                    if (TopologyType.EVSE.equals(ie.getDownType())) {

                        EvsePo evseById = evseRoDs.getEvseById(ie.getDownId());
                        if (evseById != null) {
                            vo.setDownName(evseById.getName())
                                    .setDownNo(evseById.getEvseId())
                                    .setPower(evseById.getPower());
                        }
                    } else if (TopologyType.METER.equals(ie.getDownType())) {
                        MeterPo meter = meterRoDs.getById(ie.getDownId());
                        if (meter != null) {
                            vo.setDownName(meter.getName())
                                    .setDownNo(meter.getNo());
                            // 设置电表下游对象
                            List<SiteTopologyRefVo> downList = siteTopologyRefRoDs
                                    .getEvseMeterTopology(meter.getId());
                            vo.setDownList(downList);
                        }
                    } else {
                    }
                    return vo;
                })
                .collect(Collectors.toList());

    }

    @Transactional
    public void add(TransformerVo req) {

//        IotAssert.isNull(transformerRoDs.getByNo(req.getNo()), "新增变压器失败，变压器编号已存在，请重新指定。");

        this.checkDynamicPower(req.getDownList(), req);

        IotAssert.isTrue(transformerRwDs.insertTransformer(req), "新增变压器失败");

        if(CollectionUtils.isNotEmpty(req.getDownList())) {
            req.getDownList().forEach(e -> e.setUpId(req.getId()));

            req.setDownList(this.decorateDownList(req.getDownList()));


            this.checkDownListValidation(req.getDownList());

//            req.getDownList().stream().filter(e -> TopologyType.METER.equals(e.getDownType()));

            int insertCount = siteTopologyRefRwDs.batchInsertSiteTopologyRef(req.getDownList()
                    .stream()
                    .map(e -> (SiteTopologyRefPo) e)
                    .collect(Collectors.toList()));
            log.info("新增关联{}个下级设备", insertCount);
        }
    }

    public void addWrap(TransformerVo req) {
        this.add(req);

        // 新增时判断是否传入可分配容量，以便下发更新指令到网关
        this.sendMsgByDownList(req, null, req.getAssignableCap() != null);
    }

    private void sendMsgByDownList(TransformerVo req, Set<String> extraEvseNoList, boolean update) {
        if(update) {
            log.info("变压器功率分配信息变更,下发变压器更新到网关");
            List<String> evseNos = req.getDownList()
                    .stream()
                    .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
                    .map(SiteTopologyRefVo::getDownNo)
                    .collect(Collectors.toList());

            if(extraEvseNoList != null) {
                evseNos.addAll(extraEvseNoList);
            }

            // TODO 电表下的桩 添加入列表中

            List<EvsePo> evsePoList = evseRoDs.selectByEvseIds(evseNos);

            log.info("受影响的桩关列表: {}", evsePoList);
            if(CollectionUtils.isNotEmpty(evsePoList)) {
                Set<String> gwnos = evsePoList.stream()
                        .map(EvsePo::getGwno)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());

                log.info("受影响的网关列表: {}", gwnos);
                if(CollectionUtils.isNotEmpty(gwnos)) {
                    TransformerUpdateParam param = new TransformerUpdateParam();
                    param.setGwnoList(new ArrayList<>(gwnos)).setTransformerId(req.getId());
                    BaseResponse baseResponse = iotWorkFeign.sendUpdateMsg(param);
                    FeignResponseValidate.check(baseResponse);
                }
            }
        } else {
            log.info("变压器功率分配信息未变更");
        }

    }

    public void sendMsgByTransformerId(Long transformerId) {
        log.info("变压器功率分配信息变更,下发变压器更新到网关");
        List<SiteTopologyRefVo> oldDownList = this.getDownList(transformerId);
        List<String> evseNos = oldDownList
            .stream()
            .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
            .map(SiteTopologyRefVo::getDownNo)
            .collect(Collectors.toList());

        // TODO 电表下的桩 添加入列表中

        List<EvsePo> evsePoList = evseRoDs.selectByEvseIds(evseNos);

        log.info("受影响的桩关列表: {}", evsePoList);
        if(CollectionUtils.isNotEmpty(evsePoList)) {
            Set<String> gwnos = evsePoList.stream()
                .map(EvsePo::getGwno)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

            log.info("受影响的网关列表: {}", gwnos);
            if(CollectionUtils.isNotEmpty(gwnos)) {
                TransformerUpdateParam param = new TransformerUpdateParam();
                param.setGwnoList(new ArrayList<>(gwnos)).setTransformerId(transformerId);
                BaseResponse baseResponse = iotWorkFeign.sendUpdateMsg(param);
                FeignResponseValidate.check(baseResponse);
            }
        }
    }

    /**
     * 启动可分配容量时，检查下游设备是否满足需求
     * @param downList
     * @param req
     */
    private void checkDynamicPower(List<SiteTopologyRefVo> downList,TransformerVo req) {
        if(req.getAssignableCap() != null) {
            List<String> evseNoListForBind = downList
                    .stream()
                    .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
                    .map(SiteTopologyRefVo::getDownNo)
                    .collect(Collectors.toList());

            IotAssert.isTrue(CollectionUtils.isNotEmpty(evseNoListForBind),
                    "功率分配打开时，必须绑桩。");

            List<EvseVo> evseList = redisIotReadService.getEvseList(evseNoListForBind);
            Map<String, EvseVo> evseMap = evseList.stream()
                    .collect(Collectors.toMap(EvseVo::getEvseNo, (o) -> o, (o, n) -> n));
            List<String> disappearList = evseNoListForBind.stream()
                    .filter(e -> evseMap.get(e) == null)
                    .collect(Collectors.toList());
            IotAssert.isTrue(CollectionUtils.isEmpty(disappearList),
                    "找不桩信息: " + CollectionUtils.join(disappearList, ", "));

            List<String> noAcAnd370List = evseMap.values().stream().filter(e ->
                    !Integer.valueOf(IotConstants.PROTOCOL_VERSION_370).equals(e.getProtocolVer()) ||
                            !SupplyType.DC.equals(e.getSupplyType()))
                    .map(EvseVo::getName)
                    .collect(Collectors.toList());
            IotAssert.isTrue(CollectionUtils.isEmpty(noAcAnd370List),
                    "所有绑定的桩应是3.7协议直流桩，这些桩不符合要求: " + CollectionUtils.join(noAcAnd370List, ", "));

            // FIXME 可能需要检查电表下的桩，是否是3.7协议直流桩
        }
    }

    /**
     * 检查下游设备合法性
     * @param downList
     */
    private void checkDownListValidation(List<SiteTopologyRefVo> downList) {
        List<String> evseNoListForBind = downList
                .stream()
                .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
                .map(SiteTopologyRefVo::getDownNo)
                .collect(Collectors.toList());

        this.checkEvseValidation(evseNoListForBind);

        List<Long> meterIdListForBind = downList
                .stream()
                .filter(e -> TopologyType.METER.equals(e.getDownType()))
                .map(SiteTopologyRefVo::getDownId)
                .collect(Collectors.toList());

        this.checkMeterValidation(meterIdListForBind);
    }

    /**
     * 桩合法性判断
     * @param evseNoListForBind
     */
    private void checkEvseValidation(List<String> evseNoListForBind) {
        if(CollectionUtils.isNotEmpty(evseNoListForBind)) {
            List<DeviceMeterPo> byEvseIdInList = evseMeterRoDs.getByEvseIdInList(evseNoListForBind);
            IotAssert.isTrue(CollectionUtils.isEmpty(byEvseIdInList),
                    "已经绑定在电表的桩，无法直接绑定到变压器，桩号: " +
                            byEvseIdInList.stream()
                                    .map(DeviceMeterPo::getDeviceId)
                                    .collect(Collectors.joining(",")));

            List<EvsePo> evsePoList = evseRoDs.selectBindInTransformerByEvseIds(evseNoListForBind);
            IotAssert.isTrue(CollectionUtils.isEmpty(evsePoList),
                    "已经绑定在变压器的桩，无法直接绑定到其他变压器，桩号: " +
                            evsePoList.stream()
                                    .map(EvsePo::getEvseId)
                                    .collect(Collectors.joining(",")));
        }
    }

    /**
     * 电表合法性判断
     * @param meterIdListForBind
     */
    private void checkMeterValidation(List<Long> meterIdListForBind) {
        if(CollectionUtils.isNotEmpty(meterIdListForBind)) {
            List<MeterPo> bindInTransformerByMeterId = meterRoDs.getBindInTransformerByMeterId(meterIdListForBind);
            IotAssert.isTrue(CollectionUtils.isEmpty(bindInTransformerByMeterId),
                    "已经绑定在变压器的电表，无法直接绑定到其他变压器，电表编号: " +
                            bindInTransformerByMeterId.stream()
                                    .map(MeterPo::getNo)
                                    .collect(Collectors.joining(",")));
        }

    }

    private List<SiteTopologyRefVo> decorateDownList(List<SiteTopologyRefVo> downList) {
        if(CollectionUtils.isEmpty(downList)) {
            return List.of();
        }

        return downList.stream().map(e -> {
            if(TopologyType.EVSE.equals(e.getDownType())) {
                EvsePo evse = evseRoDs.getEvse(e.getDownNo());
                if(evse == null) {
                    return null;
                } else {
                    e.setDownId(evse.getId());
                }
            }
            return e;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Transactional
    private Pair<Set<String>, Boolean> edit(TransformerVo req) {


        this.checkDynamicPower(req.getDownList(), req);

        TransformerPo oldTransformer = transformerRoDs.getById(req.getId());

        transformerRwDs.updateTransformer(req);

        List<SiteTopologyRefVo> oldDownList = this.getDownList(req.getId());

        int deleteCount = siteTopologyRefRwDs.deleteAllDown(req.getId());
        log.info("取消关联{}个下级设备", deleteCount);

        if(CollectionUtils.isNotEmpty(req.getDownList())) {
            req.setDownList(this.decorateDownList(req.getDownList()));

            this.checkDownListValidation(req.getDownList());

            req.getDownList().forEach(e -> e.setUpId(req.getId()).setUpType(TopologyType.TRANSFORMER));
            int insertCount = siteTopologyRefRwDs.batchInsertSiteTopologyRef(req.getDownList()
                    .stream()
                    .map(e -> (SiteTopologyRefPo) e)
                    .collect(Collectors.toList()));
            log.info("新增关联{}个下级设备", insertCount);
        }

        Set<String> oldEvseNoList = oldDownList.stream()
                .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
                .map(SiteTopologyRefVo::getDownNo)
                .collect(Collectors.toSet());

        List<SiteTopologyRefVo> newDownList = this.getDownList(req.getId());
        Set<String> newEvseNoList = newDownList.stream()
                .filter(e -> TopologyType.EVSE.equals(e.getDownType()))
                .map(SiteTopologyRefVo::getDownNo)
                .collect(Collectors.toSet());

        // 当可分配容量 或 下游桩列表 变更时
        boolean update = !this.isEqual(oldTransformer.getAssignableCap(), req.getAssignableCap());
        if(update) {
            log.info("可分配容量变更: {} -> {}", oldTransformer.getAssignableCap(), req.getAssignableCap());
        }

        if(!(oldEvseNoList.containsAll(newEvseNoList) && newEvseNoList.containsAll(oldEvseNoList))) {
            log.info("下游桩列表变更: {} -> {}", oldEvseNoList, newEvseNoList);
            update = true;
        }

        return Pair.of(oldEvseNoList, update);
    }

    public void editWrapper(TransformerVo req) {
        Pair<Set<String>, Boolean> edit = this.edit(req);

        this.sendMsgByDownList(req, edit.getFirst(), edit.getSecond());
    }

    private boolean isEqual(BigDecimal b1, BigDecimal b2) {
        if(b1 == null && b2 == null) {
            return true;
        }

        if(b1 != null && b2 == null) {
            return false;
        }

        if(b1 == null && b2 != null) {
            return false;
        }

        return b1.equals(b2);

    }

    @Transactional
    public BaseResponse disable(long id) {
        IotAssert.isTrue(transformerRwDs.delete(id), "删除失败");

        int deleteCount = siteTopologyRefRwDs.deleteAllDown(id);
        log.info("取消关联{}个下级设备", deleteCount);

        return RestUtils.success();
    }

    /**
     * 查询有设置场站功率分配的变压器
     */
    public List<TransformerPo> getTransformerList4SiteDynamicPower(Long transformerId){
        return this.transformerRoDs.getTransformerList4SiteDynamicPower(transformerId);
    }

    /**
     * 查询场站下指定变压器关联的枪头
     * @param siteId
     * @param transformerId 不填为查询场站所有变压器枪头
     * @return
     */
    public ListResponse<TransformerPlugsVo> getPlugListByTransformerId(String siteId, @Nullable Long transformerId) {

        IotAssert.isNotBlank(siteId, "请传入场站id");

        List<TransformerVo> transformerList = transformerRoDs.list(null, transformerId, siteId, 0, 999);
        if(CollectionUtils.isEmpty(transformerList)) {
            return new ListResponse<>();
        }

        List<TransformerPlugsVo> transformerPlugsVoList = transformerList.stream()
                .map(e -> {
                    List<SiteTopologyRefVo> collect = this.getDownList(e.getId());
                    List<String> evseList = collect.stream()
                            .filter(down ->
                                    TopologyType.EVSE.equals(down.getDownType()) || TopologyType.METER.equals(down.getDownType()))
                            .map(down -> {
                                if (TopologyType.EVSE.equals(down.getDownType())) {
                                    return List.of(down.getDownNo());
                                } else {
                                    return down.getDownList()
                                            .stream()
                                            .map(SiteTopologyRefVo::getDownNo)
                                            .collect(Collectors.toList());
                                }
                            }).flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    if(CollectionUtils.isEmpty(evseList)) {
                        return null;
                    }

                    List<String> plugNoListByEvseNoList = plugRoDs.getPlugNoListByEvseNoList(evseList);
                    if (CollectionUtils.isEmpty(plugNoListByEvseNoList)) {
                        return null;
                    }
                    List<PlugVo> plugList = redisIotReadService.getPlugList(plugNoListByEvseNoList);
                    if (CollectionUtils.isEmpty(plugNoListByEvseNoList)) {
                        return null;
                    }

                    TransformerPlugsVo transformerPlugsVo = new TransformerPlugsVo();
                    transformerPlugsVo.setTransformerId(e.getId());
                    transformerPlugsVo.setPlugs(plugList);
                    return transformerPlugsVo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new ListResponse<>(transformerPlugsVoList);
    }

    public ObjectResponse<TransformerPo> getByNo(String no) {
        return new ObjectResponse<>(transformerRoDs.getByNo(no));
    }
}