package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.model.evse.SiteAndPlugBiVo;
import com.cdz360.iot.model.evse.SiteAndPlugBiVoEx;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.cdz360.iot.model.site.po.CommercialPo;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname DeviceBiService
 * @Description
 * @Date 6/23/2020 2:53 PM
 * @Created by Rafael
 */
@Service
public class DeviceBiService {

    @Autowired
    private PlugService plugService;

    @Autowired
    private EvseService evseService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private CommercialService commercialService;

    public CommercialPo getCommById(Long commId) {
        return commercialService.getCommById(commId);
    }

    public SiteAndPlugBiVo getSiteAndPlugStatus(Long commId, String idChain) {
        SiteAndPlugBiParam biParam = new SiteAndPlugBiParam();
        biParam.setSiteStatusList(List.of(2)) // 查询‘已上线’状态站点
            .setCommId(commId)
            .setIdChain(idChain);
        return this.getSiteAndPlugStatus(biParam);
    }

    public SiteAndPlugBiVo getSiteAndPlugStatus(SiteAndPlugBiParam biParam) {

        SiteAndPlugBiVo res = plugService.getSiteAndPlugStatus(biParam);

        Long totalPower = evseService.getTotalPower(biParam);
        res.setPower(BigDecimal.valueOf(totalPower));

        Integer siteCount = siteService.getSiteCount(biParam);
        res.setSiteCount(siteCount);

        Integer totalEvseCount = evseService.getEvseCount(biParam);
        res.setEvseCount(totalEvseCount);

        return res;
    }

    public List<SiteAndPlugBiVoEx> getSiteAndPlugStatusSubComm(Long commId) {

        List<CommercialPo> subCommList = commercialService.getCommByPid(commId);

        List<SiteAndPlugBiVoEx> subCommBiVoList = subCommList.stream().map(e -> {
            SiteAndPlugBiVo res = this.getSiteAndPlugStatus(null, e.getIdChain());

            SiteAndPlugBiVoEx siteAndPlugBiVoEx = new SiteAndPlugBiVoEx();
            BeanUtils.copyProperties(res, siteAndPlugBiVoEx);
            siteAndPlugBiVoEx.setCommId(e.getId());
            return siteAndPlugBiVoEx;
        }).collect(Collectors.toList());

        SiteAndPlugBiVo rootRelative = this.getSiteAndPlugStatus(commId, null);
        SiteAndPlugBiVoEx rootRelativeEx = new SiteAndPlugBiVoEx();
        BeanUtils.copyProperties(rootRelative, rootRelativeEx);
        rootRelativeEx.setCommId(commId);

        // 直属场站的数据单独存成1行
        subCommBiVoList.add(rootRelativeEx);

        return subCommBiVoList;
    }
}