package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.rw.EvseModelRwDs;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.param.ListEvseModelParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class EvseModelService {
    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private PlugRoDs plugRoDs;
    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;
    @Autowired
    private EvseModelRoDs evseModelRoDs;
    @Autowired
    private EvseModelRwDs evseModelRwDs;
    @Autowired
    private RedisIotRwService redisIotRwService;

    public ListResponse<EvseModelPo> listEvseBundlePage(ListEvseModelParam param) {
        return evseModelRoDs.getList(param);
    }

    public BaseResponse addEvseModel(EvseModelPo po) {
        long count = evseModelRoDs.countByCondition(Boolean.TRUE, po.getModel(), po.getBrand(),
                po.getSeries(), po.getSupply(), po.getPower(), po.getPlugNum());
        if (count > 0) {
            throw new DcServiceException("新增失败，设备型号+品牌+系列+电桩类型+额定功率+枪头数量已存在");
        }
        po.setStatus(true)
                .setEnable(true);
        boolean temp = evseModelRwDs.insertOrUpdate(po);
        return temp ? RestUtils.success() : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败");
    }

    public Pair<Boolean, Boolean> editEvseModel(EvseModelPo po) {
        IotAssert.isTrue(po != null && po.getId() != null, "缺少入参");
        boolean changed = false;
        long count = evseModelRoDs.countByCondition(Boolean.TRUE, po.getModel(), po.getBrand(),
                po.getSeries(), po.getSupply(), po.getPower(), po.getPlugNum());
        if (count > 1) {
            throw new DcServiceException("修改失败，设备型号+品牌+系列+电桩类型+额定功率+枪头数量已存在");
        } else if (count == 1) {
            EvseModelPo origin = evseModelRoDs.findByExactFields(po.getModel(), po.getBrand(),
                    po.getSeries(), po.getSupply(), po.getPower(), po.getPlugNum());
            if (!po.getId().equals(origin.getId())) {
                throw new DcServiceException("修改失败，设备型号+品牌+系列+电桩类型+额定功率+枪头数量已存在");
            }
            changed = origin.isConnSupport() != po.isConnSupport();
            changed = origin.isConstantCharge() != po.isConstantCharge() || changed;
        } else {
            // nothing to do
        }
        boolean temp = evseModelRwDs.updateById(po);
        return Pair.of(temp, changed);
    }

    @Async
    public void afterEditEvseModel(EvseModelPo po) {
        // 修改桩枪缓存(constantCharge、constantCharge)
        boolean isConnSupport = po.isConnSupport();
        boolean isConstantCharge = po.isConstantCharge();
        long start = 0;
        final int size = 20;
        List<String> evseNoList = evseRoDs.findByModelId(po.getId(), start, size);
        while (CollectionUtils.isNotEmpty(evseNoList)) {
            evseNoList.forEach(evseNo -> {
                EvseVo evseVo = new EvseVo();
                evseVo.setEvseNo(evseNo)
                        .setConnSupport(isConnSupport);
                evseRwQueryMapper.updateByEvseId(new EvsePo().setEvseId(evseNo).setConnSupport(isConnSupport));
                this.redisIotRwService.updateEvseRedisCache(evseVo);

                plugRoDs.getPlugNoList(evseNo)
                        .forEach(plugNo -> {
                            PlugVo plug = new PlugVo();
                            plug.setEvseNo(evseNo)
                                    .setPlugNo(plugNo)
                                    .setConnSupport(isConnSupport)
                                    .setConstantCharge(isConstantCharge);
                            this.redisIotRwService.updatePlugRedisCache(plug);
                        });

                // 不用推送消息
            });
            start = start + size;
            evseNoList = evseRoDs.findByModelId(po.getId(), start, size);
        }
    }

    public BaseResponse changeStatus(long id, boolean enable) {
        if (Boolean.FALSE.equals(enable)
                && evseModelRoDs.check(id) > 0) {
            throw new DcServiceException("该型号已绑定桩，不可停用");
        }
        boolean temp = evseModelRwDs.changeStatus(id, enable);
        return temp ? RestUtils.success() :
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败");
    }

    public BaseResponse remove(long id) {
        boolean temp = evseModelRwDs.remove(id);
        return temp ? RestUtils.success() :
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败");
    }

    public List<String> getBrandList() {
        return evseModelRoDs.getBrandList();
    }

    public ObjectResponse<EvseModelPo> findById(Long id) {
        return new ObjectResponse<>(evseModelRoDs.findById(id));
    }

}
