package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.model.bi.vo.DeviceBi;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceBiTest extends IotDeviceMgmTestBase {

    @Autowired
    private BiService biService;

    @Test
    public void test_getDeviceBi() {
        DeviceBi bi = biService.getDeviceBi(null, null);
        log.info("bi = {}", bi);

        bi = biService.getDeviceBi("abcd123", null);
        log.info("bi = {}", bi);
    }
}
