package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class TestSiteService extends IotDeviceMgmTestBase {

    private final Logger logger = LoggerFactory.getLogger(TestSiteService.class);

    @Autowired
    private SiteService siteService;

    private String dzId = "";

    @BeforeEach
    public void init() {
        this.dzId = "999999999999";
    }

//    @Test
//    public void test_listSiteDto() {
//        //        logger.info("fakeEvse = {}", fakeEvse);
//        ListSiteParam param = new ListSiteParam();
//        param.setLon(104.078287).setLat(30.652496)//.setCityCode("370100")
//                .setStart(0L).setSize(10).setSk("")
//                .setStart(null).setSize(null);
//        SitePageDto list = this.siteService.listSiteDto(param);
//        logger.debug("size list = {}", JsonUtils.toJsonString(list));
//    }

//    @Test
//    public void insertSite() {
//        SiteDto siteDto = new SiteDto();
//        siteDto.setCityName("shanghai").setStatus(SiteStatus.NORMAL)
//                .setLon(100.0)
//                .setLat(30.0)
//                .setAreaCode("000000")
//                .setProvinceCode("100000")
//                .setAddress("shanghai xinzhuang")
//                .setName("场站名")
//                .setCityCode("200000")
//                .setDzId(dzId);
//        IotAssert.isTrue(siteService.insertIotSite(siteDto) > 0, "新增失败");
//        IotAssert.isNotNull(siteDto.getId(), "新增失败,id 为空");
//    }

//    @Test
//    public void modifySiteInfo(){
//        insertSite();
//        SiteDto siteDto = new SiteDto();
//        siteDto.setCityName("shanghai1111111111").setStatus(SiteStatus.OFF)
//                .setLon(105.0)
//                .setLat(35.0)
//                .setAreaCode("900000")
//                .setProvinceCode("200000")
//                .setAddress("shanghai xinzhuang1111111111")
//                .setName("场站名11111")
//                .setCityCode("300000")
//                .setDzId(dzId);
//
//        IotAssert.isTrue(siteService.updateIotSite(siteDto) > 0, "更新失败");
//    }
//
//    @Test
//    public void modifySiteInfoStatus(){
//        insertSite();
//
//        SiteDto siteDto = siteService.getSiteByDzId(dzId);
//        siteService.updateIotSiteStatus(dzId, SiteStatus.OFF);
//        SiteDto siteDto1 = siteService.getSiteByDzId(dzId);
//        IotAssert.isTrue(!siteDto1.getStatus().equals(siteDto.getStatus()), "状态一致，更新失败");
//    }
}
