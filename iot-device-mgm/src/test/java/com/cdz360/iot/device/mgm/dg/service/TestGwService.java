package com.cdz360.iot.device.mgm.dg.service;

import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper;
import com.cdz360.iot.device.mgm.ds.service.DeviceGwInfoService;
import com.cdz360.iot.model.type.GwStatus;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

//import com.cdz360.iot.device.mgm.dzds.service.CityService;

public class TestGwService extends IotDeviceMgmTestBase {

    private final Logger logger = LoggerFactory.getLogger(TestGwService.class);
    //@BeforeEach
    //public void init() {
    //    this.fakeEvse = this.evseBuilder.build();
    //}Geo
    List<GwStatus> statusList = new ArrayList<>();
    @InjectMocks
    private DeviceGwInfoService deviceGwInfoService;
    //    @Mock
//    private CityService cityService;
    @Mock
    private GwInfoMapper gwInfoMapper;

    //    @Test
//    public void test_listSiteDto() {
//        //        logger.info("fakeEvse = {}", fakeEvse);
//        ListGwParam param = new ListGwParam();
//        statusList.add(GwStatus.UNKNOWN);
//        statusList.add(GwStatus.UNINITIALIZED);
//        statusList.add(GwStatus.NORMAL);
//        statusList.add(GwStatus.ERROR);
//        statusList.add(GwStatus.OFFLINE);
//        statusList.add(GwStatus.OFF);
//        param.setStatusList(statusList).setLon(117.076455).setLat(36.666412)//.setCityCode("370100")
//                .setStart(0L).setSize(10);
//        List<GwInfoDto> list = this.deviceGwInfoService.listGwList(param);
//        logger.debug("size list = {}", JsonUtils.toJsonString(list));
//    }
    @Test
    public void test_configGwRef() {
        Mockito.when(gwInfoMapper.getCountByGwnoAndStatus("abc102")).thenReturn(1);
        Mockito.when(gwInfoMapper.getCountBySiteId("3")).thenReturn(1);
        Mockito.when(gwInfoMapper.logicDelete("abc100", "3")).thenReturn(1);
        Mockito.when(gwInfoMapper.getCountBySiteIdAndTrue("3")).thenReturn(0);
        Mockito.doNothing().when(gwInfoMapper).updateFalseBySiteId("3");
        Mockito.when(gwInfoMapper.insertOrUpdate("abc100", "3")).thenReturn(1);
        this.deviceGwInfoService.configGwRef("abc100", "abc102", "3");
        logger.info(">> status = {}");
    }

//    @Test
//    public void test_cityCode(){
//        Mockito.when(cityService.getCityCode("上海市")).thenReturn(new String());
//        Mockito.when(cityService.getCityCode("上海")).thenReturn(new String());
//        IotAssert.isNotNull(this.deviceGwInfoService.getCityCode("上海市"),"TestGwService.test_cityCode() Unit Test Error");
//        IotAssert.isNotNull(this.deviceGwInfoService.getCityCode("上海"),"TestGwService.test_cityCode() Unit Test Error");
//    }

    @Test
    public void test_getNotBoundGwno() {
        List<String> str = this.deviceGwInfoService.getNotBoundGwno("310100");
        for (String s : str) {
            System.out.println(s);
        }
        logger.info(">> status = {}");
    }
}
