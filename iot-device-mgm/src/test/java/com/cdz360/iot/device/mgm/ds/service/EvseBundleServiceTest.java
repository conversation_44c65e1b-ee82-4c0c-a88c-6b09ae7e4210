package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.EvseBundleContext;
import com.cdz360.iot.model.evse.EvseBundlePc;
import com.cdz360.iot.model.evse.dto.EvseBundleDto;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.type.EvseVendor;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ProjectName iotServer
 * @Description 桩升级单元测试
 * <AUTHOR>
 * @CreateDate 2019/9/25 13:48
 */
@Slf4j
public class EvseBundleServiceTest extends IotDeviceMgmTestBase {
    EvseBundleParam evseBundleParam;
    @Autowired
    private EvseBundleService evseBundleService;
    private EvseBundleContext evseBundleContext;
    @Autowired
    private RedisMgmWrapper redisMgmWrapper;
    /**
     * 自描述文件参数
     */
    private String evseBundleContextJson = "{\n" +
            "    \"protocol\": 1,\n" +
            "    \"ver\": 0,\n" +
            "    \"pcList\": [\n" +
            "        {\n" +
            "            \"type\": \"PC01\",\n" +
            "            \"hw\": 11,\n" +
            "            \"sw\": 322,\n" +
            "            \"order\": 11,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    1,\n" +
            "                    2,\n" +
            "                    3,\n" +
            "                    4\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    320,\n" +
            "                    321\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    11\n" +
            "                ]\n" +
            "            }\n" +
            "        },\n" +
            "        {\n" +
            "            \"type\": \"PC02\",\n" +
            "            \"hw\": 12,\n" +
            "            \"sw\": 393,\n" +
            "            \"order\": 11,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    11\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    1,\n" +
            "                    3,\n" +
            "                    4\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    11\n" +
            "                ]\n" +
            "            }\n" +
            "        },\n" +
            "        {\n" +
            "            \"type\": \"PC03\",\n" +
            "            \"hw\": 14,\n" +
            "            \"sw\": 15,\n" +
            "            \"order\": 12,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    11,\n" +
            "                    12\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    9,\n" +
            "                    13,\n" +
            "                    14\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    13\n" +
            "                ]\n" +
            "            }\n" +
            "        }\n" +
            "    ],\n" +
            "    \"orderMsg\": {\n" +
            "        \"chargingPoleType\": \"G02,G04,G08\",\n" +
            "        \"powerDstbScheme\": {\n" +
            "            \"name\": \"直流接触器功率分配4422方案\",\n" +
            "            \"sn\": 11\n" +
            "        }\n" +
            "    },\n" +
            "    \"releaseNote\": \"1、新增紧急充电卡功能<br />2、修复订单异常bug\",\n" +
            "    \"token\": \"7528a47405ae2b6a0e7e22ad58748806\",\n" +
            "    \"fileName\": \"鼎充平台3.3协议-PC01_v3.22-PC02_v3.93-PC03_v0.15.zip\",\n" +
            "    \"opId\": 1,\n" +
            "    \"opName\": \"默认管理员\",\n" +
            "    \"context\": \"{\\r\\n    \\\"protocol\\\": 1,\\r\\n    \\\"ver\\\": 4,\\r\\n    \\\"pcList\\\": [\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC01\\\",\\r\\n            \\\"hw\\\": 11,\\r\\n            \\\"sw\\\": 322,\\r\\n            \\\"order\\\": 12,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    1,\\r\\n                    2,\\r\\n                    3,\\r\\n                    4\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    320,\\r\\n                    321\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    11\\r\\n                ]\\r\\n            }\\r\\n        },\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC02\\\",\\r\\n            \\\"hw\\\": 12,\\r\\n            \\\"sw\\\": 393,\\r\\n            \\\"order\\\": 11,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    11\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    1,\\r\\n                    3,\\r\\n                    4\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    11\\r\\n                ]\\r\\n            }\\r\\n        },\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC03\\\",\\r\\n            \\\"hw\\\": 14,\\r\\n            \\\"sw\\\": 15,\\r\\n            \\\"order\\\": 11,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    11,\\r\\n                    12\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    9,\\r\\n                    13,\\r\\n                    14\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    13\\r\\n                ]\\r\\n            }\\r\\n        }\\r\\n    ],\\r\\n    \\\"orderMsg\\\": {\\r\\n        \\\"chargingPoleType\\\": \\\"G02,G04,G08\\\",\\r\\n        \\\"powerDstbScheme\\\": {\\r\\n            \\\"name\\\": \\\"直流接触器功率分配4422方案\\\",\\r\\n            \\\"sn\\\": 11\\r\\n        }\\r\\n    },\\r\\n    \\\"releaseNote\\\": \\\"1、新增紧急充电卡功能<br />2、修复订单异常bug\\\"\\r\\n}\\r\\n\",\n" +
            "    \"zipPath\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\\\\8d7226d2-fcd0-4ebd-8330-8711dd0c603b.zip\",\n" +
            "    \"contextFileName\": \"evseBundleContext.json\"\n" +
            "}";

    @BeforeEach
    public void setUp() throws Exception {
        evseBundleContext = JsonUtils.fromJson(evseBundleContextJson, EvseBundleContext.class);
        evseBundleParam = new EvseBundleParam();
        evseBundleParam.setStart(0L);
        evseBundleParam.setSize(5);
        evseBundleParam.setSk("0");
        evseBundleParam.setPageFlag(true);
    }

    @Test
    public void insertEvseBundle() {
        long bundleId = evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);
        log.info("插入的数据主键。bundleId = {}", bundleId);
        // 顺便测试主键查询
        EvseBundle evseBundle = evseBundleService.selectByPrimaryKey(bundleId);
        log.info("插入的数据。evseBundle = {}", evseBundle);
        Assertions.assertTrue(evseBundle != null);
    }

    @Test
    public void selectEvseBundlePcList() {
        long bundleId = evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);
        List<EvseBundlePc> list = evseBundleService.selectEvseBundlePcList(bundleId);
        Assertions.assertTrue(list.size() == 3);
    }

    @Test
    public void listEvseBundlePage() {
        evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);
        ListResponse<EvseBundleDto> listPage = evseBundleService.listEvseBundlePage(evseBundleParam);
        Assertions.assertTrue(listPage.getData().size() >= 1 && listPage.getData().size() <= 5);
    }

    @Test
    public void deleteEvseBundle() {
        long bundleId = evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);
        redisMgmWrapper.cacheEvseBundleProgress(bundleId, 100);
        // 进程100%才可删除
        evseBundleService.deleteEvseBundle(bundleId);
        log.info("数据删除成功");
    }
}