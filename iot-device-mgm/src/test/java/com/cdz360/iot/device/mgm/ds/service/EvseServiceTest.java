package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * * @ProjectName iotServer
 *
 * <AUTHOR>
 * @CreateDate 2019/11/13 17:00
 */
class EvseServiceTest extends IotDeviceMgmTestBase {
    private static final String EVES_NO = "010203040506";
    @Autowired
    private EvseService evseService;

    @Test
    void updateEvsePasscode() {
        ObjectResponse<Long> res = evseService.updateEvsePasscode(EVES_NO, this.generateEvsePasscode());
        Assert.isTrue(res.getStatus() == 0, "error");
    }

    @Test
    void getEvsePasscode() {
        this.updateEvsePasscode();
        ObjectResponse<Long> res = evseService.updateEvsePasscode(EVES_NO, this.generateEvsePasscode());
        Assert.isTrue(res.getStatus() == 0, "error");
    }

    public String generateEvsePasscode() {
        StringBuilder stringBuilder = new StringBuilder();
        String strTable = "1234567890ABCDEF";
        int len = strTable.length();
        while (stringBuilder.length() < 32) {
            int indexR = (int) Math.floor(Math.random() * strTable.length());
            stringBuilder.append(strTable.charAt(indexR));
        }
        return stringBuilder.toString();
    }
}