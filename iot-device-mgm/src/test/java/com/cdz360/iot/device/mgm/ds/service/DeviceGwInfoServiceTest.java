package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.device.mgm.IotDeviceMgmTestMain;
import com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = IotDeviceMgmTestMain.class)
@ExtendWith(SpringExtension.class)
public class DeviceGwInfoServiceTest {
    @Mock
    private GwInfoMapper gwInfoMapper;
    @InjectMocks
    private DeviceGwInfoService deviceGwInfoService;

    @Test
    public void configGwRef() {
        String oldGwno = "GWNO19050800016C";
        String currGwno = "GWNO19050800016A";
        String siteId = "5";
        Mockito.when(gwInfoMapper.getCountByGwnoAndStatus(currGwno)).thenReturn(1);
        Mockito.when(gwInfoMapper.getCountBySiteId(siteId)).thenReturn(1);
        Mockito.when(gwInfoMapper.logicDelete(oldGwno, siteId)).thenReturn(1);
        Mockito.when(gwInfoMapper.getCountBySiteIdAndTrue(siteId)).thenReturn(1);
        deviceGwInfoService.configGwRef(oldGwno, currGwno, siteId);
    }
}