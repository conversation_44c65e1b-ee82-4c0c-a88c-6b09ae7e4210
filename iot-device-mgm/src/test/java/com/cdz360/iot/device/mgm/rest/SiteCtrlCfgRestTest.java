package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.utils.CustomerTestUtils;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class SiteCtrlCfgRestTest extends IotDeviceMgmTestBase {

    private static SiteCtrlCfgPo TEST_PO = null;
    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();

        TEST_PO = CustomerTestUtils.autoInit(SiteCtrlCfgPo.class);
    }

    @Test
    void test_addOrUpdate() throws Exception {
        String url = "/device/mgm/siteCtrlCfg/addOrUpdate";

        String req = JsonUtils.toJsonString(TEST_PO); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(TEST_PO));

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("addOrUpdate")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();

        // 查询是否存在
        url = "/device/mgm/siteCtrlCfg/findByCtrlNo";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("ctrlNum", TEST_PO.getCtrlNum());
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findByCtrlNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void test_findByCtrlNo() throws Exception {
        MockHttpServletRequestBuilder rb = null;
        String url = null;

        String req = JsonUtils.toJsonString(TEST_PO); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(TEST_PO));

        // 查询是否存在
        url = "/device/mgm/siteCtrlCfg/findByCtrlNo";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("ctrlNum", TEST_PO.getCtrlNum());
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findByCtrlNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();

        // 新增
        url = "/device/mgm/siteCtrlCfg/addOrUpdate";
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("addOrUpdate")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();

        // 查询是否存在
        url = "/device/mgm/siteCtrlCfg/findByCtrlNo";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("ctrlNum", TEST_PO.getCtrlNum());
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findByCtrlNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.ctrlNum").value(TEST_PO.getCtrlNum()))
                .andDo(print())
                .andReturn();
    }

    @Test
    void test_send2GetCfg() throws Exception {
        MockHttpServletRequestBuilder rb = null;
        String url = null;

        String req = JsonUtils.toJsonString(TEST_PO); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(TEST_PO));

        // 查询是否存在
        url = "/device/mgm/siteCtrlCfg/send2GetCfg";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("ctrlNum", TEST_PO.getCtrlNum());
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("send2GetCfg")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void test_getBySiteCtrl() throws Exception {
        MockHttpServletRequestBuilder rb = null;
        String url = null;

        String req = JsonUtils.toJsonString(TEST_PO); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(TEST_PO));

        // 查询是否存在
        url = "/device/mgm/siteCtrlCfg/getBySiteCtrl";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("ctrlNum", TEST_PO.getCtrlNum());
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteCtrlCfgRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getBySiteCtrl")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists()) // 不确定是否存在
                .andDo(print())
                .andReturn();
    }
}