package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.ds.mapper.GwLogTmpMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class GwLogTmpMapperTest extends IotDeviceMgmTestBase {
    private final Logger logger = LoggerFactory.getLogger(GwLogTmpMapperTest.class);

    @Autowired
    private GwLogTmpMapper gwLogTmpMapper;

    @Test
    public void getByGwno() {
        List<String> list = gwLogTmpMapper.getGroupGwno();
        Assertions.assertFalse(list.isEmpty());
    }
}