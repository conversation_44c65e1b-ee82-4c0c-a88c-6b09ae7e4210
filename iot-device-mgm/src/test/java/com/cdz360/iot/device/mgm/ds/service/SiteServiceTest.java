package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.data.sync.model.Site;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestMain;
import com.cdz360.iot.device.mgm.ds.mapper.GwInfoMapper;
import com.cdz360.iot.ds.rw.SiteRwDs;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = IotDeviceMgmTestMain.class)
@ExtendWith(SpringExtension.class)
public class SiteServiceTest {
    //    @Mock
//    private SiteFeignClient siteFeignClient;
    @Mock
    private GwInfoMapper gwInfoMapper;
    @Mock
    private SiteRwDs siteRwDs;
    @InjectMocks
    private SiteService siteService;

//    @Test
//    public void modifySiteInfo() {
//        SiteParam siteParam = new SiteParam();
//        BaseResponse dzBaseRes = new BaseResponse();
//        dzBaseRes.setStatus(0);
//
//        Mockito.when(siteFeignClient.updateSiteInfo(siteParam)).thenReturn(dzBaseRes);
//        CommonRpcResponse<Integer> commonRpcResponse = siteService.modifySiteInfo(siteParam);
//        Assertions.assertTrue(commonRpcResponse.getStatus() == 0);
//
//    }

//    @Test
//    public void listSiteDto() {
////        SiteParam siteParam=new SiteParam();
//        ListResponse<DzSiteDto> res = new ListResponse<>();
//        DzSiteListDto dto = new DzSiteListDto();
//        List<DzSiteDto> list = new ArrayList<DzSiteDto>();
//        DzSiteDto d = new DzSiteDto();
//        d.setLocation("31.557800055413583,121.10122230805057");
//        d.setSiteId("20190107357273003557172119");
//        d.setSiteName("测试");
//        d.setAddress("工业南路");
//        d.setProvince(370000);
//        d.setCity(370100);
//        d.setCityName("");
//        d.setImages("http://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/images/yunweiapp/wxfile://tmp_e37ad2fa84c5efb465a4ae7287e7356938764272304b31a0.jpg");
//        d.setType(0);
//        d.setStatus(2);
//        list.add(d);
//        dto.setRows(list);
//        dto.setTotal(1);
//        res.setData(list);
//        res.setTotal(1l);
//        ListSiteParam listSiteParam = new ListSiteParam();
//        listSiteParam.setStart(1l).setSize(10);
//        listSiteParam.setLon(121.3222).setLat(31.0929);
//        Mockito.when(siteFeignClient.getPagedSiteGeoList(Mockito.any(SiteParam.class))).thenReturn(res);
//        SiteDto siteDto = new SiteDto();
//        siteDto.setGwId(9146l).setGwno("GWNO1905060000AB").setGwStatus(GwStatus.NORMAL).setId(6l).setName("CH测试5");
//        siteDto.setCityName("").setImages("http://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/images/yunweiapp/tmp/wxa0ec5b4d4ed913e8.o6zAJs4kqvuaN1M2YvUSRDvgu1NY.mU4RkQkQqowY656ebc40bbb662f2d5330f154fd4716e.jpg")
//                .setDzId("20190227455341116218943782").setAddress("漕河泾园区").setProvinceCode("310000").setCityCode("310100")
//                .setLat(31.0929).setLon(121.3222)
//                .setType(2).setStatus(SiteStatus.NORMAL);
//        Mockito.when(gwInfoMapper.getGwInfoByDzSiteId(Mockito.any(String.class))).thenReturn(siteDto);
//        SitePageDto sitePageDto = siteService.listSiteDto(listSiteParam);
//        IotAssert.isNotNull(sitePageDto.getSiteList().size(), "SiteServiceTest.listSiteDto() Unit Test Error");
//    }

    @Test
    public void syncSiteInfo() {
        Site site = new Site();
        site.setSiteId("123").setName("aaaaa").setCommId(123123l)
                .setProvince("10010").setCity("10010").setArea("10010").setAddress("aaaaa")
                .setStatus(1)
                .setLon(BigDecimal.valueOf(123.12))
                .setLat(BigDecimal.valueOf(123.12));
        Mockito.when(siteRwDs.insertOrUpdate(Mockito.any())).thenReturn(1);
        siteService.syncSiteInfo(site);
    }
}