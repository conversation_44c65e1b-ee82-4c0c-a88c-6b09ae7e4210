package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestMain;
import com.cdz360.iot.device.mgm.model.req.SiteCtrlReq;
import com.cdz360.iot.ds.ro.SiteCtrlRoDs;
import com.cdz360.iot.ds.rw.SiteCtrlRwDs;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = IotDeviceMgmTestMain.class)
@ExtendWith(SpringExtension.class)
class SiteCtrlServiceTest {

    @Mock
    private SiteCtrlRoDs roDs;
    @Mock
    private SiteCtrlRwDs rwDs;
    @InjectMocks
    private SiteCtrlService siteCtrlService;

    private SiteCtrlReq req;

    @BeforeEach
    public void init() {
        req = new SiteCtrlReq() {{
            setId(123l)
                .setNum("asdasdasd")
                .setName("namenamename")
                .setPasscode("passcode");
        }};
    }

    @Test
    void add() {
        Mockito.when(roDs.selectByNum(Mockito.any())).thenReturn(null);
        Mockito.when(rwDs.insertOrUpdate(Mockito.any())).thenReturn(1);
        BaseResponse response = siteCtrlService.add(req);
        IotAssert.isTrue(response.getStatus() == 0, "junit fail");
        System.out.println("response = " + response);
    }

    @Test
    void edit() {
        Mockito.when(rwDs.insertOrUpdate(Mockito.any())).thenReturn(1);
        BaseResponse response = siteCtrlService.edit(req);
        IotAssert.isTrue(response.getStatus() == 0, "junit fail");
    }
}