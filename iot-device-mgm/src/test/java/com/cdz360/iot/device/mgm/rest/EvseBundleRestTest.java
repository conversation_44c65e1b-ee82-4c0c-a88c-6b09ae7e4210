package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.ds.service.EvseBundleService;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.EvseBundleContext;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.type.EvseVendor;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @ProjectName iotServer
 * <AUTHOR>
 * @CreateDate 2019/9/25 14:49
 */
@AutoConfigureMockMvc
@Slf4j
public class EvseBundleRestTest extends IotDeviceMgmTestBase {
    private static final String MGM_PREFIX_URL = "/device/mgm/evsebundle";
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private EvseBundleService evseBundleService;
    private EvseBundleContext evseBundleContext;
    private EvseBundleParam evseBundleParam;
    /**
     * 自描述文件参数
     */
    private String evseBundleContextJson = "{\n" +
            "    \"protocol\": 1,\n" +
            "    \"ver\": 0,\n" +
            "    \"pcList\": [\n" +
            "        {\n" +
            "            \"type\": \"PC01\",\n" +
            "            \"hw\": 11,\n" +
            "            \"sw\": 322,\n" +
            "            \"order\": 11,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    1,\n" +
            "                    2,\n" +
            "                    3,\n" +
            "                    4\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    320,\n" +
            "                    321\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    11\n" +
            "                ]\n" +
            "            }\n" +
            "        },\n" +
            "        {\n" +
            "            \"type\": \"PC02\",\n" +
            "            \"hw\": 12,\n" +
            "            \"sw\": 393,\n" +
            "            \"order\": 11,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    11\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    1,\n" +
            "                    3,\n" +
            "                    4\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    11\n" +
            "                ]\n" +
            "            }\n" +
            "        },\n" +
            "        {\n" +
            "            \"type\": \"PC03\",\n" +
            "            \"hw\": 14,\n" +
            "            \"sw\": 15,\n" +
            "            \"order\": 12,\n" +
            "            \"adaptive\": {\n" +
            "                \"hw\": [\n" +
            "                    11,\n" +
            "                    12\n" +
            "                ],\n" +
            "                \"sw\": [\n" +
            "                    9,\n" +
            "                    13,\n" +
            "                    14\n" +
            "                ],\n" +
            "                \"order\": [\n" +
            "                    13\n" +
            "                ]\n" +
            "            }\n" +
            "        }\n" +
            "    ],\n" +
            "    \"orderMsg\": {\n" +
            "        \"chargingPoleType\": \"G02,G04,G08\",\n" +
            "        \"powerDstbScheme\": {\n" +
            "            \"name\": \"直流接触器功率分配4422方案\",\n" +
            "            \"sn\": 11\n" +
            "        }\n" +
            "    },\n" +
            "    \"releaseNote\": \"1、新增紧急充电卡功能<br />2、修复订单异常bug\",\n" +
            "    \"token\": \"7528a47405ae2b6a0e7e22ad58748806\",\n" +
            "    \"fileName\": \"鼎充平台3.3协议-PC01_v3.22-PC02_v3.93-PC03_v0.15.zip\",\n" +
            "    \"opId\": 1,\n" +
            "    \"opName\": \"默认管理员\",\n" +
            "    \"context\": \"{\\r\\n    \\\"protocol\\\": 1,\\r\\n    \\\"ver\\\": 4,\\r\\n    \\\"pcList\\\": [\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC01\\\",\\r\\n            \\\"hw\\\": 11,\\r\\n            \\\"sw\\\": 322,\\r\\n            \\\"order\\\": 12,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    1,\\r\\n                    2,\\r\\n                    3,\\r\\n                    4\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    320,\\r\\n                    321\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    11\\r\\n                ]\\r\\n            }\\r\\n        },\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC02\\\",\\r\\n            \\\"hw\\\": 12,\\r\\n            \\\"sw\\\": 393,\\r\\n            \\\"order\\\": 11,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    11\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    1,\\r\\n                    3,\\r\\n                    4\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    11\\r\\n                ]\\r\\n            }\\r\\n        },\\r\\n        {\\r\\n            \\\"type\\\": \\\"PC03\\\",\\r\\n            \\\"hw\\\": 14,\\r\\n            \\\"sw\\\": 15,\\r\\n            \\\"order\\\": 11,\\r\\n            \\\"adaptive\\\": {\\r\\n                \\\"hw\\\": [\\r\\n                    11,\\r\\n                    12\\r\\n                ],\\r\\n                \\\"sw\\\": [\\r\\n                    9,\\r\\n                    13,\\r\\n                    14\\r\\n                ],\\r\\n                \\\"order\\\": [\\r\\n                    13\\r\\n                ]\\r\\n            }\\r\\n        }\\r\\n    ],\\r\\n    \\\"orderMsg\\\": {\\r\\n        \\\"chargingPoleType\\\": \\\"G02,G04,G08\\\",\\r\\n        \\\"powerDstbScheme\\\": {\\r\\n            \\\"name\\\": \\\"直流接触器功率分配4422方案\\\",\\r\\n            \\\"sn\\\": 11\\r\\n        }\\r\\n    },\\r\\n    \\\"releaseNote\\\": \\\"1、新增紧急充电卡功能<br />2、修复订单异常bug\\\"\\r\\n}\\r\\n\",\n" +
            "    \"zipPath\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\\\\8d7226d2-fcd0-4ebd-8330-8711dd0c603b.zip\",\n" +
            "    \"contextFileName\": \"evseBundleContext.json\"\n" +
            "}";

    public static String getUrl(String url) {
        return MGM_PREFIX_URL.concat("/" + url);
    }

    @BeforeEach
    public void init() {
        evseBundleContext = JsonUtils.fromJson(evseBundleContextJson, EvseBundleContext.class);
        evseBundleParam = new EvseBundleParam();
        evseBundleParam.setStart(0L);
        evseBundleParam.setSize(5);
        evseBundleParam.setSk("0");
        evseBundleParam.setPageFlag(true);
    }

    @Test
    @SneakyThrows
    public void deleteEvseBundle() {
        // 造数据
        long bundleId = evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);
        RequestBuilder builder = MockMvcRequestBuilders.get(getUrl("deleteEvseBundle"))
                .param("id", bundleId + "");

        String resBody = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("response = {}", resBody);
        BaseResponse res = JsonUtils.fromJson(resBody, new TypeReference<BaseResponse>() {
        });
        Assertions.assertTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS);
    }

    @Test
    @SneakyThrows
    public void listEvseBundlePage() {
        // 造数据
        evseBundleService.insertEvseBundle(evseBundleContext, 0, EvseVendor.TOPOWER);

        RequestBuilder builder = MockMvcRequestBuilders.post(getUrl("listEvseBundlePage"))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .content(JsonUtils.toJsonString(evseBundleParam));

        String resBody = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("response = {}", resBody);
        ListResponse res = JsonUtils.fromJson(resBody, new TypeReference<ListResponse<EvseBundle>>() {
        });
        Assertions.assertTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS);
        Assertions.assertTrue(res.getData().size() >= 1 && res.getData().size() <= 5);
    }
}