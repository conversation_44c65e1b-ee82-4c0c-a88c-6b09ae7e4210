package com.cdz360.iot.device.mgm.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.device.mgm.IotDeviceMgmTestBase;
import com.cdz360.iot.device.mgm.ds.service.EvseService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @ProjectName iotServer
 * <AUTHOR>
 * @CreateDate 2019/9/25 14:49
 */
@AutoConfigureMockMvc
@Slf4j
public class EvseRestTest extends IotDeviceMgmTestBase {
    private static final String MGM_PREFIX_URL = "/device/mgm/evse";
    private static final String EVES_NO = "010203040506";
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private EvseService evseService;

    public static String getUrl(String url) {
        return MGM_PREFIX_URL.concat("/" + url);
    }

    @Test
    @SneakyThrows
    public void updateEvsePasscode() {

        RequestBuilder builder = MockMvcRequestBuilders.get(getUrl("updateEvsePasscode"))
                .param("evseNo", EVES_NO)
                .param("passcode", this.generateEvsePasscode());

        String resBody = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("response = {}", resBody);
        ObjectResponse res = JsonUtils.fromJson(resBody, new TypeReference<>() {
        });
        Assertions.assertTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS);
    }

    @Test
    @SneakyThrows
    public void getEvsePasscode() {
        // 造数据
        this.evseService.updateEvsePasscode(EVES_NO, this.generateEvsePasscode());

        RequestBuilder builder = MockMvcRequestBuilders.get(getUrl("getEvsePasscode"))
                .param("evseNo", EVES_NO);

        String resBody = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("response = {}", resBody);
        ObjectResponse res = JsonUtils.fromJson(resBody, new TypeReference<>() {
        });
        Assertions.assertTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS);
    }

    public String generateEvsePasscode() {
        StringBuilder stringBuilder = new StringBuilder();
        String strTable = "1234567890ABCDEF";
        int len = strTable.length();
        while (stringBuilder.length() < 32) {
            int indexR = (int) Math.floor(Math.random() * strTable.length());
            stringBuilder.append(strTable.charAt(indexR));
        }
        return stringBuilder.toString();
    }
}