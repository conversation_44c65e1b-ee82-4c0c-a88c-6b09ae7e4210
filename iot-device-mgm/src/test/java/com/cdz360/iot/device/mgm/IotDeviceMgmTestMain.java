package com.cdz360.iot.device.mgm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication
@EnableTransactionManagement
@EnableDiscoveryClient(autoRegister = true)
@EnableFeignClients(basePackages = { "com.cdz360.iot.*.feign", "com.cdz360.iot.device.mgm.dzds.client"})

public class IotDeviceMgmTestMain {
    private final static Logger logger = LoggerFactory.getLogger(IotDeviceMgmTestMain.class);

    public static void main(String[] args) {
        logger.info("starting....");
        SpringApplication.run(IotDeviceMgmTestMain.class, args);
        logger.info("started");
    }
}