package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Classname Ys7OauthTokenRes
 * @Description
 * @Date 8/23/2021 9:11 AM
 * @Created by Rafael
 */
@Data
@Schema(description = "萤石云母账户生成access_token返回数据")
public class Ys7OauthTokenRes {
    @JsonProperty(value = "accessToken")
    @Schema(description = "访问令牌")
    private String accessToken;

    @JsonProperty(value = "expireTime")
    @Schema(description = "unix时间戳（毫秒）")
    private Long expireTime;

}