package com.cdz360.iot.camera.client;

import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.camera.cfg.HkConfig;
import com.cdz360.iot.camera.model.*;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

@Slf4j
@Service
@Deprecated
public class HkClient {
    private static final String HK_TOKEN = "HK_TOKEN_";

    private static final ConcurrentHashMap<String, OauthTokenRes> TOKEN_MAP = new ConcurrentHashMap<>();

    private static final Predicate<HttpStatusCode> UNAUTHORIZED_HTTP_STATUS = HttpStatus.UNAUTHORIZED::equals;

    public void init(Map<String, OauthTokenRes> ctx) {
        if(ctx != null) {
            ctx.entrySet().forEach(e -> {
                TOKEN_MAP.put(this.key(e.getKey()), e.getValue());
            });
        }
    }

    @Autowired
    private HkConfig hkConfig;

    public Mono<HkResMsg<HkListResMsg<HkCamera>>> syncCameraList(CameraListParam param) {
        String qp = StringUtils.isNotBlank(param.getStoreId()) ?
                "?storeId={storeId}&pageNo={pageNo}&pageSize={pageSize}" :
                "?storeNo={storeNo}&pageNo={pageNo}&pageSize={pageSize}";

//        return this.retrieveWithAuthGet(
//                hkConfig.getCameraListUri() + qp, param)
//                .toEntity(String.class)
//                .doOnNext(res -> log.info("获取摄象机信息: {}", res.getBody()))
//                .map(HttpEntity::getBody)
//                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkListResMsg<HkCamera>>>() {
//                }))
//                .onErrorMap(e -> new DcServerException("获取摄象机信息失败"));
        return null;
    }

    public Mono<HkResMsg<HkListResMsg<HkStore>>> syncStore(String clientId, HkListReqMsg param) {
        return this.retrieveWithAuthGet(
                hkConfig.getStoreInfoUri() + "?pageNo={pageNo}&pageSize={pageSize}", param, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取门店信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkListResMsg<HkStore>>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取门店信息失败"));
    }
    public Mono<HkResMsg<HkListResMsg<HkCamera>>> syncCamera(String clientId, HkListReqMsg param) {
        String qp = StringUtils.isNotBlank(clientId) ?
                "?storeId={storeId}&pageNo={pageNo}&pageSize={pageSize}" :
                "?storeNo={storeNo}&pageNo={pageNo}&pageSize={pageSize}";

        return this.retrieveWithAuthGet(
                hkConfig.getCameraListUri() + qp, param, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取摄象机信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkListResMsg<HkCamera>>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取摄象机信息失败"));
    }

    public Mono<HkResMsg<List<HkLiveOpen>>> openLiveVideo(String clientId, String channelId) {
        String qp = "?channelIds=" + channelId;

        return this.retrieveWithAuthPost(
                        hkConfig.getLiveVideoOpen() + qp, null, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("开通标准流预览: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<List<HkLiveOpen>>>() {
                }))
                .onErrorMap(e -> new DcServerException("开通标准流预览失败"));
    }

    public Mono<HkResMsg<HkAddressLimit>> syncCameraLiveAddress(String clientId,
                                                           String channelId,
                                                           long expireTime) {
        String qp;
        if (expireTime > 0) {
            qp = "?channelId=" + channelId + "&expireTime=" + expireTime;
        } else {
            qp = "?channelId=" + channelId;
        }

        return this.retrieveWithAuthPost(
                        hkConfig.getLiveAddressLimit() + qp, null, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取摄象机直播地址: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkAddressLimit>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取摄象机直播地址失败"));
    }

    public Mono<HkResMsg<HkCameraCapture>> syncCameraCaptureUrl(String clientId,
                                                               String deviceSerial,
                                                               Integer channelNo,
                                                               int quality) {
        String qp = String.format("?deviceSerial=%s&channelNo=%d&quality=%d", deviceSerial, channelNo, quality);

        return this.retrieveWithAuthPost(
                        hkConfig.getCapture() + qp, null, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取摄象机抓图: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkCameraCapture>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取摄象机抓图失败"));
    }

    public Mono<HkResMsg<HkVideoUrl>> getCameraVideoUrl(String clientId, CameraVideoParam param) {
        String qp = "?deviceSerial={deviceSerial}&channelNo={channelNo}&quality={quality}&protocol={protocol}" +
                "&expireTime={expireTime}&type={type}&startTime={startTimeStr}&stopTime={stopTimeStr}";

        return this.retrieveWithAuthGet(
                        hkConfig.getGetVideoUrl() + qp, param, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取摄象机回放地址: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkVideoUrl>>() {
                }))
                .onErrorMap(e -> {
                    log.error("获取摄象机回放地址失败 = {}", e.getMessage(), e);
                    return new DcServerException("获取摄象机回放地址失败");
                });
    }

//    public Mono<HkResMsg<HkListResMsg<HkCamera>>> liveAddressLimit(String clientId, HkLiveAddressReqMsg param) {
//        String qp = StringUtils.isNotBlank(clientId) ?
//                "?storeId={storeId}&pageNo={pageNo}&pageSize={pageSize}" :
//                "?storeNo={storeNo}&pageNo={pageNo}&pageSize={pageSize}";
//
//        return this.retrieveWithAuthGet(
//                hkConfig.getCameraListUri() + qp, param, this.authorization(clientId))
//                .toEntity(String.class)
//                .doOnNext(res -> log.info("获取摄象机信息: {}", res.getBody()))
//                .map(HttpEntity::getBody)
//                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<HkListResMsg<HkCamera>>>() {
//                }))
//                .onErrorMap(e -> new DcServerException("获取摄象机信息失败"));
//    }

//    public Mono<OauthTokenRes> oauthToken() {
//        OauthTokenReq req = OauthTokenReq.builder()
//                .clientId(hkConfig.getClientId())
//                .clientSecret(hkConfig.getClientSecret())
//                .grantType("client_credentials")
//                .build();
//
//        return this.retrieve(hkConfig.getOauthTokenUri(), req)
//                .toEntity(OauthTokenRes.class)
//                .doOnNext(res -> log.info("获取token信息: {}", res.getBody()))
//                .map(HttpEntity::getBody)
//                .doOnNext(auth -> TOKEN_MAP.put(key(hkConfig.getClientId()), auth))
//                .onErrorMap(e -> new DcServerException("获取token失败"));
//    }

    public Mono<OauthTokenRes> oauthToken(String clientId, String clientSecret, String grantType, String scope) {
        OauthTokenReq req = OauthTokenReq.builder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType(grantType)
                .scope(scope)
                .build();

        return this.retrieve(hkConfig.getOauthTokenUri(), req)
                .toEntity(OauthTokenRes.class)
                .doOnNext(res -> log.info("获取token信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .doOnNext(auth -> TOKEN_MAP.put(key(clientId), auth))
                .onErrorMap(e -> new DcServerException("获取token失败: " + clientId));
    }

    public Mono<HkResMsg<AccountInfoRes>> accountInfo(String clientId) {

        return this.retrieveWithAuthGet(
                        hkConfig.getAccountInfo(), null, this.authorization(clientId))
                .toEntity(String.class)
                .doOnNext(res -> log.info("取流认证信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<HkResMsg<AccountInfoRes>>() {
                }))
                .onErrorMap(e -> new DcServerException("取流认证信息失败: " + clientId));
    }

    private WebClient.ResponseSpec retrieve(String uri, Object body) {
        WebClient client = WebClient.create(hkConfig.getHost());
        return client.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(object2MultiValue(body)))
                .retrieve();
    }

//    private WebClient.ResponseSpec retrieveWithAuth(String uri, Object body, String clientId) {
//        WebClient client = WebClient.create(hkConfig.getHost());
//        return client.post()
//                .uri(uri)
//                .header("Authorization", this.authorization(clientId))
//                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
//                .body(BodyInserters.fromFormData(object2MultiValue(body)))
//                .retrieve()
//                .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
//                    log.debug("token 已失效: {}.", res);
//                    oauthToken().subscribe();
//                    return Mono.error(new DcServerException("token 已失效"));
//                });
//    }

//    private WebClient.ResponseSpec retrieveWithAuthGet(String uri, Object param) {
//        WebClient client = WebClient.create(hkConfig.getHost());
//        return client.get()
//                .uri(uri, object2Map(param))
//                .header("Authorization", this.authorization())
//                .retrieve()
//                .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
//                    log.debug("token 已失效: {}.", res);
//                    oauthToken().subscribe();
//                    return Mono.error(new DcServerException("token 已失效"));
//                });
//    }

    private WebClient.ResponseSpec retrieveWithAuthGet(String uri, Object param, String auth) {
        WebClient client = WebClient.create(hkConfig.getHost());
        if(param == null) {
            return client.get()
                    .uri(uri)
                    .header("Authorization", auth)
                    .retrieve()
//                .onStatus(HttpStatus.UNAUTHORIZED::equals, res -> {
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        } else {
            return client.get()
                    .uri(uri, object2Map(param))
                    .header("Authorization", auth)
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        }
    }

    private WebClient.ResponseSpec retrieveWithAuthPost(String uri, Object param, String auth) {
        WebClient client = WebClient.create(hkConfig.getHost());
        if(param == null) {
            return client.post()
                    .uri(uri)
                    .header("Authorization", auth)
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        } else {
            return client.post()
                    .uri(uri, object2Map(param))
                    .header("Authorization", auth)
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        }
    }

    private String authorization (String clientId) {
        String key = this.key(clientId);
        OauthTokenRes s = TOKEN_MAP.get(key);
        if (s != null) {
            OauthTokenRes oauthToken = s;
            return oauthToken.getTokenType() + " " + oauthToken.getAccessToken();
        }

        return "";
    }

    private String key(String clientId) {
        return HK_TOKEN + clientId;
    }

    private static MultiValueMap<String, String> object2MultiValue(Object context) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(object2Map(context));
        return formData;
    }

    private static Map<String, String> object2Map(Object context) {
        return new ObjectMapper()
                .convertValue(context, new TypeReference<>() {});
    }
}
