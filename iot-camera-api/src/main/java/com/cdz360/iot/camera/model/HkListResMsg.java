package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class HkListResMsg<R> {
    @Schema(description = "当前页数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer pageNo;

    @Schema(description = "每页数据记录数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer pageSize;

    @Schema(description = "查询数据记录总数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long total;

    @Schema(description = "总共页数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer totalPage;

    @Schema(description = "是否有下一页")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hasNextPage;

    @Schema(description = "是否有上一页")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hasPreviousPage;

    @Schema(description = "是否首页")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isfirstPage;

    @Schema(description = "是否尾页")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean islastPage;

    @Schema(description = "对象列表，若未查到记录，空集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<R> rows;
}
