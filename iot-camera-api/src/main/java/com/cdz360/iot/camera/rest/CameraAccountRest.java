package com.cdz360.iot.camera.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.camera.service.CameraAccountService;
import com.cdz360.iot.camera.service.CameraService;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @Classname CameraAccountRest
 * @Description
 * @Date 7/28/2021 11:00 AM
 * @Created by Rafael
 */
@Slf4j
@RestController
@Tag(name = "监控摄像设备主体账号", description = "监控摄像设备主体账号")
public class CameraAccountRest {
    @Autowired
    private CameraAccountService cameraAccountService;

//    @Operation(summary = "获取所有主体账号")
//    @GetMapping(value = "/inner/camera/getAllCameraAccount")
//    public Mono<ListResponse<CameraAccountPo>> getAllCameraAccount() {
//        log.info("获取场站摄像设备列表");
//        return cameraAccountService.siteCameraAccountList();
//    }

//    @Operation(summary = "刷新主体账号token")
//    @PostMapping(value = "/inner/camera/flushAccessToken")
//    public Mono<ObjectResponse> flushAccessToken(
//            @Parameter(name = "账号id", required = true) @RequestParam(value = "id") Long id) {
//        log.info("刷新主体账号token, id = {}", id);
//        return cameraAccountService.flushAccessToken(id);
//    }

//    @Operation(summary = "刷新取流认证信息")
//    @PostMapping(value = "/inner/camera/flushStreamAccessToken")
//    public Mono<ObjectResponse> flushStreamAccessToken(
//            @Parameter(name = "账号id", required = true) @RequestParam(value = "id") Long id) {
//        log.info("刷新主体账号token, id = {}", id);
//        return cameraAccountService.flushStreamAccessToken(id);
//    }

}