package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.HkClient;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.HkResMsg;
import com.cdz360.iot.camera.model.OauthTokenRes;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.rw.CameraAccountRwDs;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Classname CameraAccountService
 * @Description
 * @Date 7/28/2021 11:01 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class CameraAccountService {
    @Autowired
    private HkClient hkClient;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;
    @Autowired
    private CameraAccountRwDs cameraAccountRwDs;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final AtomicInteger Counter = new AtomicInteger();

    @Value("${camera.account.accountExpireTime:6}")
    private Integer accountExpireTime;

    @PostConstruct
    private void init() {
        List<CameraAccountPo> all = cameraAccountRoDs.getAll();
        if(CollectionUtils.isNotEmpty(all)) {
            Map<String, OauthTokenRes> collect = all.stream()
                    .filter(e -> e.getType() != null && e.getType() == CameraConstant.YUNMOU_TYPE)
                    .collect(Collectors.toMap(CameraAccountPo::getClientId, CameraAccountService::convert2OauthTokenRes));
            hkClient.init(collect);
        }
    }

    public Mono<ListResponse<CameraAccountPo>> siteCameraAccountList() {

        return Mono.just(cameraAccountRoDs.getAll()).map(RestUtils::buildListResponse);
    }
    public Mono<ObjectResponse> flushAccessToken(Long id) {
        CameraAccountPo byId = cameraAccountRoDs.getById(id);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        return hkClient.oauthToken(byId.getClientId(), byId.getClientSecret(), byId.getGrantType(), byId.getScope())
                .doOnNext(e -> {

                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date());
                    calendar.add(Calendar.SECOND, Integer.parseInt(e.getExpiresIn()));

                    byId.setAccessToken(e.getAccessToken())
                            .setTokenType(e.getTokenType())
                            .setScope(e.getScope())
                            .setExpiresIn(calendar.getTime());

                    cameraAccountRwDs.updateCameraAccount(byId);
                })
                .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse> flushStreamAccessToken(Long id) {
        CameraAccountPo byId = cameraAccountRoDs.getById(id);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        return hkClient.accountInfo(byId.getClientId())
                .map(HkResMsg::getData)
                .doOnNext(e -> {

                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date());
                    calendar.add(Calendar.DATE, accountExpireTime);

                    byId.setAccountAppKey(e.getAppKey())
                            .setAccountToken(e.getToken())
                            .setAccountExpireTime(calendar.getTime());

                    cameraAccountRwDs.updateCameraAccount(byId);
                })
                .map(RestUtils::buildObjectResponse);
    }

    private static OauthTokenRes convert2OauthTokenRes(CameraAccountPo cameraAccountPo) {
        OauthTokenRes ret = new OauthTokenRes();
        BeanUtils.copyProperties(cameraAccountPo, ret);
        return ret;
    }

    public static String getRandomSubAccountName() {
        String prefix = LocalDateTime.now().format(DATE_FORMATTER);
        int i = Counter.addAndGet(1);
        if(i >= 999) {
            Counter.set(0);
        }
        return String.format("%s%04d", prefix, i);
    }

    public static String getSubAccountPassword(String clientId, String defaultPassword) {
        String password = String.format("%s#%s", clientId.toLowerCase(), defaultPassword);
        return DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8))
                .toLowerCase();
    }

//    public static void main(String[] args) {
//        String randomSubAccountName = getRandomSubAccountName();
//        String subAccountPassword = getSubAccountPassword("00c56778b10e4777bb9ce931b76e203a", "654321");
//        System.out.println(randomSubAccountName);
//        System.out.println(subAccountPassword);
//    }

}