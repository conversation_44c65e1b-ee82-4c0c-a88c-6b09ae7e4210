package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7DeviceInfo
 * @Description
 * @Date 9/28/2021 3:58 PM
 * @Created by Rafael
 */
@Schema(description = "萤石云录像机设备信息")
@Data
@Accessors(chain = true)
public class Ys7DeviceInfo {
    private String deviceSerial;        // 设备序列号
    private String deviceName;          // 设备名称
    private String model;               // 设备型号，如CS-C2S-21WPFR-WX
    private Integer status;             // 在线状态：0-不在线，1-在线
    private Integer defence;            // 具有防护能力的设备布撤防状态：0-睡眠，8-在家，16-外出，普通IPC布撤防状态：0-撤防，1-布防
    private Integer isEncrypt;          // 是否加密：0-不加密，1-加密
    private Integer alarmSoundMode;     // 告警声音模式：0-短叫，1-长叫，2-静音
    private Integer offlineNotify;      // 设备下线是否通知：0-不通知 1-通知
    private String category;            // 设备大类
    private String netType;             // 网络类型，如有线连接wire
    private String signal;              // 信号强度(%)
}