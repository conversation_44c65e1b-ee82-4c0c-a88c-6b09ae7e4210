package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7Camera
 * @Description
 * @Date 8/23/2021 3:33 PM
 * @Created by Rafael
 */
@Schema(description = "萤石云摄象机信息")
@Data
@Accessors(chain = true)
public class Ys7Camera {
    private String id;
    private String deviceSerial;
    private Integer channelNo;
    private String channelName;
    private Integer status;
    private String isShared;
    private String picUrl;
    private Integer isEncrypt;
    private Integer videoLevel;
    private Integer permission;
    private Integer isAdd;
}