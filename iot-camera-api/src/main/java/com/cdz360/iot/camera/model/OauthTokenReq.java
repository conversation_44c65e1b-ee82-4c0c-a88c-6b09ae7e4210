package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
public class OauthTokenReq {

    @JsonProperty(value = "client_id")
    @Schema(description = "客户端ID", required = true)
    private String clientId;

    @JsonProperty(value = "client_secret")
    @Schema(description = "访问密钥", required = true)
    private String clientSecret;

    @JsonProperty(value = "grant_type")
    @Schema(description = "认证模式: 目前仅支持client_credentials", required = true)
    private String grantType;

    @JsonProperty(value = "scope")
    @Schema(description = "权限范围")
    private String scope;
}
