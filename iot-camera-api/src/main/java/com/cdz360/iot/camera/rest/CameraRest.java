package com.cdz360.iot.camera.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.camera.model.HkVideoUrl;
import com.cdz360.iot.camera.service.CameraFacadeService;
import com.cdz360.iot.camera.service.CameraService;
import com.cdz360.iot.camera.service.ICameraService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.vo.CameraVo;
import com.cdz360.iot.model.camera.vo.OpInitCameraVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "监控摄像设备相关接口", description = "监控摄像设备相关接口")
public class CameraRest {

    @Autowired
    private CameraService cameraService;

    @Autowired
    private CameraFacadeService cameraFacadeService;

    @Operation(summary = "获取场站摄像设备列表")
    @GetMapping(value = "/inner/camera/siteCameraList")
    public Mono<ListResponse<CameraVo>> siteCameraList(@RequestBody ListCameraParam param) {
        log.info("获取场站摄像设备列表: siteId = {}", JsonUtils.toJsonString(param));
        return cameraService.siteCameraList(param);
    }

    @Operation(summary = "同步摄像头信息")
    @PostMapping(value = "/inner/camera/syncCamera")
    public Mono<BaseResponse> syncCamera(
            @Parameter(name = "门店id", required = true) @RequestParam(value = "id") Long storeId) {
        log.info("同步摄像头信息: storeId = {}", storeId);
        ICameraService cs = cameraFacadeService.getCameraServiceByStoreId(storeId);
        IotAssert.isNotNull(cs, "场站对应的母账号类型不正确. storeId: " + storeId);
//        return cameraService.syncCamera(storeId);
        return cs.syncCamera(storeId, 0);
    }

    @Operation(summary = "同步摄像头信息,siteId")
    @PostMapping(value = "/inner/camera/syncCameraBySiteId")
    public Mono<BaseResponse> syncCameraBySiteId(
            @Parameter(name = "场站id", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("同步摄像头信息: siteId = {}", siteId);
        return cameraFacadeService.syncCameraBySiteId(siteId);
//        return cs.syncCamera(siteId);
    }

    @Operation(summary = "同步商户下所有摄像头信息,idChain")
    @PostMapping(value = "/inner/camera/syncCameraByIdChain")
    public Mono<BaseResponse> syncCameraByIdChain(
            @Parameter(name = "商户id chain", required = true) @RequestParam(value = "idChain") String idChain) {
        log.info("同步商户下所有摄像头信息: idChain = {}", idChain);
        IotAssert.isNotBlank(idChain, "请传入参数");
        return cameraFacadeService.syncCameraByIdChain(idChain);
    }

//    @Schema(description = "刷新场站摄像头直播地址")// FIXME 目前仅支持云眸
//    @PostMapping(value = "/inner/camera/flushCameraLiveAddress")
//    public Mono<BaseResponse> flushCameraLiveAddress(
//            @Parameter(name = "摄像头id", required = true) @RequestParam(value = "cameraId") Long cameraId) {
//        log.info("刷新场站摄像头直播地址: cameraId = {}", cameraId);
//        return cameraService.openLiveVideo(cameraId)
//                .flatMap(e -> cameraService.flushCameraLiveAddress(cameraId));
//    }

//    @Schema(description = "刷新场站摄像头抓图")// FIXME 目前仅支持云眸
//    @PostMapping(value = "/inner/camera/flushCameraCaptureUrl")
//    public Mono<BaseResponse> flushCameraCaptureUrl(
//            @Parameter(name = "摄像头id", required = true) @RequestParam(value = "cameraId") Long cameraId) {
//        log.info("刷新场站摄像头抓图: cameraId = {}", cameraId);
//        return cameraService.flushCameraCaptureUrl(cameraId);
//    }

    @Schema(description = "获取场站摄像头回放")
    @PostMapping(value = "/inner/camera/getCameraVideoUrl")
    public Mono<ObjectResponse<HkVideoUrl>> getCameraVideoUrl(@RequestBody CameraVideoParam param) {
        log.info("获取场站摄像头回放: param = {}", JsonUtils.toJsonString(param));
//        return cameraService.getCameraVideoUrl(param);
        Mono<ObjectResponse<HkVideoUrl>> ret = cameraFacadeService.getCameraVideoUrl(param);
        IotAssert.isNotNull(ret, "通道对应的母账号类型不正确. storeId: " + param.getCameraId());
        return ret;
    }

    @Schema(description = "编辑摄像头通道信息")
    @PostMapping(value = "/inner/camera/updateChannel")
    public Mono<ObjectResponse<Boolean>> updateChannel(@RequestBody CameraVo param) {
        log.info("编辑摄像头通道信息: param = {}", JsonUtils.toJsonString(param));
        return cameraFacadeService.updateChannel(param);
    }

    @Schema(description = "获取定制大屏初始摄像头地址资源")
    @GetMapping(value = "/inner/camera/getInitLiveAddressUseForOp")
    public Mono<ListResponse<OpInitCameraVo>> getInitLiveAddressUseForOp() {
        log.info("getHlsListUseForOp");
        return cameraFacadeService.getInitLiveAddressUseForOp();
    }
}
