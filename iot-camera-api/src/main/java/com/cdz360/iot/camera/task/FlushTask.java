package com.cdz360.iot.camera.task;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.service.CameraAccountService;
import com.cdz360.iot.camera.service.CameraService;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRoDs;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname FlushTask
 * @Description
 * @Date 8/2/2021 9:40 AM
 * @Created by Rafael
 */
@Slf4j
//@Component
@Deprecated
public class FlushTask {

    @Autowired
    private CameraAccountService cameraAccountService;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraService cameraService;

    @Autowired
    private CameraRoDs cameraRoDs;

    // 定时刷新取流token
    @Scheduled(initialDelay = 4000, fixedRate = 10000)
    public void flushStreamAccessToken() {

        try {
            List<CameraAccountPo> accountPos = cameraAccountRoDs.getAccountExpireByTime(getBufferTime(30), CameraConstant.YUNMOU_TYPE);
            if(CollectionUtils.isNotEmpty(accountPos)) {
                log.info("flushStreamAccessToken: {}", accountPos.stream()
                        .map(CameraAccountPo::getId)
                        .collect(Collectors.toSet()));
                accountPos.stream().forEach(e -> {
                    cameraAccountService.flushStreamAccessToken(e.getId()).subscribe();
                });
            } else {
                log.debug("flushStreamAccessToken skip");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新访问token
    @Scheduled(initialDelay = 8000, fixedRate = 10000)
    public void flushAccessToken() {

        try {
            List<CameraAccountPo> accountPos = cameraAccountRoDs.getAccessTokenExpireByTime(getBufferTime(30), CameraConstant.YUNMOU_TYPE);
            if(CollectionUtils.isNotEmpty(accountPos)) {
                log.info("flushAccessToken: {}", accountPos.stream()
                        .map(CameraAccountPo::getId)
                        .collect(Collectors.toSet()));
                accountPos.stream().forEach(e -> {
                    cameraAccountService.flushAccessToken(e.getId()).subscribe();
                });
            } else {
                log.debug("flushAccessToken skip");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新取流相机抓图
    @Scheduled(initialDelay = 10000, fixedRateString = "${camera.taskRate.flushCameraCaptureUrl:60000}")
    public void flushCameraCaptureUrl() {

        try {
            ListCameraParam param = new ListCameraParam();
            param.setType(CameraConstant.YUNMOU_TYPE);
            List<CameraVo> cameraVos = cameraRoDs.getBySiteId(param);

            if(CollectionUtils.isNotEmpty(cameraVos)) {
                log.info("flushCameraCaptureUrl: {}", cameraVos.stream()
                        .map(CameraVo::getId)
                        .collect(Collectors.toSet()));
                cameraVos.stream().forEach(e -> {
                    cameraService.flushCameraCaptureUrl(e.getId()).subscribe();
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新取流相机直播地址
    @Scheduled(initialDelay = 15000, fixedRate = 10000)
    public void flushCameraLiveAddress() {

        try {
            ListCameraParam param = new ListCameraParam();
            param.setType(CameraConstant.YUNMOU_TYPE);
            List<CameraVo> cameraVos = cameraRoDs.getBySiteId(param);

            if(CollectionUtils.isNotEmpty(cameraVos)) {

                final Date bufferTime = getBufferTime(1);

                Set<Long> collect = cameraVos.stream()
                        .filter(e -> Objects.nonNull(e.getLiveExpireTime())) // null表示长期直播地址
                        .filter(e -> e.getLiveExpireTime().getTime() < bufferTime.getTime())
                        .map(CameraVo::getId)
                        .collect(Collectors.toSet());

                Set<Long> initCamera = cameraVos.stream()
                        .filter(e -> Objects.isNull(e.getLiveExpireTime()) && // 初始化的相机，需要刷新直播地址
                                StringUtils.isBlank(e.getLiveAddress()) &&
                                StringUtils.isBlank(e.getLiveAddressHD()))
                        .map(CameraVo::getId)
                        .collect(Collectors.toSet());

                collect.addAll(initCamera);

                log.info("flushCameraLiveAddress: {}", collect);
                collect.stream().forEach(camId -> {
//                    cameraService.flushCameraLiveAddress(e).subscribe();
                    cameraService.openLiveVideo(camId)
                            .flatMap(e -> cameraService.flushCameraLiveAddress(camId)).subscribe();
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private static Date getBufferTime(int delay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, delay);
        return calendar.getTime();
    }
}