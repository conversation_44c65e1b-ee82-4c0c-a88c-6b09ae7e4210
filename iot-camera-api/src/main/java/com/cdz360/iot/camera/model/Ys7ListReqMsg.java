package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname Ys8ListReqMsg
 * @Description
 * @Date 8/23/2021 1:22 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Ys7ListReqMsg extends CameraReqMsg{


    @Schema(description = "当前页码（最小是0）")
    private Integer pageStart;

    @Schema(description = "每页记录数，最大为999")
    private Integer pageSize;

    @Schema(description = "每页记录数，最大为999")
    private String storeId;
}