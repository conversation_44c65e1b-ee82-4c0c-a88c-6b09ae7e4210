package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.HkVideoUrl;
import com.cdz360.iot.common.base.IotCacheConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRecorderRoDs;
import com.cdz360.iot.ds.ro.CameraRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.model.camera.param.CameraRecorderParam;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraPo;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import com.cdz360.iot.model.camera.vo.CameraRecorderVo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import com.cdz360.iot.model.camera.vo.OpInitCameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname CameraFacadeService
 * @Description
 * @Date 8/23/2021 2:58 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class CameraFacadeService {
    @Autowired
    private CameraService cameraService;

    @Autowired
    private CameraYs7Service cameraYs7Service;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraRoDs cameraRoDs;

    @Autowired
    private CameraRecorderRoDs cameraRecorderRoDs;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public ICameraService getCameraServiceByStoreId(Long storeId) {
        CameraSitePo cameraSite = cameraSiteRoDs.getById(storeId);
        IotAssert.isNotNull(cameraSite, "找不到门店信息");

        CameraAccountPo account = cameraAccountRoDs.getById(cameraSite.getAccountId());
        IotAssert.isNotNull(account, "找不到视频接入账号信息");

        if(account.getType() == null) {
            return null;
        } else if(account.getType() == CameraConstant.YS_7_TYPE) {
            return cameraYs7Service;
        } else if(account.getType() == CameraConstant.YUNMOU_TYPE) {
            return cameraService;
        }
        return null;
    }

    public Mono<BaseResponse> syncCameraBySiteId(String siteId) {
        CameraSitePo cameraSite = cameraSiteRoDs.getBySiteId(siteId);
        IotAssert.isNotNull(cameraSite, "找不到门店信息");
        ICameraService cameraServiceByStoreId = this.getCameraServiceByStoreId(cameraSite.getId());
        return cameraServiceByStoreId.syncCamera(cameraSite.getId(), 0);
    }

    public Mono<ObjectResponse<HkVideoUrl>> getCameraVideoUrl(CameraVideoParam param) {
        CameraPo cameraPo = cameraRoDs.getById(param.getCameraId());
        IotAssert.isNotNull(cameraPo, "摄相机不存在");
        IotAssert.isTrue(cameraPo.getEnable(), "相机当前不可用");

        // FIXME 直连的摄像头不需要检查录像机
        CameraRecorderPo recorderPo = cameraRecorderRoDs.getById(cameraPo.getRecorderId());
        IotAssert.isNotNull(recorderPo, "录像机设备不存在");
        IotAssert.isTrue(recorderPo.getEnable(), "录像机设备当前不可用");

        CameraSitePo cameraSite = cameraSiteRoDs.getById(cameraPo.getCameraSiteId());
        IotAssert.isNotNull(cameraSite, "找不到场站信息");
        IotAssert.isTrue(cameraPo.getEnable(), "场站信息不可用");

        CameraAccountPo account = cameraAccountRoDs.getById(cameraSite.getAccountId());
        IotAssert.isNotNull(account, "找不到视频接入账号信息");
        IotAssert.isTrue(account.getEnable(), "视频接入账号不可用");

        if(account.getType() == null) {
            return null;
        } else if(account.getType() == CameraConstant.YS_7_TYPE) {
            param.setAccessToken(cameraSite.getAccessToken())
//                    .setDeviceSerial(cameraPo.getDeviceSerial())
                    // FIXME 直连的摄像头不需要使用录像机序列号
                    .setDeviceSerial(recorderPo.getDeviceSerial())
                    .setChannelNo(cameraPo.getChannelNo());
            return cameraYs7Service.getCameraVideoUrl(param);
        } else if(account.getType() == CameraConstant.YUNMOU_TYPE) {
            return cameraService.getCameraVideoUrl(param);
        }
        return null;
    }

    public Mono<ObjectResponse<Boolean>> updateChannel(CameraVo param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotNull(param.getId(), "请传入相机id");
        return cameraYs7Service.updateChannel(param);
    }

    public Mono<BaseResponse> syncCameraByIdChain(String idChain) {
        CameraRecorderParam param = new CameraRecorderParam();
        param.setIdChain(idChain);

        List<Mono<BaseResponse>> collect = cameraRecorderRoDs.listRecorder(param)
                .stream()
                .map(CameraRecorderVo::getSiteId)
                .distinct()
                .map(this::syncCameraBySiteId)
                .collect(Collectors.toList());

        return Flux.fromIterable(collect)
                .flatMap(e -> e)
                .collectList()
                .map(e -> RestUtils.success());
    }

    public Mono<ListResponse<OpInitCameraVo>> getInitLiveAddressUseForOp() {
        final int size = 4; // 只取4个资源用于轮播
        List<Long> cameraIdList = null;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(IotCacheConstants.OP_INIT_CFG_CAMERA_ID_List_LIST_KEY))) {
            cameraIdList = redisTemplate.opsForList().range(IotCacheConstants.OP_INIT_CFG_CAMERA_ID_List_LIST_KEY, 0, size)
                    .stream().map(Long::valueOf).collect(Collectors.toList());
        }

        return Mono.just(cameraRoDs.getOpInitHlsUrl(cameraIdList, size))
                .map(RestUtils::buildListResponse);
    }

}