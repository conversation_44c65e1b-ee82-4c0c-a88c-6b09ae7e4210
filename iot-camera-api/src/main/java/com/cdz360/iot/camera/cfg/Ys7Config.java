package com.cdz360.iot.camera.cfg;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Classname Ys7Config
 * @Description
 * @Date 8/23/2021 9:07 AM
 * @Created by Rafael
 */
@Data
@Component
@ConfigurationProperties(prefix = "camera.ys7")
public class Ys7Config {

    private String host;

    private String mainTokenUrl;

    private String accountList;

    private String subTokenUrl;

    private String cameraList;

    private String deviceList;

    private String capture;

    private String liveAddressLimit;

    private String liveVideoOpen;

    private String liveAddressGet;

    private String createAccount;

    private String addRecorderDevice;

    private String encryptOff;

    private String deviceCameraList;

    private String deviceDelete;

    private String statementAdd;

    private String statementDelete;

    private String updateRecorderName;

    private String updateCameraName;

    private String deviceInfo;

}