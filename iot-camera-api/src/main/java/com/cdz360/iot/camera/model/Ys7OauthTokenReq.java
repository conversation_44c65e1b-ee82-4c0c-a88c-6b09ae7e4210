package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7OauthTokenReq
 * @Description
 * @Date 8/23/2021 9:15 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Builder
public class Ys7OauthTokenReq {
    @JsonProperty(value = "appKey")
    @Schema(description = "应用key", required = true)
    private String appKey;

    @JsonProperty(value = "appSecret")
    @Schema(description = "应用密钥", required = true)
    private String appSecret;

}