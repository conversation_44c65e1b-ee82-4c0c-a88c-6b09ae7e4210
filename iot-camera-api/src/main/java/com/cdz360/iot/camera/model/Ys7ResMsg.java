package com.cdz360.iot.camera.model;

import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7ResMsg
 * @Description
 * @Date 8/23/2021 9:57 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Ys7ResMsg<T> extends BaseObject {

    private String code;

    private String msg;

    private T data;
}