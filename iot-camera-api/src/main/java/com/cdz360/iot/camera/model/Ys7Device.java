package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7Device
 * @Description
 * @Date 8/23/2021 5:18 PM
 * @Created by Rafael
 */
@Schema(description = "萤石云设备信息")
@Data
@Accessors(chain = true)
public class Ys7Device {
    private String id;
    private String deviceSerial;
    private String deviceName;
    private String deviceType;
    private Integer status;
    private Integer defence;
    private String deviceVersion;
    private Long addTime;
    private Long updateTime;
    private String parentCategory;
    private Integer riskLevel;
}