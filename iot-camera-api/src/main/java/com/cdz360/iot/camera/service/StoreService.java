package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.HkClient;
import com.cdz360.iot.camera.client.Ys7Client;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.HkListReqMsg;
import com.cdz360.iot.camera.model.HkStore;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.ds.rw.CameraSiteRwDs;
import com.cdz360.iot.model.camera.dto.CameraSiteDto;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
@Deprecated
public class StoreService {

    @Autowired
    private HkClient hkClient;

    @Autowired
    private Ys7Client ys7Client;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraSiteRwDs cameraSiteRwDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    public Mono<BaseResponse> syncStore(Long id) {
        CameraAccountPo byId = cameraAccountRoDs.getById(id);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        IotAssert.isTrue(byId.getEnable(), "未配置平台账号已禁用");
        IotAssert.isTrue(byId.getType() != null && byId.getType() == CameraConstant.YUNMOU_TYPE,
                "同步帐户类型应是云眸");

        HkListReqMsg hkListReqMsg = new HkListReqMsg();
        hkListReqMsg.setPageNo(1).setPageSize(999);
        return hkClient.syncStore(byId.getClientId(), hkListReqMsg)
                .doOnNext(e -> {
                    log.info("删除{}个门店", cameraSiteRwDs.disableSiteByAccountId(id));
                    e.getData().getRows()
                            .stream()
                            .map(StoreService::convert2CameraSitePo)
                            .forEach(es -> {
                                es.setAccountId(id);
                                boolean b = cameraSiteRwDs.insertOrUpdate(es);
                                log.info("入库门店: {}, {}", es.getCameraSiteId(), b);
                            });
                }).map(e -> RestUtils.success());
    }

    public Mono<ObjectResponse<CameraSitePo>> getStore(String siteId) {
        CameraSitePo bySiteId = cameraSiteRoDs.getBySiteId(siteId);
        return Mono.just(new ObjectResponse<>(bySiteId));
    }

    private static CameraSitePo convert2CameraSitePo(HkStore hkStore) {
        CameraSitePo ret = new CameraSitePo();
        BeanUtils.copyProperties(hkStore, ret);

        ret.setCameraSiteId(hkStore.getStoreId())
                .setCameraSiteLat(hkStore.getStoreLat() != null ? hkStore.getStoreLat().toString() : null)
                .setCameraSiteLng(hkStore.getStoreLng() != null ? hkStore.getStoreLng().toString() : null)
                .setCameraSiteMeasure(hkStore.getStoreMeasure() != null ? hkStore.getStoreMeasure().toString() : null)
                .setCameraSiteTel(hkStore.getStoreTel())
                .setCameraSiteDetailAddress(hkStore.getStoreDetailAddress())
                .setCameraSiteNo(hkStore.getStoreNo())
                .setCameraSiteName(hkStore.getStoreName())
                .setCameraSiteRemark(hkStore.getStoreRemark())
                .setEnable(true);

        return ret;
    }

    public Mono<ListResponse<CameraSiteDto>> getStoreList(ListCameraParam param) {
        List<CameraSiteDto> list = cameraSiteRoDs.getStoreListHasCamera(param);
        return Mono.just(new ListResponse<>(list));
    }

}
