package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.Ys7Client;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.Ys7ListReqMsg;
import com.cdz360.iot.camera.model.Ys7ResMsg;
import com.cdz360.iot.camera.model.Ys7SubAccount;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.ds.rw.CameraSiteRwDs;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * @Classname StoreYs7Service
 * @Description 莹石云子账户(门店)服务
 * @Date 8/23/2021 1:07 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class StoreYs7Service {

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraSiteRwDs cameraSiteRwDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private Ys7Client ys7Client;

    @Value("${camera.account.defaultMainAccountId:2}")
    private Long defaultMainAccountId;

    @Value("${camera.account.defaultSubAccountPassword:654321}")
    private String defaultSubAccountPassword;

    public Mono<ObjectResponse> flushSubAccessToken(Long accountId, CameraSitePo cameraSitePo) {
        CameraAccountPo byId = cameraAccountRoDs.getById(accountId);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        return ys7Client.getSubAccountToken(byId.getClientId(), cameraSitePo.getCameraSiteId())
                .map(Ys7ResMsg::getData)
                .doOnNext(e -> {

                    cameraSitePo.setAccessToken(e.getAccessToken())
                            .setAccessTokenExpires(new Date(e.getExpireTime()));

                    cameraSiteRwDs.updateCameraSite(cameraSitePo);
                })
                .map(RestUtils::buildObjectResponse);
    }

    public Mono<BaseResponse> syncStore(Long id) {
        CameraAccountPo byId = cameraAccountRoDs.getById(id);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        IotAssert.isTrue(byId.getEnable(), "未配置平台账号已禁用");
        IotAssert.isTrue(byId.getType() != null && byId.getType() == CameraConstant.YS_7_TYPE,
                "同步帐户类型应是莹石云");

        Ys7ListReqMsg hkListReqMsg = new Ys7ListReqMsg();
        hkListReqMsg.setPageStart(0).setPageSize(50);//TODO 需要做循环翻页
        return ys7Client.syncSubAccount(byId.getClientId(), hkListReqMsg)
                .doOnNext(e -> {
                    log.info("删除{}个子帐户", cameraSiteRwDs.disableSiteByAccountId(id));
                    e.getData()
                            .stream()
                            .map(StoreYs7Service::convert2CameraSitePo)
                            .forEach(es -> {
                                es.setAccountId(id);
                                boolean b = cameraSiteRwDs.insertOrUpdate(es);
                                log.info("入库子账号: {}, {}", es.getCameraSiteId(), b);
                            });
                }).map(e -> RestUtils.success());
    }

    /**
     * 新增子账号
     * @param param
     * @return 子账号（门店）表的id
     */
    public Mono<ObjectResponse<Long>> addSubAccount(CameraSitePo param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
        CameraSitePo bySiteId = cameraSiteRoDs.getBySiteId(param.getSiteId());
        if(bySiteId != null) {
            log.info("恢复监控服务子账号");
            IotAssert.isTrue(!bySiteId.getEnable(), "场站已配置了监控服务，无需再添加");
            bySiteId.setEnable(true);
            log.info("修改门店: {}", cameraSiteRwDs.updateCameraSite(bySiteId));
            return Mono.just(bySiteId.getId()).map(RestUtils::buildObjectResponse);
        } else {
            log.info("新增监控服务子账号");
            param.setEnable(true);
            Long mainAccountId;
            if(param.getAccountId() == null) {
                param.setAccountId(defaultMainAccountId);
                mainAccountId = this.defaultMainAccountId;
            } else {
                mainAccountId = param.getAccountId();
            }
            CameraAccountPo mainAccount = cameraAccountRoDs.getById(mainAccountId);
            IotAssert.isNotNull(mainAccount, "找不到主账号: " + mainAccountId);
            IotAssert.isTrue(mainAccount.getEnable(), "主账号已禁用: " + mainAccountId);


            String subAccountName = CameraAccountService.getRandomSubAccountName();
            String passwordMD5 = CameraAccountService.getSubAccountPassword(mainAccount.getClientId(),
                    defaultSubAccountPassword);

            return ys7Client.createSubAccount(mainAccount.getClientId(), subAccountName, passwordMD5)
                    .map(e -> {
                        IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                        param.setCameraSiteId(e.getData().getAccountId());
                        log.info("新增门店: {}", cameraSiteRwDs.insertCameraSite(param));
                        return param.getId();
                    })
                    .map(RestUtils::buildObjectResponse);
        }
    }


    private static CameraSitePo convert2CameraSitePo(Ys7SubAccount ys7SubAccount) {
        CameraSitePo ret = new CameraSitePo();

        ret.setCameraSiteId(ys7SubAccount.getAccountId())
                .setEnable(true);

        return ret;
    }

}