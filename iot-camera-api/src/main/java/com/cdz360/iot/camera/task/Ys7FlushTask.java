package com.cdz360.iot.camera.task;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.service.CameraYs7AccountService;
import com.cdz360.iot.camera.service.CameraYs7Service;
import com.cdz360.iot.camera.service.StoreYs7Service;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname Ys7FlushTask
 * @Description
 * @Date 8/23/2021 10:37 AM
 * @Created by Rafael
 */
@Slf4j
@Component
public class Ys7FlushTask {

    @Autowired
    private CameraYs7AccountService cameraYs7AccountService;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private StoreYs7Service storeYs7Service;

    @Autowired
    private CameraRoDs cameraRoDs;

    @Autowired
    private CameraYs7Service cameraYs7Service;

    // 定时刷新主账号访问token
    @Scheduled(initialDelay = 8000, fixedRate = 10000)
    public void flushMainAccessToken() {

        try {
            List<CameraAccountPo> accountPos = cameraAccountRoDs.getAccessTokenExpireByTime(getBufferTime(30), CameraConstant.YS_7_TYPE);
            if(CollectionUtils.isNotEmpty(accountPos)) {
                log.info("flushMainAccessToken: {}", accountPos.stream()
                        .map(CameraAccountPo::getId)
                        .collect(Collectors.toSet()));
                accountPos.stream().forEach(e -> {
                    cameraYs7AccountService.flushAccessToken(e.getId()).subscribe();
                });
            } else {
                log.debug("flushMainAccessToken skip");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新子账号访问token
    @Scheduled(initialDelay = 8000, fixedRate = 10000)
    public void flushSubAccessToken() {

        try {
            List<CameraSitePo> accountPos = cameraSiteRoDs.getAccessTokenExpireByTime(getBufferTime(30), CameraConstant.YS_7_TYPE);
            if(CollectionUtils.isNotEmpty(accountPos)) {
                log.info("flushSubAccessToken: {}", accountPos.stream()
                        .map(CameraSitePo::getId)
                        .collect(Collectors.toSet()));
                accountPos.stream().forEach(e -> {
                    storeYs7Service.flushSubAccessToken(e.getAccountId(), e).subscribe();
                });
            } else {
                log.debug("flushSubAccessToken skip");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新取流相机抓图
    @Scheduled(initialDelay = 10000, fixedRateString = "${camera.taskRate.flushCameraCaptureUrl:60000}")
    public void flushCameraCaptureUrl() {

        try {
            ListCameraParam param = new ListCameraParam();
            param.setType(CameraConstant.YS_7_TYPE);
            List<CameraVo> cameraVos = cameraRoDs.getBySiteId(param);

            if(CollectionUtils.isNotEmpty(cameraVos)) {
                log.info("flushCameraCaptureUrl: {}", cameraVos.stream()
                        .map(CameraVo::getId)
                        .collect(Collectors.toSet()));
                cameraVos.stream().forEach(e -> {
                    cameraYs7Service.flushCameraCaptureUrl(e).subscribe();
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    // 定时刷新取流相机直播地址
    @Scheduled(initialDelay = 15000, fixedRate = 10000)
    public void flushCameraLiveAddress() {

        try {
            ListCameraParam param = new ListCameraParam();
            param.setType(CameraConstant.YS_7_TYPE);
            List<CameraVo> cameraVos = cameraRoDs.getBySiteId(param);

            if(CollectionUtils.isNotEmpty(cameraVos)) {

                final Date bufferTime = getBufferTime(1);

                List<CameraVo> collect = cameraVos.stream()
                        .filter(e -> Objects.nonNull(e.getLiveExpireTime())) // null表示长期直播地址
                        .filter(e -> e.getLiveExpireTime().getTime() < bufferTime.getTime())
//                        .map(CameraVo::getId)
                        .collect(Collectors.toList());

                List<CameraVo> initCamera = cameraVos.stream()
                        .filter(e -> Objects.isNull(e.getLiveExpireTime()) && // 初始化的相机，需要刷新直播地址
                                StringUtils.isBlank(e.getLiveAddress()) &&
                                StringUtils.isBlank(e.getLiveAddressHD()))
//                        .map(CameraVo::getId)
                        .collect(Collectors.toList());

                collect.addAll(initCamera);

                log.info("flushCameraLiveAddress: {}", collect);
                collect.stream().forEach(cam -> {
//                    cameraYs7Service.flushCameraLiveAddress(cam).subscribe();
                    cameraYs7Service.openLiveVideo(cam)
                            .flatMap(e -> cameraYs7Service.flushCameraLiveAddress(cam)).subscribe();
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private static Date getBufferTime(int delay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, delay);
        return calendar.getTime();
    }
}