package com.cdz360.iot.camera.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.HkClient;
import com.cdz360.iot.camera.model.CameraListParam;
import com.cdz360.iot.camera.model.HkListReqMsg;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
public class TestRest {
    @Autowired
    private HkClient hkClient;

    @GetMapping(value = "/api/camera/test")
    public Mono<BaseResponse> test() {
        log.info("测试测试...");
        return Mono.just(RestUtils.success());
    }

    @GetMapping("/api/test/syncCameraList")
    public Mono<BaseResponse> testSyncCameraList() {
        CameraListParam req = new CameraListParam();
        req.setStoreId("b978bced6b6740608fe4304ad5d341fa")
                .setPageNo(1)
                .setPageSize(20);
        hkClient.syncCameraList(req)
                .doOnNext(res -> log.info(">>> {}", JsonUtils.toJsonString(res)))
                .block(Duration.ofSeconds(30L));
        return Mono.just(RestUtils.success());
    }
}
