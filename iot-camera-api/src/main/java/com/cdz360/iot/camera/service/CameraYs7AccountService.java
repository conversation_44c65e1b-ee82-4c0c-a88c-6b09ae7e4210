package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.Ys7Client;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.Ys7OauthTokenRes;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.rw.CameraAccountRwDs;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Classname CameraYs7AccountService
 * @Description
 * @Date 8/23/2021 10:33 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class CameraYs7AccountService {

    @Autowired
    private Ys7Client ys7Client;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;
    @Autowired
    private CameraAccountRwDs cameraAccountRwDs;

    @Value("${camera.account.accountExpireTime:6}")
    private Integer accountExpireTime;

    @PostConstruct
    private void init() {
        List<CameraAccountPo> all = cameraAccountRoDs.getAll();
        if(CollectionUtils.isNotEmpty(all)) {

            Map<String, Ys7OauthTokenRes> collectYs7 = all.stream()
                    .filter(e -> e.getType() != null && e.getType() == CameraConstant.YS_7_TYPE)
                    .collect(Collectors.toMap(CameraAccountPo::getClientId, CameraYs7AccountService::convert2Ys7OauthTokenRes));
            ys7Client.init(collectYs7);
        }
    }

    public Mono<ObjectResponse> flushAccessToken(Long id) {
        CameraAccountPo byId = cameraAccountRoDs.getById(id);
        IotAssert.isNotNull(byId, "未配置平台账号信息");
        return ys7Client.getMainToken(byId.getClientId(), byId.getClientSecret())
                .doOnNext(e -> {

                    byId.setAccessToken(e.getAccessToken())
                            .setExpiresIn(new Date(e.getExpireTime()));

                    log.info("更新萤石云主账号: {}", cameraAccountRwDs.updateCameraAccount(byId));
                })
                .map(RestUtils::buildObjectResponse);
    }


    private static Ys7OauthTokenRes convert2Ys7OauthTokenRes(CameraAccountPo cameraAccountPo) {
        Ys7OauthTokenRes ret = new Ys7OauthTokenRes();
        ret.setAccessToken(cameraAccountPo.getAccessToken());
        if(cameraAccountPo.getExpiresIn() != null) {
            ret.setExpireTime(cameraAccountPo.getExpiresIn().getTime());
        }
        return ret;
    }
}