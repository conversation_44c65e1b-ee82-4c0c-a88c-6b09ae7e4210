package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7AddressLimit
 * @Description
 * @Date 8/24/2021 10:56 AM
 * @Created by Rafael
 */
@Schema(description = "萤石云直播地址")
@Data
@Accessors(chain = true)
public class Ys7AddressLimit {
    private String deviceSerial;
    private Integer channelNo;
    private String deviceName;
    private String liveAddress;
    private String hdAddress;
    private String rtmp;
    private String rtmpHd;
    private String flvAddress;
    private String hdFlvAddress;
    private Integer status;
    private Integer exception;
    private Long beginTime;
    private Long endTime;
    private String publicKey;
    private Integer version;
}