package com.cdz360.iot.camera.client;

import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.camera.cfg.Ys7Config;
import com.cdz360.iot.camera.model.*;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

/**
 * @Classname Ys7Client
 * @Description 萤石云接口调用
 * @Date 8/23/2021 9:05 AM
 * @Created by Rafael
 */

@Slf4j
@Service
public class Ys7Client {
    private static final String YS_7_TOKEN = "YS7_TOKEN_";

    private static final ConcurrentHashMap<String, Ys7OauthTokenRes> TOKEN_MAP = new ConcurrentHashMap<>();

    private static final Predicate<HttpStatusCode> UNAUTHORIZED_HTTP_STATUS = HttpStatus.UNAUTHORIZED::equals;

    @Autowired
    private Ys7Config ys7Config;

    public void init(Map<String, Ys7OauthTokenRes> ctx) {
        if(ctx != null) {
            ctx.entrySet().forEach(e -> {
                TOKEN_MAP.put(this.key(e.getKey()), e.getValue());
            });
        }
    }

    public Mono<Ys7ResMsg<HkVideoUrl>> getCameraVideoUrl(CameraVideoParam param) {
        String qp = "?deviceSerial={deviceSerial}&channelNo={channelNo}&quality={quality}&protocol={protocol}" +
                "&expireTime={expireTime}&type={type}&startTime={startTimeStr}&stopTime={stopTimeStr}&accessToken={accessToken}";

        return this.retrieveWithAuthPost(
                        ys7Config.getLiveAddressGet() + qp, param)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取萤石云摄像机回放地址: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<HkVideoUrl>>() {
                }))
                .onErrorMap(e -> {
                    log.error("获取萤石云摄像机回放地址失败 = {}", e.getMessage(), e);
                    return new DcServerException("获取萤石云摄像机回放地址失败");
                });
    }

    public Mono<Ys7ResMsg<List<HkLiveOpen>>> openLiveVideo(String accessToken, String deviceSerial, Integer channelNo) {
        String qp = "?accessToken=" + accessToken + "&source=" + deviceSerial + ":" + channelNo;

        return this.retrieveWithAuthPost(
                        ys7Config.getLiveVideoOpen() + qp, null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("开通萤石云直播标准流预览: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<List<HkLiveOpen>>>() {
                }))
                .onErrorMap(e -> new DcServerException("开通萤石云直播标准流预览失败"));
    }

    public Mono<Ys7ResMsg<Ys7AddressLimit>> syncCameraLiveAddress(String accessToken,
                                                                String deviceSerial,
                                                                Integer channelNo,
                                                                long expireTime) {
        String qp;
        if (expireTime > 0) {
            qp = "?accessToken=" + accessToken + "&deviceSerial=" + deviceSerial +
                    "&channelNo=" + channelNo + "&expireTime=" + expireTime;
        } else {
            qp = "?accessToken=" + accessToken + "&deviceSerial=" + deviceSerial +
                    "&channelNo=" + channelNo;
        }

        return this.retrieveWithAuthPost(
                        ys7Config.getLiveAddressLimit() + qp, null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取萤石云摄象机直播地址: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Ys7AddressLimit>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取萤石云摄象机直播地址失败"));
    }

    public Mono<Ys7ResMsg<HkCameraCapture>> syncCameraCaptureUrl(String accessToken,
                                                                String deviceSerial,
                                                                Integer channelNo,
                                                                int quality) {
        String qp = String.format("?deviceSerial=%s&channelNo=%d&quality=%d&accessToken=%s",
                deviceSerial, channelNo, quality, accessToken);

        return this.retrieveWithAuthPost(
                        ys7Config.getCapture() + qp, null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取萤石云摄象机抓图: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<HkCameraCapture>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取萤石云摄象机抓图失败"));
    }


    public Mono<Ys7ListResMsg<List<Ys7Device>, Ys7ListPage>> getDevice(String subAccessToken, Ys7ListReqMsg param) {
        return this.retrieveWithAuthPost(
                        ys7Config.getDeviceList() +
                                "?pageStart={pageStart}&pageSize={pageSize}&accessToken=" + subAccessToken, param)
                .toEntity(String.class)
                .doOnNext(res -> log.info("同步子帐户设备信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ListResMsg<List<Ys7Device>, Ys7ListPage>>() {
                }))
                .onErrorMap(e -> {
                    log.error(e.getMessage(), e);
                    return new DcServerException("同步子帐户设备信息失败");
                });
    }

    public Mono<Ys7ListResMsg<List<Ys7Camera>, Ys7ListPage>> syncCamera(String subAccessToken, Ys7ListReqMsg param) {
        return this.retrieveWithAuthPost(
                        ys7Config.getCameraList() + "?pageStart={pageStart}&pageSize={pageSize}&accessToken=" + subAccessToken, param)
                .toEntity(String.class)
                .doOnNext(res -> log.info("同步子帐户摄象机信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ListResMsg<List<Ys7Camera>, Ys7ListPage>>() {
                }))
                .onErrorMap(e -> new DcServerException("同步子帐户摄象机信息失败"));
    }

    public Mono<Ys7ResMsg<Ys7OauthTokenRes>> getSubAccountToken(String clientId, String subAccountId) {
        return this.retrieveWithAuthPost(
                        ys7Config.getSubTokenUrl() + "?accessToken=" + this.authorization(clientId) + "&accountId=" + subAccountId,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取子账号token信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Ys7OauthTokenRes>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取子账号token信息失败"));
    }

    public Mono<Ys7ListResMsg<List<Ys7SubAccount>, Ys7ListPage>> syncSubAccount(String clientId, Ys7ListReqMsg param) {
        return this.retrieveWithAuthPost(
                        ys7Config.getAccountList() + "?pageStart={pageStart}&pageSize={pageSize}&accessToken=" + this.authorization(clientId),
                        param)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取子账号列表信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ListResMsg<List<Ys7SubAccount>, Ys7ListPage>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取子账号列表信息失败"));
    }

    public Mono<Ys7OauthTokenRes> getMainToken(String appKey, String appSecret) {
        String qp = "?appKey=" + appKey + "&appSecret=" + appSecret;

        return this.retrieveWithAuthPost(
                        ys7Config.getMainTokenUrl() + qp, null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取token信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Ys7OauthTokenRes>>() {
                }))
                .map(Ys7ResMsg::getData)
                .doOnNext(auth -> TOKEN_MAP.put(key(appKey), auth))
                .onErrorMap(e -> new DcServerException("获取token信息失败"));
    }

    public Mono<Ys7ResMsg<Ys7SubAccount>> createSubAccount(String clientId, String accountName, String password) {
        return this.retrieveWithAuthPost(
                        ys7Config.getCreateAccount() + "?accessToken=" + this.authorization(clientId) +
                                "&accountName=" + accountName +
                                "&password=" + password,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("创建子帐户: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Ys7SubAccount>>() {
                }))
                .onErrorMap(e -> new DcServerException("创建子帐户失败"));
    }

    public Mono<Ys7ResMsg<Void>> updateDeviceName(String clientId, String deviceSerial, String deviceName) {
        return this.retrieveWithAuthPost(
                        ys7Config.getUpdateRecorderName() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial +
                                "&deviceName=" + deviceName,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("修改硬盘录像机设备名称: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("修改硬盘录像机设备名称失败"));
    }

    public Mono<Ys7ResMsg<Ys7DeviceInfo>> deviceInfo(String clientId, String deviceSerial) {
        return this.retrieveWithAuthPost(
                        ys7Config.getDeviceInfo() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取单个设备信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Ys7DeviceInfo>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取单个设备信息失败"));
    }

    public Mono<Ys7ResMsg<Void>> updateCameraName(String clientId, String deviceSerial, String name, long channelNo) {
        return this.retrieveWithAuthPost(
                        ys7Config.getUpdateCameraName() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial +
                                "&name=" + name +
                                "&channelNo=" + channelNo,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("修改通道名称名称: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("修改通道名称失败"));
    }

    public Mono<Ys7ResMsg<Void>> addDevice(String clientId, String deviceSerial, String validateCode) {
        return this.retrieveWithAuthPost(
                        ys7Config.getAddRecorderDevice() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial +
                                "&validateCode=" + validateCode,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("添加硬盘录像机或摄像头设备: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("添加硬盘录像机或摄像头设备失败"));
    }

    public Mono<Ys7ResMsg<Void>> encryptOff(String clientId, String deviceSerial, String validateCode) {
        return this.retrieveWithAuthPost(
                        ys7Config.getEncryptOff() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial +
                                "&validateCode=" + validateCode,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("接触硬盘录像机设备加密: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("接触硬盘录像机设备加密失败"));
    }

    /**
     * 获取指定设备的通道信息
     * @param clientId
     * @param deviceSerial
     * @return
     */
    public Mono<Ys7ListResMsg<List<Ys7DeviceCamera>, Void>> deviceCameraList(String clientId, String deviceSerial) {
        return this.retrieveWithAuthPost(
                        ys7Config.getDeviceCameraList() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("获取设备下通道信息: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ListResMsg<List<Ys7DeviceCamera>, Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("获取设备下通道信息失败"));
    }

    public Mono<Ys7ResMsg<Void>> deviceDelete(String clientId, String deviceSerial) {
        return this.retrieveWithAuthPost(
                        ys7Config.getDeviceDelete() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("删除硬盘录像机设备: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("删除硬盘录像机设备失败"));
    }

    @Data
    class StatementAdd {
        private String accessToken;
        private String statement;
        private String accountId;
    }
    /**
     * 增加子账户权限
     * @param clientId
     * @param statement https://open.ys7.com/doc/zh/book/index/account-policy.html
     * @param accountId 子账号id对应t_camera_site.cameraSiteId
     * @return
     */
    public Mono<Ys7ResMsg<Void>> statementAdd(String clientId, String statement, String accountId) {
        StatementAdd statementAdd = new StatementAdd();
        statementAdd.setAccessToken(this.authorization(clientId));
        statementAdd.setAccountId(accountId);
        statementAdd.setStatement(statement);

        return this.retrieveWithAuthPost(
                        ys7Config.getStatementAdd() + "?accessToken={accessToken}" + //this.authorization(clientId) +
                                "&statement={statement}" + //encodeStatement +
                                "&accountId={accountId}"// + accountId
                        ,
                        statementAdd)
                .toEntity(String.class)
                .doOnNext(res -> log.info("增加子账户权限: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("增加子账户权限失败"));
    }

    /**
     * 删除子账户权限
     * @param clientId
     * @param deviceSerial
     * @param accountId 子账号id对应t_camera_site.cameraSiteId
     * @return
     */
    public Mono<Ys7ResMsg<Void>> statementDelete(String clientId, String deviceSerial, String accountId) {
        return this.retrieveWithAuthPost(
                        ys7Config.getStatementDelete() + "?accessToken=" + this.authorization(clientId) +
                                "&deviceSerial=" + deviceSerial +
                                "&accountId=" + accountId ,
                        null)
                .toEntity(String.class)
                .doOnNext(res -> log.info("删除子账户权限: {}", res.getBody()))
                .map(HttpEntity::getBody)
                .map(body -> JsonUtils.fromJson(body, new TypeReference<Ys7ResMsg<Void>>() {
                }))
                .onErrorMap(e -> new DcServerException("删除子账户权限失败"));
    }

    private static ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            log.info("Request: {} {}", clientRequest.method(), clientRequest.url());
            clientRequest.headers().forEach((name, values) -> values.forEach(value -> log.info("{}={}", name, value)));
            return Mono.just(clientRequest);
        });
    }

    private WebClient.ResponseSpec retrieveWithAuthPost(String uri, Object param) {
        WebClient client = WebClient.create(ys7Config.getHost());
//        WebClient client = WebClient.builder().filter(logRequest()).baseUrl(ys7Config.getHost()).build();
        if(param == null) {
            return client.post()
                    .uri(uri)
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        } else {
            return client.post()
                    .uri(uri, object2Map(param))
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        }
    }

    private WebClient.ResponseSpec retrieveWithAuthGet(String uri, Object param) {
        WebClient client = WebClient.create(ys7Config.getHost());
        if(param == null) {
            return client.get()
                    .uri(uri)
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        } else {
            return client.get()
                    .uri(uri, object2Map(param))
                    .retrieve()
                    .onStatus(UNAUTHORIZED_HTTP_STATUS, res -> {
                        log.debug("token 已失效: {}.", res);
//                        oauthToken().subscribe();
                        return Mono.error(new DcServerException("token 已失效"));
                    });
        }
    }

    private String authorization(String clientId) {
        String key = this.key(clientId);
        Ys7OauthTokenRes s = TOKEN_MAP.get(key);
        if (s != null) {
            Ys7OauthTokenRes oauthToken = s;
            return oauthToken.getAccessToken();
        }
        return "";
    }

    private String key(String clientId) {
        return YS_7_TOKEN + clientId;
    }

    private static Map<String, String> object2Map(Object context) {
        return new ObjectMapper()
                .convertValue(context, new TypeReference<>() {});
    }
}