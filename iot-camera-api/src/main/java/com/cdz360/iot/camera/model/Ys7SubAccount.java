package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname Ys7SubAccount
 * @Description
 * @Date 8/23/2021 1:33 PM
 * @Created by Rafael
 */
@Schema(description = "萤石云子帐户信息")
@Data
@Accessors(chain = true)
public class Ys7SubAccount {
    private String accountId;
    private String accountName;
    private String appKey;
    private String applicationId;
    private Integer accountStatus;
    private Object policy;
}