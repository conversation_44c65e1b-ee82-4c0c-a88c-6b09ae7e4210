package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "生成access_token返回数据")
public class OauthTokenRes {

    @JsonProperty(value = "access_token")
    @Schema(description = "访问令牌")
    private String accessToken;

    @JsonProperty(value = "token_type")
    @Schema(description = "令牌类型")
    private String tokenType;

    @JsonProperty(value = "expires_in")
    @Schema(description = "过期时间(秒)")
    private String expiresIn;

    @JsonProperty(value = "scope")
    @Schema(description = "权限范围")
    private String scope;
}
