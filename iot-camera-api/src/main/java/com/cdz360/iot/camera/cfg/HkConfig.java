package com.cdz360.iot.camera.cfg;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@Deprecated
@ConfigurationProperties(prefix = "camera.hk")
public class HkConfig {

    @Deprecated
    private String clientId;

    @Deprecated
    private String clientSecret;

    private String host;

    private String oauthTokenUri;

    private String storeInfoUri;

    private String cameraListUri;

    private String accountInfo;

    private String liveAddressLimit;

    private String capture;

    private String getVideoUrl;

    private String liveVideoOpen;
}
