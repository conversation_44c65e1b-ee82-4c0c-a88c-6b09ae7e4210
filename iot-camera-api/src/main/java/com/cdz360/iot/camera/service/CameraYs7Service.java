package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.Ys7Client;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.*;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRecorderRoDs;
import com.cdz360.iot.ds.ro.CameraRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.ds.rw.CameraRecorderRwDs;
import com.cdz360.iot.ds.rw.CameraRwDs;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraPo;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Classname CameraYs7Service
 * @Description
 * @Date 8/23/2021 3:08 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class CameraYs7Service implements ICameraService {

    @Autowired
    private CameraRwDs cameraRwDs;

    @Autowired
    private CameraRoDs cameraRoDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private CameraRecorderRoDs cameraRecorderRoDs;

    @Autowired
    private CameraRecorderRwDs cameraRecorderRwDs;

    private static final int MAX_MONO_PAGE_SIZE = 100;//mono调用次数

    @Autowired
    private Ys7Client ys7Client;
    @Value("${camera.capture.liveExpire:0}")
    private long CAMERA_LIVE_EXPIRE_SECOND = 0; // 过期秒数, max: ********,0表示长期地址
    @Value("${camera.capture.quality:0}")
    private int CAMERA_CAPTURE_QUALITY = 0; // 截图清晰度,0-流畅,1-高清(720P),2-FCIF,3-1080P,4-400w

    public Mono<ObjectResponse<HkVideoUrl>> getCameraVideoUrl(CameraVideoParam param) {
//        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(param.getCameraId());
//        CameraPo cameraPo = pair.getFirst();
//        CameraAccountPo cameraAccountPo = pair.getSecond();
//
//        param.setDeviceSerial(cameraPo.getDeviceSerial())
//                .setChannelNo(cameraPo.getChannelNo());

        return ys7Client.getCameraVideoUrl(param)
                .map(e -> RestUtils.buildObjectResponse(e.getData()));
    }

    // 开启直播模式
    public Mono<BaseResponse> openLiveVideo(CameraVo cameraVo) {

        return ys7Client.openLiveVideo(cameraVo.getAccessToken(),
//                        cameraVo.getDeviceSerial(),
                        // FIXME 直连的摄像头不需要使用录像机序列号
                        cameraVo.getRecordDeviceSerial(),
                        cameraVo.getChannelNo())
                .flatMap(e -> {
                    if(e.getData() == null) {
                        return Mono.error(new DcArgumentException(e.getMsg()));
                    } else {
                        return Mono.just(RestUtils.success());
                    }
                });
    }

    public Mono<BaseResponse> flushCameraLiveAddress(CameraVo cameraVo) {
//        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(cameraId);
//        CameraPo cameraPo = pair.getFirst();
//        CameraAccountPo cameraAccountPo = pair.getSecond();

        return ys7Client.syncCameraLiveAddress(cameraVo.getSubAccessToken(),
//                        cameraVo.getDeviceSerial(),
                        // FIXME 直连的摄像头不需要使用录像机序列号
                        cameraVo.getRecordDeviceSerial(),
                        cameraVo.getChannelNo(),
                        CAMERA_LIVE_EXPIRE_SECOND)
                .doOnNext(e -> {
                    Ys7AddressLimit data = e.getData();
                    IotAssert.isNotNull(data, "同步播放地址失败: " + JsonUtils.toJsonString(e));
                    if(CAMERA_LIVE_EXPIRE_SECOND > 0) {
                        if (data.getEndTime() != null) {
                            cameraVo.setLiveExpireTime(new Date(data.getEndTime()));
                        }
                    } else {
                        cameraVo.setLiveExpireTime(null);
                        log.warn("使用长期地址，并设置过期日期为null: cameraId: {}", cameraVo.getId());
                    }
                    cameraVo.setLiveAddress(data.getRtmp())
                            .setLiveAddressHD(data.getRtmpHd())
                            .setHlsLiveAddress(data.getLiveAddress())
                            .setHlsLiveAddressHD(data.getHdAddress());
                    log.info("更新直播源: {}, {}", JsonUtils.toJsonString(data), cameraRwDs.flushLiveAddress(cameraVo));
                }).map(e -> RestUtils.success());
    }

    public Mono<BaseResponse> flushCameraCaptureUrl(CameraVo cameraVo) {
        IotAssert.isNotBlank(cameraVo.getSubAccessToken(), "子账号token为空");
        return ys7Client.syncCameraCaptureUrl(cameraVo.getSubAccessToken(),
//                        cameraVo.getDeviceSerial(),
                        // FIXME 直连的摄像头不需要使用录像机序列号
                        cameraVo.getRecordDeviceSerial(),
                        cameraVo.getChannelNo(),
                        CAMERA_CAPTURE_QUALITY)
                .doOnNext(e -> {
                    if(e.getData() != null) {
                        HkCameraCapture data = e.getData();
                        cameraVo.setCameraCaptureUrl(data.getPicUrl());
                        log.info("更新抓图: {}, {}", JsonUtils.toJsonString(data), cameraRwDs.flushChannelPicUrl(cameraVo));
                    } else {
                        log.warn("抓图失败: cameraId: {}, {}", cameraVo.getId(), JsonUtils.toJsonString(e));
                    }
                }).map(e -> RestUtils.success());
    }

    /**
     * 翻页获取所有录像机
     * 与
     * @see #getAllCamera
     * 逻辑类似 FIXME: 可以抽象
     * @param subAccessToken
     * @param param
     * @return
     */
    private Mono<Ys7ListResMsg<List<Ys7Device>, Ys7ListPage>> getAllDevice(String subAccessToken, Ys7ListReqMsg param) {
        return ys7Client.getDevice(subAccessToken, param).flatMap(e -> {
            Ys7ListPage pager = e.getPage();
            if (pager != null && (pager.getPage() + 1) * pager.getSize() < pager.getTotal()) {

                List<Mono<Ys7ListResMsg<List<Ys7Device>, Ys7ListPage>>> monoList = new ArrayList<>();
                int skip = pager.getSize();
                int page = 0;
                while(skip < pager.getTotal() && page < MAX_MONO_PAGE_SIZE) {
                    skip += pager.getSize();
                    page++;
                    param.setPageStart(page);

                    monoList.add(ys7Client.getDevice(subAccessToken, param));
                }
                if(page == MAX_MONO_PAGE_SIZE) {
                    log.error("查询设备接口调用页数超出限制");
                }

                return Flux.fromIterable(monoList)
                        .flatMap(ex -> ex)
                        .collectList()
                        .map(ex -> {
                            return ex.stream()
                                    .map(Ys7ListResMsg::getData)
                                    .flatMap(List::stream)
                                    .collect(Collectors.toList());
                        })
                        .map(ex -> {
                            e.getData().addAll(ex);
                            return e.setPage(null);
                        });
            } else {
                return Mono.just(e.setPage(null));
            }
        });
    }

    /**
     * 翻页获取所有摄像头
     * 与
     * @see #getAllDevice
     * 逻辑类似 FIXME: 可以抽象
     * @param subAccessToken
     * @param param
     * @return
     */
    private Mono<Ys7ListResMsg<List<Ys7Camera>, Ys7ListPage>> getAllCamera(String subAccessToken, Ys7ListReqMsg param) {
        return ys7Client.syncCamera(subAccessToken, param).flatMap(e -> {
            Ys7ListPage pager = e.getPage();
            if (pager != null && (pager.getPage() + 1) * pager.getSize() < pager.getTotal()) {

                List<Mono<Ys7ListResMsg<List<Ys7Camera>, Ys7ListPage>>> monoList = new ArrayList<>();
                int skip = pager.getSize();
                int page = 0;
                while(skip < pager.getTotal() && page < MAX_MONO_PAGE_SIZE) {
                    skip += pager.getSize();
                    page++;
                    param.setPageStart(page);

                    monoList.add(ys7Client.syncCamera(subAccessToken, param));
                }
                if(page == MAX_MONO_PAGE_SIZE) {
                    log.error("查询设备接口调用页数超出限制");
                }

                return Flux.fromIterable(monoList)
                        .flatMap(ex -> ex)
                        .collectList()
                        .map(ex -> {
                            return ex.stream()
                                    .map(Ys7ListResMsg::getData)
                                    .flatMap(List::stream)
                                    .collect(Collectors.toList());
                        })
                        .map(ex -> {
                            e.getData().addAll(ex);
                            return e.setPage(null);
                        });
            } else {
                return Mono.just(e.setPage(null));
            }
        });
    }

    @Override
    public Mono<BaseResponse> syncCamera(Long storeId, Integer pageNo) {
        CameraSitePo cameraSite = cameraSiteRoDs.getById(storeId);
        IotAssert.isNotNull(cameraSite, "子帐户信息不存在");
        IotAssert.isTrue(cameraSite.getEnable(), "子帐户已禁用");
        IotAssert.isNotBlank(cameraSite.getAccessToken(), "子帐户token为空");

        CameraAccountPo account = cameraAccountRoDs.getById(cameraSite.getAccountId());
        IotAssert.isNotNull(account, "未配置平台账号信息");
        IotAssert.isTrue(account.getEnable(), "未配置平台账号已禁用");

        Ys7ListReqMsg hkListReqMsgDevice = new Ys7ListReqMsg();
        hkListReqMsgDevice.setPageStart(pageNo)
                .setPageSize(50)
                .setStoreId(cameraSite.getCameraSiteId());

        Ys7ListReqMsg hkListReqMsgCamera = new Ys7ListReqMsg();
        hkListReqMsgCamera.setPageStart(pageNo)
                .setPageSize(50)
                .setStoreId(cameraSite.getCameraSiteId());

//        return ys7Client.getDevice(account.getAccessToken(), hkListReqMsg)
        return this.getAllDevice(account.getAccessToken(), hkListReqMsgDevice)
                .map(Ys7ListResMsg::getData)
                .zipWith(this.getAllCamera(cameraSite.getAccessToken(), hkListReqMsgCamera).map(Ys7ListResMsg::getData))
                .doOnNext(e -> {
                    // tuple(直连设备列表, 摄像头列表)
                    Map<String, Ys7Device> deviceMap = e.getT1()
                            .stream()
                            .collect(Collectors.toMap(Ys7Device::getDeviceSerial, o -> o, (o, n) -> n));
                    log.info("禁用{}个萤石云摄像头: storeId: {}", cameraRwDs.disableSiteByCameraSiteId(storeId), storeId);

                    List<CameraRecorderPo> recorderLocalList = cameraRecorderRoDs.getByStoreId(storeId);
                    Map<String, CameraRecorderPo> recorderMap = recorderLocalList.stream()
                            .collect(Collectors.toMap(CameraRecorderPo::getDeviceSerial, o -> o, (o, n) -> n));

                    if(CollectionUtils.isNotEmpty(recorderLocalList)) {
                        log.info("更新直连设备状态 count: {}", recorderLocalList.size());
                        recorderLocalList.forEach(recorderLocal -> {
                            Ys7Device remotrecorderRemote = deviceMap.get(recorderLocal.getDeviceSerial());
                            if(remotrecorderRemote != null) {
                                if(!NumberUtils.equals(recorderLocal.getStatus(),
                                        remotrecorderRemote.getStatus())) {
                                    log.info("更新直连设备id: {}状态: {}->{}", recorderLocal.getId(),
                                            recorderLocal.getStatus(), remotrecorderRemote.getStatus());
                                    recorderLocal.setStatus(remotrecorderRemote.getStatus());
                                    log.info("更新结果: {}", cameraRecorderRwDs.updateCameraRecorder(recorderLocal));
                                } else {
                                    log.debug("状态未改变id: {}", recorderLocal.getId());
                                }
                            } else {
                                log.error("设备不存在于萤石云但本地仍然留存: {}", recorderLocal);
                            }
                        });
                    } else {
                        log.info("没有可更新的直连设备");
                    }

                    e.getT2()
                            .stream()
                            .map(CameraYs7Service::convert2CameraPo)
                            .map(es -> {
                                es.setCameraSiteId(storeId);
                                if (deviceMap.get(es.getDeviceSerial()) != null) {
                                    es.setDeviceId(deviceMap.get(es.getDeviceSerial()).getId()) // FIXME 子帐户token请求时，id可能是空
//                                            .setDeviceName(deviceMap.get(es.getDeviceSerial()).getDeviceName())
                                            .setDeviceModel(deviceMap.get(es.getDeviceSerial()).getDeviceType());
                                }

                                if(recorderMap.get(es.getDeviceSerial()) != null) {
                                    es.setRecorderId(recorderMap.get(es.getDeviceSerial()).getId());

                                    // FIXME 直连的摄像头不需要使用录像机序列号
                                    // 接口返回的摄像头deviceSerial是录像机的deviceSerial
                                    // 该接口无法获得摄像头自身deviceSerial
                                    // 故 此处将摄像头的deviceSerial设置为null
                                    es.setDeviceSerial(null);
                                }

                                // FIXME 摄像头状态已被官网废弃，此处可能

                                boolean b = cameraRwDs.insertOrUpdate(es);
                                log.info("入库萤石云摄像头: {}, {}", es.getChannelId(), b);
                                return es;
                            })
                            .forEach(es -> {
                                CameraVo byCameraId = cameraRoDs.getByCameraId(es.getId());
                                if(byCameraId != null) {
                                    this.flushCameraCaptureUrl(byCameraId).subscribe();
                                }
                            });
                })
                .map(e -> RestUtils.success());

//        return ys7Client.syncCamera(cameraSite.getAccessToken(), hkListReqMsg)
//                .doOnNext(e -> {
//                    log.info("删除{}个萤石云摄像头", cameraRwDs.disableSiteByCameraSiteId(storeId));
//                    e.getData()
//                            .stream()
//                            .map(CameraYs7Service::convert2CameraPo)
//                            .forEach(es -> {
//                                es.setCameraSiteId(storeId);
//                                boolean b = cameraRwDs.insertOrUpdate(es);
//                                log.info("入库萤石云摄像头: {}, {}", es.getChannelId(), b);
//                            });
//                }).map(e -> RestUtils.success());
    }

    public static CameraPo convert2CameraPo(Ys7Camera in) {
        CameraPo out = new CameraPo();
        return out.setChannelId(in.getId())
                .setDeviceSerial(in.getDeviceSerial())
                .setChannelNo(in.getChannelNo())
                .setChannelName(in.getChannelName())
                .setChannelStatus(in.getStatus())
                .setChannelPicUrl(in.getPicUrl())
                .setEnable(true);
    }

    public static CameraVo convert2CameraVo(Ys7DeviceCamera in) {
        CameraVo out = new CameraVo();
        out.setIpcSerial(in.getIpcSerial())
                .setChannelId(in.getId())
                .setDeviceSerial(in.getDeviceSerial())
                .setChannelNo(in.getChannelNo())
                .setChannelName(in.getChannelName())
                .setChannelStatus(in.getStatus())
                .setChannelPicUrl(in.getPicUrl())
                .setEnable(true);
        return out;
    }

    public Mono<ObjectResponse<Boolean>> updateChannel(CameraVo param) {
        CameraPo cameraPo = cameraRoDs.getById(param.getId());
        IotAssert.isNotNull(cameraPo, "相机不存在");
        IotAssert.isTrue(cameraPo.getEnable(), "相机未启用");

        // FIXME 直连的摄像头不需要检查录像机
        CameraRecorderPo recorderPo = cameraRecorderRoDs.getById(cameraPo.getRecorderId());
        IotAssert.isNotNull(recorderPo, "录像机设备不存在");
        IotAssert.isTrue(recorderPo.getEnable(), "录像机设备当前不可用");

        CameraSitePo cameraSitePo = cameraSiteRoDs.getById(cameraPo.getCameraSiteId());
        IotAssert.isNotNull(cameraSitePo, "对应场站不存在");
        IotAssert.isTrue(cameraSitePo.getEnable(), "场站未启用");

        CameraAccountPo cameraAccountPo = cameraAccountRoDs.getById(cameraSitePo.getAccountId());
        IotAssert.isNotNull(cameraAccountPo, "账户不存在");
        IotAssert.isTrue(cameraAccountPo.getEnable(), "场站未启用");

        return ys7Client.updateCameraName(cameraAccountPo.getClientId(),
//                        cameraPo.getDeviceSerial(),
                        // FIXME 直连的摄像头不需要使用录像机序列号
                        recorderPo.getDeviceSerial(),
                        param.getChannelName(),
                        cameraPo.getChannelNo())
                .map(e -> {
                    log.info("修改通道名称res: {}", param.getId());
                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                    return Boolean.TRUE;
                }).doOnNext(e -> {
                    log.info("通道名称更新: {}", cameraRwDs.updateCamera(param));
                })
                .map(RestUtils::buildObjectResponse);
    }
}