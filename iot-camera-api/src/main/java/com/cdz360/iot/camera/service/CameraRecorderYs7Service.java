package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.camera.client.Ys7Client;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRecorderRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.ds.rw.CameraRecorderRwDs;
import com.cdz360.iot.ds.rw.CameraRwDs;
import com.cdz360.iot.ds.rw.CameraSiteRwDs;
import com.cdz360.iot.model.camera.param.CameraRecorderParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import com.cdz360.iot.model.camera.type.CameraRecordType;
import com.cdz360.iot.model.camera.vo.CameraRecorderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @Classname CameraRecorderYs7Service
 * @Description 硬盘录像机相关
 * @Date 9/22/2021 9:48 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class CameraRecorderYs7Service {

    @Autowired
    private CameraRecorderRoDs cameraRecorderRoDs;

    @Autowired
    private CameraRecorderRwDs cameraRecorderRwDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private CameraSiteRwDs cameraSiteRwDs;

    @Autowired
    private CameraRwDs cameraRwDs;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Autowired
    private Ys7Client ys7Client;

    @Autowired
    private StoreYs7Service storeYs7Service;

    public Mono<ObjectResponse<Boolean>> addRecorder(CameraRecorderVo param) {

        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getDeviceSerial(), "请传入录像机设备序列号");
        IotAssert.isNotBlank(param.getValidateCode(), "请传入录像机设备验证码");
        IotAssert.isNotBlank(param.getDeviceName(), "请传入录像机设备名称");
        IotAssert.isNotBlank(param.getSiteId(), "请传入站点id");


        CameraRecorderPo recorder = cameraRecorderRoDs.findByDeviceSerial(param.getDeviceSerial(), true);
        IotAssert.isNull(recorder, "录像机设备已存在，无法添加");

        CameraSitePo bySiteId = cameraSiteRoDs.getBySiteId(param.getSiteId());
        if(bySiteId != null && !bySiteId.getEnable()) {
            IotAssert.isTrue(false, "场站对应子账号当前已禁用");
        }

        Mono<Pair<CameraSitePo, CameraAccountPo>> objectResponseMono;
        if(bySiteId == null) {
            log.info("创建场站对应子账号");
            CameraSitePo cameraSitePo = new CameraSitePo();
            cameraSitePo.setSiteId(param.getSiteId());
            objectResponseMono = storeYs7Service.addSubAccount(cameraSitePo)
                    .doOnNext(e -> {
                        param.setCameraSiteId(e.getData());
                    })
                    .map(e -> {
                        Long siteId = e.getData();
                        CameraSitePo newSite = cameraSiteRoDs.getById(siteId);

                        CameraAccountPo account = cameraAccountRoDs.getById(newSite.getAccountId());
                        IotAssert.isNotNull(account, "找不到对应主账号: " + newSite.getAccountId());
                        IotAssert.isTrue(account.getEnable(), "主账号当前已禁用: " + newSite.getAccountId());
                        return Pair.of(newSite, account);
                    });
        } else {
            param.setCameraSiteId(bySiteId.getId());

            CameraAccountPo account = cameraAccountRoDs.getById(bySiteId.getAccountId());
            IotAssert.isNotNull(account, "找不到对应主账号: " + bySiteId.getAccountId());
            IotAssert.isTrue(account.getEnable(), "主账号当前已禁用: " + bySiteId.getAccountId());
            objectResponseMono = Mono.just(Pair.of(bySiteId, account));
        }

        return objectResponseMono.flatMap(e -> {
//                    IotAssert.isTrue(e.getFirst() != null, "子账号异常");
                    return ys7Client.addDevice(e.getSecond().getClientId(),
                                    param.getDeviceSerial(),
                                    param.getValidateCode())
                            .map(ret -> {
                                log.info("创建硬盘录像相机或摄像头设备res:{}", ret);
                                IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(ret.getCode()), ret.getMsg());
                                return e;
                            });
                })
                .flatMap(e -> {
                    return ys7Client.updateDeviceName(e.getSecond().getClientId(),
                                    param.getDeviceSerial(),
                                    param.getDeviceName())
                            .map(ret -> {
                                log.info("修改硬盘录像相机或摄像头设备名称res:{}", ret);
                                IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(ret.getCode()), ret.getMsg());
                                return e;
                            });
                })
                .doOnNext(e -> {
//                    log.info("创建硬盘录像相机设备res:{}", e);
//                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                    String statement = CameraRecorderYs7Service.getStatement(param.getDeviceSerial());
                    ys7Client.statementAdd(e.getSecond().getClientId(), statement, e.getFirst().getCameraSiteId())
                            .map(ret -> {
                                log.info("增加子账户权限res:{}", ret);
                                IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(ret.getCode()), ret.getMsg());
                                return e;
                            })
                            .subscribe();
                })
                .doOnNext(e -> {
//                    log.info("增加子账户权限res:{}", e);
//                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                    ys7Client.encryptOff(e.getSecond().getClientId(),
                                    param.getDeviceSerial(),
                                    param.getValidateCode())
                            .map(ret -> {
                                log.info("解除硬盘录像相机设备加密res: {}", ret.getMsg());
                                return e;
                            })
                            .subscribe();
                })
                .flatMap(e -> {
                    return ys7Client.deviceInfo(e.getSecond().getClientId(), param.getDeviceSerial())
                            .map(ret -> {
                                log.info("获取单个设备信息res:{}", ret);
                                IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(ret.getCode()), ret.getMsg());
                                IotAssert.isNotNull(ret.getData(), "获取录像机设备信息失败");
                                param.setStatus(ret.getData().getStatus());
                                param.setModel(ret.getData().getModel());
                                return e;
                            });
                })
                .flatMap(e -> {
//                    log.info("解除硬盘录像相机设备加密res: {}", e.getMsg());
                    return ys7Client.deviceCameraList(e.getSecond().getClientId(), param.getDeviceSerial());
                })
                .flatMap(e -> {
                    log.info("获取录像机设备的通道信息res: {}", param.getDeviceSerial());
                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());

                    // 判断是否是直连摄像头
                    if(e.getData().size() == 1 &&
                            StringUtils.equalsIgnoreCase(e.getData().get(0).getDeviceSerial(),
                                    e.getData().get(0).getIpcSerial()) &&
                            !e.getData().get(0).getRelatedIpc()// FIXME 当前通道是否关联IPC, 可能不是判断直连的依据
                    ) {
                        log.info("直连摄像头: {}", JsonUtils.toJsonString(e));
                        param.setType(CameraRecordType.IPC);
                    } else {
                        log.info("硬盘录像机: {}", JsonUtils.toJsonString(e));
                        param.setType(CameraRecordType.NRV);
                    }

                    param.setEnable(true);
                    boolean b = cameraRecorderRwDs.insertCameraRecorder(param);

                    log.info("清除所有关联在录像机下的设备通道: {}", param.getDeviceSerial());
                    int removedCount = cameraRwDs.disableSiteByDeviceSerial(param.getDeviceSerial());
                    log.info("清除设备通道个数: {}", removedCount);
                    e.getData()
                            .stream()
                            .map(CameraYs7Service::convert2CameraVo)
                            .forEach(channel -> {
                                channel.setCameraSiteId(param.getCameraSiteId());
                                channel.setRecorderId(param.getId());

                                if(StringUtils.isBlank(channel.getDeviceName())) {
                                    channel.setDeviceName("视频" + channel.getChannelNo());
                                }
                                channel.setDeviceModel(param.getModel());

                                // 使用接口返回ipcSerial写入摄像头记录
                                // 实际观测，当deviceSerial != ipcSerial时，摄像头的序列号为ipcSerial
                                if(CameraRecordType.NRV.equals(param.getType())) {
                                    // NRV重置序列号
                                    if (!channel.getDeviceSerial().equals(channel.getIpcSerial())) {
                                        channel.setDeviceSerial(channel.getIpcSerial());
                                    } else {
                                        channel.setDeviceSerial(null);
                                    }
                                }

                                // TODO 若需同旧版本，可能需要添加这些字段
                                /*channel.setDeviceId(deviceMap.get(es.getDeviceSerial()).getId())
                                        .setDeviceName(deviceMap.get(es.getDeviceSerial()).getDeviceName())
                                        .setDeviceModel(deviceMap.get(es.getDeviceSerial()).getDeviceType());*/

                                cameraRwDs.insertOrUpdate(channel);
                            });
                    return Mono.just(b);
                }).doOnError(e -> {
                    // TODO 调用异常时，可能需要解除录像机的绑定
                })
//                .map(e -> {
//                    param.setEnable(true);
//                    return cameraRecorderRwDs.insertCameraRecorder(param);
//                })
                .map(RestUtils::buildObjectResponse);
    }

    public Mono<BaseResponse> deleteRecorder(CameraRecorderVo param) {

        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getDeviceSerial(), "请传入录像机设备序列号");

        CameraRecorderPo recorder = cameraRecorderRoDs.findByDeviceSerial(param.getDeviceSerial(), true);
        IotAssert.isNotNull(recorder, "硬盘录像机不存在或已经删除");

        CameraSitePo bySiteId = cameraSiteRoDs.getById(recorder.getCameraSiteId());
        IotAssert.isNotNull(bySiteId, "场站对应子账号未创建");

        CameraAccountPo account = cameraAccountRoDs.getById(bySiteId.getAccountId());
        IotAssert.isNotNull(account, "找不到对应主账号: " + bySiteId.getAccountId());
        IotAssert.isTrue(account.getEnable(), "主账号当前已禁用: " + bySiteId.getAccountId());

        return ys7Client.deviceDelete(account.getClientId(), param.getDeviceSerial())
                .doOnNext(e -> {
                    log.info("删除硬盘录像机res: {}", e);
                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                    ys7Client.statementDelete(account.getClientId(),
                                    param.getDeviceSerial(),
                                    bySiteId.getCameraSiteId())
                            .subscribe();
                })
                .map(e -> {
//                    log.info("删除子账户权限res:{}", e);
//                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());

                    log.info("禁用硬盘录像机: {}, 结果: {}",
                            param.getDeviceSerial(), cameraRecorderRwDs.disableById(recorder.getId()));

                    log.info("清除所有关联在该录像机下的设备通道, record id: {}", param.getId());
                    int removedCount = cameraRwDs.disableSiteByDeviceId(param.getId());
                    log.info("清除设备通道个数: {}", removedCount);
                    return e;
                })
                .map(e -> RestUtils.success());
    }

    private static String getStatement(String deviceSerial) {
        return "{\"Permission\":\"Get,Capture,Real,Replay\",\"Resource\":[\"dev:" + deviceSerial + "\"]}";
    }

    public Mono<ListResponse<CameraRecorderVo>> listRecorder(CameraRecorderParam param) {
        if(param.getStart() != null && param.getSize() != null) {
            return Mono.just(new ListResponse<>(cameraRecorderRoDs.listRecorder(param),
                    cameraRecorderRoDs.listRecorderTotal(param)));
        } else {
            return Mono.just(new ListResponse<>(cameraRecorderRoDs.listRecorder(param)));
        }
    }

    public Mono<ObjectResponse<Boolean>> updateRecorder(CameraRecorderVo param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotNull(param.getId(), "请传入设备id");
        IotAssert.isNotBlank(param.getDeviceName(), "请传入设备名称");

        CameraRecorderPo cameraRecorderPo = cameraRecorderRoDs.getById(param.getId());
        IotAssert.isNotNull(cameraRecorderPo, "录像机不存在");
        IotAssert.isTrue(cameraRecorderPo.getEnable(), "录像机未启用");

        CameraSitePo cameraSitePo = cameraSiteRoDs.getById(cameraRecorderPo.getCameraSiteId());
        IotAssert.isNotNull(cameraSitePo, "对应场站不存在");
        IotAssert.isTrue(cameraSitePo.getEnable(), "场站未启用");

        CameraAccountPo cameraAccountPo = cameraAccountRoDs.getById(cameraSitePo.getAccountId());
        IotAssert.isNotNull(cameraAccountPo, "账户不存在");
        IotAssert.isTrue(cameraAccountPo.getEnable(), "主账号未启用");

        return ys7Client.updateDeviceName(cameraAccountPo.getClientId(),
                        cameraRecorderPo.getDeviceSerial(),
                        param.getDeviceName())
                .map(e -> {
                    log.info("修改录像机名称res: {}", param.getDeviceSerial());
                    IotAssert.isTrue(CameraConstant.YS_7_RES_CODE_OK.equals(e.getCode()), e.getMsg());
                    return Boolean.TRUE;
                }).doOnNext(e -> {
                    log.info("录像机名称更新: {}", cameraRecorderRoDs.updateRecorder(param));
                    if ( CameraRecordType.IPC == cameraRecorderPo.getType()) {
                        log.info("直连摄像头设备长宽比更新: {}",
                            cameraRwDs.updateAspectRatioByRecordId(param.getId(), param.getAspectRatio()));
                    }
                })
                .map(RestUtils::buildObjectResponse);
    }
}