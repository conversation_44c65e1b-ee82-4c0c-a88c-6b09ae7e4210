package com.cdz360.iot.camera.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HkListReqMsg extends CameraReqMsg {

    @Schema(description = "当前页码（大于0）")
    private Integer pageNo;

    @Schema(description = "每页记录数，最大为999")
    private Integer pageSize;

    @Schema(description = "每页记录数，最大为999")
    private String storeId;
}
