package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname HkAddressLimit
 * @Description
 * @Date 7/29/2021 8:40 AM
 * @Created by Rafael
 */
@Schema(description = "海康云眸直播地址")
@Data
@Accessors(chain = true)
public class HkAddressLimit {

    @Schema(description = "设备序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceSerial;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceName;

    @Schema(description = "通道ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelId;

    @Schema(description = "通道名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String channelName;

    @Schema(description = "通道号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer channelNo;

    @Schema(description = "HLS流畅标准流预览地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String hls;

    @Schema(description = "HLS高清标准流预览地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String hlsHd;

    @Schema(description = "RTMP流畅标准流预览地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String rtmp;

    @Schema(description = "RTMP高清标准流预览地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String rtmpHd;

    @Schema(description = "地址使用状态：0-未使用或标准流预览已关闭，1-使用中，2-已过期，3-标准流预览已暂停，0状态不返回地址，其他返回。")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer status;

    @Schema(description = "地址异常状态：0-正常，1-设备不在线，2-设备开启视频加密，3-设备删除，4-失效，5-未绑定，6-账户下流量已超出，0/1/2/6状态返回地址，其他不返回。")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer exception;

    @Schema(description = "开始时间, long格式如1472694964067，精确到毫秒。expireTime参数为空时该字段无效")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long beginTime;

    @Schema(description = "过期时间，long格式如1472794964067，精确到毫秒。expireTime参数为空时该字段无效")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long endTime;
}