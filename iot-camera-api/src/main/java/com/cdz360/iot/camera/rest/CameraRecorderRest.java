package com.cdz360.iot.camera.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.camera.service.CameraRecorderYs7Service;
import com.cdz360.iot.model.camera.param.CameraRecorderParam;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.vo.CameraRecorderVo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @Classname CameraRecorderRest
 * @Description
 * @Date 9/22/2021 9:40 AM
 * @Created by Rafael
 */
@Slf4j
@RestController
@Tag(name = "监控硬盘录像机设备相关接口", description = "监控硬盘录像机设备相关接口")
public class CameraRecorderRest {

    @Autowired
    private CameraRecorderYs7Service cameraRecorderYs7Service;

    @Operation(summary = "新增监控硬盘录像机")
    @PostMapping(value = "/inner/cameraRecorder/addRecorder")
    public Mono<ObjectResponse<Boolean>> addRecorder(@RequestBody CameraRecorderVo param) {
        log.info("新增监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraRecorderYs7Service.addRecorder(param);
    }

    @Operation(summary = "修改监控硬盘录像机信息")
    @PostMapping(value = "/inner/cameraRecorder/updateRecorder")
    public Mono<ObjectResponse<Boolean>> updateRecorder(@RequestBody CameraRecorderVo param) {
        log.info("修改监控硬盘录像机信息: siteId = {}", JsonUtils.toJsonString(param));
        return cameraRecorderYs7Service.updateRecorder(param);
    }

    @Operation(summary = "删除监控硬盘录像机")
    @PostMapping(value = "/inner/cameraRecorder/deleteRecorder")
    public Mono<BaseResponse> deleteRecorder(@RequestBody CameraRecorderVo param) {
        log.info("删除监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraRecorderYs7Service.deleteRecorder(param);
    }

    @Operation(summary = "查询监控硬盘录像机")
    @PostMapping(value = "/inner/cameraRecorder/listRecorder")
    public Mono<ListResponse<CameraRecorderVo>> listRecorder(@RequestBody CameraRecorderParam param) {
        log.info("查询监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraRecorderYs7Service.listRecorder(param);
    }
}