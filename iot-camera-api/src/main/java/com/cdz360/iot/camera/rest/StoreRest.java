package com.cdz360.iot.camera.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.camera.service.StoreService;
import com.cdz360.iot.camera.service.StoreYs7Service;
import com.cdz360.iot.model.camera.dto.CameraSiteDto;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "门店（子账号）相关接口", description = "门店（子账号）相关接口")
public class StoreRest {

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreYs7Service storeYs7Service;

//    @Operation(summary = "同步门店信息")
//    @PostMapping(value = "/inner/store/syncStore")
//    public Mono<BaseResponse> syncStore(
//            @Parameter(name = "账号id", required = true) @RequestParam(value = "id") Long id) {
//        log.info("同步门店信息: id = {}", id);
//        return storeService.syncStore(id);
//    }

    @Operation(summary = "同步门店信息")
    @PostMapping(value = "/inner/store/syncSubAccount")
    public Mono<BaseResponse> syncSubAccount(
        @Parameter(name = "账号id", required = true) @RequestParam(value = "id") Long id) {
        log.info("同步门店信息: id = {}", id);
        return storeYs7Service.syncStore(id);
    }

    @Operation(summary = "获取门店信息")
    @GetMapping(value = "/inner/store/getStore")
    Mono<ObjectResponse<CameraSitePo>> getStore(@RequestParam(value = "siteId") String siteId) {
        log.info("获取门店信息: siteId = {}", siteId);
        return storeService.getStore(siteId);
    }

    @Operation(summary = "获取门店信息列表")
    @GetMapping(value = "/inner/store/getStoreList")
    Mono<ListResponse<CameraSiteDto>> getStoreList(@RequestBody ListCameraParam param) {
        log.info("获取有摄像头的场站列表: param = {}", JsonUtils.toJsonString(param));
        return storeService.getStoreList(param);
    }

    @Operation(summary = "创建子账号")
    @PostMapping(value = "/inner/store/addSubAccount")
    Mono<ObjectResponse<Long>> addSubAccount(@RequestBody CameraSitePo param) {
        log.info("创建子账号: param = {}", JsonUtils.toJsonString(param));
        return storeYs7Service.addSubAccount(param);
    }
}
