package com.cdz360.iot.camera.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname HkAddressLimit
 * @Description
 * @Date 7/29/2021 8:40 AM
 * @Created by Rafael
 */
@Schema(description = "海康云眸开通直播响应")
@Data
@Accessors(chain = true)
public class HkLiveOpen {
    @Schema(description = "设备序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String deviceSerial;

    @Schema(description = "通道号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer channelNo;

    @Schema(description = "返回码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ret;

    @Schema(description = "描述")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String desc;
}