package com.cdz360.iot.camera.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.camera.client.HkClient;
import com.cdz360.iot.camera.cfg.CameraConstant;
import com.cdz360.iot.camera.model.*;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.CameraAccountRoDs;
import com.cdz360.iot.ds.ro.CameraRoDs;
import com.cdz360.iot.ds.ro.CameraSiteRoDs;
import com.cdz360.iot.ds.rw.CameraRwDs;
import com.cdz360.iot.model.camera.param.CameraVideoParam;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import com.cdz360.iot.model.camera.po.CameraPo;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@Service
@Deprecated
public class CameraService implements ICameraService {

    @Autowired
    private HkClient hkClient;

    @Autowired
    private CameraRwDs cameraRwDs;

    @Autowired
    private CameraRoDs cameraRoDs;

    @Autowired
    private CameraSiteRoDs cameraSiteRoDs;

    @Autowired
    private CameraAccountRoDs cameraAccountRoDs;

    @Value("${camera.capture.liveExpire:0}")
    private long CAMERA_LIVE_EXPIRE_SECOND = 0; // 过期秒数, max: ********,0表示长期地址
    @Value("${camera.capture.quality:0}")
    private int CAMERA_CAPTURE_QUALITY = 0; // 截图清晰度,0-流畅,1-高清(720P),2-FCIF,3-1080P,4-400w

    public Mono<ListResponse<CameraVo>> siteCameraList(ListCameraParam param) {
        return Mono.justOrEmpty(param)
                .map(id -> RestUtils.buildListResponse(cameraRoDs.getBySiteId(param)))
                .switchIfEmpty(Mono.error(new DcArgumentException("场站ID无效")));
    }

    @Override
    public Mono<BaseResponse> syncCamera(Long storeId, Integer pageNo) {
        CameraSitePo cameraSite = cameraSiteRoDs.getById(storeId);
        IotAssert.isNotNull(cameraSite, "门店信息不存在");
        IotAssert.isTrue(cameraSite.getEnable(), "门店已禁用");

        CameraAccountPo account = cameraAccountRoDs.getById(cameraSite.getAccountId());
        IotAssert.isNotNull(account, "未配置平台账号信息");
        IotAssert.isTrue(account.getEnable(), "未配置平台账号已禁用");

        HkListReqMsg hkListReqMsg = new HkListReqMsg();
        hkListReqMsg.setPageNo(1)
                .setPageSize(999)
                .setStoreId(cameraSite.getCameraSiteId());
        return hkClient.syncCamera(account.getClientId(), hkListReqMsg)
                .doOnNext(e -> {
                    log.info("删除{}个相机", cameraRwDs.disableSiteByCameraSiteId(storeId));
                    e.getData().getRows()
                            .stream()
                            .map(CameraService::********************)
                            .map(es -> {
                                es.setCameraSiteId(storeId);
                                boolean b = cameraRwDs.insertOrUpdate(es);
                                log.info("入库摄像头: {}, {}", es.getChannelId(), b);
                                return es;
                            })
                            .filter(es -> NumberUtils.equals(es.getChannelStatus(), CameraConstant.CAMERA_STATUS_ONLINE))
                            .forEach(es -> this.flushCameraCaptureUrl(es.getId()).subscribe());
                }).map(e -> RestUtils.success());
    }

    public Mono<BaseResponse> syncCameraBySiteId(String siteId) {
        CameraSitePo cameraSitePo = cameraSiteRoDs.getBySiteId(siteId);
        IotAssert.isNotNull(cameraSitePo, "该场站未关联门店");
        return this.syncCamera(cameraSitePo.getId(), 0);
    }

    private static CameraPo ********************(HkCamera hkCamera) {
        CameraPo ret = new CameraPo();
        BeanUtils.copyProperties(hkCamera, ret);
        ret.setEnable(true);
        return ret;
    }

    // 开启直播模式
    public Mono<BaseResponse> openLiveVideo(Long cameraId) {
        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(cameraId);
        if(pair == null) {
            return Mono.empty();
        }
        CameraPo cameraPo = pair.getFirst();
        CameraAccountPo cameraAccountPo = pair.getSecond();

        return hkClient.openLiveVideo(cameraAccountPo.getClientId(), cameraPo.getChannelId())
                .flatMap(e -> {
                    if(e.getData() == null) {
                        return Mono.error(new DcArgumentException(e.getMessage()));
                    } else {
                        return Mono.just(RestUtils.success());
                    }
                });
    }

    public Mono<BaseResponse> flushCameraLiveAddress(Long cameraId) {
        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(cameraId);
        if(pair == null) {
            return Mono.empty();
        }
        CameraPo cameraPo = pair.getFirst();
        CameraAccountPo cameraAccountPo = pair.getSecond();

        return hkClient.syncCameraLiveAddress(cameraAccountPo.getClientId(), cameraPo.getChannelId(), CAMERA_LIVE_EXPIRE_SECOND)
                .doOnNext(e -> {
                    HkAddressLimit data = e.getData();
                    IotAssert.isNotNull(data, "同步播放地址失败: " + JsonUtils.toJsonString(e));
                    if(CAMERA_LIVE_EXPIRE_SECOND > 0) {
                        cameraPo.setLiveAddress(data.getHls())
                                .setLiveAddressHD(data.getHlsHd());
                        if (data.getEndTime() != null) {
                            cameraPo.setLiveExpireTime(new Date(data.getEndTime()));
                        }
                    } else {
                        cameraPo.setLiveAddress(data.getRtmp())
                                .setLiveAddressHD(data.getRtmpHd())
                                .setLiveExpireTime(null);
                        log.warn("使用长期地址，并设置过期日期为null: cameraId: {}", cameraId);
                    }
                    log.info("更新直播源: {}, {}", JsonUtils.toJsonString(data), cameraRwDs.flushLiveAddress(cameraPo));
                }).map(e -> RestUtils.success());
    }

    public Mono<BaseResponse> flushCameraCaptureUrl(Long cameraId) {
        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(cameraId);
        if(pair == null) {
            return Mono.empty();
        }
        CameraPo cameraPo = pair.getFirst();
        CameraAccountPo cameraAccountPo = pair.getSecond();

        return hkClient.syncCameraCaptureUrl(cameraAccountPo.getClientId(),
                        cameraPo.getDeviceSerial(),
                        cameraPo.getChannelNo(),
                        CAMERA_CAPTURE_QUALITY)
                .doOnNext(e -> {
                    if(e.getData() != null) {
                        HkCameraCapture data = e.getData();
                        cameraPo.setCameraCaptureUrl(data.getPicUrl());
                        log.info("更新抓图: {}, {}", JsonUtils.toJsonString(data), cameraRwDs.flushChannelPicUrl(cameraPo));
                    } else {
                        log.error("抓图失败: cameraId: {}, {}", cameraId, JsonUtils.toJsonString(e));
                    }
                }).map(e -> RestUtils.success());
    }

    public Mono<ObjectResponse<HkVideoUrl>> getCameraVideoUrl(CameraVideoParam param) {
        Pair<CameraPo, CameraAccountPo> pair = this.getCamera(param.getCameraId());
        if(pair == null) {
            return Mono.empty();
        }
        CameraPo cameraPo = pair.getFirst();
        CameraAccountPo cameraAccountPo = pair.getSecond();

        param.setDeviceSerial(cameraPo.getDeviceSerial())
                .setChannelNo(cameraPo.getChannelNo());

        return hkClient.getCameraVideoUrl(cameraAccountPo.getClientId(), param)
                .map(e -> RestUtils.buildObjectResponse(e.getData()));
    }

    private Pair<CameraPo, CameraAccountPo> getCamera(Long cameraId) {
        CameraPo cameraPo = cameraRoDs.getById(cameraId);
//        IotAssert.isNotNull(cameraPo, "摄像头不存在");
//        IotAssert.isTrue(cameraPo.getEnable(), "摄像头当前不可用");
//        IotAssert.isTrue(CameraConstant.CAMERA_STATUS_ONLINE == cameraPo.getChannelStatus(), "摄像头当前状态离线");
        if(cameraPo == null) {
            log.error("摄像头不存在: {}", cameraId);
            return null;
        }
        if(!cameraPo.getEnable()) {
            log.error("摄像头当前不可用: {}", cameraId);
            return null;
        }
        if(CameraConstant.CAMERA_STATUS_ONLINE != cameraPo.getChannelStatus()) {
            log.error("摄像头当前状态离线: {}", cameraId);
            return null;
        }

        CameraSitePo cameraSitePo = cameraSiteRoDs.getById(cameraPo.getCameraSiteId());
//        IotAssert.isNotNull(cameraSitePo, "门店不存在");
//        IotAssert.isTrue(cameraSitePo.getEnable(), "门店当前不可用");
        if(cameraSitePo == null) {
            log.error("门店不存在: {}", cameraPo.getCameraSiteId());
            return null;
        }
        if(!cameraSitePo.getEnable()) {
            log.error("门店当前不可用: {}", cameraPo.getCameraSiteId());
            return null;
        }

        CameraAccountPo cameraAccountPo = cameraAccountRoDs.getById(cameraSitePo.getAccountId());
//        IotAssert.isNotNull(cameraAccountPo, "账号不存在");
//        IotAssert.isTrue(cameraAccountPo.getEnable(), "账号当前不可用");
        if(cameraAccountPo == null) {
            log.error("账号不存在: {}", cameraSitePo.getAccountId());
            return null;
        }
        if(!cameraAccountPo.getEnable()) {
            log.error("账号当前不可用: {}", cameraSitePo.getAccountId());
            return null;
        }
        return Pair.of(cameraPo, cameraAccountPo);
    }
}
