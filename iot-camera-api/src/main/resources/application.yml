app:
  name: iot-camera-api

server:
  address: 0.0.0.0
  port: 8090
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-camera-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

management:
  context-path: /admin
  security:
    enabled: false


eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000



logging:
  level:
    com.cdz360.iot: 'DEBUG'
    org.springframework: 'WARN'
    org.mybatis: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.iot.camera.rest
  swagger-ui:
    path: /swagger-ui.html

camera:
  hk:
    clientId: 00c56778b10e4777bb9ce931b76e203a
    clientSecret: 18f30e6a4bd7444a8b5a58eb5444fb3c
    host: https://api2.hik-cloud.com
    oauthTokenUri: /oauth/token
    storeInfoUri: /v1/customization/storeInfo
    cameraListUri: /v1/customization/cameraList
    accountInfo: /v1/ezviz/account/info
    liveAddressLimit: /v1/customization/liveAddressLimit
    capture: /v1/customization/capture
    getVideoUrl: /v1/carrier/wing/endpoint/video/getVideoUrl
    liveVideoOpen: /v1/customization/liveVideoOpen
  taskRate:
    flushCameraCaptureUrl: 1200000 # 摄像头截图频率毫秒数
  capture:
    quality: 0 # 截图清晰度, 0-流畅,1-高清(720P),2-FCIF,3-1080P,4-400w
    liveExpire: 0 # 直播地址过期秒数, max: ********, 0表示长期地址
  account:
    defaultSubAccountPassword: "654321"
    defaultMainAccountId: 2 # 默认主账号id：t_camera_account.id
    accountExpireTime: 5 # AccountExpireTime过期天数
  ys7:
    host: https://open.ys7.com
    mainTokenUrl: /api/lapp/token/get
    accountList: /api/lapp/ram/account/list
    subTokenUrl: /api/lapp/ram/token/get
    cameraList: /api/lapp/camera/list
    deviceList: /api/lapp/device/list
    capture: /api/lapp/device/capture
    liveAddressLimit: /api/lapp/live/address/limited
    liveVideoOpen: /api/lapp/live/video/open
    liveAddressGet: /api/lapp/v2/live/address/get
    createAccount: /api/lapp/ram/account/create
    addRecorderDevice: /api/lapp/device/add
    encryptOff: /api/lapp/device/encrypt/off
    deviceCameraList: /api/lapp/device/camera/list
    deviceDelete: /api/lapp/device/delete
    statementAdd: /api/lapp/ram/statement/add
    statementDelete: /api/lapp/ram/statement/delete
    updateRecorderName: /api/lapp/device/name/update
    updateCameraName: /api/lapp/camera/name/update
    deviceInfo: /api/lapp/device/info
