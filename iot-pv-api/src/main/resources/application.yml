app:
  name: iot-pv

server:
  address: 0.0.0.0
  port: 8082
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-pv-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01


management:
  context-path: /admin
  security:
    enabled: false
  endpoints:
    web:
      exposure:
        include: info,health,shutdown,metrics,beans
  endpoint:
    shutdown:
      enabled: true



eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000



logging:
  level:
    com.cdz360.iot: 'DEBUG'
    org.springframework: 'WARN'
    org.mybatis: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"


springdoc:
  packagesToScan: com.cdz360.iot.pv.rest
  swagger-ui:
    path: /swagger-ui.html