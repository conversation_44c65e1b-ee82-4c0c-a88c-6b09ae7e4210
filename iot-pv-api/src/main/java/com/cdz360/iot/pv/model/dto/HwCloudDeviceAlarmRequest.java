package com.cdz360.iot.pv.model.dto;

import lombok.Data;

/**
 * 华为云设备活动告警接口请求参数
 */
@Data
public class HwCloudDeviceAlarmRequest {

    /**
     * 电站编号列表，多个电站用英文逗号分隔，电站编号由电站列表接口中plantCode获取
     * 选填
     */
    private String stationCodes;
    
    /**
     * 设备SN列表，多个设备用英文逗号分隔，参数stationCodes和sns两个至少填一个，如果两个参数同时填写，则按照stationCodes查询优先
     * 选填
     */
    private String sns;
    
    /**
     * 查询活动告警的开始时间戳（毫秒），例如: 1501862400000
     * 必填
     */
    private Long beginTime;
    
    /**
     * 查询活动告警的结束时间戳（毫秒），例如: 1501862400000
     * 必填
     */
    private Long endTime;
    
    /**
     * 语言，必须是zh_CN, en_US, ja_JP, it_IT, nl_NL, pt_BR, de_DE, fr_FR, es_ES, pl_PL, ro_RO, cs_CZ其中一个
     * zh_CN: 中文
     * en_US: 英语
     * ja_JP: 日语
     * it_IT: 意大利语
     * nl_NL: 荷兰语
     * pt_BR: 葡萄牙语
     * de_DE: 德语
     * fr_FR: 法语
     * es_ES: 西班牙语
     * pl_PL: 波兰语
     * ro_RO: 罗马尼亚语
     * cs_CZ: 捷克语
     * 必填
     */
    private String language;
    
    /**
     * 告警级别，多个用英文逗号分隔，如"1,2"。如果不传该参数或该参数为空，则默认查询所有级别的告警
     * 支持以下告警级别:
     * 1: 严重
     * 2: 重要
     * 3: 次要
     * 4: 提示
     * 选填
     */
    private String levels;
    
    /**
     * 设备类型，多个用英文逗号分隔，如"1,38"。如果不传该参数或该参数为空，则默认查询所有设备类型的告警
     * 支持以下设备类型:
     * 1: 组串式逆变器
     * 2: 数采
     * 8: 箱变
     * 10: 环境监测仪
     * 13: 通管机
     * 16: 通用设备
     * 17: 关口电表
     * 22: PID
     * 37: 品联数采
     * 38: 户用逆变器
     * 39: 储能
     * 40: 并离网控制器
     * 45: PLC
     * 46: 优化器
     * 47: 户用电表
     * 62: Dongle
     * 63: 分布式数采
     * 70: 安全关断盒
     * 60001: 市电
     * 60003: 发电机
     * 60043: 太阳能模块组
     * 60044: 太阳能模块
     * 60092: 功率控制器
     * 60014: 锂电池
     * 60010: 交流输出配电
     * 选填
     */
    private String devTypes;
}