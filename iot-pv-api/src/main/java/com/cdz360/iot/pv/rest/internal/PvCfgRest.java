//package com.cdz360.iot.pv.rest.internal;
//
//import com.cdz360.base.model.base.dto.BaseResponse;
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.iot.model.base.Update;
//import com.cdz360.iot.model.pv.param.ListGtiCfgParam;
//import com.cdz360.iot.model.pv.po.GtiCfgPo;
//import com.cdz360.iot.model.pv.vo.GtiCfgVo;
//import com.cdz360.iot.pv.biz.north.PvCfgService;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Mono;
//
//@Slf4j
//@RestController
//@Tag(name = "逆变器模板相关接口", description = "逆变器模板")
//@RequestMapping("/iot/biz/pvCfg")
//public class PvCfgRest {
//
//    @Autowired
//    private PvCfgService service;
//
//    @PostMapping(value = "/list")
//    public Mono<ListResponse<GtiCfgVo>> list(@RequestBody ListGtiCfgParam param) {
//        log.info("pvCfg list. param: {}", param);
//        return service.list(param);
//    }
//
//    @GetMapping(value = "/getById")
//    public Mono<ObjectResponse<GtiCfgPo>> getById(@RequestParam(value = "cfgId") Long cfgId) {
//        log.info("pvCfg getById. cfgId: {}", cfgId);
//        return service.getById(cfgId);
//    }
//
//    @PostMapping(value = "/add")
//    public Mono<BaseResponse> add(@RequestBody @Validated GtiCfgPo param) {
//        log.info("pvCfg add. param: {}", param);
//        return service.add(param);
//    }
//
//    @PostMapping(value = "/edit")
//    public Mono<BaseResponse> edit(@RequestBody @Validated(Update.class) GtiCfgPo param) {
//        log.info("pvCfg edit. param: {}", param);
//        return service.edit(param);
//    }
//
//    @GetMapping(value = "/del")
//    public Mono<BaseResponse> del(@RequestParam(value = "cfgId") Long cfgId) {
//        log.info("pvCfg del. cfgId: {}", cfgId);
//        return service.del(cfgId);
//    }
//
//}
