package com.cdz360.iot.pv.model.dto;

import lombok.Data;

/**
 * 华为云设备实时数据接口请求参数
 */
@Data
public class HwCloudDeviceRealKpiRequest {

    /**
     * 设备编号列表，多个设备用英文逗号分隔，参考sns字段
     * 选填
     */
    private String devIds;
    
    /**
     * 设备sn列表，多个设备用英文逗号分隔，参考devIds字段
     * 选填
     */
    private String sns;
    
    /**
     * 设备类型ID，以设备管理中获取的devTypeId为标准ID
     * 必填
     */
    private Integer devTypeId;
}