package com.cdz360.iot.pv.rest.external;

import com.cdz360.iot.pv.biz.south.HwCloudClientService;
import com.cdz360.iot.pv.model.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 华为云IoT平台API控制器
 */
@Slf4j
@RestController
@Tag(name = "华为云IoT平台API", description = "华为云IoT平台接口")
@RequestMapping("/iot/hwcloud")
public class HwCloudRest {

    @Autowired
    private HwCloudClientService hwCloudClientService;

    /**
     * 获取电站实时数据（使用请求对象）
     *
     * @param request 电站实时数据请求对象
     * @return 电站实时数据响应
     */
    @Operation(summary = "获取电站实时数据", description = "根据电站代码获取实时KPI数据")
    @PostMapping("/station/realKpi")
    public Mono<HwCloudStationRealKpiResponse> getStationRealKpi(@RequestBody HwCloudStationRealKpiRequest request) {
        log.info("开始获取电站实时数据，电站代码: {}", request.getStationCodes());
        return hwCloudClientService.getStationRealKpi(request)
                .doOnSuccess(response -> log.info("获取电站实时数据成功"))
                .doOnError(error -> log.error("获取电站实时数据失败", error));
    }

    /**
     * 获取设备实时数据
     *
     * @param request 设备实时数据请求对象
     * @return 设备实时数据响应
     */
    @Operation(summary = "获取设备实时数据", description = "根据设备编号或SN获取实时数据")
    @PostMapping("/device/realKpi")
    public Mono<HwCloudDeviceRealKpiResponse> getDeviceRealKpi(@RequestBody HwCloudDeviceRealKpiRequest request) {
        log.info("开始获取设备实时数据，设备编号: {}, 设备SN: {}, 设备类型: {}",
                request.getDevIds(), request.getSns(), request.getDevTypeId());
        return hwCloudClientService.getDevRealKpi(request)
                .doOnSuccess(response -> log.info("获取设备实时数据成功"))
                .doOnError(error -> log.error("获取设备实时数据失败", error));
    }

    /**
     * 获取设备历史数据
     *
     * @param request 设备历史数据请求对象
     * @return 设备历史数据响应
     */
    @Operation(summary = "获取设备历史数据", description = "根据设备编号或SN获取指定时间段内的5分钟粒度历史数据")
    @PostMapping("/device/historyKpi")
    public Mono<HwCloudDeviceHistoryKpiResponse> getDeviceHistoryKpi(@RequestBody HwCloudDeviceHistoryKpiRequest request) {
        log.info("开始获取设备历史数据，设备编号: {}, 设备SN: {}, 设备类型: {}, 开始时间: {}, 结束时间: {}",
                request.getDevIds(), request.getSns(), request.getDevTypeId(),
                request.getStartTime(), request.getEndTime());
        return hwCloudClientService.getDevHistoryKpi(request)
                .doOnSuccess(response -> log.info("获取设备历史数据成功"))
                .doOnError(error -> log.error("获取设备历史数据失败", error));
    }

    /**
     * 获取设备活动告警
     *
     * @param request 设备活动告警请求对象
     * @return 设备活动告警响应
     */
    @Operation(summary = "获取设备活动告警", description = "根据电站编号或设备SN获取活动告警信息，可筛选告警级别和时间范围")
    @PostMapping("/device/alarm")
    public Mono<HwCloudDeviceAlarmResponse> getDeviceAlarm(@RequestBody HwCloudDeviceAlarmRequest request) {
        log.info("开始获取设备活动告警，电站编号: {}, 设备SN: {}, 告警级别: {}, 设备类型: {}, 开始时间: {}, 结束时间: {}, 语言: {}",
                request.getStationCodes(), request.getSns(), request.getLevels(),
                request.getDevTypes(), request.getBeginTime(), request.getEndTime(),
                request.getLanguage());
        return hwCloudClientService.getDevAlarm(request)
                .doOnSuccess(response -> log.info("获取设备活动告警成功"))
                .doOnError(error -> log.error("获取设备活动告警失败", error));
    }

    /**
     * 获取设备日数据
     *
     * @param request 设备日数据请求对象
     * @return 设备日数据响应
     */
    @Operation(summary = "获取设备日数据", description = "根据设备编号或SN获取指定日期的设备日数据")
    @PostMapping("/device/kpiDay")
    public Mono<HwCloudDeviceKpiDayResponse> getDeviceKpiDay(@RequestBody HwCloudDeviceKpiDayRequest request) {
        log.info("开始获取设备日数据，设备编号: {}, 设备SN: {}, 设备类型: {}, 采集时间: {}",
                request.getDevIds(), request.getSns(), request.getDevTypeId(),
                request.getCollectTime());
        return hwCloudClientService.getDevKpiDay(request)
                .doOnSuccess(response -> log.info("获取设备日数据成功"))
                .doOnError(error -> log.error("获取设备日数据失败", error));
    }
}