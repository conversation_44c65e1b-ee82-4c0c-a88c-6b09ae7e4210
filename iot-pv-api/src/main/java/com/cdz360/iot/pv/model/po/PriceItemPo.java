package com.cdz360.iot.pv.model.po;

import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "子价格模板")
@Data
@Accessors(chain = true)
public class PriceItemPo {

    @Schema(description = "子模板ID", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "主模板ID", example = "456")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long templateId;
    /**
     * 时段编号
     */
    @Schema(description = "主模板编号**唯一标识**", example = "ABCD1234")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String code;

    @Schema(description = "时段编号, 0~255. 在单个价格模板内确保唯一", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer num;

    @Schema(description = "版本号", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long version;

    /**
     * 0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷
     */
    @Schema(description = "尖峰平谷标签 0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargePriceCategory category;

    /**
     * 价格时段的开始时间, 范围 0-1440
     * 开始时间取闭区间, 结束时间取开区间.
     * 每日的开始时间为 0点, 当日结束时间为 1440/60= 24点
     * example 420/60=7点
     */
    @Schema(description = "开始时间**单位：分钟**", example = "420")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer startTime;

    /**
     * 价格时段的结束时间, 范围 0-1440
     * 开始时间取闭区间, 结束时间取开区间.
     * 每日的开始时间为 0点, 当日结束时间为 1440/60= 24点
     * example 540/60=8点
     */
    @Schema(description = "结束时间**单位：分钟**", example = "540")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer stopTime;

    /**
     * 电费单价
     */
    @Schema(description = "电费单价, 单位'元', 4位小数", example = "2.1234")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal price;

    /**
     * 服务费单价
     */
    @Schema(description = "服务费单价, 单位'元', 4位小数", example = "2.1234")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servicePrice;

    @Schema(description = "备注", example = "ZXY")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remark;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String createTime;

}
