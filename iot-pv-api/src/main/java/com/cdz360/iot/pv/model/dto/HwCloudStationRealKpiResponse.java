package com.cdz360.iot.pv.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 华为云电站实时数据接口响应
 */
@Data
public class HwCloudStationRealKpiResponse {

    /**
     * 请求成功失败标识
     * true: 请求成功
     * false: 请求失败
     */
    private boolean success;

    /**
     * 错误码
     * 0 表示正常，其他错误码参见错误码列表
     */
    private Integer failCode;

    /**
     * 包含如下信息
     */
    private HwCloudStationRealKpiParams params;

    /**
     * 可选消息
     */
    private String message;

    /**
     * 返回数据，data里面是各个电站的实时数据对象信息列表
     */
    private List<HwCloudStationRealKpiData> data;

    /**
     * 参数信息
     */
    @Data
    public static class HwCloudStationRealKpiParams {
        /**
         * 请求参数中的电站编号列表
         */
        private String stationCodes;

        /**
         * 系统当前时间（毫秒）
         */
        private Long currentTime;
    }

    /**
     * 电站实时数据
     */
    @Data
    public static class HwCloudStationRealKpiData {
        /**
         * 电站编号
         */
        private String stationCode;

        /**
         * 每个数据项的内容，用key-value形式返回，数据项列表参见下方电站实时数据列表
         */
        private Map<String, Object> dataItemMap;
    }
}