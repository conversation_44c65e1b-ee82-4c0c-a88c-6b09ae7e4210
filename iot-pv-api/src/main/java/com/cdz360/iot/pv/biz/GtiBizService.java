package com.cdz360.iot.pv.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.DevCfgRoDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiGridDispatchCfgRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.GtiDailyRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.pv.dto.GtiGridDispatchCfgDto;
import com.cdz360.iot.model.pv.dto.PvCfgDto;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.pv.biz.south.GtiMqService;
import com.cdz360.iot.pv.biz.south.HwCloudClientService;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceKpiDayRequest;
import com.cdz360.iot.pv.model.dto.HwCloudDeviceKpiDayResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class GtiBizService {

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private DevCfgRoDs devCfgRoDs;

    @Autowired
    private GtiCfgRoDs gtiCfgRoDs;

    @Autowired
    private GtiGridDispatchCfgRoDs gtiGridDispatchCfgRoDs;

    @Autowired
    private GtiMqService gtiMqService;

    @Autowired
    private GtiDailyRwDs gtiDailyRwDs;

    @Autowired
    private HwCloudClientService hwCloudClientService;

    /**
     * 下发获取逆变器信息的指令
     *
     * @param siteIdIn
     * @param gtiNos
     * @return
     */
    public Mono<Boolean> getGtiInfo(String siteIdIn, List<String> gtiNos) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = this.getGwInfo(siteId);
                this.gtiMqService.getGtiInfo(gwInfo, gtiNos);
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 下发获取逆变器信息的指令
     *
     * @param dno 逆变器编号
     * @return
     */
    public Mono<Boolean> getGtiInfo(String dno) {
        if (StringUtils.isBlank(dno)) {
            throw new DcArgumentException("逆变器编号无效");
        }

        return Mono.just(dno)
            .doOnNext(d -> {
                GtiPo gti = gtiRoDs.getByDno(d);
                if (null == gti) {
                    throw new DcArgumentException("逆变器不存在或已失效");
                }

                if (StringUtils.isBlank(gti.getGwno())) {
                    throw new DcArgumentException("逆变器控制器编号无效");
                }

                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gti.getGwno(), true);
                if (null == gw) {
                    throw new DcArgumentException("逆变器所属控制器不存在或已失效");
                }

                this.gtiMqService.getGtiInfo(gw, List.of(dno));
            })
            .map(siteId -> Boolean.TRUE);
    }

    /**
     * 修改逆变器配置信息
     *
     * @return
     */
    public Mono<Boolean> modifyGtiCfg(String gwnoIn, String dnoIn, Long cfgId) {
        if (StringUtils.isBlank(gwnoIn)) {
            throw new DcArgumentException("控制器编号无效");
        }
        if (StringUtils.isBlank(dnoIn)) {
            throw new DcArgumentException("逆变器编号无效");
        }
        IotAssert.isNotNull(cfgId, "配置模板ID无效");

        return Mono.just(gwnoIn)
            .doOnNext(gwno -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }

                GtiPo gti = gtiRoDs.getByGwnoAndDno(gwno, dnoIn);
                if (null == gti) {
                    throw new DcArgumentException("控制器不存在指定的逆变器");
                }

                DevCfgPo devCfgPo = devCfgRoDs.getById(cfgId);
                if (null == devCfgPo) {
                    throw new DcArgumentException("配置ID无效");
                }

                SrsPo srsPo = srsRoDs.getOneByGwno(gwno);

                GtiPo update = new GtiPo();
                update.setDno(gti.getDno())
                    .setCfgId(cfgId)
                    .setCfgStatus(EquipCfgStatus.SEND_2_GW);
                gtiRwDs.updateGtiByDno(update);

                PvCfgDto dto = new PvCfgDto();
                dto.setId(gti.getSid())
                    .setSiteId(gti.getSiteId())
                    .setDeviceNo(gti.getDno());
                if (CfgType.GOOD_WE_GTI.equals(devCfgPo.getType())) {
                    GtiCfgPo cfg = gtiCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime())
                        .setBootVoltage(cfg.getBootVoltage())
                        .setMaxVoltage(cfg.getMaxVoltage())
                        .setMinVoltage(cfg.getMinVoltage())
                        .setMaxFrequency(cfg.getMaxFrequency())
                        .setMinFrequency(cfg.getMinFrequency());
                } else if (CfgType.HUAWEI_GTI.equals(devCfgPo.getType())) {
                    GtiGridDispatchCfgPo cfg = gtiGridDispatchCfgRoDs.getById(cfgId);
                    dto.setSamplingTime(cfg.getSamplingTime());

                    GtiGridDispatchCfgDto cfgDto = new GtiGridDispatchCfgDto();
                    BeanUtils.copyProperties(cfg, cfgDto);
                    dto.setGridDispatchCfg(cfgDto);
                } else {
                    throw new DcServiceException("不支持的模板类型");
                }

                this.gtiMqService.modifyGtiCfg(gwInfo,
                    devCfgPo.getVer(),
                    srsPo,
                    false,
                    List.of(dto));
            })
            .map(siteId -> Boolean.TRUE);
    }

    private GwInfoPo getGwInfo(String siteId) {
        GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
        if (gwInfo == null) {
            log.error("can't find gwno for siteId = {}", siteId);
            throw new DcArgumentException("请配置场站对应的网关");
        }
        return gwInfo;
    }

    public Mono<Boolean> uploadGtiDataFile(String siteIdIn, Date date) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }
                this.gtiMqService.uploadGtiDataFile(gwInfo, date, null);
            })
            .map(o -> Boolean.TRUE);

    }

    /**
     * 根据供应商获取设备信息映射
     *
     */
    public Map<String, GtiPo> getDeviceInfoMapByVendor(GtiVendor vendor, Integer code) {
        log.info("开始获取供应商{}的设备信息映射", vendor);
        List<GtiPo> deviceList = gtiRoDs.getByVendor(vendor, code);
        if (CollectionUtils.isEmpty(deviceList)) {
            log.warn("未找到供应商{}的设备信息", vendor);
            return Map.of();
        }

        Map<String, GtiPo> deviceMap = deviceList.stream()
                .filter(device -> StringUtils.isNotBlank(device.getSerialNo()))
                .collect(Collectors.toMap(GtiPo::getSerialNo, device -> device));

        log.info("获取到供应商{}的设备信息映射，设备数量: {}", vendor, deviceMap.size());
        return deviceMap;
    }

    /**
     * 采集并处理华为云设备日数据
     * 包含完整的流程：获取设备序列号 -> 调用华为云API -> 处理数据并入库
     */
    public Mono<String> collectAndProcessDeviceKpiDay(LocalDate collectDate) {
        long collectTime = collectDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        log.info("开始采集华为云设备日数据，采集日期: {}", collectDate);
        Map<String, GtiPo> deviceInfoMap = getDeviceInfoMapByVendor(GtiVendor.HUAWEI, EssEquipType.PV_INV.getCode());
        if (deviceInfoMap.isEmpty()) {
            String message = "未找到华为供应商的设备信息";
            log.warn(message);
            return Mono.just(message);
        }
        List<String> snsList = deviceInfoMap.keySet().stream().collect(Collectors.toList());
        int batchSize = 100;
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < snsList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, snsList.size());
            batches.add(snsList.subList(i, end));
        }
        log.info("设备总数: {}, 分批数量: {}, 每批大小: {}", snsList.size(), batches.size(), batchSize);
        return Mono.fromCallable(() -> {
            List<HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData> allData = new ArrayList<>();
            for (int i = 0; i < batches.size(); i++) {
                List<String> batch = batches.get(i);
                String sns = String.join(",", batch);
                log.info("处理第 {}/{} 批，设备数量: {}", i + 1, batches.size(), batch.size());
                HwCloudDeviceKpiDayRequest request = new HwCloudDeviceKpiDayRequest();
                request.setSns(sns);
                request.setDevTypeId(1);
                request.setCollectTime(collectTime);
                try {
                    HwCloudDeviceKpiDayResponse response = hwCloudClientService.getDevKpiDay(request).block();
                    if (response != null && response.isSuccess() && response.getData() != null) {
                        allData.addAll(response.getData());
                        log.info("第 {}/{} 批数据采集成功，数据条数: {}", i + 1, batches.size(), response.getData().size());
                    } else {
                        log.warn("第 {}/{} 批数据采集失败或无数据", i + 1, batches.size());
                    }
                } catch (Exception e) {
                    log.error("第 {}/{} 批数据采集异常: {}", i + 1, batches.size(), e.getMessage(), e);
                }
            }
            if (!allData.isEmpty()) {
                processDeviceKpiDayDataWithMap(allData, deviceInfoMap);
                String message = String.format("华为云设备日数据处理完成，总计处理 %d 条数据", allData.size());
                log.info(message);
                return message;
            } else {
                String message = "所有批次均无有效数据";
                log.warn(message);
                return message;
            }
        });
    }

    /**
     * 使用设备信息映射处理设备日数据并保存到数据库
     */
    public void processDeviceKpiDayDataWithMap(List<HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData> deviceKpiDataList,
                                               Map<String, GtiPo> deviceInfoMap) {
        log.info("开始使用设备信息映射处理设备日数据，数据条数: {}，设备映射数量: {}",
                deviceKpiDataList != null ? deviceKpiDataList.size() : 0,
                deviceInfoMap != null ? deviceInfoMap.size() : 0);
        if (CollectionUtils.isEmpty(deviceKpiDataList)) {
            log.warn("设备日数据为空，无需处理");
            return;
        }
        if (CollectionUtils.isEmpty(deviceInfoMap)) {
            log.warn("设备信息映射为空，无法处理数据");
            return;
        }

        List<GtiDailyPo> gtiDailyPoList = new ArrayList<>();
        for (HwCloudDeviceKpiDayResponse.HwCloudDeviceKpiDayData data : deviceKpiDataList) {
            try {
                GtiPo gtiPo = deviceInfoMap.get(data.getSn());
                if (gtiPo == null) {
                    log.warn("设备信息映射中未找到序列号为 {} 的设备信息，跳过数据处理", data.getSn());
                    continue;
                }
                GtiDailyPo gtiDailyPo = new GtiDailyPo();
                gtiDailyPo.setDate(new Date(data.getCollectTime()));
                gtiDailyPo.setGtiId(gtiPo.getId());
                gtiDailyPo.setDno(data.getSn());
                gtiDailyPo.setSiteId(gtiPo.getSiteId());
                Map<String, Object> dataItemMap = data.getDataItemMap();
                if (dataItemMap != null) {
                    Object productPower = dataItemMap.get("product_power");// 当日发电量 (product_power)
                    if (productPower != null) {
                        gtiDailyPo.setTodayKwh(new BigDecimal(productPower.toString()));
                    }
                }
                gtiDailyPoList.add(gtiDailyPo);
            } catch (Exception e) {
                log.error("处理设备 {} 日数据时发生异常: {}", data.getSn(), e.getMessage(), e);
            }
        }
        if (!gtiDailyPoList.isEmpty()) {
            gtiDailyRwDs.batchUpsetGtiDaily(gtiDailyPoList);
            log.info("使用设备信息映射处理设备日数据完成，成功处理 {} 条数据", gtiDailyPoList.size());
        } else {
            log.warn("没有有效的GTI日数据需要插入");
        }
    }
}
