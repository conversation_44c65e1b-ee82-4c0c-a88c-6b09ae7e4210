package com.cdz360.iot.pv.biz.south;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.pv.dto.EquipCfgReplyReq;
import com.cdz360.iot.model.pv.dto.PvRtReq;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.PvMode;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.cdz360.iot.pv.biz.RedisPvRtDataService;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvSouthBizService {

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;
//    @Autowired
//    private BizDataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @Autowired
    private RedisPvRtDataService redisPvRtDataService;

//    // 获取文件上传的STS信息
//    public Mono<CommonResponse<OssStsDto>> getOssArchiveSts() {
//        return Mono.just("")
//                .flatMap(e -> dataCoreFeignClient.getArchiveSts())
//                .doOnNext(FeignResponseValidate::check)
//                .map(e -> {
//                    CommonResponse res = new CommonResponse(e.getData());
//                    return res;
//                });
//    }

    /**
     * 逆变器数据上传
     *
     * @return
     */
    public Mono<BaseGwResponse> pvRtData(GwObjReqMsg<PvRtReq> gwReq) {
        PvRtReq req = gwReq.getData();
        log.debug(">> json = {}", JsonUtils.toJsonString(req));

        // 获取网关与场站关系信息
        GwInfoDto gwInfo = gwSiteRefRoDs.getGwInfoByGwno(gwReq.getGwno(), true);
        IotAssert.isNotNull(gwInfo, "网关信息无效");

        if (StringUtils.isBlank(gwInfo.getSiteId())) {
            log.error("微网控制器需要手动绑定到场站: gwno = {}", gwReq.getGwno());
            throw new DcArgumentException("微网控制器没有绑定到场站");
        }

        if (CollectionUtils.isEmpty(req.getPvData())) {
            throw new DcArgumentException("逆变器运行数据无效");
        }

        return Flux.fromIterable(req.getPvData())
            .doOnNext(pvReq -> {
                if (StringUtils.isNotBlank(pvReq.getDno())) {
                    GtiPo gtiOrign = gtiRoDs.getByDno(pvReq.getDno());

                    if (null != gtiOrign) {
                        GtiPo gtiUpdate = new GtiPo();
                        gtiUpdate.setDno(gtiOrign.getDno())
                            .setSerialNo(pvReq.getSerialNo())
                            .setDeviceModel(pvReq.getDeviceModel())
                            .setPartNo(pvReq.getPartNo())
                            .setGroupNum(pvReq.getGroupNum())
                            .setMpptNum(pvReq.getMpptNum());

                        if (pvReq.getRatedPower() != null) {
                            gtiUpdate.setPower(pvReq.getRatedPower().longValue());
                        }
                        if (EquipStatus.NORMAL != gtiOrign.getStatus()) {
                            gtiUpdate.setStatus(EquipStatus.NORMAL);
                        }
                        if (PvMode.NORMAL.equals(pvReq.getRtMode()) &&
                            CollectionUtils.isEmpty(pvReq.getErrorCodeList())) {
                            gtiUpdate.setAlertStatus(EquipAlertStatus.OK);
                        } else {
                            gtiUpdate.setAlertStatus(EquipAlertStatus.ABNORMAL);
                        }
                        gtiRwDs.updateGtiByDno(gtiUpdate);

                        // 推送数据到device
                        PvGtiVo gtiVo = new PvGtiVo();
                        gtiVo.setSiteId(gwInfo.getSiteId())
                            .setSiteCommId(gwInfo.getSiteCommId())
                            .setSiteName(gwInfo.getSiteName())
                            .setRecId(gtiOrign.getId().toString())
                            .setSid(gtiOrign.getSid())
                            .setGwno(gwInfo.getGwno())
                            .setGwName(gwInfo.getName())
                            .setName(gtiOrign.getName())
                            .setRtStatus(pvReq.getRtMode().getCode())
                            .setErrorCodeList(pvReq.getErrorCodeList());
                        this.dcEventPublish.publishPvGtiInfo(IotEvent.STATE_CHANGE, gtiVo);

                        // 数据临时存入缓存: 获取实时数据可用
                        this.redisPvRtDataService.pushRtData(pvReq.getDno(), pvReq);
                    } else {
                        log.error(
                            "该逆变器逆变器编号有误: gwno = {}, dno = {}, serialNo = {}, sid = {}",
                            gwInfo.getGwno(), pvReq.getDno(), pvReq.getSerialNo(),
                            pvReq.getDeviceId());
                    }
                } else {
                    log.warn("该逆变器逆变器编号没有下发: gwno = {}, serialNo = {}, sid = {}",
                        gwInfo.getGwno(), pvReq.getSerialNo(), pvReq.getDeviceId());
                }
            })
            .count()
            .map(cnt -> new BaseGwResponse());
    }

    public Mono<BaseGwResponse> pvCfgReply(EquipCfgReplyReq req) {

        // 更新配置状态
        if (CollectionUtils.isEmpty(req.getReplyList())) {
            return Mono.just(new BaseGwResponse());
        }

        return Mono.just(req)
            .map(EquipCfgReplyReq::getReplyList)
            .flatMapMany(Flux::fromIterable)
            .map(reply -> {
                String dno = reply.getDno();
                if (StringUtils.isNotBlank(dno)) {
                    GtiPo gti = gtiRoDs.getByDno(dno);
                    if (null != gti) {
                        GtiPo update = new GtiPo();
                        update.setDno(dno);
                        if (EquipCfgStatus.ARRIVE_GTI.equals(reply.getStatus())) {
                            update.setCfgSuccessId(gti.getCfgId())
                                .setCfgSuccessTime(new Date());
                        }

                        update.setCfgStatus(reply.getStatus());
                        return gtiRwDs.updateGtiByDno(update) ? 1 : 0;
                    }
                }
                return 0;
            })
            .count()
            .switchIfEmpty(Mono.just(0L))
            .map(r -> new BaseGwResponse());
    }

}
