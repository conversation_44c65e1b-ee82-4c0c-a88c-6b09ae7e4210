package com.cdz360.iot.pv;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.model.base.BaseRpcResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.support.WebExchangeBindException;

@ControllerAdvice(annotations = RestController.class)
public class IotPvExceptionHandler {
    private final Logger logger = LoggerFactory.getLogger(IotPvExceptionHandler.class);

    @Autowired
    private IotCacheService iotCacheService;

    /**
     * 处理 token 异常, 返回 http 401 并带一个 WWW-Authenticate 的头
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({DcTokenException.class})
    public ResponseEntity handleTokenException(DcTokenException ex,
                                               ServerHttpRequest request) {
        String gwno;

        if (StringUtils.isNotBlank(ex.getGwno())) {
            gwno = ex.getGwno();
        } else {
            gwno = request.getQueryParams().getFirst("n");
        }

        if (StringUtils.isBlank(gwno)) {
            logger.error("token异常时网关编号为空，需要接入调查", ex);
        }

        logger.info("handleTokenException, gwno = {}", gwno);
        logger.info(ex.getMessage());

        StringBuilder buf = new StringBuilder();
        buf.append("Basic realm=\"").append(this.iotCacheService.getOrUpdateRealm(gwno))
                .append("\"");

        ResponseEntity res = ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .header("WWW-Authenticate", buf.toString()).build();
        return res;

    }

    @ExceptionHandler({DcServiceException.class, DcArgumentException.class})
    public ResponseEntity<BaseRpcResponse> handleServiceException(DcException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseRpcResponse result = new BaseRpcResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        ResponseEntity<BaseRpcResponse> res = ResponseEntity.ok(result);
        return res;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<BaseRpcResponse> handleAssertException(IllegalArgumentException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseRpcResponse result = new BaseRpcResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        ResponseEntity<BaseRpcResponse> res = ResponseEntity.ok(result);
        return res;
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResponseEntity<BaseRpcResponse> handleAssertException(MethodArgumentNotValidException ex) {
        logger.warn(ex.getBindingResult().getFieldError().getDefaultMessage(), ex);
        BaseRpcResponse result = new BaseRpcResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getBindingResult().getFieldError().getDefaultMessage());
        ResponseEntity<BaseRpcResponse> res = ResponseEntity.ok(result);
        return res;
    }

    /**
     * 处理@Validation相关的异常
     * @param ex
     * @return
     */
    @ExceptionHandler({WebExchangeBindException.class})
    public ResponseEntity<BaseRpcResponse> handleWebExchangeBindException(WebExchangeBindException ex) {
        logger.warn(ex.getBindingResult().getFieldError().getDefaultMessage(), ex);
        BaseRpcResponse result = new BaseRpcResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getBindingResult().getFieldError().getDefaultMessage());
        ResponseEntity<BaseRpcResponse> res = ResponseEntity.ok(result);
        return res;
    }


    @ExceptionHandler
    public ResponseEntity<BaseRpcResponse> handle(Exception ex) {
        logger.error(ex.getMessage(), ex);
        BaseRpcResponse result = new BaseRpcResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        ResponseEntity<BaseRpcResponse> res = ResponseEntity.ok(result);
        return res;
    }


}
