package com.cdz360.iot.pv.rest.external;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.pv.dto.EquipCfgReplyReq;
import com.cdz360.iot.model.pv.dto.PvRtReq;
import com.cdz360.iot.model.srs.dto.SrsRtDataDto;
import com.cdz360.iot.pv.biz.south.PvSouthBizService;
import com.cdz360.iot.pv.biz.south.SrsSouthBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "iot网关接入API (v3)", description = "南向-光伏网关接口")
@RequestMapping("/iot/pv")
public class IotPvRestV3 extends IotRestBase {

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private PvSouthBizService pvService;

    @Autowired
    private SrsSouthBizService srsService;

    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }

//    @Operation(summary = "获取文件上传的STS信息")
//    @PostMapping(value = "/getOssSts", params = {"v=3"})
//    public Mono<CommonResponse<OssStsDto>> getOssSts(
//            @RequestHeader(value = "Authorization", required = false) String authHd,
//            @RequestParam(value = "n") String gwno) {
//        super.checkToken(authHd, gwno);
//        return pvService.getOssArchiveSts();
//    }

    @Operation(summary = "上传逆变器运行态数据")
    @PostMapping(value = "/pvRtData", params = {"v=3"})
    public Mono<BaseGwResponse> pvRtData(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<PvRtReq> gwReq
    ) {

        log.debug("逆变器上传运行数据。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        if (StringUtils.isBlank(gwReq.getGwno())) {
            gwReq.setGwno(gwno);
        }
        return this.pvService.pvRtData(gwReq)
                .doOnNext(res1 -> {
                    res1.setSeq(gwReq.getSeq());
                    LogHelper.logLatency(log, IotPvRestV3.class.getSimpleName(),
                            "pvRtData", "逆变器上传运行数据", startTime);
                });
    }

    @Operation(summary = "上传辐射仪运行态数据")
    @PostMapping(value = "/srsRtData", params = {"v=3"})
    public Mono<BaseGwResponse> srsRtData(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<SrsRtDataDto> gwReq
    ) {

        log.debug("辐射仪上传运行数据。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        if (StringUtils.isBlank(gwReq.getGwno())) {
            gwReq.setGwno(gwno);
        }
        return this.srsService.srsRtData(gwReq)
                .doOnNext(res1 -> {
                    res1.setSeq(gwReq.getSeq());
                    LogHelper.logLatency(log, IotPvRestV3.class.getSimpleName(),
                            "srsRtData", "辐射仪上传运行数据", startTime);
                });
    }

    @Operation(summary = "配置下发结果上报")
    @PostMapping(value = "/cfg/result")
    public Mono<BaseGwResponse> pvCfgReply(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<EquipCfgReplyReq> gwReq
    ) throws IOException {
        log.debug("配置下发结果上报。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        if (StringUtils.isBlank(gwReq.getGwno())) {
            gwReq.setGwno(gwno);
        }
        return this.pvService.pvCfgReply(gwReq.getData())
                .doOnNext(res1 -> {
                    res1.setSeq(gwReq.getSeq());
                    LogHelper.logLatency(log, IotPvRestV3.class.getSimpleName(),
                            "pvRtData", "配置下发结果上报", startTime);
                });
    }

}
