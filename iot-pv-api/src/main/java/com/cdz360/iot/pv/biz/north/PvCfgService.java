//package com.cdz360.iot.pv.biz.north;
//
//import com.cdz360.base.model.base.constants.DcConstants;
//import com.cdz360.base.model.base.dto.BaseResponse;
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.base.utils.RestUtils;
//import com.cdz360.iot.common.utils.IotAssert;
//import com.cdz360.iot.ds.ro.GtiCfgRoDs;
//import com.cdz360.iot.ds.rw.GtiCfgRwDs;
//import com.cdz360.iot.model.pv.param.ListGtiCfgParam;
//import com.cdz360.iot.model.pv.po.GtiCfgPo;
//import com.cdz360.iot.model.pv.vo.GtiCfgVo;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Mono;
//
//@Slf4j
//@Service
//public class PvCfgService {
//
//    @Autowired
//    private GtiCfgRoDs cfgRoDs;
//    @Autowired
//    private GtiCfgRwDs cfgRwDs;
//
//    public Mono<ListResponse<GtiCfgVo>> list(ListGtiCfgParam param) {
//        return Mono.just(param)
//                .map(e -> {
//                    ListResponse<GtiCfgVo> res = RestUtils.buildListResponse(cfgRoDs.findList(param));
//                    if (Boolean.TRUE.equals(param.getTotal())) {
//                        res.setTotal(cfgRoDs.findListCount(param));
//                    }
//                    return res;
//                });
//    }
//
//    public Mono<ObjectResponse<GtiCfgPo>> getById(Long cfgId) {
//        return Mono.just(cfgId)
//                .map(e -> RestUtils.buildObjectResponse(cfgRoDs.getById(e)));
//    }
//
//    public Mono<BaseResponse> add(GtiCfgPo param) {
//        IotAssert.isTrue(cfgRoDs.countByName(param.getName(), param.getSiteId(), null) == 0, "模板名称重复");
//        boolean res = cfgRwDs.insertGtiCfg(param);
//        return Mono.just(res ? RestUtils.success() : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败"));
//    }
//
//    public Mono<BaseResponse> edit(GtiCfgPo param) {
//        IotAssert.isTrue(cfgRoDs.countByName(param.getName(), param.getSiteId(), param.getCfgId()) == 0, "模板名称重复");
//        boolean res = cfgRwDs.updateGtiCfg(param);
//        return Mono.just(res ? RestUtils.success() : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败"));
//    }
//
//    public Mono<BaseResponse> del(Long cfgId) {
//        cfgRoDs.delPrecheck(cfgId);
//
//        boolean res = cfgRwDs.delGtiCfg(cfgId);
//        return Mono.just(res ? RestUtils.success() : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "操作失败"));
//    }
//
//}
