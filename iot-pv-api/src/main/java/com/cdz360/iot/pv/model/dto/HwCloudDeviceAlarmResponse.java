package com.cdz360.iot.pv.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 华为云设备活动告警接口响应
 */
@Data
public class HwCloudDeviceAlarmResponse {

    /**
     * 请求成功失败标识
     * true: 请求成功
     * false: 请求失败
     */
    private boolean success;

    /**
     * 错误码
     * 0 表示正常，其他错误码参见错误码列表
     */
    private Integer failCode;

    /**
     * 包含如下信息
     */
    private HwCloudDeviceAlarmParams params;

    /**
     * 可选消息
     */
    private String message;

    /**
     * 返回数据，data里面是告警信息列表
     */
    private List<HwCloudDeviceAlarmData> data;

    /**
     * 参数信息
     */
    @Data
    public static class HwCloudDeviceAlarmParams {
        /**
         * 请求参数中的电站编号列表
         */
        private String stationCodes;
        
        /**
         * 请求参数中的设备sn列表
         */
        private String sns;
        
        /**
         * 请求参数中的查询活动告警的开始时间戳（毫秒）
         */
        private Long beginTime;
        
        /**
         * 请求参数中的查询活动告警的结束时间戳（毫秒）
         */
        private Long endTime;
        
        /**
         * 请求参数中的语言
         */
        private String language;
        
        /**
         * 请求参数中的告警级别
         */
        private String levels;
        
        /**
         * 请求参数中的设备类型
         */
        private String devTypes;

        /**
         * 系统当前时间（毫秒）
         */
        private Long currentTime;
    }

    /**
     * 设备告警数据
     */
    @Data
    public static class HwCloudDeviceAlarmData {
        /**
         * 电站编号，电站唯一标识
         */
        private String stationCode;
        
        /**
         * 告警名称
         */
        private String alarmName;
        
        /**
         * 设备名称
         */
        private String devName;
        
        /**
         * 修复建议
         */
        private String repairSuggestion;
        
        /**
         * 设备sn
         */
        private String esnCode;
        
        /**
         * 设备类型ID
         * 支持以下设备类型:
         * 1: 组串式逆变器
         * 2: 数采
         * 8: 箱变
         * 10: 环境监测仪
         * 13: 通管机
         * 16: 通用设备
         * 17: 关口电表
         * 22: PID
         * 37: 品联数采
         * 38: 户用逆变器
         * 39: 储能
         * 40: 并离网控制器
         * 45: PLC
         * 46: 优化器
         * 47: 户用电表
         * 62: Dongle
         * 63: 分布式数采
         * 70: 安全关断盒
         * 60001: 市电
         * 60003: 发电机
         * 60043: 太阳能模块组
         * 60044: 太阳能模块
         * 60092: 功率控制器
         * 60014: 锂电池
         * 60010: 交流输出配电
         */
        private Integer devTypeId;
        
        /**
         * 原因ID
         */
        private Integer causeId;
        
        /**
         * 告警原因
         */
        private String alarmCause;
        
        /**
         * 告警类型
         * 支持以下告警类型:
         * 0: 其它告警
         * 1: 空位信号
         * 2: 异常告警
         * 3: 保护事件
         * 4: 通知状态
         * 5: 告警信息
         */
        private Integer alarmType;
        
        /**
         * 告警产生时间戳（毫秒）
         */
        private Long raiseTime;
        
        /**
         * 告警ID
         */
        private Integer alarmId;
        
        /**
         * 电站名称
         */
        private String stationName;
        
        /**
         * 告警级别
         * 支持以下告警级别:
         * 1: 严重
         * 2: 重要
         * 3: 次要
         * 4: 提示
         */
        private Integer lev;
        
        /**
         * 告警状态
         * 支持以下告警状态:
         * 1: 未处理（活动）
         */
        private Integer status;
    }
}