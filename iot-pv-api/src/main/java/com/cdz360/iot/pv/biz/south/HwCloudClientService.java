package com.cdz360.iot.pv.biz.south;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.pv.model.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * 华为云IoT平台客户端服务
 * 提供华为云IoT平台的API调用功能
 */
@Slf4j
@Service
public class HwCloudClientService {

    @Value("${hw.client.baseUrl:}")
    private String baseUrl;

    @Value("${hw.client.userName:}")
    private String userName;

    @Value("${hw.client.systemCode:}")
    private String systemCode;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * Redis中token的key
     */
    private static final String HUAWEI_CLOUD_TOKEN_KEY = "huawei:cloud:xsrf:token";

    /**
     * token有效期（分钟）
     */
    private static final int TOKEN_EXPIRE_MINUTES = 25;

    /**
     * 华为云IoT平台登录接口
     * 获取访问令牌XSRF-TOKEN，有效期30分钟
     *
     * @return token字符串
     */
    public Mono<String> login() {
        return login(this.userName, this.systemCode);
    }

    /**
     * 华为云IoT平台登录接口
     * 获取访问令牌XSRF-TOKEN，有效期30分钟
     *
     * @param userNameParam   API账户名
     * @param systemCodeParam 密码
     * @return token字符串
     */
    public Mono<String> login(String userNameParam, String systemCodeParam) {
        try {
            log.info("开始调用华为云IoT平台登录接口，userName: {}", userNameParam);

            // 构建请求参数
            String requestBody = JsonUtils.toJsonString(new Object() {
                public final String userName = userNameParam;
                public final String systemCode = systemCodeParam;
            });

            // 发送POST请求并获取完整响应（包括响应头）
            return WebClient.create(baseUrl)
                    .post()
                    .uri("/thirdData/login")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .exchange()
                    .flatMap(clientResponse -> {
                        // 获取响应头中的token
                        String xsrfToken = clientResponse.headers().header("XSRF-TOKEN")
                                .stream().findFirst().orElse(null);
                        // 获取响应体
                        return clientResponse.bodyToMono(HwCloudLoginResponse.class)
                                .map(response -> {
                                    log.info("华为云IoT平台登录成功，XSRF-TOKEN: {}, 响应: {}",
                                            xsrfToken, JsonUtils.toJsonString(response));
                                    cacheTokenToRedis(xsrfToken);
                                    return xsrfToken;
                                });
                    })
                    .onErrorResume(throwable -> {
                        log.error("华为云IoT平台登录失败", throwable);
                        return Mono.error(new RuntimeException("华为云IoT平台登录失败: " + throwable.getMessage()));
                    });
        } catch (Exception e) {
            log.error("华为云IoT平台登录异常", e);
            return Mono.error(new RuntimeException("华为云IoT平台登录异常: " + e.getMessage()));
        }
    }

    /**
     * 获取有效的token，如果当前token无效则重新登录
     *
     * @return token字符串
     */
    public Mono<String> getValidToken() {
        String cachedToken = getTokenFromRedis();
        if (cachedToken != null) {
            log.debug("使用Redis缓存的有效token");
            return Mono.just(cachedToken);
        }

        log.info("当前token无效，重新登录获取token");
        return login();
    }

    /**
     * 将token缓存到Redis
     *
     * @param token XSRF-TOKEN
     */
    private void cacheTokenToRedis(String token) {
        try {
            redisTemplate.opsForValue().set(HUAWEI_CLOUD_TOKEN_KEY, token, Duration.ofMinutes(TOKEN_EXPIRE_MINUTES));
            log.info("token已缓存到Redis，过期时间: {}分钟", TOKEN_EXPIRE_MINUTES);
        } catch (Exception e) {
            log.error("缓存token到Redis失败", e);
        }
    }

    /**
     * 从Redis获取token
     *
     * @return token字符串，如果不存在或异常则返回null
     */
    private String getTokenFromRedis() {
        try {
            return redisTemplate.opsForValue().get(HUAWEI_CLOUD_TOKEN_KEY);
        } catch (Exception e) {
            log.error("从Redis获取token失败", e);
        }
        return null;
    }

    /**
     * 获取电站实时数据接口
     * 获取电站实时数据，通过电站编号查询，一次最多查询100个电站
     *
     * @param request 请求参数，包含stationCodes字段
     * @return 电站实时数据响应
     */
    public Mono<HwCloudStationRealKpiResponse> getStationRealKpi(HwCloudStationRealKpiRequest request) {
        return getValidToken()
                .flatMap(token -> {
                    try {
                        log.info("开始调用华为云电站实时数据接口，stationCodes: {}", request.getStationCodes());

                        return WebClient.create(baseUrl)
                                .post()
                                .uri("/thirdData/getStationRealKpi")
                                .header("XSRF-TOKEN", token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(JsonUtils.toJsonString(request))
                                .retrieve()
                                .bodyToMono(HwCloudStationRealKpiResponse.class)
                                .map(response -> {
                                    if (response.isSuccess()) {
                                        log.info("华为云电站实时数据接口调用成功，返回{}个电站数据",
                                                response.getData() != null ? response.getData().size() : 0);
                                    } else {
                                        log.warn("华为云电站实时数据接口返回失败，failCode: {}, message: {}",
                                                response.getFailCode(), response.getMessage());
                                    }
                                    return response;
                                });
                    } catch (Exception e) {
                        log.error("华为云电站实时数据接口调用异常", e);
                        return Mono.error(new RuntimeException("华为云电站实时数据接口调用异常: " + e.getMessage()));
                    }
                });
    }

    /**
     * 获取设备实时数据接口
     * 获取设备实时数据，通过设备编号或SN查询，一次最多查询100个设备
     *
     * @param request 请求参数，包含devIds或sns字段，以及必填的devTypeId字段
     * @return 设备实时数据响应
     */
    public Mono<HwCloudDeviceRealKpiResponse> getDevRealKpi(HwCloudDeviceRealKpiRequest request) {
        return getValidToken()
                .flatMap(token -> {
                    try {
                        log.info("开始调用华为云设备实时数据接口，devIds: {}, sns: {}, devTypeId: {}",
                                request.getDevIds(), request.getSns(), request.getDevTypeId());

                        return WebClient.create(baseUrl)
                                .post()
                                .uri("/thirdData/getDevRealKpi")
                                .header("XSRF-TOKEN", token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(JsonUtils.toJsonString(request))
                                .retrieve()
                                .bodyToMono(HwCloudDeviceRealKpiResponse.class)
                                .map(response -> {
                                    if (response.isSuccess()) {
                                        log.info("华为云设备实时数据接口调用成功，返回{}个设备数据",
                                                response.getData() != null ? response.getData().size() : 0);
                                    } else {
                                        log.warn("华为云设备实时数据接口返回失败，failCode: {}, message: {}",
                                                response.getFailCode(), response.getMessage());
                                    }
                                    return response;
                                });
                    } catch (Exception e) {
                        log.error("华为云设备实时数据接口调用异常", e);
                        return Mono.error(new RuntimeException("华为云设备实时数据接口调用异常: " + e.getMessage()));
                    }
                });
    }

    /**
     * 获取设备历史数据接口
     * 获取设备指定时间段内的5分钟粒度历史数据，通过设备编号或SN查询，一次最多查询10个设备，3天数据
     *
     * @param request 请求参数，包含devIds或sns字段，以及必填的devTypeId、startTime、endTime字段
     * @return 设备历史数据响应
     */
    public Mono<HwCloudDeviceHistoryKpiResponse> getDevHistoryKpi(HwCloudDeviceHistoryKpiRequest request) {
        return getValidToken()
                .flatMap(token -> {
                    try {
                        log.info("开始调用华为云设备历史数据接口，devIds: {}, sns: {}, devTypeId: {}, startTime: {}, endTime: {}",
                                request.getDevIds(), request.getSns(), request.getDevTypeId(),
                                request.getStartTime(), request.getEndTime());

                        return WebClient.create(baseUrl)
                                .post()
                                .uri("/thirdData/getDevHistoryKpi")
                                .header("XSRF-TOKEN", token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(JsonUtils.toJsonString(request))
                                .retrieve()
                                .bodyToMono(HwCloudDeviceHistoryKpiResponse.class)
                                .map(response -> {
                                    if (response.isSuccess()) {
                                        log.info("华为云设备历史数据接口调用成功，返回{}个设备数据",
                                                response.getData() != null ? response.getData().size() : 0);
                                    } else {
                                        log.warn("华为云设备历史数据接口返回失败，failCode: {}, message: {}",
                                                response.getFailCode(), response.getMessage());
                                    }
                                    return response;
                                });
                    } catch (Exception e) {
                        log.error("华为云设备历史数据接口调用异常", e);
                        return Mono.error(new RuntimeException("华为云设备历史数据接口调用异常: " + e.getMessage()));
                    }
                });
    }

    /**
     * 获取设备活动告警接口
     * 获取设备的活动告警信息，通过电站编号或设备SN查询，可筛选告警级别和时间范围
     *
     * @param request 请求参数，包含stationCodes或sns字段，以及必填的beginTime、endTime、language字段，可选的levels、devTypes字段
     * @return 设备活动告警响应
     */
    public Mono<HwCloudDeviceAlarmResponse> getDevAlarm(HwCloudDeviceAlarmRequest request) {
        return getValidToken()
                .flatMap(token -> {
                    try {
                        log.info("开始调用华为云设备活动告警接口，电站编号: {}, 设备SN: {}, 告警级别: {}, 设备类型: {}, 开始时间: {}, 结束时间: {}, 语言: {}",
                                request.getStationCodes(), request.getSns(), request.getLevels(),
                                request.getDevTypes(), request.getBeginTime(), request.getEndTime(),
                                request.getLanguage());

                        return WebClient.create(baseUrl)
                                .post()
                                .uri("/thirdData/getAlarmList")
                                .header("XSRF-TOKEN", token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(JsonUtils.toJsonString(request))
                                .retrieve()
                                .bodyToMono(HwCloudDeviceAlarmResponse.class)
                                .map(response -> {
                                    if (response.isSuccess()) {
                                        log.info("华为云设备活动告警接口调用成功，返回{}个告警数据",
                                                response.getData() != null ? response.getData().size() : 0);
                                    } else {
                                        log.warn("华为云设备活动告警接口返回失败，failCode: {}, message: {}",
                                                response.getFailCode(), response.getMessage());
                                    }
                                    return response;
                                });
                    } catch (Exception e) {
                        log.error("华为云设备活动告警接口调用异常", e);
                        return Mono.error(new RuntimeException("华为云设备活动告警接口调用异常: " + e.getMessage()));
                    }
                });
    }

    /**
     * 获取设备日数据接口
     * 获取设备指定日期的日数据，通过设备编号或SN查询，一次最多查询100个设备
     *
     * @param request 请求参数，包含devIds或sns字段，以及必填的devTypeId、collectTime字段
     * @return 设备日数据响应
     */
    public Mono<HwCloudDeviceKpiDayResponse> getDevKpiDay(HwCloudDeviceKpiDayRequest request) {
        return getValidToken()
                .flatMap(token -> {
                    try {
                        log.info("开始调用华为云设备日数据接口，devIds: {}, sns: {}, devTypeId: {}, collectTime: {}",
                                request.getDevIds(), request.getSns(), request.getDevTypeId(),
                                request.getCollectTime());

                        return WebClient.create(baseUrl)
                                .post()
                                .uri("/thirdData/getDevKpiDay")
                                .header("XSRF-TOKEN", token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(JsonUtils.toJsonString(request))
                                .retrieve()
                                .bodyToMono(HwCloudDeviceKpiDayResponse.class)
                                .map(response -> {
                                    if (response.isSuccess()) {
                                        log.info("华为云设备日数据接口调用成功，返回{}个设备数据",
                                                response.getData() != null ? response.getData().size() : 0);
                                    } else {
                                        log.warn("华为云设备日数据接口返回失败，failCode: {}, message: {}",
                                                response.getFailCode(), response.getMessage());
                                    }
                                    return response;
                                });
                    } catch (Exception e) {
                        log.error("华为云设备日数据接口调用异常", e);
                        return Mono.error(new RuntimeException("华为云设备日数据接口调用异常: " + e.getMessage()));
                    }
                });
    }
}
