package com.cdz360.iot.pv.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 华为云设备实时数据接口响应
 */
@Data
public class HwCloudDeviceRealKpiResponse {

    /**
     * 请求成功失败标识
     * true: 请求成功
     * false: 请求失败
     */
    private boolean success;

    /**
     * 错误码
     * 0 表示正常，其他错误码参见错误码列表
     */
    private Integer failCode;

    /**
     * 包含如下信息
     */
    private HwCloudDeviceRealKpiParams params;

    /**
     * 可选消息
     */
    private String message;

    /**
     * 返回数据，data里面是各个设备的实时数据对象信息列表
     */
    private List<HwCloudDeviceRealKpiData> data;

    /**
     * 参数信息
     */
    @Data
    public static class HwCloudDeviceRealKpiParams {
        /**
         * 请求参数中的设备编号列表
         */
        private String devIds;
        
        /**
         * 请求参数中的设备sn列表
         */
        private String sns;
        
        /**
         * 请求参数中的设备类型ID
         */
        private Integer devTypeId;

        /**
         * 系统当前时间（毫秒）
         */
        private Long currentTime;
    }

    /**
     * 设备实时数据
     */
    @Data
    public static class HwCloudDeviceRealKpiData {
        /**
         * 设备编号
         */
        private Long devId;
        
        /**
         * 设备SN号
         */
        private String sn;

        /**
         * 每个数据项的内容，用key-value形式返回，数据项列表参见设备实时数据列表
         */
        private Map<String, Object> dataItemMap;
    }
}