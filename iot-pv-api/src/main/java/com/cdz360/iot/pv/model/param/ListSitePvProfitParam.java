package com.cdz360.iot.pv.model.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "查询场站收益结算规则")
public class ListSitePvProfitParam {

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
