//package com.cdz360.iot.pv.biz;
//
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.exception.DcArgumentException;
//import com.cdz360.base.utils.StringUtils;
//import com.cdz360.iot.common.utils.CollectionUtils;
//import com.cdz360.iot.common.utils.FeignResponseValidate;
//import com.cdz360.iot.common.utils.IotAssert;
//import com.cdz360.iot.ds.ro.GtiDailyRoDs;
//import com.cdz360.iot.ds.ro.GtiRoDs;
//import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
//import com.cdz360.iot.model.pv.param.DayKwhParam;
//import com.cdz360.iot.model.pv.param.ListCtrlParam;
//import com.cdz360.iot.model.pv.param.ListGtiParam;
//import com.cdz360.iot.model.pv.vo.*;
//import com.cdz360.iot.model.site.vo.GwInfoVo;
//import com.cdz360.iot.pv.feign.BizDataCoreFeignClient;
//import com.cdz360.iot.pv.model.dto.SitePvProfitInfo;
//import com.cdz360.iot.pv.model.param.ListSitePvProfitParam;
//import com.cdz360.iot.pv.model.po.PriceItemPo;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicReference;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class PvRtDataService {
//    @Autowired
//    private GtiRoDs gtiRoDs;
//
//    @Autowired
//    private GtiDailyRoDs gtiDailyRoDs;
//
//    @Autowired
//    private GwSiteRefRoDs gwSiteRefRoDs;
//
//    @Autowired
//    private RedisPvRtDataService redisPvRtDataService;
//
//    @Autowired
//    private BizDataCoreFeignClient bizDataCoreFeignClient;
//
//    public Mono<TotalPvRtDataBi> rtDataTotal(DayKwhParam param) {
//        return Mono.zip(this.todayRtData(param).collectList().map(list -> {
//                            BigDecimal kwh = list.stream().map(SiteTotalPvRtDataBi::getToday)
//                                    .map(SitePvRtDataBi::getTotalKwh)
//                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                            BigDecimal profit = list.stream().map(SiteTotalPvRtDataBi::getToday)
//                                    .map(SitePvRtDataBi::getTotalProfit)
//                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                            return new SitePvRtDataBi().setTotalProfit(profit)
//                                    .setTotalKwh(kwh);
//                        }), // 当天
//                        Mono.just(param).doOnNext(p -> {
//                            if (p.getYear() == null || null == p.getMonth()) {
//                                LocalDate date = LocalDate.now();
//                                if (null == p.getYear()) p.setYear(date.getYear());
//                                if (null == p.getMonth()) p.setMonth(date.getMonthValue());
//                            }
//                        }).map(gtiDailyRoDs::rtDataOfMonth), // 当月
//                        Mono.just(gtiDailyRoDs.rtDataOfTotal(param))) // 累计
//                .map(tuple -> {
//                    SitePvRtDataBi today = tuple.getT1();
//                    SitePvRtDataBi month = tuple.getT2();
//                    SitePvRtDataBi total = tuple.getT3();
//
//                    // 加上今天数据(今天数据仅在Redis中)
//                    month.setTotalProfit(month.getTotalProfit().add(today.getTotalProfit()))
//                            .setTotalKwh(month.getTotalKwh().add(today.getTotalKwh()));
//                    total.setTotalProfit(total.getTotalProfit().add(today.getTotalProfit()))
//                            .setTotalKwh(total.getTotalKwh().add(today.getTotalKwh()));
//
//                    return new TotalPvRtDataBi()
//                            .setToday(today)
//                            .setCurMonth(month)
//                            .setTotal(total);
//                });
//    }
//
//    public Mono<List<DayPvDataBi>> siteRtData7Day(String siteId) {
//
//        if (StringUtils.isBlank(siteId)) {
//            throw new DcArgumentException("场站ID不能为空");
//        }
//
//        Date toDate = new Date();
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(toDate);
//        calendar.add(Calendar.DAY_OF_MONTH, -8);
//        Date fromDate = calendar.getTime();
//        return Mono.just(gtiDailyRoDs.siteRtDataOfDay(siteId, fromDate, toDate));
//
//    }
//
//    public Mono<List<SiteTotalPvRtDataBi>> siteRtDataTotal(DayKwhParam param) {
//        return Mono.zip(this.todayRtData(param).collectList(), // 当天
//                        Mono.just(param).doOnNext(p -> {
//                            if (p.getYear() == null || null == p.getMonth()) {
//                                LocalDate date = LocalDate.now();
//                                if (null == p.getYear()) p.setYear(date.getYear());
//                                if (null == p.getMonth()) p.setMonth(date.getMonthValue());
//                            }
//                        }).map(gtiDailyRoDs::siteRtDataOfMonth), // 当月
//                        Mono.just(gtiDailyRoDs.siteRtDataOfTotal(param))) // 累计
//                .flatMap(tuple -> {
//                    List<SiteTotalPvRtDataBi> result = tuple.getT1();
//                    Map<String, SitePvRtDataBi> monthMap = tuple.getT2().stream()
//                            .collect(Collectors.toMap(SitePvRtDataBi::getSiteId, o -> o));
//                    Map<String, SitePvRtDataBi> yearMap = tuple.getT3().stream()
//                            .collect(Collectors.toMap(SitePvRtDataBi::getSiteId, o -> o));
//
//                    return Flux.fromIterable(result)
//                            .map(i -> {
//                                SitePvRtDataBi month = monthMap.get(i.getSiteId());
//                                if (null == month) {
//                                    month = new SitePvRtDataBi();
//                                    month.setSiteId(i.getSiteId())
//                                            .setTotalKwh(BigDecimal.ZERO)
//                                            .setTotalProfit(BigDecimal.ZERO);
//                                }
//
//                                // 加上今天数据(今天数据仅在Redis中)
//                                month.setTotalProfit(month.getTotalProfit().add(
//                                                i.getToday().getTotalProfit()))
//                                        .setTotalKwh(month.getTotalKwh().add(
//                                                i.getToday().getTotalKwh()));
//                                i.setCurMonth(month);
//
//                                SitePvRtDataBi total = yearMap.get(i.getSiteId());
//                                if (null == total) {
//                                    total = new SitePvRtDataBi();
//                                    total.setSiteId(i.getSiteId())
//                                            .setTotalKwh(BigDecimal.ZERO)
//                                            .setTotalProfit(BigDecimal.ZERO);
//                                }
//                                total.setTotalProfit(total.getTotalProfit().add(
//                                                i.getToday().getTotalProfit()))
//                                        .setTotalKwh(total.getTotalKwh().add(
//                                                i.getToday().getTotalKwh()));
//                                i.setTotal(total);
//                                return i;
//                            })
//                            .collectList();
//                });
//    }
//
//    private Flux<SiteTotalPvRtDataBi> todayRtData(DayKwhParam param) {
//        // 获取微网控制器列表
//        ListCtrlParam ctrlParam = new ListCtrlParam()
//                .setCommIdChain(param.getCommIdChain())
//                .setSiteIdList(param.getSiteIdList());
//        ctrlParam.setSize(100);
//        List<GwInfoVo> ctrlList = this.gwSiteRefRoDs.findCtrlList(ctrlParam);
//        List<String> siteIdList = ctrlList.stream().map(GwInfoVo::getSiteId)
//                .distinct().collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(siteIdList)) {
//            return Flux.fromIterable(List.of());
//        }
//
//        final LocalDate date = LocalDate.now();
//        AtomicReference<Map<String, List<PriceItemPo>>> siteProfitMap = new AtomicReference<>();
//        ListSitePvProfitParam profitParam = new ListSitePvProfitParam();
//        return bizDataCoreFeignClient.getSiteProfitList(profitParam.setSiteIdList(siteIdList))
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .doOnNext(profitList -> siteProfitMap.set(profitList.stream()
//                        .collect(Collectors.toMap(SitePvProfitInfo::getSiteId, SitePvProfitInfo::getPriceItemPoList))))
//                .map(i -> ctrlList)
//                .flatMapMany(Flux::fromIterable)
//                .filter(ctrl -> CollectionUtils.isNotEmpty(siteProfitMap.get().get(ctrl.getSiteId())))
//                .flatMap(ctrl -> {
//                    ListGtiParam gtiParam = new ListGtiParam().setGwno(ctrl.getGwno());
//                    List<GtiVo> gtiList = gtiRoDs.findGtiList(gtiParam);
//
//                    List<PriceItemPo> priceItems = siteProfitMap.get().get(ctrl.getSiteId());
//                    return this.gwAllGtiDayProfit(gtiList, date, priceItems)
//                            .zipWith(this.gwAllGtiDayKwh(gtiList, date))
//                            .map(tuple -> {
//                                BigDecimal profit = tuple.getT1();
//                                BigDecimal kwh = tuple.getT2();
//
//                                SiteTotalPvRtDataBi result = new SiteTotalPvRtDataBi();
//                                return result.setSiteId(ctrl.getSiteId())
//                                        .setToday(new SitePvRtDataBi()
//                                                .setSiteId(ctrl.getSiteId())
//                                                .setTotalProfit(profit)
//                                                .setTotalKwh(kwh));
//                            });
//                })
//                .collect(Collectors.toMap(SiteTotalPvRtDataBi::getSiteId, o -> o, (o1, o2) -> {
//                    o1.getToday().setTotalKwh(o1.getToday().getTotalKwh().add(o2.getToday().getTotalKwh()));
//                    o1.getToday().setTotalProfit(o1.getToday().getTotalProfit().add(o2.getToday().getTotalProfit()));
//                    return o1;
//                }))
//                .map(Map::values)
//                .flatMapMany(Flux::fromIterable);
//    }
//
//    private Mono<BigDecimal> gwAllGtiDayProfit(
//            List<GtiVo> gtiList, LocalDate date, List<PriceItemPo> priceItems) {
//        return redisPvRtDataService.gtiDayProfit(
//                gtiList.stream().map(GtiVo::getDno).collect(Collectors.toList()), date, priceItems);
//    }
//
//    private Mono<BigDecimal> gwAllGtiDayKwh(List<GtiVo> gtiList, LocalDate date) {
//        return redisPvRtDataService.gtiDayKwh(
//                gtiList.stream().map(GtiVo::getDno).collect(Collectors.toList()), date);
//    }
//
//    public Flux<DaySitePvRtDataBi> siteDayOfMonthKwh(DayKwhParam param) {
//        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
//            return Flux.fromIterable(List.of());
//        }
//
//        if (null == param.getYear()) {
//            param.setYear(LocalDate.now().getYear());
//        }
//
//        return Mono.just(param)
//                .map(gtiDailyRoDs::siteDayOfMonthKwh)
//                .flatMap(result -> {
//                    LocalDate now = LocalDate.now();
//                    if (now.getMonthValue() == param.getMonth() &&
//                            now.getYear() == param.getYear()) {
//                        return this.todayRtData(new DayKwhParam().setSiteIdList(param.getSiteIdList()))
//                                .map(p -> {
//                                    Map<String, Long> sitePowerMap = new HashMap<>();
//                                    List<GtiTinyBi> tinyBiList = gtiDailyRoDs.getGtiTinyBi(param.getSiteIdList());
//                                    if (CollectionUtils.isNotEmpty(tinyBiList)) {
//                                        sitePowerMap = tinyBiList.stream()
//                                                .collect(Collectors
//                                                        .toMap(GtiTinyBi::getSiteId, GtiTinyBi::getTotalPower));
//                                    }
//
//                                    DaySitePvRtDataBi data = new DaySitePvRtDataBi();
//                                    data.setDate(new Date())
//                                            .setTotalPower(sitePowerMap.get(p.getSiteId()))
//                                            .setSiteId(p.getSiteId())
//                                            .setTotalKwh(p.getToday().getTotalKwh())
//                                            .setTotalProfit(p.getToday().getTotalProfit());
//                                    return data;
//                                })
//                                .collectList()
//                                .map(list -> {
//                                    result.addAll(list);
//                                    return result;
//                                });
//                    }
//
//                    return Mono.just(result);
//                })
//                .flatMapMany(Flux::fromIterable);
//
////        return Mono.zip(Mono.just(gtiDailyRoDs.siteDayOfMonthKwh(param)),
////                this.todayRtData(new DayKwhParam().setSiteIdList(param.getSiteIdList()))
////                        .map(p -> {
////                            Map<String, Long> sitePowerMap = new HashMap<>();
////                            List<GtiTinyBi> tinyBiList = gtiDailyRoDs.getGtiTinyBi(param.getSiteIdList());
////                            if (CollectionUtils.isNotEmpty(tinyBiList)) {
////                                sitePowerMap = tinyBiList.stream().collect(Collectors.toMap(GtiTinyBi::getSiteId, GtiTinyBi::getTotalPower));
////                            }
////
////                            DaySitePvRtDataBi result = new DaySitePvRtDataBi();
////                            result.setDate(new Date())
////                                    .setTotalPower(sitePowerMap.get(p.getSiteId()))
////                                    .setSiteId(p.getSiteId())
////                                    .setTotalKwh(p.getToday().getTotalKwh())
////                                    .setTotalProfit(p.getToday().getTotalProfit());
////                            return result;
////                        })
////                        .collectList())
////                .map(tuple -> {
////                    List<DaySitePvRtDataBi> result = tuple.getT1();
////                    result.addAll(tuple.getT2());
////                    return result;
////                })
////                .flatMapMany(Flux::fromIterable);
//    }
//
//    public Mono<List<DaySitePvRtDataBi>> siteRecentDaysKwh(DayKwhParam param) {
//        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
//            return Mono.just(List.of());
//        }
//        IotAssert.isNotNull(param.getRecentDays(), "recentDays参数错误");
//
//        return Mono.just(gtiDailyRoDs.siteDayOfMonthKwh(param));
//    }
//
//    public Mono<List<GtiStatusBi>> getGtiStatusBi(String commIdChain) {
//        return Mono.just(gtiRoDs.getGtiStatusBi(commIdChain));
//    }
//
//}
