//package com.cdz360.iot.pv.rest.internal;
//
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.base.utils.JsonUtils;
//import com.cdz360.base.utils.RestUtils;
//import com.cdz360.iot.model.pv.param.DayKwhParam;
//import com.cdz360.iot.model.pv.vo.*;
//import com.cdz360.iot.pv.biz.PvRtDataService;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Mono;
//
//@Slf4j
//@RestController
//@Tag(name = "光伏运行时数据相关接口", description = "光伏服务")
//@RequestMapping("/iot/biz/pv/rt")
//public class PvRtDataRest {
//
//    @Autowired
//    private PvRtDataService pvRtDataService;
//
//    @Operation(summary = "获取场站近七天发电数据", description = "从昨天开始近七天(仅返回有数据的天数)")
//    @PostMapping(value = "/siteRtData7Day")
//    public Mono<ListResponse<DayPvDataBi>> siteRtData7Day(
//            @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId) {
//        log.info("获取场站近七天发电数据: siteId = {}", siteId);
//        return this.pvRtDataService.siteRtData7Day(siteId)
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取场站下发电数据")
//    @PostMapping(value = "/siteRtDataTotal")
//    public Mono<ListResponse<SiteTotalPvRtDataBi>> siteRtDataTotal(@RequestBody DayKwhParam param) {
//        log.info("获取场站下发电数据: param = {}", JsonUtils.toJsonString(param));
//        return this.pvRtDataService.siteRtDataTotal(param)
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取当天/当月/累计数据")
//    @PostMapping(value = "/rtDataTotal")
//    public Mono<ObjectResponse<TotalPvRtDataBi>> rtDataTotal(@RequestBody DayKwhParam param) {
//        log.info("获取场站下发电数据: param = {}", JsonUtils.toJsonString(param));
//        return this.pvRtDataService.rtDataTotal(param)
//                .map(RestUtils::buildObjectResponse);
//    }
//
//    @Operation(summary = "获取场站下指定月份各天的发电量及收益",
//            description = "仅返回带数据的日期,注意空数据日期")
//    @PostMapping(value = "/siteDayOfMonthKwh")
//    public Mono<ListResponse<DaySitePvRtDataBi>> siteDayOfMonthKwh(
//            @RequestBody DayKwhParam param) {
//        log.info("获取场站下指定月份各天的发电量: param = {}", JsonUtils.toJsonString(param));
//        return this.pvRtDataService.siteDayOfMonthKwh(param)
//                .collectList()
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取场站下近n天的发电量及收益",
//            description = "仅返回带数据的日期,注意空数据日期")
//    @PostMapping(value = "/siteRecentDaysKwh")
//    public Mono<ListResponse<DaySitePvRtDataBi>> siteRecentDaysKwh(
//            @RequestBody DayKwhParam param) {
//        log.info("获取场站下近n天的发电量及收益: param = {}", JsonUtils.toJsonString(param));
//        return this.pvRtDataService.siteRecentDaysKwh(param)
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取逆变器状态统计数据")
//    @GetMapping(value = "/getGtiStatusBi")
//    public Mono<ListResponse<GtiStatusBi>> getGtiStatusBi(@RequestParam(value = "commIdChain", required = false) String commIdChain) {
//        log.info("获取逆变器状态统计数据: commIdChain = {}", commIdChain);
//        return this.pvRtDataService.getGtiStatusBi(commIdChain)
//                .map(RestUtils::buildListResponse);
//    }
//
//}
