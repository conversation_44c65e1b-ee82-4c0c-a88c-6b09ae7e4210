package com.cdz360.iot.pv.rest.internal;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.pv.biz.GtiBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@RestController
@Tag(name = "光伏相关接口", description = "光伏服务")
@RequestMapping("/iot/biz/pv")
public class PvRest {

    @Autowired
    private GtiBizService gtiBizService;


//    @Operation(summary = "发送获取逆变器信息指令")
//    @PostMapping(value = "/sendGetGtiInfoCmd")
//    public Mono<BaseResponse> sendGetGtiInfoCmd(@RequestParam String siteId, @RequestParam List<String> gtiNos) {
//        log.info("发送获取逆变器信息指令。siteId = {}", siteId);
//        return gtiBizService.getGtiInfo(siteId, gtiNos)
//                .map(res -> RestUtils.success());
//    }

    @Operation(summary = "发送获取逆变器信息指令")
    @PostMapping(value = "/sendGetGtiInfoCmd")
    public Mono<BaseResponse> sendGetGtiInfoCmd(@RequestParam String dno) {
        log.info("发送获取逆变器信息指令。dno = {}", dno);
        return gtiBizService.getGtiInfo(dno)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "设置逆变器信息")
    @PostMapping(value = "/sendModifyGtiCfgCmd")
    public Mono<BaseResponse> sendModifyGtiCfgCmd(
            @Parameter(name = "微网控制器编号", required = true) @RequestParam String gwno,
            @Parameter(name = "逆变器编号", required = true) @RequestParam String dno,
            @Parameter(name = "配置模板ID", required = true) @RequestParam Long cfgId) {
        log.info("下发逆变器配置信息。gwno = {}, dno = {}, cfgId = {}", gwno, dno, cfgId);
//        // TODO: 当前仅为调试代码
//        PvCfgDto pvCfg = new PvCfgDto();
//        pvCfg.setId(247)
//                .setSamplingTime(samplingTime);
//        List<PvCfgDto> pvCfgList = List.of(pvCfg);
        return gtiBizService.modifyGtiCfg(gwno, dno, cfgId)
                .map(res -> RestUtils.success());
    }

    @Operation(summary = "上传逆变器数据文件到oss(用于手动补传数据)")
    @GetMapping(value = "/uploadGtiDataFile")
    public Mono<BaseResponse> uploadGtiDataFile(
            @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId,
            @Parameter(name = "日期", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @RequestParam(value = "date", required = false) Date date) {
        log.info("手动上传逆变器运行数据 siteId = {}, date = {}", siteId, date);
        return gtiBizService.uploadGtiDataFile(siteId, date)
                .map(o -> RestUtils.success());
    }

    /**
     * 采集并处理华为云设备日数据
     */
    @PostMapping("/collectAndProcessKpiDay")
    public Mono<BaseResponse> collectAndProcessDeviceKpiDay(@RequestParam String date) {
        log.info("接收到华为云设备日数据采集请求，采集日期: {}", date);
        java.time.LocalDate collectDate = null;
        if (date != null && !date.trim().isEmpty()) {
            try {
                collectDate = java.time.LocalDate.parse(date);
            } catch (Exception e) {
                log.error("日期格式错误: {}", date, e);
            }
        }
        return gtiBizService.collectAndProcessDeviceKpiDay(collectDate)
                .map(result -> RestUtils.success())
                .onErrorResume(throwable -> {
                    log.error("华为云设备日数据采集失败: {}", throwable.getMessage(), throwable);
                    return Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVER_ERROR, "操作失败"));
                });
    }
}
