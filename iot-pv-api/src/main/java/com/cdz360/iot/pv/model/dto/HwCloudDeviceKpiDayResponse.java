package com.cdz360.iot.pv.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 华为云设备日数据接口响应
 */
@Data
public class HwCloudDeviceKpiDayResponse {

    /**
     * 请求成功失败标识
     * true: 请求成功
     * false: 请求失败
     */
    private boolean success;

    /**
     * 错误码
     * 0 表示正常，其他错误码参见错误码列表
     */
    private Integer failCode;

    /**
     * 包含如下信息
     */
    private HwCloudDeviceKpiDayParams params;

    /**
     * 可选消息
     */
    private String message;

    /**
     * 返回数据，data里面是各个设备的日数据对象信息列表
     */
    private List<HwCloudDeviceKpiDayData> data;

    /**
     * 参数信息
     */
    @Data
    public static class HwCloudDeviceKpiDayParams {
        /**
         * 请求参数中的设备编号列表
         */
        private String devIds;
        
        /**
         * 请求参数中的设备sn列表
         */
        private String sns;
        
        /**
         * 请求参数中的设备类型ID
         */
        private Integer devTypeId;

        /**
         * 请求参数中的采集时间（毫秒）
         */
        private Long collectTime;

        /**
         * 系统当前时间（毫秒）
         */
        private Long currentTime;
    }

    /**
     * 设备日数据
     */
    @Data
    public static class HwCloudDeviceKpiDayData {
        /**
         * 设备编号
         */
        private Long devId;
        
        /**
         * 设备SN号
         */
        private String sn;
        
        /**
         * 采集时间（毫秒）
         */
        private Long collectTime;

        /**
         * 每个数据项的内容，用key-value形式返回，不同设备类型的数据项内容不一样
         */
        private Map<String, Object> dataItemMap;
    }
}