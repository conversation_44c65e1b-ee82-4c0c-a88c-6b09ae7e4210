package com.cdz360.iot.pv;

import com.netflix.discovery.EurekaClient;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;

@SpringBootApplication
//@MapperScan(basePackages = { "com.cdz360.iot.worker.ds.mapper" })
@ComponentScan(basePackages = {"com.cdz360.iot", "com.cdz360.data"})
//@EnableEurekaClient
//@EnableCircuitBreaker
@MapperScan(basePackages = {"com.cdz360.iot.**.mapper"})
@EnableDiscoveryClient(autoRegister = true)
@EnableReactiveFeignClients(basePackages = { "com.cdz360.iot.pv.feign"})
@EnableAsync
@EnableScheduling
public class IotPvMain {
    private final Logger logger = LoggerFactory.getLogger(IotPvMain.class);

    @Autowired
    private EurekaClient discoveryClient;

    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(IotPvMain.class)
                // .web(WebApplicationType.SERVLET)
                .web(WebApplicationType.REACTIVE)
                .run(args);
    }

    @PreDestroy
    public void destroy() {
        logger.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        logger.info(".....");
    }
}
