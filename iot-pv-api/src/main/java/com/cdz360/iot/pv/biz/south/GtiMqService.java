package com.cdz360.iot.pv.biz.south;

import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.model.pv.dto.PvCfgDto;
import com.cdz360.iot.model.site.mqtt.GetGtiInfoReq;
import com.cdz360.iot.model.site.mqtt.ModifyGtiCfgReq;
import com.cdz360.iot.model.site.mqtt.UploadGtiDataFileReq;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.srs.dto.SrsCfgDto;
import com.cdz360.iot.model.srs.po.SrsPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 发送逆变器Mq消息的封装
 */
@Slf4j
@Service
public class GtiMqService {


    @Autowired
    private MqService mqService;

    /**
     * 获取逆变器信息
     *
     * @return 下发消息的mq seq
     */
    public String getGtiInfo(GwInfoPo gwInfo, List<String> gtiNos) {
        GetGtiInfoReq.builder builder = new GetGtiInfoReq.builder();
        builder.setGtiNos(gtiNos)
                .setGwno(gwInfo.getGwno());
        GetGtiInfoReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

    /**
     * 修改逆变器配置信息
     *
     * @return 下发消息的mq seq
     */
    public String modifyGtiCfg(GwInfoPo gwInfo, Long cfgVer, SrsPo srsPo,
                               boolean full, List<PvCfgDto> gtiCfgList) {
        ModifyGtiCfgReq.builder builder = new ModifyGtiCfgReq.builder();
        builder.setCfgVer(cfgVer)
                .setFull(full)
                .setPvCfgList(gtiCfgList)
                .setGwno(gwInfo.getGwno());
        if (srsPo != null) {
            builder.setSrsCfg(new SrsCfgDto()
                    .setDno(srsPo.getDno())
                    .setSid(srsPo.getSid()));
        }
        ModifyGtiCfgReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }


    /**
     * 下发指令，上传逆变器数据文件
     * @param gwInfo
     * @param date
     * @param dnoList
     * @return
     */
    public String uploadGtiDataFile(GwInfoPo gwInfo, Date date, List<String> dnoList) {
        UploadGtiDataFileReq.builder builder = new UploadGtiDataFileReq.builder();
        builder.setDate(date)
                .setDnoList(dnoList);
        UploadGtiDataFileReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }
}
