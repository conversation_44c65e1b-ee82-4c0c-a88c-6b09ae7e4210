package com.cdz360.iot.pv.biz.south;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SrsRoDs;
import com.cdz360.iot.ds.rw.SrsRwDs;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.srs.dto.SrsRtDataDto;
import com.cdz360.iot.model.srs.po.SrsPo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.cdz360.iot.pv.biz.RedisPvRtDataService;
import com.cdz360.iot.pv.biz.RedisSrsRtDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SrsSouthBizService {

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;
//    @Autowired
//    private BizDataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private SrsRoDs srsRoDs;

    @Autowired
    private SrsRwDs srsRwDs;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @Autowired
    private RedisPvRtDataService redisPvRtDataService;

    @Autowired
    private RedisSrsRtDataService redisSrsRtDataService;

//    // 获取文件上传的STS信息
//    public Mono<CommonResponse<OssStsDto>> getOssArchiveSts() {
//        return Mono.just("")
//                .flatMap(e -> dataCoreFeignClient.getArchiveSts())
//                .doOnNext(FeignResponseValidate::check)
//                .map(e -> {
//                    CommonResponse res = new CommonResponse(e.getData());
//                    return res;
//                });
//    }

    /**
     * 辐射仪数据上传
     *
     * @return
     */
    public Mono<BaseGwResponse> srsRtData(GwObjReqMsg<SrsRtDataDto> gwReq) {
        SrsRtDataDto req = gwReq.getData();
        log.debug(">> json = {}", JsonUtils.toJsonString(req));

        // 获取网关与场站关系信息
        GwInfoDto gwInfo = gwSiteRefRoDs.getGwInfoByGwno(gwReq.getGwno(), true);
        IotAssert.isNotNull(gwInfo, "网关信息无效");

        if (StringUtils.isBlank(gwInfo.getSiteId())) {
            log.error("微网控制器需要手动绑定到场站: gwno = {}", gwReq.getGwno());
            throw new DcArgumentException("微网控制器没有绑定到场站");
        }

        if (req == null) {
            throw new DcArgumentException("辐射仪运行数据无效");
        }

        return Mono.just(req)
                .doOnNext(srs -> {
                    if (StringUtils.isNotBlank(srs.getDno())) {
                        SrsPo po = srsRoDs.getByDno(srs.getDno());
                        if (null != po) {
//                            // 逆变器数据更新：目前逻辑可认为 (siteId + gwno + sid) 唯一
//                            GtiPo po = gtiRwDs.getBySiteIdAndGwnoAndSid(
//                                    gwInfo.getSiteId(), gwInfo.getGwno(), srs.getDeviceId(), true);
//                            if (null == po) {
//                                po = new GtiPo()
//                                        .setDno(RandomStringUtils.randomAlphanumeric(8))
//                                        .setName("数据推送生成")
//                                        .setVendor(GtiVendor.GOOD_WE)
//                                        .setSiteId(gwInfo.getSiteId())
//                                        .setGwno(gwReq.getGwno())
//                                        .setSid(srs.getDeviceId());
//                            }

                            if (EquipStatus.NORMAL != po.getStatus()) {
                                po.setStatus(EquipStatus.NORMAL);
                            }
                            if (CollectionUtils.isEmpty(srs.getErrorCodeList())) {
                                po.setAlertStatus(EquipAlertStatus.OK);
                            } else {
                                po.setAlertStatus(EquipAlertStatus.ABNORMAL);
                            }
                            srsRwDs.upsetSrs(po);

                            // 推送数据到device
                            PvGtiVo srsVo = new PvGtiVo();
                            srsVo.setSiteId(gwInfo.getSiteId())
                                    .setSiteCommId(gwInfo.getSiteCommId())
                                    .setSiteName(gwInfo.getSiteName())
                                    .setRecId(po.getId().toString())
                                    .setDno(po.getDno())
//                                    .setSid(po.getSid())
                                    .setGwno(gwInfo.getGwno())
                                    .setGwName(gwInfo.getName())
                                    .setName(po.getName())
//                                    .setRtStatus(srs.getRtMode().getCode())
                                    .setErrorCodeList(srs.getErrorCodeList());
                            this.dcEventPublish.publishSrsInfo(IotEvent.STATE_CHANGE, srsVo);

                            // 数据临时存入缓存: 获取实时数据可用
                            this.redisSrsRtDataService.pushRtData(srs.getDno(), srs);
                        } else {
                            log.error("该辐射仪编号有误: gwno = {}, dno = {}", gwInfo.getGwno(), srs.getDno());
                        }
                    } else {
                        log.warn("该辐射仪编号没有下发: gwno = {}", gwInfo.getGwno());
                    }
                })
                .map(cnt -> new BaseGwResponse());
    }

}
