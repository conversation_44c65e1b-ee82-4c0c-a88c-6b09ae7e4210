package com.cdz360.iot.pv.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.pv.dto.PvRtDataDto;
import com.cdz360.iot.model.pv.vo.RedisPvRtData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RedisPvRtDataService {
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRE_REDIS_KEY = "pv:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 今天光伏运行时数据压入
     *
     * @param dno 逆变器编号
     * @param data 数据
     */
    public void pushRtData(String dno, PvRtDataDto data) {
        String key = formatKey(dno, LocalDate.now());

        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(RedisPvRtData.convert(data)));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时数据压入
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @param data 数据
     */
    public void pushRtData(String dno, LocalDate date, PvRtDataDto data) {
        String key = formatKey(dno, date);
        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(RedisPvRtData.convert(data)));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时数据批量压入
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @param dataList 数据
     */
    public void pushRtData(String dno, LocalDate date, List<PvRtDataDto> dataList) {
        String key = formatKey(dno, date);
        redisTemplate.opsForList()
                .rightPushAll(key, dataList.stream()
                        .filter(data -> StringUtils.isNotBlank(data.getDno()))
                        .map(RedisPvRtData::convert)
                        .map(JsonUtils::toJsonString)
                        .collect(Collectors.toList()));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    /**
     * 光伏运行时先拆分数据，再将数据批量压入
     *
     * @param date 日期
     * @param dataList 数据
     */
    public void splitPushRtData(LocalDate date, List<PvRtDataDto> dataList) {
        Map<String, List<PvRtDataDto>> collect = dataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.getDno()))
                .collect(Collectors.groupingBy(PvRtDataDto::getDno));
        collect.keySet().forEach(dno -> this.pushRtData(dno, date, collect.get(dno)));
    }

    public RedisPvRtData pvSrcInfo(String dno) {
        return this.latestRtData(dno, LocalDate.now());
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param dno
     * @param date
     * @return
     */
    public RedisPvRtData latestRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, RedisPvRtData.class);
    }

    private static String formatKey(String dno, LocalDate date) {
        return PRE_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatKey(String dno, String date) {
        return PRE_REDIS_KEY + dno + ":" + date;
    }
}
