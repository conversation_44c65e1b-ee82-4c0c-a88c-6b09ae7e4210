package com.cdz360.iot.pv;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication
@EnableTransactionManagement
@EnableDiscoveryClient(autoRegister = true)
@EnableFeignClients(basePackages = { "com.cdz360.iot.*.feign", "com.cdz360.iot.device.mgm.feign", "com.cdz360.iot.worker.ds.client"})

@ComponentScan(basePackages = {"com.cdz360.iot", "com.cdz360.data"})
public class IotPvTestMain {
    private final static Logger logger = LoggerFactory.getLogger(IotPvTestMain.class);

    public static void main(String[] args) {
        logger.info("starting....");
        SpringApplication.run(IotPvTestMain.class, args);
        logger.info("started");
    }
}