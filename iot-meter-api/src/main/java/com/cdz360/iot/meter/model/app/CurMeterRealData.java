package com.cdz360.iot.meter.model.app;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname CurMeterRealData
 * @Description
 * @Date 10/14/2020 3:38 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CurMeterRealData extends CurMeterData {

    // 电流互感器变比
    private BigDecimal currentRate;

    // 抄表时间,unix时间戳
    private Long updateTime;
}