package com.cdz360.iot.meter.biz.south;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.meter.dto.MeterRtReq;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@Service
public class MeterSouthBizService {

    @Autowired
    private MeterRedisService meterRedisService;

    @Autowired
    private MeterChannelRepository meterChannelRepository;

    @Autowired
    private MeterRwDs meterRwDs;

    @Autowired
    private MeterRoDs meterRoDs;

    public Mono<BaseGwResponse> processRtData(MeterRtReq rtDataIn) {
        return Flux.fromIterable(rtDataIn.getMeterData())
                .doOnNext(rtData -> {
                    if (StringUtils.isBlank(rtData.getDno())) {
                        log.error("电表设备编号不能为空");
                    } else {
                        meterRedisService.pushRtData(rtData.getDno(), rtData);
                        MeterPo byNo = meterRoDs.getByNo(rtData.getDno());
                        if(byNo != null) {
                            byNo.setLastActiveTime(new Date())
                                    .setStatus(MeterStatusType.ONLINE);
                            meterRwDs.updateMeter(byNo);
                        } else {
                            log.warn("找不到电表: {}, 无法更新状态", rtData.getDno());
                        }
                        meterChannelRepository.softPut(rtData.getDno(), null);
                    }
                }).collectList()
                .map(a -> new BaseGwResponse());
    }


}
