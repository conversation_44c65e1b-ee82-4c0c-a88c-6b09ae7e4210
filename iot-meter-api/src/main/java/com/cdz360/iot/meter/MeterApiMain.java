package com.cdz360.iot.meter;

import com.cdz360.iot.meter.south.NettyTcpServer;
import com.netflix.discovery.EurekaClient;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;

@Slf4j
@SpringBootApplication
//@MapperScan(basePackages = { "com.cdz360.iot.worker.ds.mapper" })
@ComponentScan(basePackages = {"com.cdz360.iot", "com.cdz360.data"})
//@EnableEurekaClient
//@EnableCircuitBreaker
@MapperScan(basePackages = {"com.cdz360.iot.**.mapper"})
@EnableDiscoveryClient(autoRegister = true)
@EnableFeignClients(basePackages = { "com.cdz360.iot.*.feign", "com.cdz360.iot.device.mgm.feign", "com.cdz360.iot.worker.ds.client"})
@EnableReactiveFeignClients(basePackages = { "com.cdz360.iot.meter.feign"})
@EnableAsync
@EnableScheduling
public class MeterApiMain {

    @Autowired
    private EurekaClient discoveryClient;

    public static void main(String[] args) throws Exception {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        ConfigurableApplicationContext context =  new SpringApplicationBuilder(MeterApiMain.class).web(WebApplicationType.REACTIVE).run(args);
        NettyTcpServer tcpServer = context.getBean(NettyTcpServer.class);
        tcpServer.start();
    }

    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info(".....");
    }
}
