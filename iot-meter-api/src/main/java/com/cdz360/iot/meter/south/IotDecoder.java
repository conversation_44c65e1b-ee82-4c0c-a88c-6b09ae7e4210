package com.cdz360.iot.meter.south;


import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.south.decoder.BaseDecoder;
import com.cdz360.iot.meter.utils.ByteUtil;
import com.cdz360.iot.meter.utils.SeqGeneratorUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import io.netty.handler.codec.TooLongFrameException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 网关和电表间数据的解包
 */
@Component
@Slf4j
@Scope(value = "singleton")
@ChannelHandler.Sharable
public class IotDecoder extends MessageToMessageDecoder<ByteBuf> {
    //private final Logger logger = LoggerFactory.getLogger(com.cdz360.iot.gw.server.IotDecoder.class);

    // 报文头
    private static final byte MSG_HEADER = (byte) 0x68;
    private static final byte HEADER_AND_FOOTER_LENGTH = 11;//报头(1)+设备序号(6)+控制码(1)+数据域长度(1)+校验码(1)+结束符(2)

    // 数据包最大长度
    private static final int MAX_FRAME_SIZE = 1024 * 2; // 2M
    private static final int MAX_PACKAGE_SIZE = 1024; // 单个报文最大长度限制
    @Autowired
    private NettyChannelRepository nettyChannelRepository;

//    @Autowired
//    private IdempotenceService idempotenceService;
//
//    @Autowired
//    private IotChannelEvseMap iotChannelEvseMap;
//
//    @Autowired
//    private QpsCounter qpsCounter;
    //
    //    @Autowired
    //    private EvseSignKeyCache evseSignKeyCache;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) {
        int msgLength = msg.readableBytes();
        ByteBuf byteBuf = msg.readBytes(msgLength);
        byte[] bytes = new byte[msgLength];
        byteBuf.getBytes(0, bytes);
        byteBuf.release();

        String rawBytes = ByteUtil.bytesToHex(bytes);
        String rootId = SeqGeneratorUtil.newStringId();
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("收到报文。rootId: {}, channelKey: {}, bytes: {}.", rootId, channelKey, rawBytes);

        // 保存源数据
//        String evseNo = this.iotChannelEvseMap.getEvseNo(channelKey);
//        if (StringUtils.isEmpty(evseNo)) {
//            log.warn("无法获取到桩号. rootId: {}, channelKey: {}", rootId, channelKey);
//        }
//        SaveTransDataService.evseExport(rootId, evseNo, rawBytes);

        if (bytes.length > MAX_FRAME_SIZE) {
            log.warn("客户端报文太大！rootId: {}, channel: {}", rootId, channelKey);
            resetClient(ctx.channel());//发送大量报文/或者从缓冲区收到大量报文直接踢掉

            throw new TooLongFrameException("Frame too big!");
        }

        // 数据长度位0
        if (0 == bytes.length) {
            log.warn("客户端没有发送报文！rootId: {}, channel: {}", rootId, channelKey);
            resetClient(ctx.channel());
            return;
        }

        // 报文头判断
//        String datagram = idempotenceService.get(channelKey);

//        if (datagram == null) {
//            if (bytes[0] != MSG_HEADER) {
//                logger.warn("客户端报文非法！rootId: {}, channel: {}", rootId, channelKey);
//                resetClient(ctx.channel());
//                return;
//            }
//        } else {
//            byte[] cacheBytes = ByteUtil.hexToBytes(datagram);
//
//            byte[] tempBytes = new byte[cacheBytes.length + bytes.length];
//
//            //合并接收到的报文
//            System.arraycopy(cacheBytes, 0, tempBytes, 0, cacheBytes.length);
//            System.arraycopy(bytes, 0, tempBytes, cacheBytes.length, bytes.length);
//
//            //将合并后的报文重新放在bytes中进行处理
//            bytes = new byte[tempBytes.length];
//            System.arraycopy(tempBytes, 0, bytes, 0, tempBytes.length);
//
//            idempotenceService.remove(channelKey);
//        }

        int size = bytes.length;

        int headerIndex = 0;
        int availableSize = 0;
        int msgCounter = 1;
        do {
            log.debug("decode. counter: {}, rootId: {}, size: {}, headerIndex: {}",
                    msgCounter, rootId, size, headerIndex);

            availableSize = size - headerIndex;

            // 数据长度位0
            if (0 == availableSize) {
                // 粘包时的0长度的报文直接返回。
                return;
            }

            // 报文头判断
            byte header = bytes[headerIndex];
            if (header != MSG_HEADER) {
                log.warn("报文头非法！rootId: {}, channel: {}, header: {}", rootId, channelKey, header);
                // resetClient(ctx.channel());
                return;
            }

            // 长度过小
            byte[] remain;
            if (availableSize < HEADER_AND_FOOTER_LENGTH) {
                // 粘包时的长度不合法的报文直接返回。
                // logger.warn("客户端{}报文非法, 报文原文： {}！", ctx.channel().remoteAddress(), msg.getByte(0));
                // resetClient(ctx.channel());

                remain = new byte[availableSize];
                System.arraycopy(bytes, headerIndex, remain, 0, availableSize);

                //idempotenceService.put(channelKey, ByteUtil.bytesToHex(remain));

                return;
            }

            IotBaseMsg baseReq = BaseDecoder.decode(bytes, headerIndex, availableSize);
            // 控制位
            // byte[] len = new byte[2];
            //System.arraycopy(bytes, headerIndex + 17, len, 0, 2);
            //short length = ByteBuffer.allocate(2).order(ByteOrder.BIG_ENDIAN).wrap(len).getShort();// 数据长度
//            int length = baseReq.getLength();
//
//            if (length > MAX_PACKAGE_SIZE) {
//                logger.warn("单个报文长度非法！rootId: {}, channel: {}, length: {}", rootId, channelKey, length);
//                resetClient(ctx.channel());
//                return;
//            }
//
//            if (length + HEADER_AND_FOOTER_LENGTH > availableSize) {
//                // logger.warn("客户端{}报文大小非法, 报文原文： {}！", ctx.channel().remoteAddress(), msg.getByte(0));
//                // resetClient(ctx.channel());
//
//                remain = new byte[availableSize];
//                System.arraycopy(bytes, headerIndex, remain, 0, availableSize);
//
//                idempotenceService.put(channelKey, ByteUtil.bytesToHex(remain));
//
//                return;
//            }

            // 完整数据包
            // logger.debug("msg byte buf size: {}, msg len: {}.", availableSize, length);

           // byte[] array = new byte[length + HEADER_AND_FOOTER_LENGTH];//解析出来的报文中的数据长度(length)+非数据域的长度

           // System.arraycopy(bytes, headerIndex, array, 0, array.length);

            // logger.debug("array: {}", ByteUtil.bytesToHex(array));

            // 报文控制位
//            EvseMessageType type = EvseMessageType.codeOf(array[1]);
//            if (type == null) {
//                logger.error("message type error, can not find in EvseMessageType.class: type: {}", array[1]);
//                out.add(null);
//                logger.warn("报文控制位非法, rootId: {}, channelKey: {}, 报文原文： {}！", rootId, channelKey, ByteUtil.bytesToHex(array));
//                resetClient(ctx.channel());
//                return;
//            }

            String traceId = SeqGeneratorUtil.newStringId();
            baseReq.setTraceId(traceId);
            baseReq.setChannelKey(channelKey);

            //下面一行日志非常重要，关系后面日志中的flowId是否可以找到原始报文，勿删！！！
  //          logger.info("第{}个报文。rootId: {}, flowId: {}, direction: {}, bytes: {}.", msgCounter, rootId, flowId, type.getStreamDirection(), ByteUtil.bytesToHex(array));

//            IotRequest iotReq = new IotRequest(baseReq, array);
//            //iotReq.setBaseMsg(baseReq);
//            iotReq.setChannelKey(channelKey);
//            iotReq.setDecodeTimestamp(System.nanoTime());
//            iotReq.setFlowId(flowId);

            //logger.debug("第{}个IotRequest初始化。rootId: {}.", msgCounter, rootId);

            // 输出对象
            out.add(baseReq);

            //logger.debug("第{}个IotRequest添加到集合。rootId: {}.", msgCounter, rootId);

            //有粘包（多个报文合在一起），将偏移量移动到下一个报文头开始的地方
            headerIndex = headerIndex + baseReq.getLength() + HEADER_AND_FOOTER_LENGTH;

            msgCounter++;

        }
        while (headerIndex < size);
  //      qpsCounter.increase(msgCounter - 1);
        log.debug("[{}] decoded end. counter: {}, rootId: {}", channelKey, msgCounter, rootId);
    }

    private void resetClient(Channel channel) {
        //idempotenceService.remove(channel.remoteAddress().toString());//踢出之前将前面发送的报文清空
        nettyChannelRepository.remove(channel.remoteAddress().toString());
        channel.close();
    }
}
