package com.cdz360.iot.meter.model.iot.p645;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class P645VoltageResponse extends P645BaseMsg {

    /**
     * A相电压， 1位小数
     */
    private BigDecimal voltageA;

    private BigDecimal voltageB;

    private BigDecimal voltageC;


    public P645VoltageResponse() {

    }

    public P645VoltageResponse(P645BaseMsg baseMsg) {
        super.setTraceId(baseMsg.getTraceId());
        super.setChannelKey(baseMsg.getChannelKey());
        super.setDeviceNo(baseMsg.getDeviceNo());
        super.setCmdCode(baseMsg.getCmdCode());
        super.setLength(baseMsg.getLength());
        super.setDataType(baseMsg.getDataType());
        super.setData(baseMsg.getData());
        super.setCs(baseMsg.getCs());
    }
}
