package com.cdz360.iot.meter.south.handler;


import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.utils.ByteUtil;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理电表请求的封装类
 */
@Slf4j
@Component
public class MeterHandlerFacade {

    //private final Logger logger = LoggerFactory.getLogger(com.cdz360.iot.gw.server.IotEvseFacade.class);

//    @Autowired
//    private IotEvseProcessor iotEvseProcessor;

    private Map<CmdCode, IotHandler> handlerMap = new ConcurrentHashMap<>();

    public void addProducer(CmdCode cmdCode, IotHandler producer) {
        this.handlerMap.put(cmdCode, producer);
    }

    //@Autowired
    //private IdempotenceService idempotenceService;

    public Mono<byte[]> process(Channel channel, IotBaseMsg msg) {
        log.info("处理电表向网关上报报文信息。traceId: {}, msg = {}", msg.getTraceId(), msg);
//        //long startTime = System.currentTimeMillis();
//
//        //收到重复报文使用缓存的报文回复
//        String responseBytes = idempotenceService.get(msg.getSeqNo4Plug());
//
//        if (!StringUtils.isEmpty(responseBytes)) {
//            logger.warn("收到重复报文，使用缓存回复。flowId: {}, seqNo:{}, msg: {}, responseBytes: {}", msg.getFlowId(), msg.getSeqNo(), ByteUtil.bytesToHex(msg.getFullMsg()), responseBytes);
//            return Mono.just(ByteUtil.hexToBytes(responseBytes));
//        }
        msg.setChannel(channel);
        IotHandler handler = this.handlerMap.get(msg.getCmdCode());
        if (handler == null) {
            log.error("不识别的指令码 {} !!!!!!!!!!!!!!!!!!! msg = {}",
                    msg.getCmdCode(), ByteUtil.bytesToHex(msg.toBytes()));
            return Mono.empty();
        }
        return handler.process( msg);
//
//        logger.info("处理桩向网关上报报文信息A。flowId: {}", msg.getFlowId());
//        //预处理部分报文
//        this.iotEvseProcessor.preprocess(msg);
//
//        logger.info("处理桩向网关上报报文信息B。flowId: {}", msg.getFlowId());
//        // 上传数据
//        return this.iotEvseProcessor.evseProcess(msg)
//                .doOnNext(bytes -> pushIdempotenceCache(msg, bytes));    // 把响应消息加入到序列号缓存中
//
    }

//    private void pushIdempotenceCache(IotRequest req, byte[] bytes) {
//        if (bytes != null && bytes.length > 0) {
//            // logger.debug("增加幂等性缓存。flowId: {}, seqNo:{}, replyBytes: {}", req.getFlowId(), req.getSeqNo(), ByteUtil.bytesToHex(bytes));
//            idempotenceService.put(req.getSeqNo4Plug(), ByteUtil.bytesToHex(bytes));
//        }
//    }
}
