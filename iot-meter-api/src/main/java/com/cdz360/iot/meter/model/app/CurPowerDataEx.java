package com.cdz360.iot.meter.model.app;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname CurPowerDataEx
 * @Description 实时读表
 * @Date 12/3/2020 5:09 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class CurPowerDataEx extends CurPowerData {
    /**
     * 总组合有功
     */
    private BigDecimal combineTotal;
    private BigDecimal combineA;
    private BigDecimal combineB;
    private BigDecimal combineC;
    private BigDecimal combineD;
    /**
     * 正向总有功
     */
    private BigDecimal positiveTotal;
    private BigDecimal positiveA;
    private BigDecimal positiveB;
    private BigDecimal positiveC;
    private BigDecimal positiveD;
    /**
     * 反向总有功
     */
    private BigDecimal negativeTotal;
    private BigDecimal negativeA;
    private BigDecimal negativeB;
    private BigDecimal negativeC;
    private BigDecimal negativeD;

    // 抄表时间,unix时间戳
    private Long updateTime;
}