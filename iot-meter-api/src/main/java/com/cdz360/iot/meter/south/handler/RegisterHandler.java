package com.cdz360.iot.meter.south.handler;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.north.biz.MeterDataService;
import com.cdz360.iot.meter.north.consumer.ReadCurDataService;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;

/**
 * 电表注册
 */
@Slf4j
@Service
public class RegisterHandler implements IotHandler {


    private static final CmdCode CMD_CODE = CmdCode.LOGIN_REQ;

    @Autowired
    private MeterHandlerFacade iotEvseFacade;

    @Autowired
    private ReadCurDataService readConsumer;


    @Autowired
    private MeterChannelRepository channelRepository;

    @Autowired
    private MeterDataService meterDataService;

    @PostConstruct
    public void init() {
        this.iotEvseFacade.addProducer(CMD_CODE, this);
    }

    @Override
    public Mono<byte[]> process(IotBaseMsg msg) {
        log.info(">> 电表注册 traceId = {}， deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
        // log.info("iot msg = {}", ByteUtil.bytesToHex(msg.toBytes()));

        meterDataService.registerMeter(msg.getDeviceNo(), msg.getTraceId());

        channelRepository.put(msg.getDeviceNo(), msg.getChannel());
        IotBaseMsg resMsg = msg.buildRes(CmdCode.LOGIN_RES, null);

//        long time = 5000L;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo());
//
//
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.POWER_POSITIVE);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.POWER_NEGATIVE);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.IDLE_POWER_1);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.IDLE_POWER_2);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.LAST_DAY_POWER_COMBINE);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.LAST_DAY_POWER_POSITIVE);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.LAST_DAY_POWER_NEGATIVE);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.LAST_DAY_IDLE_POWER_1);
//            }
//        }, time);
//        time += 1000;
//        new Timer().schedule(new TimerTask() {
//            @Override
//            public void run() {
//                log.info("test test test");
//                byte[] buf = new byte[4];
//                buf[0] = buf[1] = buf[2] = buf[3] = (byte) 0xFE;
//                // channel.writeAndFlush(buf);
//                readConsumer.process(msg.getDeviceNo(), P645DataType.LAST_DAY_IDLE_POWER_2);
//            }
//        }, time);



        return Mono.just(resMsg.toBytes());
    }
}
