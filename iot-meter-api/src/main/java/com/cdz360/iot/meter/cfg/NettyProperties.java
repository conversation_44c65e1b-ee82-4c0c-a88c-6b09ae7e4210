package com.cdz360.iot.meter.cfg;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * netty 相关的配置参数
 */
@ConfigurationProperties(prefix = "netty")
public class NettyProperties {
    private int tcpPort;

    private int bossCount;

    private int workerCount;

    private boolean keepAlive;

    private int backlog;

    public int getTcpPort() {
        return tcpPort;
    }

    public NettyProperties setTcpPort(int tcpPort) {
        this.tcpPort = tcpPort;
        return this;
    }

    public int getBossCount() {
        return bossCount;
    }

    public NettyProperties setBossCount(int bossCount) {
        this.bossCount = bossCount;
        return this;
    }

    public int getWorkerCount() {
        return workerCount;
    }

    public NettyProperties setWorkerCount(int workerCount) {
        this.workerCount = workerCount;
        return this;
    }

    public boolean isKeepAlive() {
        return keepAlive;
    }

    public NettyProperties setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
        return this;
    }

    public int getBacklog() {
        return backlog;
    }

    public NettyProperties setBacklog(int backlog) {
        this.backlog = backlog;
        return this;
    }
}