package com.cdz360.iot.meter.model.iot.p645;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * @Classname P645TransformerCurrentResponse
 * @Description
 * @Date 10/14/2020 2:44 PM
 * @Created by Rafael
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class P645TransformerCurrentResponse extends P645BaseMsg {

    private BigDecimal rate;



    public P645TransformerCurrentResponse() {

    }

    public P645TransformerCurrentResponse(P645BaseMsg baseMsg) {
        super.setTraceId(baseMsg.getTraceId());
        super.setChannelKey(baseMsg.getChannelKey());
        super.setDeviceNo(baseMsg.getDeviceNo());
        super.setCmdCode(baseMsg.getCmdCode());
        super.setLength(baseMsg.getLength());
        super.setDataType(baseMsg.getDataType());
        super.setData(baseMsg.getData());
        super.setCs(baseMsg.getCs());
    }
}