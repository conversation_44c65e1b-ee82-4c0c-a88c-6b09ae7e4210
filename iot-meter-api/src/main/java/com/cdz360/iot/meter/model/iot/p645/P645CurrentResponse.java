package com.cdz360.iot.meter.model.iot.p645;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class P645CurrentResponse extends P645BaseMsg {

    /**
     * A相电流， 3位小数
     */
    private BigDecimal currentA;

    private BigDecimal currentB;

    private BigDecimal currentC;


    public P645CurrentResponse() {

    }

    public P645CurrentResponse(P645BaseMsg baseMsg) {
        super.setTraceId(baseMsg.getTraceId());
        super.setChannelKey(baseMsg.getChannelKey());
        super.setDeviceNo(baseMsg.getDeviceNo());
        super.setCmdCode(baseMsg.getCmdCode());
        super.setLength(baseMsg.getLength());
        super.setDataType(baseMsg.getDataType());
        super.setData(baseMsg.getData());
        super.setCs(baseMsg.getCs());
    }
}
