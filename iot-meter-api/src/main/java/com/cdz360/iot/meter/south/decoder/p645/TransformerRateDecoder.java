package com.cdz360.iot.meter.south.decoder.p645;

import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645PowerResponse;
import com.cdz360.iot.meter.model.iot.p645.P645TransformerCurrentResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * @Classname TransformerRateDecoder
 * @Description 互感倍率
 * @Date 10/14/2020 1:39 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class TransformerRateDecoder extends P645Decoder {

    @Autowired
    private P645DecoderFactory p645DecoderFactory;

    @PostConstruct
    public void init() {
        p645DecoderFactory.addDecoder(P645DataType.TRANSFORMER_CUR_RATE, this);
        p645DecoderFactory.addDecoder(P645DataType.TRANSFORMER_VOL_RATE, this);

    }

    @Override
    public P645BaseMsg parse(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        P645TransformerCurrentResponse p645Msg = new P645TransformerCurrentResponse(baseMsg);
        byte[] data = p645Msg.getData();

        //p645Msg.setDataType( p645Msg.parseDataType(data[0], data[2]));

        int idx = 4;
        p645Msg.setRate(this.parseDecimal30Value(data, idx));
        idx+=4;

        return p645Msg;
    }
}