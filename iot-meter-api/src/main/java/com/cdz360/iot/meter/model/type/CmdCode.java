package com.cdz360.iot.meter.model.type;

public enum CmdCode {
    LOGIN_REQ((byte) 0x08, StreamDirection.Upstream),              // [上行]登录
    LOGIN_RES((byte) 0x88, StreamDirection.Downstream),              // [下行]登录

    APP_REQ((byte) 0x00, StreamDirection.Downstream),    // 下行， 透传消息
    APP_RES((byte) 0x80, StreamDirection.Upstream),    // 上行， 透传消息

    HB_REQ((byte) 0x0A, StreamDirection.Upstream),              // [上行]心跳
    HB_RES((byte) 0x8A, StreamDirection.Downstream),              // [下行]心跳

    READ_REQ((byte) 0x11, StreamDirection.Upstream),              // [下行]读数据
    READ_RES((byte) 0x91, StreamDirection.Downstream),              // [上行]读数据. 无后续数据帧
    READ_MORE_RES((byte) 0xB1, StreamDirection.Downstream),              // [上行]读数据. 有后续数据帧

    REPORT_REQ((byte) 0x1F, StreamDirection.Upstream),              // 上行, 电表主动上报. 无后续数据帧
    REPORT_MORE_REQ((byte) 0x3F, StreamDirection.Upstream),              // 上行, 电表主动上报. 有后续数据帧

    POWER_REQ((byte) 0x0F, StreamDirection.Upstream),              // 上行, 电表上报停电/供电信息

    ERROR_RES((byte) 0xD1, StreamDirection.Downstream),             // 异常响应
    ;

    private final byte code;
    private StreamDirection streamDirection;

    CmdCode(byte code, StreamDirection streamDirection) {
        this.code = code;
        this.streamDirection = streamDirection;
    }

    public byte getCode() {
        return code;
    }

    public StreamDirection getStreamDirection() {
        return streamDirection;
    }

    public static CmdCode codeOf(byte code) {
        for (CmdCode type : CmdCode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return null;
    }
}
