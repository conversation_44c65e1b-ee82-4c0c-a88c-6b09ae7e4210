package com.cdz360.iot.meter.yunji.qgdw376dot1;

import java.util.function.IntUnaryOperator;

public class AddOneIntUnaryOperator implements IntUnaryOperator {

    private static final AddOneIntUnaryOperator INSTANCE = new AddOneIntUnaryOperator();

    @Override
    public int applyAsInt(int operand) {
        return (operand + 1) % 256;
    }

    public static AddOneIntUnaryOperator getInstance() {
        return INSTANCE;
    }
}
