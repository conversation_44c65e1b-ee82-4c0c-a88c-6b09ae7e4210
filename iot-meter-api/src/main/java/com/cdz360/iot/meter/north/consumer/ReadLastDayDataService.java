package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.app.LastDayMeterData;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.north.job.JobTask;
import com.cdz360.iot.meter.north.job.ReadLastDayDataJob;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 抄上一天读数
 */
@Slf4j
@Service
@Getter
public class ReadLastDayDataService extends AbstractSubMeterObserver implements MeterSubObserver {

    @Autowired
    private MeterChannelRepository meterRepository;


    @Autowired
    private MeterObserverService meterObserverProxy;

    //private Map<String, ReadLastDayDataJob> jobMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        this.meterObserverProxy.addSubObserver(this, List.of(P645DataType.LAST_DAY_POWER_COMBINE,
                P645DataType.LAST_DAY_POWER_POSITIVE, P645DataType.LAST_DAY_POWER_NEGATIVE,
                P645DataType.LAST_DAY_IDLE_POWER_1, P645DataType.LAST_DAY_IDLE_POWER_2));
    }

    public Mono<LastDayMeterData> process(String deviceNo) {
        ReadLastDayDataJob job = new ReadLastDayDataJob(this.meterRepository, deviceNo);
        job.init();
        super.addJob(deviceNo, job);

        Mono<LastDayMeterData> mono =  Mono.create(sink -> {
            JobTask task = job.nextTask();
            job.setMonoSink(sink);
            job.process(task);

        });
        return mono;
    }


//    @Override
//    public void notifyEvent(P645BaseMsg msg) {
//        log.info("traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//        ReadLastDayDataJob job = jobMap.get(msg.getDeviceNo());
//        if (job == null) {
//            log.warn("job not exist. traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//            return;
//        }
//        job.fillData(msg);
//        synchronized (job) {
//            job.notifyAll();
//        }
//    }


}
