package com.cdz360.iot.meter.yunji;

import cn.hutool.core.util.HexUtil;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.meter.yunji.qgdw376dot1.QGDW376Dot1Encoder;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Connection;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@RestController
@RequestMapping("/yunji")
public class YJRest {

    @Autowired
    private YJChannelConnectionPool pool;

    // 每5分钟执行一次
    @Scheduled(cron = "0 */5 * * * ?")
    public void performTask() throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty()) {
            return;
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            if (connection.getNo() != null) {
                Channel channel = connection.getChannel();
                int pn = 1;
                int pseq = connection.getAndUpdatePSEQ();
                String address = connection.getAddress();
                String code25 = QGDW376Dot1Encoder.get0CByFnPn(25, pn, pseq, address);
                String code129 = QGDW376Dot1Encoder.get0CByFnPn(129, pn, pseq, address);
                String code130 = QGDW376Dot1Encoder.get0CByFnPn(130, pn, pseq, address);
                String code131 = QGDW376Dot1Encoder.get0CByFnPn(131, pn, pseq, address);
                String code132 = QGDW376Dot1Encoder.get0CByFnPn(132, pn, pseq, address);
                List<String> codeList = List.of(code25, code129,code130, code131, code132);
                codeList.parallelStream().forEach(e -> {
                    log.debug("qyc测试打印: 发送指令: {}", e);
                    channel.writeAndFlush(HexUtil.decodeHex(e));
                });
            }
        }
    }


    @PostMapping("/searchCfgF10")
    public String searchCfgF10() throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty()) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            if (connection.getAddress() != null) {
                Channel channel = connection.getChannel();
                int pn = 1;
                int pseq = connection.getAndUpdatePSEQ();
                String address = connection.getAddress();
                String cmd = QGDW376Dot1Encoder.getUserConfigCmdBy0AF10(pseq, address);
                log.debug("qyc测试打印: 发送指令 查找用户电能表配置数据 10: {}", cmd);
                channel.writeAndFlush(HexUtil.decodeHex(cmd));
                Thread.sleep(2000);
            }
        }
        return "success";
    }


    @PostMapping("/searchCfgFn")
    public String searchCfgFn(@RequestBody List<Integer> fnList) throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty() || CollectionUtils.isEmpty(fnList)) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            if (connection.getAddress() != null) {
                Channel channel = connection.getChannel();
                int pn = 1;
                String address = connection.getAddress();
                fnList.forEach(fn -> {
                    int pseq = connection.getAndUpdatePSEQ();
                    String cmd = QGDW376Dot1Encoder.getUserConfigCmdBy0AFn(fn, pseq, address);
                    log.debug("qyc测试打印: 发送指令 查找用户电能表配置数据 {}: {}", fn, cmd);
                    channel.writeAndFlush(HexUtil.decodeHex(cmd));
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        }
        return "success";
    }


    @PostMapping("/searchTerminalCfgFn")
    public String searchTerminalCfgFn(@RequestBody List<Integer> fnList) throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty() || CollectionUtils.isEmpty(fnList)) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            if (connection.getAddress() != null) {
                Channel channel = connection.getChannel();
                int pn = 1;
                String address = connection.getAddress();
                fnList.forEach(fn -> {
                    int pseq = connection.getAndUpdatePSEQ();
                    String cmd = QGDW376Dot1Encoder.getUserConfigCmdBy09Fn(fn, pseq, address);
                    log.debug("qyc测试打印: 发送指令 请求终端配置及信息 {}: {}", fn, cmd);
                    channel.writeAndFlush(HexUtil.decodeHex(cmd));
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        }
        return "success";
    }


    /**
     * 做完设置ip后，发送F1指令，硬件初始化
     *
     * @param fn  fn
     * @param dno 集中器号
     */
    @PostMapping("/resetTerminalCfgFn")
    public String resetTerminalCfgFn(@RequestParam("fn") Integer fn, @RequestParam("dno") String dno) throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty() || fn == null || StringUtils.isEmpty(dno)) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            String connectDno = connection.getDno();
            if (connection.getAddress() != null && connectDno != null && connectDno.equals(dno)) {
                int pseq = connection.getAndUpdatePSEQ();
                String cmd = QGDW376Dot1Encoder.getResetCmdByFn(fn, pseq, connection.getAddress());
                log.debug("qyc测试打印: 发送指令 重启集中器 {}", cmd);
                connection.getChannel().writeAndFlush(HexUtil.decodeHex(cmd));
                break;
            }
        }
        return "success";
    }


    @PostMapping("/getLastMonthData")
    public String getLastMonthData() throws InterruptedException {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty()) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            if (connection.getAddress() != null) {
                Channel channel = connection.getChannel();
                int pn = 1;
                LocalDate currentDate = LocalDate.now();

                LocalDate lastMonthDate = currentDate.minusMonths(1);

                LocalDate towMonthDate = lastMonthDate.minusMonths(1);

                String lastMonthTimeFlag = QGDW376Dot1Encoder.getMonthTimeFlag(lastMonthDate);
                String towMonthDateTimeFlag = QGDW376Dot1Encoder.getMonthTimeFlag(towMonthDate);
                int pseq = connection.getAndUpdatePSEQ();
                String address = connection.getAddress();

                String code177 = QGDW376Dot1Encoder.get0DByFnPn(177, pn, pseq, address, lastMonthTimeFlag);
                log.debug("qyc测试打印: 发送指令 上个月 月冻结正向有功电能示值 177: {}", code177);
                channel.writeAndFlush(HexUtil.decodeHex(code177));
                Thread.sleep(2000);
                pseq = connection.getAndUpdatePSEQ();
                code177 = QGDW376Dot1Encoder.get0DByFnPn(177, pn, pseq, address, towMonthDateTimeFlag);
                log.debug("qyc测试打印: 发送指令 上上个月 月冻结正向有功电能示值 177: {}", code177);
                channel.writeAndFlush(HexUtil.decodeHex(code177));
                Thread.sleep(2000);


                pseq = connection.getAndUpdatePSEQ();
                String code178 = QGDW376Dot1Encoder.get0DByFnPn(178, pn, pseq, address, lastMonthTimeFlag);
                log.debug("qyc测试打印: 发送指令 上个月 月冻结正向无功电能示值 178: {}", code178);
                channel.writeAndFlush(HexUtil.decodeHex(code178));
                Thread.sleep(2000);
                pseq = connection.getAndUpdatePSEQ();
                code178 = QGDW376Dot1Encoder.get0DByFnPn(178, pn, pseq, address, towMonthDateTimeFlag);
                log.debug("qyc测试打印: 发送指令 上上个月 月冻结正向无功电能示值 178: {}", code178);
                channel.writeAndFlush(HexUtil.decodeHex(code178));
                Thread.sleep(2000);


                pseq = connection.getAndUpdatePSEQ();
                String code179 = QGDW376Dot1Encoder.get0DByFnPn(179, pn, pseq, address, lastMonthTimeFlag);
                log.debug("qyc测试打印: 发送指令 上个月 月冻结反向有功电能示值 179: {}", code179);
                channel.writeAndFlush(HexUtil.decodeHex(code179));
                Thread.sleep(2000);
                pseq = connection.getAndUpdatePSEQ();
                code179 = QGDW376Dot1Encoder.get0DByFnPn(179, pn, pseq, address, towMonthDateTimeFlag);
                log.debug("qyc测试打印: 发送指令 上上个月 月冻结反向有功电能示值 179: {}", code179);
                channel.writeAndFlush(HexUtil.decodeHex(code179));
                Thread.sleep(2000);

                pseq = connection.getAndUpdatePSEQ();
                String code180 = QGDW376Dot1Encoder.get0DByFnPn(180, pn, pseq, address, lastMonthTimeFlag);
                log.debug("qyc测试打印: 发送指令 上个月 月冻结反向无功电能示值 24: {}", code180);
                channel.writeAndFlush(HexUtil.decodeHex(code180));
                Thread.sleep(2000);
                pseq = connection.getAndUpdatePSEQ();
                code180 = QGDW376Dot1Encoder.get0DByFnPn(180, pn, pseq, address, towMonthDateTimeFlag);
                log.debug("qyc测试打印: 发送指令 上上个月 月冻结反向无功电能示值 24: {}", code180);
                channel.writeAndFlush(HexUtil.decodeHex(code180));
                Thread.sleep(2000);
            }
        }
        return "success";
    }


    /**
     * @param dno     集中器编号
     * @param ipPort1 **************:8888
     * @param ipPort2 **************:8888
     * @param apn     apn(16进制), 应都是 636d6e65740000000000000000000000
     */
    @PostMapping("/modifyIpAndPortByDno")
    public String modifyIpAndPortByDno(@RequestParam("dno") String dno, @RequestParam("ipPort1") String ipPort1,
                                       @RequestParam("ipPort2") String ipPort2, @RequestParam("apn") String apn) {
        ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = pool.getQGDW376Dot1Connection();
        if (connectionCache.isEmpty() || StringUtils.isEmpty(dno) || StringUtils.isEmpty(ipPort1)
                || StringUtils.isEmpty(ipPort2) || StringUtils.isEmpty(apn)) {
            return "empty";
        }
        for (QGDW376Dot1Connection connection : connectionCache.values()) {
            String connectDno = connection.getDno();
            if (connection.getAddress() != null && connectDno != null && connectDno.equals(dno)) {
                int pseq = connection.getAndUpdatePSEQ();
                String cmd = QGDW376Dot1Encoder.getSettingIpAndPortCmd(ipPort1, ipPort2, apn, pseq, connection.getAddress());
                log.debug("qyc测试打印: 发送指令 设置集中器上报ip、端口和apn {}", cmd);
                connection.getChannel().writeAndFlush(HexUtil.decodeHex(cmd));
                break;
            }
        }
        return "success";
    }
}
