package com.cdz360.iot.meter.model.type;

import lombok.Getter;

/**
 * 数据标识
 */
@Getter
public enum P645DataType {

    // 当前组合有功总电能
    POWER_COMBINE(new byte[]{0x00, (byte) 0xFF, 0x00, 0x00}),


    // 当前正向有功总电能
    POWER_POSITIVE(new byte[]{0x00, (byte) 0xFF, 0x01, 0x00}),

    // 当前反向有功总电能
    POWER_NEGATIVE(new byte[]{0x00, (byte) 0xFF, 0x02, 0x00}),


    // 当前组合无功 1 总电能
    IDLE_POWER_1(new byte[]{0x00, (byte) 0xFF, 0x03, 0x00}),

    // 当前组合无功 2 总电能
    IDLE_POWER_2(new byte[]{0x00, (byte) 0xFF, 0x04, 0x00}),

    // 电压
    CUR_VOLTAGE(new byte[]{0x00, (byte) 0xFF, 0x01, 0x02}),

    // 电流
    CUR_CURRENT(new byte[]{0x00, (byte) 0xFF, 0x02, 0x02}),

    // 瞬时有功功率
    CUR_POWER(new byte[]{0x00, (byte) 0xFF, 0x03, 0x02}),

    // 瞬时无功功率
    CUR_IDLE(new byte[]{0x00, (byte) 0xFF, 0x04, 0x02}),

    // (上 1 结算日)组合有功总电能
    LAST_DAY_POWER_COMBINE(new byte[]{0x01, (byte) 0xFF, 0x00, 0x00}),

    // (上 1 结算日)正向有功总电能
    LAST_DAY_POWER_POSITIVE(new byte[]{0x01, (byte) 0xFF, 0x01, 0x00}),

    // (上 1 结算日)反向有功总电能
    LAST_DAY_POWER_NEGATIVE(new byte[]{0x01, (byte) 0xFF, 0x02, 0x00}),


    // (上 1 结算日)组合无功 1 总电能
    LAST_DAY_IDLE_POWER_1(new byte[]{0x01, (byte) 0xFF, 0x03, 0x00}),

    // (上 1 结算日)组合无功 2 总电能
    LAST_DAY_IDLE_POWER_2(new byte[]{0x01, (byte) 0xFF, 0x04, 0x00}),

    // 电流互感器变比
    TRANSFORMER_CUR_RATE(new byte[]{0x06, (byte) 0x03, 0x00, 0x04}),
    // 电压互感器变比
    TRANSFORMER_VOL_RATE(new byte[]{0x07, (byte) 0x03, 0x00, 0x04}),

    ;

    final byte[] dataType;

    P645DataType(byte[] dataType) {
        this.dataType = dataType;
    }

}
