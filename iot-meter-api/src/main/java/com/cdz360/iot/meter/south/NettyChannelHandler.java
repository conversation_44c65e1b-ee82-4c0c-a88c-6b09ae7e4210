package com.cdz360.iot.meter.south;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.south.handler.MeterHandlerFacade;
import com.cdz360.iot.meter.utils.ByteUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * netty tcp 链路处理器
 */
@Component
@Slf4j
@ChannelHandler.Sharable
public class NettyChannelHandler extends ChannelInboundHandlerAdapter {
    //    private final Logger logger = LoggerFactory.getLogger(com.cdz360.iot.gw.server.NettyChannelHandler.class);
//    @Autowired
//    private EvseStatusReportService evseStatusReportService;
    @Autowired
    private NettyChannelRepository nettyChannelRepository;
    @Autowired
    private MeterHandlerFacade iotEvseFacade;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        Assert.notNull(this.nettyChannelRepository, "[Assertion failed] - NettyChannelRepository is required; it must not be null");

        ctx.fireChannelActive();
        if (log.isDebugEnabled()) {
            log.debug(ctx.channel().remoteAddress() + "");
        }
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("电表TCP已连接。channelKey: {}", channelKey);
        nettyChannelRepository.put(channelKey, ctx.channel());

        //ctx.writeAndFlush("Your channel key is " + channelKey + "\r\n");

        if (log.isDebugEnabled()) {
            log.debug("Binded Channel Count is {}", this.nettyChannelRepository.size());
        }

    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        // TODO: IotDecoder 实现后, 可直接转成类型 IotEvseMessage 或其子类
        // TODO: 需要由IotDecoder解包
        log.debug("[{}] channelRead start", ctx.channel().remoteAddress().toString());
        if (null == msg) {
            return;
        }
        IotBaseMsg iotMsg = (IotBaseMsg) msg;  // TODO: 需要由IotDecoder解包
//
//        iotMsg.setReadTimestamp(System.nanoTime());

        this.iotEvseFacade.process(ctx.channel(), iotMsg)
                .doOnError(ex -> log.warn(ex.getMessage(), ex))
                .doOnNext(buf -> sendReply(ctx, buf, iotMsg))
                .subscribe();


        long endTime = System.nanoTime();
        long indicatorsBySecond = 1000000 * 1000;// 1秒
        long errorIndicators = 1000000 * 1000 * 5;// 5秒
//        if(endTime - iotMsg.getReadTimestamp() > errorIndicators) {
//            log.error("[{}] channelRead delay xxx : {} ms, flowId:{}", iotMsg.getChannelKey(),
//                    (endTime - iotMsg.getReadTimestamp()) / 1000000L, iotMsg.getFlowId());
//
//        }
//        else if (endTime - iotMsg.getReadTimestamp() > indicatorsBySecond) {
//            log.warn("[{}] channelRead delay xxx : {} ms, flowId:{}", iotMsg.getChannelKey(),
//                    (endTime - iotMsg.getReadTimestamp()) / 1000000L, iotMsg.getFlowId());
//        }
//        log.debug("[{}] channelRead end. flowId: {}", ctx.channel().remoteAddress().toString(), iotMsg.getFlowId());
    }


    private void sendReply(ChannelHandlerContext ctx, byte[] buf, IotBaseMsg iotMsg) {
        log.info("网关下发信息到电表。traceId: {}, channelKey: {},reply msg:{}.",
                iotMsg.getTraceId(), iotMsg.getChannelKey(),
                ByteUtil.bytesToHex(buf));

        if (buf != null && buf.length > 0) {
            asyncRequest(iotMsg.getTraceId(), iotMsg.getChannelKey(),
                    buf);
        }

//        long endTime = System.nanoTime();
//        long indicatorsBySecond = 1000000 * 2000;// 2秒
//        long errorIndicators = 1000000 * 5000;// 5秒
//        if(iotMsg.getReadTimestamp() - iotMsg.getDecodeTimestamp() > errorIndicators) {
//            log.error("decode->read delay xxx : {} ms, flowId:{}, msg: {}",
//                    (iotMsg.getReadTimestamp() - iotMsg.getDecodeTimestamp()) / 1000000L, iotMsg.getFlowId(), ByteUtil.bytesToHex(buf));
//        }
//        else if (iotMsg.getReadTimestamp() - iotMsg.getDecodeTimestamp() > indicatorsBySecond) {    // 超过1秒的
//            log.warn("decode->read delay xxx : {} ms, flowId:{}, msg: {}",
//                    (iotMsg.getReadTimestamp() - iotMsg.getDecodeTimestamp()) / 1000000L, iotMsg.getFlowId(), ByteUtil.bytesToHex(buf));
//        }
//
//        if(endTime - iotMsg.getReadTimestamp() > errorIndicators) {
//            log.error("业务处理 delay xxx : {} ms, flowId:{}, msg: {}",
//                    (endTime - iotMsg.getReadTimestamp()) / 1000000L, iotMsg.getFlowId(), ByteUtil.bytesToHex(buf));
//        }
//        else if (endTime - iotMsg.getReadTimestamp() > indicatorsBySecond) {
//            log.warn("业务处理 delay xxx : {} ms, flowId:{}, msg: {}",
//                    (endTime - iotMsg.getReadTimestamp()) / 1000000L, iotMsg.getFlowId(), ByteUtil.bytesToHex(buf));
//        }

        log.debug("[{}] reply to meter done. traceId: {}", iotMsg.getChannelKey(), iotMsg.getTraceId());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Assert.notNull(ctx, "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

        String channelKey = ctx.channel().remoteAddress().toString();

        log.error("电表异常断开，上报电表离线。channelKey: {}", channelKey, cause);

        //evseStatusReportService.reportWhenException(channelKey);

        ctx.channel().close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Assert.notNull(this.nettyChannelRepository, "[Assertion failed] - NettyChannelRepository is required; it must not be null");
        Assert.notNull(ctx, "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

        String channelKey = ctx.channel().remoteAddress().toString();

        log.info("电表TCP连接已断开。channelKey: {}", channelKey);

        //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
        //evseStatusReportService.reportWhenTimeout(channelKey);

        this.nettyChannelRepository.remove(channelKey);
        if (log.isDebugEnabled()) {
            log.debug("Binded Channel Count is " + this.nettyChannelRepository.size());
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            if (((IdleStateEvent) evt).state().equals(IdleState.READER_IDLE)) {

                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                String channelKey = ctx.channel().remoteAddress().toString();
                log.info("电表长时间未写入数据，已将其断开。channelKey: {}", channelKey);

                // 在调用ctx.channel().close();后，netty内部的机制会触发channelInactive()事件，在channelInactive里处理状态上报的逻辑。
                // evseStatusReportService.reportWhenIdle(channelKey);
                // this.nettyChannelRepository.remove(channelKey);

                ctx.channel().close();
            }
        }

        super.userEventTriggered(ctx, evt);
    }


    /**
     * 下发异步的请求指令给电表
     * <p>
     * 网关所有需要给电表发信息的统一调用这里。下发的情况有两种：
     * 1. 电表发送信息后，如果网关需要下发信息（实现了Replyable接口）
     * 2. 平台下发指令给电表
     */
    public void asyncRequest(String flowId, String key, byte[] replyBytes) {
        //logger.info("网关下发信息到桩。flowId: {}, replyBytes: {}.", flowId, ByteUtil.bytesToHex(replyBytes));
        log.info("网关下发信息到电表。flowId: {}, channelKey = {}", flowId, key);

        // 保存源数据
        //SaveTransDataService.evseImport(flowId, evseNo, ByteUtil.bytesToHex(replyBytes));
        Channel channel = nettyChannelRepository.get(key);

        if (channel == null) {
            log.error("netty channel已经关闭，无法向电表发送信息。flowId: {}", flowId);
            return;
        }

        channel.writeAndFlush(replyBytes);
    }
}