package com.cdz360.iot.meter.utils;

import java.util.Calendar;
import java.util.Date;

/**
 * @Classname DateUtil
 * @Description
 * @Date 9/25/2020 2:04 PM
 * @Created by Rafael
 */
public class DateUtil {

    /**
     * 获取当前小时的0秒时刻
     * @param date
     * @return
     */
    public static Date getThisHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        return calendar.getTime();
    }

    /**
     * 获取入参天的0秒时刻
     * @param date
     * @return
     */
    public static Date getThisDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar.getTime();
    }

    public static Date getPrevDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    public static Date getOffsetDate(Date date, int field, int offset) {
        if(offset == 0) {
            return date;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, offset);
        return calendar.getTime();
    }
}