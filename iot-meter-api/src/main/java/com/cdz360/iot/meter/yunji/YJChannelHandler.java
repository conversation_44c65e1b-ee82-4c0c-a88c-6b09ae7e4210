package com.cdz360.iot.meter.yunji;

import cn.hutool.core.util.HexUtil;
import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.utils.ByteUtil;
import com.cdz360.iot.meter.yunji.qgdw376dot1.QGDW376Dot1Decoder;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Connection;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1RawBody;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * netty tcp 链路处理器
 */
@Component("yJChannelHandler")
@Slf4j
@ChannelHandler.Sharable
public class YJChannelHandler extends ChannelInboundHandlerAdapter {

    @Autowired
    private YJChannelConnectionPool pool;

    @Autowired
    private QGDW376Dot1Decoder decoder;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        ctx.fireChannelActive();
        if (log.isDebugEnabled()) {
            log.debug(ctx.channel().remoteAddress() + "");
        }
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("电表TCP已连接。channelKey: {}", channelKey);
        if (pool.getConnection(channelKey) == null) {
            QGDW376Dot1Connection connection = new QGDW376Dot1Connection();
            connection.setChannel(ctx.channel());
            pool.addConnection(channelKey, connection);
        }
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        String channelKey = ctx.channel().remoteAddress().toString();
        if (null == msg) {
            return;
        }
        QGDW376Dot1RawBody raw = (QGDW376Dot1RawBody) msg;
        log.debug("traceId: {}, [{}] channelRead start", raw.getTraceId(), channelKey);

        log.debug("traceId: {}, read msg: {}", raw.getTraceId(), msg);
        try {
            decoder.decode(raw, channelKey);
        } catch (Exception e) {
            log.error("traceId: {}, 捕获异常: {}", raw.getTraceId(), e.getMessage(), e);
        }
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Assert.notNull(ctx, "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

        String channelKey = ctx.channel().remoteAddress().toString();

        log.error("电表异常断开，上报电表离线。channelKey: {}", channelKey, cause);

        pool.removeConnection(channelKey);
        ctx.channel().close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Assert.notNull(this.pool, "[Assertion failed] - NettyChannelRepository is required; it must not be null");
        Assert.notNull(ctx, "[Assertion failed] - ChannelHandlerContext is required; it must not be null");
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("电表TCP连接已断开。channelKey: {}", channelKey);
        pool.removeConnection(channelKey);
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            if (((IdleStateEvent) evt).state().equals(IdleState.READER_IDLE)) {
                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                String channelKey = ctx.channel().remoteAddress().toString();
                log.info("电表长时间未写入数据，已将其断开。channelKey: {}", channelKey);
                ctx.channel().close();
            }
        }
        super.userEventTriggered(ctx, evt);
    }
}