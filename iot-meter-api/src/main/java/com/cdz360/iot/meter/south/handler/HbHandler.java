package com.cdz360.iot.meter.south.handler;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.north.biz.MeterDataService;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;

@Slf4j
@Service
public class HbHandler implements IotHandler {


    private static final CmdCode CMD_CODE = CmdCode.HB_REQ;

    @Autowired
    private MeterHandlerFacade iotEvseFacade;

    @Autowired
    private MeterDataService meterDataService;

    @Autowired
    private MeterChannelRepository channelRepository;

    @PostConstruct
    public void init() {
        this.iotEvseFacade.addProducer(CMD_CODE, this);
    }

    @Override
    public Mono<byte[]> process(IotBaseMsg msg) {
        log.info(">> 电表心跳 traceId = {}， deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());

        meterDataService.hbMeter(msg.getDeviceNo(), msg.getTraceId());

        channelRepository.softPut(msg.getDeviceNo(), msg.getChannel());


        // log.info("iot msg = {}", ByteUtil.bytesToHex(msg.toBytes()));
        IotBaseMsg resMsg = msg.buildRes(CmdCode.HB_RES, null);


        return Mono.just(resMsg.toBytes());
    }
}
