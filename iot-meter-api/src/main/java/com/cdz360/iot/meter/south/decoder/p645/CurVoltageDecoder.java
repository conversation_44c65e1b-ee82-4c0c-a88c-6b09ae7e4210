package com.cdz360.iot.meter.south.decoder.p645;

import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645CurPowerResponse;
import com.cdz360.iot.meter.model.iot.p645.P645CurrentResponse;
import com.cdz360.iot.meter.model.iot.p645.P645VoltageResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Slf4j
@Service
public class CurVoltageDecoder extends P645Decoder {

    @Autowired
    private P645DecoderFactory p645DecoderFactory;

    @PostConstruct
    public void init() {
        p645DecoderFactory.addDecoder(P645DataType.CUR_VOLTAGE, this);
        p645DecoderFactory.addDecoder(P645DataType.CUR_CURRENT, this);
        p645DecoderFactory.addDecoder(P645DataType.CUR_POWER, this);
        p645DecoderFactory.addDecoder(P645DataType.CUR_IDLE, this);

    }

    @Override
    public P645BaseMsg parse(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        if (baseMsg.getDataType() == P645DataType.CUR_VOLTAGE) {
            return this.parseVoltage(baseMsg);
        } else if (baseMsg.getDataType() == P645DataType.CUR_CURRENT) {
            return this.parseCurrent(baseMsg);
        } else if (baseMsg.getDataType() == P645DataType.CUR_POWER
                || baseMsg.getDataType() == P645DataType.CUR_IDLE) {
            return this.parsePower(baseMsg);
        } else {
            log.warn("不支持的数据标识 msg = {}", baseMsg);
            return null;
        }
    }

    public P645VoltageResponse parseVoltage(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        P645VoltageResponse p645Msg = new P645VoltageResponse(baseMsg);
        byte[] data = p645Msg.getData();


        int idx = 4;
        p645Msg.setVoltageA(this.parseDecimal21Value(data, idx));
        idx += 2;

        p645Msg.setVoltageB(this.parseDecimal21Value(data, idx));
        idx += 2;

        p645Msg.setVoltageC(this.parseDecimal21Value(data, idx));

        return p645Msg;
    }

    public P645CurrentResponse parseCurrent(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        P645CurrentResponse p645Msg = new P645CurrentResponse(baseMsg);
        byte[] data = p645Msg.getData();


        int idx = 4;
        p645Msg.setCurrentA(this.parseDecimal33Value(data, idx));
        idx += 3;

        p645Msg.setCurrentB(this.parseDecimal33Value(data, idx));
        idx += 3;

        p645Msg.setCurrentC(this.parseDecimal33Value(data, idx));

        return p645Msg;
    }

    public P645CurPowerResponse parsePower(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        P645CurPowerResponse p645Msg = new P645CurPowerResponse(baseMsg);
        byte[] data = p645Msg.getData();


        int idx = 4;
        p645Msg.setPowerTotal(this.parseDecimal34ValueWithSign(data, idx));
        idx += 3;

        p645Msg.setPowerA(this.parseDecimal34ValueWithSign(data, idx));
        idx += 3;

        p645Msg.setPowerB(this.parseDecimal34ValueWithSign(data, idx));
        idx += 3;

        p645Msg.setPowerC(this.parseDecimal34ValueWithSign(data, idx));

        return p645Msg;
    }
}
