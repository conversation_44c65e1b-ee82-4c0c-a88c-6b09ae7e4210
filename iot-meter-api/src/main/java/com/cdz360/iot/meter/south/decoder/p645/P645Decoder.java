package com.cdz360.iot.meter.south.decoder.p645;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.utils.ByteUtil;

import java.math.BigDecimal;

public abstract class P645Decoder {

    public abstract P645BaseMsg parse(P645BaseMsg baseMsg);

    /**
     * 4字节，1位小数
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal41Value(byte[] bufIn, int idx) {
        int a = ByteUtil.byte2BcdInt(bufIn[idx + 3]);
        int b = ByteUtil.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(a * 10000 + b * 100 + c).add(DecimalUtils.divide100(d));
    }


    /**
     * 2字节，1位小数
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal21Value(byte[] bufIn, int idx) {
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(c * 10 + d /10).add(BigDecimal.valueOf(d%10).divide(BigDecimal.TEN));
    }


    /**
     * 3字节，3位小数
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal33Value(byte[] bufIn, int idx) {
        int b = ByteUtil.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(b * 10 + c /10).add(BigDecimal.valueOf(c%10).divide(BigDecimal.TEN))
                .add(BigDecimal.valueOf(d).divide(BigDecimal.valueOf(1000)));
    }

    public BigDecimal parseDecimal34Value(byte[] bufIn, int idx) {
        int b = ByteUtil.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(b ).add(DecimalUtils.divide100(c))
                .add(BigDecimal.valueOf(d).divide(BigDecimal.valueOf(10000)));
    }

    /**
     * 最高位表示方向，0正，1负
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal34ValueWithSign(byte[] bufIn, int idx) {
        int sign = this.getSign(bufIn[idx + 2]);
        int b = ByteUtil.byte2BcdIntSkipSign(bufIn[idx + 2]);
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        if(sign < 0) {
            return BigDecimal.valueOf(-b).subtract(DecimalUtils.divide100(c))
                    .subtract(BigDecimal.valueOf(d).divide(BigDecimal.valueOf(10000)));
        }
        return BigDecimal.valueOf(b).add(DecimalUtils.divide100(c))
                .add(BigDecimal.valueOf(d).divide(BigDecimal.valueOf(10000)));
    }

    private int getSign(byte in) {
        if((0x80 & in) > 0) {
            // 符号位为负
            return -1;
        } else {
            return 1;
        }
    }

    /**
     * 3字节，0位小数
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal30Value(byte[] bufIn, int idx) {
        int b = ByteUtil.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtil.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtil.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(b * 100 + c * 10 + d);
    }
}
