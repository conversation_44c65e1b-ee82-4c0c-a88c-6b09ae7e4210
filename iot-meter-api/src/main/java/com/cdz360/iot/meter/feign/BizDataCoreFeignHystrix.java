package com.cdz360.iot.meter.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.utils.RestUtils;
import java.time.LocalDateTime;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class BizDataCoreFeignHystrix implements FallbackFactory<BizDataCoreFeignClient> {

    @Override
    public BizDataCoreFeignClient apply(Throwable throwable) {
        return new BizDataCoreFeignClient() {

            @Override
            public Mono<BaseResponse> saveMeterRtAndHisData(String siteId, String gwno, String dno,
                String ts, MeterRtData meterRtData) {
                log.error(
                    "【服务熔断】 保存电表上传实时和历史的数据. Service = {}, api = saveMeterRtAndHisData. siteId = {}, gwno = {}, dno = {}, ts = {}, meterRtData = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, siteId, gwno, dno, ts, meterRtData);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, BizDataCoreFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super BizDataCoreFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
