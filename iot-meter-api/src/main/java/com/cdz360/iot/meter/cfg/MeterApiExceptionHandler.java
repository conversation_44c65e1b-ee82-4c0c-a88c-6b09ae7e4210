package com.cdz360.iot.meter.cfg;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.utils.LogHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@ControllerAdvice(annotations = RestController.class)
public class MeterApiExceptionHandler {


    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        LogHelper.logException(log, ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }


    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex) {
        log.error(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        return result;
    }


}
