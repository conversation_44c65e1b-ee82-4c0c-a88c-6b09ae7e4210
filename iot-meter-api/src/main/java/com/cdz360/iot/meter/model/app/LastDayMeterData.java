package com.cdz360.iot.meter.model.app;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 上一日电表读数
 */
@Data
@Accessors(chain = true)
public class LastDayMeterData {
    /**
     *  (上 1 结算日)组合有功总电能
     */
    private BigDecimal combineTotal;
    private BigDecimal combineA;
    private BigDecimal combineB;
    private BigDecimal combineC;
    private BigDecimal combineD;

    /**
     * (上 1 结算日)正向有功总电能
     */
    private BigDecimal positiveTotal;
    private BigDecimal positiveA;
    private BigDecimal positiveB;
    private BigDecimal positiveC;
    private BigDecimal positiveD;

    /**
     * (上 1 结算日)反向有功总电能
     */
    private BigDecimal negativeTotal;
    private BigDecimal negativeA;
    private BigDecimal negativeB;
    private BigDecimal negativeC;
    private BigDecimal negativeD;


    /**
     * (上 1 结算日)组合无功 1 总电能
     */
    private BigDecimal positiveIdleTotal;
    private BigDecimal positiveIdleA;
    private BigDecimal positiveIdleB;
    private BigDecimal positiveIdleC;
    private BigDecimal positiveIdleD;

    /**
     * (上 1 结算日)组合无功 2 总电能
     */
    private BigDecimal negativeIdleTotal;
    private BigDecimal negativeIdleA;
    private BigDecimal negativeIdleB;
    private BigDecimal negativeIdleC;
    private BigDecimal negativeIdleD;

}
