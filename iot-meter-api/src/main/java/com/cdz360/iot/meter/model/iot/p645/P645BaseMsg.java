package com.cdz360.iot.meter.model.iot.p645;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.meter.model.type.P645CmdCode;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.utils.ByteUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;

@Slf4j
@Data
public class P645BaseMsg {

    public static final int HEAD_LENGTH = 10;
    public static final int TAIL_LENGTH = 2;

    private String traceId;

    private String channelKey;

    // 12个数字的BCD编码， 6字节
    private String deviceNo;

    // 控制码， 1字节
    private P645CmdCode cmdCode;

    // 数据域长度， 1字节
    private int length;

    private P645DataType dataType;

    // 数据域
    private byte[] data;

    // 校验码，1字节
    private byte cs;

    public byte[] buildData() {
        return this.data;
    }

//    public IotBaseMsg buildRes(CmdCode cmdCodeIn, byte[] dataIn) {
//        IotBaseMsg res = new IotBaseMsg();
//        res.setFlowId(this.flowId);
//        res.setChannelKey(this.channelKey);
//        res.setDeviceNo(this.deviceNo);
//        res.setDeviceType(this.deviceType);
//        res.setProduceTime(this.produceTime);
//
//        res.setCmdCode(cmdCodeIn);
//        if (dataIn == null) {
//            res.setLength(0);
//        } else {
//            res.setLength(dataIn.length);
//            res.setData(dataIn);
//        }
//        res.setCs(this.cs);
//        return res;
//    }


    public byte[] toBytes() {
        int bufSize = HEAD_LENGTH + TAIL_LENGTH;
        byte[] data = this.buildData();
        for (int i = 0; i < data.length; i++) {
            data[i] = (byte) (data[i] + 0x33);
        }
        int dataLength = 0;
        if (data != null) {
            dataLength = data.length;
            bufSize += dataLength;
        }
        ByteArrayOutputStream buf = new ByteArrayOutputStream(bufSize);
        buf.write(0x68);

//        if(is68H) {
//            byte[] bufA = {(byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA};
//            buf.write(bufA, 0, 6);
//            buf.write((byte)0x00);
//            buf.write((byte)0x10);
//            buf.write((byte)0x68);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0x68);
//        }else {

        buf.write(ByteUtil.hexToBytesLittle(this.deviceNo), 0, 6);
        buf.write(0x68);
//        }
        buf.write(this.cmdCode.getCode());

        buf.write((byte) dataLength);
        if (dataLength > 0) {
            buf.write(data, 0, dataLength);
        }
//        if(is68H) {
//            buf.write(0xAD);
//            buf.write(0x16);
//        }
        int cs = 0;
        for (byte b : buf.toByteArray()) {
            cs += (b & 0xFF);
        }
        buf.write((cs % 256) & 0xFF);
        buf.write(0x16);
        return buf.toByteArray();
    }


    public P645DataType parseDataType(byte di0, byte di2, byte di3) {
        log.debug("di0 = {}, di2 = {}", di0, di2);
        if (di0 == 0x00 && di3 == 0x00) {
            if (di2 == 0x00) {
                return P645DataType.POWER_COMBINE;
            } else if (di2 == 0x01) {
                return P645DataType.POWER_POSITIVE;
            } else if (di2 == 0x02) {
                return P645DataType.POWER_NEGATIVE;
            } else if (di2 == 0x03) {
                return P645DataType.IDLE_POWER_1;
            } else if (di2 == 0x04) {
                return P645DataType.IDLE_POWER_2;
            } else {
                return null;
            }
        } else if (di0 == 0x00 && di3 == 0x02) {
            if (di2 == 0x01) {
                return P645DataType.CUR_VOLTAGE;
            } else if (di2 == 0x02) {
                return P645DataType.CUR_CURRENT;
            } else if (di2 == 0x03) {
                return P645DataType.CUR_POWER;
            } else if (di2 == 0x04) {
                return P645DataType.CUR_IDLE;
            } else {
                return null;
            }
        } else if (di0 == 0x01) {
            if (di2 == 0x00) {
                return P645DataType.LAST_DAY_POWER_COMBINE;
            } else if (di2 == 0x01) {
                return P645DataType.LAST_DAY_POWER_POSITIVE;
            } else if (di2 == 0x02) {
                return P645DataType.LAST_DAY_POWER_NEGATIVE;
            } else if (di2 == 0x03) {
                return P645DataType.LAST_DAY_IDLE_POWER_1;
            } else if (di2 == 0x04) {
                return P645DataType.LAST_DAY_IDLE_POWER_2;
            } else {
                return null;
            }
        } else if(di2 == 0x00 && di3 == 0x04) {
            if(di0 == 0x06) {
                return P645DataType.TRANSFORMER_CUR_RATE;
            } else if(di0 == 0x07) {
                return P645DataType.TRANSFORMER_VOL_RATE;
            } else {
                return null;
            }
        }else {
            return null;
        }
    }

    @Override
    public String toString() {
        //return ByteUtil.bytesToHex(this.toBytes());
        return JsonUtils.toJsonString(this);
    }
}
