package com.cdz360.iot.meter.model.type;

public enum P645CmdCode {

    READ_REQ((byte) 0x11, StreamDirection.Upstream),              // [下行]读数据
    READ_RES((byte) 0x91, StreamDirection.Downstream),              // [上行]读数据. 无后续数据帧
    READ_MORE_RES((byte) 0xB1, StreamDirection.Downstream),              // [上行]读数据. 有后续数据帧

    ERROR_RES((byte) 0xD1, StreamDirection.Downstream),             // 异常响应
    ;

    private final byte code;
    private StreamDirection streamDirection;

    P645CmdCode(byte code, StreamDirection streamDirection) {
        this.code = code;
        this.streamDirection = streamDirection;
    }

    public byte getCode() {
        return code;
    }

    public StreamDirection getStreamDirection() {
        return streamDirection;
    }

    public static P645CmdCode codeOf(byte code) {
        for (P645CmdCode type : P645CmdCode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return null;
    }
}
