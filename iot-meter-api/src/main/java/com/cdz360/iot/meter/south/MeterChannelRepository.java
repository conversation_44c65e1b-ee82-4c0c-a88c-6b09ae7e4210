package com.cdz360.iot.meter.south;

import com.cdz360.base.utils.StringUtils;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Component
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_SINGLETON)
public class MeterChannelRepository {

    private ConcurrentMap<String, String> deviceSet = new ConcurrentHashMap<>();
    private ConcurrentMap<String, Channel> channelCache = new ConcurrentHashMap<String, Channel>();

    // 用于id转换: <channelId, deviceNo>
    private ConcurrentMap<String, String> channelIdMap = new ConcurrentHashMap<String, String>();
    // 用于id转换: <deviceNo, channelId>
    private ConcurrentMap<String, String> idChannelMap = new ConcurrentHashMap<String, String>();

    public MeterChannelRepository put(String deviceNo, Channel value) {
        if (value == null) {
            // 兼容旧代码,通过串口接的电表没有channel信息
            deviceSet.put(deviceNo, "");
        } else {
            deviceSet.put(deviceNo, value.id().asShortText());
            channelCache.put(deviceNo, value);
            channelIdMap.put(value.id().asShortText(), deviceNo);
            idChannelMap.put(deviceNo, value.id().asShortText());
        }
        return this;
    }

    public MeterChannelRepository softPut(String deviceNo, Channel value) {
//        log.info("尝试新增链接映射");
        if (deviceSet.containsKey(deviceNo)) {
            log.trace("设备已存在 {}", deviceNo);
        } else {
            log.info("成功新增映射: {}", deviceNo);
            this.put(deviceNo, value);
        }
        return this;
    }

    public Channel get(String deviceNo) {
        Channel channel = channelCache.get(deviceNo);
        if (channel != null && !channel.isWritable()) {
            log.error("deviceNo {}: 连接当前不可用", deviceNo);
            return null;
        }
        return channel;
    }

    public void remove(String deviceNo) {
        log.info("断开tcp链接: {}", deviceNo);
        String channelId = idChannelMap.get(deviceNo);
        this._remove(deviceNo, channelId);
    }

    public void removeByChannelId(Channel channel) {
        if (channel == null) {
            return;
        }
        String channelId = channel.id().asShortText();
        log.info("断开channel tcp链接: {}", channelId);
        String deviceNo = channelIdMap.get(channelId);
        this._remove(deviceNo, channelId);
    }

    private void _remove(String deviceNo, String channelId) {
        if (StringUtils.isNotBlank(deviceNo)) {
            this.idChannelMap.remove(deviceNo);
            this.deviceSet.remove(deviceNo);
        }
        if (StringUtils.isNotBlank(channelId)) {
            this.channelIdMap.remove(channelId);
        }
        this.channelCache.remove(deviceNo);
    }

    public List<String> getDeviceNoList() {
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.addAll(deviceSet.keySet());
        return deviceNoList;
    }

    public int size() {
        return this.channelCache.size();
    }
}
