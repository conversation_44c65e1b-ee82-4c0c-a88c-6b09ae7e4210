package com.cdz360.iot.meter.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class SeqGeneratorUtil {

    //private static final Logger logger = LoggerFactory.getLogger(com.cdz360.iot.gw.util.SeqGeneratorUtil.class);

    private static int workedId;
    private static int dataCenterId;
    private static SnowFlake snowFlake;
    // private static IntegerSnowFlake integerSnowFlake;

    private static final Map<String, AtomicInteger> map;

    static {
        Random rand = new Random(25);

        workedId = rand.nextInt(31);
        dataCenterId = rand.nextInt(31);
        snowFlake = new SnowFlake(dataCenterId, workedId);

        // integerSnowFlake = new IntegerSnowFlake(1, 1);

        map = new ConcurrentHashMap<>();
    }

    // 计数器
    // private static AtomicInteger cnt = new AtomicInteger(0);

    // @Deprecated
    // public static String generate() {
    //     Date currentTime = new Date();
    //     SimpleDateFormat formatter = new SimpleDateFormat("yyMMddHHmmss");
    //     return formatter.format(currentTime) + String.format("%04d", cnt.addAndGet(1));
    // }

    private static long newId() {
        return snowFlake.nextId();
    }

    // public static int newIntegerId() {
    //     return integerSnowFlake.nextId();
    // }

    public static int newIntegerId(String evseId) {
        synchronized (map) {
            if (!map.containsKey(evseId)) {
                // map.put(evseId, new AtomicInteger(integerSnowFlake.nextId()));
                map.put(evseId, new AtomicInteger(0));
            }

            AtomicInteger atomicInteger = map.get(evseId);

            int value = atomicInteger.intValue();

            //快溢出的时候清零
            if (value > Integer.MAX_VALUE - 5) {
                atomicInteger.set(1);
            }

            return atomicInteger.incrementAndGet();
        }
    }

    public static String newStringId() {
        try {
            return String.valueOf(newId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return UUID.randomUUID().toString().replace("-", "");
        }
    }
}
