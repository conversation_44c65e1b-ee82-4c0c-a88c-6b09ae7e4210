package com.cdz360.iot.meter.yunji.qgdw376dot1.model;

import lombok.Data;

/**
 * Q/GDW 376.1—2012 协议
 *
 * @param <T> 实现转字节数组的类
 */
@Data
public class QGDW376Dot1Model<T extends QGDW376Dot1Format> {
    /**
     * 长度L 2个字节
     */
    private short length;

    /**
     * 控制域C 1个字节
     */
    private QGDW376Dot1FieldC controlFieldC;

    /**
     * 地址域A
     */
    private short[] AddressFieldA;

    /**
     * 链路用户数据
     */
    private T data;

    /**
     * 帧校验和是用户数据区所有字节的八位位组算术和，不考虑溢出位。用户数据区包括控制域、地址
     * 域、链路用户数据（应用层）三部分。
     */
    private byte checkSum;
}
