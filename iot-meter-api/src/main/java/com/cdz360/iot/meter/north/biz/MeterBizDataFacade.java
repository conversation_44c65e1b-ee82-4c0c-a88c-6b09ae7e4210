package com.cdz360.iot.meter.north.biz;

import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.MeterRecordRoDs;
import com.cdz360.iot.ds.rw.MeterRecordRwDs;
import com.cdz360.iot.meter.model.app.CurMeterRealData;
import com.cdz360.iot.meter.model.app.CurPowerDataEx;
import com.cdz360.iot.meter.utils.DateUtil;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.po.MeterRecordPo;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * @Classname MeterBizDataFacade
 * @Description
 * @Date 9/24/2020 11:05 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class MeterBizDataFacade {

    @Autowired
    private MeterBizService meterBizService;

    @Autowired
    private MeterDataService meterDataService;

    @Autowired
    private MeterRecordRwDs meterRecordRwDs;

    @Autowired
    private MeterRecordRoDs meterRecordRoDs;

    public Mono<CurPowerDataEx> getMeterReading(final String meterNo) {

        IotAssert.isTrue(StringUtils.isNotBlank(meterNo), "请传入电表编号");

        MeterPo meterByNo = meterDataService.getMeterByNo(meterNo);

        IotAssert.isNotNull(meterByNo, "电表不存在，请刷新后在尝试");

//        CurPowerDataEx meterData = null;
        if (NetType.S485 == meterByNo.getNet()) {
            // 使用串口连接的电表不校验是否在线
        } else {

            IotAssert.isTrue(MeterStatusType.ONLINE.equals(meterByNo.getStatus()), "电表当前不在线，无法获取电表读数");
        }

//        Mono<List<String>> meterList = meterBizService.getMeterList();
        return meterBizService.getMeterReading(meterNo);
//        return meterList.flatMapMany(e -> Flux.fromIterable(e).filter(meterNo::equals)
//                .collectList()
//                .flatMap(ie -> meterBizService.getMeterReading(ie)));

//        Flux<CurPowerData> x = Flux.empty();
//        meterList.doOnNext(e -> {
//            e.stream().forEach(ie -> {
//                x.mergeWith(meterBizService.getVoltage(ie));
//            });
//        });
//        return x;

//        Flux<List<String>> listFlux = Flux.mergeSequential(meterList);
//
//        return meterList.flatMapMany(Flux::fromIterable).map(e -> meterBizService.getVoltage(e));

//        meterList.doOnNext(e -> {
//            Flux.fromIterable(e).flatMap(ie -> meterBizService.getVoltage(ie)).collectList();
//        })
//
//        meterList.subscribe(e -> {
//            e.stream().filter( ie -> meterNo.equals(ie)).forEach(ie -> {
//                meterBizService.getVoltage(meterNo).subscribe(iie -> {
//                    log.info("{}", iie);
//                });
//            });
//        });
    }

    public Flux<Boolean> getCommodity() {
//        Flux<CurMeterData> curMeterDataFlux = meterBizService.getMeterList().flatMapMany(e -> Flux.fromIterable(e)
//                .flatMap(ie -> meterBizService.getCurMeter(ie)));


//        meterBizService.getMeterList().flatMapMany(e -> Flux.fromIterable(e)
//                .flatMap(ie -> {
//                    MeterPo meterByNo = meterDataService.getMeterByNo(ie);
//
//                }));

        final Date ThisHour = DateUtil.getThisHour(new Date());

        return meterBizService.getMeterList().flatMapMany(Flux::fromIterable)
                .flatMap(i -> {

                    MeterPo meterByNo = meterDataService.getMeterByNo(i);
                    MeterRecordPo reading = meterRecordRoDs.getReadingByTime(meterByNo.getId(), ThisHour);
                    if (reading != null) {
                        log.info("该时刻:{}, 电表: {}的抄表记录已经存在", ThisHour, meterByNo.getNo());
                        return Mono.empty();
                    } else {
                        Mono<CurMeterRealData> curMeter = meterBizService.getCurMeter(i);

                        return curMeter.map(ie -> {
                            MeterRecordPo meterRecordPo = new MeterRecordPo();
                            BeanUtils.copyProperties(ie, meterRecordPo);
                            meterRecordPo.setSiteId(meterByNo.getSiteId())
                                    .setMeterId(meterByNo.getId())
                                    .setReadingTime(ThisHour);
                            return meterRecordPo;
//                        return meterRecordRwDs.insertMeterRecord(meterRecordPo);
                        });
                    }
                })
                .map(meterRecordRwDs::insertMeterRecord)
                .doOnError(e -> log.info("抄表失败: {}", e.getMessage(), e));

    }


}