package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.app.CurPowerData;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.north.job.JobTask;
import com.cdz360.iot.meter.north.job.ReadCurPowerJob;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 抄当前读数
 */
@Slf4j
@Service
@Getter
public class ReadCurPowerService extends AbstractSubMeterObserver implements MeterSubObserver {


    @Autowired
    private MeterChannelRepository meterRepository;


    @Autowired
    private MeterObserverService meterObserverProxy;

    //private Map<String, ReadCurPowerJob> jobMap = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        this.meterObserverProxy.addSubObserver(this,List.of(P645DataType.CUR_VOLTAGE,
                P645DataType.CUR_CURRENT,P645DataType.CUR_POWER,
                P645DataType.CUR_IDLE));
    }


    public Mono<CurPowerData> process(String deviceNo) {

        ReadCurPowerJob job = new ReadCurPowerJob(this.meterRepository, deviceNo);
        job.init();
        super.addJob(deviceNo, job);

        Mono<CurPowerData> mono =  Mono.create(sink -> {
            JobTask task = job.nextTask();
            job.setMonoSink(sink);
            job.process(task);

        });
        return mono;
    }


//    @Override
//    public void notifyEvent(P645BaseMsg msg) {
//        log.info("traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//        ReadCurPowerJob job = jobMap.get(msg.getDeviceNo());
//        if (job == null) {
//            log.warn("job not exist. traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//            return;
//        }
//        job.fillData(msg);
//        synchronized (job) {
//            job.notifyAll();
//        }
//    }



}
