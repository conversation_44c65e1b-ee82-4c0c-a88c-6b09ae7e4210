package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.meter.model.app.LastDayMeterData;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645PowerResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * 抄上一日电表读数任务
 */
@Slf4j
public class ReadLastDayDataJob extends AbstractJob {

    @Getter
    private LastDayMeterData meterData = new LastDayMeterData();

    private final List<P645DataType> DATA_TYPE_LIST = List.of(P645DataType.LAST_DAY_POWER_COMBINE,
            P645DataType.LAST_DAY_POWER_POSITIVE, P645DataType.LAST_DAY_POWER_NEGATIVE,
            P645DataType.LAST_DAY_IDLE_POWER_1, P645DataType.LAST_DAY_IDLE_POWER_2);


    public ReadLastDayDataJob(MeterChannelRepository meterRepository, String deviceNo) {
        super(meterRepository, deviceNo);
    }


    @Override
    public List<P645DataType> getDataTypeList() {
        return DATA_TYPE_LIST;
    }

    @Override
    public void fillData(P645BaseMsg msgIn) {
        P645PowerResponse msg = (P645PowerResponse)msgIn;
        if (msg.getDataType() == P645DataType.LAST_DAY_POWER_COMBINE) {
            this.meterData.setCombineTotal(msg.getTotal())
                    .setCombineA(msg.getV1())
                    .setCombineB(msg.getV2())
                    .setCombineC(msg.getV3())
                    .setCombineD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.LAST_DAY_POWER_POSITIVE) {
            this.meterData.setPositiveTotal(msg.getTotal())
                    .setPositiveA(msg.getV1())
                    .setPositiveB(msg.getV2())
                    .setPositiveC(msg.getV3())
                    .setPositiveD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.LAST_DAY_POWER_NEGATIVE) {
            this.meterData.setNegativeTotal(msg.getTotal())
                    .setNegativeA(msg.getV1())
                    .setNegativeB(msg.getV2())
                    .setNegativeC(msg.getV3())
                    .setNegativeD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.LAST_DAY_IDLE_POWER_1) {
            this.meterData.setPositiveIdleTotal(msg.getTotal())
                    .setPositiveIdleA(msg.getV1())
                    .setPositiveIdleB(msg.getV2())
                    .setPositiveIdleC(msg.getV3())
                    .setPositiveIdleD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.LAST_DAY_IDLE_POWER_2) {
            this.meterData.setNegativeIdleTotal(msg.getTotal())
                    .setNegativeIdleA(msg.getV1())
                    .setNegativeIdleB(msg.getV2())
                    .setNegativeIdleC(msg.getV3())
                    .setNegativeIdleD(msg.getV4());
        }
    }

    @Override
    public void finish() {
        this.getMonoSink().success(this.meterData);
    }
}
