package com.cdz360.iot.meter.model.app;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 当前电表读数
 */
@Data
@Accessors(chain = true)
public class CurMeterData {
    /**
     * 总组合有功
     */
    private BigDecimal combineTotal = BigDecimal.ZERO;
    private BigDecimal combineA = BigDecimal.ZERO;
    private BigDecimal combineB = BigDecimal.ZERO;
    private BigDecimal combineC = BigDecimal.ZERO;
    private BigDecimal combineD = BigDecimal.ZERO;

    /**
     * 正向总有功
     */
    private BigDecimal positiveTotal = BigDecimal.ZERO;
    private BigDecimal positiveA = BigDecimal.ZERO;
    private BigDecimal positiveB = BigDecimal.ZERO;
    private BigDecimal positiveC = BigDecimal.ZERO;
    private BigDecimal positiveD = BigDecimal.ZERO;

    /**
     * 反向总有功
     */
    private BigDecimal negativeTotal = BigDecimal.ZERO;
    private BigDecimal negativeA = BigDecimal.ZERO;
    private BigDecimal negativeB = BigDecimal.ZERO;
    private BigDecimal negativeC = BigDecimal.ZERO;
    private BigDecimal negativeD = BigDecimal.ZERO;


    /**
     * 正向总无功
     */
    private BigDecimal positiveIdleTotal = BigDecimal.ZERO;
    private BigDecimal positiveIdleA = BigDecimal.ZERO;
    private BigDecimal positiveIdleB = BigDecimal.ZERO;
    private BigDecimal positiveIdleC = BigDecimal.ZERO;
    private BigDecimal positiveIdleD = BigDecimal.ZERO;

    /**
     * 反向总无功
     */
    private BigDecimal negativeIdleTotal = BigDecimal.ZERO;
    private BigDecimal negativeIdleA = BigDecimal.ZERO;
    private BigDecimal negativeIdleB = BigDecimal.ZERO;
    private BigDecimal negativeIdleC = BigDecimal.ZERO;
    private BigDecimal negativeIdleD = BigDecimal.ZERO;

    /**
     * 正向视在, 已乘变比
     */
    private BigDecimal positiveApparentTotal = BigDecimal.ZERO;
    private BigDecimal positiveApparentA = BigDecimal.ZERO;
    private BigDecimal positiveApparentB = BigDecimal.ZERO;
    private BigDecimal positiveApparentC = BigDecimal.ZERO;
    private BigDecimal positiveApparentD = BigDecimal.ZERO;

    /**
     * 反向视在, 已乘变比
     */
    private BigDecimal negativeApparentTotal = BigDecimal.ZERO;
    private BigDecimal negativeApparentA = BigDecimal.ZERO;
    private BigDecimal negativeApparentB = BigDecimal.ZERO;
    private BigDecimal negativeApparentC = BigDecimal.ZERO;
    private BigDecimal negativeApparentD = BigDecimal.ZERO;

}
