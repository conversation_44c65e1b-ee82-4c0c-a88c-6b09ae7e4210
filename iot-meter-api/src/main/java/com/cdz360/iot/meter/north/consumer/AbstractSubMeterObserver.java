package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.north.job.AbstractJob;
import com.cdz360.iot.meter.north.job.JobTask;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class AbstractSubMeterObserver<T extends AbstractJob> implements MeterSubObserver {

    private Map<String, T> jobMap = new ConcurrentHashMap<>();

    protected void addJob(String deviceNo, T job) {
        this.jobMap.put(deviceNo, job);
    }

    @Override
    public void notifyEvent(P645BaseMsg msg) {
        log.info("traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
        //Mono<ReadCurDataJob> job = jobMap.get(msg.getDeviceNo());
        T job = jobMap.get(msg.getDeviceNo());
        if (job == null) {
            log.warn("job not exist. traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
            return;
        }

        job.fillData(msg);
        JobTask task = job.nextTask();
        if (task != null) {
            job.process(task);
        } else {
            jobMap.remove(msg.getDeviceNo());
            job.finish();
        }

    }
}
