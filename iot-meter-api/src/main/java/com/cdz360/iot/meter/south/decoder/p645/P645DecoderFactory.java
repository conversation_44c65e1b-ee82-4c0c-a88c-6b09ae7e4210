package com.cdz360.iot.meter.south.decoder.p645;

import com.cdz360.iot.meter.model.type.P645DataType;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class P645DecoderFactory {

    private Map<P645DataType, P645Decoder> decoderMap = new ConcurrentHashMap<>();

    public void addDecoder(P645DataType dataType, P645Decoder decoder) {
        decoderMap.put(dataType, decoder);
    }

    public P645Decoder getDecoder(P645DataType dataType) {
        return this.decoderMap.get(dataType);
    }
}
