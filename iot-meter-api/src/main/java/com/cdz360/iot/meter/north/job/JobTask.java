package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645ReadRequest;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.model.type.P645DataType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class JobTask {

    private AbstractJob job;
    private IotBaseMsg msg;
    private P645DataType dataType;
    private String deviceNo;

    // private NettyChannelRepository channelRepository;

    public JobTask(AbstractJob job, P645DataType dataType, String deviceNo) {
        this.job = job;
        //this.channelRepository = channelRepository;
        this.dataType = dataType;
        this.deviceNo = deviceNo;
    }

    public static JobTask buildMsg(AbstractJob job, P645DataType dataType, String deviceNo) {
        JobTask j = new JobTask(job, dataType, deviceNo);
        P645ReadRequest req = new P645ReadRequest();
        req.setDataType(dataType);

        req.setDeviceNo(deviceNo);

        j.msg = new IotBaseMsg();
        j.msg.setCmdCode(CmdCode.APP_REQ);
        j.msg.setData(req.toBytes());
        j.msg.setDeviceNo(deviceNo);
        return j;
    }
}
