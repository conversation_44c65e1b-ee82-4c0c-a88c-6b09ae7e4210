package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.type.P645DataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Service
public class MeterObserverService implements MeterOvserver, Runnable {


    private Map<P645DataType, MeterSubObserver> observerMap = new ConcurrentHashMap<>();

    private Queue<P645BaseMsg> msgQ = new ConcurrentLinkedQueue<>();


    @PostConstruct
    public void init() {
        new Thread(this).start();
    }

    public void addSubObserver(MeterSubObserver observer, List<P645DataType> dataTypes) {
        dataTypes.stream().forEach(dt -> {
            this.observerMap.put(dt, observer);
        });
    }


    @Override
    public void notifyP645Event() {
        synchronized (this) {
            this.notifyAll();
        }
    }

    @Override
    public void addEventMsg(P645BaseMsg msg) {
        msgQ.add(msg);
    }

    private void processMsg() {
        while (!msgQ.isEmpty()) {
            P645BaseMsg p645Msg = msgQ.remove();
            this.processMsg(p645Msg);
        }
    }

    private void processMsg(P645BaseMsg p645Msg) {
        MeterSubObserver observer = this.observerMap.get(p645Msg.getDataType());
        if(observer != null) {
            observer.notifyEvent(p645Msg);
        }else {
            log.warn("不支持的数据标识. traceId = {}, msg = {}, dataType[0] = {}",
                    p645Msg.getTraceId(), p645Msg, p645Msg.getDataType());

        }
    }


    @Override
    public void run() {
        while (true) {
            try {
                synchronized (this) {
                    this.processMsg();
                    this.wait();
                }
            } catch (Exception e) {
                log.error("error = {}", e.getMessage(), e);
            }
        }
    }
}
