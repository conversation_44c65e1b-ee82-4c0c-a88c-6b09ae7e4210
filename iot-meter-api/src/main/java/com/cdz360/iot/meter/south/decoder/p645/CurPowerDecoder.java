package com.cdz360.iot.meter.south.decoder.p645;

import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645PowerResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Slf4j
@Service
public class CurPowerDecoder extends P645Decoder{

    @Autowired
    private P645DecoderFactory p645DecoderFactory;

    @PostConstruct
    public void init() {
        p645DecoderFactory.addDecoder(P645DataType.POWER_COMBINE, this);
        p645DecoderFactory.addDecoder(P645DataType.POWER_POSITIVE, this);
        p645DecoderFactory.addDecoder(P645DataType.POWER_NEGATIVE, this);
        p645DecoderFactory.addDecoder(P645DataType.IDLE_POWER_1, this);
        p645DecoderFactory.addDecoder(P645DataType.IDLE_POWER_2, this);

    }

    @Override
    public P645BaseMsg parse(P645BaseMsg baseMsg) {
        log.debug("msg = {}", baseMsg);
        P645PowerResponse p645Msg = new P645PowerResponse(baseMsg);
        byte[] data = p645Msg.getData();

        //p645Msg.setDataType( p645Msg.parseDataType(data[0], data[2]));

        int idx = 4;
        p645Msg.setTotal(this.parseDecimal41Value(data, idx));
        idx+=4;

        p645Msg.setV1(this.parseDecimal41Value(data, idx));
        idx+=4;

        p645Msg.setV2(this.parseDecimal41Value(data, idx));
        idx+=4;

        p645Msg.setV3( this.parseDecimal41Value(data, idx));
        idx+=4;

        p645Msg.setV4( this.parseDecimal41Value(data, idx));
        return p645Msg;
    }
}
