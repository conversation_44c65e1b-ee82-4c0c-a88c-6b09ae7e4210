package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.app.CurMeterData;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.north.job.JobTask;
import com.cdz360.iot.meter.north.job.ReadCurDataJob;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 抄当前读数
 */
@Slf4j
@Service
@Getter
public class ReadCurDataService extends AbstractSubMeterObserver implements MeterSubObserver {


    @Autowired
    private MeterChannelRepository meterRepository;

    @Autowired
    private MeterObserverService meterObserverProxy;

//    @Autowired
//    private MeterRecordRoDs meterRecordRoDs;
//    @Autowired
//    private MeterRoDs meterRoDs;
//    @Autowired
//    private EvseMeterRoDs evseMeterRoDs;
//
//    @Autowired
//    private MeterRecordRwDs meterRecordRwDs;
//    @Autowired
//    private MeterRwDs meterRwDs;
//    @Autowired
//    private EvseMeterRwDs evseMeterRwDs;

    //private Map<String, Mono<ReadCurDataJob>> jobMap = new ConcurrentHashMap<>();
    //private Map<String, ReadCurDataJob> jobMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        this.meterObserverProxy.addSubObserver(this, List.of(P645DataType.POWER_COMBINE,
                P645DataType.POWER_POSITIVE, P645DataType.POWER_NEGATIVE,
                P645DataType.IDLE_POWER_1, P645DataType.IDLE_POWER_2));
    }

    public Mono<CurMeterData> process(String deviceNo) {

        ReadCurDataJob job = new ReadCurDataJob(this.meterRepository, deviceNo);
        job.init();

        super.addJob(deviceNo, job);

        Mono<CurMeterData> mono =  Mono.create(sink -> {
            JobTask task = job.nextTask();
            job.setMonoSink(sink);
            job.process(task);

        });
        return mono;
    }


//    public void processResponse(P645BaseMsg msg) {
//
//    }


//    @Override
//    public void notifyEvent(P645BaseMsg msg) {
//        log.info("traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//        //Mono<ReadCurDataJob> job = jobMap.get(msg.getDeviceNo());
//        ReadCurDataJob job = jobMap.get(msg.getDeviceNo());
//        if (job == null) {
//            log.warn("job not exist. traceId = {}, deviceNo = {}", msg.getTraceId(), msg.getDeviceNo());
//            return;
//        }
//
//            job.fillData(msg);
//            JobTask task = job.nextTask();
//            if (task != null) {
//                job.process(task);
//            } else {
//                jobMap.remove(msg.getDeviceNo());
//                job.getMonoSink().success(job.getCurMeterData());
//            }
//
//    }
}
