package com.cdz360.iot.meter.south;

import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * netty tcp 连接的管理类
 */
@Slf4j
@Component
public class NettyChannelRepository {
    private ConcurrentMap<String, Channel> channelCache = new ConcurrentHashMap<String, Channel>();

    @Autowired
    private MeterChannelRepository meterChannelRepository;

    public NettyChannelRepository put(String key, Channel value) {
        channelCache.put(key, value);
        return this;
    }

    public Channel get(String key) {
        return channelCache.get(key);
    }

    public void remove(String key) {

        log.info("移除tcp链接: {}", key);
//        String deviceNo = this.meterChannelRepository.interpret2DeviceNo(this.channelCache.get(key));
//        this.meterChannelRepository.remove(deviceNo);
        this.meterChannelRepository.removeByChannelId(this.channelCache.get(key));

        this.channelCache.remove(key);

    }

//    public List<String> getDeviceNoList() {
//        List<String> deviceNoList = new ArrayList<>();
//        deviceNoList.addAll(channelCache.keySet());
//        return deviceNoList;
//    }

    public int size() {
        return this.channelCache.size();
    }
}
