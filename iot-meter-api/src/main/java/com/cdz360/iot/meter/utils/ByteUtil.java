package com.cdz360.iot.meter.utils;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

public class ByteUtil {
    private static final char[] DIGITS_UPPER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D',
            'E', 'F'};

    public static boolean getBit(int num, int i) {
        return ((num & (1 << i)) != 0);//true 表示第i位为1,否则为0
    }

    public static String bytesToHex(byte[] srcBytes) {
        if (srcBytes == null) {
            return null;
        }
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[srcBytes.length * 2];
        for (int j = 0; j < srcBytes.length; j++) {
            int v = srcBytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 二进制转换为HEX编码(全大写).
     *
     * @param data   二进制数据
     * @param offset 开始字节下标
     * @param length 要转换部分长度
     * @return HEX字符串
     */
    public static String encodeHexString(final byte[] data, final int offset, final int length) {
        final char[] out = new char[length << 1];
        // two characters form the hex value.
        final int end = length + offset;
        for (int i = offset, j = 0; i < end; i++) {
            out[j++] = DIGITS_UPPER[(0xF0 & data[i]) >>> 4];
            out[j++] = DIGITS_UPPER[0x0F & data[i]];
        }
        return new String(out);
    }

    /**
     * 二进制转换为HEX编码(全大写).小端
     * @param data
     * @param offset
     * @param length
     * @return
     */
    public static String encodeHexStringLittle(final byte[] data, final int offset, final int length) {
        final char[] out = new char[length << 1];
        final int end = length + offset;
        for (int i = offset, j = length << 1; i < end; i++) {
            out[--j] = DIGITS_UPPER[0x0F & data[i]];
            out[--j] = DIGITS_UPPER[(0xF0 & data[i]) >>> 4];
        }
        return new String(out);
    }


    public static int byte2BcdInt(byte data) {
        int a = ((0xF0 & data) >>> 4) * 10;
        int b = 0x0F & data;
        return a + b;
    }

    /**
     * 带符号位版本
     * @param data
     * @return
     */
    public static int byte2BcdIntSkipSign(byte data) {
        int a = ((0x70 & data) >>> 4) * 10;
        int b = 0x0F & data;
        return a + b;
    }

    public static byte int2BcdByte(int val) {
        return (byte) (val / 10 * 16 + val % 10);
    }



    public static String byteToHexStr(byte b) {
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[2];
        int v = b & 0xFF;
        hexChars[0] = hexArray[v >>> 4];
        hexChars[1] = hexArray[v & 0x0F];
        return new String(hexChars);
    }

    public static double byteArrayToDouble(byte[] bytes) {
        String result = bytesToHex(bytes);
        result = result.substring(0, result.length() - 2) + "." + result.substring(result.length() - 2);
        return Double.valueOf(result);
    }

    public static String byte2Mac(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            sb.append(String.format("%02X%s", bytes[i], (i < bytes.length - 1) ? "-" : ""));
        }
        return sb.toString();
    }

    public static String byte2Format(byte[] bytes, String format) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            sb.append(String.format("%02X%s", bytes[i], (i < bytes.length - 1) ? format : ""));
        }
        return sb.toString();
    }

    public static String bytes2ASCII(byte[] bytes) {
        // StringBuilder sb = new StringBuilder();
        // for (byte b : bytes) {
        //     sb.append((char) b);
        // }
        //
        // return sb.toString();

        return new String(bytes, StandardCharsets.US_ASCII);

    }

    /**
     * 16进制表示的字符串转换为字节数组
     *
     * @param s 16进制表示的字符串
     * @return byte[] 字节数组
     */
    public static byte[] hexToBytes(String s) {
        int len = s.length();
        byte[] b = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            // 两位一组，表示一个字节,把这样表示的16进制字符串，还原成一个字节
            b[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character
                    .digit(s.charAt(i + 1), 16));
        }
        return b;
    }

    /**
     * 16进制表示的字符串转换为字节数组(小端)
     *
     * @param s 16进制表示的字符串
     * @return byte[] 字节数组
     */
    public static byte[] hexToBytesLittle(String s) {
        byte[] bytes = hexToBytes(s);
        //逆序排列
        int length = bytes.length;
        int temp = 0;
        for (int i = 0; i < length / 2; i++) {
            temp = bytes[i];
            bytes[i] = bytes[length - i - 1];
            bytes[length - i - 1] = (byte) temp;
        }
        return bytes;
    }


    public static byte[] intToByteLE(long val) {
        byte[] b = new byte[4];
        b[0] = (byte) (val & 0xff);
        b[1] = (byte) ((val >> 8) & 0xff);
        b[2] = (byte) ((val >> 16) & 0xff);
        b[3] = (byte) ((val >> 24) & 0xff);

        return b;
    }

    public static byte[] intToByteBE(int val) {
        byte[] b = new byte[4];
        b[0] = (byte) ((val >> 24) & 0xff);
        b[1] = (byte) ((val >> 16) & 0xff);
        b[2] = (byte) ((val >> 8) & 0xff);
        b[3] = (byte) (val & 0xff);
        return b;
    }

    /**
     * 这里是将ascii码str转换为十六进制值 str
     *
     * @param asciiStr
     * @return String
     ***/
    public static String asciiToHex(String asciiStr) {
        char[] chars = asciiStr.toCharArray();
        StringBuilder hex = new StringBuilder();
        for (char ch : chars) {
            hex.append(Integer.toHexString(ch));
        }
        return hex.toString();
    }

    /**
     * hex str to ascii str
     *
     * @param hexStr 十六进制字符
     * @return String
     ***/
    public static String hexToAscii(String hexStr) {
        StringBuilder output = new StringBuilder();
        for (int i = 0; i < hexStr.length(); i += 2) {
            String str = hexStr.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }
        return output.toString();
    }

    /**
     * 将byte数组转换成ASCII字符
     *
     * @param bytes byte数组
     * @return ASCII字符
     ***/
    public static String byteArrayToASCII(byte[] bytes) {

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            byte b = bytes[i];
            // Character character = (char) b;
            // sb.append(character.toString());
            sb.append((char) b);
        }

        return sb.toString();
    }

    /**
     * ASCII字符串->HEX编码的 byteArray
     *
     * @param str ASCII字符
     * @return byte[]
     */
    public static byte[] asciiStrToByteArray(String str) {
        char[] chars = str.toCharArray();
        byte[] bytes = new byte[str.toCharArray().length];
        for (int i = 0; i < chars.length; i++) {
            // System.out.println(chars[i] + " -> " + (int) chars[i]);
            bytes[i] = (byte) chars[i];
        }
        return bytes;
    }

    /**
     * byte[]转换为short（b为大端表示）
     *
     * @param b
     * @return short
     */
    public static short byteToShortBE(byte[] b) {
        short s = 0;
        short s0 = (short) (b[0] & 0xff);// 最低位
        short s1 = (short) (b[1] & 0xff);
        s0 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    /**
     * byte[]转换为short（b为小端表示）
     *
     * @param b
     * @return short
     */
    public static short byteToShortLE(byte[] b) {
        short s = 0;
        short s0 = (short) (b[0] & 0xff);// 最低位
        short s1 = (short) (b[1] & 0xff);
        s1 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    /**
     * byte[] 转 long, 小端编码
     *
     * @param data   二进制数组
     * @param offset 开始字节下标
     * @param length 长度
     * @return 小端编码的 long 型数据
     */
    public static long bytes2Long(byte[] data, int offset, int length) {
        long result = 0L;
        int start = offset + length - 1;
        int end = offset - 1;
        for (int i = start; i > end; i--) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    public static int bytes2Int(byte[] data, int offset, int length) {
        int result = 0;
        int start = offset + length - 1;
        int end = offset - 1;
        for (int i = start; i > end; i--) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    /**
     * int类型转换为long类型，如果是value>0则直接返回，java语言自动支持转换，如果value<0则需要处理符号位：
     *
     * @param value
     * @return
     */
    public static long toUnsignedIntValue(int value) {
        if (!needWiden(value)) {
            return value;
        }
        long wideValue = value;
        // 使用 与 运算把高为字节设置为0，符号位为0，该数字为正数
        // 注意后面那个L，如果没有这个L，java会认为这个数字为int类型,在运算过程中他会自动把他转换为long类型，那么最终的返回的值就还是负数，这是由于字节少的数字类型转换为字节多的类型时太会
        // 自动用符号位填充高位字节，，举个例子,
        // 例子1
        // int value = 100;
        // long lv = value; 此时低4个字节填充的是100，而高4个字节是0
        // 例子3
        // int value = 0xf0000000;
        // long lv = value; 此时低4个字节是0xf0000000， 而高4个字节是1，因为0xf0000000的符号位是1
        // 至于为什么是这样，这里不再解释，考虑一下计算机内部负数是用补码表示的这一原则
        return wideValue & 0xffffffffL;
    }

    /**
     * 判断一个int类型的数字是否为负数，如果为负数需要使用更宽类型的数据类型来接收，使用long接收。
     *
     * @param value
     * @return
     */
    private static boolean needWiden(int value) {
        // 0x80000000，即2的31次方，对于int型数字来说就是符号位，如果符号位为1，则该数字为负数
        // 0x80000000也就是Integer.MIN_VALUE
        return (value & 0x80000000) == 0x80000000;
    }

    public static byte[] longToByte(long val) {
        return new byte[]{
                (byte) (val), (byte) (val >>> 8), (byte) (val >>> 16), (byte) (val >>> 24)
        };
        //        byte[] buf = new byte[4];
        //        buf[3] =(byte)( (val & 0xFF000000) >>> 24);
        //        buf[2] =(byte)( (val & 0x00FF0000) >>> 16);
        //        buf[1] =(byte)( (val & 0x0000FF00) >>> 8);
        //        buf[0] =(byte)( val & 0x000000FF );
        //        return buf;
    }

    public static byte[] intToByte2(int val) {
        return new byte[]{
                (byte) (val), (byte) (val >>> 8)
        };

        //        byte[] buf = new byte[2];
        //        buf[1] =(byte)(( val & 0xFF00) >>> 8 );
        //        buf[0] =(byte)( val & 0x00FF);
        //        return buf;
    }

    //    public static byte[] longToByte(long value, int length) {
    //        return ByteBuffer.allocate(length).order(ByteOrder.LITTLE_ENDIAN).putLong(value).array();
    //    }

    public static byte[] concat(byte[] bytes1, byte[] bytes2) {
        // ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // byteArrayOutputStream.write(bytes1);
        // byteArrayOutputStream.write(bytes2);
        //
        // return byteArrayOutputStream.toByteArray();

        return ByteBuffer.wrap(new byte[bytes1.length + bytes2.length]).put(bytes1).put(bytes2).array();
    }

    public static String bytes2ASCII(byte[] data, int offset, int length) {
        StringBuilder result = new StringBuilder();

        int start = offset;
        int end = offset + length;
        for (int i = start; i < end; i++) {
            result.append((char) data[i]);
        }

        return result.toString();
    }

    public static byte[] bcd(String str) {
        return hexToBytes(str);
    }

    public static byte[] bcdLittle(String str) {
        return hexToBytesLittle(str);
    }
}
