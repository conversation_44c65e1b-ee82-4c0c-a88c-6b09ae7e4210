package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.meter.model.app.CurPowerData;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645CurPowerResponse;
import com.cdz360.iot.meter.model.iot.p645.P645CurrentResponse;
import com.cdz360.iot.meter.model.iot.p645.P645VoltageResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 超当前电表读数任务
 */
@Slf4j
public class ReadCurPowerJob extends AbstractJob {

    @Getter
    private CurPowerData curMeterData = new CurPowerData();

    private final List<P645DataType> DATA_TYPE_LIST = List.of(P645DataType.CUR_VOLTAGE,
            P645DataType.CUR_CURRENT, P645DataType.CUR_POWER,
            P645DataType.CUR_IDLE);


    public ReadCurPowerJob(MeterChannelRepository meterRepository, String deviceNo) {
        super(meterRepository, deviceNo);
    }

    @Override
    public List<P645DataType> getDataTypeList() {
        return DATA_TYPE_LIST;
    }

    @Override
    public void fillData(P645BaseMsg msgIn) {
        if (msgIn.getDataType() == P645DataType.CUR_VOLTAGE) {
            P645VoltageResponse msg = (P645VoltageResponse) msgIn;
            this.curMeterData.setVoltageA(msg.getVoltageA())
                    .setVoltageB(msg.getVoltageB())
                    .setVoltageC(msg.getVoltageC());
        } else if (msgIn.getDataType() == P645DataType.CUR_CURRENT) {
            P645CurrentResponse msg = (P645CurrentResponse) msgIn;
            this.curMeterData.setCurrentA(msg.getCurrentA())
                    .setCurrentB(msg.getCurrentB())
                    .setCurrentC(msg.getCurrentC());
        } else if (msgIn.getDataType() == P645DataType.CUR_POWER) {
            P645CurPowerResponse msg = (P645CurPowerResponse) msgIn;
            this.curMeterData.setPowerTotal(msg.getPowerTotal())
                    .setPowerA(msg.getPowerA())
                    .setPowerB(msg.getPowerB())
                    .setPowerC(msg.getPowerC());
        } else if (msgIn.getDataType() == P645DataType.CUR_IDLE) {
            P645CurPowerResponse msg = (P645CurPowerResponse) msgIn;
            this.curMeterData.setIdleTotal(msg.getPowerTotal())
                    .setIdleA(msg.getPowerA())
                    .setIdleB(msg.getPowerB())
                    .setIdleC(msg.getPowerC());
        }
    }

    @Override
    public void finish() {
        this.getMonoSink().success(this.curMeterData);
    }
}
