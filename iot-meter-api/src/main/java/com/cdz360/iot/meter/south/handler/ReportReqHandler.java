package com.cdz360.iot.meter.south.handler;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;

/**
 * 电表主动上报
 */
@Slf4j
@Service
public class ReportReqHandler implements IotHandler {


    //private static final CmdCode CMD_CODE = CmdCode.LOGIN_REQ;

    @Autowired
    private MeterHandlerFacade iotEvseFacade;


    @PostConstruct
    public void init() {
        this.iotEvseFacade.addProducer(CmdCode.REPORT_REQ, this);
        this.iotEvseFacade.addProducer(CmdCode.REPORT_MORE_REQ, this);
    }

    @Override
    public Mono<byte[]> process(IotBaseMsg msg) {
        log.info(">> traceId = {}", msg.getTraceId());
        // 不需要响应
        return Mono.just(IotHandler.EMPTY);
    }
}
