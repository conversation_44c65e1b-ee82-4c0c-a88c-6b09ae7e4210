package com.cdz360.iot.meter.model.iot.p645;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 瞬时有功/无功响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class P645CurPowerResponse extends P645BaseMsg {

    /**
     * 瞬时总有功/无功功率， 3位小数
     */
    private BigDecimal powerTotal;

    /**
     * A相瞬时有功/无功功率
     */
    private BigDecimal powerA;

    private BigDecimal powerB;

    private BigDecimal powerC;


    public P645CurPowerResponse() {

    }

    public P645CurPowerResponse(P645BaseMsg baseMsg) {
        super.setTraceId(baseMsg.getTraceId());
        super.setChannelKey(baseMsg.getChannelKey());
        super.setDeviceNo(baseMsg.getDeviceNo());
        super.setCmdCode(baseMsg.getCmdCode());
        super.setLength(baseMsg.getLength());
        super.setDataType(baseMsg.getDataType());
        super.setData(baseMsg.getData());
        super.setCs(baseMsg.getCs());
    }
}
