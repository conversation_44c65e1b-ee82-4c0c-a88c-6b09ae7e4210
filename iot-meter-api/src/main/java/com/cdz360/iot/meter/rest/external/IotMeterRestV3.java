package com.cdz360.iot.meter.rest.external;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.meter.biz.south.MeterSouthBizService;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.meter.dto.MeterRtReq;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "iot网关接入API (v3)", description = "南向-电表网关接口")
@RequestMapping("/iot/meter")
public class IotMeterRestV3 extends IotRestBase {

    @Autowired
    private IotCacheService iotCacheService;

    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }

    @Autowired
    private MeterSouthBizService meterSouthBizService;

    @Operation(summary = "上传电表实时数据")
    @PostMapping(value = "/meterRtData", params = {"v=3"})
    public Mono<BaseGwResponse> meterRtData(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<MeterRtReq> gwReq) {
        log.debug("电表上传运行数据。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        if (StringUtils.isBlank(gwReq.getGwno())) {
            gwReq.setGwno(gwno);
        }
        return meterSouthBizService.processRtData(gwReq.getData())
                .doOnNext(res1 -> {
                    res1.setSeq(gwReq.getSeq());
                    LogHelper.logLatency(log, IotMeterRestV3.class.getSimpleName(),
                            "meterRtData", "电表上传运行数据", startTime);
                });
    }

}
