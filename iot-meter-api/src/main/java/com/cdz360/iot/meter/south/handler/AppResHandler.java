package com.cdz360.iot.meter.south.handler;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.model.type.P645CmdCode;
import com.cdz360.iot.meter.north.consumer.*;
import com.cdz360.iot.meter.south.decoder.p645.P645DecoderFactory;
import com.cdz360.iot.meter.utils.ByteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;

/**
 * 透传响应消息
 */
@Slf4j
@Service
public class AppResHandler implements IotHandler {
    private static final CmdCode CMD_CODE = CmdCode.APP_RES;

    @Autowired
    private MeterHandlerFacade iotEvseFacade;

    @Autowired
    private ReadCurDataService readCurDataConsumer;

    @Autowired
    private ReadLastDayDataService readLastDayDataConsumer;

    @Autowired
    private ReadCurPowerService readCurVoltageConsumer;

    @Autowired
    private ReadTransformerService readTransformerService;

    @Autowired
    private P645DecoderFactory p645DecoderFactory;

    @Autowired
    private MeterOvserver meterOvserver;




    @PostConstruct
    public void init() {
        this.iotEvseFacade.addProducer(CMD_CODE, this);
    }


    @Override
    public Mono<byte[]> process(IotBaseMsg msg) {
        log.info(">> traceId = {}", msg.getTraceId());
        P645BaseMsg p645Msg = this.parseP645(msg.getTraceId(), msg.getData());
        log.info("traceId = {}, p645Msg = {}", msg.getTraceId(), p645Msg);
        p645Msg = this.p645DecoderFactory.getDecoder(p645Msg.getDataType()).parse(p645Msg);   // 解析p645 data部分内容

        log.info("traceId = {}, p645Msg = {}", msg.getTraceId(), p645Msg);

        if (p645Msg.getCmdCode() == P645CmdCode.READ_RES) {
            //P645PowerResponse readResMsg = new P645PowerResponse(p645Msg);
            //readResMsg.parse();
            //log.info("traceId = {}, p645ReadResMsg = {}", readResMsg.getTraceId(), readResMsg);
            meterOvserver.addEventMsg(p645Msg);
            meterOvserver.notifyP645Event();



        }

        return Mono.just(IotHandler.EMPTY);
    }


    private P645BaseMsg parseP645(String traceId, byte[] bufIn) throws DcServiceException {
        int idx = 0;
        byte fe = bufIn[idx];
        while (fe == (byte) 0xFE
                && idx < 4
                && idx < bufIn.length) {
            fe = bufIn[idx++];  // 去掉头部的FE
        }
        byte b68 = bufIn[idx++];
        if (b68 != (byte) 0x68) {
            log.warn("报文格式错误. traceId = {}, bufIn = {}",
                    traceId,
                    ByteUtil.bytesToHex(bufIn));
            throw new DcServiceException("报文格式错误");
        }
        P645BaseMsg msg = new P645BaseMsg();
        msg.setTraceId(traceId);
        msg.setDeviceNo(ByteUtil.encodeHexStringLittle(bufIn, idx, 6));
        idx += 6;
        idx++; // 忽略掉中间的68
        msg.setCmdCode(P645CmdCode.codeOf(bufIn[idx++]));
        msg.setLength((int) bufIn[idx++]);
        byte[] data = new byte[msg.getLength()];
        System.arraycopy(bufIn, idx, data, 0, msg.getLength());
        msg.setData(data);
        for (int i = 0; i < data.length; i++) {
            data[i] = (byte) (data[i] - 0x33);
        }
        msg.setDataType(msg.parseDataType(data[0], data[2], data[3]));
        return msg;
    }
}
