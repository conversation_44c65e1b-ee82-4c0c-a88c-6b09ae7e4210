package com.cdz360.iot.meter.model.iot.p645;

import com.cdz360.iot.meter.model.type.P645CmdCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class P645ReadRequest extends P645BaseMsg {

    //private P645DataType dataType;

    public P645ReadRequest() {
        super.setCmdCode(P645CmdCode.READ_REQ);
    }

    @Override
    public byte[] buildData() {
        return super.getDataType().getDataType().clone();
    }
}
