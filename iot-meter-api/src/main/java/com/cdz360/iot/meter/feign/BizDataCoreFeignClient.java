package com.cdz360.iot.meter.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.es.vo.MeterRtData;
import java.time.LocalDateTime;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = BizDataCoreFeignHystrix.class)
public interface BizDataCoreFeignClient {

    /**
     * 保存电表上传实时和历史的数据
     */
    @PostMapping("/dataCore/meterData/saveMeterRtAndHisData")
    Mono<BaseResponse> saveMeterRtAndHisData(
        @RequestParam String siteId,
        @RequestParam String gwno,
        @RequestParam String dno,
        @RequestParam String ts,
        @RequestBody MeterRtData meterRtData);

}
