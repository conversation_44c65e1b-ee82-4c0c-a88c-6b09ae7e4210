package com.cdz360.iot.meter.south.decoder;

import com.cdz360.iot.meter.model.iot.IotBaseMsg;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.utils.ByteUtil;

public class BaseDecoder {

    public static IotBaseMsg decode(byte[] bufIn, int headerIdx, int availableSize) {
        IotBaseMsg msg = new IotBaseMsg();
        int idx = headerIdx +1; // 跳过 68

        msg.setDeviceNo(ByteUtil.encodeHexStringLittle(bufIn, idx, 6));
        idx += 6;

//        System.arraycopy(bufIn, idx, msg.getDeviceNo(), 0, 3);  // 设备序列号
//        idx += 3;
//
//        msg.setDeviceType(bufIn[idx++]);    // 设备类型
//
//        System.arraycopy(bufIn, idx, msg.getProduceTime(), 0, 2);  // 生产时间
//        idx += 2;

        msg.setCmdCode(CmdCode.codeOf(bufIn[idx++]));   // 控制码

        msg.setLength(bufIn[idx++] & 0xFF); // 数据域长度

        msg.setData(new byte[msg.getLength()]);
        System.arraycopy(bufIn, idx, msg.getData(), 0, msg.getLength());  // 数据域
        idx+= msg.getLength();

        msg.setCs(bufIn[idx++]);

        return msg;
    }
}
