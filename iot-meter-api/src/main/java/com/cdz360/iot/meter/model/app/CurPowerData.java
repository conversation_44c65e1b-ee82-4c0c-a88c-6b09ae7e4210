package com.cdz360.iot.meter.model.app;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 当前电流/电压/瞬时功率数据
 */
@Data
@Accessors(chain = true)
public class CurPowerData {


    /**
     * A相电压， 1位小数
     */
    private BigDecimal voltageA;

    private BigDecimal voltageB;

    private BigDecimal voltageC;

    /**
     * A相电流， 3位小数
     */
    private BigDecimal currentA;

    private BigDecimal currentB;

    private BigDecimal currentC;

    /**
     * 瞬时总有功功率， 3位小数
     */
    private BigDecimal powerTotal;

    /**
     * A相瞬时有功功率
     */
    private BigDecimal powerA;

    private BigDecimal powerB;

    private BigDecimal powerC;

    /**
     * 瞬时总无功功率， 3位小数
     */
    private BigDecimal idleTotal;

    /**
     * A相瞬时无功功率
     */
    private BigDecimal idleA;

    private BigDecimal idleB;

    private BigDecimal idleC;
}
