package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import com.cdz360.iot.meter.utils.ByteUtil;
import io.netty.channel.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.MonoSink;

import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Getter
public abstract class AbstractJob {
    private MeterChannelRepository meterRepository;

    private String deviceNo;

    private Queue<JobTask> jobQ = new ConcurrentLinkedQueue<>();


    @Setter
    private MonoSink monoSink;


    public AbstractJob(MeterChannelRepository meterRepository, String deviceNo) {
        this.meterRepository = meterRepository;
        this.deviceNo = deviceNo;
    }

    public void init() {
        List<P645DataType> dataTypes = this.getDataTypeList();
        for (P645DataType dt : dataTypes) {
            jobQ.add(JobTask.buildMsg(this, dt, deviceNo));
        }
    }

    public abstract List<P645DataType> getDataTypeList();

    public abstract void fillData(P645BaseMsg msg);

    public abstract  void finish();


    public JobTask nextTask() {
        if (CollectionUtils.isEmpty(jobQ)) {
            return null;
        }
        JobTask task = jobQ.remove();

        return task;
    }

    public void process(JobTask task) {
        byte[] buf = task.getMsg().toBytes();
        Channel channel = meterRepository.get(deviceNo);
        log.info("send read request: {}", ByteUtil.bytesToHex(buf));
        channel.writeAndFlush(buf);
    }

}
