package com.cdz360.iot.meter.yunji;

import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Connection;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public class YJChannelConnectionPool {
    private ConcurrentMap<String, QGDW376Dot1Connection> connectionCache = new ConcurrentHashMap<>();

    public void addConnection(String key, QGDW376Dot1Connection connection) {
        this.connectionCache.put(key, connection);
    }

    public QGDW376Dot1Connection getConnection(String key) {
        return this.connectionCache.get(key);
    }

    public QGDW376Dot1Connection removeConnection(String key) {
        return this.connectionCache.remove(key);
    }

    public ConcurrentMap<String, QGDW376Dot1Connection> getQGDW376Dot1Connection() {
        return this.connectionCache;
    }
}
