package com.cdz360.iot.meter.north.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.meter.vo.MeterKhwData;
import com.cdz360.base.model.meter.vo.MeterKwhItem;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseMeterRoDs;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.MeterRecordRoDs;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.ro.SiteTopologyRefRoDs;
import com.cdz360.iot.ds.rw.EvseMeterRwDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.meter.biz.south.MeterRedisService;
import com.cdz360.iot.meter.feign.BizDataCoreFeignClient;
import com.cdz360.iot.meter.feign.IotEssFeignClient;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import com.cdz360.iot.meter.utils.DateUtil;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.meter.dto.EssMeterCfg;
import com.cdz360.iot.model.meter.dto.MeterInfo;
import com.cdz360.iot.model.meter.dto.MeterRtInfo;
import com.cdz360.iot.model.meter.po.BiMeterPo;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.po.MeterRecordPo;
import com.cdz360.iot.model.meter.type.MeterEstimateType;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import com.cdz360.iot.model.meter.vo.MeterEvseVo;
import com.cdz360.iot.model.meter.vo.MeterRtData;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.meter.vo.SiteMeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.type.TopologyType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

/**
 * @Classname MeterDataService
 * @Descript1ion
 * @Date 9/21/2020 10:05 AM
 * @Created by Rafael
 */
@Slf4j
@Service
public class MeterDataService {

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private MeterRwDs meterRwDs;

    @Autowired
    private EvseMeterRwDs evseMeterRwDs;

    @Autowired
    private EvseMeterRoDs evseMeterRoDs;

    @Autowired
    private MeterRecordRoDs meterRecordRoDs;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private SiteTopologyRefRoDs siteTopologyRefRoDs;

    @Autowired
    private IotEssFeignClient iotEssFeignClient;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;

    @Autowired
    private MeterRedisService meterRedisService;

    @Autowired
    private MeterChannelRepository meterChannelRepository;

    private static final Integer SAMPLE_TIME = 20;

    private static final Integer METER_STATUS_TTL = 60 * 20;

    public MeterPo getMeterByNo(String meterNo) {
        return meterRoDs.getByNo(meterNo);
    }

    public List<MeterEvseVo> getMeterList(MeterListParam param) {
        return meterRoDs.getMeterList(param);
    }

    public long getMeterListTotal(MeterListParam param) {
        return meterRoDs.getMeterListTotal(param);
    }

    private static final int RANDOM_CODE_LEN = 12;

    private static String generateRandomCode(int len) {
        if (len <= 0 || len > RANDOM_CODE_LEN) {
            throw new IllegalArgumentException("随机数不支持此位: " + len);
        }
        long i = ThreadLocalRandom.current().nextLong(BigInteger.valueOf(10).pow(len).longValue());
        return String.format("%0" + len + "d", i);
    }

    private static String getRandomMeterNo(List<String> meterNoExcludeList) {
        if (CollectionUtils.isNotEmpty(meterNoExcludeList)) {
            int maxTry = 100;
            while (maxTry > 0) {
                String ret = MeterDataService.generateRandomCode(RANDOM_CODE_LEN);
                if (!meterNoExcludeList.contains(ret)) {
                    return ret;
                }
                maxTry--;
            }
            return null;
        } else {
            return MeterDataService.generateRandomCode(RANDOM_CODE_LEN);
        }
    }

    @Transactional
    public void createMeter(MeterEvseVo param) {
        IotAssert.isNotNull(param, "请传入参数体");
        IotAssert.isNotBlank(param.getName(), "请传入电表名称");

        if (NetType.S485.equals(param.getNet()) && StringUtils.isBlank(param.getNo())) {
            log.info("系统自动生成电表编号");
            IotAssert.isNotBlank(param.getGwno(), "请传入所属控制器");
            List<MeterPo> byGwno = meterRoDs.getByGwno(param.getGwno());
            List<String> meterNoList = byGwno.stream().map(MeterPo::getNo)
                .collect(Collectors.toList());
            String randomMeterNo = MeterDataService.getRandomMeterNo(meterNoList);
            IotAssert.isNotBlank(randomMeterNo, "无法分配电表编号，请重试");
            log.info("随机生成电表编号: {}", randomMeterNo);
            param.setNo(randomMeterNo);
        } else if (NetType.TPT.equals(param.getNet())) {
            IotAssert.isNotBlank(param.getNo(), "请传入电表编号");
            IotAssert.isNotBlank(param.getGwno(), "请传入DTU编号");
            IotAssert.isNotBlank(param.getProtocol(), "请传入所属通信协议");
            IotAssert.isTrue(
                param.getProtocol().startsWith("DLT645") || param.getProtocol().startsWith("Modbus"),
                "请传入所属通信协议");
            if (param.getProtocol().startsWith("DLT645")) {
                IotAssert.isNotBlank(param.getDno(), "表号不能为空");
            } else if (param.getProtocol().startsWith("Modbus")) {
                IotAssert.isNotNull(param.getSid(), "通讯地址不可为空");
            }
        } else {
            IotAssert.isNotBlank(param.getNo(), "请传入电表编号");
        }

        IotAssert.isNotBlank(param.getSiteId(), "请传入场站编号");

        Long deviceId = null;
        if (CollectionUtils.isNotEmpty(param.getDeviceMeterPoList())) {
            MeterEstimateType estimateType = param.getDeviceMeterPoList().get(0).getEstimateType();
            if (estimateType == null) {
                throw new DcArgumentException("计量对象类型不能传空");
            } else if (MeterEstimateType.PCS_AC_IN_OUT == estimateType) {
                IotAssert.isTrue(param.getDeviceMeterPoList().size() == 1, "计量对象数据异常");
                deviceId = Long.valueOf(param.getDeviceMeterPoList().get(0).getDeviceId());
            }
        }

        if (Boolean.TRUE.equals(param.getPowerLoss())) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceMeterPoList()),
                "请传入绑定桩列表");
        }
        IotAssert.isNotNull(param.getOtherDevice(), "请传入是否计量其他大功率用电设备");

        MeterPo byNo = meterRoDs.getByNo(param.getNo());
        if (byNo != null && StringUtils.isNotBlank(byNo.getSiteId())) {
            SitePo sitePo = siteRoDs.getSite(byNo.getSiteId());
            if (sitePo == null) {
                throw new DcArgumentException("电表已存在且绑定到未知场站: " + byNo.getSiteId());
            }
            throw new DcArgumentException("电表已存在且绑定到场站: " + sitePo.getName());
        }

        if (byNo != null) {
            byNo.setSiteId(param.getSiteId())
                .setName(param.getName())
                .setDno(StringUtils.isBlank(byNo.getDno()) ? param.getNo()
                    : param.getDno())  // 临时使用电表序列号，后续要改为由系统自动生成
                .setNo(param.getNo())
                .setComment(param.getComment())
                .setOtherDevice(param.getOtherDevice())
                .setNet(param.getNet())
                .setGwno(param.getGwno())
                .setPowerLoss(param.getPowerLoss())
                .setProtocol(param.getProtocol())
                .setSid(param.getSid());
            meterRwDs.updateMeter(byNo);
        } else {
            param.setStatus(MeterStatusType.UNKNOWN);
            byNo = param;
            if (StringUtils.isBlank(byNo.getDno())) {
                byNo.setDno(byNo.getNo());  // 临时使用电表序列号，后续要改为由系统自动生成
            }
            if (byNo.getNet() == null) {
                byNo.setNet(NetType.S485);  // 默认为使用485连接
            }
            meterRwDs.insertMeter(byNo);
        }

        final MeterPo meterPo = byNo;
        this.validateEvseMeter(param);
        if (CollectionUtils.isNotEmpty(param.getDeviceMeterPoList())) {
            evseMeterRwDs.batchInsert(
                param.getDeviceMeterPoList()
                    .stream()
                    .map(e -> {
                        IotAssert.isNotBlank(e.getDeviceId(), "请传入桩编号");
                        e.setMeterId(meterPo.getId());
                        return e;
                    })
                    .collect(Collectors.toList())
            );
        }

        if (NetType.S485.equals(param.getNet()) && StringUtils.isNotBlank(param.getGwno())) {
            log.info("新增的电表使用{}通信，下发电表配置到对应控制器", param.getNet(),
                param.getGwno());
            this.modifyEssMeterCfg(param.getGwno());

            if (deviceId != null) {
                iotEssFeignClient.sendCfgByEquipId(deviceId)
                    .subscribe();
            }
        }
    }

    private void modifyEssMeterCfg(String gwno) {
        List<MeterPo> s485MeterList = meterRoDs.getByGwno(gwno);
        if (CollectionUtils.isNotEmpty(s485MeterList)) {
            EssMeterCfg cfg = new EssMeterCfg();
            cfg.setCfgVer(null);//FIXME 版本号、波特率、端口名称、appName暂时不做下发，使用控制器配置
            cfg.setGwno(gwno);
            cfg.setMeterList(s485MeterList.stream()
                .map(e -> {
                    MeterInfo info = new MeterInfo();
                    info.setId(e.getSid());
                    info.setDno(e.getDno());
                    info.setNo(e.getNo());
                    info.setSamplingTime(SAMPLE_TIME);
                    return info;
                })
                .collect(Collectors.toList()));
            iotEssFeignClient.modifyEssMeter(cfg).subscribe();
        }
    }

    private void validateEvseMeter(MeterEvseVo param) {
        if (Boolean.TRUE.equals(param.getPowerLoss())) {
            List<String> list = param.getDeviceMeterPoList()
                .stream()
                .map(e -> e.getDeviceId())
                .collect(Collectors.toList());

            List<DeviceMeterPo> byEvseIdInList = evseMeterRoDs.getByEvseIdInList(list);
            String collect = byEvseIdInList.stream().map(DeviceMeterPo::getDeviceId)
                .collect(Collectors.joining(","));
            IotAssert.isTrue(CollectionUtils.isEmpty(byEvseIdInList),
                "这些桩已经绑定在其他电表下了: " + collect);

            List<EvsePo> evsePoList = evseRoDs.selectBindInTransformerByEvseIds(list);
            IotAssert.isTrue(CollectionUtils.isEmpty(evsePoList),
                "已经绑定在变压器的桩，无法直接绑定到电表，桩号: " +
                    evsePoList.stream()
                        .map(EvsePo::getEvseId)
                        .collect(Collectors.joining(",")));
        } else {
            log.info("不计算电损，不检查桩是否已绑定到其他电表");
        }

    }

    @Transactional
    public void updateMeter(MeterEvseVo param) {
        IotAssert.isNotNull(param, "请传入参数体");
        IotAssert.isNotNull(param.getId(), "请传入id");
        IotAssert.isNotBlank(param.getName(), "请传入电表名称");
//        IotAssert.isNotBlank(param.getNo(), "请传入电表编号");
        IotAssert.isNotBlank(param.getSiteId(), "请传入场站编号");
        if (Boolean.TRUE.equals(param.getPowerLoss())) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDeviceMeterPoList()),
                "请传入绑定桩列表");
        }
        IotAssert.isNotNull(param.getOtherDevice(), "请传入是否计量其他大功率用电设备");

        if (NetType.TPT.equals(param.getNet())) {
            IotAssert.isNotBlank(param.getNo(), "请传入电表编号");
            IotAssert.isNotBlank(param.getGwno(), "请传入DTU编号");
            IotAssert.isNotBlank(param.getProtocol(), "请传入所属通信协议");
            IotAssert.isTrue(
                param.getProtocol().startsWith("DLT645") || param.getProtocol().startsWith("Modbus"),
                "请传入所属通信协议");
            if (param.getProtocol().startsWith("DLT645")) {
                IotAssert.isNotBlank(param.getDno(), "表号不能为空");
            } else if (param.getProtocol().startsWith("Modbus")) {
                IotAssert.isNotNull(param.getSid(), "通讯地址不可为空");
            }
        }

        MeterPo byId = meterRoDs.getById(param.getId());

        IotAssert.isNotNull(byId, "记录不存在，无法更新");

        // 是否需要下发新电表配置
        // FIXME 简单判断，根据全字段比较会更好
        boolean sendEssMq =
            NetType.S485.equals(byId.getNet()) || NetType.S485.equals(param.getNet());
        Set<String> gwnoSet = new HashSet<>();
        if (sendEssMq) {
            log.info("新{}->旧{}设置", param.getNet(), byId.getNet());
            if (StringUtils.isNotBlank(byId.getGwno())) {
                gwnoSet.add(byId.getGwno());// old gwno
            }
            if (StringUtils.isNotBlank(param.getGwno())) {
                gwnoSet.add(param.getGwno());// new gwno
            }
        }

        Long deviceId = null;
        if (CollectionUtils.isNotEmpty(param.getDeviceMeterPoList())) {
            MeterEstimateType estimateType = param.getDeviceMeterPoList().get(0).getEstimateType();
            if (estimateType == null) {
                throw new DcArgumentException("计量对象类型不能传空");
            } else if (MeterEstimateType.PCS_AC_IN_OUT == estimateType) {
                IotAssert.isTrue(param.getDeviceMeterPoList().size() == 1, "计量对象数据异常");
                deviceId = Long.valueOf(param.getDeviceMeterPoList().get(0).getDeviceId());
            }
        }

        byId.setOtherDevice(param.getOtherDevice())
            .setComment(param.getComment())
            .setName(param.getName())
            .setNet(param.getNet())
            .setVendor(param.getVendor())
            .setGwno(param.getGwno())
            .setPowerLoss(param.getPowerLoss())
            .setProtocol(param.getProtocol())
            .setSid(param.getSid());

        meterRwDs.updateMeter(byId);

        log.info("删除{}个关系", evseMeterRwDs.deleteEvseMeter(byId.getId()));

        final MeterPo meterPo = byId;
        this.validateEvseMeter(param);

        if (CollectionUtils.isNotEmpty(param.getDeviceMeterPoList())) {
            evseMeterRwDs.batchInsert(
                param.getDeviceMeterPoList()
                    .stream()
                    .map(e -> {
                        IotAssert.isNotBlank(e.getDeviceId(), "请传入桩编号");
                        e.setMeterId(meterPo.getId());
                        return e;
                    })
                    .collect(Collectors.toList())
            );
        }

        if (sendEssMq && CollectionUtils.isNotEmpty(gwnoSet)) {
            log.info("修改的电表影响了控制器{}，下发电表配置到对应控制器", gwnoSet);
            gwnoSet.forEach(this::modifyEssMeterCfg);
        }
        if (sendEssMq && deviceId != null) {
            iotEssFeignClient.sendCfgByEquipId(deviceId)
                .subscribe();
        }

    }

    @Transactional
    public void deleteMeter(Long id) {
        IotAssert.isNotNull(id, "请传入id");

        MeterPo byId = meterRoDs.getById(id);

        IotAssert.isNotNull(byId, "记录不存在，无法删除");

        boolean sendEssMq = NetType.S485.equals(byId.getNet());
        String gwno = byId.getGwno();

        SiteTopologyRefPo bindRef = siteTopologyRefRoDs.getBindRefByDownIdAndType(id,
            TopologyType.METER);
        IotAssert.isNull(bindRef, "解绑失败：电表已绑定变压器，请解绑后重试。");

        byId.setSiteId("");
        byId.setGwno("");
        meterRwDs.updateMeter(byId);
        log.info("删除{}个关系", evseMeterRwDs.deleteEvseMeter(byId.getId()));

        if (sendEssMq && StringUtils.isNotBlank(gwno)) {
            log.info("修改的电表影响了控制器{}，下发电表配置到对应控制器", gwno);
            this.modifyEssMeterCfg(gwno);
        }
    }


    public void hbMeter(String deviceNo, String tracerId) {
        log.info("电表心跳: 目前心跳的用处为刷新状态，所以当前将触发注册逻辑，{}, traceId = {}",
            deviceNo, tracerId);

        this.registerMeter(deviceNo, tracerId);

    }

    public void registerMeter(String deviceNo, String tracerId) {
        log.info("电表注册: {}, traceId = {}", deviceNo, tracerId);
        if (StringUtils.isNotBlank(deviceNo)) {

            MeterPo byNo = meterRoDs.getByNo(deviceNo);
            if (byNo == null) {
                log.info("电表注册,未发现电表记录,将新建一个已删除的电表: traceId = {}", tracerId);
                byNo = new MeterPo();
                byNo.setName(deviceNo)
                    .setOtherDevice(Boolean.FALSE)
                    .setDno(deviceNo)   // 后续要改为由系统生成
                    .setNo(deviceNo)
                    .setSiteId("")
                    .setLastActiveTime(new Date())
                    .setStatus(MeterStatusType.ONLINE)
                    .setNet(NetType.M4G);
                meterRwDs.insertMeter(byNo);
                log.info("电表注册完成，新建成功");

            } else {

                log.info("电表注册,发现电表记录: traceId = {}", tracerId);
                if (!MeterStatusType.ONLINE.equals(byNo.getStatus())) {
                    log.info("电表注册,电表状态为{},更新状态为在线: traceId = {}", byNo.getStatus(),
                        tracerId);
                    byNo.setStatus(MeterStatusType.ONLINE);
                }
                byNo.setLastActiveTime(new Date());
                meterRwDs.updateMeter(byNo);
                log.info("电表注册完成，更新成功");

            }
        }
    }

    public int refreshMeterStatus(Integer timeGap) {
        log.info("刷新电表状态，超过{}秒未更新的电表设置为离线", timeGap);
        if (timeGap == null) {
            log.info("刷新电表状态,设置电表状态超时时间为:{}秒", METER_STATUS_TTL);
            timeGap = METER_STATUS_TTL;
        }

        int ret = meterRwDs.refreshMeterStatus(timeGap);
        log.info("刷新电表状态, {}", ret);
        return ret;
    }

    private Map<Long, MeterRecordPo> getReadingMap(List<MeterPo> allBindMeter, Date date) {
        List<MeterRecordPo> todayZeros = meterRecordRoDs.getReading(allBindMeter.stream()
            .map(MeterPo::getId)
            .collect(Collectors.toList()), date);
        Map<Long, MeterRecordPo> todayMap = getMeterRecordPoMap(todayZeros);
        return todayMap;

    }

    public Mono<List<BiMeterPo>> getPrevDayReading(Date date) {

        final Date fixDate = date == null ? new Date() : date;

        log.info("获取昨日抄表记录差");
//        List<MeterPo> allBindMeter = meterRoDs.getAllBindMeter();

        return Mono.just(meterRoDs.getAllBindMeter()).flatMap(e -> {
            if (e.isEmpty()) {
                return Mono.empty();
            } else {
                Date today = fixDate;
                Date yesterday = DateUtil.getPrevDate(today);
                Mono<Tuple2<Map<Long, MeterRecordPo>, Map<Long, MeterRecordPo>>> tuple =
                    Mono.zip(Mono.just(getReadingMap(e, today)),
                        Mono.just(this.getReadingMap(e, yesterday)));
                return tuple.map(t -> {
                    Map<Long, MeterRecordPo> todayMap = t.getT1();
                    Map<Long, MeterRecordPo> yesterdayMap = t.getT2();
                    return todayMap.entrySet().stream().map(te -> {
                        MeterRecordPo meterRecordPo = yesterdayMap.get(te.getKey());
                        BiMeterPo biMeterPo = new BiMeterPo();
                        biMeterPo.setDate(DateUtil.getPrevDate(te.getValue().getReadingTime()))
                            .setMeterId(te.getValue().getMeterId())
                            .setSiteId(te.getValue().getSiteId());
                        if (meterRecordPo != null) {
                            biMeterPo.setElectricity(te.getValue().getPositiveTotal()
                                .subtract(meterRecordPo.getPositiveTotal()));
                        } else {
                            // 昨日数据不存在
//                            biMeterPo.setElectricity(te.getValue().getPositiveTotal());
                            return null;
                        }
                        return biMeterPo;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                });
            }
        });

//        if(CollectionUtils.isNotEmpty(allBindMeter)) {
//
//            Mono.just()
//
//            Date today = DateUtil.getThisDate(new Date());
//            Map<Long, MeterRecordPo> todayMap = getReadingMap(allBindMeter, today);
//
//            Date yesterday = DateUtil.getPrevDate(new Date());
//            Map<Long, MeterRecordPo> yesterdayMap = this.getReadingMap(allBindMeter, yesterday);
//
//
//            return todayMap.entrySet().stream().map(e -> {
//                MeterRecordPo meterRecordPo = yesterdayMap.get(e.getKey());
//                BiMeterPo biMeterPo = new BiMeterPo();
//                biMeterPo.setDate(DateUtil.getPrevDate(e.getValue().getReadingTime()))
//                        .setMeterId(e.getValue().getMeterId())
//                        .setSiteId(e.getValue().getSiteId());
//                if(meterRecordPo != null) {
//                    biMeterPo.setElectricity(e.getValue().getPositiveTotal().subtract(meterRecordPo.getPositiveTotal()));
//                } else {
//                    biMeterPo.setElectricity(e.getValue().getPositiveTotal());
//                }
//                return biMeterPo;
//            }).collect(Collectors.toList());
//        }

//        return List.of();
    }

    public List<DeviceMeterPo> getEvseMeterList(Long meterId) {
        return evseMeterRoDs.getEvseMeterList(meterId);
    }

    public List<MeterVo> findMeterList(MeterListParam param) {
        return meterRoDs.getMeterVoList(param);
    }

    private Map<Long, MeterRecordPo> getMeterRecordPoMap(List<MeterRecordPo> list) {
        Map<Long, MeterRecordPo> meterReadingCtx = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                if (meterReadingCtx.get(e.getId()) == null) {
                    meterReadingCtx.put(e.getMeterId(), e);
                }
            });
        }
        return meterReadingCtx;
    }

    public Mono<BaseGwResponse> saveRtAndHisData(MeterRtInfo meterRtInfo) {
        IotAssert.isTrue(
            meterRtInfo != null && meterRtInfo.getDno() != null && meterRtInfo.getGwno() != null,
            "表号或者网关编号不能为空");
        IotAssert.isNotNull(meterRtInfo.getData(), "数据不能为空");
        IotAssert.isNotNull(meterRtInfo.getTs(), "时间不能为空");

        // 推送到redis中
        MeterRtData meterRtData = new MeterRtData();
        meterRtData.setAbc(meterRtInfo.getData().getAbc());
        meterRtData.setDno(meterRtInfo.getDno());
        meterRtData.setTr(null);
        String pattern = "yyyy-MM-dd HH:mm:ss.SSS";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            Date date = sdf.parse(meterRtInfo.getTs());
            meterRtData.setTime(date.getTime());
        } catch (Exception e) {
            log.error("时间戳转换异常");
            return Mono.just(new BaseGwResponse());
        }
        meterRtData.setKwhL1(meterRtInfo.getData().getKwhL1());

        MeterKwhItem meterKwhItem = new MeterKwhItem();
        meterKwhItem.setTotal(BigDecimal.ZERO);

        // 正向有功总电能，没有ABC三相，只有总值
        if (meterRtInfo.getData().getKwh() != null
            && meterRtInfo.getData().getKwh().getPositive() != null) {
            meterKwhItem.setTotal(meterRtInfo.getData().getKwh().getPositive().getTotal());
        }
        MeterKhwData meterKhwData = new MeterKhwData();
        meterKhwData.setPositive(meterKwhItem);
        meterRtData.setKwh(meterKhwData);

        // 反向有功总电能和组合有功总电能都没值
        meterRtData.getKwh().setCombine(new MeterKwhItem());
        meterRtData.getKwh().setNegative(new MeterKwhItem());
        meterRedisService.pushRtData(meterRtInfo.getDno(), meterRtData);

        // 更新电表状态
        MeterPo byNo = meterRoDs.getByNo(meterRtInfo.getDno());
        if (byNo != null) {
            byNo.setLastActiveTime(new Date())
                .setStatus(MeterStatusType.ONLINE);
            meterRwDs.updateMeter(byNo);

            // 加入可以获取抄表统计的dno列表
            meterChannelRepository.softPut(meterRtInfo.getDno(), null);

            // 更新电量数据
            Mono<BaseResponse> responseMono = bizDataCoreFeignClient.saveMeterRtAndHisData(
                byNo.getSiteId(),
                meterRtInfo.getGwno(),
                meterRtInfo.getDno(),
                meterRtInfo.getTs(),
                meterRtInfo.getData());

            // 处理响应
            responseMono.subscribe(response -> {
                // 处理响应结果
                log.debug("调用保存电表上传的实时和历史数据的接口成功：{} ", response);
            }, error -> {
                // 处理错误
                log.error("调用保存电表上传的实时和历史数据的接口失败：{}, 入参是: {} ",
                    error.getMessage(), meterRtInfo);
            });
        } else {
            log.error("找不到电表: {}, 无法更新状态", meterRtInfo.getDno());
        }

        return Mono.just(new BaseGwResponse());
    }

    public ListResponse<SiteMeterVo> getSiteMeterList(MeterListParam param) {
        // 分页按照场站id维度
        List<String> siteIdList = meterRoDs.getSiteIdList(param);
        Long total = siteIdList != null ? (long) siteIdList.size() : 0L;
        if (Boolean.TRUE.equals(param.getTotal())) {
            total = meterRoDs.getSiteIdListTotal(param);
        }

        // 填充场站对应的电表信息
        MeterListParam meterListParam = new MeterListParam();
        meterListParam.setIdChain(param.getIdChain());
        meterListParam.setSiteIdList(siteIdList);
        List<MeterEvseVo> meterEvseVoList = this.getMeterList(meterListParam);

        // 对拿到的电表信息进行筛选，组合需要返回的结果
        List<SiteMeterVo> siteMeterVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(siteIdList) && CollectionUtils.isNotEmpty(meterEvseVoList)) {
            siteIdList.forEach(siteId -> {
                List<MeterEvseVo> siteMeterEvseVoList = meterEvseVoList.stream()
                    .filter(meterEvseVo -> meterEvseVo.getSiteId().equals(siteId)).toList();
                SiteMeterVo siteMeterVo = new SiteMeterVo();
                siteMeterVo.setSiteId(siteId)
                    .setMeterEvseList(siteMeterEvseVoList);
                siteMeterVoList.add(siteMeterVo);
            });
        }
        return new ListResponse<>(siteMeterVoList, total);
    }

    public ListResponse<String> getSiteIdWithMeterList(List<String> siteIdList) {
        MeterListParam meterListParam = new MeterListParam();
        meterListParam.setSiteIdList(siteIdList);
        List<String> siteIdWithMeterList = meterRoDs.getSiteIdList(meterListParam);
        if (CollectionUtils.isNotEmpty(siteIdWithMeterList)) {
            return new ListResponse<>(siteIdWithMeterList,
                Long.valueOf(siteIdWithMeterList.size()));
        } else {
            return new ListResponse<>(new ArrayList<>(), 0L);
        }
    }
}