package com.cdz360.iot.meter.yunji.qgdw376dot1.model;

import com.cdz360.iot.meter.yunji.qgdw376dot1.AddOneIntUnaryOperator;
import io.netty.channel.Channel;
import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

@Data
public class QGDW376Dot1Connection {
    private AtomicInteger PSEQ = new AtomicInteger();

    private AtomicInteger RSEQ = new AtomicInteger();

    private Channel channel;

    private String address;

    // 集中器编号
    private String dno;

    // 电表编号
    private String no;

    // 电流变比
    private int ctr;

    // 电压变比
    private int vtr;

    // 场站id
    private String siteId;

    // 名称
    private String name;

    public int getAndUpdatePSEQ() {
        return this.PSEQ.getAndUpdate(AddOneIntUnaryOperator.getInstance());
    }

    public int getAndUpdateRSEQ() {
        return this.RSEQ.getAndUpdate(AddOneIntUnaryOperator.getInstance());
    }
}
