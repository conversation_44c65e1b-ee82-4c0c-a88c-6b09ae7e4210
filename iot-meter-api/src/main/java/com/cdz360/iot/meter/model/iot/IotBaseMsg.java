package com.cdz360.iot.meter.model.iot;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.meter.model.type.CmdCode;
import com.cdz360.iot.meter.utils.ByteUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.netty.channel.Channel;
import lombok.Data;

import java.io.ByteArrayOutputStream;

@Data
public class IotBaseMsg {

    public static final int HEAD_LENGTH = 9;
    public static final int TAIL_LENGTH = 2;

    private String traceId;

    @JsonIgnore
    private Channel channel;

    private String channelKey;


    // 12个数字的BCD编码， 6字节
    private String deviceNo;

//    // 设备序号， 3字节
//    private byte[] deviceNo = new byte[3];
//
//    // 设备类型, 1字节
//    private byte deviceType;
//
//    // 生产时间，2字节
//    private byte[] produceTime = new byte[2];

    // 控制码， 1字节
    private CmdCode cmdCode;

    // 数据域长度， 1字节
    private int length;

    // 数据域
    private byte[] data;

    // 校验码，1字节
    private byte cs;

    public byte[] buildData() {
        return  this.data;
    }

    public IotBaseMsg buildRes(CmdCode cmdCodeIn, byte[] dataIn) {
        IotBaseMsg res = new IotBaseMsg();
        res.setTraceId(this.traceId);
        res.setChannelKey(this.channelKey);
        res.setDeviceNo(this.deviceNo);
//        res.setDeviceType(this.deviceType);
//        res.setProduceTime(this.produceTime);

        res.setCmdCode(cmdCodeIn);
        if (dataIn == null) {
            res.setLength(0);
        } else {
            res.setLength(dataIn.length);
            res.setData(dataIn);
        }
        res.setCs(this.cs);
        return res;
    }


    public byte[] toBytes() {
        int bufSize = HEAD_LENGTH + TAIL_LENGTH;
        byte[] data = this.buildData();
        int dataLength = 0;
        if(data != null) {
            dataLength = data.length;
            bufSize += dataLength;
        }
        ByteArrayOutputStream buf = new ByteArrayOutputStream(bufSize);
        buf.write(0x68);

//        if(is68H) {
//            byte[] bufA = {(byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA, (byte)0xAA};
//            buf.write(bufA, 0, 6);
//            buf.write((byte)0x00);
//            buf.write((byte)0x10);
//            buf.write((byte)0x68);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0xAA);
//            buf.write((byte)0x68);
//        }else {

            buf.write(ByteUtil.bcdLittle(this.deviceNo), 0, 6);
//            buf.write(this.deviceNo, 0, 3);
//            buf.write(this.deviceType);
//            buf.write(this.produceTime, 0, 2);
//        }
        buf.write(this.cmdCode.getCode());

        buf.write((byte) dataLength);
        if (dataLength > 0) {
            buf.write(data, 0, dataLength);
        }
//        if(is68H) {
//            buf.write(0xAD);
//            buf.write(0x16);
//        }
        int cs = 0;
        for (byte b : buf.toByteArray()) {
            cs += (b & 0xFF);
        }
        buf.write((cs % 256) & 0xFF);
        buf.write(0x16);
        return buf.toByteArray();
    }

    @Override
    public String toString() {
        //return ByteUtil.bytesToHex(this.toBytes());
        return JsonUtils.toJsonString(this);
    }
}
