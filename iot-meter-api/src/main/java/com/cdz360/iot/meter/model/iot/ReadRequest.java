package com.cdz360.iot.meter.model.iot;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReadRequest extends IotBaseMsg{

    private byte di3;

    private byte di2;

    private byte di1;

    private byte di0;

    @Override
    public byte[] buildData() {
        byte[] buf = new byte[4];
        buf[0] = di0;
        buf[1] = di1;
        buf[2] = di2;
        buf[3] = di3;
        return buf;
    }
}
