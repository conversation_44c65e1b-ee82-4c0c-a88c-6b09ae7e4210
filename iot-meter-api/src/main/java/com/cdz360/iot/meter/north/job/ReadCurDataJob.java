package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.meter.model.app.CurMeterData;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645PowerResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 超当前电表读数任务
 */
@Slf4j
public class ReadCurDataJob extends AbstractJob {



    @Getter
    private CurMeterData curMeterData = new CurMeterData();

    private final List<P645DataType> DATA_TYPE_LIST = List.of(P645DataType.POWER_COMBINE,
            P645DataType.POWER_POSITIVE, P645DataType.POWER_NEGATIVE,
            P645DataType.IDLE_POWER_1, P645DataType.IDLE_POWER_2);


    public ReadCurDataJob(MeterChannelRepository meterRepository, String deviceNo) {
        super(meterRepository, deviceNo);
    }

    @Override
    public List<P645DataType> getDataTypeList() {
        return DATA_TYPE_LIST;
    }

    @Override
    public void fillData(P645BaseMsg msgIn) {
        P645PowerResponse msg = (P645PowerResponse)msgIn;
        if (msg.getDataType() == P645DataType.POWER_COMBINE) {
            this.curMeterData.setCombineTotal(msg.getTotal())
                    .setCombineA(msg.getV1())
                    .setCombineB(msg.getV2())
                    .setCombineC(msg.getV3())
                    .setCombineD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.POWER_POSITIVE) {
            this.curMeterData.setPositiveTotal(msg.getTotal())
                    .setPositiveA(msg.getV1())
                    .setPositiveB(msg.getV2())
                    .setPositiveC(msg.getV3())
                    .setPositiveD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.POWER_NEGATIVE) {
            this.curMeterData.setNegativeTotal(msg.getTotal())
                    .setNegativeA(msg.getV1())
                    .setNegativeB(msg.getV2())
                    .setNegativeC(msg.getV3())
                    .setNegativeD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.IDLE_POWER_1) {
            this.curMeterData.setPositiveIdleTotal(msg.getTotal())
                    .setPositiveIdleA(msg.getV1())
                    .setPositiveIdleB(msg.getV2())
                    .setPositiveIdleC(msg.getV3())
                    .setPositiveIdleD(msg.getV4());
        } else if (msg.getDataType() == P645DataType.IDLE_POWER_2) {
            this.curMeterData.setNegativeIdleTotal(msg.getTotal())
                    .setNegativeIdleA(msg.getV1())
                    .setNegativeIdleB(msg.getV2())
                    .setNegativeIdleC(msg.getV3())
                    .setNegativeIdleD(msg.getV4());
        }
    }


    @Override
    public void finish() {
        this.getMonoSink().success(this.curMeterData);
    }
}
