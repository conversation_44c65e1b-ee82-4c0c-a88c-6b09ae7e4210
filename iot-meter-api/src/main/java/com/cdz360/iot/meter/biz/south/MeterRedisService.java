package com.cdz360.iot.meter.biz.south;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.meter.vo.MeterRtData;
import com.cdz360.iot.model.pv.dto.PvRtDataDto;
import com.cdz360.iot.model.pv.vo.RedisPvRtData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MeterRedisService {

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRE_REDIS_KEY = "meter:";


    @Autowired
    private StringRedisTemplate redisTemplate;


    /**
     * 今天光伏运行时数据压入
     *
     * @param dno  电表唯一编号
     * @param data 数据
     */
    public void pushRtData( String  dno, MeterRtData data) {
        String key = formatKey( dno, LocalDate.now());

//        Boolean hasKey = redisTemplate.hasKey(key);

        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(data));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);

//        if (Boolean.FALSE.equals(hasKey)) {
//            redisTemplate.expire(key, 1, TimeUnit.DAYS);
//        }
    }

    /**
     * 获取最近一条运行时数据
     *
     */
    public MeterRtData latestRtData( String  dno, LocalDate date) {
        String key = formatKey( dno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, MeterRtData.class);
    }

    private static String formatKey( String  dno, LocalDate date) {
        return PRE_REDIS_KEY +  dno + ":" + date.format(DATE_FORMATTER);
    }


}
