package com.cdz360.iot.meter.yunji;

import com.cdz360.iot.meter.south.IotEncoder;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component("yJChannelInitializer")
public class YJChannelInitializer extends ChannelInitializer<SocketChannel> {

    private static final IotEncoder ENCODER = new IotEncoder();

    private IotEncoder encoder = new IotEncoder();


    @Autowired
    @Qualifier("yJChannelHandler")
    private ChannelInboundHandlerAdapter nettyChannelHandler;

    private static final EventExecutorGroup group;

    static {
        final int nThreads = Runtime.getRuntime().availableProcessors();
        group = new DefaultEventExecutorGroup(nThreads * 5);
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        var timeout = 300;
        pipeline.addLast(new IdleStateHandler(timeout, 0, 0, TimeUnit.MINUTES));
        YJDecoder decoder = new YJDecoder();
        pipeline.addLast(decoder);
        pipeline.addLast(encoder);
        pipeline.addLast(group, "handler", nettyChannelHandler);// 将I/O线程和业务线程分开
    }
}
