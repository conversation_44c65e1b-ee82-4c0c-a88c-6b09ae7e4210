package com.cdz360.iot.meter.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.meter.model.app.CurMeterRealData;
import com.cdz360.iot.meter.model.app.CurPowerData;
import com.cdz360.iot.meter.model.app.CurPowerDataEx;
import com.cdz360.iot.meter.north.biz.MeterBiService;
import com.cdz360.iot.meter.north.biz.MeterBizDataFacade;
import com.cdz360.iot.meter.north.biz.MeterBizService;
import com.cdz360.iot.meter.north.biz.MeterDataService;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.meter.dto.BiSiteMeterSummaryDto;
import com.cdz360.iot.model.meter.dto.MeterRtInfo;
import com.cdz360.iot.model.meter.po.BiMeterPo;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import com.cdz360.iot.model.meter.vo.MeterEvseVo;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.meter.vo.SiteMeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import com.cdz360.iot.model.param.MeterRecordBiParam;
import io.swagger.v3.oas.annotations.Operation;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping(value = "/meter", produces = MediaType.APPLICATION_JSON_VALUE)
public class MeterRest {


    @Autowired
    private MeterBizService meterBizService;

    @Autowired
    private MeterDataService meterDataService;

    @Autowired
    private MeterBizDataFacade meterBizDataFacade;

    @Autowired
    private MeterBiService meterBiService;

    private static final long TIME_OUT_SECOND = 60;

    /**********************************************************************************
     * 电表设备接口
     **********************************************************************************/

    @GetMapping(value = "/getMeterList")
    public Mono<ListResponse<String>> getMeterList() {
        log.info("获取电表列表");
        return meterBizService.getMeterList()
            .map(l -> RestUtils.buildListResponse(l));
    }

    @GetMapping(value = "/getCurMeter")
    public Mono<ObjectResponse<CurMeterRealData>> getCurMeter(
        @RequestParam(value = "deviceNo") String deviceNo) {
        log.info("获取当前电表读数");

        return meterBizService.getCurMeter(deviceNo)
            .map(data -> RestUtils.buildObjectResponse(data));
    }

    @GetMapping(value = "/getVoltage")
    public Mono<ObjectResponse<CurPowerData>> getVoltage(
        @RequestParam(value = "deviceNo") String deviceNo) {
        log.info("获取当前电表电流/电压/瞬时功率");

        return meterBizService.getVoltage(deviceNo)
            .map(data -> RestUtils.buildObjectResponse(data));
    }

//    @GetMapping(value = "/getTransformerRate")
//    public Mono<ObjectResponse<TransformerCurrentData>> getTransformerRate(@RequestParam(value = "deviceNo") String deviceNo) {
//        log.info("获取当前电表互感器变比");
//
//        return meterBizService.getTransformerRate(deviceNo)
//                .map(data -> RestUtils.buildObjectResponse(data));
//    }
//
//    @GetMapping(value = "/getLastDayMeter")
//    public Mono<ObjectResponse<LastDayMeterData>> getLastDayMeter(@RequestParam(value = "deviceNo") String deviceNo) {
//        log.info("获取电表上一结算日读数");
//
//        return meterBizService.getLastDayMeter(deviceNo)
//                .map(data -> RestUtils.buildObjectResponse(data));
//    }


    /**********************************************************************************
     * 电表数据接口
     **********************************************************************************/

    @PostMapping(value = "/getMeterList")
    public ListResponse<MeterEvseVo> getMeterList(@RequestBody MeterListParam param) {
        log.info("获取电表列表: {}", JsonUtils.toJsonString(param));

        List<MeterEvseVo> meterList = meterDataService.getMeterList(param);

        return new ListResponse<>(meterList, meterDataService.getMeterListTotal(param));
    }

    @PostMapping(value = "/createMeter")
    public BaseResponse createMeter(@RequestBody MeterEvseVo param) {
        log.info("创建电表: {}", JsonUtils.toJsonString(param));

        meterDataService.createMeter(param);

        return BaseResponse.success();
    }

    @PostMapping(value = "/updateMeter")
    public BaseResponse updateMeter(@RequestBody MeterEvseVo param) {
        log.info("修改电表: {}", JsonUtils.toJsonString(param));

        meterDataService.updateMeter(param);

        return BaseResponse.success();
    }

    @PostMapping(value = "/deleteMeter")
    public BaseResponse deleteMeter(@RequestParam(value = "id") Long id) {
        log.info("删除电表: {}", id);

        meterDataService.deleteMeter(id);

        return BaseResponse.success();
    }

    @GetMapping(value = "/refreshMeterStatus")
    public ObjectResponse<Integer> refreshMeterStatus(
        @RequestParam(value = "ttl", required = false) Integer ttl) {
        log.info("刷新电表状态: {} 秒", ttl);
        return new ObjectResponse<>(meterDataService.refreshMeterStatus(ttl));
    }

    @GetMapping(value = "/getMeterReading")
    public Mono<ObjectResponse<CurPowerDataEx>> getMeterReading(
        @RequestParam(value = "meterNo") String meterNo) {
        log.info("读取电表当前读数: {}", meterNo);

//        meterDataService.init();
        return meterBizDataFacade.getMeterReading(meterNo)
            .map(e -> RestUtils.buildObjectResponse(e));

//        return BaseResponse.success();
    }

    @GetMapping(value = "/getCommodity")
    public Mono<ListResponse<Boolean>> getCommodity() {
        log.info("电表抄表, 抄取所有:");
        return meterBizDataFacade.getCommodity().collectList()
            .map(e -> RestUtils.buildListResponse(e));
    }

    @GetMapping(value = "/getCommoditySync")
    public ListResponse<Boolean> getCommoditySync() {
        log.info("电表抄表, 抄取所有, sync:");
        return meterBizDataFacade.getCommodity()
            .collectList()
            .map(e -> RestUtils.buildListResponse(e))
            .block(Duration.ofSeconds(TIME_OUT_SECOND));
    }

    @GetMapping(value = "/getPrevDayReading")
    Mono<ListResponse<BiMeterPo>> getPrevDayReading(@RequestParam(value = "date", required = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date) {
        log.info("获取最新的电表日差值");
        return meterDataService.getPrevDayReading(date).map(RestUtils::buildListResponse);
    }

    @GetMapping(value = "/getEvseMeterList")
    ListResponse<DeviceMeterPo> getEvseMeterList(@RequestParam(value = "meterId") Long meterId) {
        log.info("获取电表绑定桩列表");
        return new ListResponse<>(meterDataService.getEvseMeterList(meterId));
    }

    @PostMapping(value = "/findMeterList")
    ListResponse<MeterVo> findMeterList(@RequestBody MeterListParam param) {
        log.info("电表列表-gwno: {}", JsonUtils.toJsonString(param));
        return new ListResponse<>(meterDataService.findMeterList(param));
    }

    @PostMapping(value = "/getBiSiteMeterRecord")
    ListResponse<BiSiteMeterSummaryDto> getBiSiteMeterRecord(
        @RequestBody MeterRecordBiParam param) {
        log.info("抄表统计: {}", JsonUtils.toJsonString(param));
        return meterBiService.getBiSiteMeterRecord(param);
    }

    @Operation(summary = "保存电表实时和上月数据")
    @PostMapping(value = "/saveRtAndHisData")
    public Mono<BaseGwResponse> saveRtAndHisData(
        @RequestBody String gwReq) {
        log.info("保存电表实时和上月数据。req = {}", gwReq);
        long startTime = System.nanoTime();    // debug 性能问题

        IotAssert.isTrue(gwReq != null, "数据不能为空");
        MeterRtInfo meterRtInfo = JsonUtils.fromJson(gwReq, MeterRtInfo.class);
        return meterDataService.saveRtAndHisData(meterRtInfo)
            .doOnNext(res1 -> {
                LogHelper.logLatency(log, MeterRest.class.getSimpleName(),
                    "saveRtAndHisData", "saveRtAndHisData", startTime);
            });
    }

    @PostMapping(value = "/getSiteMeterList")
    public ListResponse<SiteMeterVo> getSiteMeterList(@RequestBody MeterListParam param) {
        log.info("获取场站对应的电表列表: {}", JsonUtils.toJsonString(param));
        return meterDataService.getSiteMeterList(param);
    }

    /**
     * 获取有电表的siteIdList,返回的列表里只包含有电表的siteId,都没有时返回空数组
     *
     * @param siteIdList
     * @return
     */
    @PostMapping(value = "/getSiteIdWithMeterList")
    ListResponse<String> getSiteIdWithMeterList(@RequestBody List<String> siteIdList) {
        log.info("获取有电表的siteIdList: {}", JsonUtils.toJsonString(siteIdList));
        return meterDataService.getSiteIdWithMeterList(siteIdList);
    }

}
