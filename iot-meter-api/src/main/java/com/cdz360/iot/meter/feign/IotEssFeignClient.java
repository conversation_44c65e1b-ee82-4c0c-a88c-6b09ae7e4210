package com.cdz360.iot.meter.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.model.meter.dto.EssMeterCfg;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * @Classname IotEssFeignClient
 * @Description
 * @Date 11/4/2021 1:40 PM
 * @Created by Rafael
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_ESS,
        fallbackFactory = IotEssFeignHystrix.class)
public interface IotEssFeignClient {
    /**
     * 下发修改电表信息
     * @param param
     * @return
     */
    @PostMapping(value = "/iot/biz/ess/modifyEssMeter")
    Mono<BaseResponse> modifyEssMeter(@RequestBody EssMeterCfg param);

    // 下发ESS配置
    @PostMapping(value = "/iot/biz/ess/sendCfgByEquipId")
    Mono<BaseResponse> sendCfgByEquipId(@RequestParam(value = "equipId") Long equipId);
}