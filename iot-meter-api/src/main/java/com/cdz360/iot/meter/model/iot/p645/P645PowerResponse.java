package com.cdz360.iot.meter.model.iot.p645;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class P645PowerResponse extends P645BaseMsg {

    //private P645DataType dataType;

    private BigDecimal total;

    private BigDecimal v1;


    private BigDecimal v2;

    private BigDecimal v3;

    private BigDecimal v4;


    public P645PowerResponse() {

    }

    public P645PowerResponse(P645BaseMsg baseMsg) {
        super.setTraceId(baseMsg.getTraceId());
        super.setChannelKey(baseMsg.getChannelKey());
        super.setDeviceNo(baseMsg.getDeviceNo());
        super.setCmdCode(baseMsg.getCmdCode());
        super.setLength(baseMsg.getLength());
        super.setDataType(baseMsg.getDataType());
        super.setData(baseMsg.getData());
        super.setCs(baseMsg.getCs());
    }

//    public void parse() {
//        byte[] data = super.getData();
//        for (int i = 0; i < data.length; i++) {
//            data[i] = (byte) (data[i] - 0x33);
//        }
//        this.dataType = this.parseDataType(data[0], data[2]);
//
//        int idx = 4;
//        total = this.parseDecimalValue(data, idx);
//        idx+=4;
//
//        v1 = this.parseDecimalValue(data, idx);
//        idx+=4;
//
//        v2 = this.parseDecimalValue(data, idx);
//        idx+=4;
//
//        v3 = this.parseDecimalValue(data, idx);
//        idx+=4;
//
//        v4 = this.parseDecimalValue(data, idx);
//
//    }




}
