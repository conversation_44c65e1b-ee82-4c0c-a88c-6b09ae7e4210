package com.cdz360.iot.meter.north.job;

import com.cdz360.iot.meter.model.app.TransformerCurrentData;
import com.cdz360.iot.meter.model.iot.p645.P645BaseMsg;
import com.cdz360.iot.meter.model.iot.p645.P645TransformerCurrentResponse;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Classname ReadTransformerRateJob
 * @Description
 * @Date 10/14/2020 11:19 AM
 * @Created by Rafael
 */
@Slf4j
public class ReadTransformerRateJob extends AbstractJob {

    @Getter
    private TransformerCurrentData transformerCurrentData = new TransformerCurrentData();

    private final List<P645DataType> DATA_TYPE_LIST = List.of(
            P645DataType.TRANSFORMER_CUR_RATE
            //,
            //P645DataType.TRANSFORMER_VOL_RATE //FIXME 电压变比通常为1，目前无需获取
            );

    public ReadTransformerRateJob(MeterChannelRepository meterRepository, String deviceNo) {
        super(meterRepository, deviceNo);
    }

    @Override
    public List<P645DataType> getDataTypeList() {
        return DATA_TYPE_LIST;
    }

    @Override
    public void fillData(P645BaseMsg msg) {
        if(P645DataType.TRANSFORMER_CUR_RATE.equals(msg.getDataType())) {
            P645TransformerCurrentResponse p645TransformerCurrentResponse = (P645TransformerCurrentResponse) msg;
            transformerCurrentData.setCurrentRate(p645TransformerCurrentResponse.getRate());
        }
    }

    @Override
    public void finish() {
        this.getMonoSink().success(this.transformerCurrentData);
    }
}