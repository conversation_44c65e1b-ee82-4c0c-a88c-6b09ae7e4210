package com.cdz360.iot.meter.yunji.qgdw376dot1;

import cn.hutool.core.util.HexUtil;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.meter.utils.ByteUtil;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Constant;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Model;

import java.time.LocalDate;

public class QGDW376Dot1Encoder {

    public byte[] encode(QGDW376Dot1Model model) {
        StringBuilder sb = new StringBuilder();
        // 起始字符
        sb.append(QGDW376Dot1Constant.BEGIN_CHAR_STRING);
        // 2个长度
//        sb.append(ByteUtil.bytesToHex(model.lengthToBytes()));
        // 起始字符
        sb.append(QGDW376Dot1Constant.BEGIN_CHAR_STRING);
        // 用户区数据开始
        String userDate = "";
        // 控制域
//        userDate += ByteUtil.byteToHexStr(model.getControlFieldC());
        // 地址域
//        userDate += ByteUtil.byteToHexStr(model.getAddressFieldA());
        // 链路用户数据
        userDate += ByteUtil.bytesToHex(model.getData().toByteArray());
        // 用户区数据结束
        sb.append(userDate);
        // 校验和CS
        sb.append(ByteUtil.byteToHexStr(
                QGDW376Dot1Utils.calculateChecksum(
                        ByteUtil.hexToBytes(userDate)
                )
        ));
        // 结束字符
        sb.append(QGDW376Dot1Constant.END_CHAR_STRING);
        return ByteUtil.hexToBytes(sb.toString());
    }


    private static String encodeHeader(int length) {
        String l = QGDW376Dot1Utils.encodeLength(length);
        return "68" + l + l + "68";
    }

    private static String encodeFieldC(byte AFN) {
        String s = "";
        switch (AFN) {
            case (byte) 0x01:
                s = "4A";
                break;
            case (byte) 0x04:
                s = "4A";
                break;
            case (byte) 0x09:
                s = "4A";
                break;
            case (byte) 0x0A:
                s = "4A";
                break;
            case (byte) 0x0C:
                s = "4A";
                break;
            case (byte) 0x0D:
                s = "4A";
                break;
            default:
                throw new DcServiceException("fieldC暂不支持别的AFN: " + QGDW376Dot1Utils.encode1Byte(AFN));
        }
        return s;
    }

    private static String encodeSEQ(byte AFN, int count) {
        String s = "";
        switch (AFN) {
            case (byte) 0x01:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x04:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x09:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x0A:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x0C:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x0D:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            case (byte) 0x00:
                s = "6" + QGDW376Dot1Utils.formatTo1Hex(count);
                break;
            default:
                throw new DcServiceException("seq暂不支持别的AFN: " + QGDW376Dot1Utils.encode1Byte(AFN));
        }
        return s;
    }


    // encodeA21
    public static String getTimeFlag(int year, int month) {
        return QGDW376Dot1Utils.encodeBCD(month) + QGDW376Dot1Utils.encodeBCD(year);
    }

    public static String getMonthTimeFlag(LocalDate monthDate) {
        // 获取上个月的年份和月份
        int year = monthDate.getYear() % 100;
        int month = monthDate.getMonthValue();

        return getTimeFlag(year, month);
    }


    // 获取0C的命令通过fn和pn
    public static String get0CByFnPn(int fn, int pn, int pseq, String addressA) {
        byte byteAFN = (byte) 0x0C;
        String fieldC = encodeFieldC(byteAFN);
        String AFN = "0C";
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        // C、A是6, AFN、SEQ各是1, DA(PN) -> 2, DT(FN) -> 2
        int length = 6 + 2 + 4;
        String header = encodeHeader(length);
        String userData = fieldC + addressA + AFN + SEQ + flag;
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    // 获取0D的命令通过fn和pn以及时间时标
    public static String get0DByFnPn(int fn, int pn, int pseq, String addressA, String timeFlag) {
        byte byteAFN = (byte) 0x0D;
        String fieldC = encodeFieldC(byteAFN);
        String AFN = "0D";
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        String userData = fieldC + addressA + AFN + SEQ + flag + timeFlag;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    // 获取登陆确认命令
    public static String getConfirmLoginCmdBy00F3(byte[] msg, int rseq, boolean ACD) {
        StringBuilder sb = new StringBuilder();
        String fieldC = "30";
        String addressA = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[7], msg[8], msg[9], msg[10], msg[11]});
        int i = 12;
        byte AFN = msg[i];
        i += 2;
        int length = msg.length - 2;
        sb.append(QGDW376Dot1Utils.formatTo2Hex(AFN));
        while (i < length) {
            sb.append(QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]}));
            sb.append("00");
            i += 4;
        }
        String returnAFN = "00";
        String returnSEQ = encodeSEQ((byte) 0x00, rseq);

        // *******.3 事件计数器 EC
        String EC = "";
        if (ACD) {
            // 添加EC todo
        }

        // *******.4 时间标签 Tp todo
        String Tp = "";

        String userData = fieldC + addressA + returnAFN + returnSEQ + sb.toString();
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }

    public static String getConfirmHeartBeatCmdBy00F3(byte[] msg, int rseq, boolean ACD) {
        StringBuilder sb = new StringBuilder();
        String fieldC = "30";
        String addressA = QGDW376Dot1Utils.decodeAddress(new byte[]{msg[7], msg[8], msg[9], msg[10], msg[11]});
        int i = 12;
        byte AFN = msg[i];
        i += 2;
        int length = msg.length - 2;
        sb.append(QGDW376Dot1Utils.formatTo2Hex(AFN));
        while (i < length) {
            sb.append(QGDW376Dot1Utils.decodeAddress(new byte[]{msg[i], msg[i + 1], msg[i + 2], msg[i + 3]}));
            sb.append("00");
            i += 5;
        }
        String returnAFN = "00";
        String returnSEQ = encodeSEQ((byte) 0x00, rseq);
        String userData = fieldC + addressA + returnAFN + returnSEQ + sb.toString();
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    // 查询终端配置命令
    public static String getUserConfigCmdBy09Fn(int fn, int pseq, String addressA) {
        int pn = 0;
        byte byteAFN = (byte) 0x09;
        String AFN = "09";
        String fieldC = encodeFieldC(byteAFN);
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        String userData = fieldC + addressA + AFN + SEQ + flag;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    // 查询用户配置(电表)命令
    public static String getUserConfigCmdBy0AF10(int pseq, String addressA) {
        int fn = 10;
        int pn = 0;
        byte byteAFN = (byte) 0x0A;
        String AFN = "0A";
        String fieldC = encodeFieldC(byteAFN);
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        int num = 1;
        int no = 1;
        String numString = QGDW376Dot1Utils.encode2Byte(num);
        String noString = QGDW376Dot1Utils.encode2Byte(no);

        String userData = fieldC + addressA + AFN + SEQ + flag + numString + noString;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    public static String getUserConfigCmdBy0AFn(int fn, int pseq, String addressA) {
        int pn = 0;
        byte byteAFN = (byte) 0x0A;
        String AFN = "0A";
        String fieldC = encodeFieldC(byteAFN);
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        String userData = fieldC + addressA + AFN + SEQ + flag;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }

    // 636d6e65740000000000000000000000
    public static String encodeIpAndPortAll(String ipPort1, String ipPort2, String APN) {
        return encodeIpAndPort(ipPort1) + encodeIpAndPort(ipPort2) + APN;
    }


    // APN 通过查看命令去查看，不过应都是一样的。
    public static String getSettingIpAndPortCmd(String ipPort1, String ipPort2, String APN, int pseq, String addressA) {
        int fn = 3;
        int pn = 0;
        byte byteAFN = (byte) 0x04;
        String AFN = "04";
        String fieldC = encodeFieldC(byteAFN);
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        // 数据体1
        String body = encodeIpAndPortAll(ipPort1, ipPort2, APN);
        String userData = fieldC + addressA + AFN + SEQ + flag + body;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }


    private static String encodeIpAndPort(String ipPort) {
        String[] split = ipPort.split(":");
        String[] ips = split[0].split("\\.");
        int port = Integer.parseInt(split[1]);
        int a1 = Integer.parseInt(ips[0]);
        int a2 = Integer.parseInt(ips[1]);
        int a3 = Integer.parseInt(ips[2]);
        int a4 = Integer.parseInt(ips[3]);
        return QGDW376Dot1Utils.encode1Byte(a1) + QGDW376Dot1Utils.encode1Byte(a2) + QGDW376Dot1Utils.encode1Byte(a3) +
                QGDW376Dot1Utils.encode1Byte(a4) + QGDW376Dot1Utils.encode2Byte(port);
    }

    public static String getResetCmdByFn(int fn, int pseq, String addressA) {
        int pn = 0;
        byte byteAFN = (byte) 0x01;
        String AFN = "01";
        String fieldC = encodeFieldC(byteAFN);
        String SEQ = encodeSEQ(byteAFN, pseq);
        // 数据单元标识1
        String flag = QGDW376Dot1Utils.encodePN(pn) + QGDW376Dot1Utils.encodeFN(fn);
        String userData = fieldC + addressA + AFN + SEQ + flag;
        String header = encodeHeader(userData.length() / 2);
        // 转16进制，然后再转换
        String checkSum = QGDW376Dot1Utils.formatTo2Hex(
                QGDW376Dot1Utils.calculateChecksum(HexUtil.decodeHex(userData))
        );
        return header + userData + checkSum + "16";
    }
}
