package com.cdz360.iot.meter.north.biz;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.meter.biz.south.MeterRedisService;
import com.cdz360.iot.meter.model.app.*;
import com.cdz360.iot.meter.north.consumer.ReadCurDataService;
import com.cdz360.iot.meter.north.consumer.ReadCurPowerService;
import com.cdz360.iot.meter.north.consumer.ReadLastDayDataService;
import com.cdz360.iot.meter.north.consumer.ReadTransformerService;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import com.cdz360.iot.model.meter.vo.MeterRtData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MeterBizService {

    @Autowired
    private MeterChannelRepository meterRepository;

    @Autowired
    private ReadCurDataService readCurDataConsumer;

    @Autowired
    private ReadCurPowerService readCurPowerConsumer;

    @Autowired
    private ReadLastDayDataService readLastDayDataConsumer;

    @Autowired
    private ReadTransformerService readTransformerConsumer;

    @Autowired
    private MeterRedisService meterRedisService;

    public Mono<List<String>> getMeterList() {
        log.info(">> 获取当前在线电表列表");

        List<String> list = meterRepository.getDeviceNoList();

        log.info(">> 获取当前在线电表列表: {}",
                list.size() > 10 ? list.stream().limit(10).collect(Collectors.toList()) : list);

        return Mono.just(list);
    }

    public Mono<CurMeterRealData> getCurMeter(String deviceNo) {
        CurMeterRealData redisRt = this.getCurMeterByRedis(deviceNo);
        if (redisRt != null) {
            log.info("匹配到redis数据. deviceNo = {}", deviceNo);
            return Mono.just(redisRt);
        }
        if (meterRepository.get(deviceNo) == null) {
            log.warn("电表 {} 未在线", deviceNo);
            throw new DcServiceException("电表未在线");
        }

//        return readCurDataConsumer.process(deviceNo);

        return readTransformerConsumer.process(deviceNo)
                .flatMap(e -> readCurDataConsumer.process(deviceNo)
                        .map(ie -> {
                            Class cls = ie.getClass();
                            Field[] fields = cls.getDeclaredFields();
                            log.info("当前变比: {}", e.getCurrentRate());
                            for (int i = 0; i < fields.length; i++) {
                                Field f = fields[i];
                                f.setAccessible(true);
                                try {
                                    log.debug("属性名: {}, 属性值: {}", f.getName(), f.get(ie));
                                    if(e.getCurrentRate() != null && f.get(ie) != null) {
                                        f.set(ie, ((BigDecimal) f.get(ie)).multiply(e.getCurrentRate()));
                                    }
                                } catch (IllegalAccessException ex) {
                                    ex.printStackTrace();
                                }
                            }

                            CurMeterRealData curMeterRealData = new CurMeterRealData();
                            BeanUtils.copyProperties(ie, curMeterRealData);
                            curMeterRealData.setCurrentRate(e.getCurrentRate());
                            return curMeterRealData;
                        })
                );
    }

    /**
     * 从redis获取当前电表读数
     */
    public CurMeterRealData getCurMeterByRedis(String deviceNo) {
        MeterRtData rtData = this.meterRedisService.latestRtData(deviceNo, LocalDate.now());
        if (rtData == null) {
            return null;
        }
        CurMeterRealData rt = new CurMeterRealData();
        rt.setUpdateTime(rtData.getTime());
        if (rtData.getTr() != null) {
            rt.setCurrentRate(new BigDecimal(rtData.getTr().getCtr()));   // 电流互感器变比
        }
        if (rtData.getKwh() != null) {
            /**
             * 组合有功
             */
            if (rtData.getKwh().getCombine() != null) {
                rt.setCombineTotal(rtData.getKwh().getCombine().getTotal())
                        .setCombineA(rtData.getKwh().getCombine().getV1())
                        .setCombineB(rtData.getKwh().getCombine().getV2())
                        .setCombineC(rtData.getKwh().getCombine().getV3())
                        .setCombineD(rtData.getKwh().getCombine().getV4());
            }
            /**
             * 正向有功
             */
            if (rtData.getKwh().getPositive() != null) {
                rt.setPositiveTotal(rtData.getKwh().getPositive().getTotal())
                        .setPositiveA(rtData.getKwh().getPositive().getV1())
                        .setPositiveB(rtData.getKwh().getPositive().getV2())
                        .setPositiveC(rtData.getKwh().getPositive().getV3())
                        .setPositiveD(rtData.getKwh().getPositive().getV4());
            }
            /**
             * 反向有功
             */
            if (rtData.getKwh().getNegative() != null) {
                rt.setNegativeTotal(rtData.getKwh().getNegative().getTotal())
                        .setNegativeA(rtData.getKwh().getNegative().getV1())
                        .setNegativeB(rtData.getKwh().getNegative().getV2())
                        .setNegativeC(rtData.getKwh().getNegative().getV3())
                        .setNegativeD(rtData.getKwh().getNegative().getV4());
            }
            /**
             * 组合无功1
             */
            if (rtData.getKwh().getCombineReactive1() != null) {
                rt.setPositiveIdleTotal(rtData.getKwh().getCombineReactive1().getTotal())
                        .setPositiveIdleA(rtData.getKwh().getCombineReactive1().getV1())
                        .setPositiveIdleB(rtData.getKwh().getCombineReactive1().getV2())
                        .setPositiveIdleC(rtData.getKwh().getCombineReactive1().getV3())
                        .setPositiveIdleD(rtData.getKwh().getCombineReactive1().getV4());
            }
            /**
             * 组合无功2
             */
            if (rtData.getKwh().getCombineReactive2() != null) {
                rt.setNegativeIdleTotal(rtData.getKwh().getCombineReactive2().getTotal())
                        .setNegativeIdleA(rtData.getKwh().getCombineReactive2().getV1())
                        .setNegativeIdleB(rtData.getKwh().getCombineReactive2().getV2())
                        .setNegativeIdleC(rtData.getKwh().getCombineReactive2().getV3())
                        .setNegativeIdleD(rtData.getKwh().getCombineReactive2().getV4());
            }
            /**
             * 正向视在, 已乘变比
             */
            if (rtData.getKwh().getPositiveApparent() != null) {
                rt.setPositiveApparentTotal(rtData.getKwh().getPositiveApparent().getTotal())
                        .setPositiveApparentA(rtData.getKwh().getPositiveApparent().getV1())
                        .setPositiveApparentB(rtData.getKwh().getPositiveApparent().getV2())
                        .setPositiveApparentC(rtData.getKwh().getPositiveApparent().getV3())
                        .setPositiveApparentD(rtData.getKwh().getPositiveApparent().getV4());
            }
            /**
             * 反向视在, 已乘变比
             */
            if (rtData.getKwh().getNegativeApparent() != null) {
                rt.setNegativeApparentTotal(rtData.getKwh().getNegativeApparent().getTotal())
                        .setNegativeApparentA(rtData.getKwh().getNegativeApparent().getV1())
                        .setNegativeApparentB(rtData.getKwh().getNegativeApparent().getV2())
                        .setNegativeApparentC(rtData.getKwh().getNegativeApparent().getV3())
                        .setNegativeApparentD(rtData.getKwh().getNegativeApparent().getV4());
            }
        }
        return rt;
    }

    public Mono<CurPowerDataEx> getMeterReading(String deviceNo) {
        return getVoltage(deviceNo).flatMap(e -> {
//            log.debug("meter abc = {}", e);
            return getCurMeter(deviceNo).map(ie -> {
                CurPowerDataEx ret = new CurPowerDataEx();
                BeanUtils.copyProperties(e, ret);
                ret.setCombineTotal(ie.getCombineTotal())
                        .setCombineA(ie.getCombineA()).
                        setCombineB(ie.getCombineB()).
                        setCombineC(ie.getCombineC()).
                        setCombineD(ie.getCombineD())
                        .setNegativeTotal(ie.getNegativeTotal())
                        .setPositiveA(ie.getPositiveA()).
                        setPositiveB(ie.getPositiveB()).
                        setPositiveC(ie.getPositiveC()).
                        setPositiveD(ie.getPositiveD())
                        .setPositiveTotal(ie.getPositiveTotal())
                        .setNegativeA(ie.getNegativeA()).
                        setNegativeB(ie.getNegativeB()).
                        setNegativeC(ie.getNegativeC()).
                        setNegativeD(ie.getNegativeD())
                        .setUpdateTime(ie.getUpdateTime());

//                log.debug("meter ret = {}", ret);
                return ret;
            });
        });
    }

    public Mono<CurPowerData> getVoltage(String deviceNo) {
        CurPowerData redisRt = this.getVoltageByRedis(deviceNo);
        if (redisRt != null) {
            log.info("匹配到redis数据. deviceNo = {}", deviceNo);
            return Mono.just(redisRt);
        }
        if (meterRepository.get(deviceNo) == null) {
            log.warn("电表 {} 未在线", deviceNo);
            throw new DcServiceException("电表未在线");
        }

//        return readCurPowerConsumer.process(deviceNo);

        return readTransformerConsumer.process(deviceNo)
                .flatMap(e ->
                        readCurPowerConsumer.process(deviceNo)
                                .map(ie ->
                                        ie.setCurrentA(ie.getCurrentA().multiply(e.getCurrentRate()))
                                                .setCurrentB(ie.getCurrentB().multiply(e.getCurrentRate()))
                                                .setCurrentC(ie.getCurrentC().multiply(e.getCurrentRate()))
                                                .setPowerTotal(ie.getPowerTotal().multiply(e.getCurrentRate()))
                                                .setPowerA(ie.getPowerA().multiply(e.getCurrentRate()))
                                                .setPowerB(ie.getPowerB().multiply(e.getCurrentRate()))
                                                .setPowerC(ie.getPowerC().multiply(e.getCurrentRate()))
                                                .setIdleTotal(ie.getIdleTotal().multiply(e.getCurrentRate()))
                                                .setIdleA(ie.getIdleA().multiply(e.getCurrentRate()))
                                                .setIdleB(ie.getIdleB().multiply(e.getCurrentRate()))
                                                .setIdleC(ie.getIdleC().multiply(e.getCurrentRate()))
                                )
                );
    }


    public CurPowerData getVoltageByRedis(String deviceNo) {
        MeterRtData rtData = this.meterRedisService.latestRtData(deviceNo, LocalDate.now());
        if (rtData == null) {
            return null;
        }
        CurPowerData rt = null;
        /**
         * 电压， 1位小数
         */
        if (rtData.getAbc() != null) {
            rt = new CurPowerData();
            rt.setVoltageA(rtData.getAbc().getVoltage().getV1())
                    .setVoltageB(rtData.getAbc().getVoltage().getV2())
                    .setVoltageC(rtData.getAbc().getVoltage().getV3())
                    /**
                     * 电流， 3位小数
                     */
                    .setCurrentA(rtData.getAbc().getCurrent().getV1())
                    .setCurrentB(rtData.getAbc().getCurrent().getV2())
                    .setCurrentC(rtData.getAbc().getCurrent().getV3())
                    /**
                     * 瞬时有功功率， 3位小数
                     */
                    .setPowerTotal(rtData.getAbc().getActivePower().getTotal())
                    .setPowerA(rtData.getAbc().getActivePower().getV1())
                    .setPowerB(rtData.getAbc().getActivePower().getV2())
                    .setPowerC(rtData.getAbc().getActivePower().getV3())
                    /**
                     * 无功功率， 3位小数
                     */
                    .setIdleTotal(rtData.getAbc().getReactivePower().getTotal())
                    .setIdleA(rtData.getAbc().getReactivePower().getV1())
                    .setIdleB(rtData.getAbc().getReactivePower().getV2())
                    .setIdleC(rtData.getAbc().getReactivePower().getV3());
        }
        return rt;
    }

    public Mono<TransformerCurrentData> getTransformerRate(String deviceNo) {

        if (meterRepository.get(deviceNo) == null) {
            log.warn("电表 {} 未在线", deviceNo);
            throw new DcServiceException("电表未在线");
        }
        return readTransformerConsumer.process(deviceNo);
    }

    public Mono<LastDayMeterData> getLastDayMeter(String deviceNo) {

        if (meterRepository.get(deviceNo) == null) {
            log.warn("电表 {} 未在线", deviceNo);
            throw new DcServiceException("电表未在线");
        }
        return readLastDayDataConsumer.process(deviceNo);
    }

}
