package com.cdz360.iot.meter.south;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;
import java.net.InetSocketAddress;

/**
 * netty tcp server
 */
@Component
public class NettyTcpServer {
    @Autowired
    @Resource(name = "yunjiServerBootstrap")
    private ServerBootstrap serverBootstrap;

    @Autowired
    private InetSocketAddress tcpPort;


    private Channel serverChannel;


    public void start() throws Exception {
        serverChannel = serverBootstrap.bind(tcpPort).sync().channel().closeFuture().sync().channel();
    }

    @PreDestroy
    public void stop() {
        if (serverChannel != null) {
            serverChannel.close();
            serverChannel.parent().close();
        }
    }
}
