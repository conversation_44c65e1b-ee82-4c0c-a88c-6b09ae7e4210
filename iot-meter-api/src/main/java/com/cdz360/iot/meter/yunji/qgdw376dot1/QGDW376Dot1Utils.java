package com.cdz360.iot.meter.yunji.qgdw376dot1;

import cn.hutool.core.util.HexUtil;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1Connection;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1FieldC;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QGDW376Dot1Utils {

    /**
     * 计算校验和，只保留低8位
     *
     * @param data 原始数据
     * @return 校验和
     */
    public static byte calculateChecksum(byte[] data) {
        int sum = 0;

        // 遍历字节数组，逐个相加，并丢弃溢出
        for (byte b : data) {
            sum += ((int) b & 0xFF); // 将byte转换为无符号整数
            sum = sum & 0xFF;  // 只保留低8位
        }
        return (byte) sum; // 返回算术和
    }

    /**
     * 解析信息点DA，为PN
     *
     * @param bytes 2个字节
     * @return 序号
     */
    public static int decodePN(byte[] bytes) {
        int i = bytes[1] - 1;
        if (i < 0) {
            i = 0;
        }
        int result = parseByteWhereIs1(bytes[0]) + i * 8;
        return result;
    }


    /**
     * 解析信息点DT，为FN
     *
     * @param bytes 2个字节
     * @return 序号
     */
    public static int decodeFN(byte[] bytes) {
        int result = parseByteWhereIs1(bytes[0]) + (bytes[1] * 8);
        return result;
    }


    /**
     * 信息点DA
     *
     * @param n 数字
     * @return XXXX
     */
    public static String encodePN(int n) {
        if (n == 0) {
            return "0000";
        }
        int point1 = 0;
        int point2 = 0;
        if (n % 8 == 0) {
            point1 = 8;
            point2 = n / 8;
        } else {
            point1 = n % 8;
            point2 = n / 8 + 1;
        }
        return encodeWhereIs1(point1) + formatTo2Hex(point2);
    }

    /**
     * 信息点DT
     *
     * @param n 数字
     * @return XXXX
     */
    public static String encodeFN(int n) {
        int point1 = 0;
        int point2 = 0;
        if (n % 8 == 0) {
            point1 = 8;
            point2 = n / 8 - 1;
        } else {
            point1 = n % 8;
            point2 = n / 8;
        }
        return encodeWhereIs1(point1) + formatTo2Hex(point2);
    }

    private static String encodeWhereIs1(int n) {
        String result = "00";
        switch (n) {
            case 1:
                result = "01";
                break;
            case 2:
                result = "02";
                break;
            case 3:
                result = "04";
                break;
            case 4:
                result = "08";
                break;
            case 5:
                result = "10";
                break;
            case 6:
                result = "20";
                break;
            case 7:
                result = "40";
                break;
            case 8:
                result = "80";
                break;
            default:
                log.warn("wrong n: {}", n);
                break;
        }
        return result;
    }

    /**
     * 解析 1000 0000, 从左到右遍历,其中某位是1, 返回该位数字
     * 从左到右依次是 8-1
     *
     * @param b 字节
     * @return 1-8
     */
    private static int parseByteWhereIs1(byte b) {
        int result = 0;
        switch (b) {
            case (byte) 0x80:
                result = 8;
                break;
            case (byte) 0x40:
                result = 7;
                break;
            case (byte) 0x20:
                result = 6;
                break;
            case (byte) 0x10:
                result = 5;
                break;
            case (byte) 0x08:
                result = 4;
                break;
            case (byte) 0x04:
                result = 3;
                break;
            case (byte) 0x02:
                result = 2;
                break;
            case (byte) 0x01:
                result = 1;
                break;
        }
        return result;
    }

    public static String formatTo1Hex(int n) {
        n = n & 0x0F;
        return String.format("%01X", n);
    }

    /**
     * 格式化
     *
     * @param n 数字
     * @return XX
     */
    public static String formatTo2Hex(int n) {
        n = n & 0xFF;
        return String.format("%02X", n);
    }

    public static String formatTo4Hex(int n) {
        n = n & 0xFFFF;
        return String.format("%04X", n);
    }

    public static String decodeTerminalAddress(byte[] bytes) {
        int b1 = (int) (((int) bytes[0]) & 0xFF);
        int b2 = (int) (((int) bytes[1]) & 0xFF);
        int value = (b1 + (b2 << 8));
        return String.format("%05d", value);
    }

    public static int decodeLength(byte[] bytes) {
        int b1 = (int) (((int) bytes[0]) & 0xFF);
        int b2 = (int) (((int) bytes[1]) & 0xFF);
        int length = (b1 + (b2 << 8)) >>> 2;
        return length;
    }

    public static int decode2Byte(byte[] bytes) {
        int b1 = (int) (((int) bytes[0]) & 0xFF);
        int b2 = (int) (((int) bytes[1]) & 0xFF);
        int value = b1 + (b2 << 8);
        return value;
    }

    public static int decode1Byte(byte b) {
        int value = (int) (((int) b) & 0xFF);
        return value;
    }

    public static int[] decodeConnectionSpeedAndPort(byte b) {
        int speed = (int) (((int) b) & 0xE0) >>> 5;
        int port = (int) (((int) b) & 0x1F);
        return new int[]{speed, port};
    }

    public static String encodeLength(int length) {
        int raw = (length << 2) + 2;
        String hex = formatTo4Hex(raw);
        return hex.substring(2) + hex.substring(0, 2);
    }

    // 数字转1字节bcd
    public static String encodeBCD(int bcd) {
        int left = bcd / 10;
        int right = bcd % 10;
        return encodeHalfByte(left) + encodeHalfByte(right);
    }

    public static String encodeHalfByte(int value) {
        String hex = formatTo1Hex(value);
        return hex;
    }

    public static String encode1Byte(int value) {
        String hex = formatTo2Hex(value);
        return hex;
    }


    public static String encode2Byte(int value) {
        String hex = formatTo4Hex(value);
        return hex.substring(2) + hex.substring(0, 2);
    }

    public static QGDW376Dot1FieldC decodeFieldC(byte b) {
        QGDW376Dot1FieldC fieldC = new QGDW376Dot1FieldC();
        fieldC.setD7((byte) 0x80 == (b & (byte) 0x80));
        fieldC.setD6((byte) 0x40 == (b & (byte) 0x40));
        fieldC.setD5((byte) 0x20 == (b & (byte) 0x20));
        fieldC.setD4((byte) 0x10 == (b & (byte) 0x10));
        fieldC.setFunctionCode((byte) (b & 0x0F));
        return fieldC;
    }

    public static void pushMqtt(String s) {
        log.info("qyc测试打印: mqtt: {}", s);
    }

    public static String decodeAddress(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            int unsignedInt = Byte.toUnsignedInt(b);
            hexString.append(formatTo2Hex(unsignedInt));
        }
        return hexString.toString().toUpperCase(); // 转换为大写
    }

    public static int decodeEnergyMNum(byte b) {
        int value = ((int) b) & 0x3F;
        return value;
    }

    public static int[] decodeEnergyIntegerAndDecimal(byte b) {
        int integer = (((int) b) & 0x0C) >>> 2;
        int decimal = ((int) b) & 0x03;
        return new int[]{integer, decimal};
    }

    public static int[] decodeUserBigAndSmall(byte b) {
        int big = (((int) b) & 0xF0) >>> 4;
        int small = ((int) b) & 0x0F;
        return new int[]{big, small};
    }


    /**
     * 下面的代码用于发送指令
     */

    /**
     * 发送0A指令
     *
     * @param connection connection
     * @param fn         fn
     * @param traceId    traceId
     */
    public static void sendCMDBy0AFn(QGDW376Dot1Connection connection, int fn, String traceId) {
        String address = connection.getAddress();
        Channel channel = connection.getChannel();
        int pseq = connection.getAndUpdatePSEQ();
        String cmd = QGDW376Dot1Encoder.getUserConfigCmdBy0AFn(fn, pseq, address);
        log.info("traceId: {}, 发送0A指令 查找用户电能表配置数据 {}: {}", traceId, fn, cmd);
        channel.writeAndFlush(HexUtil.decodeHex(cmd));
    }

    // 发送0AF10指令
    public static void sendCMDBy0AF10(QGDW376Dot1Connection connection, String traceId) {
        Channel channel = connection.getChannel();
        int pseq = connection.getAndUpdatePSEQ();
        String address = connection.getAddress();
        String cmd = QGDW376Dot1Encoder.getUserConfigCmdBy0AF10(pseq, address);
        log.info("traceId: {}, 发送指令 查找用户电能表配置数据 10: {}", traceId, cmd);
        channel.writeAndFlush(HexUtil.decodeHex(cmd));
    }
}
