package com.cdz360.iot.meter.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.meter.dto.EssMeterCfg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * @Classname IotEssFeignHystrix
 * @Description
 * @Date 11/4/2021 1:43 PM
 * @Created by Rafael
 */
@Slf4j
@Component
public class IotEssFeignHystrix implements FallbackFactory<IotEssFeignClient> {
    @Override
    public IotEssFeignClient apply(Throwable throwable) {
        return new IotEssFeignClient() {
            @Override
            public Mono<BaseResponse> modifyEssMeter(EssMeterCfg param) {
                log.error("【服务熔断】 下发修改电表信息. Service = {}, api = modifyEssMeter. param = {}",
                        DcConstants.KEY_FEIGN_IOT_ESS, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> sendCfgByEquipId(Long equipId) {
                log.error("【服务熔断】 下发ESS配置. Service = {}, api = sendCfgByEquipId. equipId = {}",
                        DcConstants.KEY_FEIGN_IOT_ESS, equipId);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, IotEssFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_ESS);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super IotEssFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_ESS);
        return null;
    }
}