package com.cdz360.iot.meter.north.consumer;

import com.cdz360.iot.meter.model.app.TransformerCurrentData;
import com.cdz360.iot.meter.model.type.P645DataType;
import com.cdz360.iot.meter.north.job.JobTask;
import com.cdz360.iot.meter.north.job.ReadTransformerRateJob;
import com.cdz360.iot.meter.south.MeterChannelRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @Classname ReadTransformerService
 * @Description
 * @Date 10/14/2020 1:03 PM
 * @Created by Rafael
 */
@Slf4j
@Service
@Getter
public class ReadTransformerService extends AbstractSubMeterObserver implements MeterSubObserver {


    @Autowired
    private MeterChannelRepository meterRepository;


    @Autowired
    private MeterObserverService meterObserverProxy;

    @PostConstruct
    public void init() {
        this.meterObserverProxy.addSubObserver(this, List.of(P645DataType.TRANSFORMER_CUR_RATE,
                P645DataType.TRANSFORMER_VOL_RATE));
    }

    public Mono<TransformerCurrentData> process(String deviceNo) {

        ReadTransformerRateJob job = new ReadTransformerRateJob(this.meterRepository, deviceNo);
        job.init();
        super.addJob(deviceNo, job);

        Mono<TransformerCurrentData> mono = Mono.create(sink -> {
            JobTask task = job.nextTask();
            job.setMonoSink(sink);
            job.process(task);

        });
        return mono;
    }

}