package com.cdz360.iot.meter.yunji.qgdw376dot1;

import cn.hutool.core.util.HexUtil;
import com.cdz360.iot.meter.yunji.qgdw376dot1.model.QGDW376Dot1RawBody;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class QGDW376Dot1DecoderTest {

    @InjectMocks
    private QGDW376Dot1Decoder decoder;

    @Test
    public void test() {
        QGDW376Dot1RawBody rawBody = new QGDW376Dot1RawBody();
        rawBody.setTraceId("123456789");
        String input = "6842004200688821336f06000a630000010b21336f069316";
        rawBody.setBody(HexUtil.decodeHex(input));
        decoder.decode(rawBody, "123");
    }
}
