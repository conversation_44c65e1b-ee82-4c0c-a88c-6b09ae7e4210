package com.cdz360.iot.meter.yunji.qgdw376dot1;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

@SpringBootTest
public class QGDW376Dot1EncoderTest {

    @InjectMocks
    private QGDW376Dot1Encoder encoder;


    @Test
    public void testGetUserConfigCmdBy0AF10() {
        String address = "2133510600";
        String userConfigCmdBy0AF10 = QGDW376Dot1Encoder.getUserConfigCmdBy0AF10(0, address);
        System.out.println(userConfigCmdBy0AF10);
    }


    @Test
    public void testGetMonthTimeFlag() {
        LocalDate currentDate = LocalDate.now();
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        String lastMonthTimeFlag = QGDW376Dot1Encoder.getMonthTimeFlag(lastMonthDate);
        System.out.println(lastMonthTimeFlag);
    }

    @Test
    public void testGet0DbyFnPn() {
        int pn = 1;
        int pseq = 1;
        String address = "21336F0600";
        LocalDate currentDate = LocalDate.now();
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        String lastMonthTimeFlag = QGDW376Dot1Encoder.getMonthTimeFlag(lastMonthDate);
        String code21 = QGDW376Dot1Encoder.get0DByFnPn(21, pn, pseq, address, lastMonthTimeFlag);
        System.out.println(code21);
    }

    @Test
    public void testEncodeIpAndPortAll() {
        String expected = "3af0ed06050b792aafb1b822636d6e65740000000000000000000000";
        String encode = QGDW376Dot1Encoder.encodeIpAndPortAll("************:2821", "**************:8888", "636d6e65740000000000000000000000");
        Assertions.assertEquals(expected.toUpperCase(), encode.toUpperCase());
    }

    @Test
    public void testGetSettingIpAndPortCmd() {
        String expected = "68A200A200684A21335106000461000004006A0E372E64193AF0ED06050B636d6e65740000000000000000000000FC16";
        String encode = QGDW376Dot1Encoder.getSettingIpAndPortCmd("************:6500", "************:2821", "636d6e65740000000000000000000000",
                1, "2133510600");
        Assertions.assertEquals(expected.toUpperCase(), encode.toUpperCase());
    }


}
