package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.order.type.SiteCtrlCfgStatusType;
import com.cdz360.iot.model.site.po.SiteCtrlCfgLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname SiteCtrlCfgLogRwMapper
 * @Description
 * @Date 4/22/2020 3:36 PM
 * @Created by Rafael
 */
@Mapper
public interface SiteCtrlCfgLogRwMapper {
    int insertOrUpdate(SiteCtrlCfgLogPo param);
    int checkTimeout(@Param("bufferTime") Integer bufferTime,
                     @Param("status") SiteCtrlCfgStatusType status);
}