package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.SiteCtrlCfgRoMapper;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname SiteCtrlCfgRoDs
 * @Description
 * @Date 4/22/2020 7:23 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlCfgRoDs {
    @Autowired
    private SiteCtrlCfgRoMapper siteCtrlCfgRoMapper;

    public SiteCtrlCfgPo selectByNum(String ctrlNum) {
        return siteCtrlCfgRoMapper.selectByNum(ctrlNum);
    }
}