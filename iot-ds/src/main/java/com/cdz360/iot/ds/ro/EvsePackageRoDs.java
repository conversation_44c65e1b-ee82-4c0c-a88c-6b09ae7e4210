package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvsePackageRoMapper;
import com.cdz360.iot.model.evse.param.ListPackageParam;
import com.cdz360.iot.model.evse.vo.EvsePackageVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class EvsePackageRoDs {

    @Autowired
    private EvsePackageRoMapper evsePackageRoMapper;

    public List<EvsePackageVo> getList(ListPackageParam params) {
        return evsePackageRoMapper.getList(params);
    }

    public Long getCount(ListPackageParam params) {
        return evsePackageRoMapper.getCount(params);
    }

    public List<String> getBrandList() {
        return evsePackageRoMapper.getBrandList();
    }

    public EvsePackageVo selectByPrimaryKey(Long id) {
        return evsePackageRoMapper.selectByPrimaryKey(id);
    }
}