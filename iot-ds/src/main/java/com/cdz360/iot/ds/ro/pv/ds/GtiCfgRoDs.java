package com.cdz360.iot.ds.ro.pv.ds;


import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.pv.mapper.GtiCfgRoMapper;
import com.cdz360.iot.model.pv.param.ListGtiCfgParam;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import com.cdz360.iot.model.pv.vo.GtiCfgVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class GtiCfgRoDs {



	@Autowired
	private GtiCfgRoMapper gtiCfgRoMapper;


	public GtiCfgPo getById(Long id) {

		return this.gtiCfgRoMapper.getById(id);

	}

	public List<GtiCfgVo> findList(ListGtiCfgParam param) {
		return gtiCfgRoMapper.findList(param, EquipStatus.OFFLINE);
	}
	public Long findListCount(ListGtiCfgParam param) {
		return gtiCfgRoMapper.findListCount(param);
	}

	public Long countByName(@NonNull String name, @NonNull String siteId, @Nullable Long id) {
		return gtiCfgRoMapper.countByName(name, siteId, id);
	}

	// 获取关联的逆变器数量
	public Long getConnectedGtiNum(Long cfgId) {
		return gtiCfgRoMapper.getConnectedGtiNum(cfgId);
	}
	// 获取该模板下发中的逆变器数量
	public Long getIssuingGtiNum(Long cfgId) {
		return gtiCfgRoMapper.getIssuingGtiNum(cfgId);
	}

	public void delPrecheck(Long cfgId) {
		long connectedGtiNum = gtiCfgRoMapper.getConnectedGtiNum(cfgId);
		IotAssert.isTrue(connectedGtiNum == 0, "删除失败：存在关联网元");
		long issuingGtiNum = gtiCfgRoMapper.getIssuingGtiNum(cfgId);
		IotAssert.isTrue(issuingGtiNum == 0, "删除失败：模板下发中");
	}

}

