package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.park.param.ListParkingLockParam;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface ParkingLockRoMapper {


    ParkingLockPo getByUniqueKey(
        @Param("partner") ParkingLockPartner partner,
        @Param("serialNumber") String serialNumber);

    ParkingLockPo getByEvseNoAndPlugId(
        @Param("evseNo") String evseNo, @Param("plugId") Integer plugId);

    ParkingLockPo getById(@Param("id") Long id);

    ParkingLockVo getVoById(@Param("id") Long id);

    List<ParkingLockVo> parkingLockList(ListParkingLockParam param);

    long count(ListParkingLockParam param);
}

