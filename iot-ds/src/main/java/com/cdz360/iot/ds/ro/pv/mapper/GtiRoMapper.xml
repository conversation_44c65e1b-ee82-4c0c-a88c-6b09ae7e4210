<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.pv.mapper.GtiRoMapper">



	<resultMap id="RESULT_GTI_PO" type="com.cdz360.iot.model.pv.po.GtiPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="serialNo" jdbcType="VARCHAR" property="serialNo" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="power" jdbcType="BIGINT" property="power" />
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel" />
		<result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="alertStatus" property="alertStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />
		<result column="cfgStatus" property="cfgStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId" />
		<result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<resultMap id="RESULT_GTI_VO" type="com.cdz360.iot.model.pv.vo.GtiVo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="groupNum" jdbcType="INTEGER" property="groupNum" />
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="siteName" jdbcType="VARCHAR" property="siteName" />
		<result column="gwName" jdbcType="VARCHAR" property="gwName" />
		<result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="alertStatus" property="alertStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />
		<result column="cfgStatus" property="cfgStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId" />
		<result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime" />

		<result column="ownEquipId" property="ownEquipId" />
		<result column="equipIdSwVer" property="equipIdSwVer" />
		<result column="essEquipId" property="essEquipId" />
		<result column="essDno" property="essDno" />
		<result column="essName" property="essName" />
	</resultMap>



	<select id="getByGwnoAndDno"

			resultMap="RESULT_GTI_PO">	
		select * from t_gti where gwno = #{gwno} and dno = #{dno}

	</select>
	<select id="getByDno"

			resultMap="RESULT_GTI_PO">
		select * from t_gti where dno = #{dno}

	</select>
	<sql id="query_gti_condition">
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and trc.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
			and gti.gwno = #{gwno}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dno )">
			and gti.dno = #{dno}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
			and (gti.id = #{devNo} or gti.sid = #{devNo})
		</if>
		<if test="status != null">
			and gti.status = #{status}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( vendor )">
			and gti.vendor = #{vendor}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceModel )">
			and gti.deviceModel = #{deviceModel}
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
			and gti.name like concat('%', #{devName}, '%')
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
			and devCfg.name = #{tempName}
		</if>
	</sql>
    <select id="findGtiList"
			parameterType="com.cdz360.iot.model.pv.param.ListGtiParam"
			resultMap="RESULT_GTI_VO">
		select
			gti.*,
			devCfg.name cfgSuccessName,
			site.name as siteName,
			gw.name AS gwName,
			equip.equipId as ownEquipId,
			equip.swVer as equipIdSwVer,
			ess.dno essDno,
			ess.name essName
		from t_gti gti
		left join t_gti_cfg cfgSuccess on cfgSuccess.cfgId = gti.cfgSuccessId
		left join t_dev_cfg devCfg on devCfg.id = cfgSuccess.cfgId
		LEFT JOIN t_gw_info gw ON gw.gwno = gti.gwno
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		LEFT JOIN t_r_commercial trc ON site.commId = trc.id
		left join t_ess_equip equip on equip.id = gti.essEquipId
		left join t_ess ess on ess.dno = equip.essDno
		where gti.status != 99
		<include refid="query_gti_condition" />
		order by
			gti.id desc
		<choose>
			<when test="start != null and size != null">
				limit #{start},#{size}
			</when>
			<when test="size != null">
				limit #{size}
			</when>
		</choose>
	</select>
    <select id="getByName"
		resultMap="RESULT_GTI_PO">
		select * from t_gti
		where status != 99 and
		<choose>
			<when test="null != includeDno and includeDno">
				<foreach item="gti" collection="gtiDtoList"
						 open="(" separator="or" close=")">
					(name = #{gti.name} and dno != #{gti.dno})
				</foreach>
			</when>
			<otherwise>
				name in
				<foreach item="gti" collection="gtiDtoList"
						 open="(" separator="," close=")">
					#{gti.name}
				</foreach>
			</otherwise>
		</choose>
		limit 1
	</select>
	<select id="count" resultType="java.lang.Long">
		select count(*)
		from t_gti gti
		left join t_gti_cfg cfgSuccess on cfgSuccess.cfgId = gti.cfgSuccessId
		left join t_dev_cfg devCfg on devCfg.id = cfgSuccess.cfgId
		LEFT JOIN t_site site ON site.dzid = gti.siteId
		LEFT JOIN t_r_commercial trc ON site.commId = trc.id
		where gti.status != 99
		<include refid="query_gti_condition"></include>
	</select>
	<select id="getCountByCfgId" resultType="java.lang.Long">
		select count(*) from t_gti where cfgSuccessId = #{cfgId}
	</select>

	<select id="getGtiStatusBi" resultType="com.cdz360.iot.model.pv.vo.GtiStatusBi">
		select
			gti.status ,
			count(*) as num
		from
			t_gti gti
		inner join t_site s on
			gti.siteId = s.dzId
		inner join t_r_commercial comm on
			s.commId = comm.id
		where
			gti.status in (1,2)
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.idChain like concat(#{commIdChain},"%")
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
		group by
			gti.status
	</select>

	<select id="getAbnormalGtiNum" resultType="java.lang.Long">
		select
			count(*) as num
		from
			t_gti gti
		inner join t_site s on
			gti.siteId = s.dzId
		inner join t_r_commercial comm on
			s.commId = comm.id
		where
			gti.status = 1
			and gti.alertStatus = 2
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.idChain like concat(#{commIdChain},"%")
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and gti.siteId = #{siteId}
		</if>
	</select>
	<select id="getByEssEquipId" resultMap="RESULT_GTI_PO">
		select * from t_gti where essEquipId = #{essEquipId}
		limit 1
	</select>

	<select id="getByVendor" resultMap="RESULT_GTI_PO">
		select gti.* from t_gti gti
		inner join t_ess_equip equip on equip.dno = gti.serialNo
		where gti.vendor = #{vendor} and equip.equipType = #{equipType}
	</select>

</mapper>

