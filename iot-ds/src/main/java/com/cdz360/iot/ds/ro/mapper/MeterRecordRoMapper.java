package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.meter.dto.BiSiteMeterSummaryDto;
import com.cdz360.iot.model.meter.po.MeterRecordPo;
import com.cdz360.iot.model.param.MeterRecordBiParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface MeterRecordRoMapper {

	MeterRecordPo getById(@Param("id") Long id);

	List<MeterRecordPo> getReading(@Param("ids") List<Long> ids, @Param("date") Date date);

	MeterRecordPo getReadingByTime(@Param("meterId") Long meterId, @Param("readingTime") Date readingTime);

    List<BiSiteMeterSummaryDto> getBiSiteMeterRecord(MeterRecordBiParam param);
}
