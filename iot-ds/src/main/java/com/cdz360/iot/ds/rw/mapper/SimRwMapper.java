package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.SimDeviceType;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SimRwMapper {

    int addSimList(@Param(value = "poList") List<SimPo> list);

    int updateSimListByIccid(@Param(value = "poList") List<SimPo> list);

    int updateSimListByMsisdn(@Param(value = "poList") List<SimPo> list);

    int updateByIccid(SimPo po);

    int resetDeviceTypeById(@Param("simId") Long simId,
        @Param("deviceType") SimDeviceType deviceType);

    int resetRemark(@Param("simId") Long simId);

}
