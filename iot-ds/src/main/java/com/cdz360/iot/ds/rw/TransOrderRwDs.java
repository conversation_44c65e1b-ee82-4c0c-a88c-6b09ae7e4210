package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.TransOrderRwMapper;
import com.cdz360.iot.model.parts.po.TransOrderPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransOrderRwDs {

    @Autowired
    private TransOrderRwMapper transOrderRwMapper;

    public TransOrderPo getByOrderNo(String orderNo, boolean lock) {
        return this.transOrderRwMapper.getByOrderNo(orderNo, lock);
    }

    public boolean insertTransOrder(TransOrderPo TransOrderPo) {
        return this.transOrderRwMapper.insertTransOrder(TransOrderPo) > 0;
    }

    public boolean updateTransOrder(TransOrderPo TransOrderPo) {
        return this.transOrderRwMapper.updateTransOrder(TransOrderPo) > 0;
    }

    public boolean postPartsReceive(String partsCode) {
        return this.transOrderRwMapper.postPartsReceive(partsCode) > 0;
    }
}

