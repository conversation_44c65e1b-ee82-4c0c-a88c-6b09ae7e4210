<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseModuleDetailRoMapper">

    <resultMap id="BASE" type="com.cdz360.iot.model.evse.po.EvseModuleDetailPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="moduleId" jdbcType="BIGINT" property="moduleId" />
        <result column="deviceNo" jdbcType="VARCHAR" property="deviceNo" />
        <result column="oldDeviceNo" property="oldDeviceNo" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <select id="getValidModuleNum" resultType="java.lang.Long">
        select
            count(*)
        from
            t_evse_module_detail
        where
            moduleId = #{moduleId}
            and deviceNo is not null
            and updateTime is not null
    </select>

    <select id="countByCondition" resultType="java.lang.Long">
        select
            count(*)
        from
            t_evse_module_detail
        where
            moduleId = #{moduleId}
    </select>

    <select id="findById" resultMap="BASE">
        select
            *
        from
            t_evse_module_detail
        where
            moduleId = #{moduleId}
        order by
            id asc
    </select>

</mapper>