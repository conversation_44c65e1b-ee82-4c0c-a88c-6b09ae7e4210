package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssBatteryPackRoMapper;
import com.cdz360.iot.model.ess.param.ListEssBatteryPackParam;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class EssBatteryPackRoDs {

    @Autowired
    private EssBatteryPackRoMapper essBatteryPackRoMapper;

    public List<EssEquipBatteryPackVo> findBatteryPack(ListEssBatteryPackParam param) {
        return this.essBatteryPackRoMapper.findBatteryPack(param);
    }

    public Long countBatteryPack(ListEssBatteryPackParam param) {
        return this.essBatteryPackRoMapper.countBatteryPack(param);
    }

    public List<EssEquipBatteryPackSimpleVo> findBatteryPackSimpleVoList(
        @NonNull String essDno,
        @Nullable Long stackEquipId,
        @Nullable Long clusterEquipId,
        @Nullable Long start,
        @Nullable Integer size) {
        return this.essBatteryPackRoMapper.findBatteryPackSimpleVoList(essDno,
            stackEquipId,
            clusterEquipId,
            start,
            size);
    }

}

