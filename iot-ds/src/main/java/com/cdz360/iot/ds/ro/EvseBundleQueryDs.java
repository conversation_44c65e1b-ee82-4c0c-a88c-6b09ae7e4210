package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseBundleQueryMapper;
import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.type.BundleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname EvseBundleQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:27 PM
 * @Created by Rafael
 */
@Service
public class EvseBundleQueryDs {
    @Autowired
    private EvseBundleQueryMapper evseBundleQueryMapper;
    
    public EvseBundle selectByPrimaryKey(Long id) {
        return evseBundleQueryMapper.selectByPrimaryKey(id);
    }

    public List<EvseBundle> selectByVersion(Long version, BundleType type, Integer enable) {
        return evseBundleQueryMapper.selectByVersion(version, type, enable);
    }

    public List<EvseBundle> listEvseBundle(EvseBundleParam evseBundleParam) {
        return evseBundleQueryMapper.listEvseBundle(evseBundleParam);
    }

    public long getTotals(EvseBundleParam evseBundleParam) {
        return evseBundleQueryMapper.getTotals(evseBundleParam);
    }
}