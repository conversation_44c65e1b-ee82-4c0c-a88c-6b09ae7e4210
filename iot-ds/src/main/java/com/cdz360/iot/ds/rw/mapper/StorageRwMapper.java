package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.parts.po.StoragePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface StorageRwMapper {

	int insertStorage(StoragePo storagePo);


    StoragePo getByUid(@Param("uid") Long uid, @Param("lock") boolean lock);
    StoragePo getOneByName(@Param("name") String name, @Param("lock") boolean lock);
}

