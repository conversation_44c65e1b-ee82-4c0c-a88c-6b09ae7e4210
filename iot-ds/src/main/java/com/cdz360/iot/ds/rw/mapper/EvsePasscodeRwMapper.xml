<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvsePasscodeRwMapper">
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.po.EvsePasscodePo"
            useGeneratedKeys="true">
        insert into t_evse_passcode (evseNo, ver, passcode, enable, createTime, updateTime)
        values (#{evseNo,jdbcType=VARCHAR}, #{ver,jdbcType=BIGINT}, #{passcode,jdbcType=VARCHAR},
        #{enable,jdbcType=BOOLEAN}, now(), now())
    </insert>

    <update id="disableEvsePasscode">
        update t_evse_passcode
        set enable = false
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>