package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.PartsTypeRwMapper;
import com.cdz360.iot.model.parts.po.PartsTypePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class PartsTypeRwDs {



	@Autowired

	private PartsTypeRwMapper partsTypeRwMapper;



	public PartsTypePo getById(Long id, boolean lock) {

		return this.partsTypeRwMapper.getById(id, lock);

	}



	public boolean insertPartsType(PartsTypePo partsTypePo) {

		return this.partsTypeRwMapper.insertPartsType(partsTypePo) > 0;

	}



	public boolean updatePartsType(PartsTypePo partsTypePo) {

		return this.partsTypeRwMapper.updatePartsType(partsTypePo) > 0;

	}





}

