package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.vo.DeviceVo;
import com.cdz360.iot.model.evse.vo.EvseDeviceVo;
import com.cdz360.iot.model.param.FindDeviceParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface EvseModuleRoMapper {

    List<EvseModulePo> getList(@Param("start") Long start,
                               @Param("size") Integer size);

    EvseModulePo findChargingModule(@Param("evseNo") String evseNo,
                                    @Param("deviceName") String deviceName);

    List<DeviceVo> getByEvseNo(@NonNull @Param("evseNo") String evseNo,
                               @Nullable @Param("deviceName") String deviceName,
                               @Nullable @Param("start") Long start,
                               @Nullable @Param("size") Long size);

    List<DeviceVo> getBySiteId(@NonNull @Param("siteId") String siteId,
                               @Nullable @Param("start") Long start,
                               @Nullable @Param("size") Long size);

    Long getByEvseNoCount(@NonNull @Param("evseNo") String evseNo,
                          @Nullable @Param("deviceName") String deviceName);

    List<EvseModulePo> queryByEvseNo(@Param("evseNo") String evseNo);

    EvseDeviceVo getByDeviceNo(FindDeviceParam param);

}
