<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseReportModuleDetailRwMapper">

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.po.EvseReportModuleDetailPo"
            useGeneratedKeys="true">
        INSERT INTO t_evse_report_module_detail
            (moduleId, idx, deviceNo, status, plugId, intakeTemp,
            voltage, actualVoltage, `current`, actualCurrent)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.moduleId}, #{item.idx}, #{item.deviceNo}, #{item.status}, #{item.plugId}, #{item.intakeTemp},
            #{item.voltage}, #{item.actualVoltage}, #{item.current}, #{item.actualCurrent})
        </foreach>
    </insert>

    <delete id="removeByModuleId">
        delete from t_evse_report_module_detail where moduleId = #{moduleId}
    </delete>

</mapper>