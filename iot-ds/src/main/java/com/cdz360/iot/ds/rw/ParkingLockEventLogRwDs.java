package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.ParkingLockEventLogRwMapper;
import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ParkingLockEventLogRwDs {

    @Autowired
    private ParkingLockEventLogRwMapper parkingLockEventLogRwMapper;

    public ParkingLockEventLogPo getById(Long id, boolean lock) {
        return this.parkingLockEventLogRwMapper.getById(id, lock);
    }

    public boolean insertParkingLockEventLog(ParkingLockEventLogPo parkingLockEventLogPo) {
        return this.parkingLockEventLogRwMapper.insertParkingLockEventLog(
            parkingLockEventLogPo) > 0;
    }

    public boolean updateParkingLockEventLog(ParkingLockEventLogPo parkingLockEventLogPo) {
        return this.parkingLockEventLogRwMapper.updateParkingLockEventLog(
            parkingLockEventLogPo) > 0;
    }

}

