package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseReportModuleRwMapper;
import com.cdz360.iot.model.evse.po.EvseReportModulePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EvseReportModuleRwDs {

    @Autowired
    private EvseReportModuleRwMapper mapper;

    public boolean insertOrUpdate(EvseReportModulePo po) {
        return mapper.insertOrUpdate(po) > 0;
    }

    public boolean updateNumber(EvseReportModulePo po) {
        return mapper.updateNumber(po) > 0;
    }

}
