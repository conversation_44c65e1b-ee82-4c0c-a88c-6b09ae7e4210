package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.camera.po.CameraPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface CameraRwMapper {
	CameraPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertCamera(CameraPo cameraPo);

	int updateCamera(CameraPo cameraPo);


	int disableSiteByCameraSiteId(@Param("cameraSiteId") Long cameraSiteId);

	boolean insertOrUpdate(CameraPo cameraPo);

	boolean flushLiveAddress(CameraPo cameraPo);

	boolean flushChannelPicUrl(CameraPo cameraPo);

    int disableSiteByDeviceSerial(@Param("deviceSerial") String deviceSerial);

	int disableSiteByDeviceId(@Param("recorderId") Long recorderId);

	int updateAspectRatioByRecordId(@Param("recorderId") Long recordId, @Param("aspectRatio") String aspectRatio);
}
