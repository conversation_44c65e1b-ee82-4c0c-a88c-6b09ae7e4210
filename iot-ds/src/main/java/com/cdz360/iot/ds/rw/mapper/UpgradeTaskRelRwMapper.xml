<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.UpgradeTaskRelRwMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskRelVo">
        insert into t_upgrade_task_rel(taskId, evseIds, createTime)
        values(#{taskId}, #{evseIds}, now())
    </insert>
</mapper>