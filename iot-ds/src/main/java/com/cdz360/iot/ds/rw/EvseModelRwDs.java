package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseModelRwMapper;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseModelRwDs {

    @Autowired
    private EvseModelRwMapper mapper;

    public boolean insertOrUpdate(EvseModelPo po) {
        return this.mapper.insertOrUpdate(po) > 0;
    }

    public boolean updateById(EvseModelPo po) {
        return this.mapper.updateById(po) > 0;
    }

    public boolean changeStatus(long id, boolean enable) {
        return this.mapper.changeStatus(id, enable) > 0;
    }

    //
//	public int batchInsert(List<EvseMeterPo> evseMeterPoList) {
//		return this.evseMeterRwMapper.batchInsert(evseMeterPoList);
//	}
//
    public boolean remove(Long id) {
        return this.mapper.remove(id) > 0;
    }


}
