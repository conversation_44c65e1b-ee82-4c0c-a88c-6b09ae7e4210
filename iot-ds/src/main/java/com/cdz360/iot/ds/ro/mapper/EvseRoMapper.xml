<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseRoMapper">
  <resultMap id="RESULT_MAP_EVSE_PO" type="com.cdz360.iot.model.evse.EvsePo">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="gwno" property="gwno" jdbcType="VARCHAR"/>
    <id column="evseId" property="evseId" jdbcType="VARCHAR"/>
    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="power" property="power" jdbcType="INTEGER"/>
    <id column="bizStatus" property="bizStatus" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="evseStatus" property="evseStatus" jdbcType="VARCHAR"/>
    <id column="supply" property="supply" jdbcType="VARCHAR"/>
    <id column="net" property="net" jdbcType="VARCHAR"/>
    <id column="dtuType" property="dtuType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="ip" property="ip" jdbcType="VARCHAR"/>
    <id column="model" property="model" jdbcType="VARCHAR"/>
    <id column="modelId" property="modelId" jdbcType="BIGINT"/>
    <id column="physicalNo" property="physicalNo" jdbcType="VARCHAR"/>
    <id column="plugNum" property="plugNum" jdbcType="INTEGER"/>
    <id column="siteId" property="siteId" jdbcType="VARCHAR"/>
    <id column="commId" property="commId" jdbcType="BIGINT"/>
    <id column="priceCode" property="priceCode" jdbcType="BIGINT"/>
    <id column="protocolVer" property="protocolVer" jdbcType="INTEGER"/>
    <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR"/>
    <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
    <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
    <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    <id column="protocol" property="protocol" jdbcType="VARCHAR"/>
    <id column="modelName" property="modelName" jdbcType="VARCHAR"/>
    <id column="voltage" property="voltage" jdbcType="DECIMAL"/>
    <id column="current" property="current" jdbcType="DECIMAL"/>
    <id column="passcodeVer" property="passcodeVer" jdbcType="BIGINT"/>
    <id column="debugTag" property="debugTag" jdbcType="BOOLEAN"/>
    <id column="iccid" property="iccid" jdbcType="VARCHAR"/>
    <id column="imsi" property="imsi" jdbcType="VARCHAR"/>
    <id column="imei" property="imei" jdbcType="VARCHAR"/>
    <id column="produceNo" property="produceNo" jdbcType="VARCHAR"/>
    <id column="produceDate" property="produceDate" jdbcType="TIMESTAMP"/>
    <id column="expireDate" property="expireDate" jdbcType="TIMESTAMP"/>
    <id column="odm" property="odm" jdbcType="VARCHAR"
      typeHandler="com.cdz360.iot.ds.ODMTypeHandler"/>
    <id column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
    <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
  </resultMap>

  <resultMap id="EVSE_MODEL_VO" type="com.cdz360.iot.model.evse.vo.EvseModelVo"
    extends="RESULT_MAP_EVSE_PO">
    <result column="brand" jdbcType="VARCHAR" property="brand"/>
    <result column="series" jdbcType="VARCHAR" property="series"/>
    <result column="plugNo" jdbcType="VARCHAR" property="plugNo"/>
    <result column="flags" property="flags"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="simIccid" jdbcType="VARCHAR" property="simIccid"/>
    <result column="simMsisdn" jdbcType="VARCHAR" property="simMsisdn"/>
    <collection property="evseModulePoList" ofType="com.cdz360.iot.model.evse.po.EvseModulePo"
      javaType="list">
      <result column="moduleType" jdbcType="VARCHAR" property="moduleType"/>
      <result column="number" jdbcType="INTEGER" property="number"/>
      <result column="deviceName" jdbcType="VARCHAR" property="deviceName"/>
    </collection>
    <collection property="plugList" ofType="com.cdz360.iot.model.evse.dto.PlugDto" javaType="list">
      <result column="onlyPlugNo" jdbcType="VARCHAR" property="plugNo"/>
      <result column="plugName" jdbcType="VARCHAR" property="plugName"/>
      <result column="plugId" jdbcType="INTEGER" property="plugId"/>
    </collection>
  </resultMap>

  <sql id="EVSE_INFO_COLUMNS">
    e.id,
    e.evseId as evseNo,
    e.gwno,
    e.bizStatus,
    e.evseStatus as status,
    e.name,
    e.plugNum,
    e.priceCode,
    e.supply as supplyType,
    e.model as modelName,
    e.modelId,
    e.physicalNo,
    e.net,
    e.dtuType,
    e.bizType,
    e.ip as evseIp,
    e.iccid,
    e.imsi,
    e.imei,
    e.`power`,
    e.voltage,
    e.`current`,
    e.protocolVer,
    e.protocol,
    e.firmwareVer,
    e.pc01Ver,
    e.pc02Ver,
    e.pc03Ver,
    e.produceNo,
    e.produceDate,
    e.expireDate,
    e.updateTime,
    s.commId as siteCommId,
    s.dzId as siteId,
    s.name as siteName,
    gw.name as gwName
  </sql>

  <resultMap id="EVSEINFODTO" type="com.cdz360.iot.model.evse.dto.EvseInfoDto">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <id column="evseNo" property="evseNo" jdbcType="VARCHAR"/>
    <id column="gwno" property="gwno" jdbcType="VARCHAR"/>
    <id column="bizStatus" property="bizStatus" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="status" property="status" jdbcType="VARCHAR"/>
    <id column="name" property="name" jdbcType="VARCHAR"/>
    <id column="plugNum" property="plugNum" jdbcType="INTEGER"/>
    <id column="priceCode" property="priceCode" jdbcType="BIGINT"/>
    <id column="supplyType" property="supplyType" jdbcType="VARCHAR"/>
    <id column="modelName" property="modelName" jdbcType="VARCHAR"/>
    <id column="modelId" property="modelId" jdbcType="BIGINT"/>
    <id column="brand" property="brand" jdbcType="VARCHAR"/>
    <id column="physicalNo" property="physicalNo" jdbcType="VARCHAR"/>
    <id column="net" property="net" jdbcType="VARCHAR"/>
    <id column="dtuType" property="dtuType" jdbcType="INTEGER"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <id column="bizType" property="bizType" jdbcType="VARCHAR"/>
    <id column="evseIp" property="evseIp" jdbcType="VARCHAR"/>
    <id column="iccid" property="iccid" jdbcType="VARCHAR"/>
    <id column="imsi" property="imsi" jdbcType="VARCHAR"/>
    <id column="imei" property="imei" jdbcType="VARCHAR"/>
    <id column="power" property="power" jdbcType="INTEGER"/>
    <id column="voltage" property="voltage" jdbcType="DECIMAL"/>
    <id column="current" property="current" jdbcType="DECIMAL"/>
    <id column="protocolVer" property="protocolVer" jdbcType="INTEGER"/>
    <id column="protocol" property="protocol" jdbcType="VARCHAR"/>
    <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR"/>
    <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
    <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
    <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    <id column="produceNo" property="produceNo" jdbcType="VARCHAR"/>
    <id column="produceDate" property="produceDate" jdbcType="TIMESTAMP"/>
    <id column="expireDate" property="expireDate" jdbcType="TIMESTAMP"/>
    <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
    <id column="siteCommId" property="siteCommId" jdbcType="BIGINT"/>
    <id column="siteId" property="siteId" jdbcType="VARCHAR"/>
    <id column="siteName" property="siteName" jdbcType="VARCHAR"/>
    <id column="gwName" property="gwName" jdbcType="VARCHAR"/>
    <id column="simIccid" property="simIccid" jdbcType="VARCHAR"/>
    <id column="simMsisdn" property="simMsisdn" jdbcType="VARCHAR"/>
    <result column="flags" property="flags"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
  </resultMap>

  <select id="getEvse" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse where evseId = #{evseNo}
  </select>

  <select id="getEvseById" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse where id = #{id}
  </select>

  <select id="getByIccid" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse where iccid = #{iccid} limit 1
  </select>

  <select id="getEvseInfo" resultMap="EVSEINFODTO">
    select
    <include refid="EVSE_INFO_COLUMNS"/>
    ,
    sim.iccid as simIccid,
    sim.msisdn as simMsisdn,
    m.brand
    from t_evse e
    left join t_site s on e.siteId = s.dzId
    left join t_gw_info as gw on e.gwno = gw.gwno
    left join t_sim sim on e.iccid = sim.iccid
    left join t_evse_model m on m.id = e.modelId
    where e.evseId = #{evseNo}
  </select>

  <select id="getEvseInfoList" parameterType="com.cdz360.iot.model.evse.param.ListEvseParam"
    resultMap="EVSEINFODTO">
    select
    <include refid="EVSE_INFO_COLUMNS"/>
    ,m.flags
    ,m.brand
    from t_evse e
    left join t_site s on e.siteId = s.dzId
    left join t_gw_info as gw on e.gwno = gw.gwno
    left join t_evse_model m on m.id = e.modelId
    <where>
      1=1
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
        and e.iccid like concat('%', #{iccid}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
        and m.brand = #{brand}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyword )">
        and (e.name like concat('%', #{keyword}, '%') or e.evseId like concat('%', #{keyword}, '%'))
      </if>
      <if test="bizType != null">
        and e.bizType = #{bizType}
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( firmwareVerList )">
        and e.`firmwareVer` in
        <foreach item="item" collection="firmwareVerList"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="isVinAuth != null and isVinAuth == true">
        and JSON_CONTAINS(m.flags->'$[*]','3','$')
      </if>
      <if test="isVinAuth != null and isVinAuth == false">
        and !JSON_CONTAINS(m.flags->'$[*]','3','$')
      </if>
      <if test="topCommId != null">
        and s.topCommId = #{topCommId}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
        and s.dzId=#{siteId}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( evseNo )">
        and e.evseId like CONCAT('%', #{evseNo}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( modelName )">
        and e.model like CONCAT('%', #{modelName}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
        <foreach item="bizStatus" collection="bizStatusList"
          open="and e.bizStatus in (" close=")" separator=",">
          #{bizStatus.code}
        </foreach>
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseStatusList )">
        <foreach item="evseStatus" collection="evseStatusList"
          open="and e.evseStatus in (" close=")" separator=",">
          #{evseStatus}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteName )">
        and s.name like CONCAT('%', #{siteName}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
        and s.`dzId` in
        <foreach item="siteId" collection="siteIdList"
          open="(" close=")" separator=",">
          #{siteId}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gids )">
        and s.dzId in (
        select siteId from t_r_site_group_site_ref as sg
        where sg.gid in
        <foreach item="gid" index="index" collection="gids"
          open=" (" separator="," close=")">
          #{gid}
        </foreach>
        )
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
        and e.`evseId` in
        <foreach item="evseNo" collection="evseNoList"
          open="(" close=")" separator=",">
          #{evseNo}
        </foreach>
      </if>
    </where>
    order by e.createTime desc
  </select>

  <select id="getEvseList" resultMap="RESULT_MAP_EVSE_PO">
    select
    e.*,
    group_concat(distinct CONCAT(p.evseId, LPAD(p.plugId, 2, 0))) as plugNo
    from t_evse as e
    left join t_plug p on p.evseId = e.evseId
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and e.siteId=#{siteId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and e.siteId in
      <foreach item="siteId" collection="siteIdList"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
      <foreach item="bizStatus" collection="bizStatusList"
        open="and e.bizStatus in (" close=")" separator=",">
        #{bizStatus.code}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and e.`evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseIdList )">
      and e.`id` in
      <foreach item="evseId" collection="evseIdList"
        open="(" close=")" separator=",">
        #{evseId}
      </foreach>
    </if>
    group by e.evseId
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by e.id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <select id="getEvseTinyList" resultType="com.cdz360.iot.model.evse.dto.EvseTinyDto">
    select
    e.evseId as evseNo,
    e.name
    from
    t_evse e
    inner join t_site s on
    e.siteId = s.dzId
    inner join t_r_commercial comm on
    s.commId = comm .id
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and e.siteId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and e.evseId in
      <foreach collection="evseNoList" item="evseNo" open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( bizStatusList )">
      <foreach item="bizStatus" collection="bizStatusList"
        open="and e.bizStatus in (" close=")" separator=",">
        #{bizStatus.code}
      </foreach>
    </if>
    order by
    e.id desc
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <select id="getEvseModelVoList" resultMap="EVSE_MODEL_VO">
    select
    a.*,
    CONCAT(p.evseId, LPAD(p.plugId, 2, 0)) onlyPlugNo,
    (case
    when LENGTH(trim(p.name))>0 then p.name
    else CONCAT(p.plugId, '枪')
    end) plugName,
    p.plugId plugId,
    module.moduleType ,
    module.`number`,
    module.`deviceName`,
    sim.iccid AS simIccid,
    sim.msisdn AS simMsisdn
    from
    (select
    t_evse.*,
    group_concat(distinct CONCAT(p.evseId, LPAD(p.plugId, 2, 0))) as plugNo,
    model.brand,
    model.series,
    model.flags
    from
    t_evse
    left join t_plug p on
    p.evseId = t_evse.evseId
    left join t_evse_model model on
    model.id = t_evse.modelId
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and t_evse.siteId=#{siteId}
    </if>
    <if test="supplyType != null">
      and t_evse.supply = #{supplyType}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and t_evse.siteId in
      <foreach item="siteId" collection="siteIdList"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( flags )">
      and
      <foreach item="flag" collection="flags"
        open="(" close=")" separator=" or ">
        JSON_CONTAINS(flags, #{flag})
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and t_evse.`evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseIdList )">
      and t_evse.`id` in
      <foreach item="evseId" collection="evseIdList"
        open="(" close=")" separator=",">
        #{evseId}
      </foreach>
    </if>
    group by t_evse.evseId
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          convert(t_evse.${sort.columnsString}, CHAR) ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
    ) as a
    left join t_plug p on p.evseId = a.evseId
    left join t_evse_module module on
    module.evseNo = a.evseId
    and module.enable = true
    LEFT JOIN t_sim sim ON a.iccid = sim.iccid
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          convert(a.${sort.columnsString}, CHAR) ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
  </select>

  <select id="getEvseListForTopology" resultMap="RESULT_MAP_EVSE_PO">
    select
    t_evse.*,
    group_concat(distinct CONCAT(p.evseId, LPAD(p.plugId, 2, 0))) as plugNo
    from t_evse
    left join t_plug p on
    p.evseId = t_evse.evseId
    left JOIN t_device_meter dm ON
    t_evse.evseId = dm.deviceId
    and dm.estimateType = 'EVSE_INPUT'
    where 1=1
    and dm.id is null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and siteId=#{siteId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and siteId in
      <foreach item="siteId" collection="siteIdList"
        open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
      and `evseId` in
      <foreach item="evseNo" collection="evseNoList"
        open="(" close=")" separator=",">
        #{evseNo}
      </foreach>
    </if>
    group by t_evse.evseId
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <!-- 不支持服务费分时的桩号列表-->
  <select id="selectByEvseIds" resultMap="RESULT_MAP_EVSE_PO">
    select * from t_evse
    <where>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(evseNoList)">
        evseId in
        <foreach collection="evseNoList" item="evseId" open="(" close=")" separator=",">
          #{evseId}
        </foreach>
      </if>
    </where>
  </select>


  <select id="selectBindInTransformerByEvseIds" resultMap="RESULT_MAP_EVSE_PO">
    select
    evse.*
    from t_site_topology_ref st
    left join t_evse evse on evse.id=st.downId
    <where>

      st.downType='EVSE'
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(evseNoList)">
        and evse.evseId in
        <foreach collection="evseNoList" item="evseId" open="(" close=")" separator=",">
          #{evseId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getEvseRecordInfo" resultType="com.cdz360.iot.model.evse.po.EvsePlugRecordPo">
    select
    supply,
    count(*) as evseTotalNum,
    COALESCE(sum(power), 0) as totalPower
    from
    iot.t_evse
    where
    `siteId` = #{siteId}
    and `evseStatus` != 'OFF'
    and `bizStatus` = 1
    group by
    supply
  </select>

  <select id="getUpgradeCleaningEvseInfo"
    resultType="com.cdz360.iot.model.evse.po.EvsePlugRecordPo">
    select
    `siteId`,
    supply,
    count(*) as evseTotalNum,
    COALESCE(sum(power), 0) as totalPower
    from
    iot.t_evse
    where
    `siteId` is not null
    and `evseStatus` != 'OFF'
    group by
    `siteId`,
    supply
  </select>


  <select id="getEvseStatusPowerBi"
    resultType="com.cdz360.iot.model.evse.vo.EvseStatusPowerBiVo">
    select e.evseStatus ,
    COALESCE(sum(e.`power`), 0) as power
    from t_evse e
    left join t_site s on e.siteId = s.dzId
    left join t_r_commercial comm on comm.id = s.commId
    where
    e.siteId is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( provinceCode )">
      and s.provinceCode = #{provinceCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cityCode )">
      and s.cityCode = #{cityCode}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and s.dzId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    group by e.evseStatus
  </select>

  <sql id="SiteAndPlugBiParamFilter">
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteStatusList )">
      and s.status in
      <foreach collection="siteStatusList" item="status"
        open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( bizTypeList )">
      and s.bizType in
      <foreach collection="bizTypeList" item="bizType"
        open="(" close=")" separator=",">
        #{bizType}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">
      and c.idChain like CONCAT(#{idChain}, '%')
    </if>
    <if test="commId != null">
      and c.id = #{commId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gids )">
      and s.dzId in (
      select siteId from t_r_site_group_site_ref as sg
      where sg.gid in
      <foreach item="gid" index="index" collection="gids"
        open=" (" separator="," close=")">
        #{gid}
      </foreach>
      )
    </if>
  </sql>

  <select id="getTotalPower" resultType="java.lang.Long">
    select IFNULL(sum(e.power), 0)
    from t_evse e
    left join t_site s on s.dzId=e.siteId
    left join t_r_commercial c on c.id=s.commId
    <where>
      e.bizStatus = 1
      <include refid="SiteAndPlugBiParamFilter"/>
    </where>
  </select>

  <select id="getEvseCount" resultType="java.lang.Integer">
    select count(1)
    from t_evse e
    left join t_site s on s.dzId=e.siteId
    left join t_r_commercial c on c.id=s.commId
    <where>
      e.bizStatus = 1
      <include refid="SiteAndPlugBiParamFilter"/>
    </where>
  </select>

  <select id="getEvseModelOrFirm" resultType="java.lang.String">
    select distinct ${type} from t_evse where ${type} is not null order by ${type} desc
  </select>

  <select id="count" resultType="java.lang.Long">
    select
    count(*)
    from
    t_evse
    where
    evseId = #{evseNo}
  </select>

  <select id="getNeedCheckCfgEvse" resultType="java.lang.String">
    select
    e.evseId
    from
    t_evse e
    inner join t_site s on
    e.siteId = s.dzId
    where
    s.topCommId = #{topCommId}
    and e.protocolVer not in (0, 200, 320)
    and e.evseStatus not in ('OFF', 'OFFLINE', 'UNKNOWN')
    limit #{start}, #{size}
  </select>

  <select id="findByModelId" resultType="java.lang.String">
    select
    evseId
    from
    t_evse
    where
    modelId = #{modelId}
    limit #{start}, #{size}
  </select>

  <select id="getFirmwareVerList" resultType="java.lang.String">
    select
    distinct firmwareVer
    from
    t_evse
    where
    firmwareVer is not null
    and firmwareVer != ''
    <if test="start != null and size != null">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getEvseSimVo" resultType="com.cdz360.iot.model.evse.dto.EvseInfoDto">
    select
    e.iccid,
    e.evseId as evseNo,
    e.siteId,
    s.name as siteName
    from
    t_evse e
    left join t_site s on
    e.siteId = s.dzId
    where
    e.iccid is not null
    and e.iccid != ''
    and e.iccid in
    <foreach item="iccid" collection="iccids" open="(" close=")" separator=",">
      #{iccid}
    </foreach>
  </select>

</mapper>