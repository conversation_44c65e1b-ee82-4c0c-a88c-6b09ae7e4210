package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.ParkChannelRoMapper;
import com.cdz360.iot.model.park.po.ParkChannelPo;
import com.cdz360.iot.model.park.vo.ParkChannelVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class ParkChannelRoDs {



	@Autowired

	private ParkChannelRoMapper parkChannelRoMapper;



	public ParkChannelPo getById(Long id) {

		return this.parkChannelRoMapper.getById(id);

	}

	public List<ParkChannelVo> getSiteChannelList(String siteId) {
		return this.parkChannelRoMapper.findSiteChannelList(siteId);
	}

	public ParkChannelPo findChannel(ParkChannelPo channelPo) {
		return this.parkChannelRoMapper.findChannel(channelPo);
	}
}

