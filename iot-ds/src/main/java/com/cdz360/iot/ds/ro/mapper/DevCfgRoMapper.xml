<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.DevCfgRoMapper">



	<resultMap id="RESULT_DEVCFG_PO" type="com.cdz360.iot.model.ess.po.DevCfgPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="commId" jdbcType="BIGINT" property="commId" />

		<result column="siteId" jdbcType="VARCHAR" property="siteId" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="type" property="type" />

		<result column="opUid" jdbcType="BIGINT" property="opUid" />

		<result column="opName" jdbcType="VARCHAR" property="opName" />

		<result column="enable" jdbcType="BOOLEAN" property="enable" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>

	<resultMap id="RESULT_DEVCFG_VO" type="com.cdz360.iot.model.ess.vo.GtiCfgVo">

    <id column="id" jdbcType="BIGINT" property="id" />

    <result column="siteId" jdbcType="VARCHAR" property="siteId" />

    <result column="name" jdbcType="VARCHAR" property="name" />

    <result column="code" jdbcType="VARCHAR" property="code" />

    <result column="ver" jdbcType="BIGINT" property="ver" />

    <result column="type" property="type" />

    <result column="commId" jdbcType="BIGINT" property="commId" />

    <result column="samplingTime" jdbcType="INTEGER" property="samplingTime" />

    <result column="timeout" jdbcType="INTEGER" property="timeout" />
    <result column="gridMode" jdbcType="INTEGER" property="gridMode" />
    <result column="bootVoltage" jdbcType="DECIMAL" property="bootVoltage" />
    <result column="minVoltage" jdbcType="DECIMAL" property="minVoltage" />
    <result column="maxVoltage" jdbcType="DECIMAL" property="maxVoltage" />
    <result column="minFrequency" jdbcType="DECIMAL" property="minFrequency" />
    <result column="maxFrequency" jdbcType="DECIMAL" property="maxFrequency" />

    <result column="opUid" jdbcType="BIGINT" property="opUid" />

    <result column="opName" jdbcType="VARCHAR" property="opName" />

    <result column="enable" jdbcType="BOOLEAN" property="enable" />

		<collection property="gridDispatchCfgPo" ofType="com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo">
			<result column="samplingTime" jdbcType="INTEGER" property="samplingTime" />
			<result column="systemSwitch" jdbcType="BOOLEAN" property="systemSwitch"/>
			<result column="systemTime" jdbcType="BOOLEAN" property="systemTime"/>
			<result column="timezone" jdbcType="INTEGER" property="timezone"/>
			<result column="quCurveMode" jdbcType="BOOLEAN" property="quCurveMode"/>
			<result column="quDispatchPowerPct" jdbcType="INTEGER" property="quDispatchPowerPct"/>
			<result column="activePowerFixedDeratingKw" jdbcType="DECIMAL" property="activePowerFixedDeratingKw"/>
			<result column="reactivePowerCompensationPf" jdbcType="DECIMAL" property="reactivePowerCompensationPf"/>
			<result column="reactivePowerCompensationQs" jdbcType="DECIMAL" property="reactivePowerCompensationQs"/>
			<result column="activePowerPctDerating" jdbcType="DECIMAL" property="activePowerPctDerating"/>
			<result column="activePowerFixedDeratingW" jdbcType="DECIMAL" property="activePowerFixedDeratingW"/>
			<result column="nightReactivePowerCompensation" jdbcType="DECIMAL" property="nightReactivePowerCompensation"/>
			<result column="reactivePowerAdjustmentTime" jdbcType="INTEGER" property="reactivePowerAdjustmentTime"/>
			<result column="quExitPowerPct" jdbcType="INTEGER" property="quExitPowerPct"/>
			<result column="reactivePowerChangeGradient" jdbcType="DECIMAL" property="reactivePowerChangeGradient"/>
			<result column="activePowerChangeGradient" jdbcType="DECIMAL" property="activePowerChangeGradient"/>
			<result column="schedulingCommandMaintenanceTime" jdbcType="INTEGER" property="schedulingCommandMaintenanceTime"/>
			<result column="gridStandardCode" jdbcType="INTEGER" property="gridStandardCode"/>
			<result column="ppnCurve" property="ppnCurve"
				typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
			<result column="quCurve" property="quCurve"
				typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
			<result column="pfuCurve" property="pfuCurve"
				typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
		</collection>

	</resultMap>



	<select id="getById"  resultMap="RESULT_DEVCFG_PO">
		select
		t_dev_cfg.*
		from t_dev_cfg
		left join t_r_commercial trc on t_dev_cfg.commId = trc.id
		where
		t_dev_cfg.enable = true
		and t_dev_cfg.id = #{id}
		and trc.idChain  like concat(#{commIdChain}, '%')
	</select>

	<select id="queryById" resultMap="RESULT_DEVCFG_PO">
		select
		*
		from t_dev_cfg
		where enable = true
		and id = #{id}
	</select>

	<sql id="queryCondition">
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and siteId = #{siteId}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( name )">
			and name like concat('%', #{name}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and trc.idChain like concat(#{commIdChain}, '%')
		</if>
		<if test="type != null">
			and `type` = #{type}
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( typeList )">
			and `type` in
			<foreach collection="typeList" item="type" open="(" close=")" separator=",">
				#{type}
			</foreach>
		</if>
	</sql>
    <select id="getDevCfgCount" resultType="java.lang.Long">
			SELECT
				count(*)
			FROM
				t_dev_cfg
				left join t_r_commercial trc on t_dev_cfg.commId = trc.id
			WHERE
				t_dev_cfg.enable = TRUE
				<include refid="queryCondition"></include>
	</select>
	<select id="getDevCfgList" resultType="com.cdz360.iot.model.ess.vo.DevCfgVo">
		SELECT
		t_dev_cfg.id,
		t_dev_cfg.NAME,
			siteId,
			type,
			opName,
			updateTime
		FROM
			t_dev_cfg
			left join t_r_commercial trc on trc.id = t_dev_cfg.commId
		WHERE
		t_dev_cfg.enable = TRUE
		<include refid="queryCondition"></include>
		ORDER BY
			id DESC
			LIMIT #{start},#{size}
	</select>
    <select id="getGtiDetailById" resultMap="RESULT_DEVCFG_VO">
		SELECT
			*
		FROM
			t_dev_cfg dev
      <choose>
        <when test="cfgType.name == 'GOOD_WE_GTI'">
          LEFT JOIN t_gti_cfg gti ON dev.id = gti.cfgId
        </when>
        <when test="cfgType.name == 'HUAWEI_GTI'">
					LEFT JOIN t_gti_grid_dispatch_cfg gti ON dev.id = gti.cfgId
        </when>
      </choose>
			LEFT JOIN t_r_commercial trc ON dev.commId = trc.id
		WHERE
			dev.ENABLE = TRUE
			AND dev.id = #{id}
			AND trc.idChain  like concat(#{commIdChain}, '%')
	</select>

	<select id="getByName" resultType="java.lang.Long">
		select id from t_dev_cfg
		where
		enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( name )">
			and name = #{name}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and siteId = #{siteId}
		</if>
	</select>


</mapper>

