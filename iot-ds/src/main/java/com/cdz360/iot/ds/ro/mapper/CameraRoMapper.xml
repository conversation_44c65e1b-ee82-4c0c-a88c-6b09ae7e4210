<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.CameraRoMapper">

	<resultMap id="RESULT_CAMERA_PO" type="com.cdz360.iot.model.camera.po.CameraPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="cameraSiteId" jdbcType="BIGINT" property="cameraSiteId" />
		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel" />
		<result column="deviceSerial" jdbcType="VARCHAR" property="deviceSerial" />
		<result column="channelId" jdbcType="VARCHAR" property="channelId" />
		<result column="channelName" jdbcType="VARCHAR" property="channelName" />
		<result column="channelNo" jdbcType="INTEGER" property="channelNo" />
		<result column="channelStatus" jdbcType="INTEGER" property="channelStatus" />
		<result column="channelPicUrl" jdbcType="VARCHAR" property="channelPicUrl" />
		<result column="liveAddress" jdbcType="VARCHAR" property="liveAddress" />
		<result column="liveAddressHD" jdbcType="VARCHAR" property="liveAddressHD" />
		<result column="hlsLiveAddress" jdbcType="VARCHAR" property="hlsLiveAddress" />
		<result column="hlsLiveAddressHD" jdbcType="VARCHAR" property="hlsLiveAddressHD" />
		<result column="liveExpireTime" jdbcType="TIMESTAMP" property="liveExpireTime" />
		<result column="cameraCaptureUrl" jdbcType="VARCHAR" property="cameraCaptureUrl" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="recorderId" jdbcType="BIGINT" property="recorderId" />
		<result column="cameraSerial" jdbcType="VARCHAR" property="cameraSerial" />
		<result column="validateCode" jdbcType="VARCHAR" property="validateCode" />
		<result column="password" jdbcType="VARCHAR" property="password" />
	</resultMap>

	<resultMap id="RESULT_CAMERA_VO" type="com.cdz360.iot.model.camera.vo.CameraVo" extends="RESULT_CAMERA_PO">
		<result column="accountToken" jdbcType="VARCHAR" property="accountToken" />
		<result column="accountExpireTime" jdbcType="TIMESTAMP" property="accountExpireTime" />
		<result column="SubAccessToken" jdbcType="VARCHAR" property="subAccessToken" />
		<result column="accessToken" jdbcType="VARCHAR" property="accessToken" />
		<result column="recordDeviceSerial" jdbcType="VARCHAR" property="recordDeviceSerial" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERA_PO">	
		select * from t_camera where id = #{id}
	</select>

	<select id="getBySiteId"
			resultMap="RESULT_CAMERA_VO">
		select
			cam.*,
			acc.accountToken as accountToken,
			acc.accountExpireTime as accountExpireTime,
			site.accessToken as SubAccessToken,
			acc.accessToken as accessToken,
			rec.deviceSerial as recordDeviceSerial
		from t_camera cam
		left join t_camera_recorder rec on rec.id = cam.recorderId
		left join t_camera_site site on site.id = cam.cameraSiteId
		left join t_camera_account acc on acc.id = site.accountId
		where
		1 = 1
		<if test="siteId != null">
			and site.siteId = #{siteId}
		</if>
		<if test="cameraId != null">
			and cam.id = #{cameraId}
		</if>
		<if test="deviceNameLike != null">
			and cam.deviceName like concat('%', #{deviceNameLike}, '%')
		</if>
		<if test="deviceSerialLike != null">
			and cam.deviceSerial like concat('%', #{deviceSerialLike}, '%')
		</if>
		<if test="enable != null">
			and cam.enable = #{enable}
			and site.enable = #{enable}
			and acc.enable = #{enable}
		</if>
		<if test="type != null">
			and acc.type = #{type}
		</if>
	</select>

	<select id="getByCameraId"
			resultMap="RESULT_CAMERA_VO">
		select
			cam.*,
			acc.accountToken as accountToken,
			acc.accountExpireTime as accountExpireTime,
			site.accessToken as SubAccessToken,
			acc.accessToken as accessToken,
			rec.deviceSerial as recordDeviceSerial
		from t_camera cam
		left join t_camera_recorder rec on rec.id = cam.recorderId
		left join t_camera_site site on site.id = cam.cameraSiteId
		left join t_camera_account acc on acc.id = site.accountId
		where
			cam.enable = true
			and site.enable = true
			and acc.enable = true
			and cam.id = #{cameraId}
	</select>

	<select id="getOpInitHlsUrl" resultType="com.cdz360.iot.model.camera.vo.OpInitCameraVo">
		select
			c.hlsLiveAddress,
			s.name as siteName
		from
			t_camera c
		inner join t_camera_site cs on
			c.cameraSiteId = cs.id
		inner join t_site s on
			cs.siteId = s.dzId
		where
			c.enable = 1
			and c.channelStatus = 1
			and c.hlsLiveAddress is not null
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( cameraIdList )">
			and c.id in
			<foreach collection="cameraIdList" item="cameraId" open="(" close=")" separator=",">
				#{cameraId}
			</foreach>
		</if>
		limit #{size}
	</select>

</mapper>
