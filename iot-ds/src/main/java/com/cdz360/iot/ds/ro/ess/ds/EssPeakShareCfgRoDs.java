package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssPeakShareCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssPeakShareCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssPeakShareCfgRoDs {



	@Autowired

	private EssPeakShareCfgRoMapper essPeakShareCfgRoMapper;

	public EssPeakShareCfgPo getById(Long id) {
		return this.essPeakShareCfgRoMapper.getById(id);
	}





}

