package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.evse.param.ListEvseModelParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.gw.vo.EvseOpInfoVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface EvseModelRoMapper {

    EvseModelPo findById(Long id);

    List<EvseModelPo> getList(ListEvseModelParam param);

    List<String> getBrandList();

    long getListCount(ListEvseModelParam param);

    EvseModelPo findByName(@NonNull @Param("exactModel") String exactModel);

    long countByCondition(@Nullable @Param("enable") Boolean enable,
                          @Nullable @Param("exactModel") String exactModel,
                          @Nullable @Param("exactBrand") String exactBrand,
                          @Nullable @Param("exactSeries") String exactSeries,
                          @Nullable @Param("exactSupply") SupplyType exactSupply,
                          @Nullable @Param("exactPower") Integer exactPower,
                          @Nullable @Param("exactPlugNum") Integer exactPlugNum);

    EvseModelPo findOneByCondition(@Nullable @Param("status") Boolean status,
                                   @Nullable @Param("exactModel") String exactModel,
                                   @Nullable @Param("exactBrand") String exactBrand,
                                   @Nullable @Param("exactSeries") String exactSeries,
                                   @Nullable @Param("exactSupply") SupplyType exactSupply,
                                   @Nullable @Param("exactPower") Integer exactPower,
                                   @Nullable @Param("exactPlugNum") Integer exactPlugNum);

    long check(@Param("id") long id);

    EvseOpInfoVo getEvseOpInfoByEvseId(@Param("evseId") String evseId);

}
