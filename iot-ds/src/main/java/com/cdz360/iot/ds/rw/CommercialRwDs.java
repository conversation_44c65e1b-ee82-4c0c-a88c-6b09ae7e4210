package com.cdz360.iot.ds.rw;

import com.cdz360.data.sync.model.DzCommercial;
import com.cdz360.iot.ds.rw.mapper.CommercialRwMapper;
import com.cdz360.iot.model.site.po.CommercialPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommercialRwDs {

    @Autowired
    private CommercialRwMapper commercialRwMapper;

    public void syncCommercial(DzCommercial comm) {
        CommercialPo commercial = new CommercialPo();
        commercial.setId(comm.getId())
                .setPid(comm.getPid())
                .setCommLevel(comm.getCommLevel())
                .setTopCommId(comm.getTopCommId())
                .setCommName(comm.getCommName())
                .setShortName(comm.getShortName())
                .setIdChain(comm.getIdChain())
                .setEnable(comm.getStatus() != null && comm.getStatus() == 1);

        this.commercialRwMapper.syncCommercial(commercial);
    }
}
