package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.ro.mapper.EvsePasscodeMapper;
import com.cdz360.iot.ds.rw.mapper.EvsePasscodeRwMapper;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;

@Service
@Slf4j
public class EvsePasscodeRwDs {
    @Autowired
    private EvsePasscodeMapper evsePasscodeMapper;
    @Autowired
    private EvsePasscodeRwMapper evsePasscodeRwMapper;

    /**
     * @Description: 更新成功返回最新版本号更新失败返回空
     * @Author: JLei
     * @CreateDate: 14:21 2019/11/13
     */
    @Transactional
    public Long updateEvsePasscode(String evseNo, String passcode) {
        if (passcode == null) {
            passcode = "";
        }
        // 获取桩密钥最新版本号
        Long ver = evsePasscodeMapper.lastEvsePasscodeVer(evseNo);
        if (ver != null) {
            EvsePasscodePo lastVerEvsePasscode = evsePasscodeMapper.selectByEvseNo(evseNo, ver, false);
            if (passcode.equalsIgnoreCase(lastVerEvsePasscode.getPasscode())) {
                log.info("最新版本秘钥未更新，不做更改");
                // 密钥为改变不做更新
                return null;
            }
            // 2, 如果当前有密钥记录, 更新为enable=false;
            evsePasscodeRwMapper.disableEvsePasscode(lastVerEvsePasscode.getId());
        }
        // 3, 插入一条新的密钥记录, ver +1
        EvsePasscodePo evsePasscode = new EvsePasscodePo()
                .setPasscode(passcode)
                .setVer(ver == null ? 1L : ver + 1)
                .setEvseNo(evseNo)
                .setEnable(true);
        int insertFlag = evsePasscodeRwMapper.insert(evsePasscode);
        return insertFlag >= 1 ? evsePasscode.getVer() : null;
    }

    /**
     * 获取充电桩的密钥
     *
     * @param evseNo 桩编号
     * @param ver    密钥版本号. 传null时查询最新的
     * @param lock   是否锁记录
     * @return 返回充电桩密钥的数据结构
     */
    public EvsePasscodePo getEvsePasscode(@NotBlank(message = "参数错误, evseNo不能为空") String evseNo, @Nullable Long ver, boolean lock) {
        EvsePasscodePo evsePasscodePo = null;
        if (ver == null) {
            // 最新版本号
            Long lastVer = evsePasscodeMapper.lastEvsePasscodeVer(evseNo);
            if (lastVer != null) {
                evsePasscodePo = evsePasscodeMapper.selectByEvseNo(evseNo, lastVer, lock);
            }
            return evsePasscodePo;
        }
        evsePasscodePo = evsePasscodeMapper.selectByEvseNo(evseNo, ver, lock);
        return evsePasscodePo;
    }
}
