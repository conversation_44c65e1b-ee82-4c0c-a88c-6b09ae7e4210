package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssGeneratorCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssGeneratorCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssGeneratorCfgRwDs {



	@Autowired

	private EssGeneratorCfgRwMapper essGeneratorCfgRwMapper;



	public boolean insertEssGeneratorCfg(EssGeneratorCfgPo essGeneratorCfgPo) {

		return this.essGeneratorCfgRwMapper.insertEssGeneratorCfg(essGeneratorCfgPo) > 0;

	}

	public void insertBeforeSelect(Long oldId, Long newId) {

		this.essGeneratorCfgRwMapper.insertBeforeSelect(oldId,newId);

	}

	public boolean updateEssGeneratorCfg(EssGeneratorCfgPo essGeneratorCfgPo) {

		return this.essGeneratorCfgRwMapper.updateEssGeneratorCfg(essGeneratorCfgPo) > 0;

	}





}

