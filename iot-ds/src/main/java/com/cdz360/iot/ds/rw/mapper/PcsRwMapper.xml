<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.PcsRwMapper">

  <update id="updatePcs" parameterType="com.cdz360.iot.model.pcs.po.PcsPo">
    update t_pcs set
    <if test="name != null">
      `name` = #{name},
    </if>
    <if test="vendor != null">
      `vendor` = #{vendor},
    </if>
    <if test="syncSysTime != null">
      `syncSysTime` = #{syncSysTime},
    </if>
    <if test="ratedPower != null">
      `ratedPower` = #{ratedPower},
    </if>
    <if test="invQg != null">
      `invQg` = #{invQg},
    </if>
    <if test="invPg != null">
      `invPg` = #{invPg},
    </if>
    <if test="gridMode != null">
      `gridMode` = #{gridMode, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
    </if>
    updateTime=now()
    where id = #{id}
  </update>

  <insert id="batchUpsetPcs">
    insert into t_pcs
    (`name`,`dno`, `vendor`, `essDno`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.name},#{po.dno}, #{po.vendor}, #{po.essDno}, now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    `name` = values(name),
    `vendor` = values(vendor),
    `enable` = true,
    updateTime=now()
  </insert>

  <update id="offlinePcs">
    update t_pcs set
    enable = false
    where essDno = #{essDno}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( remainDnoList )">
      and dno not in
      <foreach collection="remainDnoList" open="(" close=")"
        separator="," item="dno">
        #{dno}
      </foreach>
    </if>
  </update>

</mapper>

