<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssRoMapper">

  <resultMap id="RESULT_ESS_PO" type="com.cdz360.iot.model.ess.po.EssPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="sn" jdbcType="VARCHAR" property="sn"/>
    <result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="countryCode" jdbcType="VARCHAR" property="countryCode"/>
    <result column="timeZone" jdbcType="VARCHAR" property="timeZone"/>
    <result column="net" property="net"/>
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
    <result column="type" jdbcType="VARCHAR" property="type"/>
    <result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="alertStatus" property="alertStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="cfgId" jdbcType="BIGINT" property="cfgId"/>
    <result column="deliverCfgId" jdbcType="BIGINT" property="deliverCfgId"/>
    <result column="cfgStatus" property="cfgStatus"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="cfgSuccessId" jdbcType="BIGINT" property="cfgSuccessId"/>
    <result column="cfgSuccessTime" jdbcType="TIMESTAMP" property="cfgSuccessTime"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

    <result column="powerMax" property="powerMax"/>
    <result column="powerMin" property="powerMin"/>
    <result column="capacityMax" property="capacityMax"/>
    <result column="capacityMin" property="capacityMin"/>
    <result column="plantType" property="plantType"/>
    <result column="softVersion" property="softVersion"/>
    <result column="otherVersion" property="otherVersion"/>
  </resultMap>

  <resultMap id="RESULT_ESS_TINY_DTO" type="com.cdz360.iot.model.ess.dto.EssTinyDto">
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <result column="status" property="status" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
  </resultMap>

  <resultMap id="RESULT_ESS_DTO" extends="RESULT_ESS_PO" type="com.cdz360.iot.model.ess.dto.EssDto">
    <result column="cfgSuccessName" jdbcType="VARCHAR" property="cfgSuccessName"/>
  </resultMap>

  <resultMap id="RESULT_USER_ESS_VO" extends="RESULT_ESS_PO"
    type="com.cdz360.iot.model.ess.vo.UserEssVo">
    <result column="linkUserCount" property="linkUserCount"/>
    <result column="dtuSerialNo" property="dtuSerialNo"/>
    <!-- 电池信息: 铭牌 nameplate -->
    <collection property="batInfoList"
      column="{essDno=dno,size=limitSize}"
      ofType="com.cdz360.iot.model.ess.vo.BatteryInfo"
      select="com.cdz360.iot.ds.ro.ess.mapper.EssEquipRoMapper.findBatteryCluster"/>
    <!-- BMS信息: 状态 -->
    <collection property="bmsInfoList"
      column="{essDno=dno,size=limitSize,equipType=bmEquipType}"
      ofType="com.cdz360.iot.model.ess.vo.EquipInfo"
      select="com.cdz360.iot.ds.ro.ess.mapper.EssEquipRoMapper.getEquipList"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_ESS_PO">
    select * from t_ess where id = #{id}
  </select>

  <select id="findBySiteId" parameterType="com.cdz360.iot.model.pv.param.ListCtrlParam"
    resultMap="RESULT_ESS_DTO">
    select ess.*
    from t_ess ess
    where siteId = #{siteId}
  </select>

  <select id="getByGwno" parameterType="com.cdz360.iot.model.pv.param.ListCtrlParam"
    resultMap="RESULT_ESS_DTO">
    select
    ess.*,
    essCfg.name as cfgSuccessName
    from
    t_ess ess
    left join t_dev_cfg essCfg on
    essCfg.id = ess.cfgSuccessId
    and esscfg.enable = true
    left join t_ess_equip tee on
    tee.essDno = ess.dno
    and tee.status != 99
    <where>
      ess.status != 99
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
        and ess.gwno = #{gwno}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devNo )">
        and (ess.sn = #{devNo} or tee.id = #{devNo})
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( devName )">
        and (ess.name like concat('%', #{devName}, '%') or tee.name like concat('%', #{devName},
        '%'))
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tempName )">
        and essCfg.name = #{tempName}
      </if>
    </where>
    group by
    ess.dno
  </select>

  <select id="getByDno"
    resultMap="RESULT_ESS_PO">
    select * from t_ess where dno = #{dno}
  </select>
  <sql id="query_essList_condition">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ref.siteId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ref.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and trc.idChain like concat(#{commIdChain},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dno )">
      and ess.dno = #{dno}
    </if>
  </sql>
  <select id="getEssVo" resultType="com.cdz360.iot.model.ess.vo.EssVo">
    SELECT
    ess.id,
    ess.gwno,
    ess.siteId,
    dno,
    ess.name,
    ess.sn,
    ess.STATUS,
    ess.alertStatus,
    vendor,
    ess.timeZone,
    site.NAME AS siteName,
    gw.NAME AS gwName
    FROM
    t_ess ess
    LEFT JOIN t_gw_info gw ON ess.gwno = gw.gwno
    LEFT JOIN t_site site ON site.dzId = ess.siteId
    WHERE ess.dno = #{dno}

  </select>
  <select id="findEssList" resultType="com.cdz360.iot.model.ess.vo.EssVo">
    SELECT
    ess.id,
    ess.gwno,
    ess.siteId,
    dno,
    ess.name,
    ess.sn,
    ess.STATUS,
    ess.alertStatus,
    vendor,
    site.NAME AS siteName,
    gw.NAME AS gwName
    FROM
    t_ess ess
    LEFT JOIN t_gw_info gw ON ess.gwno = gw.gwno
    LEFT JOIN t_site site ON site.dzId = ess.siteId
    LEFT JOIN t_gw_site_ref ref on ref.gwno=ess.gwno
    LEFT JOIN t_r_commercial trc ON site.commId = trc.id
    WHERE
    1 =1
    and ess.status != 99
    AND ref.enable = true
    <include refid="query_essList_condition"/>
    order by ess.id DESC
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>
  <select id="getEssCount" resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    t_ess ess
    LEFT JOIN t_site site ON site.dzId = ess.siteId
    LEFT JOIN t_r_commercial trc ON site.commId = trc.id
    LEFT JOIN t_gw_site_ref ref on ref.gwno=ess.gwno
    WHERE
    1 =1
    and ess.status != 99
    and ref.enable=true
    <include refid="query_essList_condition"/>
  </select>

  <select id="getByDnoAndSn" resultMap="RESULT_ESS_PO">
    select * from t_ess where dno = #{dno} and sn = #{sn}
  </select>
  <select id="getEssCountByCfgId" resultType="java.lang.Long">
    select count(*) from t_ess where cfgSuccessId=#{cfgId}
  </select>

  <select id="getEssStatusBi" resultType="com.cdz360.iot.model.ess.vo.EssStatusBi">
    select
    ess.status ,
    count(*) as num
    from
    t_ess ess
    inner join t_site s on
    ess.siteId = s.dzId
    inner join t_r_commercial comm on
    s.commId = comm.id
    where
    ess.status in (1,2)
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like concat(#{commIdChain},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ess.siteId = #{siteId}
    </if>
    group by
    ess.status
  </select>

  <select id="getAbnormalEssNum" resultType="java.lang.Long">
    select
    count(*) as num
    from
    t_ess ess
    inner join t_site s on
    ess.siteId = s.dzId
    inner join t_r_commercial comm on
    s.commId = comm.id
    where
    ess.status = 1
    and ess.alertStatus = 2
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like concat(#{commIdChain},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and ess.siteId = #{siteId}
    </if>
  </select>

  <select id="getEquipPowerBiVo" resultType="com.cdz360.iot.model.ess.vo.EquipPowerBiVo">
    select
    te.siteId,
    max(case when tee.equipType = 2100 then tee.equipId else null end) as pcsEquipId,
    max(case when tee.equipType = 3000 then tee.equipId else null end) as batteryStackEquipId
    from
    t_ess_equip tee
    inner join t_ess te on
    tee.essDno = te.dno
    where
    tee.essDno = #{essDno}
    and te.status != 99
    and tee.status != 99
    and tee.enable = true
  </select>

  <select id="getEssMeterVo" resultType="com.cdz360.iot.model.ess.vo.EssMeterVo">
    select
    tm.dno as meterDno,
    dm.pcsAcInput,
    dm.pcsAcOutput
    from
    t_ess ess
    inner join t_ess_equip tee on
    ess.dno = tee.essDno
    inner join t_device_meter dm on
    tee.id = dm.deviceId
    inner join t_meter tm on
    dm.meterId = tm.id
    where
    ess.status != 99
    and tee.enable = true
    and dm.estimateType = #{estimateType}
    and ess.dno = #{essDno}
  </select>


  <sql id="FetchUserEssSql">
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(dtuSerialNoLike)">
        and dtu.serialNo like CONCAT('%', #{dtuSerialNoLike}, '%')
      </if>
      <if test="null != vendor">
        and ess.vendor = #{vendor}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(dnoLike)">
        and ess.dno like CONCAT('%', #{dnoLike}, '%')
      </if>
      <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( dnoList )">
        and ess.dno IN
        <foreach collection="dnoList" index="index" item="item"
          open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="(null != uid) or @com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( uidList )">
      having linkUserCount > 0
    </if>
  </sql>

  <select id="getEssTinyList"
    parameterType="com.cdz360.iot.model.ess.param.ListEssParam"
    resultMap="RESULT_ESS_TINY_DTO">
    select * from t_ess ess
    where ess.status <![CDATA[ <> ]]> 99
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(siteId)">
      and ess.siteId = #{siteId}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( gids )">
      and ess.siteId in (
      select siteId from t_r_site_group_site_ref where gid in
      <foreach collection="gids" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
      )
    </if>
  </select>

  <select id="findUserEssList"
    parameterType="com.cdz360.iot.model.ess.param.FetchUserEssParam"
    resultMap="RESULT_USER_ESS_VO">
    select 100 as limitSize, 2400 as bmEquipType, ess.*,
    dtu.serialNo dtuSerialNo,
    (select count(`ref`.uid) from t_user_device_ref `ref`
    where `ref`.dno = ess.dno and `ref`.enable = true
    <if test="null != uid">
      and `ref`.uid = #{uid}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( uidList )">
      and `ref`.uid IN
      <foreach collection="uidList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    ) linkUserCount
    <!--    ,-->
    <!--    (select bms.status from t_ess_equip bms-->
    <!--    where bms.essDno = ess.dno and bms.equipId = 2400) bmsStatus &lt;!&ndash;2400表示BMS&ndash;&gt;-->
    from t_ess_dtu dtu
    left join t_ess_dtu_ess_ref `ref` on `ref`.serialNo = dtu.serialNo
    left join t_ess ess on ess.dno = `ref`.dno
    <include refid="FetchUserEssSql"/>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>
  <select id="userEssCount"
    parameterType="com.cdz360.iot.model.ess.param.FetchUserEssParam"
    resultType="java.lang.Long">
    select count(ess.dno) from
    (select ess.*, (select count(`ref`.uid) from t_user_device_ref `ref`
    where `ref`.dno = ess.dno and `ref`.enable = true
    <if test="null != uid">
      and `ref`.uid = #{uid}
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( uidList )">
      and `ref`.uid IN
      <foreach collection="uidList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    ) linkUserCount
    from t_ess_dtu dtu
    left join t_ess_dtu_ess_ref `ref` on `ref`.serialNo = dtu.serialNo
    left join t_ess ess on ess.dno = `ref`.dno
    <include refid="FetchUserEssSql"/>
    ) ess
  </select>

  <select id="countUserEss"
    parameterType="com.cdz360.iot.model.ess.param.CountUserEssParam"
    resultType="com.cdz360.iot.model.ess.dto.UserDeviceCountDto">
    select uid, count(dno) essCount
    from t_user_device_ref
    where `enable` = true
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( uidList )">
      and uid IN
      <foreach collection="uidList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by uid
  </select>

  <select id="getUserEssBySerialNumber" resultMap="RESULT_ESS_PO">
    select * from t_ess where serialNo = #{serialNo}
    limit 1
  </select>

  <select id="findSiteEssAmount" resultType="com.cdz360.iot.model.ess.vo.SiteEssVo">
    SELECT
    ess.siteId,
    count(*) as count
    FROM
    t_ess as ess
    LEFT JOIN t_gw_site_ref as ref ON ref.gwno = ess.gwno
    WHERE
    `status` != 99
    AND ref.`enable` = TRUE
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( siteIdList )">
      AND ess.siteId IN
      <foreach collection="siteIdList" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY
    siteId;
  </select>
</mapper>

