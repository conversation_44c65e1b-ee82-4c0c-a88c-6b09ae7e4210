package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.SrsRwMapper;
import com.cdz360.iot.model.srs.po.SrsPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class SrsRwDs {

	@Autowired
	private SrsRwMapper srsRwMapper;

	public boolean upsetSrs(SrsPo srsPo) {
		return this.srsRwMapper.upsetSrs(srsPo) > 0;
	}

	public int batchUpsetSrs(List<SrsPo> srsPoList) {

		return this.srsRwMapper.batchUpsetSrs(srsPoList);

	}

    public int offlineEss(@NonNull String gwno, @Nullable List<String> remainDnoList) {

        return this.srsRwMapper.offlineSrs(gwno, remainDnoList);

    }

}

