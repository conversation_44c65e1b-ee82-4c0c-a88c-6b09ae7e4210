package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.EvseBundlePc;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName： EvseBundlePcRwMapper
 * @Description: 升级包上的各个PC板详情数据访问层-RW
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:24
 */
@Mapper
public interface EvseBundlePcRwMapper {
    int deleteByBundleId(@Param("bundleId") Long bundleId);

    int insert(EvseBundlePc record);

    int insertSelective(EvseBundlePc record);

    int updateByPrimaryKeySelective(EvseBundlePc record);

    int updateByPrimaryKey(EvseBundlePc record);

    int batchInsert(@Param("list") List<EvseBundlePc> list);
}