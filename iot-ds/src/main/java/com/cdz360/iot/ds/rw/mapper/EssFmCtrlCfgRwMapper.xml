<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssFmCtrlCfgRwMapper">



	<resultMap id="RESULT_ESSFMCTRLCFG_PO" type="com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="fmCtrlEnable" jdbcType="BOOLEAN" property="fmCtrlEnable" />

		<result column="freqDeviation" jdbcType="INTEGER" property="freqDeviation" />

		<result column="scope" jdbcType="INTEGER" property="scope" />

		<result column="stepLength" jdbcType="INTEGER" property="stepLength" />

		<result column="inOutPower" jdbcType="DECIMAL" property="inOutPower" />

		<result column="climbingTime" jdbcType="INTEGER" property="climbingTime" />

		<result column="droop" jdbcType="INTEGER" property="droop" />

		<result column="deadZone" jdbcType="INTEGER" property="deadZone" />

		<result column="negFreqRespError" jdbcType="INTEGER" property="negFreqRespError" />

		<result column="posFreqRespError" jdbcType="INTEGER" property="posFreqRespError" />

		<result column="innerStrDeadZone" jdbcType="INTEGER" property="innerStrDeadZone" />

		<result column="accumTime" jdbcType="INTEGER" property="accumTime" />

		<result column="accumThreshold" jdbcType="INTEGER" property="accumThreshold" />

		<result column="settlingTime" jdbcType="INTEGER" property="settlingTime" />

		<result column="waitingTime" jdbcType="INTEGER" property="waitingTime" />

		<result column="powerResetSlope" jdbcType="INTEGER" property="powerResetSlope" />

	</resultMap>



	<insert id="insertEssFmCtrlCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo">

		insert into t_ess_fm_ctrl_cfg (`cfgId`,

			`fmCtrlEnable`,

			`freqDeviation`,

			`scope`,

			`stepLength`,

			`inOutPower`,

			`climbingTime`,

			`droop`,

			`deadZone`,

			`negFreqRespError`,

			`posFreqRespError`,

			`innerStrDeadZone`,

			`accumTime`,

			`accumThreshold`,

			`settlingTime`,

			`waitingTime`,

			`powerResetSlope`)

		values (#{cfgId},

			#{fmCtrlEnable},

			#{freqDeviation},

			#{scope},

			#{stepLength},

			#{inOutPower},

			#{climbingTime},

			#{droop},

			#{deadZone},

			#{negFreqRespError},

			#{posFreqRespError},

			#{innerStrDeadZone},

			#{accumTime},

			#{accumThreshold},

			#{settlingTime},

			#{waitingTime},

			#{powerResetSlope})

		ON DUPLICATE KEY UPDATE
			fmCtrlEnable = #{fmCtrlEnable},

			freqDeviation = #{freqDeviation},

			scope = #{scope},

			stepLength = #{stepLength},

			inOutPower = #{inOutPower},

			climbingTime = #{climbingTime},

			droop = #{droop},

			deadZone = #{deadZone},

			negFreqRespError = #{negFreqRespError},

			posFreqRespError = #{posFreqRespError},

			innerStrDeadZone = #{innerStrDeadZone},

			accumTime = #{accumTime},

			accumThreshold = #{accumThreshold},

			settlingTime = #{settlingTime},

			waitingTime = #{waitingTime},

			powerResetSlope = #{powerResetSlope}


	</insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_fm_ctrl_cfg(cfgId,fmCtrlEnable,freqDeviation,scope,stepLength,inOutPower,climbingTime,droop,deadZone,negFreqRespError,posFreqRespError,innerStrDeadZone,accumTime,accumThreshold,settlingTime,waitingTime,powerResetSlope)
		select #{newId}, fmCtrlEnable,freqDeviation,scope,stepLength,inOutPower,climbingTime,droop,deadZone,negFreqRespError,posFreqRespError,innerStrDeadZone,accumTime,accumThreshold,settlingTime,waitingTime,powerResetSlope from t_ess_fm_ctrl_cfg where cfgId = #{oldId};
	</insert>


</mapper>

