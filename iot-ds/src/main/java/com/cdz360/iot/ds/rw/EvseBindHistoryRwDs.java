package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseBindHistoryRwMapper;
import com.cdz360.iot.model.evse.EvseBindHistoryPo;
import com.cdz360.iot.model.type.EvseHistoryActionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Classname EvseBindHistoryRwDs
 * @Description
 * @Date 3/28/2020 1:59 PM
 * @Created by Rafael
 */
@Service
public class EvseBindHistoryRwDs {
    @Autowired
    private EvseBindHistoryRwMapper evseBindHistoryRwMapper;

    private int insert(EvseBindHistoryPo evseBindHistoryPo) {
        return evseBindHistoryRwMapper.insert(evseBindHistoryPo);
    }

    public int insertEvseAction(EvseHistoryActionType action, String evseNo, String siteId) {
        EvseBindHistoryPo evseBindHistoryPo = new EvseBindHistoryPo();
        evseBindHistoryPo.setTime(new Date());
        evseBindHistoryPo.setSiteId(siteId);
        evseBindHistoryPo.setEvseNo(evseNo);
        evseBindHistoryPo.setAction(action);
        return insert(evseBindHistoryPo);
    }
}