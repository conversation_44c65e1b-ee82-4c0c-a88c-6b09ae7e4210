package com.cdz360.iot.ds.ro;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.iot.ds.ro.mapper.PlugRoMapper;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.SiteAndPlugBiVo;
import com.cdz360.iot.model.evse.dto.SiteDeviceBiDto;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.vo.PlugStatusBiVo;
import com.cdz360.iot.model.evse.vo.PlugSupplyBiVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PlugRoDs {

    @Autowired
    private PlugRoMapper plugRoMapper;


    public PlugPo getPlug(String evseNo, int plugIdx) {
        return this.plugRoMapper.getPlug(evseNo, plugIdx);
    }

    public PlugVo getPlugByPlugNo(String plugNo) {
        return plugRoMapper.getPlugByPlugNo(plugNo);
    }

    public ListResponse<PlugVo> getPlugList(ListPlugParam param) {
        return new ListResponse<>(plugRoMapper.getPlugList(param));
    }

    public List<String> getPlugNos(ListPlugParam param) {
        return plugRoMapper.getPlugNos(param);
    }

    public List<EvsePlugRecordPo> getPlugRecordInfo(String siteId) {
        return plugRoMapper.getPlugRecordInfo(siteId);
    }

    public List<EvsePlugRecordPo> getUpgradeCleaningPlugInfo() {
        return plugRoMapper.getUpgradeCleaningPlugInfo();
    }

    /**
     * 根据电流类型统计桩/枪数量
     *
     * @return
     */
    public List<PlugSupplyBiVo> getPlugSupplyBi(@Nullable String commIdChain) {
        return this.plugRoMapper.getPlugSupplyBi(commIdChain);
    }

    /**
     * 根据状态统计桩/枪数量
     *
     * @return
     */
    public List<PlugStatusBiVo> getPlugStatusBi(@Nullable String provinceCode,
                                                @Nullable String cityCode,
                                                @Nullable String siteId,
                                                @Nullable String commIdChain) {
        return this.plugRoMapper.getPlugStatusBi(provinceCode, cityCode, siteId, commIdChain);
    }

    /**
     * 统计场站枪头数量
     *
     * @param siteIdList
     * @return
     */
    public List<SiteDeviceBiDto> getSitePlugBiList(List<String> siteIdList) {
        return this.plugRoMapper.getSitePlugBiList(siteIdList);
    }

    public SiteAndPlugBiVo getSiteAndPlugStatus(SiteAndPlugBiParam param) {
        return plugRoMapper.getSiteAndPlugStatus(param);
    }


    /**
     * 获取枪头编号列表
     *
     * @param evseNo 桩编号
     * @return 枪头编号列表
     */
    public List<String> getPlugNoList(String evseNo) {
        return this.plugRoMapper.getPlugNoList(evseNo);
    }

    public List<String> getPlugNoListByEvseNoList(List<String> evseNoList) {
        return this.plugRoMapper.getPlugNoListByEvseNoList(evseNoList);
    }

    public List<PlugPo> getPlugPoList(String evseNo) {
        return this.plugRoMapper.getPlugPoList(evseNo);
    }
}
