package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.pv.po.GtiPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface GtiRwMapper {

    GtiPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    GtiPo getBySiteIdAndGwnoAndSid(
        @Param("siteId") String siteId,
        @Param("gwno") String gwno,
        @Param("sid") Integer sid,
        @Param("lock") boolean lock);

    int upsetGti(GtiPo gtiPo);

    int batchUpsetGti(@Param("poList") List<GtiPo> poList);

    int updateGtiByDno(GtiPo gtiPo);

    int offlineGti(
        @NonNull @Param("gwno") String gwno,
        @Nullable @Param("remainDnoList") List<String> remainDnoList);
}

