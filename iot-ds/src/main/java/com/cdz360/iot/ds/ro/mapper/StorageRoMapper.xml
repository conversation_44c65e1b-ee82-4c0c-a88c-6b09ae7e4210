<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.StorageRoMapper">

  <resultMap id="RESULT_STORAGE_PO" type="com.cdz360.iot.model.parts.po.StoragePo">
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="type" property="type"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
  </resultMap>

  <select id="getByCode" resultType="com.cdz360.iot.model.parts.po.StoragePo">
    select * from t_storage where `code` = #{code}
  </select>
  <select id="getByUid" resultType="com.cdz360.iot.model.parts.po.StoragePo">
    select * from t_storage where `uid` in
    <foreach collection="uidList" open="(" close=")" separator="," item="uid">
      #{uid}
    </foreach>
  </select>


</mapper>

