package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.meter.po.MeterPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssBatteryBundleRwMapper {

	int upsetEssBatteryCluster(EssBatteryBundlePo essBatteryClusterPo);

	int batchInsertBundle(@Param("poList") List<EssBatteryBundlePo> poList);





}

