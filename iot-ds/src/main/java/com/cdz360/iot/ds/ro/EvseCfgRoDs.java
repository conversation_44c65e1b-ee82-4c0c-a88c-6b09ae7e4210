package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.EvseCfgRoMapper;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.po.EvseCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class EvseCfgRoDs {


    @Autowired
    private EvseCfgRoMapper mapper;

    public EvseCfgPo queryById(Long cfgId) {
        return this.mapper.queryById(cfgId);
    }

    public CfgEvseAllV2 queryByEvseNo(String essDno) {
        return this.mapper.queryByEvseNo(essDno);
    }

}

