package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteGroupSiteRefRwMapper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * t_r_site_group_site_ref 写操作
 */
@Service
public class SiteGroupSiteRefRwDs {

    @Autowired
    private SiteGroupSiteRefRwMapper siteGroupSiteRefRwMapper;

    public int batchInsert(String gid, List<String> siteIdList) {
        return siteGroupSiteRefRwMapper.batchInsert(gid, siteIdList);
    }

    public int batchDelete(String gid, List<String> exSiteIdList) {
        return siteGroupSiteRefRwMapper.batchDelete(gid, exSiteIdList);
    }

    public int deleteByGid(String gid) {
        return siteGroupSiteRefRwMapper.deleteByGid(gid);
    }

    public int deleteBySiteId(String siteId) {
        return siteGroupSiteRefRwMapper.deleteBySiteId(siteId);
    }
}
