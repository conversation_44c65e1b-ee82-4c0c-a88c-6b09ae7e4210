<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.MeterRoMapper">

	<resultMap id="RESULT_METER_PO" type="com.cdz360.iot.model.meter.po.MeterPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="no" jdbcType="VARCHAR" property="no" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="status" jdbcType="JAVA_OBJECT" property="status" />
		<result column="net" jdbcType="JAVA_OBJECT" property="net" />
		<result column="vendor" jdbcType="VARCHAR" property="vendor" />
		<result column="ctr" jdbcType="INTEGER" property="ctr" />
		<result column="vtr" jdbcType="INTEGER" property="vtr" />
		<result column="otherDevice" jdbcType="BOOLEAN" property="otherDevice" />
		<result column="comment" jdbcType="VARCHAR" property="comment" />
		<result column="lastActiveTime" jdbcType="TIMESTAMP" property="lastActiveTime" />
		<result column="gwno" jdbcType="VARCHAR" property="gwno" />
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="protocol" jdbcType="VARCHAR" property="protocol" />
		<result column="sid" jdbcType="INTEGER" property="sid" />
		<result column="powerLoss" jdbcType="BOOLEAN" property="powerLoss" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<resultMap id="RESULT_METER_EVSE_VO" type="com.cdz360.iot.model.meter.vo.MeterEvseVo" extends="RESULT_METER_VO">
		<collection property="deviceMeterPoList" ofType="com.cdz360.iot.model.meter.vo.DeviceMeterVo" javaType="list">
			<result column="eid" jdbcType="BIGINT" property="id" />
			<result column="estimateType" jdbcType="VARCHAR" property="estimateType" />
			<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
			<result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
			<result column="meterId" jdbcType="BIGINT" property="meterId" />
			<result column="pcsAcInput" jdbcType="VARCHAR" property="pcsAcInput" />
			<result column="pcsAcOutput" jdbcType="VARCHAR" property="pcsAcOutput" />
			<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		</collection>
	</resultMap>

	<resultMap id="RESULT_METER_VO" type="com.cdz360.iot.model.meter.vo.MeterVo" extends="RESULT_METER_PO">
		<result column="siteName" jdbcType="VARCHAR" property="siteName" />
		<result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
		<result column="type" property="type" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
	</resultMap>

	<select id="getById"
			resultMap="RESULT_METER_PO">	
		select * from t_meter where id = #{id}
	</select>

	<select id="getByNo"
			resultMap="RESULT_METER_PO">
		select * from t_meter where no = #{no}
	</select>

	<select id="getByDno"
			resultMap="RESULT_METER_PO">
		select * from t_meter where dno = #{dno}
	</select>

    <select id="getAllBindMeter" resultMap="RESULT_METER_PO">
		select * from t_meter where siteId <![CDATA[ <> ]]> ''
    </select>

    <select id="getBindInTransformerByMeterId" resultMap="RESULT_METER_PO">
		SELECT
			meter.*
		FROM
			t_meter meter
		LEFT JOIN t_site_topology_ref st ON st.downId = meter.id
		<where>
			st.downType = 'METER'
			<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(ids)">
				and meter.id in
				<foreach collection="ids" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>

		</where>

    </select>

    <select id="getByGwno" resultMap="RESULT_METER_PO">
		SELECT
			meter.*
		FROM
			t_meter meter
		<where>
			meter.gwno = #{gwno}
			and meter.net = 'S485'
		</where>

    </select>

	<select id="getMeterList"
			resultMap="RESULT_METER_EVSE_VO">

		select
			m.*,
			em.id AS eid,
			em.estimateType,
			em.deviceId,
			case
				em.estimateType when 'EVSE_INPUT' then (
				select
					name
				from
					t_evse
				where
					evseId = em.deviceId)
				when 'PCS_AC_IN_OUT' then (
				select
					name
				from
					t_ess_equip
				where
					id = em.deviceId)
				else null
			end as deviceName,
			em.meterId,
			em.pcsAcInput,
			em.pcsAcOutput,
			em.createTime,
			siteName as siteName
		from (

		select
			meter.*,
			site.name as siteName
		from t_meter meter
		left join t_site site on site.dzId=meter.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where
		1 = 1
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( meterIdList )">
			and meter.id IN
			<foreach collection="meterIdList" index="index" item="item"
					 open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="siteId != null">
			and meter.siteId = #{siteId}
		</if>
		<if test="idChain != null">
			and comm.idChain like concat(#{idChain}, '%')
		</if>
		<if test="powerLoss != null">
			and meter.powerLoss = #{powerLoss}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(sk)">
		and (
			meter.`NO` LIKE concat('%', #{sk}, '%')
		OR meter.`NAME` LIKE concat('%', #{sk}, '%')
		)
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(meterNoLike)">
		and meter.`no` LIKE concat('%', #{meterNoLike}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(siteNameLike)">
		and site.`name` LIKE concat('%', #{siteNameLike}, '%')
		</if>
		order by meter.updateTime desc
		<if test="start != null and size != null">
			limit #{start}, #{size}
		</if>
		)

		m LEFT JOIN t_device_meter em ON em.meterId = m.id
		order by m.updateTime desc
	</select>

	<select id="getMeterListTotal" resultType="long">
		select count(1)
		from t_meter meter
		left join t_site site on site.dzId=meter.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where
		1 = 1
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( meterIdList )">
			and meter.id IN
			<foreach collection="meterIdList" index="index" item="item"
					 open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="siteId != null">
			and meter.siteId = #{siteId}
		</if>
		<if test="idChain != null">
			and comm.idChain like concat(#{idChain}, '%')
		</if>
		<if test="powerLoss != null">
			and meter.powerLoss = #{powerLoss}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(sk)">
			and (
			meter.`NO` LIKE concat('%', #{sk}, '%')
			OR meter.`NAME` LIKE concat('%', #{sk}, '%')
			)
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(meterNoLike)">
			and meter.`no` LIKE concat('%', #{meterNoLike}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(siteNameLike)">
			and site.`name` LIKE concat('%', #{siteNameLike}, '%')
		</if>
	</select>

	<select id="getAllOnline"
			resultMap="RESULT_METER_PO">
		select * from t_meter where status = 'ONLINE'
	</select>

	<select id="getMeterVoList"
			resultMap="RESULT_METER_VO">
		select
		meter.*,
		site.name as siteName
		from
		t_meter meter
		left join t_site site on meter.siteId = site.dzId
		where 1=1
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
			and meter.gwno = #{gwno}
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( dnoList )">
			and meter.dno in
			<foreach collection="dnoList" index="index" item="item"
				open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		limit #{start},#{size}
	</select>

	<select id="getMeterVoList2" parameterType="com.cdz360.iot.model.param.MeterListParam"
		resultMap="RESULT_METER_VO">
		select
		meter.*,
		site.name as siteName,
		ee.equipModel deviceModel,
		ee.equipType type
		from t_meter meter
		left join t_site site on meter.siteId = site.dzId
		left join t_r_commercial comm on comm.id = site.commId
		left join t_ess_equip ee on meter.dno = ee.dno
		<where>
			<if test="idChain != null">
				and comm.idChain like concat(#{idChain}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( meterNoLike )">
				and meter.no LIKE concat('%', #{meterNoLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( meterNameLike )">
				and meter.name LIKE concat('%', #{meterNameLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( verdorLike )">
				and meter.vendor LIKE concat('%', #{verdorLike}, '%')
			</if>
			<if test="status != null">
				and meter.status = #{status}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceModelLike )">
				and ee.equipModel LIKE concat('%', deviceModelLike, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteNameLike )">
				and site.name LIKE concat('%', siteNameLike, '%')
			</if>
		</where>
		limit #{start},#{size}
	</select>

	<select id="getMeterVoList2Count" parameterType="com.cdz360.iot.model.param.MeterListParam"
		resultMap="RESULT_METER_VO">
		select
		count(*)
		from t_meter meter
		left join t_site site on meter.siteId = site.dzId
		left join t_r_commercial comm on comm.id = site.commId
		left join t_ess_equip ee on meter.dno = ee.dno
		<where>
			<if test="idChain != null">
				and comm.idChain like concat(#{idChain}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( meterNoLike )">
				and meter.no LIKE concat('%', #{meterNoLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( meterNameLike )">
				and meter.name LIKE concat('%', #{meterNameLike}, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( verdorLike )">
				and meter.vendor LIKE concat('%', #{verdorLike}, '%')
			</if>
			<if test="status != null">
				and meter.status = #{status}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceModelLike )">
				and ee.equipModel LIKE concat('%', deviceModelLike, '%')
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteNameLike )">
				and site.name LIKE concat('%', siteNameLike, '%')
			</if>
		</where>
	</select>

	<select id="getSiteIdList" resultType="java.lang.String">
		select distinct(siteId)
		from t_meter meter
		<if test="idChain != null">
			left join t_site site on meter.siteId = site.dzId
			left join t_r_commercial comm on comm.id = site.commId
		</if>
		where
		    1=1
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(siteIdList)">
			and meter.siteId in
			<foreach collection="siteIdList" item="siteId"
				open="(" separator="," close=")">
				#{siteId}
			</foreach>
		</if>
		<if test="idChain != null">
			and comm.idChain like concat(#{idChain}, '%')
		</if>
		order by meter.siteId desc
		<if test="start != null and size != null">
			limit #{start}, #{size}
		</if>
	</select>

	<select id="getSiteIdListTotal" resultType="java.lang.Long">
		select count(distinct(siteId))
		from t_meter meter
		left join t_site site on meter.siteId = site.dzId
		left join t_r_commercial comm on comm.id = site.commId
		where
		    1=1
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(siteIdList)">
			and siteId in
			<foreach collection="siteIdList" item="siteId"
				open="(" separator="," close=")">
				#{siteId}
			</foreach>
		</if>
		<if test="idChain != null">
			and comm.idChain like concat(#{idChain}, '%')
		</if>
	</select>


</mapper>
