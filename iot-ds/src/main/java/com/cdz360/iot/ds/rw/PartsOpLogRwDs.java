package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.PartsOpLogRwMapper;
import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class PartsOpLogRwDs {

    @Autowired
    private PartsOpLogRwMapper partsOpLogRwMapper;

    public PartsOpLogPo getById(Long id, boolean lock) {
        return this.partsOpLogRwMapper.getById(id, lock);
    }

    public boolean insertPartsOpLog(PartsOpLogPo partsOpLogPo) {
        return this.partsOpLogRwMapper.insertPartsOpLog(partsOpLogPo) > 0;
    }

    public boolean updatePartsOpLog(PartsOpLogPo partsOpLogPo) {
        return this.partsOpLogRwMapper.updatePartsOpLog(partsOpLogPo) > 0;
    }
}

