package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname UpgradeTaskMapper
 * @Description TODO
 * @Date 9/11/2019 2:43 PM
 * @Created by Rafael
 */
@Mapper
public interface UpgradeTaskQueryMapper {
    List<UpgradeTaskVo> select(@Param("siteId") String siteId,
                               @Param("taskId") Long taskId,
                               @Param("bundleKeyword") String bundleKeyword,
                               @Param("start") Integer start,
                               @Param("end") Integer end);

    /**
     * 海外版查询列表，bundleId对应的是t_evse_package里的id
     * @param siteId
     * @param taskId
     * @param bundleKeyword
     * @param start
     * @param end
     * @return
     */
    List<UpgradeTaskVo> selectEssList(@Param("siteId") String siteId,
                               @Param("taskId") Long taskId,
                               @Param("bundleKeyword") String bundleKeyword,
                               @Param("start") Integer start,
                               @Param("end") Integer end);

    Long selectCount(@Param("siteId") String siteId,
                     @Param("taskId") Long taskId,
                     @Param("bundleKeyword") String bundleKeyword);

    /**
     * 海外版查询列表计数，bundleId对应的是t_evse_package里的id
     * @param siteId
     * @param taskId
     * @param bundleKeyword
     * @return
     */
    Long selectEssCount(@Param("siteId") String siteId,
                     @Param("taskId") Long taskId,
                     @Param("bundleKeyword") String bundleKeyword);

    UpgradeTaskVo selectById(@Param("id") Long id);

    UpgradeTaskInfoVo getUpgradeTaskInfo(@Param("taskId") Long taskId);

    UpgradeTaskInfoVo getEssUpgradeTaskInfo(@Param("taskId") Long taskId);

    List<UpgradeRecordVo> getUpgradeRecordVo(@Param("evseNo") String evseNo,
                                             @Param("taskId") Long taskId,
                                             @Param("bundleKeyword") String bundleKeyword,
                                             @Param("start") Integer start,
                                             @Param("size") Integer size);

    Long getUpgradeRecordVoCount(@Param("evseNo") String evseNo,
                                 @Param("taskId") Long taskId,
                                 @Param("bundleKeyword") String bundleKeyword);

}