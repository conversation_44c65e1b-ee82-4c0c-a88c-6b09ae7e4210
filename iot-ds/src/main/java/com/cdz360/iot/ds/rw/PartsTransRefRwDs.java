package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.PartsTransRefRwMapper;
import com.cdz360.iot.model.parts.po.PartsTransRefPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsTransRefRwDs {

    @Autowired
    private PartsTransRefRwMapper partsTransRefRwMapper;

    public boolean insertPartsTransRef(String partsCode, String transOrderNo) {
        final PartsTransRefPo po = new PartsTransRefPo()
            .setPartsCode(partsCode)
            .setTransOrderNo(transOrderNo);
        return this.partsTransRefRwMapper.insertPartsTransRef(po) > 0;
    }


    public boolean updatePartsTransRef(PartsTransRefPo partsTransRefPo) {
        return this.partsTransRefRwMapper.updatePartsTransRef(partsTransRefPo) > 0;
    }

}

