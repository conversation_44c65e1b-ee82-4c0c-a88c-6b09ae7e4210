package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.PlugRwMapper;
import com.cdz360.iot.model.evse.po.PlugPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PlugRwDs {

    @Autowired
    private PlugRwMapper plugRwMapper;

    public boolean updatePlug(PlugPo plug) {
        return plugRwMapper.updatePlug(plug) > 0;
    }
}
