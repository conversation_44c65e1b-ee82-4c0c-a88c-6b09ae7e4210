package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseBundlePcQueryMapper;
import com.cdz360.iot.model.evse.EvseBundlePc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname EvseBundlePcQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:32 PM
 * @Created by Rafael
 */
@Service
public class EvseBundlePcQueryDs {
    @Autowired
    private EvseBundlePcQueryMapper evseBundlePcQueryMapper;
    
    public EvseBundlePc selectByPrimaryKey(Long id) {
        return evseBundlePcQueryMapper.selectByPrimaryKey(id);
    }

    public List<EvseBundlePc> selectByBundleId(Long bundleId) {
        return evseBundlePcQueryMapper.selectByBundleId(bundleId);
    }
}