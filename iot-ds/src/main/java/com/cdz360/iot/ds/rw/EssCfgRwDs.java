package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EssCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssCfgRwDs {

    @Autowired
    private EssCfgRwMapper essCfgRwMapper;

    public EssCfgPo getByCfgId(Long cfgId, boolean lock) {
        return this.essCfgRwMapper.getByCfgId(cfgId, lock);
    }

    public boolean insertEssCfg(EssCfgPo essCfgPo) {
        return this.essCfgRwMapper.insertEssCfg(essCfgPo) > 0;
    }

    public void insertBeforeSelect(Long oldId, Long newId) {
        this.essCfgRwMapper.insertBeforeSelect(oldId, newId);
    }

//    public boolean updateEssCfg(EssCfgPo essCfgPo) {
//        return this.essCfgRwMapper.updateEssCfg(essCfgPo) > 0;
//    }

}

