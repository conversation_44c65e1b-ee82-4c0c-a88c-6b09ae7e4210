<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseModuleRwMapper">




	<insert id="insert" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.evse.po.EvseModulePo">
		INSERT INTO t_evse_module
			(evseNo,deviceName,moduleType,`number`,enable,createTime)
		VALUES
			(#{evseNo}, #{deviceName}, #{moduleType}, #{number}, 1, now())
	</insert>

	<update id="updateById">
		update
			t_evse_module
		set
			moduleType = #{moduleType},
			`number` = #{number},
			updateTime = now()
		where
			id = #{id}
	</update>

	<update id="updateModuleTypeAndNumber">
		update
			t_evse_module
		set
			moduleType = #{moduleType},
			`number` = #{number}
		where
			evseNo = #{evseNo}
			and deviceName = #{deviceName}
			and enable = 1
	</update>

	<select id="count" resultType="java.lang.Long">
		select
			count(*)
		from
			t_evse_module
		where
			evseNo = #{evseNo}
			and deviceName = #{deviceName}
			and enable = 1
	</select>

</mapper>
