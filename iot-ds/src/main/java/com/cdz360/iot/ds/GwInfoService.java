package com.cdz360.iot.ds;

import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.ds.rw.mapper.GwInfoRwQueryMapper;
import com.cdz360.iot.model.gw.GwTimeoutPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GwInfoService {
    private final Logger logger = LoggerFactory.getLogger(GwInfoService.class);

    @Autowired
    private GwInfoRwQueryMapper gwInfoRwQueryMapper;
    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;

    public GwInfoDto getByGwno(String gwno, boolean lock) {
        return this.gwInfoRwQueryMapper.getByGwno(gwno, lock);
    }

    public List<GwInfoDto> getByMac(String mac, boolean lock) {
        return this.gwInfoRwQueryMapper.getByMac(mac, lock);
    }

    public boolean update(GwInfoPo gwInfo) {
        return this.gwInfoRwQueryMapper.update(gwInfo) > 0;
    }

    public boolean updateStatus(String gwno, GwStatus gwStatus) {
        return this.gwInfoRwQueryMapper.updateStatus(gwno, gwStatus.name()) > 0;
    }

    public boolean updateLoginTime(String gwno) {
        return this.gwInfoRwQueryMapper.updateLoginTime(gwno) > 0;
    }

    public int insert(GwInfoPo gwInfo) {
        return this.gwInfoRwQueryMapper.insert(gwInfo);
    }

    public boolean updateLocation(String gwno, String cityCode, Double lon, Double lat) {
        return this.gwInfoRwQueryMapper.updateLocation(gwno, cityCode, lon, lat) > 0;
    }

    /**
     * 获取登陆超时网关下的所有场站ID列表
     * @return
     */
    public List<GwTimeoutPo> listGwTimeout(Integer timeout) {
        List<GwTimeoutPo> gwnos = this.gwInfoRwQueryMapper.getGwnosLoginTout(timeout);
        return gwnos;
    }
}
