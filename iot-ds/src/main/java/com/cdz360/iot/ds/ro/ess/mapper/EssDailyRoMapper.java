package com.cdz360.iot.ds.ro.ess.mapper;


import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import com.cdz360.iot.model.ess.vo.CommEssMapDataVo;
import com.cdz360.iot.model.ess.vo.DayEssDataBi;
import com.cdz360.iot.model.ess.vo.DaySiteEssRtDataBi;
import com.cdz360.iot.model.ess.vo.EquipSampleData;
import com.cdz360.iot.model.ess.vo.EssDataBi;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssDailyRoMapper {


    EssDailyPo getById(@Param("id") Long id);

    List<DayEssDataBi> siteRtDataOfDay(@Param("siteId") String siteId,
        @Param("fromDate") Date fromDate,
        @Param("toDate") Date toDate);

    EssDataBi rtDataOfYesterday(DayKwhParam param);

    EssDataBi rtDataOfMonth(DayKwhParam param);

    EssDataBi rtDataOfTotal(DayKwhParam param);

    List<DaySiteEssRtDataBi> siteDayOfRangeKwh(DayKwhParam param);

    List<EquipSampleData> equipRtDataSample(DataBiParam param);

    @Deprecated(since = "20240523")
    CommEssMapDataVo commEssMapData(EssMapDataParam param);
}

