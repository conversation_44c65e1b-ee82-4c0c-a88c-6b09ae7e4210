package com.cdz360.iot.ds.rw.ess.mapper;




import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

import java.lang.Double;

import java.util.Date;



@Mapper

public interface EssEquipLangRwMapper {

	EssEquipLangPo getById(@Param("id") Long id, @Param("lock") boolean lock);



	int insertEssEquipLang(EssEquipLangPo essEquipLangPo);



	int updateEssEquipLang(EssEquipLangPo essEquipLangPo);





}

