<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvsePasscodeMapper">
    <select id="selectByEvseNo" resultType="com.cdz360.iot.model.evse.po.EvsePasscodePo">
        select * from t_evse_passcode where evseNo=#{evseNo} and ver=#{ver}
        <if test="lock == true">
            for update
        </if>
    </select>

    <select id="lastEvsePasscodeVer" resultType="long">
        select MAX(ver) from t_evse_passcode where evseNo = #{evseNo,jdbcType=VARCHAR}
    </select>

    <select id="selectLatestByEvseNo" resultType="com.cdz360.iot.model.evse.po.EvsePasscodePo">
        select * from t_evse_passcode
        where enable = 1 and evseNo = #{evseNo}
        order by ver desc
        limit 1
    </select>
    
</mapper>