package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.SiteCtrlCfgLogRoMapper;
import com.cdz360.iot.model.site.po.SiteCtrlCfgLogPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname SiteCtrlCfgLogRoDs
 * @Description
 * @Date 4/23/2020 1:56 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlCfgLogRoDs {
    @Autowired
    private SiteCtrlCfgLogRoMapper siteCtrlCfgLogRoMapper;

    public SiteCtrlCfgLogPo selectByNum(String ctrlNum) {
        return siteCtrlCfgLogRoMapper.selectByNum(ctrlNum);
    }
}