<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SimRoMapper">

    <sql id="SIM_VO_FIELDS">
        s.id, s.iccid, s.msisdn, s.vendor, s.imsi,
        s.imei, s.slotStatus, s.status, s.activatedDate, s.`usage`,
        case
            when s.siteId is null or s.siteId = '' then e.siteId
            else s.siteId
        end as siteId,
        s.remark, s.createTime, s.updateTime,
        ts.name as siteName,
        e.evseId as evseNo
    </sql>

    <sql id="SIM_VO_JOIN">
        left join t_evse e on
            s.iccid = e.iccid
        left join t_site ts on
            case
                when s.siteId is null or s.siteId = '' then e.siteId = ts.dzId
                else s.siteId = ts.dzId
            end
    </sql>

    <sql id="SimListQueryCondition">
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.exactKeyword)">
            and (s.iccid = #{param.exactKeyword}
            or s.msisdn = #{param.exactKeyword}
            or s.imsi = #{param.exactKeyword}
            or s.imei = #{param.exactKeyword})
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.keyword)">
            and (s.iccid like concat("%", #{param.keyword}, "%")
            or s.msisdn like concat("%", #{param.keyword}, "%")
            or s.imsi like concat("%", #{param.keyword}, "%")
            or s.imei like concat("%", #{param.keyword}, "%"))
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.iccidLike)">
            and s.iccid like concat("%", #{param.iccidLike}, "%")
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.msisdnLike)">
            and s.msisdn like concat("%", #{param.msisdnLike}, "%")
        </if>
        <if test="param.slotStatus != null">
            and s.slotStatus = #{param.slotStatus}
        </if>
        <if test="param.relationship != null">
            <choose>
                <when test="param.relationship == 1">
                    and s.deviceType is not null
                </when>
                <when test="param.relationship == 2">
                    and s.deviceType is null
                </when>
                <when test="param.relationship == 3">
                    and s.deviceType = 'EVSE'
                </when>
                <when test="param.relationship == 4">
                    and (s.deviceType != 'EVSE' or s.deviceType is null)
                </when>
            </choose>
        </if>
        <if test="param.vendor != null">
            and s.vendor = #{param.vendor}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( param.vendors )">
            and s.vendor in
            <foreach collection="param.vendors" item="vendor" open="(" close=")" separator=",">
                #{vendor}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.siteId)">
            and (s.siteId = #{param.siteId}
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdIccids )">
                or s.iccid in
                <foreach collection="siteIdIccids" item="iccid" open="(" close=")"
                  separator=",">
                    #{iccid}
                </foreach>
            </if>
            )
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.evseNo)
            and @com.cdz360.base.utils.CollectionUtils@isNotEmpty( evseNoIccids )">
            and s.iccid in
            <foreach collection="evseNoIccids" item="iccid" open="(" close=")"
              separator=",">
                #{iccid}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.remark)">
            and s.remark like concat("%", #{param.remark}, "%")
        </if>
    </sql>

    <select id="getSimList" parameterType="com.cdz360.iot.model.sim.param.ListSimParam"
            resultType="com.cdz360.iot.model.sim.vo.SimVo">
        select
            s.*
        from
            t_sim s
        <where>
            <include refid="SimListQueryCondition"/>
        </where>
        order by
            s.id desc
        <choose>
            <when test="param.start != null and param.size != null">
                limit #{param.start}, #{param.size}
            </when>
            <when test="param.size != null">
                limit #{param.size}
            </when>
        </choose>
    </select>

    <select id="getSimListCount" parameterType="com.cdz360.iot.model.sim.param.ListSimParam"
            resultType="java.lang.Long">
        select
            count(s.id)
        from
            t_sim s
        <where>
            <include refid="SimListQueryCondition"/>
        </where>
    </select>

    <select id="getTinyList" parameterType="com.cdz360.iot.model.sim.param.ListSimParam"
            resultType="com.cdz360.iot.model.sim.vo.SimTinyVo">
        select
            s.id, s.iccid, s.msisdn, s.imsi, s.imei
        from
            t_sim s
        <where>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.iccidLike)">
                and s.iccid like concat("%", #{param.iccidLike}, "%")
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.msisdnLike)">
                and s.msisdn like concat("%", #{param.msisdnLike}, "%")
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(param.idList)">
                and s.id in
                <foreach collection="param.idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by
            s.id desc
        <choose>
            <when test="param.start != null and param.size != null">
                limit #{param.start}, #{param.size}
            </when>
            <when test="param.size != null">
                limit #{param.size}
            </when>
        </choose>
    </select>

    <select id="getVoBySimId" resultType="com.cdz360.iot.model.sim.vo.SimVo">
        select
            <include refid="SIM_VO_FIELDS" />
        from
            t_sim s
        <include refid="SIM_VO_JOIN" />
        where
            s.id = #{simId}
        limit 1
    </select>

    <select id="queryByCondition" parameterType="com.cdz360.iot.model.sim.param.SimQueryCondition"
            resultType="com.cdz360.iot.model.sim.vo.SimVo">
        select
            <include refid="SIM_VO_FIELDS" />
        from
            t_sim s
        <include refid="SIM_VO_JOIN" />
        where 1=1
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( idList )">
            and s.id in
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( iccidList )">
            and s.iccid in
            <foreach collection="iccidList" item="iccid" open="(" close=")" separator=",">
                #{iccid}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( msisdnList )">
            and s.msisdn in
            <foreach collection="msisdnList" item="msisdn" open="(" close=")" separator=",">
                #{msisdn}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
            <foreach item="sort" collection="sorts"
              open="order by" separator="," close=" ">
                ${sort.columnsString} ${sort.order}
            </foreach>
        </if>
    </select>

    <select id="getOneByCode" resultType="com.cdz360.iot.model.sim.po.SimPo">
        select
            *
        from
            t_sim
        where
            iccid = #{code} or msisdn = #{code}
        limit 1
    </select>

    <select id="getById" resultType="com.cdz360.iot.model.sim.po.SimPo">
        select
            *
        from
            t_sim
        where
            id = #{simId}
        limit 1
    </select>

    <select id="getByIccid" resultType="com.cdz360.iot.model.sim.po.SimPo">
        select
            *
        from
            t_sim
        where
            iccid = #{iccid}
        limit 1
    </select>

    <select id="getUnionAll" resultType="com.cdz360.iot.model.sim.po.SimPo">
        select
            any_value(iccid) as iccid ,
            any_value(msisdn) as msisdn
        from
            (
            select
                ts.id,
                ts.iccid,
                ts.msisdn
            from
                t_sim ts
            where
                iccid in
            <foreach collection="codeList" item="iccid" open="(" close=")" separator=",">
                #{iccid}
            </foreach>
        union all
            select
                ts.id,
                ts.iccid,
                ts.msisdn
            from
                t_sim ts
            where
                msisdn in
            <foreach collection="codeList" item="msisdn" open="(" close=")" separator=",">
                #{msisdn}
            </foreach>
        ) a
        group by
            id
        having
            count(*) > 1;
    </select>

    <select id="getUnion" resultType="java.lang.String">
        select
            ts.iccid as code
        from
            t_sim ts
        where
            iccid in
        <foreach collection="codeList" item="iccid" open="(" close=")" separator=",">
            #{iccid}
        </foreach>
        union
        select
            ts.msisdn as code
        from
            t_sim ts
        where
            msisdn in
        <foreach collection="codeList" item="msisdn" open="(" close=")" separator=",">
            #{msisdn}
        </foreach>
    </select>

    <select id="getUnionVo" resultType="com.cdz360.iot.model.sim.vo.SimUnionVo">
        select
            s.id as simId,
            s.iccid as code,
            1 as codeType,
            case
                when s.siteId is null or s.siteId = '' then e.siteId
                else s.siteId
            end as siteId,
            e.evseId as evseNo
        from
            t_sim s
        <include refid="SIM_VO_JOIN" />
        where
            s.iccid in
        <foreach collection="codeList" item="iccid" open="(" close=")" separator=",">
            #{iccid}
        </foreach>
        union
        select
            s.id as simId,
            s.msisdn as code,
            2 as codeType,
            case
                when s.siteId is null or s.siteId = '' then e.siteId
                else s.siteId
            end as siteId,
            e.evseId as evseNo
        from
            t_sim s
        <include refid="SIM_VO_JOIN" />
        where
            s.msisdn in
        <foreach collection="codeList" item="msisdn" open="(" close=")" separator=",">
            #{msisdn}
        </foreach>
    </select>

</mapper>
