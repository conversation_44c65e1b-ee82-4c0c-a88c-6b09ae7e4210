package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface CameraRecorderRwMapper {
	CameraRecorderPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertCameraRecorder(CameraRecorderPo cameraRecorderPo);

	int updateCameraRecorder(CameraRecorderPo cameraRecorderPo);

    int disableById(@Param("id") Long id);
}
