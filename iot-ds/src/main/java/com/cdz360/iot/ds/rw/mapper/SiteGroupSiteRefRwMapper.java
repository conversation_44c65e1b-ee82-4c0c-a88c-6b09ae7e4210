package com.cdz360.iot.ds.rw.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteGroupSiteRefRwMapper {

    int batchInsert(@Param("gid") String gid, @Param("siteIdList") List<String> siteIdList);

    int batchDelete(@Param("gid") String gid,
        @Param("exSiteIdList") List<String> exSiteIdList);

    int deleteByGid(@Param("gid") String gid);

    int deleteBySiteId(@Param("siteId") String siteId);
}
