package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.SiteTopologyRefRoMapper;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.topology.vo.SiteTopologyRefVo;
import com.cdz360.iot.model.type.TopologyType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SiteTopologyRefRoDs {

	@Autowired
	private SiteTopologyRefRoMapper siteTopologyRefRoMapper;

	public SiteTopologyRefPo getById(Long id) {
		return this.siteTopologyRefRoMapper.getById(id);
	}

	public SiteTopologyRefPo getBindRefByDownIdAndType(Long downId, TopologyType downType) {
		return this.siteTopologyRefRoMapper.getBindRefByDownIdAndType(downId, downType);
	}

	public List<SiteTopologyRefPo> getByUpId(Long id) {
		return this.siteTopologyRefRoMapper.getByUpId(id);
	}

	public List<SiteTopologyRefVo> getEvseMeterTopology(Long id) {
		return this.siteTopologyRefRoMapper.getEvseMeterTopology(id);
	}

	public List<SiteTopologyRefPo> getByUpIdList(TopologyType upType, List<Long> upIdList) {
		return this.siteTopologyRefRoMapper.getByUpIdList(upType, upIdList);
	}

	public List<SiteTopologyRefPo> getByDownIdList(TopologyType downType, List<Long> downIdList) {
		return this.siteTopologyRefRoMapper.getByDownIdList(downType, downIdList);
	}
}
