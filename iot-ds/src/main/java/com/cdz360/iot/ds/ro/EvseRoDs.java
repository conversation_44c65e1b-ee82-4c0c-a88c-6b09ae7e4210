package com.cdz360.iot.ds.ro;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.iot.ds.ro.mapper.EvseOfflineRoMapper;
import com.cdz360.iot.ds.ro.mapper.EvseRoMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.evse.vo.EvseStatusPowerBiVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;


@Service
public class EvseRoDs {
    @Autowired
    private EvseRoMapper evseRoMapper;
    @Autowired
    private EvseOfflineRoMapper evseOfflineRoMapper;


    public EvsePo getEvseById(Long id) {
        return evseRoMapper.getEvseById(id);
    }
    public EvsePo getEvse(String evseNo) {
        return evseRoMapper.getEvse(evseNo);
    }

    public List<String> getEvseModelOrFirm(String type) {
        return evseRoMapper.getEvseModelOrFirm(type);
    }

    public EvseInfoDto getEvseInfo(String evseNo) {
        return evseRoMapper.getEvseInfo(evseNo);
    }

    public ListResponse<EvseInfoDto> getEvseInfoList(ListEvseParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        Page<EvseInfoDto> pageInfo = PageHelper.offsetPage(param.getStart().intValue(), param.getSize(),
                Boolean.TRUE.equals(param.getTotal()));
        List<EvseInfoDto> list = this.evseRoMapper.getEvseInfoList(param);

        ListResponse<EvseInfoDto> res;
        if (Boolean.TRUE.equals(param.getTotal())) {
            res = new ListResponse<>(list, pageInfo.getTotal());
        } else {
            res = new ListResponse<>(list);
        }
        return res;
    }

    public List<EvsePo> getEvseList(ListEvseParam param) {
        return evseRoMapper.getEvseList(param);
    }

    public List<EvseTinyDto> getEvseTinyList(EvseTinyParam param) {
        return evseRoMapper.getEvseTinyList(param);
    }

    public List<EvseModelVo> getEvseModelVoList(ListEvseParam param) {
        return evseRoMapper.getEvseModelVoList(param);
    }

    public List<EvsePo> getEvseListForTopology(ListEvseParam param) {
        return evseRoMapper.getEvseListForTopology(param);
    }

    public List<EvsePo> selectByEvseIds(List<String> evseIds) {
        return evseRoMapper.selectByEvseIds(evseIds);
    }

    public List<EvsePo> selectBindInTransformerByEvseIds(List<String> evseIds) {
        return evseRoMapper.selectBindInTransformerByEvseIds(evseIds);
    }

    public List<EvsePlugRecordPo> getEvseRecordInfo(String siteId) {
        return evseRoMapper.getEvseRecordInfo(siteId);
    }

    public List<EvsePlugRecordPo> getUpgradeCleaningEvseInfo() {
        return evseRoMapper.getUpgradeCleaningEvseInfo();
    }


    /**
     * 根据状态统计桩功率
     * @param commIdChain
     * @return
     */
    public List<EvseStatusPowerBiVo> getEvseStatusPowerBi(@Nullable String provinceCode,
                                                          @Nullable String cityCode,
                                                          @Nullable String siteId,
                                                          @Nullable String commIdChain){
        return this.evseRoMapper.getEvseStatusPowerBi(provinceCode, cityCode, siteId, commIdChain);
    }

    public Long getTotalPower(SiteAndPlugBiParam param) {
        return this.evseRoMapper.getTotalPower(param);
    }

    public Integer getEvseCount(SiteAndPlugBiParam param) {
        return this.evseRoMapper.getEvseCount(param);
    }

    public Long count(String evseNo) {
        return evseRoMapper.count(evseNo);
    }

    public List<String> getNeedCheckCfgEvse(long topCommId, long start, int size) {
        return evseRoMapper.getNeedCheckCfgEvse(topCommId, start, size);
    }

    public List<String> findByModelId(long modelId, long start, int size) {
        return evseRoMapper.findByModelId(modelId, start, size);
    }

    public EvsePo getByIccid(String iccid) {
        return evseRoMapper.getByIccid(iccid);
    }

    public List<String> getFirmwareVerList(BaseListParam param) {
        return evseRoMapper.getFirmwareVerList(param);
    }

    public List<EvseInfoDto> getEvseSimVo(List<String> iccids) {
        return evseRoMapper.getEvseSimVo(iccids);
    }

}