package com.cdz360.iot.ds.ro;

import com.cdz360.iot.model.meter.dto.BiSiteMeterSummaryDto;
import com.cdz360.iot.model.meter.po.MeterRecordPo;
import com.cdz360.iot.ds.ro.mapper.MeterRecordRoMapper;
import com.cdz360.iot.model.param.MeterRecordBiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MeterRecordRoDs {

	@Autowired
	private MeterRecordRoMapper meterRecordRoMapper;

	public MeterRecordPo getById(Long id) {
		return this.meterRecordRoMapper.getById(id);
	}

	public List<MeterRecordPo> getReading(List<Long> ids, Date date) {
		return this.meterRecordRoMapper.getReading(ids, date);
	}
	public MeterRecordPo getReadingByTime(Long meterId, Date readingTime) {
		return this.meterRecordRoMapper.getReadingByTime(meterId, readingTime);
	}

	public List<BiSiteMeterSummaryDto> getBiSiteMeterRecord(MeterRecordBiParam param) {
		return this.meterRecordRoMapper.getBiSiteMeterRecord(param);
	}
}
