package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.ExpressRwMapper;
import com.cdz360.iot.model.parts.po.ExpressPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExpressRwDs {

    @Autowired
    private ExpressRwMapper expressRwMapper;

    public ExpressPo getById(Long id, boolean lock) {
        return this.expressRwMapper.getById(id, lock);
    }

    public boolean insertExpress(ExpressPo expressPo) {
        return this.expressRwMapper.insertExpress(expressPo) > 0;
    }

    public boolean updateExpress(ExpressPo expressPo) {
        return this.expressRwMapper.updateExpress(expressPo) > 0;
    }
}

