package com.cdz360.iot.ds.rw;


import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.rw.mapper.EssBatteryPackRwMapper;
import com.cdz360.iot.model.ess.po.EssBatteryPackPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class EssBatteryPackRwDs {



	@Autowired

	private EssBatteryPackRwMapper essBatteryPackRwMapper;



	public boolean upsetEssBatteryPack(EssBatteryPackPo essBatteryPackPo) {

		return this.essBatteryPackRwMapper.upsetEssBatteryPack(essBatteryPackPo) > 0;

	}

	public boolean batchUpsetEssBatteryPack(List<EssBatteryPackPo> poList) {
		if (CollectionUtils.isEmpty(poList)) return true;

		return this.essBatteryPackRwMapper.batchUpsetEssBatteryPack(poList) > 0;

	}


}

