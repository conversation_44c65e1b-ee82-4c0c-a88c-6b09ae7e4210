package com.cdz360.iot.ds.ro.ess.mapper;


import com.cdz360.iot.model.ess.dto.EssDto;
import com.cdz360.iot.model.ess.dto.EssTinyDto;
import com.cdz360.iot.model.ess.dto.UserDeviceCountDto;
import com.cdz360.iot.model.ess.param.CountUserEssParam;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.EquipPowerBiVo;
import com.cdz360.iot.model.ess.vo.EssMeterVo;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.SiteEssVo;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import com.cdz360.iot.model.meter.type.MeterEstimateType;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssRoMapper {


    EssPo getById(@Param("id") Long id);

    List<EssDto> getByGwno(ListCtrlParam param);

    List<EssDto> findBySiteId(@Param("siteId") String siteId);

    EssPo getByDno(@Param("dno") String dno);

    EssVo getEssVo(@Param("dno") String dno);



    //    List<EssTinyDto> getEssTinyList(ListEssParam param);
    List<EssVo> findEssList(ListEssParam param);

    List<SiteEssVo> findSiteEssAmount(ListEssParam param);

    Long getEssCount(ListEssParam param);

    Long getEssCountByCfgId(Long cfgId);

    EssPo getByDnoAndSn(@Param("dno") String dno, @Param("sn") String sn);

    List<EssStatusBi> getEssStatusBi(@Param("commIdChain") String commIdChain,
        @Param("siteId") String siteId);

    Long getAbnormalEssNum(@Param("commIdChain") String commIdChain,
        @Param("siteId") String siteId);

    EquipPowerBiVo getEquipPowerBiVo(@Param("essDno") String essDno);

    EssMeterVo getEssMeterVo(@Param("essDno") String essDno,
        @Param("estimateType") MeterEstimateType estimateType);

    List<EssTinyDto> getEssTinyList(ListEssParam param);

    List<UserEssVo> findUserEssList(FetchUserEssParam param);

    Long userEssCount(FetchUserEssParam param);

    List<UserDeviceCountDto> countUserEss(CountUserEssParam param);

    EssPo getUserEssBySerialNumber(@Param("serialNo") String serialNo);
}

