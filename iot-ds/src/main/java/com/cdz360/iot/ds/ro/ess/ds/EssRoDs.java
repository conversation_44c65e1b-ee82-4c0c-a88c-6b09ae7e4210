package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.ds.ro.ess.mapper.EssRoMapper;
import com.cdz360.iot.model.ess.dto.EssDto;
import com.cdz360.iot.model.ess.dto.EssTinyDto;
import com.cdz360.iot.model.ess.dto.UserDeviceCountDto;
import com.cdz360.iot.model.ess.param.CountUserEssParam;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.param.ListEssParam;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.EquipPowerBiVo;
import com.cdz360.iot.model.ess.vo.EssMeterVo;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.SiteEssVo;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import com.cdz360.iot.model.meter.type.MeterEstimateType;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssRoDs {

    @Autowired
    private EssRoMapper essRoMapper;

    public EssPo getById(Long id) {
        return this.essRoMapper.getById(id);
    }

    public List<EssDto> getByGwno(ListCtrlParam param) {
        return this.essRoMapper.getByGwno(param);
    }

    public List<EssDto> findBySiteId(String siteId) {
        return this.essRoMapper.findBySiteId(siteId);
    }

    public List<EssVo> findEssList(ListEssParam param) {
        return this.essRoMapper.findEssList(param);
    }

    public List<SiteEssVo> findSiteEssAmount(ListEssParam param) {
        return this.essRoMapper.findSiteEssAmount(param);
    }

    public List<EssVo> findUserEssList(ListEssParam param) {
        return this.essRoMapper.findEssList(param);
    }

    public Long getEssCount(ListEssParam param) {
        return this.essRoMapper.getEssCount(param);
    }

    public Long getEssCountByCfgId(Long cfgId) {
        return this.essRoMapper.getEssCountByCfgId(cfgId);
    }

    public EssPo getByDno(String dno) {
        return this.essRoMapper.getByDno(dno);
    }

    public EssVo getEssVo(String dno) {
        return this.essRoMapper.getEssVo(dno);
    }

    public EssPo getByDnoAndSn(String dno, String sn) {
        return this.essRoMapper.getByDnoAndSn(dno, sn);
    }

    public List<EssStatusBi> getEssStatusBi(String commIdChain, String siteId) {
        List<EssStatusBi> res = this.essRoMapper.getEssStatusBi(commIdChain, siteId);
        Long abnormalEssNum = this.essRoMapper.getAbnormalEssNum(commIdChain, siteId);
        res.add(new EssStatusBi().setStatus(3).setNum(abnormalEssNum));
        return res;
    }

    public EquipPowerBiVo getEquipPowerBiVo(String essDno) {
        return essRoMapper.getEquipPowerBiVo(essDno);
    }

    public EssMeterVo getEssMeterVo(String essDno) {
        return essRoMapper.getEssMeterVo(essDno, MeterEstimateType.PCS_AC_IN_OUT);
    }

    public List<EssTinyDto> getEssTinyList(ListEssParam param) {
        return this.essRoMapper.getEssTinyList(param);
    }

    public List<UserEssVo> userEssList(FetchUserEssParam param) {
        return this.essRoMapper.findUserEssList(param);
    }

    public Long userEssCount(FetchUserEssParam param) {
        return this.essRoMapper.userEssCount(param);
    }

    public List<UserDeviceCountDto> countUserEss(CountUserEssParam param) {
        return this.essRoMapper.countUserEss(param);
    }

    public EssPo getUserEssBySerialNumber(String serialNo) {
        return this.essRoMapper.getUserEssBySerialNumber(serialNo);
    }

}
