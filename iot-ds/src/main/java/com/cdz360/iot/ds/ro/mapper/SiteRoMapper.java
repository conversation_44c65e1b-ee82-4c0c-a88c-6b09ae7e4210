package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.cdz360.iot.model.site.po.SitePo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteRoMapper {

    List<SitePo> queryByCondition(@Param("siteIdList") List<String> siteIdList);

    SitePo getSite(@Param(value = "siteId") String siteId);

    List<SitePo> listSite(@Param(value = "start") long start, @Param(value = "size") int size);

    List<SitePo> listSiteUnify(@Param(value = "start") long start, @Param(value = "size") int size);

    long listSiteUnifyCount();

//    List<SiteDto> listSiteDto(ListSiteParam param);

    /**
     * 获取有空闲枪头的场站ID列表
     */
    List<String> getIdleSiteIdList(@Param(value = "topCommId") Long topCommId);

//    SiteDto getSiteByDzId(@Param(value = "dzId") String dzId);

    Integer getSiteCount(SiteAndPlugBiParam param);

    Date getExpireDate(@Param("siteId") String siteId);

}
