package com.cdz360.iot.ds;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.ds.ro.mapper.EvsePasscodeMapper;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import jakarta.validation.constraints.NotNull;
import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvsePasscodeRoDs {

    @Autowired
    private EvsePasscodeMapper evsePasscodeMapper;

    @Value("${iot.evse.passcode:}")
    private String passcodeV0;

    /**
     * 获取充电桩的密钥
     *
     * @param evseNo 桩编号
     * @param ver    密钥版本号. 传null时查询最新的
     * @return 返回充电桩密钥的数据结构
     */
    public EvsePasscodePo getEvsePasscode(@NotBlank(message = "参数错误, evseNo不能为空") String evseNo, @Nullable Long ver) {
        String passcode = null;
        if (null == ver || ver == 0) {
            passcode = passcodeV0.toUpperCase();
            return new EvsePasscodePo().setPasscode(passcode).setEvseNo(evseNo).setVer(ver);
        }

        EvsePasscodePo evsePasscodePo = evsePasscodeMapper.selectByEvseNo(evseNo, ver, false);
        if (null == evsePasscodePo) {
//            passcode = passcodeV0.toUpperCase();
//            return new EvsePasscodePo().setPasscode(passcode).setEvseNo(evseNo).setVer(ver);
            log.warn("获取长效密钥失败. evseNo = {}, ver = {}", evseNo, ver);
            throw new DcServiceException("密钥不存在");
        }

        passcode = evsePasscodePo.getPasscode().toUpperCase();
        return evsePasscodePo.setPasscode(passcode);
    }

    public EvsePasscodePo getEvsePasscode(@NotNull String evseNo) {
        return evsePasscodeMapper.selectLatestByEvseNo(evseNo);
    }

}
