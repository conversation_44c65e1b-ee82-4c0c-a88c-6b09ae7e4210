package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.CameraAccountRwMapper;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class CameraAccountRwDs {

	@Autowired
	private CameraAccountRwMapper cameraAccountRwMapper;

	public CameraAccountPo getById(Long id, boolean lock) {
		return this.cameraAccountRwMapper.getById(id, lock);
	}

	public boolean insertCameraAccount(CameraAccountPo cameraAccountPo) {
		return this.cameraAccountRwMapper.insertCameraAccount(cameraAccountPo) > 0;
	}

	public boolean updateCameraAccount(CameraAccountPo cameraAccountPo) {
		return this.cameraAccountRwMapper.updateCameraAccount(cameraAccountPo) > 0;
	}


}
