<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.UpgradeTaskQueryMapper">

    <resultMap id="RESULT_UPGRADE_TASK_VO" type="com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="siteId" property="siteId" jdbcType="VARCHAR"/>
        <result column="bundleId" property="bundleId" jdbcType="BIGINT"/>
        <result column="packageInfo" property="packageInfo"
          typeHandler="com.cdz360.iot.ds.EvsePackageInfoItemListTypeHandler"/>
        <result column="evseCount" property="evseCount" jdbcType="INTEGER"/>
        <result column="opId" property="opId" jdbcType="BIGINT"/>
        <result column="opName" property="opName" jdbcType="VARCHAR"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="bundleName" property="bundleName" jdbcType="VARCHAR"/>
        <result column="updateProcess" property="updateProcess" jdbcType="VARCHAR"/>
        <result column="bundleVersion" property="bundleVersion" jdbcType="BIGINT"/>
        <result column="packageVersion" property="packageVersion" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="RESULT_UPGRADE_TASK_INFO_VO" type="com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="bundleId" property="bundleId" jdbcType="BIGINT"/>
        <result column="packageInfo" property="packageInfo"
          typeHandler="com.cdz360.iot.ds.EvsePackageInfoItemListTypeHandler"/>
        <result column="upgradeTime" property="upgradeTime" jdbcType="TIMESTAMP"/>
        <result column="bundleName" property="bundleName" jdbcType="VARCHAR"/>
        <result column="updateProcess" property="updateProcess" jdbcType="VARCHAR"/>
        <result column="bundleVersion" property="bundleVersion" jdbcType="BIGINT"/>
        <result column="packageVersion" property="packageVersion" jdbcType="VARCHAR"/>
        <result column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR"/>
        <result column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR"/>
        <result column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="select" resultMap="RESULT_UPGRADE_TASK_VO">
        select

        tut.id As id,
        tut.siteId As siteId,
        tut.bundleId As bundleId,
        tut.evseCount As evseCount,
        tut.opId As opId,
        tut.opName As opName,
        tut.createTime As createTime,
        teb.fileName As bundleName,
        case when (select count(1) from t_upgrade_task_detail where taskId=tut.id)=0 then NULL else concat(round(( (select count(1) from t_upgrade_task_detail where taskId=tut.id and `status`='UPDATED') / (select count(1) from t_upgrade_task_detail where taskid=tut.id) * 100 ),2),'%') end AS updateProgress,
        teb.version AS bundleVersion

        from t_upgrade_task tut left join t_evse_bundle teb on tut.bundleId=teb.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
        order by tut.id desc
        <if test="start != null and end != null">
            limit #{start}, #{end}
        </if>
    </select>

    <select id="selectEssList" resultMap="RESULT_UPGRADE_TASK_VO">
        select
        tut.id As id,
        tut.siteId As siteId,
        tut.bundleId As bundleId,
        tut.packageInfo AS packageInfo,
        tut.evseCount As evseCount,
        tut.opId As opId,
        tut.opName As opName,
        tut.createTime As createTime,
        tep.packageName As bundleName,
        case when (select count(1) from t_upgrade_task_detail where taskId=tut.id)=0 then NULL else concat(round(( (select count(1) from t_upgrade_task_detail where taskId=tut.id and `status`='UPDATED') / (select count(1) from t_upgrade_task_detail where taskid=tut.id) * 100 ),2),'%') end AS updateProgress,
        tep.version AS packageVersion

        from t_upgrade_task tut left join t_evse_package tep on tut.bundleId=tep.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(tep.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(tep.packageName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
        order by tut.id desc
        <if test="start != null and end != null">
            limit #{start}, #{end}
        </if>
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from t_upgrade_task tut left join t_evse_bundle teb on tut.bundleId=teb.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
    </select>

    <select id="selectEssCount" resultType="java.lang.Long">
        select count(1) from t_upgrade_task tut left join t_evse_package tep on tut.bundleId=tep.id
        <where>
            <if test="siteId != null and siteId != ''">
                and tut.siteId=#{siteId}
            </if>
            <if test="taskId != null and taskId != 0">
                and tut.id=#{taskId}
            </if>
            <if test="bundleKeyword != null and bundleKeyword != ''">
                and (UPPER(CONCAT(tep.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(tep.packageName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
            </if>
        </where>
    </select>

    <select id="selectById" resultMap="RESULT_UPGRADE_TASK_VO">
        select * from t_upgrade_task
        <where>
            id=#{id}
        </where>
    </select>

    <select id="getUpgradeTaskInfo" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo">
        select

        tut.createTime AS upgradeTime,
        teb.version AS bundleVersion,
        teb.fileName AS bundleName,
        teb.id AS bundleId

        from t_upgrade_task tut left join t_evse_bundle teb
        on tut.bundleId=teb.id
        <where>
            tut.id=#{taskId}
        </where>
    </select>

    <select id="getEssUpgradeTaskInfo" resultMap="RESULT_UPGRADE_TASK_INFO_VO">
        select

        tut.createTime AS upgradeTime,
        tut.packageInfo AS packageInfo,
        tep.version AS packageVersion,
        tep.packageName AS bundleName,
        tep.id AS bundleId

        from t_upgrade_task tut left join t_evse_package tep
        on tut.bundleId=tep.id
        <where>
            tut.id=#{taskId}
        </where>
    </select>

    <select id="getUpgradeRecordVo" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo">
        select
            t.id,
            t.createTime,
            teb.`version` as bundleVersion,
            teb.fileName,
            td.pc01Ver,
            td.pc02Ver,
            td.pc03Ver,
            td.status,
            t.opName,
            t.siteId,
            site.name siteName
        from
            t_upgrade_task_detail td
        inner join t_upgrade_task t on
            td.taskId = t.id
        left join t_evse_bundle teb on
            t.bundleId = teb.id
        left join t_site site on
            site.dzId = t.siteId
        where
            td .evseId = #{evseNo}
        <if test="taskId != null and taskId != 0">
            and td.taskId = #{taskId}
        </if>
        <if test="bundleKeyword != null and bundleKeyword != ''">
            and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
        </if>
        order by
            t.id desc
        limit #{start}, #{size}
    </select>

    <select id="getUpgradeRecordVoCount" resultType="java.lang.Long">
        select
            count(t.id)
        from
            t_upgrade_task_detail td
        inner join t_upgrade_task t on
            td.taskId = t.id
        left join t_evse_bundle teb on
            t.bundleId = teb.id
        where
            td.evseId = #{evseNo}
        <if test="taskId != null and taskId != 0">
            and td.taskId = #{taskId}
        </if>
        <if test="bundleKeyword != null and bundleKeyword != ''">
            and (UPPER(CONCAT(teb.version)) like UPPER(CONCAT('%', #{bundleKeyword}, '%')) or UPPER(teb.fileName) like UPPER(CONCAT('%', #{bundleKeyword}, '%')))
        </if>
    </select>

</mapper>
