package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.ParkDeviceRoMapper;
import com.cdz360.iot.model.park.po.ParkDevicePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class ParkDeviceRoDs {



	@Autowired

	private ParkDeviceRoMapper parkDeviceRoMapper;



	public ParkDevicePo getById(Long id) {

		return this.parkDeviceRoMapper.getById(id);

	}

}

