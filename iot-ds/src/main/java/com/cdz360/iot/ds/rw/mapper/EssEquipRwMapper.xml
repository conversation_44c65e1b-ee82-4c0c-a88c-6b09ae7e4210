<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssEquipRwMapper">

  <resultMap id="RESULT_ESSEQUIP_PO" type="com.cdz360.iot.model.ess.po.EssEquipPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="equipId" jdbcType="BIGINT" property="equipId"/>
    <result column="equipTypeId" jdbcType="INTEGER" property="equipTypeId"/>
    <result column="equipType" jdbcType="INTEGER" property="equipType"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="netType" jdbcType="VARCHAR" property="netType"/>
    <result column="netCfg" jdbcType="VARCHAR" property="netCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="logCfg" jdbcType="VARCHAR" property="logCfg"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="equipNameCn" jdbcType="VARCHAR" property="equipNameCn"/>
    <result column="equipNameEn" jdbcType="VARCHAR" property="equipNameEn"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_ESSEQUIP_PO">
    select * from t_ess_equip where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertEssEquip" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssEquipPo">
    insert into t_ess_equip (`dno`,
    `essDno`,
    `equipId`,
    `equipTypeId`,
    `equipType`,
    `equipModel`,
    `vendor`,
    `swVer`,
    `netType`,
    `netCfg`,
    `logCfg`,
    `equipNameCn`,
    `equipNameEn`,
    `enable`)
    values (#{dno},
    #{essDno},
    #{equipId},
    #{equipTypeId},
    #{equipType},
    #{equipModel},
    #{vendor},
    #{swVer},
    #{netType},
    #{netCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    #{logCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    #{equipNameCn},
    #{equipNameEn},
    #{enable})
  </insert>

  <insert id="batchUpset">
    insert into t_ess_equip
    (`dno`,`essDno`,
    `name`,
    `status`,
    `equipId`,
    `equipTypeId`,
    `equipType`,
    `equipModel`,
    `vendor`,
    `swVer`,
    `netType`,
    `netCfg`,
    `equipNameCn`,
    `equipNameEn`,
    `enable`,
    `createTime`,
    `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.dno},#{po.essDno},
      #{po.name},
      #{po.status, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
      #{po.equipId},
      #{po.equipTypeId},
      #{po.equipType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
      #{po.equipModel},
      #{po.vendor},
      #{po.swVer},
      #{po.netType},
      #{po.netCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
      #{po.equipNameCn},
      #{po.equipNameEn},
      true,
      now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    `status` = values(status),
    `enable` = true,
    updateTime=now()
  </insert>

  <update id="updateEssEquip" parameterType="com.cdz360.iot.model.ess.po.EssEquipPo">
    update t_ess_equip set
    <if test="essDno != null">
      essDno = #{essDno},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="equipId != null">
      equipId = #{equipId},
    </if>
    <if test="equipTypeId != null">
      equipTypeId = #{equipTypeId},
    </if>
    <if test="equipType != null">
      equipType = #{equipType},
    </if>
    <if test="equipModel != null">
      equipModel = #{equipModel},
    </if>
    <if test="vendor != null">
      vendor = #{vendor},
    </if>
    <if test="swVer != null">
      swVer = #{swVer},
    </if>
    <if test="netType != null">
      netType = #{netType},
    </if>
    <if test="netCfg != null">
      netCfg = #{netCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="logCfg != null">
      logCfg = #{logCfg, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    </if>
    <if test="equipNameCn != null">
      equipNameCn = #{equipNameCn},
    </if>
    <if test="equipNameEn != null">
      equipNameEn = #{equipNameEn},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where dno = #{dno}
  </update>

  <update id="offEquip">
    update t_ess_equip set status = #{status.code}
    where essDno = #{essDno}
    and equipId not in
    <foreach collection="onlineEquipIdList" open="(" close=")"
      separator="," item="item">
      #{item}
    </foreach>
  </update>

  <update id="updateEssEquipStatus">
    update t_ess_equip
    <set>
      <if test="status != null">
        status = #{status.code},
      </if>
      <if test="alertStatus != null">
        alertStatus = #{alertStatus.code},
      </if>
    </set>
    where
    essDno = #{essDno}
    and equipId in
    <foreach collection="equipIdList" open="(" close=")"
      separator="," item="item">
      #{item}
    </foreach>
  </update>

  <update id="offlineByEssDno">
    update
    t_ess_equip
    set
    status = 2
    where
    essDno = #{essDno}
  </update>

  <update id="updateEssEquipNameplate">
    update t_ess_equip
    set nameplate = #{nameplate, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler}
    where essDno = #{essDno}
    and equipId = #{equipId}
  </update>

  <update id="enableByEssDno">
    update t_ess_equip set enable = #{enable} where essDno = #{essDno}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( excludeDnoList )">
      <foreach collection="excludeDnoList" index="index" item="item"
        open="and dno not in (" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </update>

</mapper>

