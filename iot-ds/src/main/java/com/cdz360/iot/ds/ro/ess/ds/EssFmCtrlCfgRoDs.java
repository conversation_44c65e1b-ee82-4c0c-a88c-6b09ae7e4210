package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssFmCtrlCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssFmCtrlCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssFmCtrlCfgRoDs {



	@Autowired

	private EssFmCtrlCfgRoMapper essFmCtrlCfgRoMapper;


	public EssFmCtrlCfgPo getById(Long id) {
		return this.essFmCtrlCfgRoMapper.getById(id);
	}


}

