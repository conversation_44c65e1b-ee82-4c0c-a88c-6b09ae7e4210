package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseModuleRoMapper;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.type.EvseDeviceType;
import com.cdz360.iot.model.evse.vo.DeviceVo;
import com.cdz360.iot.model.evse.vo.EvseDeviceVo;
import com.cdz360.iot.model.param.FindDeviceParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseModuleRoDs {

    @Autowired
    private EvseModuleRoMapper mapper;

    public List<EvseModulePo> getList(Long start, Integer size) {
        return mapper.getList(start, size);
    }

    /**
     * 获取有效的充电模块
     * @param evseNo
     * @return
     */
    public EvseModulePo findChargingModule(String evseNo) {
        return mapper.findChargingModule(evseNo,EvseDeviceType.GYZLCDMK.getDesc());
    }

    public List<DeviceVo> getByEvseNo(@NonNull String evseNo,
                                      @Nullable String deviceName,
                                      @Nullable Long start,
                                      @Nullable Long size) {
        return mapper.getByEvseNo(evseNo, deviceName, start, size);
    }

    public List<DeviceVo> getBySiteId(@Nullable String siteId,
                                      @Nullable Long start,
                                      @Nullable Long size) {
        return mapper.getBySiteId(siteId, start, size);
    }

    public Long getByEvseNoCount(@NonNull String evseNo, @Nullable String deviceName) {
        return mapper.getByEvseNoCount(evseNo, deviceName);
    }

    public List<EvseModulePo> queryByEvseNo(@NonNull String evseNo) {
        return mapper.queryByEvseNo(evseNo);
    }

    public EvseDeviceVo getByDeviceNo(@NonNull FindDeviceParam param) {
        return mapper.getByDeviceNo(param);
    }

}
