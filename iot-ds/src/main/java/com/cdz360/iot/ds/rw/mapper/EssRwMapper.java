package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;


@Mapper

public interface EssRwMapper {

    int upsetEss(EssPo essPo);

    int batchUpsetEss(@Param("poList") List<EssPo> poList);

    int offlineEss(@NonNull @Param("gwno") String gwno,
        @Nullable @Param("remainDnoList") List<String> remainDnoList);

    int updateEss(EssPo ess);

    int updateStatus(String dno, @NonNull EquipStatus status);

    int updateAlertStatus(String dno, @NonNull EquipAlertStatus status);

    int updateUserEss(EssPo ess);
}

