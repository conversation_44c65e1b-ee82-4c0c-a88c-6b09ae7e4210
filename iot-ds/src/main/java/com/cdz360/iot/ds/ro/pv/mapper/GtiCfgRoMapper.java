package com.cdz360.iot.ds.ro.pv.mapper;


import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.model.pv.param.ListGtiCfgParam;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import com.cdz360.iot.model.pv.vo.GtiCfgVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;


@Mapper

public interface GtiCfgRoMapper {



	GtiCfgPo getById(@Param("id") Long id);

	List<GtiCfgVo> findList(@Param("param") ListGtiCfgParam param,
							@Param("status") EquipStatus status);
	Long findListCount(ListGtiCfgParam param);

	Long countByName(@NonNull @Param("name") String name,
					 @NonNull @Param("siteId") String siteId,
					 @Nullable @Param("id") Long id);

	Long getConnectedGtiNum(@Param("cfgId") Long cfgId);
	Long getIssuingGtiNum(@Param("cfgId") Long cfgId);

}

