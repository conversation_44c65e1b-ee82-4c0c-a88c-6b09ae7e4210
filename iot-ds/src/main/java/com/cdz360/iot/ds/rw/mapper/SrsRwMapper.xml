<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.SrsRwMapper">

    <insert id="upsetSrs" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.srs.po.SrsPo">
        insert into t_srs (`dno`,
        `name`,
        `vendor`,
        `gwno`,
        `siteId`,
        `deviceModel`,
        `status`,
        `createTime`,
        `updateTime`)
        values (#{dno},
        #{name},
        #{vendor},
        #{gwno},
        #{siteId},
        #{deviceModel},
        #{status.code},
        now(),
        now())
        on DUPLICATE key UPDATE
        <if test="name != null">
            name = #{name},
        </if>
        <if test="status != null">
            status = #{status.code},
        </if>
        <if test="alertStatus != null">
            alertStatus = #{alertStatus.code},
        </if>
        updateTime = now()
    </insert>

    <insert id="batchUpsetSrs">
            insert into t_srs
            (`name`, `sid`, `dno`, `vendor`, `gwno`, `siteId`, `createTime`, `updateTime`)
            <foreach collection="poList" open="values" close=""
                     separator="," item="po">
                (#{po.name}, #{po.sid}, #{po.dno}, #{po.vendor}, #{po.gwno}, #{po.siteId}, now(), now())
            </foreach>
            ON DUPLICATE KEY UPDATE
            `name` = values(name),
            `sid` = values(sid),
            `vendor` = values(vendor),
            updateTime=now()
    </insert>

    <update id="offlineSrs">
        update t_srs set
        status = 99
        where gwno = #{gwno}
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( remainDnoList )">
            and dno not in
            <foreach collection="remainDnoList" open="(" close=")"
                     separator="," item="dno">
                #{dno}
            </foreach>
        </if>
    </update>

</mapper>

