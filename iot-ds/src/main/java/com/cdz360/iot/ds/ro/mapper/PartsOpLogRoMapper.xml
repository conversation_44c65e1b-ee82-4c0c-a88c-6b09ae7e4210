<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.PartsOpLogRoMapper">


  <resultMap id="RESULT_PARTSOPLOG_PO" type="com.cdz360.iot.model.parts.po.PartsOpLogPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="opType" property="opType"/>
    <result column="opUid" jdbcType="BIGINT" property="opUid"/>
    <result column="detail" property="detail" jdbcType="VARCHAR"
      typeHandler="com.cdz360.iot.ds.GenericTypeHandler"/>
    <result column="partsCode" jdbcType="VARCHAR" property="partsCode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>

  <resultMap id="RESULT_PARTSOPLOG_VO" extends="RESULT_PARTSOPLOG_PO"
    type="com.cdz360.iot.model.parts.vo.PartsOpLogVo">
  </resultMap>

  <select id="getById"
    resultMap="RESULT_PARTSOPLOG_PO">
    select * from t_parts_op_log where id = #{id}
  </select>
  <select id="getAllByPartsCode" resultMap="RESULT_PARTSOPLOG_VO">
    select * from t_parts_op_log where partsCode = #{partsCode}
  </select>


</mapper>

