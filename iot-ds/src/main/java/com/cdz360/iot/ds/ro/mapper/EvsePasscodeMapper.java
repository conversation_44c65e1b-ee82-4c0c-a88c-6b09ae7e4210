package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname EvseMapper
 * @Description TODO
 * @Date 9/19/2019 11:14 AM
 * @Created by Rafael
 */
@Mapper
public interface EvsePasscodeMapper {
    EvsePasscodePo selectByEvseNo(@Param(value = "evseNo") String evseNo, @Param(value = "ver") Long ver, @Param("lock") boolean lock);

    Long lastEvsePasscodeVer(@Param(value = "evseNo") String evseNo);

    EvsePasscodePo selectLatestByEvseNo(@Param(value = "evseNo") String evseNo);

}