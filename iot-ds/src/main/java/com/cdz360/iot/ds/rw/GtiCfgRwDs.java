package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.GtiCfgRwMapper;
import com.cdz360.iot.model.pv.po.GtiCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class GtiCfgRwDs {



	@Autowired

	private GtiCfgRwMapper gtiCfgRwMapper;



	public GtiCfgPo getById(Long id, boolean lock) {

		return this.gtiCfgRwMapper.getById(id, lock);

	}



	public boolean insertGtiCfg(GtiCfgPo gtiCfgPo) {

		return this.gtiCfgRwMapper.insertGtiCfg(gtiCfgPo) > 0;

	}



	public boolean updateGtiCfg(GtiCfgPo gtiCfgPo) {

		return this.gtiCfgRwMapper.updateGtiCfg(gtiCfgPo) > 0;

	}


}

