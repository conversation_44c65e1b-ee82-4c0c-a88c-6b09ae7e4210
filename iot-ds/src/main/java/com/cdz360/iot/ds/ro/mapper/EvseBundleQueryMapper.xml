<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseBundleQueryMapper">
  <resultMap id="BaseResultMap" type="com.cdz360.iot.model.evse.EvseBundle">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="VARCHAR" property="type"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="bundleSize" jdbcType="BIGINT" property="bundleSize"/>
    <result column="version" property="version"/>
    <result column="fileName" jdbcType="VARCHAR" property="fileName"/>
    <result column="releaseNote" jdbcType="LONGVARCHAR" property="releaseNote"/>
    <result column="opId" jdbcType="BIGINT" property="opId"/>
    <result column="opName" jdbcType="VARCHAR" property="opName"/>
    <result column="protocol" jdbcType="INTEGER" property="protocol"/>
    <result column="context" jdbcType="LONGVARCHAR" property="context"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, version, type, status, vendor,
    bundleSize, fileName, releaseNote, opId, opName, protocol, context,
    createTime, updateTime, enable
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_evse_bundle
    where id = #{id,jdbcType=BIGINT}
    and enable = true
  </select>

  <select id="selectByVersion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_evse_bundle
    where version = #{version}
    and `type` = #{type}
    <choose>
      <when test="enable!=null">
        and enable = #{enable,jdbcType=BOOLEAN}
      </when>
      <otherwise>
        and enable = true
      </otherwise>
    </choose>
  </select>

  <sql id="listEvseBundleSQL">
    <choose>
      <when test="enable!=null">
        enable = #{enable,jdbcType=BOOLEAN}
      </when>
      <otherwise>
        enable = true
      </otherwise>
    </choose>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(sk)">
      and (version = #{sk} or fileName like CONCAT('%', #{sk}, '%'))
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( typeList )">
      and `type` in
      <foreach item="item" collection="typeList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
      and `status` in
      <foreach item="item" collection="statusList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="null != protocol">
      and protocol = #{protocol}
    </if>
  </sql>

  <select id="listEvseBundle" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    ,
    (CASE `type` WHEN 'EVSE_SOFT' THEN context->>"$.pcList[0].type" ELSE null end) as pcType
    <!--        ,context->>"$.pcList[0].type" as pcType-->
    from t_evse_bundle
    <where>
      <include refid="listEvseBundleSQL"/>
    </where>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( sorts )">
      <foreach item="sort" collection="sorts"
        open="order by" separator="," close=" ">
        ${sort.columnsString} ${sort.order}
      </foreach>
    </if>
    <if test="start != null and size != null and pageFlag == true">
      limit #{start},#{size}
    </if>
  </select>

  <select id="getTotals" resultType="long">
    select
    count(*)
    from t_evse_bundle
    <where>
      <include refid="listEvseBundleSQL"/>
    </where>
  </select>
</mapper>