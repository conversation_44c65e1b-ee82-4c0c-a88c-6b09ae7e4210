<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SiteCtrlCfgRoMapper">
    <resultMap id="BaseResultMap" type="com.cdz360.iot.model.site.po.SiteCtrlCfgPo" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="pwrCtrlLmt" property="pwrCtrlLmt" jdbcType="VARCHAR" typeHandler="com.cdz360.iot.ds.MybatisJsonArrayTypeHandler"/>
    </resultMap>

<!--    <sql id="Base_Column_List" >-->
<!--        id,-->
<!--        ctrlNum,-->
<!--        pwrCtrl,-->
<!--        infoUp,-->
<!--        pwrLoadAlm,-->
<!--        pwrTemp<PERSON>lm,-->
<!--        chgFireAlm,-->
<!--        chgDoorAlm,-->
<!--        pwrCap,-->
<!--        pwrCtrlLmt,-->
<!--        tempSample,-->
<!--        pwrSample,-->
<!--        infoUpLoop,-->
<!--        pwrLoadLmt,-->
<!--        pwrTempLmt,-->
<!--        loadRatio,-->
<!--        pwrTemp,-->
<!--        createTime,-->
<!--        updateTime-->
<!--    </sql>-->

    <select id="selectByNum" resultType="com.cdz360.iot.model.site.po.SiteCtrlCfgPo" resultMap="BaseResultMap">
        select
        *
        from t_site_ctrl_cfg
        where ctrlNum = #{ctrlNum}
    </select>
</mapper>