package com.cdz360.iot.ds.ro.pv.ds;

import com.cdz360.iot.ds.ro.pv.mapper.GtiGridDispatchCfgRoMapper;
import com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GtiGridDispatchCfgRoDs {

    @Autowired
    private GtiGridDispatchCfgRoMapper mapper;

    public GtiGridDispatchCfgPo getById(Long id) {
        return mapper.getById(id);
    }

}

