package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseModuleDetailRoMapper {

    Long getValidModuleNum(@Param("moduleId") Long moduleId);

    Long countByCondition(@Param("moduleId") Long moduleId);

    List<EvseModuleDetailPo> findById(@Param("moduleId") Long moduleId);

}
