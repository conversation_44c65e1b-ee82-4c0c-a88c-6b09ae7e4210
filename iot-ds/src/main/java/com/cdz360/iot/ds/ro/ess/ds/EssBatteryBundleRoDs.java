package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.ess.mapper.EssBatteryBundleRoMapper;
import com.cdz360.iot.model.ess.param.BmsRelevantParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.vo.BmsRelevantVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssBatteryBundleRoDs {

    @Autowired
    private EssBatteryBundleRoMapper mapper;

    public EssBatteryBundlePo getByDno(String dno) {
        return mapper.getByDno(dno);
    }

    public EssBatteryBundlePo getByEssDnoEquipIdStackEquipId(
        String essDno, Long equipId, Long stackEquipId) {
        return mapper.getByEssDnoEquipIdStackEquipId(
            essDno, equipId, stackEquipId);
    }

    public List<EssBatteryBundlePo> getBatteryBundleList(ListEssEquipParam param) {
        if (StringUtils.isEmpty(param.getEssDno())
            && CollectionUtils.isEmpty(param.getBmsDnos())
        ) {    // 限制返回的数据数量,避免参数都为空的时候返回太多的数据
            param.setStartIfNull(0L)
                .setSizeIfNull(100, 100);
        }
        return mapper.getBatteryBundleList(param);
    }

    public BmsRelevantVo bmsRelevant(BmsRelevantParam param) {
        return mapper.bmsRelevant(param);
    }
}

