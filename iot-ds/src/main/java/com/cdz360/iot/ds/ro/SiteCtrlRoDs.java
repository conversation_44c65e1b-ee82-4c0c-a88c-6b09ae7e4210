package com.cdz360.iot.ds.ro;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ds.ro.mapper.SiteCtrlRoMapper;
import com.cdz360.iot.model.site.dto.SiteCtrlDto;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname SiteCtrlRoDs
 * @Description
 * @Date 4/22/2020 6:49 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlRoDs {
    @Autowired
    private SiteCtrlRoMapper siteCtrlRoMapper;

    public SiteCtrlPo selectByNum(String ctrlNum) {
        return siteCtrlRoMapper.selectByNum(ctrlNum);
    }

    public ListResponse<SiteCtrlDto> list(String keyword, String siteId, long start, long size) {
        List<SiteCtrlDto> res = siteCtrlRoMapper.list(keyword, siteId, start, size);
        long total = siteCtrlRoMapper.listCount(keyword, siteId);
        return RestUtils.buildListResponse(res, total);
    }
}