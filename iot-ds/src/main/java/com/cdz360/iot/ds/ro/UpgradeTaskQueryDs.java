package com.cdz360.iot.ds.ro;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ds.ro.mapper.UpgradeTaskQueryMapper;
import com.cdz360.iot.model.evse.upgrade.UpgradeRecordVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskInfoVo;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname UpgradeTaskQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:13 PM
 * @Created by Rafael
 */
@Service
public class UpgradeTaskQueryDs {
    @Autowired
    private UpgradeTaskQueryMapper upgradeTaskQueryMapper;

    public List<UpgradeTaskVo> select(String siteId, Long taskId, String packageKeyword, Integer start, Integer end) {
        return upgradeTaskQueryMapper.select(siteId, taskId, packageKeyword, start, end);
    }

    public List<UpgradeTaskVo> selectEssList(String siteId, Long taskId, String packageKeyword,
        Integer start, Integer end) {
        return upgradeTaskQueryMapper.selectEssList(siteId, taskId, packageKeyword, start, end);
    }

    public Long selectCount(String siteId, Long taskId, String bundleKeyword) {
        return upgradeTaskQueryMapper.selectCount(siteId, taskId, bundleKeyword);
    }

    public Long selectEssCount(String siteId, Long taskId, String bundleKeyword) {
        return upgradeTaskQueryMapper.selectEssCount(siteId, taskId, bundleKeyword);
    }

    public UpgradeTaskVo selectById(Long id) {
        return upgradeTaskQueryMapper.selectById(id);
    }

    public UpgradeTaskInfoVo getUpgradeTaskInfo(Long taskId) {
        return upgradeTaskQueryMapper.getUpgradeTaskInfo(taskId);
    }

    public UpgradeTaskInfoVo getEssUpgradeTaskInfo(Long taskId) {
        return upgradeTaskQueryMapper.getEssUpgradeTaskInfo(taskId);
    }

    public ListResponse<UpgradeRecordVo> getUpgradeRecordVo(String evseNo,
                                                            Long taskId,
                                                            String bundleKeyword,
                                                            Integer start,
                                                            Integer size) {
        List<UpgradeRecordVo> res = upgradeTaskQueryMapper.getUpgradeRecordVo(evseNo, taskId, bundleKeyword, start, size);
        Long total = upgradeTaskQueryMapper.getUpgradeRecordVoCount(evseNo, taskId, bundleKeyword);
        return RestUtils.buildListResponse(res, total);
    }
}