package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



@Mapper

public interface ParkingLockRwMapper {

	ParkingLockPo getById(@Param("id") Long id, @Param("lock") boolean lock);
	ParkingLockPo getByUniqueKey(@Param("partner") ParkingLockPartner partner,
								 @Param("serialNumber") String serialNumber,
								 @Param("lock") boolean lock);



	int upsetParkingLock(ParkingLockPo parkingLockPo);






}

