package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.ess.po.EssCfgPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EssCfgRwMapper {

    EssCfgPo getByCfgId(@Param("cfgId") Long cfgId, @Param("lock") boolean lock);

    int insertEssCfg(EssCfgPo essCfgPo);

    int insertBeforeSelect(@Param("oldId") Long oldId, @Param("newId") Long newId);

//    int updateEssCfg(EssCfgPo essCfgPo);

}

