<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.MeterRecordRoMapper">

	<resultMap id="RESULT_METERRECORD_PO" type="com.cdz360.iot.model.meter.po.MeterRecordPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="readingTime" jdbcType="TIMESTAMP" property="readingTime" />
		<result column="combineTotal" jdbcType="DECIMAL" property="combineTotal" />
		<result column="combineA" jdbcType="DECIMAL" property="combineA" />
		<result column="combineB" jdbcType="DECIMAL" property="combineB" />
		<result column="combineC" jdbcType="DECIMAL" property="combineC" />
		<result column="combineD" jdbcType="DECIMAL" property="combineD" />
		<result column="positiveTotal" jdbcType="DECIMAL" property="positiveTotal" />
		<result column="positiveA" jdbcType="DECIMAL" property="positiveA" />
		<result column="positiveB" jdbcType="DECIMAL" property="positiveB" />
		<result column="positiveC" jdbcType="DECIMAL" property="positiveC" />
		<result column="positiveD" jdbcType="DECIMAL" property="positiveD" />
		<result column="negativeTotal" jdbcType="DECIMAL" property="negativeTotal" />
		<result column="negativeA" jdbcType="DECIMAL" property="negativeA" />
		<result column="negativeB" jdbcType="DECIMAL" property="negativeB" />
		<result column="negativeC" jdbcType="DECIMAL" property="negativeC" />
		<result column="negativeD" jdbcType="DECIMAL" property="negativeD" />
		<result column="positiveIdleTotal" jdbcType="DECIMAL" property="positiveIdleTotal" />
		<result column="positiveIdleA" jdbcType="DECIMAL" property="positiveIdleA" />
		<result column="positiveIdleB" jdbcType="DECIMAL" property="positiveIdleB" />
		<result column="positiveIdleC" jdbcType="DECIMAL" property="positiveIdleC" />
		<result column="positiveIdleD" jdbcType="DECIMAL" property="positiveIdleD" />
		<result column="negativeIdleTotal" jdbcType="DECIMAL" property="negativeIdleTotal" />
		<result column="negativeIdleA" jdbcType="DECIMAL" property="negativeIdleA" />
		<result column="negativeIdleB" jdbcType="DECIMAL" property="negativeIdleB" />
		<result column="negativeIdleC" jdbcType="DECIMAL" property="negativeIdleC" />
		<result column="negativeIdleD" jdbcType="DECIMAL" property="negativeIdleD" />
		<result column="positiveApparentTotal" jdbcType="DECIMAL" property="positiveApparentTotal" />
		<result column="positiveApparentA" jdbcType="DECIMAL" property="positiveApparentA" />
		<result column="positiveApparentB" jdbcType="DECIMAL" property="positiveApparentB" />
		<result column="positiveApparentC" jdbcType="DECIMAL" property="positiveApparentC" />
		<result column="positiveApparentD" jdbcType="DECIMAL" property="positiveApparentD" />
		<result column="negativeApparentTotal" jdbcType="DECIMAL" property="negativeApparentTotal" />
		<result column="negativeApparentA" jdbcType="DECIMAL" property="negativeApparentA" />
		<result column="negativeApparentB" jdbcType="DECIMAL" property="negativeApparentB" />
		<result column="negativeApparentC" jdbcType="DECIMAL" property="negativeApparentC" />
		<result column="negativeApparentD" jdbcType="DECIMAL" property="negativeApparentD" />
		<result column="currentRate" jdbcType="DECIMAL" property="currentRate" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_METERRECORD_PO">	
		select * from t_meter_record where id = #{id}
	</select>

	<select id="getReading"
			resultMap="RESULT_METERRECORD_PO">
		select * from t_meter_record where id in
		(select max(id) from t_meter_record
			where
			readingTime <![CDATA[ <= ]]> #{date}
			and readingTime <![CDATA[ > ]]> DATE_ADD(#{date}, INTERVAL -1 day)
			and meterId in
		<foreach item="item" index="index" collection="ids"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
		group by meterId)
	</select>

    <select id="getReadingByTime" resultMap="RESULT_METERRECORD_PO">
        select * from t_meter_record
        <where>
            meterId = #{meterId}
            and readingTime = #{readingTime}
        </where>
    </select>

	<resultMap id="RESULT_SUMMARY" type="com.cdz360.iot.model.meter.dto.BiSiteMeterSummaryDto">
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="meterName" jdbcType="VARCHAR" property="meterName" />
		<result column="paramType" jdbcType="JAVA_OBJECT" property="paramType" />
		<collection property="biSummaryList"
					ofType="com.cdz360.iot.model.meter.po.BiMeterSumPo"
					javaType="list">
			<result column="amount" jdbcType="DECIMAL" property="amount" />
			<result column="day" jdbcType="TIMESTAMP" property="day" />
			<result column="month" jdbcType="TIMESTAMP" property="month" />
		</collection>
	</resultMap>


	<select id="getBiSiteMeterRecord" resultMap="RESULT_SUMMARY">

		select
		m.id as meterId,
		m.name as meterName,
		p.param as paramType,

		ifnull((
		CASE
		WHEN p.param='combineTotal' THEN
		max(r.combineTotal)-min(r.combineTotal)
		WHEN p.param='positiveTotal' THEN
		max(r.positiveTotal)-min(r.positiveTotal)
		WHEN p.param='negativeTotal' THEN
		max(r.negativeTotal)-min(r.negativeTotal)
		WHEN p.param='positiveIdleTotal' THEN
		max(r.positiveIdleTotal)-min(r.positiveIdleTotal)
		WHEN p.param='negativeIdleTotal' THEN
		max(r.negativeIdleTotal)-min(r.negativeIdleTotal)
		WHEN p.param='positiveApparentTotal' THEN
		max(r.positiveApparentTotal)-min(r.positiveApparentTotal)
		WHEN p.param='negativeApparentTotal' THEN
		max(r.negativeApparentTotal)-min(r.negativeApparentTotal)
		ELSE 0
		END
		), 0) amount,

<!--		max(r.combineTotal)-min(r.combineTotal),-->
<!--		max(r.positiveTotal)-min(r.positiveTotal),-->
<!--		max(r.negativeTotal)-min(r.negativeTotal),-->
<!--		DATE_FORMAT(readingTime, '%Y-%m-%d'),-->
<!--		DATE_FORMAT(readingTime, '%Y-%m-%d') as `day`-->
		<choose>
			<when test="sampleType != null and sampleType.name() == 'MONTH'">
				DATE_FORMAT(r.readingTime, '%Y-%m-01') as `month`
			</when>
			<otherwise>
				DATE_FORMAT(r.readingTime, '%Y-%m-%d') as `day`
			</otherwise>
		</choose>


		from t_meter m
		left join

		t_meter_param p

		on p.param in
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isEmpty( paramTypeList )">
		(
		'combineTotal',
		'positiveTotal',
		'negativeTotal',
		'positiveIdleTotal',
		'negativeIdleTotal',
		'positiveApparentTotal',
		'negativeApparentTotal'
		)
		</if>
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( paramTypeList )">
			<foreach item="paramType" collection="paramTypeList"
					 open="(" close=")" separator=",">
				#{paramType}
			</foreach>
		</if>

		LEFT JOIN
		(
			select * from t_meter_record mr
			where
			1 = 1
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( meterIdList )">
			and mr.meterId in
			<foreach item="meterId" collection="meterIdList"
					 open="(" close=")" separator=",">
				#{meterId}
			</foreach>
		</if>
			and mr.readingTime <![CDATA[ >= ]]> DATE_FORMAT(#{fromTime}, '%Y-%m-%d')
			and mr.readingTime <![CDATA[ < ]]> DATE_FORMAT(#{toTime}, '%Y-%m-%d')
		) r
		on r.meterId = m.id

		where
		1=1
		<if test="siteId != null">
			and m.siteId = #{siteId}
		</if>
<!--		and DATE_FORMAT(readingTime, '%Y-%m-%d')='2021-11-08'-->
		group by m.id, p.param,
<!--		DATE_FORMAT(readingTime, '%Y-%m-%d')-->
		<choose>
			<when test="sampleType != null and sampleType.name() == 'MONTH'">
				DATE_FORMAT(r.readingTime, '%Y-%m-01')
			</when>
			<otherwise>
				DATE_FORMAT(r.readingTime, '%Y-%m-%d')
			</otherwise>
		</choose>

		having
			1=1
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( meterIdList )">
			and m.id in
			<foreach item="meterId" collection="meterIdList"
					 open="(" close=")" separator=",">
				#{meterId}
			</foreach>
		</if>
<!--		<choose>-->
<!--			<when test="sampleType != null and sampleType.name() == 'MONTH'">-->
<!--				<if test="fromTime != null">-->
<!--					and (`month` <![CDATA[ >= ]]> DATE_FORMAT(#{fromTime}, '%Y-%m-01') or `month` is null)-->
<!--				</if>-->
<!--				<if test="toTime != null">-->
<!--					and (`month` <![CDATA[ < ]]> DATE_FORMAT(#{toTime}, '%Y-%m-01') or `month` is null)-->
<!--				</if>-->
<!--			</when>-->
<!--			<otherwise>-->
<!--				<if test="fromTime != null">-->
<!--					and (`day` <![CDATA[ >= ]]> DATE_FORMAT(#{fromTime}, '%Y-%m-%d') or `day` is null)-->
<!--				</if>-->
<!--				<if test="toTime != null">-->
<!--					and (`day` <![CDATA[ < ]]> DATE_FORMAT(#{toTime}, '%Y-%m-%d') or `day` is null)-->
<!--				</if>-->
<!--			</otherwise>-->
<!--		</choose>-->
	</select>

</mapper>
