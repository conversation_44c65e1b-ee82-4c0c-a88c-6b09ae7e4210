<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseModelRwMapper">


	<insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.evse.po.EvseModelPo">
		INSERT INTO t_evse_model
		(model, brand, series, supply, power, plugNum,
		flags,splitFlag,
		createTime)
		VALUES
		(#{model}, #{brand}, #{series}, #{supply}, #{power}, #{plugNum},
		#{flags, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler}, #{splitFlag},
		now())
		on DUPLICATE key UPDATE
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( series )">
			series = #{series},
		</if>
		<if test="supply != null">
			supply = #{supply},
		</if>
		<if test="power != null">
			power = #{power},
		</if>
		<if test="plugNum != null">
			plugNum = #{plugNum},
		</if>
		<if test="flags != null">
			flags = #{flags, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
		</if>
		<if test="splitFlag != null">
			splitFlag = #{splitFlag},
		</if>
		<if test="status != null">
			status = #{status},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		createTime = now(),
		updateTime = now()
	</insert>

	<insert id="updateById" parameterType="com.cdz360.iot.model.evse.po.EvseModelPo">
		UPDATE
		t_evse_model
		<set>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
				model = #{model},
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
				brand = #{brand},
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( series )">
				series = #{series},
			</if>
			<if test="supply != null">
				supply = #{supply},
			</if>
			<if test="power != null">
				power = #{power},
			</if>
			<if test="plugNum != null">
				plugNum = #{plugNum},
			</if>
			<if test="flags != null">
				flags = #{flags, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
			</if>
			<if test="splitFlag != null">
				splitFlag = #{splitFlag},
			</if>
			<if test="status != null">
				status = #{status},
			</if>
			<if test="enable != null">
				enable = #{enable},
			</if>
			updateTime = now()
		</set>
		where
		id = #{id}
	</insert>

	<update id="changeStatus">
		update
		t_evse_model
		set
		`status` = #{status},
		updateTime = now()
		where
		id = #{id}
	</update>

	<update id="remove">
		update
		t_evse_model
		set
		`enable` = 0,
		updateTime = now()
		where
		id = #{id}
	</update>

</mapper>
