package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseMeterRoMapper {

	DeviceMeterPo getById(@Param("id") Long id);

	List<DeviceMeterPo> getByEvseIdInList(List<String> list);

	List<DeviceMeterPo> getEvseMeterList(@Param("meterId") Long meterId);
}
