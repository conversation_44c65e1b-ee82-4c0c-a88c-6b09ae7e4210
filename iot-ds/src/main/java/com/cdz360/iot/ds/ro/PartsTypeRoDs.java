package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.PartsTypeRoMapper;
import com.cdz360.iot.model.parts.param.ListPartsTypeParam;
import com.cdz360.iot.model.parts.param.PartsCheckParam;
import com.cdz360.iot.model.parts.po.PartsTypePo;
import com.cdz360.iot.model.parts.vo.PartsTypeVo;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsTypeRoDs {

    @Autowired
    private PartsTypeRoMapper partsTypeRoMapper;

    public PartsTypePo getById(Long id) {
        return this.partsTypeRoMapper.getById(id);
    }

    public PartsTypePo getOneByFields(
        @NotNull String name, @NotNull String code, @NotNull String fullModel) {
        return this.partsTypeRoMapper.getOneByFields(name, code, fullModel);
    }

    public List<PartsTypeVo> findPartsType(ListPartsTypeParam param) {
        return this.partsTypeRoMapper.findPartsType(param);
    }

    public Long countPartsType(ListPartsTypeParam param) {
        return this.partsTypeRoMapper.countPartsType(param);
    }

    public List<PartsTypePo> getByFields(List<PartsCheckParam> list) {
        return this.partsTypeRoMapper.getByFields(list);
    }

}

