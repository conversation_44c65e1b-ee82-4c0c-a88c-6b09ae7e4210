<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.ParkDeviceRwMapper">



	<resultMap id="RESULT_PARKDEVICE_PO" type="com.cdz360.iot.model.park.po.ParkDevicePo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="parkId" jdbcType="INTEGER" property="parkId" />

		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />

		<result column="ip" property="ip" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler" />

		<result column="eventMsg" jdbcType="VARCHAR" property="eventMsg" />

		<result column="eventTime" jdbcType="INTEGER" property="eventTime" />

		<result column="status" jdbcType="INTEGER" property="status" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_PARKDEVICE_PO">	
		select * from t_park_device where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="upsetParkDevice" useGeneratedKeys="true" keyProperty="id"

			keyColumn="id" parameterType="com.cdz360.iot.model.park.po.ParkDevicePo">

		insert into t_park_device (`parkId`,

			`deviceId`,

			`ip`,

			`eventMsg`,

			`eventTime`,

			`status`,

			`createTime`,

			`updateTime`)

		values (#{parkId},

			#{deviceId},

			#{ip, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},

			#{eventMsg},

			#{eventTime},

			#{status},

			now(),

			now())

		ON DUPLICATE key update

		<if test="parkId != null">

			parkId = #{parkId},

		</if>

		<if test="deviceId != null">

			deviceId = #{deviceId},

		</if>

		<if test="ip != null">

			ip = #{ip, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},

		</if>

		<if test="eventMsg != null">

			eventMsg = #{eventMsg},

		</if>

		<if test="eventTime != null">

			eventTime = #{eventTime},

		</if>

		<if test="status != null">

			status = #{status},

		</if>

		updateTime = now()

	</insert>

</mapper>

