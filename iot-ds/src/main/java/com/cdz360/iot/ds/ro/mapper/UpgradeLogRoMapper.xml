<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.UpgradeLogRoMapper">


  <resultMap id="RESULT_UPGRADELOG_PO" type="com.cdz360.iot.model.gw.po.UpgradeLogPo">

    <id column="id" jdbcType="BIGINT" property="id"/>

    <result column="deviceNo" jdbcType="VARCHAR" property="deviceNo"/>

    <result column="bundleId" jdbcType="BIGINT" property="bundleId"/>

    <result column="bundleType" property="bundleType"/>

    <result column="upgradeStatus" property="upgradeStatus"/>

    <result column="downTime" jdbcType="TIMESTAMP" property="downTime"/>

    <result column="receiveTime" jdbcType="TIMESTAMP" property="receiveTime"/>

    <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>

    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

  </resultMap>

  <resultMap id="RESULT_UPGRADELOG_VO" type="com.cdz360.iot.model.gw.vo.UpgradeLogVo">
    <result column="deviceNo" jdbcType="VARCHAR" property="deviceNo"/>
    <result column="bundleId" jdbcType="BIGINT" property="bundleId"/>
    <result column="bundleType" property="bundleType"/>
    <result column="upgradeStatus" property="upgradeStatus"/>
    <result column="downTime" jdbcType="TIMESTAMP" property="downTime"/>
    <result column="receiveTime" jdbcType="TIMESTAMP" property="receiveTime"/>
    <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

    <!--升级包信息-->
    <result column="bundleVer" jdbcType="BIGINT" property="bundleVer"/>
    <result column="bundleName" jdbcType="BIGINT" property="bundleName"/>
  </resultMap>

  <sql id="where_sql">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gwno )">
      and log.deviceNo = #{gwno}
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( gwnoList )">
      and log.deviceNo in
      <foreach collection="gwnoList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( statusList )">
      and log.upgradeStatus in
      <foreach collection="statusList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </sql>


  <select id="getById"

    resultMap="RESULT_UPGRADELOG_PO">
    select * from t_upgrade_log where id = #{id}

  </select>
  <select id="upgradeLogList"
    parameterType="com.cdz360.iot.model.gw.param.ListUpgradeLogParam"
    resultMap="RESULT_UPGRADELOG_VO">
    select log.*, bundle.version bundleVer, bundle.fileName bundleName
    from t_upgrade_log log
    left join t_evse_bundle bundle on bundle.id = log.bundleId
    where 1=1
    <include refid="where_sql"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by log.id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>
  <select id="count"
    parameterType="com.cdz360.iot.model.gw.param.ListUpgradeLogParam"
    resultType="java.lang.Long">
    select count(distinct id) from t_upgrade_log log
    where 1=1
    <include refid="where_sql"/>
  </select>


</mapper>

