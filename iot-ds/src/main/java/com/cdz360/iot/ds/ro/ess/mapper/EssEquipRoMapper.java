package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.base.model.es.vo.EssDeviceBiVo;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.ess.dto.EssEquipTinyDto;
import com.cdz360.iot.model.ess.param.ListEssBatteryClusterParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryClusterVo;
import com.cdz360.iot.model.ess.vo.EssEquipVo;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface EssEquipRoMapper {

    EssEquipPo getByDno(@Param("dno") String dno);

    EssEquipPo getById(@Param("id") Long id);

    List<EssEquipPo> findEquipList(ListCtrlParam param);

    List<EssEquipPo> findEquipListBySiteId(@NonNull @Param("siteId") String siteId,
        @Nullable @Param("equipType") EssEquipType equipType);

    EssEquipPo getByEssDnoAndEquipId(@Param("essDno") String essDno,
        @Param("equipId") Long equipId);

    List<EssEquipVo> getEssEquipListByDno(@Param("dnoList") List<String> dnoList);

    List<EssEquipVo> findEssEquipList(ListEssEquipParam param);

    /**
     * 搜索储能下属设备（缩略）信息
     */
    List<EssEquipTinyDto> getEssEquipTinyList(ListEssEquipParam param);

    Long getEssEquipCount(ListEssEquipParam param);

    EssEquipPo getEssStack(@Param("essDno") String essDno);

    List<EssEquipBatteryClusterVo> findBatteryCluster(ListEssBatteryClusterParam param);

    Long countBatteryCluster(ListEssBatteryClusterParam param);

    List<EssEquipPo> getEquipList(ListEssEquipParam param);

    List<EssDeviceBiVo> equipCountByEssDno(@Param("essDnoList") List<String> dnoList);

    List<EssStatusBi> getEquipStatusBi(@Param("siteId") String siteId);

    Long getAbnormalEquipNum(@Param("siteId") String siteId);

    List<UpdateCtrlDeviceDto> getEquipListByEssDnoList(@Param("essDnoList") List<String> essDnoList);

    int getMaxEquipIdByDno (@Param("essDno") String essDno) ;
}

