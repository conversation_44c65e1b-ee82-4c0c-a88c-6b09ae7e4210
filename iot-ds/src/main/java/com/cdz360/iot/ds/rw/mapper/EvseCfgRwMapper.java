package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.param.ListEvseCfgParam;
import com.cdz360.iot.model.evse.po.EvseCfgPo;
import com.cdz360.iot.model.evse.vo.EvseCfgVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseCfgRwMapper {

    void insertEvseCfg(EvseCfgPo evseCfg);

    void disableEvseCfg(String evseNo);

    void disableEvseCfgList(@Param("evseNoList") List<String> evseNoList);
    EvseCfgPo getEvseCfg(@Param("evseNo") String evseNo, @Param("lock") boolean lock);

    List<EvseCfgVo> getEvseCfgVoList(ListEvseCfgParam param);

    Long getEvseCfgVoCount(ListEvseCfgParam param);

//    /**
//     * 默认for update
//     *
//     * @param size 查询大小
//     * @return
//     */
//    List<EvseCfgVo> getEvseCfgList(@Param("size") Long size);

    /**
     * 重置使用(激活倒数第二条数据)
     *
     * @param ids
     */
    void resetEvseCfgUpdate(@Param("ids") List<Long> ids);

    List<Long> resetEvseCfgSelect(@Param("evseNoList") List<String> evseNoList);
}
