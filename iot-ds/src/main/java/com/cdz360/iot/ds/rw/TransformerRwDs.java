package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.TransformerRwMapper;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransformerRwDs {

	@Autowired
	private TransformerRwMapper ransformerRwMapper;

	public TransformerPo getById(Long id, boolean lock) {
		return this.ransformerRwMapper.getById(id, lock);
	}

	public boolean insertTransformer(TransformerPo TransformerPo) {
		return this.ransformerRwMapper.insertTransformer(TransformerPo) > 0;
	}

	public boolean updateTransformer(TransformerPo TransformerPo) {
		return this.ransformerRwMapper.updateTransformer(TransformerPo) > 0;
	}

	public boolean delete(long id) {
		return this.ransformerRwMapper.delete(id) > 0;
	}


}
