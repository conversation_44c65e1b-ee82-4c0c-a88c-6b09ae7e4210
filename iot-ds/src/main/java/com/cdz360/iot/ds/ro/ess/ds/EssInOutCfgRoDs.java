package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssInOutCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssInOutCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssInOutCfgRoDs {



	@Autowired

	private EssInOutCfgRoMapper essInOutCfgRoMapper;
	public EssInOutCfgPo getById(Long id) {
		return this.essInOutCfgRoMapper.getById(id);
	}




}

