package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.meter.po.MeterRecordPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface MeterRecordRwMapper {
	MeterRecordPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertMeterRecord(MeterRecordPo meterRecordPo);

	int updateMeterRecord(MeterRecordPo meterRecordPo);


}
