package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.UpgradeTaskDetailRwMapper;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname UpgradeTaskDetailRwDs
 * @Description TODO
 * @Date 9/21/2019 2:41 PM
 * @Created by Rafael
 */
@Service
public class UpgradeTaskDetailRwDs {
    @Autowired
    private UpgradeTaskDetailRwMapper upgradeTaskDetailRwMapper;

    public int insert(UpgradeTaskDetailVo upgradeTaskDetailVo) {
        return upgradeTaskDetailRwMapper.insert(upgradeTaskDetailVo);
    }
    public int update(UpgradeTaskDetailVo upgradeTaskDetailVo) {
        return upgradeTaskDetailRwMapper.update(upgradeTaskDetailVo);
    }
}