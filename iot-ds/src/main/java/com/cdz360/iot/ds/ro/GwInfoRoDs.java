package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.GwInfoRoMapper;
import com.cdz360.iot.model.gw.GwTimeoutPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.type.GwStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GwInfoRoDs {

    @Autowired
    private GwInfoRoMapper gwInfoRoMapper;

    public GwInfoDto getByGwno(String gwno) {
        return gwInfoRoMapper.getByGwno(gwno);
    }

    public GwInfoDto getByKeyword(String keyword) {
        return gwInfoRoMapper.getByKeyword(keyword);
    }

    public GwInfoDto getByGwnoAndEnable(String gwno) {

        return gwInfoRoMapper.getByGwnoAndEnable(gwno);
    }

    public List<GwInfoDto> getByMac(String mac) {
        return gwInfoRoMapper.getByMac(mac);

    }


    public List<GwInfoDto> getGwList(List<String> gwnos, GwStatus status) {

        return gwInfoRoMapper.getGwList(gwnos, status);
    }


    public List<GwInfoPo> listGw(List<GwStatus> statusList,
                                 GwMqType mqType,
                                 Date updateTime,
                                 long start, int size) {

        return gwInfoRoMapper.listGw(statusList, mqType, updateTime, start, size);
    }

    public List<GwTimeoutPo> getGwnosLoginTout(Integer minute) {

        return gwInfoRoMapper.getGwnosLoginTout(minute);
    }
}
