<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.GtiGridDispatchCfgRwMapper">

  <insert id="insertGtiCfg" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo">

    INSERT INTO iot.t_gti_grid_dispatch_cfg
    (cfgId, systemSwitch, systemTime, timezone,
    quCurveMode, quDispatchPowerPct, activePowerFixedDeratingKw,
    reactivePowerCompensationPf, reactivePowerCompensationQs, activePowerPctDerating,
    activePowerFixedDeratingW, nightReactivePowerCompensation, reactivePowerAdjustmentTime,
    quExitPowerPct, reactivePowerChangeGradient, activePowerChangeGradient,
    schedulingCommandMaintenanceTime, gridStandardCode,
    ppnCurve,
    quCurve,
    pfuCurve
    )
    VALUES
    (#{cfgId}, #{systemSwitch}, #{systemTime}, #{timezone},
    #{quCurveMode}, #{quDispatchPowerPct}, #{activePowerFixedDeratingKw},
    #{reactivePowerCompensationPf}, #{reactivePowerCompensationQs}, #{activePowerPctDerating},
    #{activePowerFixedDeratingW}, #{nightReactivePowerCompensation}, #{reactivePowerAdjustmentTime},
    #{quExitPowerPct}, #{reactivePowerChangeGradient}, #{activePowerChangeGradient},
    #{schedulingCommandMaintenanceTime}, #{gridStandardCode},
    #{ppnCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    #{quCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
    #{pfuCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler}
    )
  </insert>


  <update id="updateGtiCfg" parameterType="com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo">

    update
    t_gti_grid_dispatch_cfg
    <set>
      <if test="systemSwitch != null">
        systemSwitch = #{systemSwitch},
      </if>
      <if test="systemTime != null">
        systemTime = #{systemTime},
      </if>
      <if test="timezone != null">
        timezone = #{timezone},
      </if>
      <if test="quCurveMode != null">
        quCurveMode = #{quCurveMode},
      </if>
      <if test="quDispatchPowerPct != null">
        quDispatchPowerPct = #{quDispatchPowerPct},
      </if>
      <if test="activePowerFixedDeratingKw != null">
        activePowerFixedDeratingKw = #{activePowerFixedDeratingKw},
      </if>
      <if test="reactivePowerCompensationPf != null">
        reactivePowerCompensationPf = #{reactivePowerCompensationPf},
      </if>
      <if test="reactivePowerCompensationQs != null">
        reactivePowerCompensationQs = #{reactivePowerCompensationQs},
      </if>
      <if test="activePowerPctDerating != null">
        activePowerPctDerating = #{activePowerPctDerating},
      </if>
      <if test="activePowerFixedDeratingW != null">
        activePowerFixedDeratingW = #{activePowerFixedDeratingW},
      </if>
      <if test="nightReactivePowerCompensation != null">
        nightReactivePowerCompensation = #{nightReactivePowerCompensation},
      </if>
      <if test="reactivePowerAdjustmentTime != null">
        reactivePowerAdjustmentTime = #{reactivePowerAdjustmentTime},
      </if>
      <if test="quExitPowerPct != null">
        quExitPowerPct = #{quExitPowerPct},
      </if>
      <if test="reactivePowerChangeGradient != null">
        reactivePowerChangeGradient = #{reactivePowerChangeGradient},
      </if>
      <if test="activePowerChangeGradient != null">
        activePowerChangeGradient = #{activePowerChangeGradient},
      </if>
      <if test="schedulingCommandMaintenanceTime != null">
        schedulingCommandMaintenanceTime = #{schedulingCommandMaintenanceTime},
      </if>
      <if test="gridStandardCode != null">
        gridStandardCode = #{gridStandardCode},
      </if>
      <if test="ppnCurve != null">
        ppnCurve = #{ppnCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
      </if>
      <if test="quCurve != null">
        quCurve = #{quCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
      </if>
      <if test="pfuCurve != null">
        pfuCurve = #{pfuCurve, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
      </if>
    </set>
    where
    cfgId = #{cfgId}
  </update>


</mapper>

