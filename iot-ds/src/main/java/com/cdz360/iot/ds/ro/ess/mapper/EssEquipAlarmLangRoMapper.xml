<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssEquipAlarmLangRoMapper">


  <resultMap id="RESULT_ESS_EQUIP_ALARM_LANG_PO"
    type="com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo">

    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="equipType" jdbcType="VARCHAR" property="equipType"/>
    <result column="lang" jdbcType="VARCHAR" property="lang"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="level" jdbcType="INTEGER" property="level"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="DATE" property="createTime"/>
    <result column="updateTime" jdbcType="DATE" property="updateTime"/>

  </resultMap>

  <resultMap id="RESULT_ESS_EQUIP_ALARM_LANG_DTO"
    type="com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto">

    <result column="equipType" jdbcType="VARCHAR" property="equipType"/>
    <result column="lang" jdbcType="VARCHAR" property="lang"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="level" jdbcType="INTEGER" property="level"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt"/>

  </resultMap>


  <select id="getById"

    resultMap="RESULT_ESS_EQUIP_ALARM_LANG_PO">
    select * from t_ess_equip_alarm_lang where id = #{id}

  </select>

  <select id="getAlarmLangDtoList"
    parameterType="com.cdz360.iot.model.ess.param.ListAlarmLangParam"
    resultMap="RESULT_ESS_EQUIP_ALARM_LANG_DTO">
    select eal.* from t_ess_equip_alarm_lang eal
    where eal.`lang` = #{lang}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( equipTypes )">
      and eal.equipType in
      <foreach collection="equipTypes" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( codes )">
      and eal.`code` in
      <foreach collection="codes" index="index" item="item"
        open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    and eal.`enable` = true
  </select>
</mapper>

