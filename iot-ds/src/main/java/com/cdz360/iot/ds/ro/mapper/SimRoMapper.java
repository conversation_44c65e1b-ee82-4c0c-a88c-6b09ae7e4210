package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.sim.param.ListSimParam;
import com.cdz360.iot.model.sim.param.SimQueryCondition;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.vo.SimTinyVo;
import com.cdz360.iot.model.sim.vo.SimUnionVo;
import com.cdz360.iot.model.sim.vo.SimVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname BiPlugRoMapper
 * @Description
 * @Date 3/23/2020 6:15 PM
 * @Created by Rafael
 */
@Mapper
public interface SimRoMapper {

    List<SimVo> getSimList(@Param("param") ListSimParam param,
        @Param("evseNoIccids") List<String> evseNoIccids,
        @Param("siteIdIccids") List<String> siteIdIccids);

    Long getSimListCount(@Param("param") ListSimParam param,
        @Param("evseNoIccids") List<String> evseNoIccids,
        @Param("siteIdIccids") List<String> siteIdIccids);

    List<SimTinyVo> getTinyList(@Param("param") ListSimParam param);

    SimVo getVoBySimId(@Param("simId") Long simId);

    List<SimVo> queryByCondition(SimQueryCondition param);

    SimPo getOneByCode(@Param("code") String code);

    SimPo getById(@Param("simId") Long simId);

    List<SimPo> getUnionAll(@Param("codeList") List<String> codeList);

    List<String> getUnion(@Param("codeList") List<String> codeList);

    SimPo getByIccid(@Param("iccid") String iccid);

    List<SimUnionVo> getUnionVo(@Param("codeList") List<String> codeList);

}