package com.cdz360.iot.ds.ro.pv.mapper;


import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import com.cdz360.iot.model.pv.vo.DayPvDataBi;
import com.cdz360.iot.model.pv.vo.DaySitePvRtDataBi;
import com.cdz360.iot.model.pv.vo.GtiDataInTimeVo;
import com.cdz360.iot.model.pv.vo.GtiSampleData;
import com.cdz360.iot.model.pv.vo.GtiTinyBi;
import com.cdz360.iot.model.pv.vo.PvDataBi;
import com.cdz360.iot.model.pv.vo.SitePvRtDataBi;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface GtiDailyRoMapper {



	GtiDailyPo getById(@Param("id") Long id);

	GtiDataInTimeVo gtiInfoInTime(@Param("dno") String dno);

    List<DaySitePvRtDataBi> siteDayOfMonthKwh(DayKwhParam param);

	List<DaySitePvRtDataBi> siteDayOfYearKwh(DayKwhParam param);

	SitePvRtDataBi rtDataOfMonth(DayKwhParam param);

	SitePvRtDataBi rtDataOfYear(DayKwhParam param);

	SitePvRtDataBi rtDataOfTotal(DayKwhParam param);

	List<SitePvRtDataBi> siteRtDataOfMonth(DayKwhParam param);
	List<SitePvRtDataBi> siteRtDataOfTotal(DayKwhParam param);

	List<DayPvDataBi> siteRtDataOfDay(@Param("siteId") String siteId,
									  @Param("fromDate") Date fromDate,
									  @Param("toDate") Date toDate);

	List<DayPvDataBi> siteRtDataOfMonthGroup(@Param("siteId") String siteId,
									  @Param("fromDate") Date fromDate,
									  @Param("toDate") Date toDate);

	List<GtiTinyBi> getGtiTinyBi(@Param("siteIdList") List<String> siteIdList);

	List<GtiSampleData> gtiRtDataSample(DataBiParam param);

	PvDataBi getPvMapDataVo(@Param("gids") List<String> gids);

}
