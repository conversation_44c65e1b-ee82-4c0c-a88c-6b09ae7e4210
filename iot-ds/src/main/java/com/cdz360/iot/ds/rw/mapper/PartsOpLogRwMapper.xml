<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.PartsOpLogRwMapper">

  <resultMap id="RESULT_PARTSOPLOG_PO" type="com.cdz360.iot.model.parts.po.PartsOpLogPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="opType" property="opType"/>
    <result column="opUid" jdbcType="BIGINT" property="opUid"/>
    <result column="detail" property="detail" jdbcType="VARCHAR"
      typeHandler="com.cdz360.iot.ds.GenericTypeHandler"/>
    <result column="partsCode" jdbcType="VARCHAR" property="partsCode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>


  <select id="getById"
    resultMap="RESULT_PARTSOPLOG_PO">
    select * from t_parts_op_log where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertPartsOpLog" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.parts.po.PartsOpLogPo">
    insert into t_parts_op_log (`opType`,
    `opUid`,
    `detail`,
    `partsCode`,
    `createTime`)
    values (#{opType},
    #{opUid},
    #{detail, typeHandler=com.cdz360.iot.ds.GenericTypeHandler},
    #{partsCode},
    now())
  </insert>

  <update id="updatePartsOpLog" parameterType="com.cdz360.iot.model.parts.po.PartsOpLogPo">
    update t_parts_op_log set
    <if test="opType != null">
      opType = #{opType},
    </if>
    <if test="opUid != null">
      opUid = #{opUid},
    </if>
    <if test="detail != null">
      detail = #{detail, typeHandler=com.cdz360.iot.ds.GenericTypeHandler},
    </if>
    <if test="partsCode != null">
      partsCode = #{partsCode},
    </if>
    where id = #{id}
  </update>

</mapper>

