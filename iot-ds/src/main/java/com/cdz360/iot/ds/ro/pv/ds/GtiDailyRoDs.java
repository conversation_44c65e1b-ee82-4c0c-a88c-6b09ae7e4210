package com.cdz360.iot.ds.ro.pv.ds;


import com.cdz360.iot.ds.ro.pv.mapper.GtiDailyRoMapper;
import com.cdz360.iot.model.pv.param.DataBiParam;
import com.cdz360.iot.model.pv.param.DayKwhParam;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import com.cdz360.iot.model.pv.vo.DayPvDataBi;
import com.cdz360.iot.model.pv.vo.DaySitePvRtDataBi;
import com.cdz360.iot.model.pv.vo.GtiDataInTimeVo;
import com.cdz360.iot.model.pv.vo.GtiSampleData;
import com.cdz360.iot.model.pv.vo.GtiTinyBi;
import com.cdz360.iot.model.pv.vo.PvDataBi;
import com.cdz360.iot.model.pv.vo.SitePvRtDataBi;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class GtiDailyRoDs {



	@Autowired

	private GtiDailyRoMapper gtiDailyRoMapper;



	public GtiDailyPo getById(Long id) {

		return this.gtiDailyRoMapper.getById(id);

	}

	public GtiDataInTimeVo gtiInfoInTime(String dno) {
		return this.gtiDailyRoMapper.gtiInfoInTime(dno);
	}

	public List<DaySitePvRtDataBi> siteDayOfMonthKwh(DayKwhParam param) {
		return this.gtiDailyRoMapper.siteDayOfMonthKwh(param);
	}

	public List<DaySitePvRtDataBi> siteDayOfYearKwh(DayKwhParam param) {
		return this.gtiDailyRoMapper.siteDayOfYearKwh(param);
	}

	public SitePvRtDataBi rtDataOfMonth(DayKwhParam param) {
		SitePvRtDataBi bi = this.gtiDailyRoMapper.rtDataOfMonth(param);
		return null != bi ? bi : new SitePvRtDataBi().setTotalKwh(BigDecimal.ZERO).setTotalProfit(BigDecimal.ZERO);
	}

	public SitePvRtDataBi rtDataOfYear(DayKwhParam param) {
		SitePvRtDataBi bi = this.gtiDailyRoMapper.rtDataOfYear(param);
		return null != bi ? bi : new SitePvRtDataBi().setTotalKwh(BigDecimal.ZERO).setTotalProfit(BigDecimal.ZERO);
	}

	public SitePvRtDataBi rtDataOfTotal(DayKwhParam param) {
		SitePvRtDataBi bi = this.gtiDailyRoMapper.rtDataOfTotal(param);
		return null != bi ? bi : new SitePvRtDataBi().setTotalKwh(BigDecimal.ZERO).setTotalProfit(BigDecimal.ZERO);
	}

	public List<SitePvRtDataBi> siteRtDataOfMonth(DayKwhParam param) {
		return this.gtiDailyRoMapper.siteRtDataOfMonth(param);
	}
	public List<SitePvRtDataBi> siteRtDataOfTotal(DayKwhParam param) {
		return this.gtiDailyRoMapper.siteRtDataOfTotal(param);
	}

	public List<DayPvDataBi> siteRtDataOfDay(String siteId, Date fromDate, Date toDate) {
		return this.gtiDailyRoMapper.siteRtDataOfDay(siteId, fromDate, toDate);
	}

	public List<DayPvDataBi> siteRtDataOfMonthGroup(String siteId, Date fromDate, Date toDate) {
		return this.gtiDailyRoMapper.siteRtDataOfMonthGroup(siteId, fromDate, toDate);
	}

	public List<GtiTinyBi> getGtiTinyBi(List<String> siteIdList) {
		return this.gtiDailyRoMapper.getGtiTinyBi(siteIdList);
	}

	public List<GtiSampleData> gtiRtDataSample(DataBiParam param) {
		return this.gtiDailyRoMapper.gtiRtDataSample(param);
	}

	public PvDataBi getPvMapDataVo(List<String> gids) {
		return this.gtiDailyRoMapper.getPvMapDataVo(gids);
	}

}

