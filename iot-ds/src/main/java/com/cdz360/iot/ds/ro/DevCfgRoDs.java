package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.DevCfgRoMapper;
import com.cdz360.iot.model.ess.param.ListDevCfgParam;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.ess.vo.DevCfgVo;
import com.cdz360.iot.model.ess.vo.GtiCfgVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class DevCfgRoDs {


    @Autowired

    private DevCfgRoMapper devCfgRoMapper;


    public DevCfgPo getById(Long id, String commIdChain) {

        return this.devCfgRoMapper.getById(id, commIdChain);

    }


    public DevCfgPo getById(Long id) {
        return this.devCfgRoMapper.queryById(id);
    }

    public List<Long> getByName(String name, String siteId) {

        return this.devCfgRoMapper.getByName(name, siteId);

    }

    public GtiCfgVo getGtiDetailById(Long id, String commIdChain, CfgType cfgType) {
        return this.devCfgRoMapper.getGtiDetailById(id, commIdChain, cfgType);
    }

    public Long getDevCfgCount(ListDevCfgParam param) {

        return this.devCfgRoMapper.getDevCfgCount(param);

    }

    public List<DevCfgVo> getDevCfgList(ListDevCfgParam param) {

        return this.devCfgRoMapper.getDevCfgList(param);

    }

}

