package com.cdz360.iot.ds.rw;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.rw.mapper.EvseCfgRwMapper;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.model.evse.param.ListEvseCfgParam;
import com.cdz360.iot.model.evse.po.EvseCfgPo;
import com.cdz360.iot.model.evse.vo.EvseCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EvseCfgRwDs {

    @Autowired
    private EvseCfgRwMapper evseCfgRwMapper;

    @Transactional
    public void insertEvseCfg(EvseCfgPo evseCfg) {
        this.disableEvseCfg(evseCfg.getEvseNo());
        this.evseCfgRwMapper.insertEvseCfg(evseCfg);
    }

    public void disableEvseCfg(String evseNo) {
        this.evseCfgRwMapper.disableEvseCfg(evseNo);
    }

    public void disableEvseCfgList(List<String> evseNoList) {
        this.evseCfgRwMapper.disableEvseCfgList(evseNoList);
    }

    public void resetEvseCfg(List<String> evseNoList) {
        if (CollectionUtils.isEmpty(evseNoList)) {
            return;
        }

        this.disableEvseCfgList(evseNoList);
        List<Long> ids = this.evseCfgRwMapper.resetEvseCfgSelect(evseNoList);
        if (CollectionUtils.isNotEmpty(ids)) {
            this.evseCfgRwMapper.resetEvseCfgUpdate(ids);
        }
    }

    public EvseCfgPo getEvseCfg(String evseNo, boolean lock) {
        return this.evseCfgRwMapper.getEvseCfg(evseNo, lock);
    }

    public ListResponse<EvseCfgVo> getEvseCfgVoList(ListEvseCfgParam param) {
        var list = evseCfgRwMapper.getEvseCfgVoList(param);
        if (Boolean.TRUE.equals(param.getTotal())) {
            var total = evseCfgRwMapper.getEvseCfgVoCount(param);
            return new ListResponse<>(list, total);
        } else {
            return new ListResponse<>(list);
        }
    }

//    /**
//     * 用来即时下发查询操作
//     *
//     * @param size
//     * @return
//     */
//    public ListResponse<EvseCfgVo> getEvseCfgList(Long size) {
//        return new ListResponse<>(evseCfgRwMapper.getEvseCfgList(size));
//    }
}
