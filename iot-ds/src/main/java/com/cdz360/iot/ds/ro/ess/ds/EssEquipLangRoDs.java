package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.ds.ro.ess.mapper.EssEquipLangRoMapper;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class EssEquipLangRoDs {


    @Autowired
    private EssEquipLangRoMapper essEquipLangRoMapper;


    public EssEquipLangPo getById(Long id) {
        return this.essEquipLangRoMapper.getById(id);
    }

    public List<EssEquipLangPo> getEquipLangList(String vendor,
        EssEquipType equipType,
        String lang) {
        return this.essEquipLangRoMapper.getEquipLangList(vendor, equipType, lang);
    }

}

