package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.park.po.ParkChannelPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



@Mapper

public interface ParkChannelRwMapper {

	ParkChannelPo getById(@Param("id") Integer id, @Param("lock") boolean lock);



	int insertParkChannel(ParkChannelPo parkChannelPo);



	int updateParkChannel(ParkChannelPo parkChannelPo);





}

