<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseBundlePcOriginQueryMapper">
    <resultMap id="BaseResultMap" type="com.cdz360.iot.model.evse.EvseBundlePcOrigin">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bundleId" jdbcType="BIGINT" property="bundleId"/>
        <result column="pcId" jdbcType="BIGINT" property="pcId"/>
        <result column="pcName" jdbcType="VARCHAR" property="pcName"/>
        <result column="origin" jdbcType="VARCHAR" property="origin"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, bundleId, pcId, pcName, origin, createTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_evse_bundle_pc_origin
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByBundleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_evse_bundle_pc_origin
        where bundleId = #{id,jdbcType=BIGINT}
    </select>
</mapper>