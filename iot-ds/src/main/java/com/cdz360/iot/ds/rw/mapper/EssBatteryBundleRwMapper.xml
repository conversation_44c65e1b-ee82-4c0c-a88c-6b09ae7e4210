<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssBatteryBundleRwMapper">



	<resultMap id="RESULT_ESSBATTERYCLUSTER_PO" type="com.cdz360.iot.model.ess.po.EssBatteryBundlePo">
		<result column="dno" jdbcType="VARCHAR" property="dno" />
		<result column="essDno" jdbcType="VARCHAR" property="essDno" />
		<result column="bmsDno" jdbcType="VARCHAR" property="bmsDno" />
		<result column="stackDno" jdbcType="VARCHAR" property="stackDno" />
		<result column="idx" jdbcType="INTEGER" property="idx" />
		<result column="stackEquipId" jdbcType="BIGINT" property="stackEquipId" />
		<result column="equipId" jdbcType="BIGINT" property="equipId" />
		<result column="clusterNo" jdbcType="BIGINT" property="clusterNo" />
	</resultMap>



	<insert id="upsetEssBatteryCluster" useGeneratedKeys="true" keyProperty="id"
			keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssBatteryBundlePo">
		insert into t_ess_battery_bundle (`dno`, `essDno`, bmsDno, stackDno,	`idx`,
			`stackEquipId`,
			`equipId`,
			`clusterNo`)
		values (#{dno}, #{essDno}, #{bmsDno}, #{stackDno},	#{idx},
			#{stackEquipId},
			#{equipId},
			#{clusterNo})
		ON DUPLICATE KEY UPDATE
		clusterNo = #{clusterNo}
	</insert>


	<insert id="batchInsertBundle">
		insert into t_ess_battery_bundle
		(dno, essDno, bmsDno, stackEquipId, equipId)
		<foreach collection="poList" open="values" close=""
			separator="," item="po">
			(#{po.dno}, #{po.essDno}, #{po.bmsDno}, #{po.stackEquipId}, #{po.equipId})
		</foreach>
		ON DUPLICATE KEY UPDATE
		`essDno` = values(essDno),
		`bmsDno` = values(bmsDno),
		`stackDno` = values(stackDno),
		`clusterNo` = values(clusterNo)
	</insert>



</mapper>

