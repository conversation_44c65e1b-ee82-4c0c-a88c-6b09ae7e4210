package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.iot.model.ess.param.BmsRelevantParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.vo.BmsRelevantVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EssBatteryBundleRoMapper {

    List<EssBatteryBundlePo> getBatteryBundleList(ListEssEquipParam param);

    EssBatteryBundlePo getByEssDnoEquipIdStackEquipId(
        @Param("essDno") String essDno,
        @Param("equipId") Long equipId,
        @Param("stackEquipId") Long stackEquipId);

    EssBatteryBundlePo getByDno(@Param("dno") String dno);

    BmsRelevantVo bmsRelevant(BmsRelevantParam param);
}

