package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.type.PartsLocationStatus;
import com.cdz360.iot.model.parts.type.PartsStatus;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PartsRwMapper {

    PartsPo getByCode(String code, boolean lock);

    int insertParts(PartsPo partsPo);

    int updateParts(PartsPo partsPo);


    int updatePartsLocationStatus(
        @Param("code") String code,
        @Param("storageCode") String storageCode,
        @Param("fromStatus") PartsLocationStatus fromStatus,
        @Param("toStatus") PartsLocationStatus toStatus);

    int batchInsert(@Param("poList") List<PartsPo> poList);

    int updatePartsBroken(@Param("codeList") List<String> codeList);

    int batchUpdatePartsStatus(@Param("codeList") List<String> codeList,
        @Param("typeId") Long typeId, @Param("toStatus") PartsStatus toStatus);

    int updatePartsTransReview(@Param("agree") Boolean agree,
        @Param("code") String code,
        @Param("storageCode") String storageCode,
        @Param("remark") String remark);

    int updatePartsTransApply(@Param("code") String code, @Param("uid") Long uid);
}

