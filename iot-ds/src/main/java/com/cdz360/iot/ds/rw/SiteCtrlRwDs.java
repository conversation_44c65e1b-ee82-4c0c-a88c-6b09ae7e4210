package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteCtrlRwMapper;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname SiteCtrlRwDs
 * @Description
 * @Date 4/22/2020 4:34 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlRwDs {
    @Autowired
    private SiteCtrlRwMapper siteCtrlRwMapper;

    public int insertOrUpdate(SiteCtrlPo param) {
        return siteCtrlRwMapper.insertOrUpdate(param);
    }

    public boolean updateLoginTime(String num) {
        return siteCtrlRwMapper.updateLoginTime(num) > 0;
    }

    public boolean disable(String num) {
        return siteCtrlRwMapper.disable(num) > 0;
    }
}