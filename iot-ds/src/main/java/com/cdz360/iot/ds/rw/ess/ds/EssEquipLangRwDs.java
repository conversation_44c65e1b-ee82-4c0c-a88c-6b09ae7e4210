package com.cdz360.iot.ds.rw.ess.ds;


import com.cdz360.iot.ds.rw.ess.mapper.EssEquipLangRwMapper;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class EssEquipLangRwDs {


    @Autowired

    private EssEquipLangRwMapper essEquipLangRwMapper;


    public EssEquipLangPo getById(Long id, boolean lock) {

        return this.essEquipLangRwMapper.getById(id, lock);

    }


    public boolean insertEssEquipLang(EssEquipLangPo essEquipLangPo) {

        return this.essEquipLangRwMapper.insertEssEquipLang(essEquipLangPo) > 0;

    }


    public boolean updateEssEquipLang(EssEquipLangPo essEquipLangPo) {

        return this.essEquipLangRwMapper.updateEssEquipLang(essEquipLangPo) > 0;

    }


}

