package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.common.base.IotCacheConstants;
import com.cdz360.iot.model.gw.GwTimeoutPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.type.GwStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;

import java.util.Date;
import java.util.List;

@Mapper
public interface GwInfoRoMapper {


    @Cacheable(cacheNames = IotCacheConstants.IOT_GW_INFO_KEY)
    GwInfoDto getByGwno(@Param("gwno") String gwno);

    GwInfoDto getByGwnoAndEnable(@Param("gwno") String gwno);

    List<GwInfoDto> getByMac(@Param("mac") String mac);


    List<GwInfoDto> getGwList(@Param("gwnos") List<String> gwnos, @Param("status") GwStatus status);


    List<GwInfoPo> listGw(@Param("statusList") List<GwStatus> statusList,
                          @Param("mqType") GwMqType mqType,
                          @Param("updateTime") Date updateTime,
                          @Param("start") long start, @Param("size") int size);

    List<GwTimeoutPo> getGwnosLoginTout(@Param("timeout") Integer minute);

    GwInfoDto getByKeyword(@Param("keyword") String keyword);
}
