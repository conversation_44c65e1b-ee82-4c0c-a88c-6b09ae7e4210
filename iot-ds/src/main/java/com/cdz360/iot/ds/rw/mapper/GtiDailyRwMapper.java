package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.pv.po.GtiDailyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GtiDailyRwMapper {

	GtiDailyPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int upsetGtiDaily(GtiDailyPo gtiDailyPo);

	int batchUpsetGtiDaily(@Param("list") List<GtiDailyPo> gtiDailyPoList);
}

