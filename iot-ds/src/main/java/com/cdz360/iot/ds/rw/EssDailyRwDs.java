package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EssDailyRwMapper;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDailyRwDs {

    @Autowired
    private EssDailyRwMapper essDailyRwMapper;

    public EssDailyPo getById(Long id, boolean lock) {
        return this.essDailyRwMapper.getById(id, lock);
    }

    public boolean upsetEssDaily(EssDailyPo dailyPo) {
        return this.essDailyRwMapper.upsetEssDaily(dailyPo) > 0;
    }
}

