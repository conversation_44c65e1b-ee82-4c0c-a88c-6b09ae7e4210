<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseOfflineRwMapper">

    <resultMap id="RESULT_MAP_EVSE_PO" type="com.cdz360.iot.model.evse.EvsePo" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="gwno" property="gwno" jdbcType="VARCHAR" />
        <id column="evseId" property="evseId" jdbcType="VARCHAR" />
        <id column="name" property="name" jdbcType="VARCHAR" />
        <id column="power" property="power" jdbcType="INTEGER" />
        <id column="evseStatus" property="evseStatus" jdbcType="VARCHAR" />
        <id column="supply" property="supply" jdbcType="VARCHAR" />
        <id column="net" property="net" jdbcType="VARCHAR" />
        <id column="dtuType" property="dtuType" jdbcType="INTEGER"
            typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
        <id column="ip" property="ip" jdbcType="VARCHAR" />
        <id column="iccid" property="iccid" jdbcType="VARCHAR" />
        <id column="imsi" property="imsi" jdbcType="VARCHAR" />
        <id column="imei" property="imei" jdbcType="VARCHAR" />
        <id column="model" property="model" jdbcType="VARCHAR" />
        <id column="plugNum" property="plugNum" jdbcType="INTEGER" />
        <id column="siteId" property="siteId" jdbcType="VARCHAR" />
        <id column="commId" property="commId" jdbcType="BIGINT" />
        <id column="priceCode" property="priceCode" jdbcType="BIGINT" />
        <id column="protocolVer" property="protocolVer" jdbcType="INTEGER" />
        <id column="firmwareVer" property="firmwareVer" jdbcType="VARCHAR" />
        <id column="pc01Ver" property="pc01Ver" jdbcType="VARCHAR" />
        <id column="pc02Ver" property="pc02Ver" jdbcType="VARCHAR" />
        <id column="pc03Ver" property="pc03Ver" jdbcType="VARCHAR" />
        <id column="protocol" property="protocol" jdbcType="VARCHAR" />
        <id column="modelName" property="modelName" jdbcType="VARCHAR" />
        <id column="voltage" property="voltage" jdbcType="DECIMAL" />
        <id column="current" property="current" jdbcType="DECIMAL" />
        <id column="passcodeVer" property="passcodeVer" jdbcType="BIGINT" />
        <id column="debugTag" property="debugTag" jdbcType="BOOLEAN" />
        <id column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <id column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
        <id column="upgradeStatus" property="upgradeStatus" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="batchAddOfflineEvse" parameterType="com.cdz360.iot.model.evse.vo.OfflineEvseVo">
        insert into
        t_evse_offline(evseId, siteId, `name`, supply, model,
            protocolVer, firmwareVer, power, iccid, createTime, updateTime)
        values
        <foreach collection="list" item="e"
                 open="" close="" separator=",">
            (#{e.evseNo}, #{e.siteId}, #{e.name}, #{e.supplyType}, #{e.model},
                #{e.protocolVer}, #{e.firmwareVer}, #{e.power}, #{e.iccid}, now(), now())
        </foreach>
    </insert>

    <update id="updateOfflineBatch" parameterType="com.cdz360.iot.model.evse.param.ModifyEvseInfoParam">
        update t_evse_offline
        set
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
                siteId = #{siteId},
            </if>
            <if test="power != null">
                `power` = #{power},
            </if>
            <if test="model != null">
                `model` = #{model},
            </if>
            <if test="supplyType != null">
                `supply` = #{supplyType},
            </if>
            <if test="protocolVer != null">
                protocolVer = #{protocolVer},
            </if>
            <if test="firmwareVer != null">
                firmwareVer = #{firmwareVer},
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( iccid )">
                iccid = #{iccid},
            </if>
            updateTime=now()
        where
            evseId in
            <foreach collection="evseNoList" item="evseNo" open="(" close=")" separator=",">
                #{evseNo}
            </foreach>
    </update>

    <delete id="removeOfflineEvse" parameterType="com.cdz360.iot.model.param.OfflineEvseParam">
        <foreach collection="paramList" item="p" open="" close="" separator=";">
            delete
            from t_evse_offline
            where
                evseId = #{p.evseNo}
                and siteId = #{p.siteId}
        </foreach>
    </delete>

</mapper>