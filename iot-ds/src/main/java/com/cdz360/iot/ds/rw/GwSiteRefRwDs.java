package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.GwSiteRefRwMapper;
import com.cdz360.iot.model.site.po.GwSiteRefPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GwSiteRefRwDs {

    @Autowired
    private GwSiteRefRwMapper gwSiteRefRwMapper;

    public Boolean upset(GwSiteRefPo po) {
        return gwSiteRefRwMapper.upset(po)>0;
    }
}
