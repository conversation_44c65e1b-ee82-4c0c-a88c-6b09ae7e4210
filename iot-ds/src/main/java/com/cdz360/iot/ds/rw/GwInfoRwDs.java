package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.GwInfoRwQueryMapper;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.GwStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GwInfoRwDs {


    @Autowired
    private GwInfoRwQueryMapper gwInfoRwQueryMapper;

    public GwInfoDto getByGwno(String gwno, boolean lock) {
        return gwInfoRwQueryMapper.getByGwno(gwno, lock);
    }

    public boolean update(GwInfoPo gw) {
        return this.gwInfoRwQueryMapper.update(gw) > 0;
    }

    public boolean upset(GwInfoPo gw) {
        return this.gwInfoRwQueryMapper.upset(gw) > 0;
    }

    public boolean updateStatus(String gwno, GwStatus status) {
        return this.gwInfoRwQueryMapper.updateStatus(gwno, status.name()) > 0;
    }
}
