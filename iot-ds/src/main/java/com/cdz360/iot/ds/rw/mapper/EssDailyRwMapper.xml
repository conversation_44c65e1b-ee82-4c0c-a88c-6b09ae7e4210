<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssDailyRwMapper">

	<resultMap id="RESULT_ESSDAILY_PO" type="com.cdz360.iot.model.ess.po.EssDailyPo">
		<id column="id" jdbcType="BIGINT" property="id"/>
		<result column="date" jdbcType="DATE" property="date"/>
		<result column="essId" jdbcType="BIGINT" property="essId"/>
		<result column="dno" jdbcType="VARCHAR" property="dno"/>
		<result column="siteId" jdbcType="VARCHAR" property="siteId"/>
		<result column="totalInKwh" jdbcType="DECIMAL" property="totalInKwh"/>
		<result column="totalAcInKwh" jdbcType="DECIMAL" property="totalAcInKwh"/>
		<result column="totalOutKwh" jdbcType="DECIMAL" property="totalOutKwh"/>
		<result column="totalAcOutKwh" jdbcType="DECIMAL" property="totalAcOutKwh"/>
		<result column="todayInKwh" jdbcType="DECIMAL" property="todayInKwh"/>
		<result column="todayAcInKwh" jdbcType="DECIMAL" property="todayAcInKwh"/>
		<result column="todayOutKwh" jdbcType="DECIMAL" property="todayOutKwh"/>
		<result column="todayAcOutKwh" jdbcType="DECIMAL" property="todayAcOutKwh"/>
		<result column="todayProfit" jdbcType="DECIMAL" property="todayProfit"/>
		<result column="todayInExpend" jdbcType="DECIMAL" property="todayInExpend"/>
		<result column="todayOutIncome" jdbcType="DECIMAL" property="todayOutIncome"/>
		<result column="inPriceId" jdbcType="BIGINT" property="inPriceId"/>
		<result column="outPriceId" jdbcType="BIGINT" property="outPriceId"/>
		<result column="enable" jdbcType="BOOLEAN" property="enable"/>
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
	</resultMap>

	<select id="getById"
		resultMap="RESULT_ESSDAILY_PO">
		select * from t_ess_daily where id = #{id}
		and enable = true
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="upsetEssDaily">
		insert into t_ess_daily (`date`,
		`essId`,
		`dno`,
		`siteId`,
		`totalInKwh`,
		`totalAcInKwh`,
		`totalOutKwh`,
		`totalAcOutKwh`,
		`todayInKwh`,
		`todayAcInKwh`,
		`todayOutKwh`,
		`todayAcOutKwh`,
		`todayProfit`,
		`todayInExpend`,
		`todayOutIncome`,
		`inPriceId`,
		`outPriceId`,
		`enable`,
		`createTime`,
		`updateTime`)
		values (#{date},
		#{essId},
		#{dno},
		#{siteId},
		#{totalInKwh},
		#{totalAcInKwh},
		#{totalOutKwh},
		#{totalAcOutKwh},
		#{todayInKwh},
		#{todayAcInKwh},
		#{todayOutKwh},
		#{todayAcOutKwh},
		#{todayProfit},
		#{todayInExpend},
		#{todayOutIncome},
		#{inPriceId},
		#{outPriceId},
		true,
		now(),
		now())
		ON DUPLICATE KEY UPDATE
		totalInKwh = #{totalInKwh},
		totalAcInKwh = #{totalAcInKwh},
		totalOutKwh = #{totalOutKwh},
		totalAcOutKwh = #{totalAcOutKwh},
		todayInKwh = #{todayInKwh},
		todayAcInKwh = #{todayAcInKwh},
		todayOutKwh = #{todayOutKwh},
		todayAcOutKwh = #{todayAcOutKwh},
		<if test="null != todayProfit">
			todayProfit = #{todayProfit},
		</if>
		<if test="null != todayInExpend">
			todayInExpend = #{todayInExpend},
		</if>
		<if test="null != todayOutIncome">
			todayOutIncome = #{todayOutIncome},
		</if>
		<if test="null != inPriceId">
			inPriceId = #{inPriceId},
		</if>
		<if test="null != outPriceId">
			outPriceId = #{outPriceId},
		</if>
		<if test="null != enable">
			`enable` = #{enable},
		</if>
		`updateTime` = now()
	</insert>

</mapper>

