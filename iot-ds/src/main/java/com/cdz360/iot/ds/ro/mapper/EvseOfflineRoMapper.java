package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseOfflineRoMapper {

    List<EvseInfoDto> getEvseInfoList(ListEvseParam param);

    Long count(@Param("evseNo") String evseNo,
               @Param("siteId") String siteId);

    List<EvsePlugRecordPo> getEvseRecordInfo(String siteId);

    List<EvsePo> getEvseList(ListEvseParam param);

    List<EvseTinyDto> getEvseTinyList(EvseTinyParam param);

    List<EvseModelVo> getEvseModelVoList(ListEvseParam param);

}
