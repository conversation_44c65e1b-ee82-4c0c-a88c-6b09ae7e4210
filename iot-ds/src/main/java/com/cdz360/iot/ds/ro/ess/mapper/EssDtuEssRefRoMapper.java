package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EssDtuEssRefRoMapper {

    EssDtuEssRefPo getBySerialNoAndDno(
        @Param("serialNo") String serialNo, @Param("dno") String dno);

    List<EssDtuEssRefPo> getBySerialNo(
        @Param("serialNo") String serialNo);
}

