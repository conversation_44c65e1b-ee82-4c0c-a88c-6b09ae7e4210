package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface PartsOpLogRwMapper {

    PartsOpLogPo getById(@Param("id") Long id, @Param("lock") boolean lock);


    int insertPartsOpLog(PartsOpLogPo partsOpLogPo);


    int updatePartsOpLog(PartsOpLogPo partsOpLogPo);


}

