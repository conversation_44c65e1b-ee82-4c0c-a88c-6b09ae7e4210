package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;

import java.util.List;

@Mapper
public interface EvseModuleDetailRwMapper {

    int insert(@Param("poList") List<EvseModuleDetailPo> poList);

    int delete(@Param("idList") List<Long> idList);

    int deleteByNumber(@NonNull @Param("moduleId") Long moduleId,
                       @NonNull @Param("number") Integer number);

    int updateById(EvseModuleDetailPo evseModuleDetailPo);

    int insertOrUpdate(EvseModuleDetailPo evseModuleDetailPo);
}
