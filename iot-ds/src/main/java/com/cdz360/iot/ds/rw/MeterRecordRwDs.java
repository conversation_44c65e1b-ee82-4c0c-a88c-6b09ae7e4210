package com.cdz360.iot.ds.rw;

import com.cdz360.iot.model.meter.po.MeterRecordPo;
import com.cdz360.iot.ds.rw.mapper.MeterRecordRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class MeterRecordRwDs {

	@Autowired
	private MeterRecordRwMapper meterRecordRwMapper;

	public MeterRecordPo getById(Long id, boolean lock) {
		return this.meterRecordRwMapper.getById(id, lock);
	}

	public boolean insertMeterRecord(MeterRecordPo meterRecordPo) {
		return this.meterRecordRwMapper.insertMeterRecord(meterRecordPo) > 0;
	}

	public boolean updateMeterRecord(MeterRecordPo meterRecordPo) {
		return this.meterRecordRwMapper.updateMeterRecord(meterRecordPo) > 0;
	}


}
