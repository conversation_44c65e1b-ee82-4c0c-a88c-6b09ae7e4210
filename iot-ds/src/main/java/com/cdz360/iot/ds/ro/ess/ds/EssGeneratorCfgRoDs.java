package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssGeneratorCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssGeneratorCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssGeneratorCfgRoDs {



	@Autowired

	private EssGeneratorCfgRoMapper essGeneratorCfgRoMapper;

	public EssGeneratorCfgPo getById(Long id) {
		return this.essGeneratorCfgRoMapper.getById(id);
	}



}

