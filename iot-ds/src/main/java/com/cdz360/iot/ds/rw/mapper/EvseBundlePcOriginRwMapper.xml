<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseBundlePcOriginRwMapper">
    <delete id="deleteByBundleId" parameterType="java.lang.Long">
        delete from t_evse_bundle_pc_origin
        where bundleId = #{bundleId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cdz360.iot.model.evse.EvseBundlePcOrigin"
            useGeneratedKeys="true">
        insert into t_evse_bundle_pc_origin (bundleId, pcId, pcName, origin, createTime)
        values (#{bundleId,jdbcType=BIGINT}, #{pcId,jdbcType=BIGINT}, #{pcName,jdbcType=VARCHAR},
        #{origin,jdbcType=VARCHAR}, now())
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cdz360.iot.model.evse.EvseBundlePcOrigin" useGeneratedKeys="true">
        insert into t_evse_bundle_pc_origin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">
                bundleId,
            </if>
            <if test="pcId != null">
                pcId,
            </if>
            <if test="pcName != null">
                pcName,
            </if>
            <if test="origin != null">
                origin,
            </if>
            createTime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">
                #{bundleId,jdbcType=BIGINT},
            </if>
            <if test="pcId != null">
                #{pcId,jdbcType=BIGINT},
            </if>
            <if test="pcName != null">
                #{pcName,jdbcType=VARCHAR},
            </if>
            <if test="origin != null">
                #{origin,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.cdz360.iot.model.evse.EvseBundlePcOrigin">
        update t_evse_bundle_pc_origin
        <set>
            <if test="bundleId != null">
                bundleId = #{bundleId,jdbcType=BIGINT},
            </if>
            <if test="pcId != null">
                pcId = #{pcId,jdbcType=BIGINT},
            </if>
            <if test="pcName != null">
                pcName = #{pcName,jdbcType=VARCHAR},
            </if>
            <if test="origin != null">
                origin = #{origin,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.cdz360.iot.model.evse.EvseBundlePcOrigin">
        update t_evse_bundle_pc_origin
        set bundleId = #{bundleId,jdbcType=BIGINT},
        pcId = #{pcId,jdbcType=BIGINT},
        pcName = #{pcName,jdbcType=VARCHAR},
        origin = #{origin,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into t_evse_bundle_pc_origin (bundleId, pcId, pcName, origin, createTime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.bundleId,jdbcType=BIGINT}, #{item.pcId,jdbcType=BIGINT}, #{item.pcName,jdbcType=VARCHAR},
            #{item.origin,jdbcType=VARCHAR}, now())
        </foreach>
    </insert>
</mapper>