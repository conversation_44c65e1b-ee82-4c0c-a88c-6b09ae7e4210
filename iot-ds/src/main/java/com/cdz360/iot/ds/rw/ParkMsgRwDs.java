package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.ParkMsgRwMapper;
import com.cdz360.iot.model.park.po.ParkMsgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ParkMsgRwDs {

	@Autowired
	private ParkMsgRwMapper parkMsgRwMapper;

	public ParkMsgPo getById(Integer id, boolean lock) {
		return this.parkMsgRwMapper.getById(id, lock);
	}

	public boolean insertParkMsg(ParkMsgPo parkMsgPo) {
		return this.parkMsgRwMapper.insertParkMsg(parkMsgPo) > 0;
	}

	public boolean updateParkMsg(ParkMsgPo parkMsgPo) {
		return this.parkMsgRwMapper.updateParkMsg(parkMsgPo) > 0;
	}


}
