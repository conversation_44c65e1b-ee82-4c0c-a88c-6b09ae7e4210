package com.cdz360.iot.ds.ro.ess.ds;


import com.cdz360.iot.ds.ro.ess.mapper.EssDrySpotCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssDrySpotCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssDrySpotCfgRoDs {



	@Autowired

	private EssDrySpotCfgRoMapper essDrySpotCfgRoMapper;

	public EssDrySpotCfgPo getById(Long id) {
		return this.essDrySpotCfgRoMapper.getById(id);
	}



}

