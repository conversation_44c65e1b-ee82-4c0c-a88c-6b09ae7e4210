<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.ess.mapper.EssEquipLangRwMapper">


  <resultMap id="RESULT_ESS_EQUIP_LANG_PO" type="com.cdz360.iot.model.ess.po.EssEquipLangPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="equipType"  property="equipType"
      typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
    <result column="lang" jdbcType="VARCHAR" property="lang"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="unit" jdbcType="VARCHAR" property="unit"/>
    <result column="vals" jdbcType="VARCHAR" property="values"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="desc" jdbcType="VARCHAR" property="desc"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="DATE" property="createTime"/>
    <result column="updateTime" jdbcType="DATE" property="updateTime"/>
  </resultMap>


  <select id="getById"
    resultMap="RESULT_ESS_EQUIP_LANG_PO">
    select * from t_ess_equip_lang where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>


  <insert id="insertEssEquipLang" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssEquipLangPo">
    insert into t_ess_equip_lang (`vendor`,
    `equipType`,
    `lang`,
    `code`,
    `name`,
    `unit`,
    `vals`,
    `desc`,
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{vendor},
    #{equipType},
    #{lang},
    #{code},
    #{name},
    #{unit},
    #{vals},
    #{desc},
    #{enable},
    now(),
    now())
  </insert>


  <update id="updateEssEquipLang" parameterType="com.cdz360.iot.model.ess.po.EssEquipLangPo">
    update t_ess_equip_lang set
    <if test="vendor != null">
      vendor = #{vendor},
    </if>
    <if test="equipType != null">
      equipType = #{equipType},
    </if>
    <if test="lang != null">
      lang = #{lang},
    </if>
    <if test="code != null">
      code = #{code},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="unit != null">
      unit = #{unit},
    </if>
    <if test="vals != null">
      vals = #{vals},
    </if>
    <if test="desc != null">
      desc = #{desc},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>


</mapper>

