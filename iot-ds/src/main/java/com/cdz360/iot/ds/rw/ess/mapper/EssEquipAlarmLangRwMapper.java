package com.cdz360.iot.ds.rw.ess.mapper;



import com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo;

import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

import java.lang.Double;

import java.util.Date;



@Mapper

public interface EssEquipAlarmLangRwMapper {

	EssEquipAlarmLangPo getById(@Param("id") Long id, @Param("lock") boolean lock);



	int insertEssEquipAlarmLang(EssEquipAlarmLangPo essEquipAlarmLangPo);



	int updateEssEquipAlarmLang(EssEquipAlarmLangPo essEquipAlarmLangPo);





}

