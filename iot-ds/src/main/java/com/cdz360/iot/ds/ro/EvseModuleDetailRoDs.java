package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseModuleDetailRoMapper;
import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseModuleDetailRoDs {

    @Autowired
    private EvseModuleDetailRoMapper mapper;

    public Long getValidModuleNum(Long moduleId) {
        return mapper.getValidModuleNum(moduleId);
    }

    public Long countByCondition(Long moduleId) {
        return mapper.countByCondition(moduleId);
    }

    public List<EvseModuleDetailPo> findById(Long moduleId) {
        return mapper.findById(moduleId);
    }

}
