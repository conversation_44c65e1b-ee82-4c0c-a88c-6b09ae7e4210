package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UpgradeLogRoMapper {

    UpgradeLogPo getById(@Param("id") Long id);

    List<UpgradeLogVo> upgradeLogList(ListUpgradeLogParam param);

    Long count(ListUpgradeLogParam param);
}

