package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssInOutCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssInOutCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssInOutCfgRwDs {



	@Autowired

	private EssInOutCfgRwMapper essInOutCfgRwMapper;



	public boolean insertEssInOutCfg(EssInOutCfgPo essInOutCfgPo) {

		return this.essInOutCfgRwMapper.insertEssInOutCfg(essInOutCfgPo) > 0;

	}
	public void insertBeforeSelect(Long oldId, Long newId) {

		 this.essInOutCfgRwMapper.insertBeforeSelect(oldId,newId);

	}



	public boolean updateEssInOutCfg(EssInOutCfgPo essInOutCfgPo) {

		return this.essInOutCfgRwMapper.updateEssInOutCfg(essInOutCfgPo) > 0;

	}





}

