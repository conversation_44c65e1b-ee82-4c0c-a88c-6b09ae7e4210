package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SiteTopologyRefRwMapper {
	SiteTopologyRefPo getById(@Param("id") Integer id, @Param("lock") boolean lock);

	int insertSiteTopologyRef(SiteTopologyRefPo siteTopologyRefPo);

	int updateSiteTopologyRef(SiteTopologyRefPo siteTopologyRefPo);


	int deleteAllDown(@Param("upId") Long upId);

	int batchInsertSiteTopologyRef(@Param("list") List<SiteTopologyRefPo> list);
}
