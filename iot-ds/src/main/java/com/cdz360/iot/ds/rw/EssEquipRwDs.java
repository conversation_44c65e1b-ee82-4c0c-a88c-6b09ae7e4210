package com.cdz360.iot.ds.rw;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.mapper.EssEquipRwMapper;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EquipNameplateInfo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssEquipRwDs {

    @Autowired
    private EssEquipRwMapper essEquipRwMapper;

    public EssEquipPo getById(Long id, boolean lock) {
        return this.essEquipRwMapper.getById(id, lock);
    }

    public boolean insertEssEquip(EssEquipPo essEquipPo) {
        return this.essEquipRwMapper.insertEssEquip(essEquipPo) > 0;
    }

    public boolean updateEssEquip(EssEquipPo essEquipPo) {
        return this.essEquipRwMapper.updateEssEquip(essEquipPo) > 0;
    }

    public boolean batchUpset(List<EssEquipPo> equipPoList) {
        return this.essEquipRwMapper.batchUpset(equipPoList) > 0;
    }

    public boolean offEquip(String essDno, List<Long> onlineEquipIdList) {
        IotAssert.isTrue(essDno != null && CollectionUtils.isNotEmpty(onlineEquipIdList),
            "参数错误");
        return this.essEquipRwMapper.offEquip(essDno, onlineEquipIdList, EquipStatus.OFF) > 0;
    }

    public boolean updateEssEquipStatus(String essDno, List<Long> equipIdList, EquipStatus status,
        EquipAlertStatus alertStatus) {
        IotAssert.isTrue(status != null || alertStatus != null, "不能同时为null");
        IotAssert.isNotNull(essDno, "essDno不能为null");
        return this.essEquipRwMapper.updateEssEquipStatus(essDno, equipIdList, status, alertStatus)
            > 0;
    }

    public boolean offlineByEssDno(String essDno) {
        IotAssert.isNotNull(essDno, "essDno不能为null");
        return this.essEquipRwMapper.offlineByEssDno(essDno) > 0;
    }

    public boolean updateEssEquipNameplate(
        String essDno, Long equipId, EquipNameplateInfo nameplate) {
        return this.essEquipRwMapper.updateEssEquipNameplate(
            essDno, equipId, nameplate) > 0;
    }

    public boolean enableByEssDno(String essDno, Boolean enable) {
        return this.enableByEssDno(essDno, enable, null);
    }

    public boolean enableByEssDno(String essDno, Boolean enable, List<String> excludeDnoList) {
        return this.essEquipRwMapper.enableByEssDno(
            essDno, enable, excludeDnoList) > 0;
    }
}

