<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.CameraAccountRoMapper">

	<resultMap id="RESULT_CAMERAACCOUNT_PO" type="com.cdz360.iot.model.camera.po.CameraAccountPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="commId" jdbcType="BIGINT" property="commId" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="clientId" jdbcType="VARCHAR" property="clientId" />
		<result column="clientSecret" jdbcType="VARCHAR" property="clientSecret" />
		<result column="grantType" jdbcType="VARCHAR" property="grantType" />
		<result column="scope" jdbcType="VARCHAR" property="scope" />
		<result column="accessToken" jdbcType="VARCHAR" property="accessToken" />
		<result column="tokenType" jdbcType="VARCHAR" property="tokenType" />
		<result column="expiresIn" jdbcType="TIMESTAMP" property="expiresIn" />
		<result column="accountToken" jdbcType="VARCHAR" property="accountToken" />
		<result column="accountAppKey" jdbcType="VARCHAR" property="accountAppKey" />
		<result column="accountExpireTime" jdbcType="TIMESTAMP" property="accountExpireTime" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_CAMERAACCOUNT_PO">	
		select * from t_camera_account where id = #{id}
	</select>
	<select id="getAll"
			resultMap="RESULT_CAMERAACCOUNT_PO">
		select * from t_camera_account
		<where>
			enable = 1
		</where>
	</select>

	<select id="getAccessTokenExpireByTime"
			resultMap="RESULT_CAMERAACCOUNT_PO">
		select * from t_camera_account
		<where>
			`enable` = true
			and type = #{type}
			and
			(
			expiresIn <![CDATA[ <= ]]> #{expire}
			or expiresIn is null
			)
		</where>
	</select>

	<select id="getAccountExpireByTime"
			resultMap="RESULT_CAMERAACCOUNT_PO">
		select * from t_camera_account
		<where>
			`enable` = true
			<if test="type != null">
				and type = #{type}
			</if>
			and
			(
			accountExpireTime <![CDATA[ <= ]]> #{expire}
			or accountExpireTime is null
			)
		</where>
	</select>

</mapper>
