package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseInfoDto;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.evse.vo.EvseStatusPowerBiVo;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname EvseMapper
 * @Description TODO
 * @Date 9/19/2019 11:14 AM
 * @Created by Rafael
 */
@Mapper
public interface EvseRoMapper {
    EvsePo getEvse(@Param("evseNo") String evseNo);
    List<String> getEvseModelOrFirm(@Param("type") String type);

    EvseInfoDto getEvseInfo(@Param("evseNo") String evseNo);

    List<EvseInfoDto> getEvseInfoList(ListEvseParam param);

    List<EvsePo> getEvseList(ListEvseParam param);

    List<EvseTinyDto> getEvseTinyList(EvseTinyParam param);

    List<EvseModelVo> getEvseModelVoList(ListEvseParam param);

    List<EvsePo> getEvseListForTopology(ListEvseParam param);

    /**
     * @Description: 获取evseIds的桩信息列表
     * @Author: JLei
     * @CreateDate: 10:13 2019/11/5
     */
    List<EvsePo> selectByEvseIds(@Param("evseNoList") List<String> evseIds);

    List<EvsePo> selectBindInTransformerByEvseIds(@Param("evseNoList") List<String> evseIds);

    List<EvsePlugRecordPo> getEvseRecordInfo(@Param("siteId") String siteId);

    List<EvsePlugRecordPo> getUpgradeCleaningEvseInfo();

    List<EvseStatusPowerBiVo> getEvseStatusPowerBi(@Param("provinceCode") String provinceCode,
                                                   @Param("cityCode") String cityCode,
                                                   @Param("siteId") String siteId,
                                                   @Param("commIdChain") String commIdChain);

    Long getTotalPower(SiteAndPlugBiParam param);

    Integer getEvseCount(SiteAndPlugBiParam param);

    EvsePo getEvseById(@Param("id") Long id);

    Long count(@Param("evseNo") String evseNo);

    List<String> getNeedCheckCfgEvse(@Param("topCommId") long topCommId,
                                     @Param("start") long start,
                                     @Param("size") int size);

    List<String> findByModelId(@Param("modelId") long modelId,
                               @Param("start") long start,
                               @Param("size") int size);

    EvsePo getByIccid(@Param("iccid") String iccid);

    List<String> getFirmwareVerList(BaseListParam param);

    List<EvseInfoDto> getEvseSimVo(@Param("iccids") List<String> iccids);

}