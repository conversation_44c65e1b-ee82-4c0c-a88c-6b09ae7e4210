package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseMeterRwMapper;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EvseMeterRwDs {

	@Autowired
	private EvseMeterRwMapper evseMeterRwMapper;

	public DeviceMeterPo getById(Long id, boolean lock) {
		return this.evseMeterRwMapper.getById(id, lock);
	}

	public boolean insertEvseMeter(DeviceMeterPo deviceMeterPo) {
		return this.evseMeterRwMapper.insertEvseMeter(deviceMeterPo) > 0;
	}

	public boolean updateEvseMeter(DeviceMeterPo deviceMeterPo) {
		return this.evseMeterRwMapper.updateEvseMeter(deviceMeterPo) > 0;
	}

	public int batchInsert(List<DeviceMeterPo> deviceMeterPoList) {
		return this.evseMeterRwMapper.batchInsert(deviceMeterPoList);
	}

	public int deleteEvseMeter(Long meterId) {
		return this.evseMeterRwMapper.deleteEvseMeter(meterId);
	}


}
