package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.UserDeviceRefRoMapper;
import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserDeviceRefRoDs {

    @Autowired
    private UserDeviceRefRoMapper userDeviceRefRoMapper;

    public UserDeviceRefPo getById(Long id) {
        return this.userDeviceRefRoMapper.getById(id);
    }

    public List<UserDeviceRefPo> getByUidAndMaster(Long uid, Boolean master) {
        return this.userDeviceRefRoMapper.getByUidAndMaster(uid, master);
    }

    public UserDeviceRefPo getMasterByDno(String dno) {
        return this.userDeviceRefRoMapper.getMasterByDno(dno,null);
    }

    public UserDeviceRefPo getOneValidByDno(String dno) {
        return this.userDeviceRefRoMapper.getOneByUidAndDnoAndEnable(null, dno, true);
    }

    public UserDeviceRefPo getOneByUidAndDno(Long uid, String dno) {
        return this.userDeviceRefRoMapper.getOneByUidAndDnoAndEnable(uid, dno, null);
    }

    public UserDeviceRefPo getOneValidByUidAndDno(Long uid, String dno) {
        return this.userDeviceRefRoMapper.getOneByUidAndDnoAndEnable(uid, dno, true);
    }

    public UserDeviceRefPo getOneByUidAndDnoAndEnable(Long uid, String dno, Boolean enable) {
        return this.userDeviceRefRoMapper.getOneByUidAndDnoAndEnable(uid, dno, enable);
    }
}

