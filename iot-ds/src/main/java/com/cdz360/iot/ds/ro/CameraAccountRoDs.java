package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.CameraAccountRoMapper;
import com.cdz360.iot.model.camera.po.CameraAccountPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CameraAccountRoDs {

	@Autowired
	private CameraAccountRoMapper cameraAccountRoMapper;

	public CameraAccountPo getById(Long id) {
		return this.cameraAccountRoMapper.getById(id);
	}
	public List<CameraAccountPo> getAll() {
		return this.cameraAccountRoMapper.getAll();
	}
	public List<CameraAccountPo> getAccessTokenExpireByTime(Date expire, Integer type) {
		return this.cameraAccountRoMapper.getAccessTokenExpireByTime(expire, type);
	}
	public List<CameraAccountPo> getAccountExpireByTime(Date expire, Integer type) {
		return this.cameraAccountRoMapper.getAccountExpireByTime(expire, type);
	}
}
