package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.vo.MeterEvseVo;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MeterRoMapper {

	MeterPo getById(@Param("id") Long id);

	MeterPo getByNo(@Param("no") String no);

	MeterPo getByDno(@Param("dno") String dno);

	List<MeterEvseVo> getMeterList(MeterListParam param);

	long getMeterListTotal(MeterListParam param);

	List<MeterPo> getAllOnline();

	List<MeterPo> getAllBindMeter();

	List<MeterPo> getBindInTransformerByMeterId(@Param("ids") List<Long> ids);

	List<MeterPo> getByGwno(@Param("gwno") String gwno);

	List<MeterVo> getMeterVoList(MeterListParam param);

	List<MeterVo> getMeterVoList2(MeterListParam param);

	long getMeterVoList2Count(MeterListParam param);

    List<String> getSiteIdList(MeterListParam param);

	Long getSiteIdListTotal(MeterListParam param);
}
