<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.PcsRoMapper">


  <resultMap id="RESULT_PCS_VO" type="com.cdz360.iot.model.pcs.vo.PcsVo">
    <result column="gridMode" jdbcType="SMALLINT" property="gridMode"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="RESULT_PCS_PO" type="com.cdz360.iot.model.pcs.po.PcsPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="vendor" jdbcType="VARCHAR" property="vendor"/>
    <result column="syncSysTime" property="syncSysTime"/>
    <result column="essDno" jdbcType="VARCHAR" property="essDno"/>
    <result column="gridMode" jdbcType="SMALLINT" property="gridMode"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="ratedPower" property="ratedPower"/>
    <result column="invQg" property="invQg"/>
    <result column="invPg" property="invPg"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>


  <select id="getByDno"
    resultMap="RESULT_PCS_PO">
    select * from t_pcs pcs
    where pcs.dno = #{pcsDno}
  </select>

  <select id="getPcsListByEssDno"
    resultMap="RESULT_PCS_PO">
    select * from t_pcs pcs
    where pcs.essDno = #{essDno}
    and pcs.`enable` = true
  </select>


  <select id="getPcsList"
    parameterType="com.cdz360.iot.model.ess.param.ListPcsParam"
    resultMap="RESULT_PCS_VO">
    select pcs.*, ess.name essName from t_pcs pcs
    left join t_ess ess on pcs.essDno = ess.dno
    where pcs.enable = true
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( pcsDnoList )">
      and pcs.dno in
      <foreach collection="pcsDnoList" item="pcsDno" open="(" close=")" separator=",">
        #{pcsDno}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( essDnoList )">
      and ess.dno in
      <foreach collection="essDnoList" item="essDno" open="(" close=")" separator=",">
        #{essDno}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and ess.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
  </select>

</mapper>

