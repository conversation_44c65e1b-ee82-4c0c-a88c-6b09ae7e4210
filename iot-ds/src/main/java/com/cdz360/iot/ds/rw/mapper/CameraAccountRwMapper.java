package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.camera.po.CameraAccountPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface CameraAccountRwMapper {
	CameraAccountPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertCameraAccount(CameraAccountPo cameraAccountPo);

	int updateCameraAccount(CameraAccountPo cameraAccountPo);


}
