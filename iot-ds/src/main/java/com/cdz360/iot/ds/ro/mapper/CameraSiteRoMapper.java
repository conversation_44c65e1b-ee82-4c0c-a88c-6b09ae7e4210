package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.camera.dto.CameraSiteDto;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface CameraSiteRoMapper {

	CameraSitePo getById(@Param("id") Long id);

	CameraSitePo getBySiteId(@Param("siteId") String siteId);

	List<CameraSitePo> getStoreList(ListCameraParam param);

	List<CameraSiteDto> getStoreListHasCamera(ListCameraParam param);

    List<CameraSitePo> getAccessTokenExpireByTime(@Param("expire") Date expire, @Param("type") Integer type);
}
