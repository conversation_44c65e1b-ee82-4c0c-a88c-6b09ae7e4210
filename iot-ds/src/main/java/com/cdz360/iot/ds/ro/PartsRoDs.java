package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.PartsRoMapper;
import com.cdz360.iot.model.parts.param.ListPartsParam;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.vo.PartsVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsRoDs {

    @Autowired
    private PartsRoMapper partsRoMapper;

    public PartsPo getByCode(String code) {
        return partsRoMapper.getByCode(code);
    }

    public List<PartsVo> findParts(ListPartsParam param) {
        return partsRoMapper.findParts(param);
    }

    public List<PartsPo> findPartsStatus(List<String> codeList) {
        return partsRoMapper.findPartsStatus(codeList);
    }

    public Long countParts(ListPartsParam param) {
        return partsRoMapper.countParts(param);
    }
}

