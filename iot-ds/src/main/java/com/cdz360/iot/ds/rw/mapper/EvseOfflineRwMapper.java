package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.param.ModifyEvseInfoParam;
import com.cdz360.iot.model.evse.vo.OfflineEvseVo;
import com.cdz360.iot.model.param.OfflineEvseParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvseOfflineRwMapper {

    int batchAddOfflineEvse(@Param("list") List<OfflineEvseVo> list);

    int updateOfflineBatch(ModifyEvseInfoParam param);

    int removeOfflineEvse(@Param(value = "paramList") List<OfflineEvseParam> paramList);

}
