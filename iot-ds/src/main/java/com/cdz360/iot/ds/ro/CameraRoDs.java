package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.CameraRoMapper;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraPo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import com.cdz360.iot.model.camera.vo.OpInitCameraVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CameraRoDs {

	@Autowired
	private CameraRoMapper cameraRoMapper;

	public CameraPo getById(Long id) {
		return this.cameraRoMapper.getById(id);
	}

	public List<CameraVo> getBySiteId(ListCameraParam param) {
		return this.cameraRoMapper.getBySiteId(param);
	}

	public CameraVo getByCameraId(Long cameraId) {
		return this.cameraRoMapper.getByCameraId(cameraId);
	}

	public List<OpInitCameraVo> getOpInitHlsUrl(@Nullable List<Long> cameraIdList, int size) {
		return this.cameraRoMapper.getOpInitHlsUrl(cameraIdList, size);
	}
}
