<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssDailyRoMapper">


  <resultMap id="RESULT_ESSDAILY_PO" type="com.cdz360.iot.model.ess.po.EssDailyPo">

    <id column="id" jdbcType="BIGINT" property="id"/>

    <result column="date" jdbcType="DATE" property="date"/>

    <result column="essId" jdbcType="BIGINT" property="essId"/>

    <result column="dno" jdbcType="VARCHAR" property="dno"/>

    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>

    <result column="totalInKwh" jdbcType="DECIMAL" property="totalInKwh"/>

    <result column="totalAcInKwh" jdbcType="DECIMAL" property="totalAcInKwh"/>

    <result column="totalOutKwh" jdbcType="DECIMAL" property="totalOutKwh"/>

    <result column="totalAcOutKwh" jdbcType="DECIMAL" property="totalAcOutKwh"/>

    <result column="todayInKwh" jdbcType="DECIMAL" property="todayInKwh"/>

    <result column="todayAcInKwh" jdbcType="DECIMAL" property="todayAcInKwh"/>

    <result column="todayOutKwh" jdbcType="DECIMAL" property="todayOutKwh"/>

    <result column="todayAcOutKwh" jdbcType="DECIMAL" property="todayAcOutKwh"/>

    <result column="todayProfit" jdbcType="DECIMAL" property="todayProfit"/>

    <result column="todayInExpend" jdbcType="DECIMAL" property="todayInExpend"/>

    <result column="todayOutIncome" jdbcType="DECIMAL" property="todayOutIncome"/>

    <result column="inPriceId" jdbcType="BIGINT" property="inPriceId"/>

    <result column="outPriceId" jdbcType="BIGINT" property="outPriceId"/>

    <result column="enable" jdbcType="BOOLEAN" property="enable"/>

    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>

    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

  </resultMap>


  <select id="getById"

    resultMap="RESULT_ESSDAILY_PO">
    select * from t_ess_daily where id = #{id}
    and enable = true
  </select>
  <select id="siteRtDataOfDay" resultType="com.cdz360.iot.model.ess.vo.DayEssDataBi">
    select daily.`date` date,
    sum(todayInKwh) inKwh,
    sum(todayOutKwh) outKwh,
    sum(todayOutIncome) outIncome,
    sum(todayInExpend) inExpend,
    sum(todayProfit) profit
    from t_ess_daily daily
    where daily.`date` <![CDATA[ >= ]]> #{fromDate}
    and daily.`date` <![CDATA[ < ]]> #{toDate}
    and daily.siteId = #{siteId}
    and daily.enable = true
    group by daily.`date`
  </select>

  <select id="rtDataOfYesterday"
    parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
    resultType="com.cdz360.iot.model.ess.vo.EssDataBi">
    select
    IFNULL(sum(daily.todayInKwh), 0) inKwh,
    IFNULL(sum(daily.todayInExpend), 0) inExpend,
    IFNULL(sum(daily.todayOutKwh), 0) outKwh,
    IFNULL(sum(daily.todayOutIncome), 0) outIncome,
    IFNULL(sum(daily.todayProfit), 0) profit
    from t_ess_daily daily
    left join t_site site on site.dzId = daily.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where daily.`date` = date_sub(curdate(), interval 1 day)
    and daily.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and daily.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
  </select>
  <select id="rtDataOfMonth"
    parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
    resultType="com.cdz360.iot.model.ess.vo.EssDataBi">
    select sum(daily.todayInKwh) inKwh, sum(daily.todayInExpend) inExpend,
    sum(daily.todayOutKwh) outKwh, sum(daily.todayOutIncome) outIncome,
    sum(daily.todayProfit) profit
    from t_ess_daily daily
    left join t_site site on site.dzId = daily.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where year(daily.`date`) = #{year} and month(daily.`date`) = #{month}
    and daily.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and daily.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
  </select>
  <select id="rtDataOfTotal"
    parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
    resultType="com.cdz360.iot.model.ess.vo.EssDataBi">
    select sum(daily.todayInKwh) inKwh, sum(daily.todayInExpend) inExpend,
    sum(daily.todayOutKwh) outKwh, sum(daily.todayOutIncome) outIncome,
    sum(daily.todayProfit) profit
    from t_ess_daily daily
    left join t_site site on site.dzId = daily.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where daily.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and daily.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
  </select>
  <select id="siteDayOfRangeKwh"
    parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
    resultType="com.cdz360.iot.model.ess.vo.DaySiteEssRtDataBi">
    select daily.`date` date, daily.siteId siteId,
    sum(daily.todayInKwh) inKwh, sum(daily.todayInExpend) inExpend,
    sum(daily.todayOutKwh) outKwh, sum(daily.todayOutIncome) outIncome,
    sum(daily.todayProfit) profit
    from t_ess_daily daily
    left join t_site site on site.dzId = daily.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where
    daily.date <![CDATA[ >= ]]> #{date.startTime}
    and daily.date <![CDATA[ < ]]> #{date.endTime}
    and daily.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dno )">
      and daily.`dno` = #{dno}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and daily.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    group by daily.`date` , daily.siteId
  </select>

  <select id="equipRtDataSample"
    parameterType="com.cdz360.iot.model.pv.param.DataBiParam"
    resultType="com.cdz360.iot.model.ess.vo.EquipSampleData">
    select
    <choose>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@DAY">
        daily.`date` time,
      </when>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@MONTH">
        DATE_FORMAT(any_value(daily.`date`) , '%Y-%m-01 00:00:00') time,
      </when>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@YEAR">
        DATE_FORMAT(any_value(daily.`date`) , '%Y-01-01 00:00:00') time,
      </when>
    </choose>
    sum(daily.todayInKwh) inKwh, sum(daily.todayInExpend) inExpend,
    sum(daily.todayOutKwh) outKwh, sum(daily.todayOutIncome) outIncome,
    sum(daily.todayProfit) profit
    from t_ess_daily daily
    left join t_site site on site.dzId = daily.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where daily.`date` <![CDATA[ >= ]]> #{fromDate}
    and daily.`date` <![CDATA[ < ]]> #{toDate}
    and daily.enable = true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
      and daily.siteId = #{siteId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      and daily.siteId in
      <foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
        #{siteId}
      </foreach>
    </if>
    <choose>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@DAY">
        group by daily.`date`
      </when>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@MONTH">
        group by DATE_FORMAT(daily.`date`, '%Y-%m')
      </when>
      <when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@YEAR">
        group by year(daily.`date`)
      </when>
    </choose>
  </select>
  <select id="commEssMapData"
    parameterType="com.cdz360.iot.model.ess.param.EssMapDataParam"
    resultType="com.cdz360.iot.model.ess.vo.CommEssMapDataVo">
    select
    count(distinct r.siteId) siteCnt,
    sum(ed.totalInKwh) totalDischargeKwh,
    sum(ed.totalOutKwh) totalChargeKwh,
    sum(ed.todayProfit) totalProfit
    from t_r_site_group_site_ref r
    left join t_ess ess on ess.siteId = r.siteId
    left join t_ess_daily ed on ed.essId = ess.id
    where r.gid in
    <foreach collection="gids" item="gid" open="(" close=")" separator=",">
      #{gid}
    </foreach>
  </select>


</mapper>

