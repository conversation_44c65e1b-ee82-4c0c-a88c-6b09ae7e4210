package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.UpgradeTaskRelRwMapper;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskRelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname UpgradeTaskRelRwDs
 * @Description TODO
 * @Date 9/21/2019 2:46 PM
 * @Created by Rafael
 */
@Service
public class UpgradeTaskRelRwDs {
    @Autowired
    private UpgradeTaskRelRwMapper upgradeTaskRelRwMapper;

    public int insert(UpgradeTaskRelVo updateTaskRelVo) {
        return upgradeTaskRelRwMapper.insert(updateTaskRelVo);
    }
}