package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.TransOrderRoMapper;
import com.cdz360.iot.model.parts.po.TransOrderPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransOrderRoDs {

    @Autowired
    private TransOrderRoMapper transOrderRoMapper;

    public TransOrderPo getByOrderNo(String orderNo) {
        return this.transOrderRoMapper.getByOrderNo(orderNo);
    }
}

