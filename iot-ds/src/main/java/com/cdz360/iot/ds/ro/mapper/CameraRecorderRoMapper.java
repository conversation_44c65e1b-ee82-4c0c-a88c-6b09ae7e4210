package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.camera.param.CameraRecorderParam;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.vo.CameraRecorderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface CameraRecorderRoMapper {

	CameraRecorderPo getById(@Param("id") Long id);

    CameraRecorderPo findByDeviceSerial(@Param("deviceSerial") String deviceSerial,
                                        @Param("enable") Boolean enable);

    List<CameraRecorderVo> listRecorder(CameraRecorderParam param);

    long listRecorderTotal(CameraRecorderParam param);

    List<CameraRecorderPo> getByStoreId(@Param("cameraSiteId") Long storeId);

    boolean updateRecorder(@Param("param") CameraRecorderVo param);
}
