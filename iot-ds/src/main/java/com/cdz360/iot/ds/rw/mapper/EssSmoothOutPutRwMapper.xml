<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssSmoothOutPutRwMapper">

    <insert id="insertEssSmoothOutPutCfg" useGeneratedKeys="true" keyProperty="id"

            keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.SmoothOutPutCfgPo">
        	insert into t_ess_smooth_output_cfg (
			`cfgId`,

			`smoothOutputEnable`,

			`monitoringPeriod`,

			`amplitude`,

			`targetPowerRating`)

		values (
			#{cfgId},

			#{smoothOutputEnable},

			#{monitoringPeriod},

			#{amplitude},

			#{targetPowerRating})

		ON DUPLICATE KEY UPDATE

			smoothOutputEnable = #{smoothOutputEnable},

			monitoringPeriod = #{monitoringPeriod},

			amplitude = #{amplitude},

			targetPowerRating = #{targetPowerRating}

    </insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_smooth_output_cfg(cfgId,smoothOutputEnable,monitoringPeriod,amplitude,targetPowerRating)
		select #{newId},smoothOutputEnable,monitoringPeriod,amplitude,targetPowerRating from t_ess_smooth_output_cfg where cfgId = #{oldId};
	</insert>
</mapper>

