package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.park.po.ParkChannelPo;
import com.cdz360.iot.model.park.vo.ParkChannelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper

public interface ParkChannelRoMapper {



	ParkChannelPo getById(@Param("id") Long id);

	List<ParkChannelVo> findSiteChannelList(@Param("siteId") String siteId);

	ParkChannelPo findChannel(ParkChannelPo channelPo);
}

