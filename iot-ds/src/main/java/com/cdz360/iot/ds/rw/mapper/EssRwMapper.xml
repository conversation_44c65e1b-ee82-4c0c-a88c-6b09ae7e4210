<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssRwMapper">
  <insert id="upsetEss" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssPo">
    insert into t_ess (`dno`,
    `name`,
    `sn`,
    <if test="null != serialNo">
      `serialNo`,
    </if>
    <if test="null != vendor">
      `vendor`,
    </if>
    `gwno`,
    `siteId`,
    <if test="null != deviceModel">
      `deviceModel`,
    </if>
    `status`,
    `createTime`,
    `updateTime`)
    values (#{dno},
    #{name},
    #{sn},
    <if test="null != serialNo">
      #{serialNo},
    </if>
    <if test="null != vendor">
      #{vendor},
    </if>
    #{gwno},
    #{siteId},
    <if test="null != deviceModel">
      #{deviceModel},
    </if>
    #{status.code},
    now(),
    now())
    on DUPLICATE key UPDATE
    <if test="name != null">
      name = #{name},
    </if>
    <if test="gwno != null">
      gwno = #{gwno},
    </if>
    <if test="sn != null">
      sn = #{sn},
    </if>
    <if test="status != null">
      status = #{status.code},
    </if>
    <if test="cfgId != null">
      cfgId = #{cfgId},
    </if>
    <if test="cfgStatus != null">
      cfgStatus = #{cfgStatus.code},
    </if>
    <if test="cfgSuccessId != null">
      cfgSuccessId = #{cfgSuccessId},
    </if>
    <if test="cfgSuccessTime != null">
      cfgSuccessTime = #{cfgSuccessTime},
    </if>
    <if test="null != deviceModel">
      deviceModel = #{deviceModel},
    </if>
    <if test="null != softVersion">
      softVersion = #{softVersion},
    </if>
    <if test="null != otherVersion">
      otherVersion = #{otherVersion},
    </if>
    updateTime = now()
  </insert>
  <insert id="batchUpsetEss">
    insert into t_ess
    (`name`,`dno`, `vendor`, `gwno`, `siteId`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.name},#{po.dno}, #{po.vendor}, #{po.gwno}, #{po.siteId}, now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    `name` = values(name),
    `vendor` = values(vendor),
    updateTime=now()
  </insert>

  <update id="offlineEss">
    update t_ess set
    status = 99
    where gwno = #{gwno}
    <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( remainDnoList )">
      and dno not in
      <foreach collection="remainDnoList" open="(" close=")"
        separator="," item="dno">
        #{dno}
      </foreach>
    </if>
  </update>
  <update id="updateUserEss"
    parameterType="com.cdz360.iot.model.ess.po.EssPo">
    update t_ess
    <set>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( serialNo )">
        serialNo = #{serialNo},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( softVersion )">
        softVersion = #{softVersion},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( otherVersion )">
        otherVersion = #{otherVersion},
      </if>
      updateTime =now()
    </set>
    where dno = #{dno}
  </update>


  <update id="updateEss" parameterType="com.cdz360.iot.model.ess.po.EssPo">
    update t_ess set
    <if test="status != null">
      status = #{status.code},
    </if>
    <if test="cfgId != null">
      cfgId = #{cfgId},
    </if>
    <if test="deliverCfgId != null">
      deliverCfgId = #{deliverCfgId},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="cfgStatus != null">
      cfgStatus = #{cfgStatus.code},
    </if>
    <if test="cfgStatus != null">
      cfgStatus = #{cfgStatus.code},
    </if>
    <if test="cfgStatus != null">
      cfgStatus = #{cfgStatus.code},
    </if>
    <if test="actualUpgradeLogId != null">
      actualUpgradeLogId = #{actualUpgradeLogId},
    </if>
    <if test="expectUpgradeLogId != null">
      expectUpgradeLogId = #{expectUpgradeLogId},
    </if>
    <if test="countryCode != null">
      countryCode = #{countryCode},
    </if>
    <if test="timeZone != null">
      timeZone = #{timeZone},
    </if>
    updateTime = now()
    where dno=#{dno}
  </update>

  <update id="updateStatus">
    update t_ess set
    status=#{status.code}
    where dno=#{dno}
  </update>
  <update id="updateAlertStatus">
    update t_ess set
    alertStatus=#{status.code}
    where dno=#{dno}
  </update>
</mapper>

