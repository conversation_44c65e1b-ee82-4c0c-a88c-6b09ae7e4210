package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.topology.vo.SiteTopologyRefVo;
import com.cdz360.iot.model.type.TopologyType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SiteTopologyRefRoMapper {

	SiteTopologyRefPo getById(@Param("id") Long id);

    List<SiteTopologyRefPo> getByUpId(@Param("upId") Long id);

    SiteTopologyRefPo getBindRefByDownIdAndType(@Param("downId") Long downId,
                                                      @Param("downType") TopologyType downType);

    List<SiteTopologyRefVo> getEvseMeterTopology(@Param("meterId") Long meterId);

    List<SiteTopologyRefPo> getByUpIdList(@Param("upType")TopologyType upType, @Param("upIdList")List<Long> upIdList);

    List<SiteTopologyRefPo> getByDownIdList(@Param("downType")TopologyType downType, @Param("downIdList")List<Long> downIdList);
}
