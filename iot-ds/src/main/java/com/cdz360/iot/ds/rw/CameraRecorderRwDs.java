package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.CameraRecorderRwMapper;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class CameraRecorderRwDs {

	@Autowired
	private CameraRecorderRwMapper cameraRecorderRwMapper;

	public CameraRecorderPo getById(Long id, boolean lock) {
		return this.cameraRecorderRwMapper.getById(id, lock);
	}

	public boolean insertCameraRecorder(CameraRecorderPo cameraRecorderPo) {
		return this.cameraRecorderRwMapper.insertCameraRecorder(cameraRecorderPo) > 0;
	}

	public boolean updateCameraRecorder(CameraRecorderPo cameraRecorderPo) {
		return this.cameraRecorderRwMapper.updateCameraRecorder(cameraRecorderPo) > 0;
	}

	public boolean disableById(Long id) {
		return this.cameraRecorderRwMapper.disableById(id) > 0;
	}
}
