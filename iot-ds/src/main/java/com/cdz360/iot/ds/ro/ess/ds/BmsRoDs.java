package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.ds.ro.ess.mapper.BmsRoMapper;
import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.ess.param.ListBmsParam;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BmsRoDs {


    @Autowired
    private BmsRoMapper mapper;

    public BmsPo getByDno(String bmsDno) {
        return this.mapper.getByDno(bmsDno);
    }

    public List<BmsPo> getBmsListByEssDno(String essDno) {
        return mapper.getBmsListByEssDno(essDno);
    }

    public List<BmsPo> getBmsList(ListBmsParam paramIn) {
        return mapper.getBmsList(paramIn);
    }
}
