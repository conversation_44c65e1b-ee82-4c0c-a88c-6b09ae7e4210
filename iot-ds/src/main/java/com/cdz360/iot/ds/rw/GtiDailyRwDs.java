package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.GtiDailyRwMapper;
import com.cdz360.iot.model.pv.po.GtiDailyPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class GtiDailyRwDs {

	@Autowired
	private GtiDailyRwMapper gtiDailyRwMapper;

	public GtiDailyPo getById(Long id, boolean lock) {
		return this.gtiDailyRwMapper.getById(id, lock);
	}

	public boolean upsetGtiDaily(GtiDailyPo gtiDailyPo) {
		return this.gtiDailyRwMapper.upsetGtiDaily(gtiDailyPo) > 0;
	}

	public int batchUpsetGtiDaily(List<GtiDailyPo> gtiDailyPoList) {
		if (CollectionUtils.isEmpty(gtiDailyPoList)) {
			return 0;
		}
		int result = this.gtiDailyRwMapper.batchUpsetGtiDaily(gtiDailyPoList);
		return result;
	}
}

