<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssInOutCfgRwMapper">



	<resultMap id="RESULT_ESSINOUTCFG_PO" type="com.cdz360.iot.model.ess.po.EssInOutCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="coupleMode" property="coupleMode" />

		<result column="demandCtrlEnable" jdbcType="BOOLEAN" property="demandCtrlEnable" />

		<result column="demand" jdbcType="INTEGER" property="demand" />

		<result column="capacity" jdbcType="INTEGER" property="capacity" />

		<result column="inEnable" jdbcType="BOOLEAN" property="inEnable" />

		<result column="inPower" jdbcType="DECIMAL" property="inPower" />

		<result column="inTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="inTime" />

		<result column="inStopSoc" jdbcType="INTEGER" property="inStopSoc" />

		<result column="outEnable" jdbcType="BOOLEAN" property="outEnable" />

		<result column="outStopSoc" jdbcType="INTEGER" property="outStopSoc" />

		<result column="outTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="outTime" />

	</resultMap>



	<insert id="insertEssInOutCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssInOutCfgPo">

		insert into t_ess_in_out_cfg (`cfgId`,

			`coupleMode`,

			`demandCtrlEnable`,

			`demand`,

			`capacity`,

			`inEnable`,

			`inPower`,

			`inTime`,

			`inStopSoc`,

			`outEnable`,

			`outStopSoc`,

			`outTime`)

		values (#{cfgId},

			#{coupleMode},

			#{demandCtrlEnable},

			#{demand},

			#{capacity},

			#{inEnable},

			#{inPower},

			#{inTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			#{inStopSoc},

			#{outEnable},

			#{outStopSoc},

			#{outTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler})

		 on DUPLICATE key UPDATE
		 	coupleMode = #{coupleMode},

			demandCtrlEnable = #{demandCtrlEnable},

			demand = #{demand},

			capacity = #{capacity},

			inEnable = #{inEnable},

			inPower = #{inPower},

			inTime = #{inTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			inStopSoc = #{inStopSoc},

			outEnable = #{outEnable},

			outStopSoc = #{outStopSoc},

			outTime = #{outTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler}

	</insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_in_out_cfg(cfgId,coupleMode,demandCtrlEnable,demand,capacity,inEnable,inPower,inTime,outEnable,outStopSoc,outTime)
		select #{newId},coupleMode,demandCtrlEnable,demand,capacity,inEnable,inPower,inTime,outEnable,outStopSoc,outTime from t_ess_in_out_cfg where cfgId = #{oldId};
	</insert>


</mapper>

