<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseCfgResultRwMapper">

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.evse.po.EvseCfgResultPo">
        insert into t_evse_cfg_result(`evseNo`, priceCodeResult, `expectPriceCode`, actualPriceCode,
        cfgResult, expectCfgCode, `actualCfgCode`,
        `whiteCardResult`, expectWhiteCardCode, actualWhiteCardCode,
        `enable`, createTime, updateTime
        )
        values (#{evseNo}, #{priceCodeResult}, #{expectPriceCode}, #{actualPriceCode},
            #{cfgResult}, #{expectCfgCode}, #{actualCfgCode},
            #{whiteCardResult}, #{expectWhiteCardCode}, #{actualWhiteCardCode},
            #{enable}, now(), now()
        )
        on DUPLICATE key UPDATE
        <if test="priceCodeResult != null">
            `priceCodeResult` = #{priceCodeResult},
        </if>
        <if test="expectPriceCode != null">
            `expectPriceCode` = #{expectPriceCode},
        </if>
        <if test="actualPriceCode != null">
            `actualPriceCode` = #{actualPriceCode},
        </if>
        <if test="priceCodeEffectiveTime != null">
            `priceCodeEffectiveTime` = #{priceCodeEffectiveTime},
        </if>
        <if test="cfgResult != null">
            `cfgResult` = #{cfgResult},
        </if>
        <if test="expectCfgCode != null">
            `expectCfgCode` = #{expectCfgCode},
        </if>
        <if test="actualCfgCode != null">
            `actualCfgCode` = #{actualCfgCode},
        </if>
        <if test="whiteCardResult != null">
            `whiteCardResult` = #{whiteCardResult},
        </if>
        <if test="expectWhiteCardCode != null">
            `expectWhiteCardCode` = #{expectWhiteCardCode},
        </if>
        <if test="actualWhiteCardCode != null">
            `actualWhiteCardCode` = #{actualWhiteCardCode},
        </if>
        <if test="enable != null">
            `enable` = #{enable},
        </if>
        updateTime = now()

    </insert>

    <update id="updateByEvseNo" parameterType="com.cdz360.iot.model.evse.po.EvseCfgResultPo">
        UPDATE
            t_evse_cfg_result
        SET
        <if test="priceCodeResult != null">
            `priceCodeResult` = #{priceCodeResult},
        </if>
        <if test="expectPriceCode != null">
            `expectPriceCode` = #{expectPriceCode},
        </if>
        <if test="actualPriceCode != null">
            `actualPriceCode` = #{actualPriceCode},
        </if>
        <if test="priceCodeEffectiveTime != null">
            `priceCodeEffectiveTime` = #{priceCodeEffectiveTime},
        </if>
        <if test="cfgResult != null">
            `cfgResult` = #{cfgResult},
        </if>
        <if test="expectCfgCode != null">
            `expectCfgCode` = #{expectCfgCode},
        </if>
        <if test="actualCfgCode != null">
            `actualCfgCode` = #{actualCfgCode},
        </if>
        <if test="whiteCardResult != null">
            `whiteCardResult` = #{whiteCardResult},
        </if>
        <if test="expectWhiteCardCode != null">
            `expectWhiteCardCode` = #{expectWhiteCardCode},
        </if>
        <if test="actualWhiteCardCode != null">
            `actualWhiteCardCode` = #{actualWhiteCardCode},
        </if>
        <if test="enable != null">
            `enable` = #{enable},
        </if>
            updateTime = now()
        WHERE
            evseNo = #{evseNo}
    </update>


    <select id="getByEvseNo" resultType="com.cdz360.iot.model.evse.po.EvseCfgResultPo">
        select * from t_evse_cfg_result where evseNo = #{evseNo}
        <if test="lock == true">
            for update
        </if>
    </select>

    <!--获取计费模板下发结果-->
    <select id="getByPriceSchemeId" resultType="com.cdz360.iot.model.evse.vo.EvseCfgResultVo">
        SELECT
        cfg.evseNo,
        cfg.priceCodeResult,
        cfg.expectPriceCode,
        cfg.actualPriceCode,
        evse.siteId,
        site.`name` as siteName
        FROM
        t_evse_cfg_result cfg
        LEFT JOIN t_evse evse ON evse.evseId = cfg.evseNo
        LEFT JOIN t_site site ON evse.siteId = site.dzId
        WHERE
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( priceSchemeIdList )">
            cfg.expectPriceCode in
            <foreach collection="priceSchemeIdList" item="priceSchemeId" open="(" close=")" separator=",">
                #{priceSchemeId}
            </foreach>
            OR cfg.actualPriceCode in
            <foreach collection="priceSchemeIdList" item="priceSchemeId" open="(" close=")" separator=",">
                #{priceSchemeId}
            </foreach>
        </if>
        <if test="lock == true">
            for update
        </if>
    </select>

    <!--获取桩配置下发结果列表-->
    <select id="getEvseCfgResultList" resultType="com.cdz360.iot.model.evse.vo.EvseCfgResultVo">
        SELECT
        cfg.*,
        evse.siteId,
        site.`name` as siteName
        FROM
        t_evse_cfg_result cfg
        LEFT JOIN t_evse evse ON evse.evseId = cfg.evseNo
        LEFT JOIN t_site site ON evse.siteId = site.dzId
        WHERE
        <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
            cfg.evseNo in
            <foreach collection="evseNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="resetBaseCfgAndPriceCfg">
        update
            t_evse_cfg_result
        set
            priceCodeResult = null,
            expectPriceCode = null,
            actualPriceCode = null,
            priceCodeEffectiveTime = null,
            cfgResult = null,
            expectCfgCode = null,
            actualCfgCode = null,
            updateTime = now()
        where
            evseNo = #{evseNo}
    </update>

</mapper>