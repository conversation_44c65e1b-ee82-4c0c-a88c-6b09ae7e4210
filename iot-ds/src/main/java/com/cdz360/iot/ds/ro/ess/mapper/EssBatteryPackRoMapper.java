package com.cdz360.iot.ds.ro.ess.mapper;


import com.cdz360.iot.model.ess.param.ListEssBatteryPackParam;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryPackVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;


@Mapper

public interface EssBatteryPackRoMapper {


    List<EssEquipBatteryPackVo> findBatteryPack(ListEssBatteryPackParam param);

    Long countBatteryPack(ListEssBatteryPackParam param);

    List<EssEquipBatteryPackSimpleVo> findBatteryPackSimpleVoList(
        @NonNull String essDno,
        @Nullable Long stackEquipId,
        @Nullable Long clusterEquipId,
        @Nullable Long start,
        @Nullable Integer size);

}

