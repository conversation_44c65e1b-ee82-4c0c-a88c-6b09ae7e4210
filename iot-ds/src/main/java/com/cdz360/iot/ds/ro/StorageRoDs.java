package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.StorageRoMapper;
import com.cdz360.iot.model.parts.po.StoragePo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class StorageRoDs {


    @Autowired

    private StorageRoMapper storageRoMapper;


    public StoragePo getByCode(String code) {
        return storageRoMapper.getByCode(code);
    }

    public List<StoragePo> getByUid(List<Long> uidList) {
        return storageRoMapper.getByUid(uidList);
    }
}

