package com.cdz360.iot.ds.ro;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.mapper.ParkingLockEventLogRoMapper;
import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ParkingLockEventLogRoDs {

    @Autowired
    private ParkingLockEventLogRoMapper parkingLockEventLogRoMapper;

    public ParkingLockEventLogPo getById(Long id) {
        return this.parkingLockEventLogRoMapper.getById(id);
    }

    public List<ParkingLockEventLogVo> eventLogRecent(Long parkingLockId, Integer limit) {
        if (null == limit) {
            limit = 20;
        }
        return this.parkingLockEventLogRoMapper.eventLogRecent(parkingLockId, limit);
    }

    public List<ParkingLockEventLogPo> latestStatusUpdateTime(List<Long> recIdList) {
        if (CollectionUtils.isEmpty(recIdList)) {
            return List.of();
        }
        return this.parkingLockEventLogRoMapper.latestStatusUpdateTime(recIdList);
    }
}

