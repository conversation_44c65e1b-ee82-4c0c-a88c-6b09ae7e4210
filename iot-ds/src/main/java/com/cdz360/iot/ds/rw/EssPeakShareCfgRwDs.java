package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssPeakShareCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssPeakShareCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssPeakShareCfgRwDs {



	@Autowired

	private EssPeakShareCfgRwMapper essPeakShareCfgRwMapper;



	public boolean insertEssPeakShareCfg(EssPeakShareCfgPo essPeakShareCfgPo) {

		return this.essPeakShareCfgRwMapper.insertEssPeakShareCfg(essPeakShareCfgPo) > 0;

	}

	public void insertBeforeSelect(Long oldId, Long newId) {

		this.essPeakShareCfgRwMapper.insertBeforeSelect(oldId,newId);

	}



	public boolean updateEssPeakShareCfg(EssPeakShareCfgPo essPeakShareCfgPo) {

		return this.essPeakShareCfgRwMapper.updateEssPeakShareCfg(essPeakShareCfgPo) > 0;

	}





}

