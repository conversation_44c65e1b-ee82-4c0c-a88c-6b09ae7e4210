package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseBundleRwMapper;
import com.cdz360.iot.model.evse.EvseBundle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname EvseBundleRwDs
 * @Description TODO
 * @Date 9/21/2019 2:54 PM
 * @Created by Rafael
 */
@Service
public class EvseBundleRwDs {
    @Autowired
    private EvseBundleRwMapper evseBundleRwMapper;

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    public int updateEnableById(Long id) {
        return evseBundleRwMapper.updateEnableById(id);
    }

    /**
     * 物理删除
     *
     * @param id
     * @return
     */
    public int deleteById(Long id) {
        return evseBundleRwMapper.deleteById(id);
    }

    public int insert(EvseBundle evseBundle) {
        return evseBundleRwMapper.insert(evseBundle);
    }

    public int insertSelective(EvseBundle evseBundle) {
        return evseBundleRwMapper.insertSelective(evseBundle);
    }

    public int updateByPrimaryKeySelective(EvseBundle evseBundle) {
        return evseBundleRwMapper.updateByPrimaryKeySelective(evseBundle);
    }

    public int updateByPrimaryKey(EvseBundle evseBundle) {
        return evseBundleRwMapper.updateByPrimaryKey(evseBundle);
    }
}