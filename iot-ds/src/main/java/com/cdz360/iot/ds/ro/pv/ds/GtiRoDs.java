package com.cdz360.iot.ds.ro.pv.ds;


import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.pv.mapper.GtiRoMapper;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.GtiStatusBi;
import com.cdz360.iot.model.pv.vo.GtiVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class GtiRoDs {



	@Autowired

	private GtiRoMapper gtiRoMapper;



	public GtiPo getByGwnoAndDno(String gwno, String dno) {

		return this.gtiRoMapper.getByGwnoAndDno(gwno, dno);

	}

	public GtiPo getByDno(String dno) {

		return this.gtiRoMapper.getByDno(dno);

	}

	public List<GtiVo> findGtiList(ListGtiParam param) {
		return this.gtiRoMapper.findGtiList(param);
	}

	public Long count(ListGtiParam param) {
		return this.gtiRoMapper.count(param);
	}

	public Long getCountByCfgId(Long cfgId) {
		return this.gtiRoMapper.getCountByCfgId(cfgId);
	}

	public GtiPo getByName(Boolean includeDno, List<UpdateCtrlDeviceDto> gtiDtoList) {
		if(CollectionUtils.isEmpty(gtiDtoList)) {
			return null;
		}
		return this.gtiRoMapper.getByName(includeDno, gtiDtoList);
	}

	public List<GtiStatusBi> getGtiStatusBi(String commIdChain,String siteId) {
		List<GtiStatusBi> res = this.gtiRoMapper.getGtiStatusBi(commIdChain,siteId);
		Long abnormalGtiNum = this.gtiRoMapper.getAbnormalGtiNum(commIdChain,siteId);
		res.add(new GtiStatusBi().setStatus(3).setNum(abnormalGtiNum));
		return res;
	}

	public GtiPo getByEssEquipId(Long essEquipId) {
		return this.gtiRoMapper.getByEssEquipId(essEquipId);
	}
	
	public List<GtiPo> getByVendor(GtiVendor vendor, Integer equipType) {
		return this.gtiRoMapper.getByVendor(vendor, equipType);
	}
}

