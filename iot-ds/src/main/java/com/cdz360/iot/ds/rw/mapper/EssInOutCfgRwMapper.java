package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.ess.po.EssInOutCfgPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssInOutCfgRwMapper {

	int insertEssInOutCfg(EssInOutCfgPo essInOutCfgPo);

	int insertBeforeSelect(@Param("oldId") Long oldId, @Param("newId") Long newId);

	int updateEssInOutCfg(EssInOutCfgPo essInOutCfgPo);





}

