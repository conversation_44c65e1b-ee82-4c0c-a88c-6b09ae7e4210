<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.DevCfgRwMapper">

  <resultMap id="RESULT_DEVCFG_PO" type="com.cdz360.iot.model.ess.po.DevCfgPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="ver" jdbcType="BIGINT" property="ver"/>
    <result column="commId" jdbcType="BIGINT" property="commId"/>
    <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="type" property="type"/>
    <result column="opUid" jdbcType="BIGINT" property="opUid"/>
    <result column="opName" jdbcType="VARCHAR" property="opName"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_DEVCFG_PO">
    select * from t_dev_cfg where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertDevCfg" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.DevCfgPo">
    insert into t_dev_cfg (
    `code`,
    `ver`,
    `commId`,
    `siteId`,
    `name`,
    `type`,
    `opUid`,
    `opName`,
    `enable`,
    `createTime`,
    `updateTime`)
    values (
    #{code},
    #{ver},
    #{commId},
    #{siteId},
    #{name},
    #{type},
    #{opUid},
    #{opName},
    #{enable},
    now(),
    now())
  </insert>

  <update id="updateDevCfg" parameterType="com.cdz360.iot.model.ess.po.DevCfgPo">
    update t_dev_cfg set
    <if test="commId != null">
      commId = #{commId},
    </if>
    <if test="siteId != null">
      siteId = #{siteId},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="type != null">
      type = #{type},
    </if>
    <if test="opUid != null">
      opUid = #{opUid},
    </if>
    <if test="opName != null">
      opName = #{opName},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>
</mapper>

