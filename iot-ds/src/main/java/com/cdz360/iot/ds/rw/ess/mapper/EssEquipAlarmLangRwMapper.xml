<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.ess.mapper.EssEquipAlarmLangRwMapper">


  <resultMap id="RESULT_ESS_EQUIP_ALARM_LANG_PO"
    type="com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo">

    <id column="id" jdbcType="BIGINT" property="id"/>

    <result column="equipType" jdbcType="VARCHAR" property="equipType"/>

    <result column="lang" jdbcType="VARCHAR" property="lang"/>

    <result column="code" jdbcType="VARCHAR" property="code"/>

    <result column="name" jdbcType="VARCHAR" property="name"/>

    <result column="level" jdbcType="INTEGER" property="level"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>

    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt"/>

    <result column="enable" jdbcType="BOOLEAN" property="enable"/>

    <result column="createTime" jdbcType="DATE" property="createTime"/>

    <result column="updateTime" jdbcType="DATE" property="updateTime"/>

  </resultMap>


  <select id="getById"

    resultMap="RESULT_ESS_EQUIP_ALARM_LANG_PO">
    select * from t_ess_equip_alarm_lang where id = #{id}

    <if test="lock == true">

      for update

    </if>

  </select>


  <insert id="insertEssEquipAlarmLang" useGeneratedKeys="true" keyProperty="id"

    keyColumn="id" parameterType="com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo">

    insert into t_ess_equip_alarm_lang (`equipType`,
    `lang`,
    `code`,
    `name`,
    `level`,
    `prompt`,
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{equipType},
    #{lang},
    #{code},
    #{name},
    #{level.code},
    #{prompt},
    #{enable},
    now(),
    now())

  </insert>


  <update id="updateEssEquipAlarmLang"
    parameterType="com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo">

    update t_ess_equip_alarm_lang set
    <if test="equipType != null">
      equipType = #{equipType},
    </if>
    <if test="lang != null">
      lang = #{lang},
    </if>
    <if test="code != null">
      code = #{code},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="level != null">
      level = #{level.code},
    </if>
    <if test="prompt != null">
      prompt = #{prompt},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}

  </update>


</mapper>

