package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.ParkDeviceRwMapper;
import com.cdz360.iot.model.park.po.ParkDevicePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class ParkDeviceRwDs {



	@Autowired

	private ParkDeviceRwMapper parkDeviceRwMapper;



	public ParkDevicePo getById(Long id, boolean lock) {

		return this.parkDeviceRwMapper.getById(id, lock);

	}



	public boolean upsetParkDevice(ParkDevicePo parkDevicePo) {

		return this.parkDeviceRwMapper.upsetParkDevice(parkDevicePo) > 0;

	}
}

