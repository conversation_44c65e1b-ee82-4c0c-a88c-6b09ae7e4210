package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface ParkingLockEventLogRoMapper {


    ParkingLockEventLogPo getById(@Param("id") Long id);

    // 指定地锁ID最近记录
    List<ParkingLockEventLogVo> eventLogRecent(
        @Param("parkingLockId") Long parkingLockId, @Param("limit") Integer limit);

    List<ParkingLockEventLogPo> latestStatusUpdateTime(
        @Param("parkingLockIdList") List<Long> parkingLockIdList);
}

