package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.UpgradeLogRoMapper;
import com.cdz360.iot.model.gw.param.ListUpgradeLogParam;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UpgradeLogRoDs {

    @Autowired
    private UpgradeLogRoMapper upgradeLogRoMapper;


    public UpgradeLogPo getById(Long id) {
        return this.upgradeLogRoMapper.getById(id);
    }

    public List<UpgradeLogVo> upgradeLogList(ListUpgradeLogParam param) {
        return this.upgradeLogRoMapper.upgradeLogList(param);
    }

    public Long count(ListUpgradeLogParam param) {
        return this.upgradeLogRoMapper.count(param);
    }
}

