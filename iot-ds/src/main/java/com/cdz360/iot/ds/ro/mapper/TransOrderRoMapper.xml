<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.mapper.TransOrderRoMapper">



	<resultMap id="RESULT_RANSORDER_PO" type="com.cdz360.iot.model.parts.po.TransOrderPo">

		<id column="orderNo" jdbcType="VARCHAR" property="orderNo" />

		<result column="type" property="type" />

		<result column="status" property="status" />

		<result column="fromCode" jdbcType="VARCHAR" property="fromCode" />

		<result column="toCode" jdbcType="VARCHAR" property="toCode" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="createBy" jdbcType="BIGINT" property="createBy" />

	</resultMap>



	<select id="getByOrderNo"

			resultMap="RESULT_RANSORDER_PO">	
		select * from t_trans_order where orderNo = #{orderNo}

	</select>



</mapper>

