package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.ess.po.DevCfgPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper

public interface DevCfgRwMapper {

    DevCfgPo getById(@Param("id") Long id, @Param("lock") boolean lock);


    int insertDevCfg(DevCfgPo devCfgPo);

    int updateDevCfg(DevCfgPo devCfgPo);


}

