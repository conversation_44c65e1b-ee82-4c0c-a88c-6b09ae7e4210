package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.po.EvseModulePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;

@Mapper
public interface EvseModuleRwMapper {

    int insert(EvseModulePo po);

    int updateById(EvseModulePo po);

    int updateModuleTypeAndNumber(EvseModulePo po);

    long count(@NonNull @Param("evseNo") String evseNo,
               @NonNull @Param("deviceName") String deviceName);

}
