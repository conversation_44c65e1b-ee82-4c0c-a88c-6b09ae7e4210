<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.UserDeviceRefRoMapper">

  <resultMap id="RESULT_USERDEVICEREF_PO" type="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="countryCode" jdbcType="VARCHAR" property="countryCode"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="master" jdbcType="BOOLEAN" property="master"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_USERDEVICEREF_PO">
    select * from t_user_device_ref where id = #{id}
  </select>

  <select id="getByUidAndMaster"
    resultMap="RESULT_USERDEVICEREF_PO">
    select * from t_user_device_ref where uid = #{uid}
    <if test="null != master">
      and master = #{master}
    </if>
  </select>

  <select id="getMasterByDno"
    resultMap="RESULT_USERDEVICEREF_PO">
    select * from t_user_device_ref
    <where>
      dno = #{dno} and master = true
      <if test="null != enable">
        and `enable` = #{enable}
      </if>
    </where>
    limit 1
  </select>

  <select id="getOneByUidAndDnoAndEnable"
    resultType="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    select * from t_user_device_ref
    <where>
      dno = #{dno}
      <if test="null != uid">
        and uid = #{uid}
      </if>
      <if test="null != enable">
        and `enable` = #{enable}
      </if>
    </where>
    limit 1
  </select>

</mapper>

