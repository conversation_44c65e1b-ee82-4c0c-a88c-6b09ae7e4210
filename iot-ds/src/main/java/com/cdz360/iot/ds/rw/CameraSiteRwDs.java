package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.CameraSiteRwMapper;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class CameraSiteRwDs {

	@Autowired
	private CameraSiteRwMapper cameraSiteRwMapper;

	public CameraSitePo getById(Long id, boolean lock) {
		return this.cameraSiteRwMapper.getById(id, lock);
	}

	public boolean insertCameraSite(CameraSitePo cameraSitePo) {
		return this.cameraSiteRwMapper.insertCameraSite(cameraSitePo) > 0;
	}

	public boolean updateCameraSite(CameraSitePo cameraSitePo) {
		return this.cameraSiteRwMapper.updateCameraSite(cameraSitePo) > 0;
	}

	public int disableSiteByAccountId(Long accountId) {
		return this.cameraSiteRwMapper.disableSiteByAccountId(accountId);
	}

	public boolean insertOrUpdate(CameraSitePo cameraSitePo) {
		return this.cameraSiteRwMapper.insertOrUpdate(cameraSitePo) > 0;
	}


}
