package com.cdz360.iot.ds;

import com.cdz360.iot.model.site.ctrl.PowerCtrlLmt;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @Classname MybatisJsonArrayTypeHandler
 * @Description
 * @Date 4/28/2020 10:52 AM
 * @Created by Rafael
 */
public class MybatisJsonArrayTypeHandler<T extends List<PowerCtrlLmt>> extends BaseTypeHandler<T> {

    private Class clazz;
    private static final ObjectMapper mapper = new ObjectMapper();

    public MybatisJsonArrayTypeHandler(Class clazz) {
        if (clazz == null) throw new IllegalArgumentException("Type argument cannot be null");
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {

//        String typeName = null;
//        if (parameter instanceof Integer[]) {
//            typeName = TYPE_NAME_INTEGER;
//        } else if (parameter instanceof String[]) {
//            typeName = TYPE_NAME_VARCHAR;
//        } else if (parameter instanceof Boolean[]) {
//            typeName = TYPE_NAME_BOOLEAN;
//        } else if (parameter instanceof Double[]) {
//            typeName = TYPE_NAME_NUMERIC;
//        }

//        if (typeName == null) {
//            throw new TypeException("ArrayTypeHandler parameter typeName error, your type is " + parameter.getClass().getName());
//        }

//        Connection conn = ps.getConnection();
//        Array array = conn.createArrayOf(typeName, parameter);
        ps.setString(i, this.toJson(parameter));

    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        return getArray(rs.getArray(columnName));
        return getArray(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        return getArray(rs.getArray(columnIndex));
        return getArray(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//        return getArray(cs.getArray(columnIndex));
        return getArray(cs.getString(columnIndex));
    }

    private T getArray(String array) {

        if (array == null) {
            return null;
        }

        try {
            T objects = mapper.readValue(array, new TypeReference<T>(){});
            return objects;
        } catch (Exception e) {
        }

        return null;
    }

    private String toJson(List object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

//    private <T> T toObject[](String json, Class<T> clazz) {
//
//        if (json == null || json.length() == 0) {
//            return null;
//        }
//
//        try {
//            return mapper.readValue(json, clazz);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
}