package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.SiteGroupSiteRefRoMapper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SiteGroupSiteRefRoDs {

    @Autowired
    private SiteGroupSiteRefRoMapper mapper;

    public List<String> selectSiteIdByGids(List<String> gis) {
        return mapper.selectSiteIdByGids(gis);
    }
}
