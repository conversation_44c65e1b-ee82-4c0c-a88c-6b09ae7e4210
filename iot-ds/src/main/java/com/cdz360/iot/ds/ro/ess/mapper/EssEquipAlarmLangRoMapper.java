package com.cdz360.iot.ds.ro.ess.mapper;


import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo;
import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssEquipAlarmLangRoMapper {


    EssEquipAlarmLangPo getById(@Param("id") Long id);

    List<EssEquipAlarmLangDto> getAlarmLangDtoList(ListAlarmLangParam param);
}

