package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.GtiRwMapper;
import com.cdz360.iot.model.pv.po.GtiPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GtiRwDs {

    @Autowired
    private GtiRwMapper gtiRwMapper;

    public GtiPo getById(Long id, boolean lock) {
        return this.gtiRwMapper.getById(id, lock);
    }

    public GtiPo getBySiteIdAndGwnoAndSid(String siteId, String gwno, Integer sid, boolean lock) {
        return this.gtiRwMapper.getBySiteIdAndGwnoAndSid(siteId, gwno, sid, lock);
    }

    public boolean upsetGti(GtiPo gtiPo) {
        return this.gtiRwMapper.upsetGti(gtiPo) > 0;
    }

    public int batchUpsetGti(List<GtiPo> gtiPoList) {
        return this.gtiRwMapper.batchUpsetGti(gtiPoList);
    }

    public boolean updateGtiByDno(GtiPo gtiPo) {
        return this.gtiRwMapper.updateGtiByDno(gtiPo) > 0;
    }

    public int offlineGti(@NonNull String gwno, @Nullable List<String> remainDnoList) {
        return this.gtiRwMapper.offlineGti(gwno, remainDnoList);
    }
}

