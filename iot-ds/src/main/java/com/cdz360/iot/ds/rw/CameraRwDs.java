package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.CameraRwMapper;
import com.cdz360.iot.model.camera.po.CameraPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class CameraRwDs {

	@Autowired
	private CameraRwMapper cameraRwMapper;

	public CameraPo getById(Long id, boolean lock) {
		return this.cameraRwMapper.getById(id, lock);
	}

	public boolean insertCamera(CameraPo cameraPo) {
		return this.cameraRwMapper.insertCamera(cameraPo) > 0;
	}

	public boolean updateCamera(CameraPo cameraPo) {
		return this.cameraRwMapper.updateCamera(cameraPo) > 0;
	}


    public int disableSiteByCameraSiteId(Long cameraSiteId) {
		return cameraRwMapper.disableSiteByCameraSiteId(cameraSiteId);
    }

	public boolean insertOrUpdate(CameraPo cameraPo) {
		return cameraRwMapper.insertOrUpdate(cameraPo);
	}

	public boolean flushLiveAddress(CameraPo cameraPo) {
		return cameraRwMapper.flushLiveAddress(cameraPo);
	}

	public boolean flushChannelPicUrl(CameraPo cameraPo) {
		return cameraRwMapper.flushChannelPicUrl(cameraPo);
	}

    public int disableSiteByDeviceSerial(String deviceSerial) {
		return cameraRwMapper.disableSiteByDeviceSerial(deviceSerial);
    }

    public int disableSiteByDeviceId(Long recorderId) {
		return cameraRwMapper.disableSiteByDeviceId(recorderId);
    }

	public boolean updateAspectRatioByRecordId(Long recordId, String aspectRatio) {
		return cameraRwMapper.updateAspectRatioByRecordId(recordId, aspectRatio) > 0;
	}
}
