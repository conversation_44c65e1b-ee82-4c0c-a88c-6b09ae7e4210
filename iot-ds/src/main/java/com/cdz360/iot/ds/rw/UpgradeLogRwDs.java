package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.UpgradeLogRwMapper;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UpgradeLogRwDs {

    @Autowired
    private UpgradeLogRwMapper upgradeLogRwMapper;

    public UpgradeLogPo getById(Long id, boolean lock) {
        return this.upgradeLogRwMapper.getById(id, lock);
    }

    public boolean insertUpgradeLog(UpgradeLogPo upgradeLogPo) {
        return this.upgradeLogRwMapper.insertUpgradeLog(upgradeLogPo) > 0;
    }

    public boolean updateUpgradeLog(UpgradeLogPo upgradeLogPo) {
        return this.upgradeLogRwMapper.updateUpgradeLog(upgradeLogPo) > 0;
    }

}

