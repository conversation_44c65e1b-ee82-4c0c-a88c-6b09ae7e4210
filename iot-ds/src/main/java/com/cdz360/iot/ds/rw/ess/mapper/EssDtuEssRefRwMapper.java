package com.cdz360.iot.ds.rw.ess.mapper;

import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EssDtuEssRefRwMapper {

    EssDtuEssRefPo getBySerialNoAndDno(
        @Param("serialNo") String serialNo, @Param("dno") String dno,
        @Param("lock") boolean lock);

    int insertEssDtuEssRef(EssDtuEssRefPo essDtuEssRefPo);

}

