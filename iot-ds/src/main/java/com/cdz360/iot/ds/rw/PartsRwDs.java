package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.PartsRwMapper;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.type.PartsLocationStatus;
import com.cdz360.iot.model.parts.type.PartsStatus;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class PartsRwDs {

    @Autowired
    private PartsRwMapper partsRwMapper;

    public PartsPo getByCode(String code, boolean lock) {
        return partsRwMapper.getByCode(code, lock);
    }

    public boolean insertParts(PartsPo partsPo) {
        return this.partsRwMapper.insertParts(partsPo) > 0;
    }

    public boolean updateParts(PartsPo partsPo) {
        return this.partsRwMapper.updateParts(partsPo) > 0;
    }

    public int batchInsert(List<PartsPo> partsPoList) {
        return this.partsRwMapper.batchInsert(partsPoList);
    }

    public boolean updatePartsLocationStatus(
        String code, String storageCode,
        PartsLocationStatus fromStatus, PartsLocationStatus toStatus) {
        return this.partsRwMapper.updatePartsLocationStatus(
            code, storageCode, fromStatus, toStatus) > 0;
    }

    public int updatePartsBroken(List<String> codeList) {
        return this.partsRwMapper.updatePartsBroken(codeList);
    }

    public int batchUpdateParts(List<String> codeList, Long typeId, PartsStatus toStatus) {
        return this.partsRwMapper.batchUpdatePartsStatus(codeList, typeId, toStatus);
    }

    public boolean updatePartsTransReview(
        Boolean agree, String code, String storageCode, String remark) {
        return this.partsRwMapper.updatePartsTransReview(agree, code, storageCode, remark) > 0;
    }

    public boolean updatePartsTransApply(String code, Long uid) {
        return this.partsRwMapper.updatePartsTransApply(code, uid) > 0;
    }
}

