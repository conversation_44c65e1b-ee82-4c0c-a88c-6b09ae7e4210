<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.GtiDailyRwMapper">


    <resultMap id="RESULT_GTIDAILY_PO" type="com.cdz360.iot.model.pv.po.GtiDailyPo">

        <id column="id" jdbcType="BIGINT" property="id"/>

        <result column="date" jdbcType="DATE" property="date"/>

        <result column="gtiId" jdbcType="BIGINT" property="gtiId"/>

        <result column="dno" jdbcType="VARCHAR" property="dno"/>

        <result column="siteId" jdbcType="VARCHAR" property="siteId"/>

        <result column="totalKwh" jdbcType="DECIMAL" property="totalKwh"/>

        <result column="totalHour" jdbcType="INTEGER" property="totalHour"/>

        <result column="todayKwh" jdbcType="DECIMAL" property="todayKwh"/>
        <result column="todayProfit" jdbcType="DECIMAL" property="todayProfit"/>

        <result column="enable" jdbcType="BOOLEAN" property="enable"/>

        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>

        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>

    </resultMap>


    <insert id="upsetGtiDaily" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.pv.po.GtiDailyPo">

        insert into t_gti_daily (`date`,

        `gtiId`,

        `dno`,

        `siteId`,

        `totalKwh`,

        `totalHour`,

        `todayKwh`,
        `todayProfit`,
        `profitTempId`,

        `enable`,

        `createTime`,

        `updateTime`)

        values (#{date},

        #{gtiId},

        #{dno},

        #{siteId},

        #{totalKwh},

        #{totalHour},

        #{todayKwh},
        #{todayProfit},
        #{profitTempId},

        true,

        now(),

        now())

        ON DUPLICATE KEY UPDATE
        totalKwh = #{totalKwh},
        totalHour = #{totalHour},
        todayKwh = #{todayKwh},
        <if test="null != todayProfit">
            todayProfit = #{todayProfit},
        </if>
        <if test="null != profitTempId">
            profitTempId = #{profitTempId},
        </if>
        <if test="null != enable">
            `enable` = #{enable},
        </if>
        `updateTime` = now()

    </insert>


    <select id="getById"

            resultMap="RESULT_GTIDAILY_PO">
        select * from t_gti_daily where id = #{id}
        and enable = true
        <if test="lock == true">

            for update

        </if>

    </select>

    <insert id="batchUpsetGtiDaily" parameterType="java.util.List">
        insert into t_gti_daily (`date`,
        `gtiId`,
        `dno`,
        `siteId`,
        `totalKwh`,
        `totalHour`,
        `todayKwh`,
        `todayProfit`,
        `profitTempId`,
        `enable`,
        `createTime`,
        `updateTime`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.date},
            #{item.gtiId},
            #{item.dno},
            #{item.siteId},
            #{item.totalKwh},
            #{item.totalHour},
            #{item.todayKwh},
            #{item.todayProfit},
            #{item.profitTempId},
            true,
            now(),
            now())
        </foreach>
        ON DUPLICATE KEY UPDATE
        totalKwh = VALUES(totalKwh),
        totalHour = VALUES(totalHour),
        todayKwh = VALUES(todayKwh),
        todayProfit = VALUES(todayProfit),
        profitTempId = VALUES(profitTempId),
        `enable` = VALUES(`enable`),
        `updateTime` = now()
    </insert>

</mapper>

