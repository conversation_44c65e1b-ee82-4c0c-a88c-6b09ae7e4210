<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.CommercialRwMapper">

    <update id="syncCommercial" parameterType="com.cdz360.iot.model.site.po.CommercialPo">
        insert into t_r_commercial(id, pid, commLevel,
        topCommId, commName, shortName, idChain, `enable`)
        values( #{id}, #{pid}, #{commLevel},
        #{topCommId}, #{commName}, #{shortName}, #{idChain}, #{enable})
        ON DUPLICATE KEY UPDATE `pid` = #{pid},
        commLevel = #{commLevel},
        topCommId = #{topCommId},
        commName = #{commName},
        `shortName` = #{shortName},
        idChain = #{idChain},
        `enable` = #{enable}
    </update>
</mapper>