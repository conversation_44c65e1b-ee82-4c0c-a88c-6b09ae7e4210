<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.MeterRecordRwMapper">

	<resultMap id="RESULT_METERRECORD_PO" type="com.cdz360.iot.model.meter.po.MeterRecordPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="combineTotal" jdbcType="DECIMAL" property="combineTotal" />
		<result column="combineA" jdbcType="DECIMAL" property="combineA" />
		<result column="combineB" jdbcType="DECIMAL" property="combineB" />
		<result column="combineC" jdbcType="DECIMAL" property="combineC" />
		<result column="combineD" jdbcType="DECIMAL" property="combineD" />
		<result column="positiveTotal" jdbcType="DECIMAL" property="positiveTotal" />
		<result column="positiveA" jdbcType="DECIMAL" property="positiveA" />
		<result column="positiveB" jdbcType="DECIMAL" property="positiveB" />
		<result column="positiveC" jdbcType="DECIMAL" property="positiveC" />
		<result column="positiveD" jdbcType="DECIMAL" property="positiveD" />
		<result column="negativeTotal" jdbcType="DECIMAL" property="negativeTotal" />
		<result column="negativeA" jdbcType="DECIMAL" property="negativeA" />
		<result column="negativeB" jdbcType="DECIMAL" property="negativeB" />
		<result column="negativeC" jdbcType="DECIMAL" property="negativeC" />
		<result column="negativeD" jdbcType="DECIMAL" property="negativeD" />
		<result column="positiveIdleTotal" jdbcType="DECIMAL" property="positiveIdleTotal" />
		<result column="positiveIdleA" jdbcType="DECIMAL" property="positiveIdleA" />
		<result column="positiveIdleB" jdbcType="DECIMAL" property="positiveIdleB" />
		<result column="positiveIdleC" jdbcType="DECIMAL" property="positiveIdleC" />
		<result column="positiveIdleD" jdbcType="DECIMAL" property="positiveIdleD" />
		<result column="negativeIdleTotal" jdbcType="DECIMAL" property="negativeIdleTotal" />
		<result column="negativeIdleA" jdbcType="DECIMAL" property="negativeIdleA" />
		<result column="negativeIdleB" jdbcType="DECIMAL" property="negativeIdleB" />
		<result column="negativeIdleC" jdbcType="DECIMAL" property="negativeIdleC" />
		<result column="negativeIdleD" jdbcType="DECIMAL" property="negativeIdleD" />
		<result column="positiveApparentTotal" jdbcType="DECIMAL" property="positiveApparentTotal" />
		<result column="positiveApparentA" jdbcType="DECIMAL" property="positiveApparentA" />
		<result column="positiveApparentB" jdbcType="DECIMAL" property="positiveApparentB" />
		<result column="positiveApparentC" jdbcType="DECIMAL" property="positiveApparentC" />
		<result column="positiveApparentD" jdbcType="DECIMAL" property="positiveApparentD" />
		<result column="negativeApparentTotal" jdbcType="DECIMAL" property="negativeApparentTotal" />
		<result column="negativeApparentA" jdbcType="DECIMAL" property="negativeApparentA" />
		<result column="negativeApparentB" jdbcType="DECIMAL" property="negativeApparentB" />
		<result column="negativeApparentC" jdbcType="DECIMAL" property="negativeApparentC" />
		<result column="negativeApparentD" jdbcType="DECIMAL" property="negativeApparentD" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_METERRECORD_PO">	
		select * from t_meter_record where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertMeterRecord" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.meter.po.MeterRecordPo">
		insert into t_meter_record (`siteId`,
			`meterId`,
			`readingTime`,
		<if test="combineTotal != null">
			`combineTotal`,
		</if>
		<if test="combineA != null">
			`combineA`,
		</if>
		<if test="combineB != null">
			`combineB`,
		</if>
		<if test="combineC != null">
			`combineC`,
		</if>
		<if test="combineD != null">
			`combineD`,
		</if>
		<if test="positiveTotal != null">
			`positiveTotal`,
		</if>
		<if test="positiveA != null">
			`positiveA`,
		</if>
		<if test="positiveB != null">
			`positiveB`,
		</if>
		<if test="positiveC != null">
			`positiveC`,
		</if>
		<if test="positiveD != null">
			`positiveD`,
		</if>
		<if test="negativeTotal != null">
			`negativeTotal`,
		</if>
		<if test="negativeA != null">
			`negativeA`,
		</if>
		<if test="negativeB != null">
			`negativeB`,
		</if>
		<if test="negativeC != null">
			`negativeC`,
		</if>
		<if test="negativeD != null">
			`negativeD`,
		</if>
		<if test="positiveIdleTotal != null">
			`positiveIdleTotal`,
		</if>
		<if test="positiveIdleA != null">
			`positiveIdleA`,
		</if>
		<if test="positiveIdleB != null">
			`positiveIdleB`,
		</if>
		<if test="positiveIdleC != null">
			`positiveIdleC`,
		</if>
		<if test="positiveIdleD != null">
			`positiveIdleD`,
		</if>
		<if test="negativeIdleTotal != null">
			`negativeIdleTotal`,
		</if>
		<if test="negativeIdleA != null">
			`negativeIdleA`,
		</if>
		<if test="negativeIdleB != null">
			`negativeIdleB`,
		</if>
		<if test="negativeIdleC != null">
			`negativeIdleC`,
		</if>
		<if test="negativeIdleD != null">
			`negativeIdleD`,
		</if>
		<if test="positiveApparentTotal != null">
			`positiveApparentTotal`,
		</if>
		<if test="positiveApparentA != null">
			`positiveApparentA`,
		</if>
		<if test="positiveApparentB != null">
			`positiveApparentB`,
		</if>
		<if test="positiveApparentC != null">
			`positiveApparentC`,
		</if>
		<if test="positiveApparentD != null">
			`positiveApparentD`,
		</if>
		<if test="negativeApparentTotal != null">
			`negativeApparentTotal`,
		</if>
		<if test="negativeApparentA != null">
			`negativeApparentA`,
		</if>
		<if test="negativeApparentB != null">
			`negativeApparentB`,
		</if>
		<if test="negativeApparentC != null">
			`negativeApparentC`,
		</if>
		<if test="negativeApparentD != null">
			`negativeApparentD`,
		</if>
		<if test="currentRate != null">
			`currentRate`,
		</if>
			`createTime`)
		values (#{siteId},
			#{meterId},
			#{readingTime},
		<if test="combineTotal != null">
			#{combineTotal},
		</if>
		<if test="combineA != null">
			#{combineA},
		</if>
		<if test="combineB != null">
			#{combineB},
		</if>
		<if test="combineC != null">
			#{combineC},
		</if>
		<if test="combineD != null">
			#{combineD},
		</if>
		<if test="positiveTotal != null">
			#{positiveTotal},
		</if>
		<if test="positiveA != null">
			#{positiveA},
		</if>
		<if test="positiveB != null">
			#{positiveB},
		</if>
		<if test="positiveC != null">
			#{positiveC},
		</if>
		<if test="positiveD != null">
			#{positiveD},
		</if>
		<if test="negativeTotal != null">
			#{negativeTotal},
		</if>
		<if test="negativeA != null">
			#{negativeA},
		</if>
		<if test="negativeB != null">
			#{negativeB},
		</if>
		<if test="negativeC != null">
			#{negativeC},
		</if>
		<if test="negativeD != null">
			#{negativeD},
		</if>
		<if test="positiveIdleTotal != null">
			#{positiveIdleTotal},
		</if>
		<if test="positiveIdleA != null">
			#{positiveIdleA},
		</if>
		<if test="positiveIdleB != null">
			#{positiveIdleB},
		</if>
		<if test="positiveIdleC != null">
			#{positiveIdleC},
		</if>
		<if test="positiveIdleD != null">
			#{positiveIdleD},
		</if>
		<if test="negativeIdleTotal != null">
			#{negativeIdleTotal},
		</if>
		<if test="negativeIdleA != null">
			#{negativeIdleA},
		</if>
		<if test="negativeIdleB != null">
			#{negativeIdleB},
		</if>
		<if test="negativeIdleC != null">
			#{negativeIdleC},
		</if>
		<if test="negativeIdleD != null">
			#{negativeIdleD},
		</if>
		<if test="positiveApparentTotal != null">
			#{positiveApparentTotal},
		</if>
		<if test="positiveApparentA != null">
			#{positiveApparentA},
		</if>
		<if test="positiveApparentB != null">
			#{positiveApparentB},
		</if>
		<if test="positiveApparentC != null">
			#{positiveApparentC},
		</if>
		<if test="positiveApparentD != null">
			#{positiveApparentD},
		</if>
		<if test="negativeApparentTotal != null">
			#{negativeApparentTotal},
		</if>
		<if test="negativeApparentA != null">
			#{negativeApparentA},
		</if>
		<if test="negativeApparentB != null">
			#{negativeApparentB},
		</if>
		<if test="negativeApparentC != null">
			#{negativeApparentC},
		</if>
		<if test="negativeApparentD != null">
			#{negativeApparentD},
		</if>
		<if test="currentRate != null">
			#{currentRate},
		</if>
			now())
	</insert>

	<update id="updateMeterRecord" parameterType="com.cdz360.iot.model.meter.po.MeterRecordPo">
		update t_meter_record set
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="meterId != null">
			meterId = #{meterId},
		</if>
		<if test="readingTime != null">
			readingTime = #{readingTime},
		</if>
		<if test="combineTotal != null">
			combineTotal = #{combineTotal},
		</if>
		<if test="combineA != null">
			combineA = #{combineA},
		</if>
		<if test="combineB != null">
			combineB = #{combineB},
		</if>
		<if test="combineC != null">
			combineC = #{combineC},
		</if>
		<if test="combineD != null">
			combineD = #{combineD},
		</if>
		<if test="positiveTotal != null">
			positiveTotal = #{positiveTotal},
		</if>
		<if test="positiveA != null">
			positiveA = #{positiveA},
		</if>
		<if test="positiveB != null">
			positiveB = #{positiveB},
		</if>
		<if test="positiveC != null">
			positiveC = #{positiveC},
		</if>
		<if test="positiveD != null">
			positiveD = #{positiveD},
		</if>
		<if test="negativeTotal != null">
			negativeTotal = #{negativeTotal},
		</if>
		<if test="negativeA != null">
			negativeA = #{negativeA},
		</if>
		<if test="negativeB != null">
			negativeB = #{negativeB},
		</if>
		<if test="negativeC != null">
			negativeC = #{negativeC},
		</if>
		<if test="negativeD != null">
			negativeD = #{negativeD},
		</if>
		<if test="positiveIdleTotal != null">
			positiveIdleTotal = #{positiveIdleTotal},
		</if>
		<if test="positiveIdleA != null">
			positiveIdleA = #{positiveIdleA},
		</if>
		<if test="positiveIdleB != null">
			positiveIdleB = #{positiveIdleB},
		</if>
		<if test="positiveIdleC != null">
			positiveIdleC = #{positiveIdleC},
		</if>
		<if test="positiveIdleD != null">
			positiveIdleD = #{positiveIdleD},
		</if>
		<if test="negativeIdleTotal != null">
			negativeIdleTotal = #{negativeIdleTotal},
		</if>
		<if test="negativeIdleA != null">
			negativeIdleA = #{negativeIdleA},
		</if>
		<if test="negativeIdleB != null">
			negativeIdleB = #{negativeIdleB},
		</if>
		<if test="negativeIdleC != null">
			negativeIdleC = #{negativeIdleC},
		</if>
		<if test="negativeIdleD != null">
			negativeIdleD = #{negativeIdleD},
		</if>
		<if test="positiveApparentTotal != null">
			positiveApparentTotal = #{positiveApparentTotal},
		</if>
		<if test="positiveApparentA != null">
			positiveApparentA = #{positiveApparentA},
		</if>
		<if test="positiveApparentB != null">
			positiveApparentB = #{positiveApparentB},
		</if>
		<if test="positiveApparentC != null">
			positiveApparentC = #{positiveApparentC},
		</if>
		<if test="positiveApparentD != null">
			positiveApparentD = #{positiveApparentD},
		</if>
		<if test="negativeApparentTotal != null">
			negativeApparentTotal = #{negativeApparentTotal},
		</if>
		<if test="negativeApparentA != null">
			negativeApparentA = #{negativeApparentA},
		</if>
		<if test="negativeApparentB != null">
			negativeApparentB = #{negativeApparentB},
		</if>
		<if test="negativeApparentC != null">
			negativeApparentC = #{negativeApparentC},
		</if>
		<if test="negativeApparentD != null">
			negativeApparentD = #{negativeApparentD},
		</if>
		<if test="currentRate != null">
			currentRate = #{currentRate},
		</if>
		where id = #{id}
	</update>

</mapper>
