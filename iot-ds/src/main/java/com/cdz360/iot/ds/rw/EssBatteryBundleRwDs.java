package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EssBatteryBundleRwMapper;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.meter.po.MeterPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssBatteryBundleRwDs {

    @Autowired
    private EssBatteryBundleRwMapper essBatteryClusterRwMapper;

    public boolean upsetEssBatteryCluster(EssBatteryBundlePo essBatteryClusterPo) {
        return this.essBatteryClusterRwMapper.upsetEssBatteryCluster(essBatteryClusterPo) > 0;
    }

    public int batchInsertBundle(List<EssBatteryBundlePo> bundlePoList) {

        return this.essBatteryClusterRwMapper.batchInsertBundle(bundlePoList);

    }

}

