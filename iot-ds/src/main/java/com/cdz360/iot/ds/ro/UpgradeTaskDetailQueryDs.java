package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.UpgradeTaskDetailQueryMapper;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname UpgradeTaskDetailQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:17 PM
 * @Created by Rafael
 */
@Service
public class UpgradeTaskDetailQueryDs {
    @Autowired
    private UpgradeTaskDetailQueryMapper upgradeTaskDetailQueryMapper;

    public List<UpgradeTaskDetailVo> select(Long taskId,
                                            String evseId,
                                            List<UpdateTaskStatusEnum> status) {
        return upgradeTaskDetailQueryMapper.select(taskId, evseId, status);
    }

    public long countUpdatingEvses(UpdateTaskStatusEnum status, Long bundleId) {
        return upgradeTaskDetailQueryMapper.countUpdatingEvses(status, bundleId);
    }

    ;
}