package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.CameraRecorderRoMapper;
import com.cdz360.iot.model.camera.param.CameraRecorderParam;
import com.cdz360.iot.model.camera.po.CameraRecorderPo;
import com.cdz360.iot.model.camera.vo.CameraRecorderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CameraRecorderRoDs {

	@Autowired
	private CameraRecorderRoMapper cameraRecorderRoMapper;

	public CameraRecorderPo getById(Long id) {
		return this.cameraRecorderRoMapper.getById(id);
	}

	public CameraRecorderPo findByDeviceSerial(String deviceSerial, boolean enable) {
		return this.cameraRecorderRoMapper.findByDeviceSerial(deviceSerial, enable);
	}

	public List<CameraRecorderVo> listRecorder(CameraRecorderParam param) {
		return this.cameraRecorderRoMapper.listRecorder(param);
	}

	public long listRecorderTotal(CameraRecorderParam param) {
		return this.cameraRecorderRoMapper.listRecorderTotal(param);
	}

	public List<CameraRecorderPo> getByStoreId(Long storeId) {
		return this.cameraRecorderRoMapper.getByStoreId(storeId);
	}

	public boolean updateRecorder(CameraRecorderVo param) {
		return this.cameraRecorderRoMapper.updateRecorder(param);
	}
}
