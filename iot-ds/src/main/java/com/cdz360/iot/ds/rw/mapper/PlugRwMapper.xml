<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.PlugRwMapper">


    <update id="updatePlug" parameterType="com.cdz360.iot.model.evse.po.PlugPo">
        update t_plug set
        <if test="name != null">
        `name` = #{name},
        </if>
        updateTime = now()
        where evseId = #{evseId} and plugId = #{plugId}
    </update>
</mapper>