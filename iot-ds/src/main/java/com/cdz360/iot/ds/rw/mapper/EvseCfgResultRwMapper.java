package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.param.ListEvseCfgResultParam;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.vo.EvseCfgResultVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EvseCfgResultRwMapper {

    void insertOrUpdate(EvseCfgResultPo evseCfgResult);

    int updateByEvseNo(EvseCfgResultPo evseCfgResult);

    EvseCfgResultPo getByEvseNo(@Param("evseNo") String evseNo, @Param("lock") boolean lock);

    List<EvseCfgResultVo> getByPriceSchemeId(
            @Param("priceSchemeIdList") List<Long> priceSchemeIdList, @Param("lock") boolean lock);

    List<EvseCfgResultVo> getEvseCfgResultList(ListEvseCfgResultParam param);

    int resetBaseCfgAndPriceCfg(@Param("evseNo") String evseNo);

}
