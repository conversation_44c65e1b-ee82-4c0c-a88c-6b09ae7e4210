package com.cdz360.iot.ds.rw.ess.ds;

import com.cdz360.iot.ds.rw.mapper.PcsRwMapper;
import com.cdz360.iot.model.pcs.po.PcsPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PcsRwDs {

    @Autowired
    private PcsRwMapper pcsRwMapper;

    public int updatePcs(PcsPo pcs) {
        return this.pcsRwMapper.updatePcs(pcs);
    }

    public int batchUpsetPcs(List<PcsPo> pcsPoList) {
        return this.pcsRwMapper.batchUpsetPcs(pcsPoList);
    }

    public int offlinePcs(@NonNull String essDno, @Nullable List<String> remainDnoList) {
        return pcsRwMapper.offlinePcs(essDno, remainDnoList);
    }

}
