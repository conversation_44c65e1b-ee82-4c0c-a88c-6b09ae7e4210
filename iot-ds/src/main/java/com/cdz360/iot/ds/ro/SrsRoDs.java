package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.SrsRoMapper;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.srs.po.SrsPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class SrsRoDs {

    @Autowired
    private SrsRoMapper srsRoMapper;

    public List<SrsPo> getByGwno(ListCtrlParam param) {
        return this.srsRoMapper.getByGwno(param);
    }

    public SrsPo getOneByGwno(String gwno) {
        return this.srsRoMapper.getOneByGwno(gwno);
    }

    public SrsPo getByDno(String dno) {
        return this.srsRoMapper.getByDno(dno);
    }

}
