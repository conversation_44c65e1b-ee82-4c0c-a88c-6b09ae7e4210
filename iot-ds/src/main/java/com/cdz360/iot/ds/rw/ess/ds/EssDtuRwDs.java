package com.cdz360.iot.ds.rw.ess.ds;

import com.cdz360.iot.ds.rw.ess.mapper.EssDtuRwMapper;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDtuRwDs {

    @Autowired
    private EssDtuRwMapper essDtuRwMapper;

    public EssDtuPo getBySerialNo(String serialNo, boolean lock) {
        return this.essDtuRwMapper.getBySerialNo(serialNo, lock);
    }

    public boolean insertEssDtu(EssDtuPo essDtuPo) {
        return this.essDtuRwMapper.insertEssDtu(essDtuPo) > 0;
    }

    public boolean updateEssDtu(EssDtuPo essDtuPo) {
        return this.essDtuRwMapper.updateEssDtu(essDtuPo) > 0;
    }

}

