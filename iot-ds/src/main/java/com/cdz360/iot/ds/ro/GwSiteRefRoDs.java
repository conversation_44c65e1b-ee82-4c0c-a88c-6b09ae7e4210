package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.GwSiteRefRoMapper;
import com.cdz360.iot.model.pv.dto.CntCtrlGtiDto;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GwSiteRefRoDs {
    @Autowired
    private GwSiteRefRoMapper mapper;

    public GwInfoDto getGwInfo(String siteId) {
        return mapper.getGwInfo(siteId);
    }

    public GwInfoDto getGwInfoByGwno(String gwno, Boolean enable) {
        return mapper.getGwInfoByGwno(gwno, enable);
    }

    public CntCtrlGtiDto countCtrlGti(String siteId) {
        CntCtrlGtiDto dto = new CntCtrlGtiDto();
        dto.setCtrlNum(mapper.countCtrl(siteId).intValue());
        dto.setGtiNum(mapper.countGti(siteId).intValue());
        dto.setEssNum(mapper.countEss(siteId).intValue());
        return dto;
    }

    public List<DeviceInfoVo> findCtrlVoList(@Nullable List<String> siteIdList) {
        return mapper.findCtrlVoList(siteIdList);
    }

    public List<DeviceInfoVo> findSrsVoList(@Nullable List<String> siteIdList) {
        return mapper.findSrsVoList(siteIdList);
    }

    public List<GwInfoVo> findCtrlList(ListCtrlParam param) {
        return mapper.findCtrlList(param);
    }
    public Long findCtrlListCount(ListCtrlParam param) {
        return mapper.findCtrlListCount(param);
    }

    public List<UpdateCtrlDeviceDto> findGtiAndEssListByGwno(List<String> gwnoList) {
        return mapper.findGtiAndEssListByGwno(gwnoList);
    }

    public GwInfoDto getGwInfoByName(String excludeGwno, String name) {
        return mapper.getGwInfoByName(excludeGwno, name);
    }
}
