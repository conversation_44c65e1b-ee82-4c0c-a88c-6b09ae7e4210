package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.SiteRoMapper;
import com.cdz360.iot.model.site.param.SiteAndPlugBiParam;
import com.cdz360.iot.model.site.po.SitePo;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SiteRoDs {
    @Autowired
    private SiteRoMapper siteRoMapper;


    public List<SitePo> queryByCondition(List<String> siteIdList) {
        return siteRoMapper.queryByCondition(siteIdList);
    }

    public SitePo getSite(String siteId) {
        return siteRoMapper.getSite(siteId);
    }

    /**
     * 查询未删除的场站信息
     * @param start
     * @param size
     * @return
     */
    public List<SitePo> listSite(long start, int size) {
        return siteRoMapper.listSite(start, size);
    }

    public List<SitePo> listSiteUnify(long start, int size) {
        return siteRoMapper.listSiteUnify(start, size);
    }

    public long listSiteUnifyCount() {
        return siteRoMapper.listSiteUnifyCount();
    }

//    public List<SiteDto> listSiteDto(ListSiteParam param){
//        return siteRoMapper.listSiteDto(param);
//    }

    /**
     * 获取有空闲枪头的场站ID列表
     */
    public List<String> getIdleSiteIdList(Long topCommId) {
        return this.siteRoMapper.getIdleSiteIdList(topCommId);
    }
//    public SiteDto getSiteByDzId(@Param(value = "dzId") String dzId){
//        return siteQueryMapper.getSiteByDzId(dzId);
//    }

    public Integer getSiteCount(SiteAndPlugBiParam param){
        return siteRoMapper.getSiteCount(param);
    }

    public Date getExpireDate(String siteId) {
        return siteRoMapper.getExpireDate(siteId);
    }

}
