package com.cdz360.iot.ds.rw;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.rw.mapper.EvseReportModuleDetailRwMapper;
import com.cdz360.iot.model.evse.po.EvseReportModuleDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseReportModuleDetailRwDs {

    @Autowired
    private EvseReportModuleDetailRwMapper mapper;

    public boolean batchInsert(List<EvseReportModuleDetailPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }else {
            return mapper.insert(list) > 0;
        }
    }

    public boolean removeByModuleId(Long moduleId) {
        return mapper.removeByModuleId(moduleId) > 0;
    }

}
