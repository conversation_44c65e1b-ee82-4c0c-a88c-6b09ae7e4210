<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.TransformerRoMapper">

	<resultMap id="RESULT_TRANSFORMER_PO" type="com.cdz360.iot.model.transformer.po.TransformerPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="no" jdbcType="VARCHAR" property="no" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="capacity" jdbcType="DECIMAL" property="capacity" />
		<result column="orderlyCaps" property="orderlyCaps" typeHandler="com.cdz360.iot.ds.ListTypeHandler"/>
		<result column="assignableCap" jdbcType="DECIMAL" property="assignableCap" />
		<result column="inputVoltage" jdbcType="DECIMAL" property="inputVoltage" />
		<result column="outputVoltage" jdbcType="DECIMAL" property="outputVoltage" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<resultMap id="RESULT_TRANSFORMER_VO" type="com.cdz360.iot.model.transformer.vo.TransformerVo">
		<result column="orderlyCaps" property="orderlyCaps" typeHandler="com.cdz360.iot.ds.ListTypeHandler"/>
		<result column="siteName" jdbcType="VARCHAR" property="siteName" />
	</resultMap>



	<select id="getById"
			resultMap="RESULT_TRANSFORMER_PO">	
		select * from t_transformer where id = #{id}
	</select>

	<select id="getByNo"
			resultMap="RESULT_TRANSFORMER_PO">
		select * from t_transformer where no = #{no}
	</select>

	<select id="list"
			resultMap="RESULT_TRANSFORMER_VO">
		select
			tt.*,
			site.name as siteName
		from
			t_transformer tt left join t_site site on tt.siteId=site.dzId
		where
			tt.siteId = #{siteId}
		<if test="transformerId != null">
			and tt.id = #{transformerId}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
			and (tt.no like concat('%', #{keyword}, '%') or tt.name like concat('%', #{keyword}, '%'))
		</if>
		order by tt.updateTime desc
		limit #{start}, #{size}
	</select>

	<select id="listCount" resultType="int">
		select
			count(0)
		from
			t_transformer tt
		where
			tt.siteId = #{siteId}
		<if test="transformerId != null">
			and tt.id = #{transformerId}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
			and (tt.no like concat('%', #{keyword}, '%') or tt.name like concat('%', #{keyword}, '%'))
		</if>
	</select>


	<select id="getTransformerList4SiteDynamicPower"
			resultMap="RESULT_TRANSFORMER_PO">
		select tfm.* from t_transformer tfm
		<where>
			tfm.assignableCap is not null
			<if test="transformerId != null">
				and tfm.id = #{transformerId}
			</if>
		</where>
	</select>

	<select id="getTransformerList4SiteOrderly"
		resultMap="RESULT_TRANSFORMER_PO">
		select tfm.* from t_transformer tfm
		<where>
			tfm.orderlyCaps is not null
			<if test="transformerId != null">
				and tfm.id = #{transformerId}
			</if>
		</where>
	</select>

</mapper>
