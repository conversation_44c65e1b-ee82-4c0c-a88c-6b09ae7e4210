package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.ds.ro.ess.mapper.EssDtuRoMapper;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.vo.EssMapDataVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDtuRoDs {

    @Autowired
    private EssDtuRoMapper essDtuRoMapper;

    public EssDtuPo getBySerialNo(String serialNo) {
        return this.essDtuRoMapper.getBySerialNo(serialNo);
    }

    public EssDtuPo getByEssDno(String essDno) {
        return this.essDtuRoMapper.getByEssDno(essDno);
    }

    public List<EssMapDataVo> essMapData(EssMapDataParam param) {
        return this.essDtuRoMapper.essMapData(param);
    }
}

