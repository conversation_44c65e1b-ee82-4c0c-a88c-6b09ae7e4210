package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.camera.po.CameraAccountPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface CameraAccountRoMapper {

	CameraAccountPo getById(@Param("id") Long id);
	List<CameraAccountPo> getAll();

	List<CameraAccountPo> getAccessTokenExpireByTime(@Param("expire") Date expire, @Param("type") Integer type);

	List<CameraAccountPo> getAccountExpireByTime(@Param("expire") Date expire, @Param("type") Integer type);
}
