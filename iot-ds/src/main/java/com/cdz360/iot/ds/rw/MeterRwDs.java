package com.cdz360.iot.ds.rw;

import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.ds.rw.mapper.MeterRwMapper;
import com.cdz360.iot.model.pv.po.GtiPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class MeterRwDs {

	@Autowired
	private MeterRwMapper meterRwMapper;

	public MeterPo getById(Long id, boolean lock) {
		return this.meterRwMapper.getById(id, lock);
	}

	public boolean insertMeter(MeterPo meterPo) {
		return this.meterRwMapper.insertMeter(meterPo) > 0;
	}

	public int batchInsertMeter(List<MeterPo> meterPoList) {

		return this.meterRwMapper.batchInsertMeter(meterPoList);

	}

	public boolean updateMeter(MeterPo meterPo) {
		return this.meterRwMapper.updateMeter(meterPo) > 0;
	}

	public int refreshMeterStatus(int ttl) {
		return this.meterRwMapper.refreshMeterStatus(ttl);
	}


}
