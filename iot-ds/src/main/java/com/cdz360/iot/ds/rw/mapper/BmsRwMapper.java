package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.bms.po.BmsPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface BmsRwMapper {

    int batchUpsetBms(@Param("poList") List<BmsPo> poList);

    int upsetBms(BmsPo bmsPo);

    int updateBms(BmsPo bmsPo);

    int offlineBms(
        @NonNull @Param("essDno") String essDno,
        @Nullable @Param("remainDnoList") List<String> remainDnoList);

}

