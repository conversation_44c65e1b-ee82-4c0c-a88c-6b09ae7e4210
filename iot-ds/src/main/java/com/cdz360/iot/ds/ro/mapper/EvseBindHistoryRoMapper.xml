<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseBindHistoryRoMapper">
    <select id="selectPrevious" resultType="com.cdz360.iot.model.evse.EvseBindHistoryPo">
        select * from t_evse_bind_history
        <where>
            time <![CDATA[ < ]]> #{time}
            and evseNo = #{evseNo}
            and siteId = #{siteId}
        </where>
        order by time desc
        limit 1
    </select>

<!--    <select id="selectNext" resultType="com.cdz360.iot.model.evse.EvseBindHistoryPo">-->
<!--        select * from t_evse_bind_history-->
<!--        <where>-->
<!--            time <![CDATA[ > ]]> #{time}-->
<!--            and evseNo = #{evseNo}-->
<!--            and siteId = #{siteId}-->
<!--        </where>-->
<!--        order by time-->
<!--        limit 1-->
<!--    </select>-->

    <select id="selectByDateRange" resultType="com.cdz360.iot.model.evse.EvseBindHistoryPo">
        select * from t_evse_bind_history
        <where>
            time <![CDATA[ >= ]]> #{startTime}
            and time <![CDATA[ <= ]]> #{endTime}
            and evseNo = #{evseNo}
            and siteId = #{siteId}
        </where>
        order by time
    </select>
</mapper>