package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseBundlePcOriginRwMapper;
import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname EvseBundlePcOriginRwDs
 * @Description
 * @Date 9/21/2019 3:04 PM
 * @Created by Rafael
 */
@Service
public class EvseBundlePcOriginRwDs {
    @Autowired
    private EvseBundlePcOriginRwMapper evseBundlePcOriginRwMapper;

    public int deleteByBundleId(Long bundleId) {
        return evseBundlePcOriginRwMapper.deleteByBundleId(bundleId);
    }

    public int insert(EvseBundlePcOrigin record) {
        return evseBundlePcOriginRwMapper.insert(record);
    }

    public int insertSelective(EvseBundlePcOrigin record) {
        return evseBundlePcOriginRwMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(EvseBundlePcOrigin record) {
        return evseBundlePcOriginRwMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(EvseBundlePcOrigin record) {
        return evseBundlePcOriginRwMapper.updateByPrimaryKey(record);
    }

    public int batchInsert(List<EvseBundlePcOrigin> list) {
        return evseBundlePcOriginRwMapper.batchInsert(list);
    }
}