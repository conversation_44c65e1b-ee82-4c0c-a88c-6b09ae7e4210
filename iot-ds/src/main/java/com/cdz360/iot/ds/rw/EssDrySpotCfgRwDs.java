package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssDrySpotCfgRwMapper;
import com.cdz360.iot.model.ess.po.EssDrySpotCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class EssDrySpotCfgRwDs {



	@Autowired

	private EssDrySpotCfgRwMapper essDrySpotCfgRwMapper;



	public boolean insertEssDrySpotCfg(EssDrySpotCfgPo essDrySpotCfgPo) {

		return this.essDrySpotCfgRwMapper.insertEssDrySpotCfg(essDrySpotCfgPo) > 0;

	}

	public void insertBeforeSelect(Long oldId, Long newId) {

		this.essDrySpotCfgRwMapper.insertBeforeSelect(oldId,newId);

	}

	public boolean updateEssDrySpotCfg(EssDrySpotCfgPo essDrySpotCfgPo) {

		return this.essDrySpotCfgRwMapper.updateEssDrySpotCfg(essDrySpotCfgPo) > 0;

	}





}

