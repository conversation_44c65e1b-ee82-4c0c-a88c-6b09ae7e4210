package com.cdz360.iot.ds.ro;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.mapper.EvseBindHistoryRoMapper;
import com.cdz360.iot.model.evse.EvseBindHistoryPo;
import com.cdz360.iot.model.type.EvseHistoryActionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Stack;

/**
 * @Classname EvseBindHistoryRoDs
 * @Description
 * @Date 3/28/2020 2:31 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class EvseBindHistoryRoDs {
    @Autowired
    private EvseBindHistoryRoMapper evseBindHistoryRoMapper;

    public EvseBindHistoryPo selectPrevious(Date time, String evseNo, String siteId) {
        return evseBindHistoryRoMapper.selectPrevious(time, evseNo, siteId);
    }

//    public EvseBindHistoryPo selectNext(Date time, String evseNo, String siteId) {
//        return evseBindHistoryRoMapper.selectNext(time, evseNo, siteId);
//    }

    /**
     * 获取时间范围内，桩在平台注册的总时长（秒）
     * @param startTime
     * @param endTime
     * @param evseNo
     * @param siteId
     */
    public int selectActiveTime(Date startTime, Date endTime, String evseNo, String siteId) {

        log.info("获取时间范围内，桩在平台注册的总时长: startTime-{}, endTime-{}, evseNo-{}, siteId-{}",
                startTime,
                endTime,
                evseNo,
                siteId);

        IotAssert.isTrue(startTime.before(endTime), "开始时间必须小于结束时间");

//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<EvseBindHistoryPo> inRange = evseBindHistoryRoMapper.selectByDateRange(startTime, endTime, evseNo, siteId);
        log.info("inRange: {}", JsonUtils.toJsonString(inRange));

        EvseBindHistoryPo startPoint = selectPrevious(startTime, evseNo, siteId);
        log.info("startPoint: {}", JsonUtils.toJsonString(startPoint));

        if(CollectionUtils.isEmpty(inRange)) {
            if(startPoint == null ||
                    EvseHistoryActionType.UNBIND.equals(startPoint.getAction())) {
                return 0;
            } else if(EvseHistoryActionType.BIND.equals(startPoint.getAction())) {
                return (int) (endTime.getTime() - startTime.getTime()) / 1000;
            } else {
                return 0;
            }
        } else {

            int activeTimeSum = 0;

            Stack<EvseBindHistoryPo> historyStack = new Stack<>();

            Iterator<EvseBindHistoryPo> iterator = inRange.iterator();

            EvseBindHistoryPo currentHistory = iterator.next();
            if(EvseHistoryActionType.BIND.equals(currentHistory.getAction())) {
                historyStack.push(currentHistory);
            } else {
                // 第一个为解绑，则开始时间减去解绑时间为可用时间
                activeTimeSum += ((currentHistory.getTime().getTime() - startTime.getTime()) / 1000);
            }

            while(iterator.hasNext()) {
                currentHistory = iterator.next();

                if(EvseHistoryActionType.BIND.equals(currentHistory.getAction())) {
                    historyStack.push(currentHistory);
                } else {
                    activeTimeSum += (currentHistory.getTime().getTime() - historyStack.pop().getTime().getTime()) / 1000;
                }
            }

            if(historyStack.size() != 0) {
                activeTimeSum += (endTime.getTime() - historyStack.pop().getTime().getTime()) / 1000;
            }

            return activeTimeSum;

        }

    }
}