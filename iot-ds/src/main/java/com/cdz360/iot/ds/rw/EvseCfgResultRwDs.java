package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseCfgResultRwMapper;
import com.cdz360.iot.model.evse.param.ListEvseCfgResultParam;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.vo.EvseCfgResultVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class EvseCfgResultRwDs {

    @Autowired
    private EvseCfgResultRwMapper evseCfgResultRwMapper;

    public void insertOrUpdate(EvseCfgResultPo evseCfgResult) {
        this.evseCfgResultRwMapper.insertOrUpdate(evseCfgResult);
    }

    public boolean updateByEvseNo(EvseCfgResultPo evseCfgResult) {
        return this.evseCfgResultRwMapper.updateByEvseNo(evseCfgResult) > 0;
    }

    public EvseCfgResultPo getByEvseNo(String evseNo, boolean lock) {
        return this.evseCfgResultRwMapper.getByEvseNo(evseNo, lock);
    }

    @Transactional
    public List<EvseCfgResultVo> getByPriceSchemeId(List<Long> priceSchemeIdList, boolean lock) {
        return this.evseCfgResultRwMapper.getByPriceSchemeId(priceSchemeIdList, lock);
    }

    public List<EvseCfgResultVo> getEvseCfgResultList(ListEvseCfgResultParam param) {
        return this.evseCfgResultRwMapper.getEvseCfgResultList(param);
    }

    public boolean resetBaseCfgAndPriceCfg(String evseNo) {
        return evseCfgResultRwMapper.resetBaseCfgAndPriceCfg(evseNo) > 0;
    }

}
