package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.upgrade.UpgradeTaskRelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname UpgradeTaskRelMapper
 * @Description TODO
 * @Date 9/18/2019 4:53 PM
 * @Created by Rafael
 */
@Mapper
public interface UpgradeTaskRelQueryMapper {
    UpgradeTaskRelVo selectById(@Param("id") Long id);
}