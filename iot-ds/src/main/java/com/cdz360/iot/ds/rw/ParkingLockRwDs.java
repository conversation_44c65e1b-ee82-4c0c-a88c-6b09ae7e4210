package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.ParkingLockRwMapper;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class ParkingLockRwDs {



	@Autowired

	private ParkingLockRwMapper parkingLockRwMapper;



	public ParkingLockPo getById(Long id, boolean lock) {

		return this.parkingLockRwMapper.getById(id, lock);

	}

	public ParkingLockPo getByUniqueKey(ParkingLockPartner partner, String serialNumber, boolean lock) {

		return this.parkingLockRwMapper.getByUniqueKey(partner, serialNumber, lock);

	}



	public boolean upsetParkingLock(ParkingLockPo parkingLockPo) {

		return this.parkingLockRwMapper.upsetParkingLock(parkingLockPo) > 0;

	}





}

