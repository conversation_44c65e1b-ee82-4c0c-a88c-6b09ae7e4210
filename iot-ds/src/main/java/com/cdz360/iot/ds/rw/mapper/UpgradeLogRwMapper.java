package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UpgradeLogRwMapper {

    UpgradeLogPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertUpgradeLog(UpgradeLogPo upgradeLogPo);

    int updateUpgradeLog(UpgradeLogPo upgradeLogPo);

}

