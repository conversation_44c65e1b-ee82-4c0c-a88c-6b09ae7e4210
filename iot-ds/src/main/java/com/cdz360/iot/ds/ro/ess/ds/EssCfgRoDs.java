package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ess.mapper.EssCfgRoMapper;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssCfgRoDs {

    @Autowired
    private EssCfgRoMapper essCfgRoMapper;

    public EssCfgPo getByCfgId(Long cfgId) {
        return this.essCfgRoMapper.getByCfgId(cfgId);
    }

    // 获取关联的ESS数量
    public Long getConnectedEssNum(Long cfgId) {
        return essCfgRoMapper.getConnectedEssNum(cfgId);
    }

    // 获取该模板下发中的ESS数量
    public Long getIssuingEssNum(Long cfgId) {
        return essCfgRoMapper.getIssuingEssNum(cfgId);
    }

    public void delPrecheck(Long cfgId) {
        long connectedEssNum = essCfgRoMapper.getConnectedEssNum(cfgId);
        IotAssert.isTrue(connectedEssNum == 0, "删除失败：存在关联网元");
        long issuingEssNum = essCfgRoMapper.getIssuingEssNum(cfgId);
        IotAssert.isTrue(issuingEssNum == 0, "删除失败：模板下发中");
    }

}

