package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.iot.ds.ro.ess.mapper.PcsRoMapper;
import com.cdz360.iot.model.ess.param.ListPcsParam;
import com.cdz360.iot.model.pcs.po.PcsPo;
import com.cdz360.iot.model.pcs.vo.PcsVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PcsRoDs {

    @Autowired
    private PcsRoMapper mapper;

    public PcsPo getByDno(String pcsDno) {
        return this.mapper.getByDno(pcsDno);
    }

    public List<PcsPo> getPcsListByEssDno(String essDno) {
        return mapper.getPcsListByEssDno(essDno);
    }


    public List<PcsVo> getPcsList(ListPcsParam paramIn) {
        return mapper.getPcsList(paramIn);
    }
}
