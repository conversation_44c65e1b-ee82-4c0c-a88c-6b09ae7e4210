package com.cdz360.iot.ds.rw;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.mapper.EssRwMapper;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssRwDs {

    @Autowired
    private EssRwMapper essRwMapper;

    public boolean upsetEss(EssPo essPo) {
        return this.essRwMapper.upsetEss(essPo) > 0;
    }

    public int batchUpsetEss(List<EssPo> essPoList) {
        return this.essRwMapper.batchUpsetEss(essPoList);
    }

    public int offlineEss(@NonNull String gwno, @Nullable List<String> remainDnoList) {
        return this.essRwMapper.offlineEss(gwno, remainDnoList);
    }

    public boolean updateEss(EssPo ess) {
        return this.essRwMapper.updateEss(ess) > 0;
    }

    public boolean updateStatus(String dno, @NonNull EquipStatus status) {
        return this.essRwMapper.updateStatus(dno, status) > 0;
    }

    public boolean updateAlertStatus(String dno, @NonNull EquipAlertStatus status) {
        return this.essRwMapper.updateAlertStatus(dno, status) > 0;
    }

    public boolean updateUserEss(EssPo ess) {
        IotAssert.isNotBlank(ess.getDno(), "户用储能设备编号无效");
        return this.essRwMapper.updateUserEss(ess) > 0;
    }
}

