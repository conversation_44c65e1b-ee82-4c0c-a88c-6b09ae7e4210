<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseMeterRoMapper">

	<resultMap id="RESULT_EVSEMETER_PO" type="com.cdz360.iot.model.meter.po.DeviceMeterPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="estimateType" jdbcType="VARCHAR" property="estimateType" />
		<result column="deviceId" jdbcType="VARCHAR" property="deviceId" />
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="pcsAcInput" jdbcType="VARCHAR" property="pcsAcInput" />
		<result column="pcsAcOutput" jdbcType="VARCHAR" property="pcsAcOutput" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_EVSEMETER_PO">	
		select * from t_device_meter where id = #{id}
	</select>

	<select id="getByEvseIdInList"
			resultMap="RESULT_EVSEMETER_PO">
		select * from t_device_meter where deviceId in
		<foreach item="item" index="index" collection="list"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getEvseMeterList"
			resultMap="RESULT_EVSEMETER_PO">
		select * from t_device_meter where
		meterId = #{meterId}
	</select>

</mapper>
