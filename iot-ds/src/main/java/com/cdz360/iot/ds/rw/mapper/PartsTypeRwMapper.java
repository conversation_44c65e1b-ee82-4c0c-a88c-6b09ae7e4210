package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.parts.po.PartsTypePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



@Mapper

public interface PartsTypeRwMapper {

	PartsTypePo getById(@Param("id") Long id, @Param("lock") boolean lock);



	int insertPartsType(PartsTypePo partsTypePo);



	int updatePartsType(PartsTypePo partsTypePo);





}

