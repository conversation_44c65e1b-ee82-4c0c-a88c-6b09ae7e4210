package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.UserDeviceRefRwMapper;
import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class UserDeviceRefRwDs {

    @Autowired
    private UserDeviceRefRwMapper userDeviceRefRwMapper;

    public UserDeviceRefPo getById(Long id, boolean lock) {
        return this.userDeviceRefRwMapper.getById(id, lock);
    }

    public int batchInsert(List<UserDeviceRefPo> poList) {
        return this.userDeviceRefRwMapper.batchInsert(poList);
    }

    public int batchUpset(List<UserDeviceRefPo> poList) {
        return this.userDeviceRefRwMapper.batchUpset(poList);
    }

    public boolean insertUserDeviceRef(UserDeviceRefPo userDeviceRefPo) {
        return this.userDeviceRefRwMapper.insertUserDeviceRef(userDeviceRefPo) > 0;
    }

    public boolean updateUserDeviceRef(UserDeviceRefPo userDeviceRefPo) {
        return this.userDeviceRefRwMapper.updateUserDeviceRef(userDeviceRefPo) > 0;
    }

    @Transactional
    public boolean resetNewMaster(UserDeviceRefPo device) {
        device.setEnable(false).setMaster(false);
        boolean b = this.updateUserDeviceRef(device);
        if (!b) {
            return false;
        }
        this.userDeviceRefRwMapper.resetNewMaster(device.getDno());
        return true;
    }

    public boolean upsetUserDeviceRef(UserDeviceRefPo userDeviceRefPo) {
        return this.userDeviceRefRwMapper.upsetUserDeviceRef(userDeviceRefPo) > 0;
    }
}

