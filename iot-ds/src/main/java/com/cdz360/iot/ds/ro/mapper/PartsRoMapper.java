package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.parts.param.ListPartsParam;
import com.cdz360.iot.model.parts.po.PartsPo;
import com.cdz360.iot.model.parts.vo.PartsVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface PartsRoMapper {

    PartsPo getByCode(@Param("code") String code);

    List<PartsVo> findParts(ListPartsParam param);

    List<PartsPo> findPartsStatus(List<String> codeList);

    Long countParts(ListPartsParam param);
}

