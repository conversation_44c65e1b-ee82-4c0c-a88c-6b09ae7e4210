package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseMeterRoMapper;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EvseMeterRoDs {

	@Autowired
	private EvseMeterRoMapper evseMeterRoMapper;

	public DeviceMeterPo getById(Long id) {
		return this.evseMeterRoMapper.getById(id);
	}

	public List<DeviceMeterPo> getByEvseIdInList(List<String> list) {
		return this.evseMeterRoMapper.getByEvseIdInList(list);
	}

	public List<DeviceMeterPo> getEvseMeterList(Long meterId) {
		return this.evseMeterRoMapper.getEvseMeterList(meterId);
	}

}
