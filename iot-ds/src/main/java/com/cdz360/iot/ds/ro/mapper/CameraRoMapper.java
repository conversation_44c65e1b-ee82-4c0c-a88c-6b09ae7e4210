package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraPo;
import com.cdz360.iot.model.camera.vo.CameraVo;
import com.cdz360.iot.model.camera.vo.OpInitCameraVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface CameraRoMapper {

	CameraPo getById(@Param("id") Long id);

	List<CameraVo> getBySiteId(ListCameraParam param);

    CameraVo getByCameraId(@Param("cameraId") Long cameraId);

	List<OpInitCameraVo> getOpInitHlsUrl(@Nullable @Param("cameraIdList") List<Long> cameraIdList,
										 @Param("size") int size);
}
