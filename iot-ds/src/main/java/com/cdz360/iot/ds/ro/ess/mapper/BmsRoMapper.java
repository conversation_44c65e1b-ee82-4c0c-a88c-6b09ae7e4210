package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.ess.param.ListBmsParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BmsRoMapper {

    BmsPo getByDno(@Param("bmsDno") String bmsDno);

    List<BmsPo> getBmsListByEssDno(@Param("essDno") String essDno);

    List<BmsPo> getBmsList(ListBmsParam paramIn);
}
