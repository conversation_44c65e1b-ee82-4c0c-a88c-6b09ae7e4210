package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.EssSmoothOutPutRwMapper;
import com.cdz360.iot.model.ess.po.SmoothOutPutCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class EssSmoothOutPutRwDs {

	@Autowired
	private EssSmoothOutPutRwMapper essSmoothOutPutRwMapper;


	public boolean insertEssSmoothOutPutCfg(SmoothOutPutCfgPo essDrySpotCfgPo) {

		return this.essSmoothOutPutRwMapper.insertEssSmoothOutPutCfg(essDrySpotCfgPo) > 0;

	}

	public void insertBeforeSelect(Long oldId, Long newId) {

		this.essSmoothOutPutRwMapper.insertBeforeSelect(oldId,newId);

	}
}

