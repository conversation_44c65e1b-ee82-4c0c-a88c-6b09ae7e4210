package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.BmsRwMapper;
import com.cdz360.iot.model.bms.po.BmsPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BmsRwDs {

    @Autowired
    private BmsRwMapper bmsRwMapper;

    public int batchUpsetBms(List<BmsPo> essPoList) {
        return this.bmsRwMapper.batchUpsetBms(essPoList);
    }

    public int upsetBms(BmsPo bmsPo) {
        return this.bmsRwMapper.upsetBms(bmsPo);
    }

    public int updateBms(BmsPo bmsPo) {
        return this.bmsRwMapper.updateBms(bmsPo);
    }

    public int offlineBms(@NonNull String essDno, @Nullable List<String> remainDnoList) {
        return bmsRwMapper.offlineBms(essDno, remainDnoList);
    }

}

