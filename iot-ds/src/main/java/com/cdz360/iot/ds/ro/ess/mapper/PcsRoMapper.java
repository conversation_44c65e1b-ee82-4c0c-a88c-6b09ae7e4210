package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.iot.model.ess.param.ListPcsParam;
import com.cdz360.iot.model.pcs.po.PcsPo;
import com.cdz360.iot.model.pcs.vo.PcsVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PcsRoMapper {

    PcsPo getByDno(@Param("pcsDno") String pcsDno);

    List<PcsPo> getPcsListByEssDno(@Param("essDno") String essDno);

    List<PcsVo> getPcsList(ListPcsParam paramIn);
}
