<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.pv.mapper.GtiGridDispatchCfgRoMapper">

  <resultMap id="RESULT_GTICFG_PO" type="com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo">
    <id column="cfgId" jdbcType="BIGINT" property="cfgId"/>
    <result column="systemSwitch" jdbcType="BOOLEAN" property="systemSwitch"/>
    <result column="systemTime" jdbcType="BOOLEAN" property="systemTime"/>
    <result column="timezone" jdbcType="INTEGER" property="timezone"/>
    <result column="quCurveMode" jdbcType="BOOLEAN" property="quCurveMode"/>
    <result column="quDispatchPowerPct" jdbcType="INTEGER" property="quDispatchPowerPct"/>
    <result column="activePowerFixedDeratingKw" jdbcType="INTEGER"
      property="activePowerFixedDeratingKw"/>
    <result column="reactivePowerCompensationPf" jdbcType="INTEGER"
      property="reactivePowerCompensationPf"/>
    <result column="reactivePowerCompensationQs" jdbcType="INTEGER"
      property="reactivePowerCompensationQs"/>
    <result column="activePowerPctDerating" jdbcType="INTEGER" property="activePowerPctDerating"/>
    <result column="activePowerFixedDeratingW" jdbcType="INTEGER"
      property="activePowerFixedDeratingW"/>
    <result column="nightReactivePowerCompensation" jdbcType="INTEGER"
      property="nightReactivePowerCompensation"/>
    <result column="reactivePowerAdjustmentTime" jdbcType="INTEGER"
      property="reactivePowerAdjustmentTime"/>
    <result column="quExitPowerPct" jdbcType="INTEGER" property="quExitPowerPct"/>
    <result column="reactivePowerChangeGradient" jdbcType="INTEGER"
      property="reactivePowerChangeGradient"/>
    <result column="activePowerChangeGradient" jdbcType="INTEGER"
      property="activePowerChangeGradient"/>
    <result column="schedulingCommandMaintenanceTime" jdbcType="INTEGER"
      property="schedulingCommandMaintenanceTime"/>
    <result column="gridStandardCode" jdbcType="INTEGER" property="gridStandardCode"/>
    <result column="ppnCurve" property="ppnCurve"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="quCurve" property="quCurve"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
    <result column="pfuCurve" property="pfuCurve"
      typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
  </resultMap>

  <select id="getById" resultMap="RESULT_GTICFG_PO">
    select
    *
    from
    t_gti_grid_dispatch_cfg ggdc
    left join t_dev_cfg dev on
    ggdc.cfgId = dev.id
    where cfgId = #{id}
    limit 1
  </select>

</mapper>

