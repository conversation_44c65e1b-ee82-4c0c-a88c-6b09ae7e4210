package com.cdz360.iot.ds;

import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

@Slf4j
public class EssInOutStrategyItemListTypeHandler extends
    BaseTypeHandler<List<EssInOutStrategyItem>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<EssInOutStrategyItem> parameter,
        JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtils.toJsonString(parameter));
    }

    @Override
    public List<EssInOutStrategyItem> getNullableResult(ResultSet rs, String columnName)
        throws SQLException {
        Object ordinal = rs.getObject(columnName);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return JsonUtils.fromJson(rs.getString(columnName),
                new TypeReference<>() {
                });
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }

    @Override
    public List<EssInOutStrategyItem> getNullableResult(ResultSet rs, int columnIndex)
        throws SQLException {
        Object ordinal = rs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return JsonUtils.fromJson(rs.getString(columnIndex),
                new TypeReference<>() {
                });
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }

    @Override
    public List<EssInOutStrategyItem> getNullableResult(CallableStatement cs, int columnIndex)
        throws SQLException {
        Object ordinal = cs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return JsonUtils.fromJson(cs.getString(columnIndex),
                new TypeReference<>() {
                });
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }
}
