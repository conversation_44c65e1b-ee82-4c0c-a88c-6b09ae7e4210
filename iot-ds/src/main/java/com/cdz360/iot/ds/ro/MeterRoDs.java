package com.cdz360.iot.ds.ro;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.mapper.MeterRoMapper;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.vo.MeterEvseVo;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MeterRoDs {

    @Autowired
    private MeterRoMapper meterRoMapper;

    public MeterPo getById(Long id) {
        return this.meterRoMapper.getById(id);
    }

    public List<MeterPo> getByGwno(String gwno) {
        return this.meterRoMapper.getByGwno(gwno);
    }

    public MeterPo getByNo(String no) {
        return this.meterRoMapper.getByNo(no);
    }

    public MeterPo getByDno(String dno) {
        return this.meterRoMapper.getByDno(dno);
    }

    public List<MeterEvseVo> getMeterList(MeterListParam param) {
        List<MeterEvseVo> ret = this.meterRoMapper.getMeterList(param);
        ret.forEach(e -> {
            e.setDeviceMeterPoList(e.getDeviceMeterPoList()
                .stream()
                .filter(el -> StringUtils.isNotBlank(el.getDeviceId()))
                .collect(Collectors.toList())
            );
            e.setBindEvseCount(e.getDeviceMeterPoList().size());
        });
        return ret;
    }

    public long getMeterListTotal(MeterListParam param) {
        return this.meterRoMapper.getMeterListTotal(param);
    }

    public List<MeterPo> getAllOnline() {
        return this.meterRoMapper.getAllOnline();
    }

    public List<MeterPo> getAllBindMeter() {
        return this.meterRoMapper.getAllBindMeter();
    }

    public List<MeterPo> getBindInTransformerByMeterId(List<Long> ids) {
        return this.meterRoMapper.getBindInTransformerByMeterId(ids);
    }

    public List<MeterVo> getMeterVoList(MeterListParam param) {
        param.setStartIfNull(0L)
            .setSizeIfNull(100, 1000);
        return this.meterRoMapper.getMeterVoList(param);
    }

    public List<MeterVo> getMeterVoList2(MeterListParam param) {
        return this.meterRoMapper.getMeterVoList2(param);
    }

    public long getMeterVoList2Count(MeterListParam param) {
        return this.meterRoMapper.getMeterVoList2Count(param);
    }

    public List<String> getSiteIdList(MeterListParam param) {
        return this.meterRoMapper.getSiteIdList(param);
    }

    public Long getSiteIdListTotal(MeterListParam param) {
        return this.meterRoMapper.getSiteIdListTotal(param);
    }
}
