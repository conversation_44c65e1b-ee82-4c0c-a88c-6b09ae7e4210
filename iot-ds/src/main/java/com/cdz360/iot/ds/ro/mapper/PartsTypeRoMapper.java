package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.parts.param.ListPartsTypeParam;
import com.cdz360.iot.model.parts.param.PartsCheckParam;
import com.cdz360.iot.model.parts.po.PartsTypePo;
import com.cdz360.iot.model.parts.vo.PartsTypeVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface PartsTypeRoMapper {

    PartsTypePo getOneByFields(
        @Param("name") String name,
        @Param("code") String code,
        @Param("fullModel") String fullModel);

    PartsTypePo getById(@Param("id") Long id);

    List<PartsTypeVo> findPartsType(ListPartsTypeParam param);

    Long countPartsType(ListPartsTypeParam param);

    List<PartsTypePo> getByFields(@Param("paramList") List<PartsCheckParam> list);

}

