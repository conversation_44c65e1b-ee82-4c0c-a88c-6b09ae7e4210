package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.base.model.es.vo.EssDeviceBiVo;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.ds.ro.ess.mapper.EssEquipRoMapper;
import com.cdz360.iot.model.ess.dto.EssEquipTinyDto;
import com.cdz360.iot.model.ess.param.ListEssBatteryClusterParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EssEquipBatteryClusterVo;
import com.cdz360.iot.model.ess.vo.EssEquipVo;
import com.cdz360.iot.model.ess.vo.EssStatusBi;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssEquipRoDs {

    @Autowired
    private EssEquipRoMapper essEquipRoMapper;

    public EssEquipPo getByDno(String dno) {
        return this.essEquipRoMapper.getByDno(dno);
    }

    public EssEquipPo getById(Long id) {
        return this.essEquipRoMapper.getById(id);
    }

    public List<EssEquipPo> findEquipList(ListCtrlParam param) {
        return this.essEquipRoMapper.findEquipList(param);
    }

    public List<EssEquipPo> findEquipListBySiteId(@NonNull String siteId,
        @Nullable EssEquipType equipType) {
        return this.essEquipRoMapper.findEquipListBySiteId(siteId, equipType);
    }

    public EssEquipPo getByEssDnoAndEquipId(String essDno, Long equipId) {
        return this.essEquipRoMapper.getByEssDnoAndEquipId(essDno, equipId);
    }

    public List<EssEquipVo> getEssEquipListByDno(List<String> dnoList) {
        return this.essEquipRoMapper.getEssEquipListByDno(dnoList);
    }

    public List<EssEquipVo> findEssEquipList(ListEssEquipParam param) {
        return this.essEquipRoMapper.findEssEquipList(param);
    }

    /**
     * 获取储能设备的下属子设备(缩略信息)
     */
    public List<EssEquipTinyDto> getEssEquipTinyList(ListEssEquipParam param) {
        return this.essEquipRoMapper.getEssEquipTinyList(param);
    }

    public Long getEssEquipCount(ListEssEquipParam param) {
        return this.essEquipRoMapper.getEssEquipCount(param);
    }

    // 临时使用
    public EssEquipPo getEssStack(String essDno) {
        return this.essEquipRoMapper.getEssStack(essDno);
    }

    public List<EssEquipBatteryClusterVo> findBatteryCluster(ListEssBatteryClusterParam param) {
        return this.essEquipRoMapper.findBatteryCluster(param);
    }

    public Long countBatteryCluster(ListEssBatteryClusterParam param) {
        return this.essEquipRoMapper.countBatteryCluster(param);
    }

    public List<EssEquipPo> getEquipList(ListEssEquipParam param) {
        param.setStartIfNull(0L)
            .setSizeIfNull(100, 1000);
        return this.essEquipRoMapper.getEquipList(param);
    }

    public List<EssDeviceBiVo> equipCountByEssDno(List<String> dnoList) {
        return this.essEquipRoMapper.equipCountByEssDno(dnoList);
    }

    public List<EssStatusBi> getEquipStatusBi(String siteId) {
        List<EssStatusBi> res = this.essEquipRoMapper.getEquipStatusBi(siteId);
        Long abnormalNum = this.essEquipRoMapper.getAbnormalEquipNum(siteId);
        res.add(new EssStatusBi().setStatus(3).setNum(abnormalNum));
        return res;
    }

    public List<UpdateCtrlDeviceDto> getEquipListByEssDnoList(List<String> essDnoList) {
        return this.essEquipRoMapper.getEquipListByEssDnoList(essDnoList);
    }

    public int getMaxEquipIdByDno(String essDno) {
        return essEquipRoMapper.getMaxEquipIdByDno(essDno);
    }
}

