package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.ess.param.ListDevCfgParam;
import com.cdz360.iot.model.ess.po.DevCfgPo;
import com.cdz360.iot.model.ess.type.CfgType;
import com.cdz360.iot.model.ess.vo.DevCfgVo;
import com.cdz360.iot.model.ess.vo.GtiCfgVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface DevCfgRoMapper {



	DevCfgPo getById(@Param("id") Long id,@Param("commIdChain") String commIdChain);

	DevCfgPo queryById(@Param("id") Long id);

	List<Long> getByName(@Param("name") String name,@Param("siteId") String siteId);

	GtiCfgVo getGtiDetailById(@Param("id") Long id,
		@Param("commIdChain") String commIdChain,
		@Param("cfgType") CfgType cfgType);

	Long getDevCfgCount(ListDevCfgParam param);

	List<DevCfgVo> getDevCfgList(ListDevCfgParam param);
}

