package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.EvseBindHistoryPo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Classname EvseBindHistoryRoMapper
 * @Description
 * @Date 3/28/2020 2:00 PM
 * @Created by Rafael
 */
public interface EvseBindHistoryRoMapper {
    /**
     * 获取之前最后一个动作
     * @param time
     * @param evseNo
     * @param siteId
     * @return
     */
    EvseBindHistoryPo selectPrevious(@Param("time") Date time,
                                     @Param("evseNo") String evseNo,
                                     @Param("siteId") String siteId);

    /**
     * 获取之后最先一个动作
     * @param time
     * @param evseNo
     * @param siteId
     * @return
     */
//    EvseBindHistoryPo selectNext(@Param("time") Date time,
//                                 @Param("evseNo") String evseNo,
//                                 @Param("siteId") String siteId);

    List<EvseBindHistoryPo> selectByDateRange(@Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("evseNo") String evseNo,
                                              @Param("siteId") String siteId);
}