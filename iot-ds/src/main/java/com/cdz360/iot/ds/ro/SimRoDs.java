package com.cdz360.iot.ds.ro;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.mapper.SimRoMapper;
import com.cdz360.iot.model.sim.param.ListSimParam;
import com.cdz360.iot.model.sim.param.SimQueryCondition;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.vo.SimTinyVo;
import com.cdz360.iot.model.sim.vo.SimUnionVo;
import com.cdz360.iot.model.sim.vo.SimVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SimRoDs {

    @Autowired
    private SimRoMapper mapper;

    public List<SimVo> getList(ListSimParam param, List<String> evseNoIccids, List<String> siteIdIccids) {
        if (param.getStart() == null && param.getSize() == null) {
            param.setStart(0L)
                .setSize(10);
        }
        return mapper.getSimList(param, evseNoIccids, siteIdIccids);
    }

    public Long getListCount(ListSimParam param, List<String> evseNoIccids, List<String> siteIdIccids) {
        return mapper.getSimListCount(param, evseNoIccids, siteIdIccids);
    }

    public List<SimTinyVo> getTinyList(ListSimParam param) {
        if (param.getStart() == null) param.setStart(0L);
        if (param.getSize() == null) param.setSize(999);

        return mapper.getTinyList(param);
    }

    public SimVo getVoBySimId(Long simId) {
        return mapper.getVoBySimId(simId);
    }

    public List<SimVo> queryByCondition(SimQueryCondition param) {
        return mapper.queryByCondition(param);
    }

    public SimPo getOneByCode(String code) {
        return mapper.getOneByCode(code);
    }

    public SimPo getById(Long simId) {
        return mapper.getById(simId);
    }

    public SimPo getByIccid(String iccid) {
        return mapper.getByIccid(iccid);
    }

    public List<SimPo> getUnionAll(List<String> codeList) {
        return mapper.getUnionAll(codeList);
    }

    public List<String> getUnion(List<String> codeList) {
        return mapper.getUnion(codeList);
    }

    public List<SimUnionVo> getUnionVo(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) return List.of();
        return mapper.getUnionVo(codeList);
    }

}
