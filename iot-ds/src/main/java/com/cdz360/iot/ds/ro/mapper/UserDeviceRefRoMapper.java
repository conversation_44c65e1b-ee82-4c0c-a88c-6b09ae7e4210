package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserDeviceRefRoMapper {

    UserDeviceRefPo getById(@Param("id") Long id);

    List<UserDeviceRefPo> getByUidAndMaster(
        @Param("uid") Long uid, @Param("master") Boolean master);

    UserDeviceRefPo getMasterByDno(
        @Param("dno") String dno, @Param("enable") Boolean enable);

    UserDeviceRefPo getOneByUidAndDnoAndEnable(
        @Param("uid") Long uid, @Param("dno") String dno, @Param("enable") Boolean enable);
}

