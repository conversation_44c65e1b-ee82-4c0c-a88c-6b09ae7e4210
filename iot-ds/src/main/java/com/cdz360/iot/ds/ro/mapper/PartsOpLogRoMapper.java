package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import com.cdz360.iot.model.parts.vo.PartsOpLogVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PartsOpLogRoMapper {

    PartsOpLogPo getById(@Param("id") Long id);

    List<PartsOpLogVo> getAllByPartsCode(@Param("partsCode") String partsCode);
}

