package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.site.dto.SiteCtrlDto;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname SiteCtrlRoMapper
 * @Description
 * @Date 4/22/2020 3:38 PM
 * @Created by Rafael
 */
@Mapper
public interface SiteCtrlRoMapper {
    SiteCtrlPo selectByNum(@Param("ctrlNum") String ctrlNum);

    List<SiteCtrlDto> list(@Param("keyword") String keyword,
                           @Param("siteId") String siteId,
                           @Param("start") long start, @Param("size") long size);

    Long listCount(@Param("keyword") String keyword,
                   @Param("siteId") String siteId);
}