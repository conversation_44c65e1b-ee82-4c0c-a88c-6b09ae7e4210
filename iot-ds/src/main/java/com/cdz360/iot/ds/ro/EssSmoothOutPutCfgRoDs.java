package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.ess.mapper.EssSmoothOutPutRoMapper;
import com.cdz360.iot.model.ess.po.SmoothOutPutCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class EssSmoothOutPutCfgRoDs {


    @Autowired

    private EssSmoothOutPutRoMapper essSmoothOutPutRoMapper;

    public SmoothOutPutCfgPo getById(Long id) {
        return this.essSmoothOutPutRoMapper.getById(id);
    }


}

