package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName： EvseBundlePcOriginRwMapper
 * @Description: 升级包支持的源版本信息数据访问层-RW
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:38
 */
@Mapper
public interface EvseBundlePcOriginRwMapper {
    int deleteByBundleId(@Param("bundleId") Long bundleId);

    int insert(EvseBundlePcOrigin record);

    int insertSelective(EvseBundlePcOrigin record);

    int updateByPrimaryKeySelective(EvseBundlePcOrigin record);

    int updateByPrimaryKey(EvseBundlePcOrigin record);

    int batchInsert(@Param("list") List<EvseBundlePcOrigin> list);
}