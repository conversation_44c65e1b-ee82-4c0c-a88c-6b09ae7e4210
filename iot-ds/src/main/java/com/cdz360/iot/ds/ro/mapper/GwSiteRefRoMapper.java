package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.common.base.IotCacheConstants;
import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwSiteRefPo;
import com.cdz360.iot.model.site.vo.DeviceInfoVo;
import com.cdz360.iot.model.site.vo.GwInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface GwSiteRefRoMapper {
    List<GwSiteRefPo> listAllGwSiteRef();


    @Cacheable(cacheNames = IotCacheConstants.IOT_GW_SITE_REF_KEY)
    GwInfoDto getGwInfo(@Param("siteId") String siteId);

//    @Cacheable(cacheNames = IotCacheConstants.IOT_GW_SITE_REF_KEY)
    GwInfoDto getGwInfoByGwno(@Param("gwno") String gwno, @Param("enable") Boolean enable);

    Long countCtrl(String siteId);
    Long countGti(String siteId);
    Long countEss(String siteId);

    List<DeviceInfoVo> findCtrlVoList(@Nullable @Param("siteIdList") List<String> siteIdList);

    List<DeviceInfoVo> findSrsVoList(@Nullable @Param("siteIdList") List<String> siteIdList);

    List<GwInfoVo> findCtrlList(ListCtrlParam param);
    Long findCtrlListCount(ListCtrlParam param);

    List<UpdateCtrlDeviceDto> findGtiAndEssListByGwno(@Param("gwnoList") List<String> gwnoList);

    GwInfoDto getGwInfoByName(@Param("excludeGwno") String excludeGwno, @Param("name") String name);
}
