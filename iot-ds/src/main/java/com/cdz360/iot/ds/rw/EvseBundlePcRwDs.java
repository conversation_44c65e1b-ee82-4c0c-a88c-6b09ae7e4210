package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvseBundlePcRwMapper;
import com.cdz360.iot.model.evse.EvseBundlePc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname EvseBundlePcRwDs
 * @Description TODO
 * @Date 9/21/2019 3:01 PM
 * @Created by Rafael
 */
@Service
public class EvseBundlePcRwDs {
    @Autowired
    private EvseBundlePcRwMapper evseBundlePcRwMapper;

    public int deleteByBundleId(Long bundleId) {
        return evseBundlePcRwMapper.deleteByBundleId(bundleId);
    }

    public int insert(EvseBundlePc record) {
        return evseBundlePcRwMapper.insert(record);
    }

    public int insertSelective(EvseBundlePc record) {
        return evseBundlePcRwMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(EvseBundlePc record) {
        return evseBundlePcRwMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(EvseBundlePc record) {
        return evseBundlePcRwMapper.updateByPrimaryKey(record);
    }

    public int batchInsert(List<EvseBundlePc> list) {
        return evseBundlePcRwMapper.batchInsert(list);
    }
}