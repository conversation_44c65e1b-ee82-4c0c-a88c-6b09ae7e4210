<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.ParkingLockEventLogRwMapper">

  <resultMap id="RESULT_PARKINGLOCKEVENTLOG_PO"
    type="com.cdz360.iot.model.park.po.ParkingLockEventLogPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="parkingLockId" jdbcType="BIGINT" property="parkingLockId"/>
    <result column="eventType" jdbcType="INTEGER" property="eventType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="carNo" jdbcType="VARCHAR" property="carNo"/>
    <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>


  <select id="getById"
    resultMap="RESULT_PARKINGLOCKEVENTLOG_PO">
    select * from t_parking_lock_event_log where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>


  <insert id="insertParkingLockEventLog" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.park.po.ParkingLockEventLogPo">
    insert into t_parking_lock_event_log (`parkingLockId`,
    `eventType`,
    `carNo`,
    `orderNo`)
    values (#{parkingLockId},
    #{eventType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
    #{carNo},
    #{orderNo})
  </insert>

  <update id="updateParkingLockEventLog"
    parameterType="com.cdz360.iot.model.park.po.ParkingLockEventLogPo">
    update t_parking_lock_event_log set
    <if test="parkingLockId != null">
      parkingLockId = #{parkingLockId},
    </if>
    <if test="eventType != null">
      eventType = #{eventType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
    </if>
    <if test="carNo != null">
      carNo = #{carNo},
    </if>
    <if test="orderNo != null">
      orderNo = #{orderNo},
    </if>
    where id = #{id}
  </update>

</mapper>

