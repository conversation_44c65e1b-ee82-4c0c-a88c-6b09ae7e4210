package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.model.transformer.vo.TransformerVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TransformerRoMapper {

	TransformerPo getById(@Param("id") Long id);

	List<TransformerVo> list(@Param("keyword") String keyword,
							 @Param("transformerId") Long transformerId,
							 @Param("siteId") String siteId,
							 @Param("start") long start,
							 @Param("size") long size);

	int listCount(@Param("keyword") String keyword,
				  @Param("transformerId") Long transformerId,
				  @Param("siteId") String siteId,
				  @Param("start") long start,
				  @Param("size") long size);

    TransformerPo getByNo(@Param("no") String no);

	/**
	 * 查询有设置场站功率分配的变压器
	 */
	List<TransformerPo> getTransformerList4SiteDynamicPower(@Param("transformerId") Long transformerId);

	/**
	 * 查询有设置场站有序充电的变压器
	 */
	List<TransformerPo> getTransformerList4SiteOrderly(@Param("transformerId") Long transformerId);
}
