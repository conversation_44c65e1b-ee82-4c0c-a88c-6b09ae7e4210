package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseReportModuleRoMapper;
import com.cdz360.iot.model.evse.po.EvseReportModulePo;
import com.cdz360.iot.model.evse.vo.EvseModuleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseReportModuleRoDs {

    @Autowired
    private EvseReportModuleRoMapper moduleRoMapper;

    public EvseReportModulePo getByEvseNo(String evseNo) {
        return moduleRoMapper.getByEvseNo(evseNo);
    }

    public List<EvseModuleVo> getEvseModuleVoList(String evseNo) {
        return moduleRoMapper.getEvseModuleVoList(evseNo);
    }

}
