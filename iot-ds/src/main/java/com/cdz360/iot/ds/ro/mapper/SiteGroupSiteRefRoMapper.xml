<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SiteGroupSiteRefRoMapper">

  <select id="selectSiteIdByGids" resultType="java.lang.String">
    select distinct siteId from t_r_site_group_site_ref
    and gid in
    <foreach collection="gids" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

</mapper>