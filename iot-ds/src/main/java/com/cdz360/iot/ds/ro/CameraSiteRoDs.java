package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.CameraSiteRoMapper;
import com.cdz360.iot.model.camera.dto.CameraSiteDto;
import com.cdz360.iot.model.camera.param.ListCameraParam;
import com.cdz360.iot.model.camera.po.CameraSitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CameraSiteRoDs {

	@Autowired
	private CameraSiteRoMapper cameraSiteRoMapper;

	public CameraSitePo getById(Long id) {
		return this.cameraSiteRoMapper.getById(id);
	}

	public CameraSitePo getBySiteId(String siteId) {
		return this.cameraSiteRoMapper.getBySiteId(siteId);
	}

	public List<CameraSitePo> getStoreList(ListCameraParam param) {
		return this.cameraSiteRoMapper.getStoreList(param);
	}
	public List<CameraSiteDto> getStoreListHasCamera(ListCameraParam param) {
		return this.cameraSiteRoMapper.getStoreListHasCamera(param);
	}

	public List<CameraSitePo> getAccessTokenExpireByTime(Date bufferTime, int ys7Type) {
		return this.cameraSiteRoMapper.getAccessTokenExpireByTime(bufferTime, ys7Type);
	}
}
