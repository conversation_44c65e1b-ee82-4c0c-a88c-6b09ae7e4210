package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseOfflineRoMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.dto.EvseTinyDto;
import com.cdz360.iot.model.evse.param.EvseTinyParam;
import com.cdz360.iot.model.evse.param.ListEvseParam;
import com.cdz360.iot.model.evse.po.EvsePlugRecordPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvseOfflineRoDs {

    @Autowired
    private EvseOfflineRoMapper evseOfflineRoMapper;

    public Long count(String evseNo, String siteId) {
        return evseOfflineRoMapper.count(evseNo, siteId);
    }

    public List<EvsePlugRecordPo> getEvseRecordInfo(String siteId) {
        return evseOfflineRoMapper.getEvseRecordInfo(siteId);
    }

    public List<EvsePo> getEvseList(ListEvseParam param) {
        return evseOfflineRoMapper.getEvseList(param);
    }

    public List<EvseTinyDto> getEvseTinyList(EvseTinyParam param) {
        return evseOfflineRoMapper.getEvseTinyList(param);
    }

    public List<EvseModelVo> getEvseModelVoList(ListEvseParam param) {
        return evseOfflineRoMapper.getEvseModelVoList(param);
    }

}
