<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssCfgRoMapper">

  <resultMap id="RESULT_ESSCFG_PO" type="com.cdz360.iot.model.ess.po.EssCfgPo">
    <result column="cfgId" jdbcType="BIGINT" property="cfgId"/>
    <result column="cfgNo" jdbcType="VARCHAR" property="cfgNo"/>
    <result column="strategy" property="strategy"/>
    <result column="otherStrategy" property="otherStrategy"/>
    <result column="repeatCycle" property="repeatCycle"/>
    <result column="effectiveStartTime" property="effectiveStartTime"/>
    <result column="effectiveEndTime" property="effectiveEndTime"/>
    <result column="samplingTime" jdbcType="INTEGER" property="samplingTime"/>
    <result column="bootMode" property="bootMode"/>
    <result column="switchCommand" property="switchCommand"/>
    <result column="strategyCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="strategyCfg"/>
    <result column="priceCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="priceCfg"/>
    <result column="dischargePriceCfg" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="dischargePriceCfg"/>
    <result column="chargeStrategy" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"
      property="chargeStrategy"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>

  <select id="getByCfgId" resultMap="RESULT_ESSCFG_PO">
    select * from t_ess_cfg where cfgId = #{cfgId}
  </select>

  <select id="getConnectedEssNum" resultType="java.lang.Long">
    select
    count(*)
    from
    t_ess
    where
    status != 99
    and cfgSuccessId = #{cfgId}
  </select>

  <select id="getIssuingEssNum" resultType="java.lang.Long">
    select
    count(*)
    from
    t_ess
    where
    status != 99
    and cfgId = #{cfgId}
  </select>

</mapper>

