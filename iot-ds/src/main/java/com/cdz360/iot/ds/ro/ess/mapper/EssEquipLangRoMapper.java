package com.cdz360.iot.ds.ro.ess.mapper;


import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssEquipLangRoMapper {


    EssEquipLangPo getById(@Param("id") Long id);


	List<EssEquipLangPo> getEquipLangList(@Param("vendor") String vendor,
		@Param("equipType") EssEquipType equipType,
		@Param("lang") String lang);
}

