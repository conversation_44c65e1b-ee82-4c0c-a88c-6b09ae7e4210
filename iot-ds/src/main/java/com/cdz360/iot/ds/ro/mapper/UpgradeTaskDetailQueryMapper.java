package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname UpgradeTaskDetailMapper
 * @Description TODO
 * @Date 9/18/2019 4:15 PM
 * @Created by Rafael
 */
@Mapper
public interface UpgradeTaskDetailQueryMapper {
    List<UpgradeTaskDetailVo> select(@Param("taskId") Long taskId,
                                     @Param("evseId") String evseId,
                                     @Param("status") List<UpdateTaskStatusEnum> status);

    long countUpdatingEvses(@Param("status") UpdateTaskStatusEnum status,
                            @Param("bundleId") Long bundleId);

}