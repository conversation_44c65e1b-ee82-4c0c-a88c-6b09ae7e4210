<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.UpgradeTaskDetailQueryMapper">
    <select id="select" resultType="com.cdz360.iot.model.evse.upgrade.UpgradeTaskDetailVo">
        select

        tutd.id AS id,
        tutd.taskId AS taskId,
        tutd.evseId AS evseId,
        te.name AS evseName,
        te.pc01Ver AS pc01Ver,
        te.pc02Ver AS pc02Ver,
        te.pc03Ver AS pc03Ver,
        tutd.status AS status,
        tutd.failReason AS failReason,
        tutd.updateTime AS updateTime


        from t_upgrade_task_detail tutd left join t_evse te
        on tutd.evseId=te.evseId
        <where>
            <if test="evseId != null and evseId != ''">
                and tutd.evseId=#{evseId}
            </if>
            <if test="taskId != null">
                and tutd.taskId=#{taskId}
            </if>
            <if test="status != null and status.size() >0 ">
                and tutd.status IN
                <foreach collection="status" index="index" item="item"
                         open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
<!--            <if test="order != null">-->
<!--                order by-->
<!--                <choose>-->
<!--                    <when test="order.name() == 'UPDATE_STATUS'">FIELD(status, 'FAIL', 'UPDATING', 'UPDATED')</when>-->
<!--                    <when test="order.name() == 'EVSE_NO'">evseId</when>-->
<!--                    <otherwise>id</otherwise>-->
<!--                </choose>-->
<!--            </if>-->
        </where>
    </select>
    <!-- 升级中的桩数 -->
    <select id="countUpdatingEvses" resultType="long">
        select
        count(*)
        from
        t_upgrade_task_detail A
        left join t_upgrade_task B on A.taskId=B.id
        <where>
            <if test="status != null">
                and A.status=#{status}
            </if>
            <if test="bundleId != null">
                and B.bundleId=#{bundleId}
            </if>
        </where>
    </select>
</mapper>
