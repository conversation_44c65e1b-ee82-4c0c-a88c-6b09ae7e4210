package com.cdz360.iot.ds;

import com.cdz360.base.model.iot.type.ODMType;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

@Slf4j
public class ODMTypeHandler extends BaseTypeHandler<ODMType> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ODMType parameter,
        JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getCode().replace("\"", ""));
    }

    @Override
    public ODMType getNullableResult(ResultSet rs, String columnName)
        throws SQLException {
        String code = rs.getString(columnName);
        return code == null ? null : ODMType.valueOf((Object) (code));
    }

    @Override
    public ODMType getNullableResult(ResultSet rs, int columnIndex)
        throws SQLException {
        String code = rs.getString(columnIndex);
        return code == null ? null : ODMType.valueOf((Object) (code));
    }

    @Override
    public ODMType getNullableResult(CallableStatement cs, int columnIndex)
        throws SQLException {
        String code = cs.getString(columnIndex);
        return code == null ? null : ODMType.valueOf((Object) (code));
    }
}
