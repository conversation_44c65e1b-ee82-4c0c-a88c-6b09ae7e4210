package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.EvsePackageRwMapper;
import com.cdz360.iot.model.evse.param.UpdateEvsePackageParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 海外平台升级包管理
 */
@Service
public class EvsePackageRwDs {

    @Autowired
    private EvsePackageRwMapper evsePackageRwMapper;

    public void updatePackage(UpdateEvsePackageParam param) {
        evsePackageRwMapper.updatePackage(param);
    }

    public int create(UpdateEvsePackageParam param) {
        return evsePackageRwMapper.create(param);
    }

    public int disablePackageByCode(String code) {
        return evsePackageRwMapper.disablePackageByCode(code);
    }
}