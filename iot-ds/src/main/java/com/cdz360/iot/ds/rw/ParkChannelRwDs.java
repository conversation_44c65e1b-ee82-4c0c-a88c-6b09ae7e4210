package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.ParkChannelRwMapper;
import com.cdz360.iot.model.park.po.ParkChannelPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class ParkChannelRwDs {



	@Autowired

	private ParkChannelRwMapper parkChannelRwMapper;



	public ParkChannelPo getById(Integer id, boolean lock) {

		return this.parkChannelRwMapper.getById(id, lock);

	}



	public boolean insertParkChannel(ParkChannelPo parkChannelPo) {

		return this.parkChannelRwMapper.insertParkChannel(parkChannelPo) > 0;

	}



	public boolean updateParkChannel(ParkChannelPo parkChannelPo) {

		return this.parkChannelRwMapper.updateParkChannel(parkChannelPo) > 0;

	}





}

