package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.pcs.po.PcsPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface PcsRwMapper {

    int updatePcs(PcsPo po);

    int batchUpsetPcs(@Param("poList") List<PcsPo> poList);

    int offlinePcs(
        @NonNull @Param("essDno") String essDno,
        @Nullable @Param("remainDnoList") List<String> remainDnoList);

}

