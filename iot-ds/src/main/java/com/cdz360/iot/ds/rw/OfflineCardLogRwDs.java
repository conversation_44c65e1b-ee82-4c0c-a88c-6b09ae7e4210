package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.OfflineCardLogRwMapper;
import com.cdz360.iot.model.evse.po.OfflineCardLogPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OfflineCardLogRwDs {
    @Autowired
    private OfflineCardLogRwMapper offlineCardLogRwMapper;

    public void addOfflineCardLog(OfflineCardLogPo cardLog) {
        this.offlineCardLogRwMapper.addOfflineCardLog(cardLog);
    }
}
