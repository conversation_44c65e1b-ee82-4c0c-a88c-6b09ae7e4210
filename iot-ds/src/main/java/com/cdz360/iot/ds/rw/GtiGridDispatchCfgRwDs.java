package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.GtiGridDispatchCfgRwMapper;
import com.cdz360.iot.model.pv.po.GtiGridDispatchCfgPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GtiGridDispatchCfgRwDs {

    @Autowired
    private GtiGridDispatchCfgRwMapper mapper;

    public boolean insertGtiCfg(GtiGridDispatchCfgPo gtiCfgPo) {
        return mapper.insertGtiCfg(gtiCfgPo) > 0;
    }

    public boolean updateGtiCfg(GtiGridDispatchCfgPo gtiCfgPo) {
        return mapper.updateGtiCfg(gtiCfgPo) > 0;
    }

}

