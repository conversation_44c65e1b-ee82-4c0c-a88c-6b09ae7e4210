package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.parts.po.TransOrderPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TransOrderRwMapper {

    TransOrderPo getByOrderNo(@Param("orderNo") String orderNo, @Param("lock") boolean lock);

    int insertTransOrder(TransOrderPo TransOrderPo);

    int updateTransOrder(TransOrderPo TransOrderPo);

    int postPartsReceive(@Param("partsCode") String partsCode);
}

