<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.EvseModuleDetailRwMapper">




	<insert id="insert" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.evse.po.EvseModuleDetailPo">
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( poList )">
		INSERT INTO t_evse_module_detail
			(moduleId,
			idx,
			deviceNo,
			oldDeviceNo,
			updateTime)
		VALUES
			<foreach collection="poList" open="" close=""
					 separator="," item="po">
				(#{po.moduleId},
				#{po.idx},
				#{po.deviceNo},
				#{po.oldDeviceNo, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
				#{po.updateTime})
			</foreach>
		</if>
	</insert>

	<delete id="delete">
		delete from
			t_evse_module_detail
		where
			id in
			<foreach collection="idList" item="id"
					 open="(" separator="," close=")">
				#{id}
			</foreach>
	</delete>

	<delete id="deleteByNumber">
		delete from
			t_evse_module_detail
		where
			moduleId = #{moduleId}
			and idx > #{number}
	</delete>

	<update id="updateById">
		update
			t_evse_module_detail
		set
			deviceNo = #{deviceNo},
			oldDeviceNo = #{oldDeviceNo, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
			updateTime = #{updateTime}
		where id = #{id}
	</update>

	<update id="insertOrUpdate">
		INSERT INTO t_evse_module_detail
			(moduleId, idx, deviceNo,
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( oldDeviceNo )">
			oldDeviceNo,
		</if>
			updateTime)
		VALUES
			(#{moduleId}, #{idx}, #{deviceNo},
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( oldDeviceNo )">
			#{oldDeviceNo, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
		</if>
			#{updateTime})
		on DUPLICATE key update
			deviceNo=#{deviceNo},
		<if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( oldDeviceNo )">
			oldDeviceNo=#{oldDeviceNo, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
		</if>
			updateTime=#{updateTime}
	</update>

</mapper>
