package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.ListEvseParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EvseRwQueryMapper {

    int insert(EvsePo evse);

    int update(EvsePo evse);

    int updateById(EvsePo evse);

    int batchUpdate(@Param(value = "list") List<EvsePo> evses);

    int updateByEvseId(EvsePo evse);

    int unBindSite(EvsePo evse);

    EvsePo getEvsePo(@Param("evseId") String evseId, @Param("lock") boolean lock);

    List<EvsePo> getEvsePos(@Param("evseIds") List<String> evseIds, @Param("lock") boolean lock);

    int removeByEvseId(@Param("evseId") String evseId);

    List<EvsePo> listBySiteId(@Param("siteId") String siteId);

    List<EvsePo> listEvse(@Param("statusList") List<EvseStatus> statusList,
        @Param("start") long start,
        @Param("size") int size);

    List<EvsePo> getEvseList(ListEvseParam param);

    List<EvsePo> getOnlineAndNotBoundEvseList(BaseListParam param);

    Long getOnlineAndNotBoundEvseListCount();

    List<EvsePo> listBySiteIdForUpgrade(ListEvseParam param);

    List<EvsePo> getUpgradeStatus(@Param("evseIdList") List<String> evseIdList);

    int updateDebugTag(@Param("evseId") String evseId, @Param("debugTag") Boolean debugTag);

    int clearSimConfig(@Param("evseId") String evseId);

    int clearSimByIccid(@Param("iccid") String iccid);

}
