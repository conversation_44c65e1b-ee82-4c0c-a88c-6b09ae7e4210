<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteCtrlCfgRwMapper">

    <resultMap id="BaseResultMap" type="com.cdz360.iot.model.site.po.SiteCtrlCfgPo" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <id column="pwrCtrlLmt" property="pwrCtrlLmt" jdbcType="VARCHAR" typeHandler="com.cdz360.iot.ds.MybatisJsonArrayTypeHandler"/>
    </resultMap>

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.site.po.SiteCtrlCfgPo">
        insert into t_site_ctrl_cfg
        (
        `ctrlNum`
        <if test="pwrCtrl != null">
            ,`pwrCtrl`
        </if>
        <if test="infoUp != null">
            ,`infoUp`
        </if>
        <if test="pwrLoadAlm != null">
            ,`pwrLoadAlm`
        </if>
        <if test="pwrTempAlm != null">
            ,`pwrTempAlm`
        </if>
        <if test="chgFireAlm != null">
            ,`chgFireAlm`
        </if>
        <if test="chgDoorAlm != null">
            ,`chgDoorAlm`
        </if>
        <if test="pwrCap != null">
            ,`pwrCap`
        </if>
        <if test="pwrCtrlLmt != null">
            ,`pwrCtrlLmt`
        </if>
        <if test="tempSample != null">
            ,`tempSample`
        </if>
        <if test="pwrSample != null">
            ,`pwrSample`
        </if>
        <if test="infoUpLoop != null">
            ,`infoUpLoop`
        </if>
        <if test="pwrLoadLmt != null">
            ,`pwrLoadLmt`
        </if>
        <if test="pwrTempLmt != null">
            ,`pwrTempLmt`
        </if>
        ) values(
        #{ctrlNum}
        <if test="pwrCtrl != null">
            ,#{pwrCtrl}
        </if>
        <if test="infoUp != null">
            ,#{infoUp}
        </if>
        <if test="pwrLoadAlm != null">
            ,#{pwrLoadAlm}
        </if>
        <if test="pwrTempAlm != null">
            ,#{pwrTempAlm}
        </if>
        <if test="chgFireAlm != null">
            ,#{chgFireAlm}
        </if>
        <if test="chgDoorAlm != null">
            ,#{chgDoorAlm}
        </if>
        <if test="pwrCap != null">
            ,#{pwrCap}
        </if>
        <if test="pwrCtrlLmt != null">
            ,#{pwrCtrlLmt, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler}
        </if>
        <if test="tempSample != null">
            ,#{tempSample}
        </if>
        <if test="pwrSample != null">
            ,#{pwrSample}
        </if>
        <if test="infoUpLoop != null">
            ,#{infoUpLoop}
        </if>
        <if test="pwrLoadLmt != null">
            ,#{pwrLoadLmt}
        </if>
        <if test="pwrTempLmt != null">
            ,#{pwrTempLmt}
        </if>
        )
        on DUPLICATE key UPDATE
        <if test="pwrCtrl != null">
            pwrCtrl = #{pwrCtrl},
        </if>
        <if test="infoUp != null">
            infoUp = #{infoUp},
        </if>
        <if test="pwrLoadAlm != null">
            pwrLoadAlm = #{pwrLoadAlm},
        </if>
        <if test="pwrTempAlm != null">
            pwrTempAlm = #{pwrTempAlm},
        </if>
        <if test="chgFireAlm != null">
            chgFireAlm = #{chgFireAlm},
        </if>
        <if test="chgDoorAlm != null">
            chgDoorAlm = #{chgDoorAlm},
        </if>
        <if test="pwrCap != null">
            pwrCap = #{pwrCap},
        </if>
        <if test="pwrCtrlLmt != null">
            pwrCtrlLmt = #{pwrCtrlLmt, typeHandler=com.cdz360.iot.ds.MybatisJsonTypeHandler},
        </if>
        <if test="tempSample != null">
            tempSample = #{tempSample},
        </if>
        <if test="pwrSample != null">
            pwrSample = #{pwrSample},
        </if>
        <if test="infoUpLoop != null">
            infoUpLoop = #{infoUpLoop},
        </if>
        <if test="pwrLoadLmt != null">
            pwrLoadLmt = #{pwrLoadLmt},
        </if>
        <if test="pwrTempLmt != null">
            pwrTempLmt = #{pwrTempLmt},
        </if>
        updateTime = now()
    </insert>
</mapper>