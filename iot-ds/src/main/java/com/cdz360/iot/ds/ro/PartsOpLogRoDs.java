package com.cdz360.iot.ds.ro;


import com.cdz360.iot.ds.ro.mapper.PartsOpLogRoMapper;
import com.cdz360.iot.model.parts.po.PartsOpLogPo;
import com.cdz360.iot.model.parts.vo.PartsOpLogVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsOpLogRoDs {

    @Autowired
    private PartsOpLogRoMapper partsOpLogRoMapper;

    public PartsOpLogPo getById(Long id) {
        return this.partsOpLogRoMapper.getById(id);
    }

    public List<PartsOpLogVo> getAllByPartsCode(String partsCode) {
        return this.partsOpLogRoMapper.getAllByPartsCode(partsCode);
    }
}

