package com.cdz360.iot.ds.ro;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.ds.ro.mapper.EvseModelRoMapper;
import com.cdz360.iot.model.evse.param.ListEvseModelParam;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.gw.vo.EvseOpInfoVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
public class EvseModelRoDs {

    @Autowired
    private EvseModelRoMapper mapper;

    public EvseModelPo findById(Long id) {
        return mapper.findById(id);
    }

    public ListResponse<EvseModelPo> getList(ListEvseModelParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }
        List<EvseModelPo> data = mapper.getList(param);
        long total = mapper.getListCount(param);
        return new ListResponse<>(data, total);
    }

    public EvseModelPo findByName(@NonNull String exactName) {
        return mapper.findByName(exactName);
    }

    public long countByCondition(@Nullable Boolean enable, @Nullable String exactModel,
                                 @Nullable String exactBrand, @Nullable String exactSeries,
                                 @Nullable SupplyType exactSupply, @Nullable Integer exactPower,
                                 @Nullable Integer exactPlugNum) {
        return mapper.countByCondition(enable, exactModel, exactBrand,
                exactSeries, exactSupply, exactPower, exactPlugNum);
    }

    /**
     * 校验设备型号+品牌+额定功率+枪头数量库中是否存在，匹配到多个时取最近那条数据
     * （需有效且未停用）
     */
    public EvseModelPo findByExactFields(@NonNull String exactModel,
                                         @NonNull String exactBrand,
                                         @NonNull Integer exactPower,
                                         @NonNull Integer exactPlugNum) {
        return mapper.findOneByCondition(Boolean.TRUE, exactModel, exactBrand, null,
                null, exactPower, exactPlugNum);
    }

    public EvseModelPo findByExactFields(@NonNull String exactModel,
                                         @NonNull String exactBrand,
                                         @NonNull String exactSeries,
                                         @NonNull SupplyType exactSupply,
                                         @NonNull Integer exactPower,
                                         @NonNull Integer exactPlugNum) {
        return mapper.findOneByCondition(Boolean.TRUE, exactModel, exactBrand, exactSeries,
                exactSupply, exactPower, exactPlugNum);
    }

    public long check(long id) {
        return this.mapper.check(id);
    }

    public EvseOpInfoVo getEvseOpInfoByEvseId(String evseId) {
        return mapper.getEvseOpInfoByEvseId(evseId);
    }

    public List<EvseModelPo> getList(List<Long> idList) {
        ListEvseModelParam param = new ListEvseModelParam();
        param.setModelIdList(idList);
        return mapper.getList(param);
    }

    public List<String> getBrandList() {
        return mapper.getBrandList();
    }

}
