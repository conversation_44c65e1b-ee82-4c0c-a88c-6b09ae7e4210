<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.ro.pv.mapper.GtiDailyRoMapper">



	<resultMap id="RESULT_GTIDAILY_PO" type="com.cdz360.iot.model.pv.po.GtiDailyPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="date" jdbcType="DATE" property="date" />

		<result column="gtiId" jdbcType="BIGINT" property="gtiId" />

		<result column="dno" jdbcType="VARCHAR" property="dno" />

		<result column="siteId" jdbcType="VARCHAR" property="siteId" />

		<result column="totalKwh" jdbcType="DECIMAL" property="totalKwh" />

		<result column="totalHour" jdbcType="INTEGER" property="totalHour" />

		<result column="todayKwh" jdbcType="DECIMAL" property="todayKwh" />
		<result column="todayKwh" jdbcType="DECIMAL" property="todayKwh" />

		<result column="enable" jdbcType="BOOLEAN" property="enable" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_GTIDAILY_PO">	
		select * from t_gti_daily where id = #{id}
		and enable = true
	</select>
	<select id="gtiInfoInTime" resultType="com.cdz360.iot.model.pv.vo.GtiDataInTimeVo">
		select gti.dno dno, '光伏逆变器' type, gti.name name, gti.vendor vendor,
		cfg.timeout, cfg.bootVoltage, cfg.minVoltage, cfg.maxVoltage, cfg.minFrequency, cfg.maxFrequency
		from t_gti gti
		left join t_gti_cfg cfg on cfg.cfgId = gti.cfgSuccessId
		where gti.dno = #{dno}
	</select>
    <select id="rtDataOfMonth"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.SitePvRtDataBi">
		select sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where year(daily.`date`) = #{year} and month(daily.`date`) = #{month}
			and daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
	</select>
    <select id="rtDataOfTotal"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.SitePvRtDataBi">
		select sum(todayKwh) totalKwh, sum(todayProfit) totalProfit,count(distinct date) as totalDay
		from t_gti_daily daily
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
	</select>

    <select id="siteRtDataOfMonth"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.SitePvRtDataBi">
		select daily.siteId siteId, sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where year(daily.`date`) = #{year} and month(daily.`date`) = #{month}
			and daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		group by month(daily.`date`) , daily.siteId
	</select>
    <select id="siteRtDataOfTotal"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.SitePvRtDataBi">
		select daily.siteId siteId, sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		group by daily.siteId
	</select>

    <select id="siteDayOfMonthKwh"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.DaySitePvRtDataBi">
		select daily.`date` date, daily.siteId siteId,
		sum(tg.power) totalPower, sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		inner join t_gti tg on daily.dno = tg.dno
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where daily.enable = true
		<if test="year != null">
			and year(daily.`date`) = #{year}
		</if>
		<if test="month != null">
			and month(daily.`date`) = #{month}
		</if>
		<if test="recentDays != null">
			and daily.`date` >= date_sub(curdate(), interval #{recentDays} day)
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		group by daily.`date` , daily.siteId
	</select>

	<sql id="siteRtDataOfWhat">
		select daily.`date` date, sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		where daily.`date` <![CDATA[ >= ]]> #{fromDate}
		and daily.`date` <![CDATA[ < ]]> #{toDate}
		and daily.siteId = #{siteId}
		and daily.enable = true
		group by daily.`date`
		order by daily.`date`
	</sql>

	<select id="siteRtDataOfDay"
			resultType="com.cdz360.iot.model.pv.vo.DayPvDataBi">
		<include refid="siteRtDataOfWhat"/>
	</select>

	<select id="siteRtDataOfMonthGroup"
			resultType="com.cdz360.iot.model.pv.vo.DayPvDataBi">
		select
			y.date as date,
			sum(totalKwh) as totalKwh,
			sum(totalProfit) as totalProfit
		from (
			select
				date_sub(x.date, interval day(x.date)-1 day) as date,
				x.totalKwh,
				x.totalProfit
			from (
		<include refid="siteRtDataOfWhat"/>
			) x
		) y
		group by y.date
		order by date
	</select>

	<select id="getGtiTinyBi" resultType="com.cdz360.iot.model.pv.vo.GtiTinyBi">
		select
			siteId ,
			IFNULL(sum(power), 0) as totalPower
		from
			t_gti
		where
			status != 99
			and siteId in
		<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
			#{siteId}
		</foreach>
		group by
			siteId
	</select>
	<select id="siteDayOfYearKwh" resultType="com.cdz360.iot.model.pv.vo.DaySitePvRtDataBi">
		select DATE_FORMAT(daily.`date`,'%Y-%m-01') firstDayOfMonth, daily.siteId siteId,
		sum(ifnull(tg.power,0)) totalPower, sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		inner join t_gti tg on daily.dno = tg.dno
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where daily.enable = true
		<if test="date != null">
			<![CDATA[ and daily.date >= #{date.startTime} ]]>
			<![CDATA[ and daily.date <= #{date.endTime} ]]>
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		group by firstDayOfMonth , daily.siteId
	</select>
	<select id="rtDataOfYear"
			parameterType="com.cdz360.iot.model.pv.param.DayKwhParam"
			resultType="com.cdz360.iot.model.pv.vo.SitePvRtDataBi">
		select sum(todayKwh) totalKwh, sum(todayProfit) totalProfit
		from t_gti_daily daily
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where year(daily.`date`) = #{year}
		and daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
	</select>

	<select id="gtiRtDataSample"
			parameterType="com.cdz360.iot.model.pv.param.DataBiParam"
			resultType="com.cdz360.iot.model.pv.vo.GtiSampleData">
		select
		<choose>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@DAY">
				daily.`date` time,
			</when>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@MONTH">
				DATE_FORMAT(any_value(daily.`date`) , '%Y-%m-01 00:00:00') time,
			</when>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@YEAR">
				DATE_FORMAT(any_value(daily.`date`) , '%Y-01-01 00:00:00') time,
			</when>
		</choose>
		sum(todayKwh) kwh, sum(todayProfit) profit,
		ROUND(sum(todayKwh / gti.power), 4) equivalentTime
		from t_gti_daily daily
		left join t_gti gti on daily.dno = gti.dno
		left join t_site site on site.dzId = daily.siteId
		left join t_r_commercial comm on comm.id = site.commId
		where daily.`date` <![CDATA[ >= ]]> #{fromDate}
		and daily.`date` <![CDATA[ < ]]> #{toDate}
		and daily.enable = true
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and comm.`idChain` like concat(#{commIdChain}, '%')
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
			and daily.siteId = #{siteId}
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
			and daily.siteId in
			<foreach collection="siteIdList" item="siteId" open="(" close=")" separator=",">
				#{siteId}
			</foreach>
		</if>
		<choose>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@DAY">
				group by daily.`date`
			</when>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@MONTH">
				group by DATE_FORMAT(daily.`date`, '%Y-%m')
			</when>
			<when test="sampleType == @com.cdz360.iot.model.type.SiteBiSampleType@YEAR">
				group by year(daily.`date`)
			</when>
		</choose>
	</select>

	<select id="getPvMapDataVo" resultType="com.cdz360.iot.model.pv.vo.PvDataBi">
		select
			sum(gd.todayKwh) totalKwh,
			sum(gd.todayProfit) totalProfit
		from t_gti_daily gd
		where gd.siteId in (
			select distinct siteId
			from t_site site
			left join t_r_site_group_site_ref r on r.siteId = site.dzId
			where r.gid in
			<foreach collection="gids" item="gid" open="(" close=")" separator=",">
				#{gid}
			</foreach>
		)
	</select>

</mapper>

