package com.cdz360.iot.ds.rw.ess.ds;

import com.cdz360.iot.ds.rw.ess.mapper.EssDtuEssRefRwMapper;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDtuEssRefRwDs {

    @Autowired
    private EssDtuEssRefRwMapper essDtuEssRefRwMapper;

    public EssDtuEssRefPo getBySerialNoAndDno(String serialNo, String dno, boolean lock) {
        return this.essDtuEssRefRwMapper.getBySerialNoAndDno(serialNo, dno, lock);
    }

    public boolean insertEssDtuEssRef(EssDtuEssRefPo essDtuEssRefPo) {
        return this.essDtuEssRefRwMapper.insertEssDtuEssRef(essDtuEssRefPo) > 0;
    }

}

