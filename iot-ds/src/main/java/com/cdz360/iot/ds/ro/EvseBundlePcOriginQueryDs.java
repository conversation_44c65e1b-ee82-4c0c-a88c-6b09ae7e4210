package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.EvseBundlePcOriginQueryMapper;
import com.cdz360.iot.model.evse.EvseBundlePcOrigin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname EvseBundlePcOriginQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:35 PM
 * @Created by Rafael
 */
@Service
public class EvseBundlePcOriginQueryDs {
    @Autowired
    private EvseBundlePcOriginQueryMapper evseBundlePcOriginQueryMapper;

    public EvseBundlePcOrigin selectByPrimaryKey (Long id) {
        return evseBundlePcOriginQueryMapper.selectByPrimaryKey(id);
    }

    public List<EvseBundlePcOrigin> selectByBundleId(Long bundleId) {
        return evseBundlePcOriginQueryMapper.selectByBundleId(bundleId);
    }
}