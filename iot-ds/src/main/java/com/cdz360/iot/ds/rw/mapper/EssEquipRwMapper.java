package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EquipNameplateInfo;
import com.cdz360.iot.model.type.EquipAlertStatus;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface EssEquipRwMapper {

    EssEquipPo getById(@Param("id") Long id, @Param("lock") boolean lock);


    int insertEssEquip(EssEquipPo essEquipPo);


    int updateEssEquip(EssEquipPo essEquipPo);


    int batchUpset(@Param("poList") List<EssEquipPo> poList);

    int offEquip(@Param("essDno") String essDno,
        @Param("onlineEquipIdList") List<Long> onlineEquipIdList,
        @Param("status") EquipStatus status);

    int updateEssEquipStatus(@Param("essDno") String essDno,
        @Param("equipIdList") List<Long> equipIdList,
        @Param("status") EquipStatus status,
        @Param("alertStatus") EquipAlertStatus alertStatus);

    int offlineByEssDno(@Param("essDno") String essDno);

    int enableByEssDno(
        @Param("essDno") String essDno,
        @Param("enable") Boolean enable,
        @Param("excludeDnoList") List<String> excludeDnoList);

    int updateEssEquipNameplate(
        @Param("essDno") String essDno,
        @Param("equipId") Long equipId,
        @Param("nameplate") EquipNameplateInfo nameplate);
}

