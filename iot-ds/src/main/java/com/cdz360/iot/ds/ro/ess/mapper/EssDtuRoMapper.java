package com.cdz360.iot.ds.ro.ess.mapper;

import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.vo.EssMapDataVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EssDtuRoMapper {

    EssDtuPo getBySerialNo(@Param("serialNo") String serialNo);

    EssDtuPo getByEssDno(@Param("essDno") String essDno);

    List<EssMapDataVo> essMapData(EssMapDataParam param);
}

