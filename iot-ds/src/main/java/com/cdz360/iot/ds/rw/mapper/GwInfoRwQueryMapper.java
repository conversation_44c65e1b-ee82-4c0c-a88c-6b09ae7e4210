package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.gw.GwTimeoutPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.GwStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GwInfoRwQueryMapper {

    int insert(GwInfoPo gwInfo);

    int update(GwInfoPo gwInfo);

    int updateStatus(@Param("gwno") String gwno, @Param("status") String gwStatus);

    int updateLoginTime(@Param("gwno") String gwno);

    GwInfoDto getByGwno(@Param("gwno") String gwno, @Param("lock") boolean lock);

    GwInfoDto getByGwnoAndEnable(@Param("gwno") String gwno, @Param("lock") boolean lock);

    List<GwInfoDto> getByMac(@Param("mac") String mac, @Param("lock") boolean lock);

    int updateLocation(@Param("gwno") String gwno, @Param("cityCode") String cityCode,
                       @Param("lon") Double lon, @Param("lat") Double lat);

    List<GwInfoDto> getGwList(@Param("gwnos") List<String> gwnos, @Param("status") GwStatus staus);


    List<GwInfoPo> listGw(@Param("statusList") List<GwStatus> statusList,
                          @Param("start") long start, @Param("size") int size);

    List<GwTimeoutPo> getGwnosLoginTout(@Param("timeout") Integer minute);

    int upset(GwInfoPo gw);
}
