<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.EvseModuleRoMapper">

    <select id="getList" resultType="com.cdz360.iot.model.evse.po.EvseModulePo">
        select *
        from t_evse_module
        order by createTime desc
        <if test="start != null and size != null">
            limit #{start}, #{size}
        </if>
    </select>

    <select id="findChargingModule" resultType="com.cdz360.iot.model.evse.po.EvseModulePo">
        select
            *
        from
            t_evse_module
        where
            enable = true
            and deviceName = #{deviceName}
            and moduleType is not null
            and evseNo = #{evseNo}
        order by
            createTime desc
        limit 1
    </select>

    <resultMap id="BASE" type="com.cdz360.iot.model.evse.vo.DeviceVo">
        <id column="mId" jdbcType="BIGINT" property="id" />
        <result column="deviceName" jdbcType="VARCHAR" property="deviceName" />
        <result column="moduleType" jdbcType="VARCHAR" property="moduleType" />
        <collection property="detailList" ofType="com.cdz360.iot.model.evse.po.EvseModuleDetailPo" javaType="list">
            <id column="mdId" jdbcType="BIGINT" property="id" />
            <result column="idx" jdbcType="INTEGER" property="idx" />
            <result column="deviceNo" jdbcType="VARCHAR" property="deviceNo" />
            <result column="oldDeviceNo" property="oldDeviceNo" typeHandler="com.cdz360.iot.ds.MybatisJsonTypeHandler"/>
            <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
        </collection>
    </resultMap>

    <select id="getByEvseNo" resultMap="BASE">
        select
            m.id as mId,
            m.deviceName ,
            m.moduleType ,
            md.id as mdId,
            md.idx,
            md.deviceNo ,
            md.oldDeviceNo ,
            md.updateTime
        from
            t_evse_module m
        inner join t_evse_module_detail md on
            m.id = md.moduleId
        where
            m.enable = true
            and m.evseNo = #{evseNo}
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceName )">
            and m.deviceName = #{deviceName}
        </if>
        order by
            md.id asc
        <if test="start != null and size != null">
            limit #{start}, #{size}
        </if>
    </select>

    <select id="getBySiteId" resultMap="BASE">
        select
            m.id as mId,
            m.deviceName ,
            m.moduleType ,
            md.id as mdId,
            md.idx,
            md.deviceNo ,
            md.oldDeviceNo ,
            md.updateTime
        from
            t_evse_module m
        inner join t_evse_module_detail md on
            m.id = md.moduleId
        left join t_evse evse
            on evse.evseId = m.evseNo
        where
            m.enable = true
            and evse.siteId = #{siteId}
        order by
        md.id asc
        <if test="start != null and size != null">
            limit #{start}, #{size}
        </if>
    </select>

    <select id="getByEvseNoCount" resultType="java.lang.Long">
        select
            count(md.id)
        from
            t_evse_module m
        inner join t_evse_module_detail md on
            m.id = md.moduleId
        where
            m.enable = true
            and m.evseNo = #{evseNo}
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( deviceName )">
            and m.deviceName = #{deviceName}
        </if>
    </select>

    <select id="queryByEvseNo" resultType="com.cdz360.iot.model.evse.po.EvseModulePo">
        select
            *
        from
            t_evse_module
        where
            evseNo = #{evseNo}
    </select>

    <select id="getByDeviceNo" resultType="com.cdz360.iot.model.evse.vo.EvseDeviceVo">
        select
            m.evseNo ,
            e.name as evseName ,
            md.idx as deviceIdx
        from
            t_evse_module m
        inner join t_evse_module_detail md on
            m.id = md.moduleId
        inner join t_evse e on
            m.evseNo = e.evseId
        <where>
            <if test="@com.cdz360.iot.common.utils.CollectionUtils@isNotEmpty( evseNoList )">
                m.evseNo in
                <foreach collection="evseNoList" item="evseNo" open="(" separator="," close=")">
                    #{evseNo}
                </foreach>
            </if>
            and (md.deviceNo = #{deviceNo}
            or md.oldDeviceNo like concat('[{"no": "', #{deviceNo}, '%'))
        </where>
        order by
            md.id desc
        limit 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.param.DeviceParam">
        insert into t_evse_module(
        evseNo,
        deviceName,
        moduleType,
        number,
        enable,
        createTime,
        updateTime
        ) values (
        #{param.evseNo},
        #{param},
        #{param},
        #{param},
        #{param},
        now(),
        now()
        )
    </insert>

    <update id="update" parameterType="com.cdz360.iot.model.param.DeviceParam">
    update t_upgrade_task_detail
    </update>

</mapper>