package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SiteCtrlCfgRwMapper;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname SiteCtrlCfgRoDs
 * @Description
 * @Date 4/22/2020 7:23 PM
 * @Created by Rafael
 */
@Service
public class SiteCtrlCfgRwDs {
    @Autowired
    private SiteCtrlCfgRwMapper siteCtrlCfgRwMapper;

    public int insertOrUpdate(SiteCtrlCfgPo param) {
        return siteCtrlCfgRwMapper.insertOrUpdate(param);
    }
}