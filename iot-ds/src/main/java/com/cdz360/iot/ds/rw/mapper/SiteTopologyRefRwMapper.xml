<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SiteTopologyRefRwMapper">

	<resultMap id="RESULT_SITETOPOLOGYREF_PO" type="com.cdz360.iot.model.topology.po.SiteTopologyRefPo">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="upType" jdbcType="VARCHAR" property="upType" />
		<result column="upId" jdbcType="INTEGER" property="upId" />
		<result column="downType" jdbcType="VARCHAR" property="downType" />
		<result column="downId" jdbcType="INTEGER" property="downId" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_SITETOPOLOGYREF_PO">	
		select * from t_site_topology_ref where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertSiteTopologyRef" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.iot.model.topology.po.SiteTopologyRefPo">
		insert into t_site_topology_ref (`upType`,
			`upId`,
			`downType`,
			`downId`,
			`createTime`,
			`updateTime`)
		values (#{upType},
			#{upId},
			#{downType},
			#{downId},
			now(),
			now())
	</insert>

	<update id="updateSiteTopologyRef" parameterType="com.cdz360.iot.model.topology.po.SiteTopologyRefPo">
		update t_site_topology_ref set
		<if test="upType != null">
			upType = #{upType},
		</if>
		<if test="upId != null">
			upId = #{upId},
		</if>
		<if test="downType != null">
			downType = #{downType},
		</if>
		<if test="downId != null">
			downId = #{downId},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

	<delete id="deleteAllDown">
		delete from t_site_topology_ref
		<where>
			upId = #{upId}
		</where>
	</delete>

	<insert id="batchInsertSiteTopologyRef" useGeneratedKeys="true" keyProperty="id"
			keyColumn="id" parameterType="com.cdz360.iot.model.topology.po.SiteTopologyRefPo">
		insert into t_site_topology_ref (
			`upType`,
			`upId`,
			`downType`,
			`downId`,
			`createTime`,
			`updateTime`
		)
		values
		<foreach collection="list" item="item" separator=",">
			(
				#{item.upType},
				#{item.upId},
				#{item.downType},
				#{item.downId},
				now(),
				now()
			)
		</foreach>
	</insert>

</mapper>
