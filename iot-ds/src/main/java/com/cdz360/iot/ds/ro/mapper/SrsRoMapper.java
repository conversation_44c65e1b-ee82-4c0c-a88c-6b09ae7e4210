package com.cdz360.iot.ds.ro.mapper;


import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.srs.po.SrsPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface SrsRoMapper {

    List<SrsPo> getByGwno(ListCtrlParam param);

    SrsPo getOneByGwno(@Param("gwno") String gwno);

    SrsPo getByDno(@Param("dno") String dno);


}

