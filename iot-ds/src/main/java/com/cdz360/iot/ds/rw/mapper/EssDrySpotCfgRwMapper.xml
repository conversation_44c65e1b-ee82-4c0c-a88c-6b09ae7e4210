<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssDrySpotCfgRwMapper">



	<resultMap id="RESULT_ESSDRYSPOTCFG_PO" type="com.cdz360.iot.model.ess.po.EssDrySpotCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="enable" jdbcType="BOOLEAN" property="enable" />

		<result column="ctrlMode" property="ctrlMode" />

		<result column="rangeTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="rangeTime" />

		<result column="socThreshold" jdbcType="INTEGER" property="socThreshold" />

		<result column="weekEnable" jdbcType="INTEGER" property="weekEnable" />

	</resultMap>



	<insert id="insertEssDrySpotCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssDrySpotCfgPo">

		insert into t_ess_dry_spot_cfg (`cfgId`,

			`enable`,

			`ctrlMode`,

			`rangeTime`,

			`socThreshold`,

			`weekEnable`)

		values (#{cfgId},

			#{enable},

			#{ctrlMode},

			#{rangeTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			#{socThreshold},

			#{weekEnable})

		ON DUPLICATE KEY UPDATE

			enable = #{enable},

			ctrlMode = #{ctrlMode},

			rangeTime = #{rangeTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			socThreshold = #{socThreshold},

			weekEnable = #{weekEnable}

	</insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_dry_spot_cfg(cfgId,enable,ctrlMode,rangeTime,socThreshold,weekEnable)
		select #{newId}, enable,ctrlMode,rangeTime,socThreshold,weekEnable from t_ess_dry_spot_cfg where cfgId = #{oldId};
	</insert>


</mapper>

