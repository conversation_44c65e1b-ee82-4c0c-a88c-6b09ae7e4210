package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.UpgradeTaskRelQueryMapper;
import com.cdz360.iot.model.evse.upgrade.UpgradeTaskRelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname UpgradeTaskRelQueryDs
 * @Description TODO
 * @Date 9/21/2019 3:10 PM
 * @Created by Rafael
 */
@Service
public class UpgradeTaskRelQueryDs {
    @Autowired
    private UpgradeTaskRelQueryMapper upgradeTaskRelQueryMapper;

    public UpgradeTaskRelVo selectById(Long id) {
        return upgradeTaskRelQueryMapper.selectById(id);
    }
}