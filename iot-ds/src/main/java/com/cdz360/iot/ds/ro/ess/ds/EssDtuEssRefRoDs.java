package com.cdz360.iot.ds.ro.ess.ds;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.ess.mapper.EssDtuEssRefRoMapper;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssDtuEssRefRoDs {

    @Autowired
    private EssDtuEssRefRoMapper essDtuEssRefRoMapper;

    public EssDtuEssRefPo getBySerialNoAndDno(String serialNo, String dno) {
        return this.essDtuEssRefRoMapper.getBySerialNoAndDno(serialNo, dno);
    }

    public List<EssDtuEssRefPo> getBySerialNo(String serialNo) {
        return this.essDtuEssRefRoMapper.getBySerialNo(serialNo);
    }
}

