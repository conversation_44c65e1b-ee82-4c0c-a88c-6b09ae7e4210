package com.cdz360.iot.ds.ro.pv.mapper;

import com.cdz360.iot.model.pv.dto.UpdateCtrlDeviceDto;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.GtiStatusBi;
import com.cdz360.iot.model.pv.vo.GtiVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface GtiRoMapper {

	GtiPo getByGwnoAndDno(@Param("gwno") String gwno,
						  @Param("dno") String dno);

	GtiPo getByDno(@Param("dno") String dno);

	List<GtiVo> findGtiList(ListGtiParam param);

	Long count(ListGtiParam param);

	Long getCountByCfgId(Long cfgId);

    GtiPo getByName(@Param("includeDno") Boolean includeDno,
					@Param("gtiDtoList") List<UpdateCtrlDeviceDto> gtiDtoList);

	List<GtiStatusBi> getGtiStatusBi(@Param("commIdChain") String commIdChain,
									 @Param("siteId") String siteId);

	Long getAbnormalGtiNum(@Param("commIdChain") String commIdChain,
						   @Param("siteId") String siteId);

    GtiPo getByEssEquipId(@Param("essEquipId") Long essEquipId);

    List<GtiPo> getByVendor(@Param("vendor") GtiVendor vendor, @Param("equipType") Integer equipType);
}

