<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.ParkingLockRwMapper">



	<resultMap id="RESULT_PARKINGLOCK_PO" type="com.cdz360.iot.model.park.po.ParkingLockPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="partner" property="partner" />

		<result column="serialNumber" jdbcType="VARCHAR" property="serialNumber" />

		<result column="devUuid" jdbcType="VARCHAR" property="devUuid" />

		<result column="type" jdbcType="VARCHAR" property="type" />

		<result column="electricQuantity" jdbcType="INTEGER" property="electricQuantity" />

		<result column="parkingLotId" jdbcType="VARCHAR" property="parkingLotId" />

		<result column="parkingLotName" jdbcType="VARCHAR" property="parkingLotName" />

		<result column="parkingSpaceCode" jdbcType="VARCHAR" property="parkingSpaceCode" />

		<result column="carNo" jdbcType="VARCHAR" property="carNo" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_PARKINGLOCK_PO">	
		select * from t_parking_lock where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>

	<select id="getByUniqueKey"
			resultMap="RESULT_PARKINGLOCK_PO">
		select * from t_parking_lock
		where partner = #{partner} and serialNumber = #{serialNumber}
		<if test="lock == true">
			for update
		</if>
	</select>



	<insert id="upsetParkingLock" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.park.po.ParkingLockPo">

		insert into t_parking_lock (`partner`,

			`serialNumber`,
			`positionCode`,
			`status`,

			`devUuid`,

			`type`,

			`electricQuantity`,

			`parkingLotId`,

			`parkingLotName`,

			`parkingSpaceCode`,

			`carNo`,
			`evseNo`,
			`plugId`,
			`srcDetail`,
			`errorMsg`,

			`createTime`,

			`updateTime`)

		values (#{partner},

			#{serialNumber},
			#{positionCode},
			#{status},

			#{devUuid},

			#{type},

			#{electricQuantity},

			#{parkingLotId},

			#{parkingLotName},

			#{parkingSpaceCode},

			#{carNo},
			#{evseNo},
			#{plugId},
			#{srcDetail},
			#{errorMsg},

			now(),

			now())
			on DUPLICATE key UPDATE
			<if test="positionCode != null">
				positionCode = #{positionCode},
			</if>
			<if test="status != null">
				status = #{status},
			</if>
			<if test="errorMsg != null">
				errorMsg = #{errorMsg},
			</if>
			<if test="devUuid != null">
				devUuid = #{devUuid},
			</if>
			<if test="type != null">
				type = #{type},
			</if>
			<if test="electricQuantity != null">
				electricQuantity = #{electricQuantity},
			</if>
			<if test="parkingLotId != null">
				parkingLotId = #{parkingLotId},
			</if>
			<if test="parkingLotName != null">
				parkingLotName = #{parkingLotName},
			</if>
			<if test="parkingSpaceCode != null">
				parkingSpaceCode = #{parkingSpaceCode},
			</if>
			<if test="carNo != null">
				carNo = #{carNo},
			</if>
			<if test="evseNo != null">
				evseNo = #{evseNo},
			</if>
			<if test="plugId != null">
				plugId = #{plugId},
			</if>
			<if test="srcDetail != null">
				srcDetail = #{srcDetail},
			</if>
			updateTime = now()
	</insert>

</mapper>

