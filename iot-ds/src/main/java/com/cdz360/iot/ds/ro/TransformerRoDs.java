package com.cdz360.iot.ds.ro;

import com.cdz360.iot.ds.ro.mapper.TransformerRoMapper;
import com.cdz360.iot.model.transformer.po.TransformerPo;
import com.cdz360.iot.model.transformer.vo.TransformerVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TransformerRoDs {

	@Autowired
	private TransformerRoMapper transformerRoMapper;

	public TransformerPo getById(Long id) {
		return this.transformerRoMapper.getById(id);
	}

	public TransformerPo getByNo(String no) {
		return this.transformerRoMapper.getByNo(no);
	}

	public List<TransformerVo> list(String keyword, Long transformerId, String siteId, long start, long size) {
		return transformerRoMapper.list(keyword, transformerId, siteId, start, size);
	}

	public int listCount(String keyword, Long transformerId, String siteId, long start, long size) {
		return transformerRoMapper.listCount(keyword, transformerId, siteId, start, size);
	}

	/**
	 * 查询有设置场站功率分配的变压器
	 */
	public List<TransformerPo> getTransformerList4SiteDynamicPower(Long transformerId){
		return this.transformerRoMapper.getTransformerList4SiteDynamicPower(transformerId);
	}

	/**
	 * 查询有设置场站有序充电的变压器
	 */
	public List<TransformerPo> getTransformerList4SiteOrderly(Long transformerId){
		return this.transformerRoMapper.getTransformerList4SiteOrderly(transformerId);
	}
}
