package com.cdz360.iot.ds.rw;

import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.mapper.EvseModuleRwMapper;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EvseModuleRwDs {

    @Autowired
    private EvseModuleRwMapper mapper;

    public boolean insert(EvseModulePo po) {
        IotAssert.isNotNull(po.getEvseNo(),"桩编号不能为空");
        IotAssert.isNotNull(po.getDeviceName(),"器件名称不能为空");
        IotAssert.isNotNull(po.getNumber(),"槽位数量不能为空");
//        if(po.getEvseNo().length()!=12){
//            throw  new DcArgumentException("桩编号是12位数字");
//        }
        return mapper.insert(po) > 0;
    }

    public boolean updateById(EvseModulePo po) {
        IotAssert.isNotNull(po.getId(), "桩编号不能为空");
        return mapper.updateById(po) > 0;
    }

    public boolean updateModuleTypeAndNumber(EvseModulePo po) {
        IotAssert.isNotNull(po.getEvseNo(),"桩编号不能为空");
        IotAssert.isNotNull(po.getDeviceName(),"器件名称不能为空");
        IotAssert.isNotNull(po.getNumber(),"槽位数量不能为空");
        long count = mapper.count(po.getEvseNo(), po.getDeviceName());
        if (count > 0) {
            return mapper.updateModuleTypeAndNumber(po) > 0;
        } else {
            return mapper.insert(po) > 0;
        }
    }

}
