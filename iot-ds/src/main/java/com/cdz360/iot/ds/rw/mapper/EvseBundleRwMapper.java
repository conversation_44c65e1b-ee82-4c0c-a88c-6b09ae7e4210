package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.evse.EvseBundle;
import org.apache.ibatis.annotations.Mapper;


/**
 * @ClassName： EvseBundleRwMapper
 * @Description: 桩升级包信息数据访问层-RW
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:15
 */
@Mapper
public interface EvseBundleRwMapper {
    int updateEnableById(Long id);

    int deleteById(Long id);

    int insert(EvseBundle evseBundle);

    int insertSelective(EvseBundle evseBundle);

    int updateByPrimaryKeySelective(EvseBundle evseBundle);

    int updateByPrimaryKey(EvseBundle evseBundle);
}