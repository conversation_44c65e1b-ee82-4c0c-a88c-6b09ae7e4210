<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.iot.ds.rw.mapper.EssPeakShareCfgRwMapper">



	<resultMap id="RESULT_ESSPEAKSHARECFG_PO" type="com.cdz360.iot.model.ess.po.EssPeakShareCfgPo">

		<result column="cfgId" jdbcType="BIGINT" property="cfgId" />

		<result column="peakFillEnable" jdbcType="BOOLEAN" property="peakFillEnable" />

		<result column="peakTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="peakTime" />

		<result column="peakPower" jdbcType="DECIMAL" property="peakPower" />

		<result column="valleyTime"  typeHandler="com.cdz360.iot.ds.ListTypeHandler" property="valleyTime" />

		<result column="valleyPower" jdbcType="DECIMAL" property="valleyPower" />

		<result column="smoothOutputEnable" jdbcType="BOOLEAN" property="smoothOutputEnable" />

		<result column="monitoringPeriod" jdbcType="INTEGER" property="monitoringPeriod" />

		<result column="amplitude" jdbcType="INTEGER" property="amplitude" />

		<result column="targetPowerRating" jdbcType="DECIMAL" property="targetPowerRating" />

	</resultMap>



	<insert id="insertEssPeakShareCfg" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.EssPeakShareCfgPo">

		insert into t_ess_peak_share_cfg (`cfgId`,

			`peakFillEnable`,

			`peakTime`,

			`peakPower`,

			`valleyTime`,

			`valleyPower`)

		values (#{cfgId},

			#{peakFillEnable},

			#{peakTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			#{peakPower},

			#{valleyTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			#{valleyPower})

		ON DUPLICATE KEY UPDATE

			peakFillEnable = #{peakFillEnable},

			peakTime = #{peakTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			peakPower = #{peakPower},

			valleyTime = #{valleyTime, typeHandler=com.cdz360.iot.ds.ListTypeHandler},

			valleyPower = #{valleyPower}

	</insert>
    <insert id="insertBeforeSelect">
		insert into t_ess_peak_share_cfg(cfgId,peakFillEnable,peakTime,peakPower,valleyTime,valleyPower)
		select #{newId},peakFillEnable,peakTime,peakPower,valleyTime,valleyPower from t_ess_peak_share_cfg where cfgId = #{oldId};
	</insert>


</mapper>

