package com.cdz360.iot.ds.rw.mapper;


import com.cdz360.iot.model.srs.po.SrsPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;


@Mapper
public interface SrsRwMapper {

	int upsetSrs(SrsPo srsPo);

	int batchUpsetSrs(@Param("poList") List<SrsPo> poList);

    int offlineSrs(@NonNull @Param("gwno") String gwno,
                   @Nullable @Param("remainDnoList") List<String> remainDnoList);
}

