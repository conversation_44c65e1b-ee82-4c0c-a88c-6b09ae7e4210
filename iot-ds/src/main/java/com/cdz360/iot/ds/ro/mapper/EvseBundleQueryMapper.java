package com.cdz360.iot.ds.ro.mapper;

import com.cdz360.iot.model.evse.EvseBundle;
import com.cdz360.iot.model.evse.param.EvseBundleParam;
import com.cdz360.iot.model.evse.type.BundleType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName： EvseBundleQueryMapper
 * @Description: 桩升级包信息数据访问层-QUERY
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:15
 */
@Mapper
public interface EvseBundleQueryMapper {

    EvseBundle selectByPrimaryKey(Long id);

    List<EvseBundle> selectByVersion(
            @Param("version") Long version,
            @Param("type") BundleType type,
            @Param("enable") Integer enable);

    List<EvseBundle> listEvseBundle(EvseBundleParam evseBundleParam);

    long getTotals(EvseBundleParam evseBundleParam);

}