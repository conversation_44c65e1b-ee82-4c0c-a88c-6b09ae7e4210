package com.cdz360.iot.ds.rw.mapper;

import com.cdz360.iot.model.camera.po.CameraSitePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface CameraSiteRwMapper {
	CameraSitePo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertCameraSite(CameraSitePo cameraSitePo);

	int updateCameraSite(CameraSitePo cameraSitePo);


	int disableSiteByAccountId(@Param("accountId") Long accountId);

	int insertOrUpdate(CameraSitePo cameraSitePo);
}
