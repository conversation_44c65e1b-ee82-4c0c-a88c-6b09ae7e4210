package com.cdz360.iot.ds.ro.ess.ds;



import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo;

import com.cdz360.iot.ds.ro.ess.mapper.EssEquipAlarmLangRoMapper;

import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import java.lang.Double;

import java.util.Date;



@Slf4j

@Service

public class EssEquipAlarmLangRoDs {



	@Autowired

	private EssEquipAlarmLangRoMapper essEquipAlarmLangRoMapper;



	public EssEquipAlarmLangPo getById(Long id) {

		return this.essEquipAlarmLangRoMapper.getById(id);

	}

	public List<EssEquipAlarmLangDto> getAlarmLangDtoList(ListAlarmLangParam param) {
		return this.essEquipAlarmLangRoMapper.getAlarmLangDtoList(param);
	}

}

