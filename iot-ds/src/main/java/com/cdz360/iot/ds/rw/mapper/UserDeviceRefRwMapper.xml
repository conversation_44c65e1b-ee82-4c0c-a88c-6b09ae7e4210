<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.UserDeviceRefRwMapper">

  <resultMap id="RESULT_USERDEVICEREF_PO" type="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="dno" jdbcType="VARCHAR" property="dno"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="master" jdbcType="BOOLEAN" property="master"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_USERDEVICEREF_PO">
    select * from t_user_device_ref where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="batchInsert">
    insert ignore into
    t_user_device_ref(`uid`, `dno`, `enable`, `master`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="item">
      (#{item.uid}, #{item.dno}, true, #{item.master}, now(), now())
    </foreach>
  </insert>

  <insert id="batchUpset">
    insert into t_user_device_ref(`uid`, `dno`, `enable`, `master`, `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="item">
      (#{item.uid}, #{item.dno}, true, #{item.master}, now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    master = values(master),
    `enable` = true,
    updateTime=now()
  </insert>

  <insert id="insertUserDeviceRef" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    insert into t_user_device_ref (`uid`,
    `dno`,
    `countryCode`,
    `enable`,
    `master`,
    `createTime`,
    `updateTime`)
    values (#{uid},
    #{dno},
    #{countryCode},
    #{enable},
    #{master},
    now(),
    now())
  </insert>

  <update id="upsetUserDeviceRef" parameterType="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    insert into t_user_device_ref(`uid`, `dno`, `countryCode`, `enable`, `master`,
    `createTime`, `updateTime`)
    values (#{uid}, #{dno}, #{countryCode}, #{enable}, #{master}, now(), now())
    ON DUPLICATE KEY UPDATE
    <if test="countryCode != null">
      countryCode = #{countryCode},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    <if test="master != null">
      master = #{master},
    </if>
    updateTime = now()
  </update>

  <update id="updateUserDeviceRef" parameterType="com.cdz360.iot.model.ess.po.UserDeviceRefPo">
    update t_user_device_ref set
    <if test="uid != null">
      uid = #{uid},
    </if>
    <if test="dno != null">
      dno = #{dno},
    </if>
    <if test="countryCode != null">
      countryCode = #{countryCode},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    <if test="master != null">
      master = #{master},
    </if>
    updateTime = now()
    where id = #{id}
  </update>
  <update id="resetNewMaster">
    update t_user_device_ref set
    master = true, updateTime = now()
    where `enable` = true and dno = #{dno}
    limit 1
  </update>

</mapper>

