<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.mapper.SiteTopologyRefRoMapper">

	<resultMap id="RESULT_SITETOPOLOGYREF_PO" type="com.cdz360.iot.model.topology.po.SiteTopologyRefPo">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="upType" jdbcType="VARCHAR" property="upType" />
		<result column="upId" jdbcType="INTEGER" property="upId" />
		<result column="downType" jdbcType="VARCHAR" property="downType" />
		<result column="downId" jdbcType="INTEGER" property="downId" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>
	<resultMap id="RESULT_SITETOPOLOGYREF_VO" type="com.cdz360.iot.model.topology.vo.SiteTopologyRefVo">
		<result column="downName" jdbcType="VARCHAR" property="downName" />
		<result column="downNo" jdbcType="VARCHAR" property="downNo" />
		<result column="power" jdbcType="INTEGER" property="power" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_SITETOPOLOGYREF_PO">	
		select * from t_site_topology_ref where id = #{id}
	</select>

	<select id="getByUpId"
			resultMap="RESULT_SITETOPOLOGYREF_PO">
		select * from t_site_topology_ref where upId = #{upId}
	</select>

	<select id="getBindRefByDownIdAndType" resultMap="RESULT_SITETOPOLOGYREF_PO">
		select * from t_site_topology_ref
		<where>
			downId = #{downId}
			and downType=#{downType}
			and upType="TRANSFORMER"
		</where>
	</select>

	<select id="getEvseMeterTopology"
			resultMap="RESULT_SITETOPOLOGYREF_VO">
		select

            em.id as id,
            'METER' as upType,
            #{meterId} as upId,
            'EVSE' as downType,
            evse.id as downId,
            em.createTime as createTime,
            em.createTime as updateTime,
            evse.name as downName,
            evse.evseId as downNo,
			evse.power as power

        from
			t_device_meter em
            left join t_evse evse on evse.evseId = em.evseId
        <where>
            em.meterId = #{meterId}
			and em.estimateType = 'EVSE_INPUT'
        </where>

	</select>

	<select id="getByUpIdList" resultMap="RESULT_SITETOPOLOGYREF_PO">
		select stf.* from t_site_topology_ref stf
		where stf.upType = #{upType}
		<foreach item="upId" collection="upIdList"
				 open="and stf.upId in (" separator="," close=")">
			#{upId}
		</foreach>
	</select>


	<select id="getByDownIdList" resultMap="RESULT_SITETOPOLOGYREF_PO">
		select stf.* from t_site_topology_ref stf
		where stf.downType = #{downType}
		<foreach item="downId" collection="downIdList"
				 open="and stf.downId in (" separator="," close=")">
			#{downId}
		</foreach>
	</select>
</mapper>
