<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.ro.ess.mapper.EssDtuRoMapper">

  <resultMap id="RESULT_ESS_DTU_PO" type="com.cdz360.iot.model.dtu.po.EssDtuPo">
    <id column="serialNo" jdbcType="VARCHAR" property="serialNo"/>
    <result column="gwno" jdbcType="VARCHAR" property="gwno"/>
    <result column="essDtuType" property="essDtuType"/>
    <result column="communicationWay" property="communicationWay"/>
    <result column="deviceName" jdbcType="VARCHAR" property="deviceName"/>
    <result column="deviceModel" jdbcType="VARCHAR" property="deviceModel"/>
    <result column="hardwareVer" jdbcType="VARCHAR" property="hardwareVer"/>
    <result column="softwareVer" jdbcType="VARCHAR" property="softwareVer"/>
    <result column="iccid" jdbcType="VARCHAR" property="iccid"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getBySerialNo"
    resultMap="RESULT_ESS_DTU_PO">
    select * from t_ess_dtu where serialNo = #{serialNo}
  </select>

  <select id="getByEssDno"
    resultMap="RESULT_ESS_DTU_PO">
    select dtu.* from t_ess_dtu dtu
    right join t_ess_dtu_ess_ref `ref` on `ref`.serialNo = dtu.serialNo
    where `ref`.dno = #{essDno}
  </select>

  <select id="essMapData"
    parameterType="com.cdz360.iot.model.ess.param.EssMapDataParam"
    resultType="com.cdz360.iot.model.ess.vo.EssMapDataVo">
    select udRef.countryCode, count(udRef.uid) userCnt,
    count(dtu.serialNo) deviceCnt, sum(ess.capacityMin) deviceCapacity
    from t_ess_dtu dtu
    left join t_ess_dtu_ess_ref `ref` on `ref`.serialNo = dtu.serialNo
    left join t_ess ess on ess.dno = `ref`.dno
    left join t_user_device_ref udRef on udRef.dno = ess.dno
    <where>
      udRef.master = 1
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( countryCode )">
        and udRef.countryCode = #{countryCode}
      </if>
    </where>
    group by udRef.countryCode
  </select>
</mapper>

