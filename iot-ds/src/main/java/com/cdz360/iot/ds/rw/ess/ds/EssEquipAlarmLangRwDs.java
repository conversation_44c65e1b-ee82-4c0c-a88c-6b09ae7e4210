package com.cdz360.iot.ds.rw.ess.ds;



import com.cdz360.iot.model.alarm.po.EssEquipAlarmLangPo;

import com.cdz360.iot.ds.rw.ess.mapper.EssEquipAlarmLangRwMapper;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import java.lang.Double;

import java.util.Date;



@Slf4j

@Service

public class EssEquipAlarmLangRwDs {



	@Autowired

	private EssEquipAlarmLangRwMapper essEquipAlarmLangRwMapper;



	public EssEquipAlarmLangPo getById(Long id, boolean lock) {

		return this.essEquipAlarmLangRwMapper.getById(id, lock);

	}



	public boolean insertEssEquipAlarmLang(EssEquipAlarmLangPo essEquipAlarmLangPo) {

		return this.essEquipAlarmLangRwMapper.insertEssEquipAlarmLang(essEquipAlarmLangPo) > 0;

	}



	public boolean updateEssEquipAlarmLang(EssEquipAlarmLangPo essEquipAlarmLangPo) {

		return this.essEquipAlarmLangRwMapper.updateEssEquipAlarmLang(essEquipAlarmLangPo) > 0;

	}





}

