package com.cdz360.iot.ds.rw;


import com.cdz360.iot.ds.rw.mapper.StorageRwMapper;
import com.cdz360.iot.model.parts.po.StoragePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;



@Slf4j

@Service

public class StorageRwDs {



	@Autowired

	private StorageRwMapper storageRwMapper;



	public boolean insertStorage(StoragePo storagePo) {

		return this.storageRwMapper.insertStorage(storagePo) > 0;

	}


	public StoragePo getByUid(Long uid, boolean lock) {
		return this.storageRwMapper.getByUid(uid, lock);
	}

	@Cacheable(key = "#name")
	public StoragePo getOneByName(String name, boolean lock) {
		return this.storageRwMapper.getOneByName(name, lock);
	}
}

