# 编译输出和构建目录
**/build/
**/out/
**/bin/
*/target/
**/generated/

# Gradle
.gradle/
!gradle/wrapper/gradle-wrapper.jar

# 日志文件
**/*.log
**/*.log.*

# IDE - IntelliJ IDEA
.idea/
**/*.iml
**/*.iws
**/*.ipr

# IDE - Eclipse/STS
.project
.classpath
.settings/
.apt_generated/
.factorypath
.springBeans
.sts4-cache/

# IDE - VS Code
.vscode/

# IDE - NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# 临时文件和本地配置
**/temp/
*local*
**/dump/
workspace/
nul
*.swp
*~

# 项目特定文件
**/rules/
*CLAUDE*
/gwCmd.json

# 配置文件（如果需要忽略，取消注释）
#application.yml
#bootstrap.yml
