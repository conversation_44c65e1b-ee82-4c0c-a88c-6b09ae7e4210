package com.cdz360.iot.park.client.cs;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.park.cfg.CsClientProperties;
import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.service.cs.CsClientBizFacade;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CsClient {

    @Autowired
    private CsClientProperties csClientProperties;

    @Autowired
    private CsClientHandler csClientHandler;

    @Autowired
    private CsClientBizFacade csClientBizFacade;

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(this.csClientProperties.getLotServerList())) {
            log.warn("未配置地锁");
            return;
        }

        try {
            this.csClientProperties.getLotServerList()
                    .forEach(this::newChannel);
        } catch (Exception e) {
            log.error("初始化地锁客户端异常, 需要人为干预");
        }
    }

    // 2分钟检查一次
    @Scheduled(initialDelay = 60 * 1000, fixedDelay = 2 * 60 * 1000)
    public void checkChannel() {
        try {
            log.debug("通道检查: >>> <<<");
            this.csClientProperties.getLotServerList()
                    .stream()
                    .filter(serv -> StringUtils.isNotBlank(serv.getHost()))
                    .forEach(serv -> {
                        try {
                            String channelKey = serv.channelKey();
                            Channel channel = csClientBizFacade.getChannel(channelKey);
                            if (null == channel || !channel.isActive()) {
                                log.warn("通道失效, 需要重连: {}", channelKey);
                                this.newChannel(serv);
                            }
                        } catch (Exception ex) {
                            log.error("重连异常: {} - {}", serv.getHost(), ex.getMessage(), ex);
                        }
                    });
        } catch (Exception ex) {
            log.warn("重连处理逻辑异常，需要立即排查: {}", ex.getMessage(), ex);
        }
    }

    public Channel newChannel(CsClientProperties.LotServer serv) {
        Channel channel = this.csClientBizFacade.getChannel(serv.channelKey());
        if (null != channel && channel.isActive()) {
            return channel;
        }

        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioSocketChannel.class)
                    .handler(new ChannelInitializer<>() {
                        @Override
                        protected void initChannel(Channel ch) throws Exception {
                            ch.pipeline().addLast(new IdleStateHandler(
                                    0, 0, 15, TimeUnit.SECONDS));

                            // 分包可根据实际调整
                            ch.pipeline().addLast(
                                    new AnneffiPackageDecoder(1024 * 10, 4, 2, 8));
                            ch.pipeline().addLast(new ByteArrayDecoder());
                            ch.pipeline().addLast(new ByteArrayEncoder());

                            ch.pipeline().addLast(csClientHandler);
                        }
                    });

            ChannelFuture channelFuture = bootstrap.connect(serv.getHost(), serv.getPort()).sync();
            this.csClientBizFacade.putChannel(serv.channelKey(),
                    CsClientChannel.builder()
                            .channel(channelFuture.channel())
                            .partner(serv.getPartner())
                            .appId(serv.getAppId())
                            .appKey(serv.getAppKey())
                            .build());
            return channelFuture.channel();
        } catch (Exception e) {
            log.error("服务端连接不上: err = {}", e.getMessage(), e);
            group.shutdownGracefully();
            throw new DcServiceException("服务端连接不上");
        }
    }
}
