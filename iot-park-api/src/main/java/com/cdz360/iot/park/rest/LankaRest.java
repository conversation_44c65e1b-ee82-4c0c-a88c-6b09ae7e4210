package com.cdz360.iot.park.rest;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.park.model.api.LankaHbReq;
import com.cdz360.iot.park.model.api.LankaInReq;
import com.cdz360.iot.park.model.api.LankaOutReq;
import com.cdz360.iot.park.model.api.LankaRes;
import com.cdz360.iot.park.model.api.ParkBaseRes;
import com.cdz360.iot.park.model.api.ParkOrderCouponReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponRes;
import com.cdz360.iot.park.service.ApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @Classname LankaRest
 * @Description 蓝卡接口
 * @Date 1/26/2024 1:37 PM
 * @Created by Rafael
 */
@Slf4j
@RestController
@RequestMapping(value = "/", produces = MediaType.APPLICATION_JSON_VALUE)
public class LankaRest {

    @Autowired
    private ApiService apiService;

    /**
     蓝卡云定期发送场库名称、场库总车位数、场库空位数、区域名称、区域车位数，
     区域空位数、区域可预约车位数等信息给第三方云，第三方云返回时间戳，心跳频率30s。
     */
    @PostMapping(value = "/v1/lanka/hb")
    public Mono<LankaRes> hb(
        @RequestHeader(value = "sign", required = false) String sign,
        @RequestBody LankaHbReq param) {
        log.info("心跳 = {}, sign = {}", JsonUtils.toJsonString(param), sign);

        return Mono.just(LankaRes.getSuccess());
    }

    @PostMapping(value = "/v1/lanka/carIn")
    public Mono<LankaRes> carIn(
        @RequestHeader(value = "sign", required = false) String sign,
        @RequestBody LankaInReq param) {
        log.info("车辆入场 = {}, sign = {}", JsonUtils.toJsonString(param), sign);
        return Mono.just(LankaRes.getSuccess());
    }

    @PostMapping(value = "/v1/lanka/carOut")
    public Mono<LankaRes> carOut(
        @RequestHeader(value = "sign", required = false) String sign,
        @RequestBody LankaOutReq param) {
        log.info("车辆出场 = {}, sign = {}", JsonUtils.toJsonString(param), sign);
        return Mono.just(LankaRes.getSuccess());
    }
}