package com.cdz360.iot.park.client;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.park.model.api.ChargeReduction;
import com.cdz360.iot.park.model.api.YzParkReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2023/4/4
 * @description
 */
@Service
@Slf4j
public class CrClient {

//    @Value("${iot.park.bsUrlCr}")
    private String baseUrlCr;

//    @Value("${iot.park.bsSignKey:}")
    private String signKey;

    public Mono<ChargeReduction.Response> chargeReduction() {
//        Map<String, Object> map = creq.getMap();
        //要求先把appKeyMD5加密
//        appKey:pttest1234567
//        车场ID:5246181862404622040
//
//        测试车牌号 湘EAA693
        YzParkReq creq = new YzParkReq();
//        creq.setPlateNumber("湘EAA693")
//                .setParkingId("5246181862404622040")
//                .setFavourableDuration(10)
//                .setTimestamp(System.currentTimeMillis());
//        String sign = genSign(creq);
//        String md5AppKey = getMd5(creq.getAppKey());
//        String sign = createSign(map, md5AppKey);
//        ChargeReduction req = new ChargeReduction(creq.getParkingId(), creq.getPlateNumber(), creq.getFavourableDuration(), creq.getTimestamp(), md5AppKey, sign);
//        Mono<ClientResponse> mono = exchangeCr(req);
        return exchangeCr(creq)
                .flatMap(response ->
                response.bodyToMono(ChargeReduction.Response.class)
                        .doOnSuccess(s -> {
                            log.info("第三方平台返回结果为: {}", s.toString());
                        })
                        .doOnError(f -> {
                            log.error("第三方平台返回错误: {}", f.getMessage());
                        })
        );
    }

    private Mono<ClientResponse> exchangeCr(Object body) {
        WebClient webClient = WebClient.create(baseUrlCr);
        String json = body.toString();
        log.info("充电减免编码后的请求参数为: {}", json);
        return webClient
                .post()
                .uri("/cxzn/interface/ChargeReduction")
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(json)
                .exchange();
    }


    private String genSign(Object data) {
        return genSign(data, "pttest1234567");
//        String jsonStr = JsonUtils.toJsonString(data);
//        String origStr = jsonStr + "key=" + this.signKey;
//        return DigestUtils.md5DigestAsHex(origStr.getBytes(Charset.forName("utf-8")))
//                .toUpperCase();
    }

    private String genSign(Object data, String parkSignKey) {
        String jsonStr = JsonUtils.toJsonString(data);
        String origStr = jsonStr + "key=" + parkSignKey;
        return DigestUtils.md5DigestAsHex(origStr.getBytes(Charset.forName("utf-8")))
                .toUpperCase();
    }
    /**
     * 将参数编码以获取签名参数
     *
     * @param params p
     * @param appKey appKey要提供MD5加密后提交
     * @return 签名参数
     */
    private static String createSign(Map<String, Object> params, String appKey) {
        StringBuilder sb = new StringBuilder();
        // 将参数以参数名的字典升序排序
        Map<String, Object> sortParams = new TreeMap<String, Object>(params);
        // 遍历排序的字典,并拼接"key=value"格式
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (!ObjectUtils.isEmpty(value))
                sb.append("&").append(key).append("=").append(value);
        }
        String stringA = sb.toString().replaceFirst("&", "");
        //私钥最后放在配置文件里面读取
        String stringSignTemp = stringA + appKey;
        log.info("明文为: {}", stringSignTemp);
        //将签名使用MD5加密并全部字母变为大写
        return getMd5(stringSignTemp);
    }

    /**
     * MD5转32位大写
     *
     * @param plainText
     * @return
     */
    private static String getMd5(String plainText) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes());
            byte b[] = md.digest();
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i).toUpperCase());
            }
            //32位加密
            return buf.toString();
            // 16位的加密
            //return buf.toString().substring(8, 24);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
}
