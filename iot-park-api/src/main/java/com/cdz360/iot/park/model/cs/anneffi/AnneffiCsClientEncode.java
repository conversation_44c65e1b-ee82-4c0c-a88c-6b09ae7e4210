package com.cdz360.iot.park.model.cs.anneffi;

import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AnneffiCsClientEncode extends CsClientEncodeBase {
    // 地所云提供的客户密码
    private String appKey;

    public AnneffiCsClientEncode() {}

    public AnneffiCsClientEncode(Long clientCode) {
        super(clientCode);
    }
}
