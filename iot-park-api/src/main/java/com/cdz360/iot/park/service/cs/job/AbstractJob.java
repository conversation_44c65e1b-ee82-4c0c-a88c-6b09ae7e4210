package com.cdz360.iot.park.service.cs.job;

import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.model.cs.EventDataBase;
import com.cdz360.iot.park.service.cs.CsClientBizFactory;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.MonoSink;

@Slf4j
@Getter
@SuppressWarnings("all")
public abstract class AbstractJob {

    private final CsClientBizFactory bizFactory;

    private final CsClientChannel channel;

    private final String deviceNo;

    protected final Queue<JobTask> jobQ = new ConcurrentLinkedQueue<>();

    @Setter
    private long T = System.currentTimeMillis(); // 记录添加时间点()

    @Setter
    private MonoSink monoSink;

    public AbstractJob(CsClientBizFactory bizFactory, CsClientChannel channel, String deviceNo) {
        this.bizFactory = bizFactory;
        this.channel = channel;
        this.deviceNo = deviceNo;
    }

    public abstract void fillData(EventDataBase msg);

    public abstract void finish();

    public abstract void exception(Throwable ex);

    public void process(JobTask task) {
        bizFactory.getCommander(channel.getPartner())
            .flatMap(commander -> commander.process(task.getEncodeBase()))
            .doOnNext(buf -> CsClientChannel.channelWrite(channel, buf))
            .subscribe();
    }
}
