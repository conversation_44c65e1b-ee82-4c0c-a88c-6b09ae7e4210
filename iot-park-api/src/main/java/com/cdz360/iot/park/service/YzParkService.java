package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.park.client.CrClient;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import com.cdz360.iot.park.model.api.ChargeReduction;
import com.cdz360.iot.park.model.api.YzParkReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

import static com.cdz360.base.model.base.constants.DcConstants.KEY_RES_CODE_SERVICE_ERROR;

/**
 * <AUTHOR>
 * @date 2023/4/6
 * @description
 */
@Service
@Slf4j
public class YzParkService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

}
