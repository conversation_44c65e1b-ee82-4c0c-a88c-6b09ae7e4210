package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.model.park.vo.ParkCouponZkMetaVo;
import com.cdz360.iot.model.park.vo.ParkCouponZkVo;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import java.nio.charset.StandardCharsets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

/**
 * @Classname ParkCouponZKService
 * @Description
 * @Date 5/16/2024 4:21 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponZKService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_API_TYPE_ZK, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        ParkCouponZkMetaVo meta = new ParkCouponZkMetaVo();
        meta.setName(data.getCarNo())
            .setValue(data.getDuration() / ParkConstants.PARK_API_ZK_DURATION_RATE);

        ParkCouponZkVo param = new ParkCouponZkVo();
        param.setApiStoreCode(String.valueOf(data.getParkId())) // FIXME 此处沿用的parkId，应该是parkStoreCode才对
            .setApiType(ParkConstants.PARK_API_TYPE_ZK)
            .setAppId(data.getParkAppId())
            .setEventId(orderStopRequestV2.getOrderNo())
            .setPlate(data.getCarNo())
            .setSubject("车辆" + data.getCarNo() + "停车减免")
            .setMeta(meta);

        String headerAuth = getHeaderAuthZk(param, data.getParkAppSecret());

        this.setCouponZk(param, headerAuth).subscribe();
        return Mono.just(RestUtils.success());
    }

    /**
     * 中控-停车减免优惠券
     * @param param
     * @param headerAuth
     * @return
     */
    public Mono<BaseResponse> setCouponZk(ParkCouponZkVo param, String headerAuth) {
        log.info("zk请求参数: {}, header: {}", JsonUtils.toJsonString(param), headerAuth);
        return bsService.setCouponZk(param, headerAuth);
    }

    private static String getHeaderAuthZk(ParkCouponZkVo param, String appSecret) {
        String jsonStr = JsonUtils.toJsonString(param);
        String encryptStr = jsonStr + "&app_secret=" + appSecret;
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8));
        return s;
    }
}