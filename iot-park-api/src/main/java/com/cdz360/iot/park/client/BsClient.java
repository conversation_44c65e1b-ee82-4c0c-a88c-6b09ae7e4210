package com.cdz360.iot.park.client;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.rw.ParkMsgRwDs;
import com.cdz360.iot.model.park.po.ParkChannelPo;
import com.cdz360.iot.model.park.po.ParkMsgPo;
import com.cdz360.iot.model.park.type.ParkMsgStatusType;
import com.cdz360.iot.model.park.vo.*;
import com.cdz360.iot.park.model.bs.BsChargeCoupon;
import com.cdz360.iot.park.model.bs.BsLiftRod;
import com.cdz360.iot.park.model.bs.BsReqMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.nio.charset.Charset;
import java.util.UUID;

@Slf4j
@Service
public class BsClient {

    @Value("${iot.park.bsUrl:}")
    private String baseUrl;

    @Value("${iot.park.bsUrlZk:}")
    private String baseUrlZk;

    @Value("${iot.park.bsUrlJs:}")
    private String baseUrlJs;

    @Value("${iot.park.bsUrlYb:}")
    private String bsUrlYb;

    @Value("${iot.park.bsUrlNtc:}")
    private String bsUrlNtc;

    @Value("${iot.park.bsUrlStc:}")
    private String bsUrlStc;

    @Value("${iot.park.bsUrlPp:}")
    private String bsUrlPp;

    @Value("${iot.park.bsUrlCx:}")
    private String bsUrlCx;

    @Value("${iot.park.bsSignKey:}")
    private String signKey;

    @Autowired
    private ParkMsgRwDs parkMsgRwDs;

    private static Integer STATE_OK = 1;

    /**
     * 道闸操作
     *
     * @param channelPo 道闸信息
     * @param up        操作: true(抬杆); false(落杆)
     * @return
     */
    public Mono<Boolean> opLiftRod(ParkChannelPo channelPo, String parkSignKey, boolean up) {
        BsReqMsg<BsLiftRod> reqMsg = new BsReqMsg<>();
        reqMsg.setServiceName("operate_liftrod");
        reqMsg.setData(mapBsLiftRod(channelPo, up));
        reqMsg.setParkId(channelPo.getParkId());
        reqMsg.setSign(this.genSign(reqMsg.getData()));

        return this.exchange("/public/gate/liftrod", reqMsg)
                .flatMap(res -> res.bodyToMono(BsLiftRod.Response.class))
                .map(opResult -> {
                    log.info("道闸操作 res: {}", opResult);
                    return STATE_OK.equals(opResult.getState());
                });
    }

    private static BsLiftRod mapBsLiftRod(ParkChannelPo channelPo, boolean up) {
        return new BsLiftRod()
                .setLiftRodId(UUID.randomUUID().toString())
                .setDeviceId(channelPo.getDeviceId())
                .setChannelId(channelPo.getChannelId())
                .setChannelName(channelPo.getChannelName())
                .setOperate(up ? 0 : 1);
    }

    /**
     * 充电桩下发优惠信息
     *
     * @param parkId
     * @param parkSignKey
     * @param coupon
     * @return
     */
    public Mono<BsChargeCoupon.Response> setDiscount(Long parkId, String parkSignKey, BsChargeCoupon coupon) {
        BsReqMsg<BsChargeCoupon> reqMsg = new BsReqMsg<>();
        reqMsg.setServiceName("charge_discount_notice");
        reqMsg.setData(coupon);
        reqMsg.setParkId(parkId);
        if (StringUtils.isBlank(parkSignKey)) {
            reqMsg.setSign(this.genSign(coupon));
        } else {
            reqMsg.setSign(this.genSign(coupon, parkSignKey));
        }

        Mono<ClientResponse> mono = this.exchange("/public/charge/discountNotice", reqMsg);
//        mono.flatMap(res -> res.bodyToMono(BsChargeCoupon.Response.class))
//                .doOnSuccess(e -> {
//                    e.getState()
//                })

        return mono
//                .flatMap(e -> this.exchange("/public/charge/discountNotice", e))
                .flatMap(res -> res.bodyToMono(BsChargeCoupon.Response.class)
                        .doOnSuccess(e -> {
                            if (STATE_OK.equals(e.getState())) {
                                this.storeDiscount(reqMsg, e, ParkMsgStatusType.FINISHED);
                            } else {
                                this.storeDiscount(reqMsg, e, ParkMsgStatusType.INIT);
                            }
                        }).doOnError(e -> {
                            this.storeDiscount(reqMsg, null, ParkMsgStatusType.INIT);
                        })
                );

//        Mono<ClientResponse> mono = this.exchange("/public/charge/discountNotice", reqMsg);
//        return mono
//                .flatMap(res ->
//                        res.bodyToMono(BsChargeCoupon.Response.class)
//                );
    }

    private Mono<BsReqMsg<BsChargeCoupon>> storeDiscount(BsReqMsg<BsChargeCoupon> discount,
                                                         BsChargeCoupon.Response response,
                                                         ParkMsgStatusType status) {

        ParkMsgPo parkMsgPo = new ParkMsgPo();
        parkMsgPo.setParkOrderId(discount.getData().getOrderId())
                .setStatus(status)
                .setMsg(JsonUtils.toJsonString(discount))
                .setResponse(JsonUtils.toJsonString(response));
        parkMsgRwDs.insertParkMsg(parkMsgPo);

        return Mono.just(discount);
    }

    private String genSign(Object data) {
        return genSign(data, this.signKey);
//        String jsonStr = JsonUtils.toJsonString(data);
//        String origStr = jsonStr + "key=" + this.signKey;
//        return DigestUtils.md5DigestAsHex(origStr.getBytes(Charset.forName("utf-8")))
//                .toUpperCase();
    }

    private String genSign(Object data, String parkSignKey) {
        String jsonStr = JsonUtils.toJsonString(data);
        String origStr = jsonStr + "key=" + parkSignKey;
        return DigestUtils.md5DigestAsHex(origStr.getBytes(Charset.forName("utf-8")))
                .toUpperCase();
    }

    private Mono<ClientResponse> exchange(String uri, Object body) {
        WebClient client = WebClient.create(baseUrl);
        log.info("req: {}", JsonUtils.toJsonString(body));
        return client.post()
                .uri(uri)
                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    public Mono<String> setDiscountZk(ParkCouponZkVo param, String headerAuth) {
//        BsReqMsg<BsChargeCoupon> reqMsg = new BsReqMsg<>();
//        reqMsg.setServiceName("charge_discount_notice");
//        reqMsg.setData(coupon);
//        reqMsg.setParkId(parkId);
//        if(StringUtils.isBlank(parkSignKey)) {
//            reqMsg.setSign(this.genSign(coupon));
//        } else {
//            reqMsg.setSign(this.genSign(coupon, parkSignKey));
//        }

        Mono<ClientResponse> mono = this.exchangeZk("/gate/1.0/parking/coupon/event", param, headerAuth);

        return mono
                .flatMap(res -> res.bodyToMono(String.class)
                                .doOnSuccess(e -> {
                                    log.info("第三方ZK返回结果: {}", e);
//                            if(STATE_OK.equals(e.getState())) {
//                                this.storeDiscount(reqMsg, e, ParkMsgStatusType.FINISHED);
//                            } else {
//                                this.storeDiscount(reqMsg, e, ParkMsgStatusType.INIT);
//                            }
                                }).doOnError(e -> {
//                            this.storeDiscount(reqMsg, null, ParkMsgStatusType.INIT);
                                    log.error(e.getMessage(), e);
                                })
                );
    }

    public Mono<String> setDiscountJs(ParkJsReqVo<ParkJs3CReqVo<ParkJsCouponReqVo>> param) {
        Mono<ClientResponse> mono = this.exchangeJs("/openApi", param);

        return mono
                .flatMap(res -> res.bodyToMono(String.class)
                        .doOnSuccess(e -> {
                            log.info("第三方JS返回结果: {}", e);
                        }).doOnError(e -> {
                            log.error(e.getMessage(), e);
                        })
                );
    }

    public Mono<String> setDiscountYb(ParkCouponYbVo param, String parkCode, String paramAuth) {
        Mono<ClientResponse> mono = this.exchangeYb("/partnerService/getDeductible", param, parkCode, paramAuth);
        return mono
                .flatMap(res -> res.bodyToMono(String.class)
                        .doOnSuccess(e -> {
                            log.info("第三方YB返回结果: {}", e);
                        }).doOnError(e -> {
                            log.error(e.getMessage(), e);
                        })
                );
    }

    public Mono<ParkCouponCxVo.Response> setDiscountCx(ParkCouponCxVo param) {
        Mono<ClientResponse> mono = this.exchangeCx("/cxzn/interface/ChargeReduction", param);
        return mono
                .flatMap(res -> res.bodyToMono(ParkCouponCxVo.Response.class)
                        .doOnSuccess(e -> {
                            log.info("第三方CX返回结果: {}", e);
                        }).doOnError(e -> {
                            log.error(e.getMessage(), e);
                        })
                );
    }

    private Mono<ClientResponse> exchangeZk(String uri, Object body, String authorization) {
        WebClient client = WebClient.create(baseUrlZk);
        return client.post()
                .uri(uri)
                .header("Authorization", authorization)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
//                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    private Mono<ClientResponse> exchangeJs(String uri, Object body) {
        WebClient client = WebClient.create(baseUrlJs);
        return client.post()
                .uri(uri)
//                .header("Authorization", authorization)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
//                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    private Mono<ClientResponse> exchangeYb(String uri, Object body, String parkCode, String paramAuth) {
        WebClient client = WebClient.create(bsUrlYb);
        return client.post()
                .uri(builder -> builder.path(uri)
                        .queryParam("partnerid", parkCode)
                        .queryParam("sign", paramAuth)
                        .build())
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
//                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    private Mono<ClientResponse> exchangeCx(String uri, Object body) {
        WebClient client = WebClient.create(bsUrlCx);
        return client.post()
                .uri(builder -> builder.path(uri)
                        .build())
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
//                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    public Mono<ParkCouponNtcVo.Response> setDiscountNtc(ParkCouponNtcVo param, String headerAuth) {
        Mono<ClientResponse> mono = this.exchangeNtc("/coupon/standardCharge", param, headerAuth);
        return mono.flatMap(res -> res.bodyToMono(ParkCouponNtcVo.Response.class)
                .doOnSuccess(e -> log.info("NTC返回结果: {}", e))
                .doOnError(e -> log.error(e.getMessage(), e))
        );
    }

    private Mono<ClientResponse> exchangeNtc(String uri, ParkCouponNtcVo body, String headerAuth) {
        WebClient client = WebClient.create(bsUrlNtc);
        return client.post()
                .uri(builder -> builder.path(uri)
                        .queryParam("sign", headerAuth)
                        .build())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(JsonUtils.toJsonString(body))
                .exchange();
    }

    public Mono<String> setDiscountStc(ParkCouponStcVo param) {
        Mono<ClientResponse> mono = this.exchangeStc("/elife/do/api/gateway", param);
        return mono.flatMap(res -> res.bodyToMono(String.class)
                .doOnSuccess(e -> log.info("STC返回结果: {}", e))
                .doOnError(e -> log.error(e.getMessage(), e))
        );
    }

    public Mono<ClientResponse> exchangeStc(String uri, ParkCouponStcVo body) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("appId", body.getAppId());
        formData.add("method", body.getMethod());
        formData.add("sign", body.getSign());
        formData.add("timestamp", body.getTimestamp());
        formData.add("parkId", body.getParkId());
        formData.add("plateNumber", body.getPlateNumber());
        formData.add("couponType", String.valueOf(body.getCouponType()));
        formData.add("disHours", String.valueOf(body.getDisHours()));
        formData.add("startTime", body.getStartTime());
        formData.add("endTime", body.getEndTime());

        WebClient client = WebClient.create(bsUrlStc);
        return client.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(formData))
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .exchange();
    }

    public Mono<ParkCouponPpVo.Response> setCouponPp(ParkCouponPpVo param) {
        Mono<ClientResponse> mono = this.exchangePp(param);
        return mono.flatMap(res -> res.bodyToMono(ParkCouponPpVo.Response.class)
                .doOnSuccess(e -> log.info("PP返回结果: {}", e))
                .doOnError(e -> log.error(e.getMessage(), e))
        );
    }

    private Mono<ClientResponse> exchangePp(ParkCouponPpVo body) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("app_id", body.getAppId());
        formData.add("sign", body.getSign());
        formData.add("merchant", body.getMerchant());
        formData.add("store_code", body.getStoreCode());
        formData.add("coupon_code", body.getCouponCode());
        formData.add("quantity", body.getQuantity());
        formData.add("plate", body.getPlate());
        formData.add("reason", body.getReason());
        WebClient client = WebClient.create(bsUrlPp);
        return client.post()
                .uri("/gate/1.0/parking/mcoupon/grant/create")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(formData))
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .exchange();
    }
}
