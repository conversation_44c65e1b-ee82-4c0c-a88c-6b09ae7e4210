package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaPlaceInfoDto
 * @Description
 * @Date 2/2/2024 3:41 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaPlaceInfoDto {
    @Schema(description = "区域id")
    private String areaId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "车位编号")
    private String placeNumber;

    @Schema(description = "备注信息")
    private String memo;
}