package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaCarInfoDto
 * @Description
 * @Date 2/2/2024 3:55 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaCarInfoDto {
    @Schema(description = "车牌")
    private String plate;

    @Schema(description = "车牌颜色")
    private String plateColor;

    @Schema(description = "无牌车票号")
    private String ticketCode;

    @Schema(description = "车类型")
    private String carType;

    @Schema(description = "车牌识别可信度")
    private Integer confidence;

}