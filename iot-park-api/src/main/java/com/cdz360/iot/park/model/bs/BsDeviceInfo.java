package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 */
@Data
@Schema(description = "上传设备在线状态")
public class BsDeviceInfo {

    @JsonProperty(value = "device_id")
    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "设备ip")
    private BsIp ip;

    @Schema(description = "设备掉线/上线时间(时间戳)")
    private Long time;

    @Schema(description = "消息")
    private String errmsg;

    @Data
    public static class BsIp {

        @Schema(description = "ip地址")
        private String address;


        @Schema(description = "端口")
        private Integer port;
    }
}
