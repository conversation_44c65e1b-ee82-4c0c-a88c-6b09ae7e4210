package com.cdz360.iot.park.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.park.param.PlugStatusEventLogParam;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import com.cdz360.iot.park.service.ParkingLockEventLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "地锁事件日志相关接口")
@RestController
@Slf4j
@RequestMapping("/park/parkingLock/eventLog")
public class ParkingLockEventLogRest {

    @Autowired
    private ParkingLockEventLogService eventLogService;

    @Operation(summary = "获取地锁近20天事件日志")
    @GetMapping(value = "/recent20")
    public Mono<ListResponse<ParkingLockEventLogVo>> eventLogRecent20(
        @Parameter(name = "地锁ID", required = true) @RequestParam Long parkingLockId) {
        log.info("获取地锁近20天事件日志: parkingLockId = {}", parkingLockId);
        return eventLogService.eventLogRecent20(parkingLockId)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "枪头状态变更事件日志")
    @PostMapping(value = "/plugStatus")
    public Mono<BaseResponse> plugStatusEventLog(@RequestBody PlugStatusEventLogParam param) {
        log.debug("枪头状态变更事件日志: {}", param);
        eventLogService.plugStatusEventLog(param);
        return Mono.just(RestUtils.success());
    }
}
