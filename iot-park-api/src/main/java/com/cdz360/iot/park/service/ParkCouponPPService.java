package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponPpVo;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.TreeMap;

@Slf4j
@Service
public class ParkCouponPPService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_PP, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        String merchant = "";
        String storeCode = "";
        String couponCode = "";
        if (StringUtils.isNotBlank(data.getParkConfig())) {
            final JsonNode jsonNode = JsonUtils.fromJson(data.getParkConfig());
            merchant = jsonNode.get("merchant").asText("");
            storeCode = jsonNode.get("storeCode").asText("");
            couponCode = jsonNode.get("couponCode").asText("");
        }

        ParkCouponPpVo parkCouponPpVo = new ParkCouponPpVo();
        parkCouponPpVo.setAppId(data.getParkAppId());
        parkCouponPpVo.setMerchant(merchant);
        parkCouponPpVo.setStoreCode(storeCode);
        parkCouponPpVo.setCouponCode(couponCode);
        parkCouponPpVo.setQuantity("1");
//        parkCouponPpVo.setParkingSerial();
        parkCouponPpVo.setPlate(data.getCarNo());
        parkCouponPpVo.setReason("派发");
        String sign = getSign(parkCouponPpVo, data.getParkAppSecret());
        parkCouponPpVo.setSign(sign);

        this.setCouponPp(parkCouponPpVo)
                .doOnSuccess(e -> {
                    if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.info("第三方道闸PP返回成功");
                    } else {
                        log.warn("第三方道闸PP返回状态异常: {}", e);
                    }
                })
                .doOnError(e -> log.error(e.getMessage(), e))
                .subscribe(
                        res -> log.info("第三方道闸PP executed successfully"),
                        err -> log.error("第三方道闸PP Error executing : {}", err.getMessage())
                );
        return Mono.just(RestUtils.success());
    }

    private static String getSign(ParkCouponPpVo param, String appSecret) {
        TreeMap<String, String> map = new TreeMap<>();
        /*for (Field field : param.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            JsonProperty annotation = field.getAnnotation(JsonProperty.class);
            String keyName = (annotation != null) ? annotation.value() : field.getName();
            Object value = null;
            try {
                value = field.get(param);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            if (value != null) {
                map.put(keyName, value.toString());
            }
        }*/
        // app_id应用身份ID
        map.put("app_id", param.getAppId());
        // 停车场商户号
        map.put("merchant", param.getMerchant());
        // 商家商户号
        map.put("store_code", param.getStoreCode());
        // 优惠券编码ID
        map.put("coupon_code", param.getCouponCode());
        // 派发张数
        map.put("quantity", param.getQuantity());
        // 派发车牌
        map.put("plate", param.getPlate());
        // 派发原因说明
        map.put("reason", param.getReason());
        StringBuilder builder = new StringBuilder();
        for (String key : map.keySet()) {
            builder.append(key + "=" + map.get(key) + "&");
        }
        String encriptStr = builder + "app_secret=" + appSecret;
        String sign = DigestUtils.md5DigestAsHex(encriptStr.getBytes(StandardCharsets.UTF_8));
        return sign;
    }

    public Mono<BaseResponse> setCouponPp(ParkCouponPpVo param) {
        log.info("PP请求参数: {}", JsonUtils.toJsonString(param));
        return bsService.setCouponPp(param);
    }
}