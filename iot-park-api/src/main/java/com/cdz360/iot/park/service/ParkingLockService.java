package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ParkingLockRoDs;
import com.cdz360.iot.ds.rw.ParkingLockRwDs;
import com.cdz360.iot.model.park.param.ListParkingLockParam;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiCommanderData;
import com.cdz360.iot.park.service.cs.CsClientBizFacade;
import com.cdz360.iot.park.service.cs.comsumer.AnneffiParkingLockOpService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ParkingLockService {

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private ParkingLockRoDs parkingLockRoDs;

    @Autowired
    private ParkingLockRwDs parkingLockRwDs;

    @Autowired
    private ParkingLockEventLogService eventLogService;

    @Autowired
    private CsClientBizFacade csClientBizFacade;

    @Autowired
    private AnneffiParkingLockOpService anneffiParkingLockOpService;

    public Mono<BaseResponse> switchLock(Long lockId, Boolean open) {
        return Mono.justOrEmpty(parkingLockRoDs.getById(lockId))
            .switchIfEmpty(Mono.error(new DcArgumentException("平台地锁记录ID无效")))
            .flatMap(lock -> {
                EvseVo evse = redisIotReadService.getEvseRedisCache(lock.getEvseNo());
                IotAssert.isNotNull(evse, "锁还没有绑定到桩");
                IotAssert.isNotBlank(evse.getSiteId(), "请先将桩绑定到场站，再操作");
                return anneffiParkingLockOpService.process(
                    csClientBizFacade.getChannelInfo(
                        evse.getSiteId()), AnneffiCommanderData.builder().open(open)
                        .deviceNo(lock.getSerialNumber())
                        .taskCode(ClientTaskCode.SWITCH_LOCK)
                        .build());
            })
            .map(x -> RestUtils.success());
    }

    public Mono<BaseResponse> cutPower(Long lockId) {
        return Mono.justOrEmpty(parkingLockRoDs.getById(lockId))
            .switchIfEmpty(Mono.error(new DcArgumentException("平台地锁记录ID无效")))
            .flatMap(lock -> {
                EvseVo evse = redisIotReadService.getEvseRedisCache(lock.getEvseNo());
                IotAssert.isNotNull(evse, "锁还没有绑定到桩");
                IotAssert.isNotBlank(evse.getSiteId(), "请先将桩绑定到场站，再操作");
                return anneffiParkingLockOpService.process(
                    csClientBizFacade.getChannelInfo(evse.getSiteId()),
                    AnneffiCommanderData.builder()
                        .deviceNo(lock.getSerialNumber())
                        .taskCode(ClientTaskCode.CUT_POWER)
                        .build());
            })
            .map(x -> RestUtils.success());
    }

    public Mono<BaseResponse> rebootLock(Long lockId) {
        return Mono.justOrEmpty(parkingLockRoDs.getById(lockId))
            .switchIfEmpty(Mono.error(new DcArgumentException("平台地锁记录ID无效")))
            .flatMap(lock -> {
                EvseVo evse = redisIotReadService.getEvseRedisCache(lock.getEvseNo());
                IotAssert.isNotNull(evse, "锁还没有绑定到桩");
                IotAssert.isNotBlank(evse.getSiteId(), "请先将桩绑定到场站，再操作");
                return anneffiParkingLockOpService.process(
                    csClientBizFacade.getChannelInfo(evse.getSiteId()),
                    AnneffiCommanderData.builder()
                        .deviceNo(lock.getSerialNumber())
                        .taskCode(ClientTaskCode.REBOOT)
                        .build());
            })
            .map(x -> RestUtils.success());
    }

    public Mono<ParkingLockVo> lookForLock(String siteId, String remoteLockId) {
        return anneffiParkingLockOpService.process(
                csClientBizFacade.getChannelInfo(siteId), AnneffiCommanderData.builder()
                    .deviceNo(remoteLockId)
                    .taskCode(ClientTaskCode.GET_LOCK_INFO)
                    .build())
            .map(s -> ((ParkingLockVo) s).setSiteId(siteId));
    }

    public Mono<ParkingLockStatus> fetchLockStatus(Long lockId) {
        return Mono.justOrEmpty(parkingLockRoDs.getById(lockId))
            .switchIfEmpty(Mono.error(new DcArgumentException("平台地锁记录ID无效")))
            .flatMap(lock -> {
                EvseVo evse = redisIotReadService.getEvseRedisCache(lock.getEvseNo());
                IotAssert.isNotNull(evse, "锁还没有绑定到桩");
                IotAssert.isNotBlank(evse.getSiteId(), "请先将桩绑定到场站，再操作");
                return anneffiParkingLockOpService.process(
                    csClientBizFacade.getChannelInfo(evse.getSiteId()),
                    AnneffiCommanderData.builder()
                        .deviceNo(lock.getSerialNumber())
                        .taskCode(ClientTaskCode.GET_LOCK_STATUS)
                        .build());
            })
            .map(s -> (ParkingLockStatus) s)
            .switchIfEmpty(Mono.error(new DcServerException("同步地锁状态异常")));
    }

    public void fetchParkingLockStatus(String siteId) {
        long start = 0L;
        int size = 100;

        ListParkingLockParam param = new ListParkingLockParam();
        param.setSize(size);
        param.setSiteId(siteId);
        List<ParkingLockVo> lockVoList;
        do {
            param.setStart(start);
            lockVoList = this.parkingLockRoDs.parkingLockList(param);
            start = start + lockVoList.size();
            Flux.fromIterable(lockVoList)
                .flatMap(lock -> this.fetchLockStatus(lock.getId())
                    .onErrorReturn(lock.getStatus())
                    .doOnNext(status -> {
                        if (!lock.getStatus().equals(status)) {
                            ParkingLockPo lockPo = new ParkingLockPo()
                                .setPartner(lock.getPartner())
                                .setSerialNumber(lock.getSerialNumber())
                                .setStatus(status);
                            parkingLockRwDs.upsetParkingLock(lockPo);

                            // 事件日志
                            eventLogService.parkingLockStatus(
                                lock.getPartner(), lock.getSerialNumber(), status);
                        }
                    }))
                .subscribe();
        } while (CollectionUtils.isNotEmpty(lockVoList) && lockVoList.size() == size);
    }

    public Mono<BaseResponse> fetchLockInfo(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID不能为空");
        }

        this.fetchParkingLockStatus(siteId);
        return Mono.just(RestUtils.success());
    }

    public void clsTimeoutJob() {
        anneffiParkingLockOpService.clsTimeoutJob();
    }
}
