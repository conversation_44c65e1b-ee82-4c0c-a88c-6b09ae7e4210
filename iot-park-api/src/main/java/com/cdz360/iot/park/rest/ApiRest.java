package com.cdz360.iot.park.rest;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.park.model.api.ParkBaseRes;
import com.cdz360.iot.park.model.api.ParkFeeReq;
import com.cdz360.iot.park.model.api.ParkFeeRes;
import com.cdz360.iot.park.model.api.ParkOrderCouponReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponRes;
import com.cdz360.iot.park.service.ApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @Classname ApiRest
 * @Description 对外接口
 * @Date 8/15/2022 5:02 PM
 * @Created by Rafael
 */
@Slf4j
@RestController
@RequestMapping(value = "/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ApiRest {

    @Autowired
    private ApiService apiService;

    // 查询订单停车优惠信息
    @PostMapping(value = "/v1/order/discount")
    public Mono<ParkBaseRes<ParkOrderCouponRes>> discount(@RequestBody ParkOrderCouponReq param) {
        log.info("查询订单停车优惠信息 = {}", JsonUtils.toJsonString(param));
        return apiService.getDiscount(param);
    }

    // 上报停车收费信息
    @PostMapping(value = "/v1/order/parkFee")
    public Mono<ParkFeeRes> parkFee(@RequestBody ParkFeeReq param) {
        log.info("上报停车收费信息 = {}", JsonUtils.toJsonString(param));
//        apiService.checkSign("QQQWWWEEE", param);
//        return parkBizService.checkCoupon(orderStopRequestV2);
        return apiService.parkFee(param);
    }
}