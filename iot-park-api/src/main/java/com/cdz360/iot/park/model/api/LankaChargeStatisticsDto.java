package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaChargeStatisticsDto
 * @Description
 * @Date 2/2/2024 4:10 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaChargeStatisticsDto {
    @Schema(description = "总停车费")
    private String charge;

    @Schema(description = "线上总收费")
    private String onLineCharge;

    @Schema(description = "线下总收费")
    private String offLineCharge;

    @Schema(description = "线上线下金额和时间优惠累计抵扣值")
    private String profitChargeTotal;

    @Schema(description = "线上累计优惠金额总面值")
    private String onLineProfitChargeNum;

    @Schema(description = "线上累计优惠金额总抵扣值")
    private String onLineProfitChargeValue;

    @Schema(description = "线下累计优惠金额总面值")
    private String offLineProfitChargeNum;

    @Schema(description = "线下累计优惠金额总抵扣值")
    private String offLineProfitChargeValue;

    @Schema(description = "单位(分钟)")
    private String onLineProfitTimeNum;

    @Schema(description = "线上累计优惠时间总抵扣值")
    private String onLineProfitTimeValue;

    @Schema(description = "线下累计优惠时间")
    private String offLineProfitTimeNum;

    @Schema(description = "线下累计优惠时间总抵扣值")
    private String offLineProfitTimeValue;

}