package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname LankaHbReq
 * @Description
 * @Date 1/26/2024 2:16 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LankaHbReq extends LankaBaseReq {
    @Schema(description = "场库名称")
    private String parkName;

    @Schema(description = "场库总车位数")
    private Integer spaceCount;

    @Schema(description = "场库空车位数")
    private Integer freeSpaceCount;

    @Schema(description = "场库可预约数")
    private Integer bookSpaceCount;

    @Schema(description = "场库在场预约数")
    private Integer bookInParkCount;

    @Schema(description = "区域属性")
    private List<LankaAreaDto> areaList;

    @Schema(description = "设备信息")
    private List<LankaDeviceDto> deviceList;
}