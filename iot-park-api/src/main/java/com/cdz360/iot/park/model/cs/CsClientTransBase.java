package com.cdz360.iot.park.model.cs;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CsClientTransBase {

    private int len;

    private long customerId;

    @Schema(description = "地锁供应商")
    private ParkingLockPartner partner;

    private ClientTaskCode taskCode;

    private int param1;

    private int param2;

    private byte[] customerIdBytes;
}
