package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 上传通道信息
 */
@Data
@Schema(description = "上传通道信息")
public class BsChannelInfo {


    @JsonProperty(value = "record_id")
    @Schema(description = "记录id，用于编辑删除查询")
    private Long recordId;

    @JsonProperty(value = "device_id")
    @Schema(description = "设备号")
    private String deviceId;


    @JsonProperty(value = "channel_id")
    @Schema(description = "通道编号")
    private Long channelId;

    @JsonProperty(value = "channel_name")
    @Schema(description = "通道名称")
    private String channelName;

    @JsonProperty(value = "pass_type")
    @Schema(description = "通道类型. 0:入口，1：出口，2出入口")
    private Integer passType;

}
