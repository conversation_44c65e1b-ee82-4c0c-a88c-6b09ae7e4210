package com.cdz360.iot.park.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactiveDataCoreHystrixFeignClientFactory implements FallbackFactory<ReactiveDataCoreFeignClient> {

    @Override
    public ReactiveDataCoreFeignClient apply(Throwable throwable) {
        return new ReactiveDataCoreFeignClient() {

            @Override
            public Mono<ObjectResponse<Integer>> setParkCouponDuration(String orderNo, Integer duration) {
                log.error("【服务熔断】 setParkCouponDuration Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactiveDataCoreFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super ReactiveDataCoreFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
