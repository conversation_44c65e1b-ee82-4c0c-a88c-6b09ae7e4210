package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaAreaDto
 * @Description
 * @Date 1/26/2024 3:34 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaAreaDto {
    @Schema(description = "场库名称")
    private String areaName;

    @Schema(description = "区域车位数")
    private Integer spaceCount;

    @Schema(description = "区域空位数")
    private Integer lastSpaceCount;

    @Schema(description = "区域可预约车位数")
    private Integer bookSpaceCount;

    @Schema(description = "区域在场预约数")
    private Integer bookInParkCount;

    @Schema(description = "区域Id")
    private Integer areaId;
}