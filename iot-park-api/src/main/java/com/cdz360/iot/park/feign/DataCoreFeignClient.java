package com.cdz360.iot.park.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.model.api.ParkFeeReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponRes;
import com.cdz360.iot.park.model.bs.BsParkOrder;
import com.cdz360.iot.park.model.cs.ParkingLockErrorParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * @Classname DataCoreFeignClient
 * @Description
 * @Date 4/21/2021 10:13 AM
 * @Created by Rafael
 */
@FeignClient(value = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = DataCoreHystrixFeignClientFactory.class)
public interface DataCoreFeignClient {
    @GetMapping("/dataCore/parkOrder/getParkSignKey")
    ObjectResponse<String> getParkSignKey(@RequestParam("parkId") Long parkId);

    @GetMapping("/dataCore/parkOrder/getParkSignKeyBySiteId")
    ObjectResponse<String> getParkSignKeyBySiteId(@RequestParam("siteId") String siteId);

    // 查询订单停车优惠信息
    @PostMapping("/dataCore/parkOrder/getDiscount")
    ObjectResponse<ParkOrderCouponRes> getDiscount(@RequestBody ParkOrderCouponReq param);

    // 上报停车收费信息
    @PostMapping("/dataCore/parkOrder/uploadOrderParkFee")
    BaseResponse uploadOrderParkFee(@RequestBody ParkFeeReq param);

    @PostMapping("/dataCore/parkOrder/in")
    ObjectResponse<Boolean> in(@RequestBody BsParkOrder bsParkOrder);

    @PostMapping("/dataCore/parkOrder/out")
    ObjectResponse<Boolean> out(@RequestBody BsParkOrder bsParkOrder);

    @PostMapping("/dataCore/parkOrder/checkParkCoupon")
    ObjectResponse<ParkCouponVo> checkParkCoupon(@RequestParam("orderNo") String orderNo,
                                                 @RequestParam("kwh") BigDecimal kwh);

    @PostMapping("/dataCore/parkOrder/setParkCouponDuration")
    ObjectResponse<Integer> setParkCouponDuration(@RequestParam("orderNo") String orderNo,
                                                  @RequestParam("duration") Integer duration);

    @PostMapping("/dataCore/msgSend/parkingLockError")
    BaseResponse sendParkingLockError(@RequestBody ParkingLockErrorParam param);

}