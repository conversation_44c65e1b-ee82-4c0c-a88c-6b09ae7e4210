package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @Classname ParkCouponISTService
 * @Description
 * @Date 5/16/2024 2:34 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponISTService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_IST, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        this.setCoupon(data.getSiteId(),
                data.getCarNo(),
                data.getParkId(),
                data.getParkSignKey(),
                data.getParkOrderNo(),
                data.getDuration())
            .doOnSuccess(e -> {
                if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    dataCoreFeignClient.setParkCouponDuration(orderStopRequestV2.getOrderNo(),
                        data.getDuration());

                } else {
                    log.error("第三方道闸IST返回状态异常: {}", e);
                }
            })
            .doOnError(e -> log.error(e.getMessage(), e))
            .subscribe();
        return Mono.just(RestUtils.success());
    }

    /**
     * 艾视特-停车减免优惠券
     * @param siteId
     * @param carNo
     * @param parkId
     * @param parkSignKey
     * @param parkOrderNo
     * @param duration
     * @return
     */
    public Mono<BaseResponse> setCoupon(String siteId,
        String carNo,
        Long parkId,
        String parkSignKey,
        String parkOrderNo,
        int duration) {
        return bsService.setCoupon(siteId, carNo, parkId, parkSignKey, parkOrderNo, duration);
    }
}