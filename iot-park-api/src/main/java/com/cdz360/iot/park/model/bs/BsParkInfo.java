package com.cdz360.iot.park.model.bs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 停车云注册车场上传
 */
@Data
@Schema(description = "停车云注册车场上传")
public class BsParkInfo {

    @Schema(description = "车场编号")
    private Long id;

    @Schema(description = "车场名称")
    private String companyName;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "创建时间")
    private Long createTime;


    @Schema(description = "城市编码")
    private Integer cityid;


    @Schema(description = "联系电话")
    private String phone;


    @Schema(description = "经度")
    private String longitude;


    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "总车位数")
    private Integer parkingTotal;

    @Schema(description = "状态. 0：可用，1表示删除")
    private Integer state;

    @Schema(description = "密钥")
    private String ukey;
}
