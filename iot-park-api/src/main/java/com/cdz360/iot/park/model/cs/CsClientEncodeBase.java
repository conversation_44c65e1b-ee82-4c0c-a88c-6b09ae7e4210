package com.cdz360.iot.park.model.cs;

import com.cdz360.iot.common.utils.ByteArrayUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CsClientEncodeBase {

    private CsClientTransBase base;

    public CsClientEncodeBase() {}
    public CsClientEncodeBase(Long appId) {
        base = new CsClientTransBase();
        base.setCustomerId(appId);
        base.setCustomerIdBytes(ByteArrayUtil.transInt(appId.intValue()));
    }
}
