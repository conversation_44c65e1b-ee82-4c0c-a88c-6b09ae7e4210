package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.EventDataBase;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class AnneffiSwitchLockHandler extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.SWITCH_LOCK;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        long lockId = msg.readUnsignedIntLE();
        log.warn("开闭结果: {}, result = {}", lockId, base.getParam1() == 1 ? "通过" : "没有权限");
        EventDataBase dataBase;

        if (base.getParam1() == 1){
            dataBase = EventDataBase.success(base.getParam1() + "");
        } else {
            dataBase = EventDataBase.fail("没有权限");
        }

        dataBase.setTaskCode(TASK_CODE)
                .setPartner(base.getPartner())
                .setDeviceNo("" + lockId);
        parkingLockObserver.addEventMsg(dataBase);
        parkingLockObserver.notifyEvent();
        return Mono.empty();
    }
}
