package com.cdz360.iot.park.service.cs.anneffi.commander;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.service.cs.common.CsClientTransCommander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AnneffiClientCommanderFactory {

    private Map<ClientTaskCode, CsClientTransCommander> commanders = new HashMap<>();

    public void addCommander(ClientTaskCode taskCode, CsClientTransCommander commander) {
        this.commanders.put(taskCode, commander);
    }

    public Mono<CsClientTransCommander> getCommander(ClientTaskCode taskCode) {
        return Mono.justOrEmpty(this.commanders.get(taskCode));
    }
}
