package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "上传月卡套餐")
public class BsMonthCardSchema {
    @JsonProperty(value = "package_id")
    @Schema(description = "月卡套餐编号")
    private String packageId;


    @JsonProperty(value = "create_time")
    @Schema(description = "新建时间(unix时间戳格式)")
    private Long createTime;


    @JsonProperty(value = "update_time")
    @Schema(description = "修改时间(unix时间戳格式)")
    private Long updateTime;

    @Schema(description = "月卡套餐描述")
    private String describe;


    @JsonProperty(value = "car_type")
    @Schema(description = "车辆类型")
    private String carType;

    @JsonProperty(value = "period")
    @Schema(description = "缴费周期(只作显示用，购买月卡按月计算金额）", example = "月，季，半年，年")
    private String period;


    @Schema(description = "月卡套餐价格")
    private String price;


    @Schema(description = "月卡套餐名称")
    private String name;


}
