package com.cdz360.iot.park.model.api;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/4
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ChargeReduction {
    private String parkingId;
    private String plateNumber;
    private Integer favourableDuration;
    private Long timestamp;
    private String appKey;
    private String sign;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }


    @Data
    public static class Response {
        private String status;

        private String message;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }
}
