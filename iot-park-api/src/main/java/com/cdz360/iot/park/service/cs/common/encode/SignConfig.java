package com.cdz360.iot.park.service.cs.common.encode;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * @Classname SignConfig
 * @Description
 * @Date 6/18/2024 10:48 AM
 * @Created by Rafael
 */
public class SignConfig {
    private static volatile Wrapper wrapper = new Wrapper() {
    };

    public static void enableUrlencodeMode() {
        wrapper = new Wrapper() {
            @Override
            public String wrapVal(Object val) {
                String valStr = String.valueOf(val);
                try {
                    return URLEncoder.encode(valStr, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    return valStr;
                }
            }
        };
    }

    public static String wrapVal(Object val) {
        return wrapper.wrapVal(val);
    }

    interface Wrapper {
        default String wrapVal(Object val) {
            return String.valueOf(val);
        }
    }

}
