package com.cdz360.iot.park.task;

import com.cdz360.iot.park.service.ParkingLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CsClientTask {

    @Autowired
    private ParkingLockService parkingLockService;

    /**
     * 地锁状态刷新
     */
    @Scheduled(cron = "0 0/1 * * * ?")  // 每1分钟执行一次
    public void refreshParkingLockStatus() {
        try {
            parkingLockService.fetchParkingLockStatus(null);
        } catch (Exception ex) {
            log.error("地锁状态刷新异常: err = {}", ex.getMessage(), ex);
        }
    }

    /**
     * 清理超时没有回复消息
     */
    @Scheduled(cron = "0 0/1 * * * ?") // 每1分钟执行一次
    public void clsTimeoutJob() {
        try {
            parkingLockService.clsTimeoutJob();
        } catch (Exception ex) {
            log.error("清理超时没有回复消息: err = {}", ex.getMessage(), ex);
        }
    }
}
