package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaPassInfoDto
 * @Description
 * @Date 2/2/2024 3:58 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaPassInfoDto {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "入场时间")
    private Date inTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "出场时间")
    private Date outTime;

    @Schema(description = "入场图片名")
    private String inImage;

    @Schema(description = "出场图片名")
    private String outImage;

    @Schema(description = "入口通道名称")
    private String inChannel;

    @Schema(description = "出口通道名称")
    private String outChannel;

    @Schema(description = "抬杆模式")
    private String openGateMode;

    @Schema(description = "匹配模式")
    private String matchMode;
}