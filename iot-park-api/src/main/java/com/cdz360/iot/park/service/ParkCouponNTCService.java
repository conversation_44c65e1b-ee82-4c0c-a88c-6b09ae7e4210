package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponNtcVo;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Service
public class ParkCouponNTCService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @Autowired
    private EvseRoDs evseRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Value("${iot.park.bsUrlNtc:}")
    private String bsUrlNtc;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_NTC, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        ParkCouponNtcVo req = new ParkCouponNtcVo();
        req.setAppkey(data.getParkAppId());
        req.setTime(String.valueOf(System.currentTimeMillis()));
        req.setSecret(data.getParkAppSecret());
        req.setParkCode(String.format("%09d", data.getParkId()));
        req.setOrderNo(data.getParkOrderNo());
        req.setPlateNo(data.getCarNo());
        req.setStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderStopRequestV2.getStartTime()));
        req.setEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orderStopRequestV2.getStopTime()));
        req.setStationId(data.getSiteId());
        SitePo site = siteRoDs.getSite(data.getSiteId());
        req.setStationName(site.getName());
        req.setDeviceNo(orderStopRequestV2.getEvseNo());
        EvsePo evse = evseRoDs.getEvse(orderStopRequestV2.getEvseNo());
        req.setDeviceName(evse.getName());
//        req.setSpaceNo();
        req.setPower(orderStopRequestV2.getKwh().toString());
//        req.setElecMoney();
        req.setSeviceMoney(orderStopRequestV2.getServFee().toString());
        req.setTotalMoney(orderStopRequestV2.getServFee().add(orderStopRequestV2.getElecFee()).toString());
        req.setFreeType("1");
        req.setFreeDuration("2.0");
//        req.setFreeAmount();
        req.setISP(data.getISP());

        String paramAuth = getHeaderAuthNtc(bsUrlNtc + "/coupon/standardCharge", req, data.getParkAppSecret());

        this.setCouponNtc(req, paramAuth)
                .doOnSuccess(e -> {
                    if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.info("第三方道闸NTC返回成功");
                    } else {
                        log.warn("第三方道闸NTC返回状态异常: {}", e);
                    }
                })
                .doOnError(e -> log.error(e.getMessage(), e))
                .subscribe(
                        res -> log.info("第三方道闸NTC executed successfully"),
                        err -> log.error("第三方道闸NTC Error executing : {}", err.getMessage())
                );
        return Mono.just(RestUtils.success());
    }

    public Mono<BaseResponse> setCouponNtc(ParkCouponNtcVo param, String headerAuth) {
        log.info("ntc请求参数: {}", toJsonString(param));
        return bsService.setCouponNtc(param, headerAuth);
    }

    //sign=MD5(请求 URI+参数串+secret)；加密的字符串统一采用 UTF-8 编码。Md5 采用 32 位小写。
    private static String getHeaderAuthNtc(String uri, ParkCouponNtcVo param, String appSecret) {
        String jsonStr = toJsonString(param);
        String encryptStr = uri + jsonStr + appSecret;
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8)).toLowerCase();
        log.info("NTC sign: {}", s);
        return s;
    }

    //按参数名 ascii 码升序排序。
    private static String toJsonString(Object obj) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> map = mapper.convertValue(obj, new TypeReference<Map<String, Object>>() {
        });
        Map<String, Object> sortedMap = new LinkedHashMap<>();
        map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(x -> sortedMap.put(x.getKey(), x.getValue()));
        try {
            return mapper.writeValueAsString(sortedMap);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
            return "";
        }
    }
}