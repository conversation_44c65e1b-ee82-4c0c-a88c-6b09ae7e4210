package com.cdz360.iot.park.service.cs;

import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.park.cfg.CsClientProperties;
import com.cdz360.iot.park.model.cs.CsClientChannel;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class CsClientBizFacade {

    @Autowired
    private CsClientProperties csClientProperties;

    @Autowired
    private CsClientBizFactory clientBizFactory;

    private final Map<String, CsClientChannel> channelMap = new ConcurrentHashMap<>();

    public CsClientChannel getChannelInfo(String siteId) {
        CsClientProperties.LotServer serv = csClientProperties.getLotServer(siteId);

        // FIXME: 目前指定配置第一个为默认供应商，后续可根据实际调整
        if (null == serv && csClientProperties.getLotServerList().size() > 0) {
            serv = csClientProperties.getLotServerList().get(0);
        }

        IotAssert.isNotNull(serv, "场站没有配置供应商服务信息");
        return this.channelMap.get(serv.channelKey());
    }

    public Channel getChannel(String key) {
        CsClientChannel ch = this.channelMap.get(key);
        return null != ch ? ch.getChannel() : null;
    }

    public void putChannel(String key, CsClientChannel ch) {
        this.channelMap.put(key, ch);
    }

    public void csClientActive(String channelKey) {
        log.info("[客户端: {}]通道激活", channelKey);
        Mono.justOrEmpty(channelMap.get(channelKey))
                .flatMap(channel -> clientBizFactory.getHandler(channel.getPartner())
                        .flatMap(handler -> handler.active(channel))
                        .doOnNext(buf -> CsClientChannel.channelWrite(channel, buf)))
                .subscribe();
    }

    public void csClientRead(String channelKey, ByteBuf in) {
        log.info("[客户端: {}]接收到数据: msg = {}", channelKey, ByteBufUtil.hexDump(in));
        Mono.justOrEmpty(channelMap.get(channelKey))
                .flatMap(channel -> clientBizFactory.getHandler(channel.getPartner())
                        .flatMap(handler -> handler.read(channel, in))
                        .doOnNext(buf -> CsClientChannel.channelWrite(channel, buf)))
                .subscribe();
    }

    public void csClientError(String channelKey, String flag) {
        log.info("[客户端: {}]异常: {}", channelKey, flag);
        Mono.justOrEmpty(channelMap.get(channelKey))
                .doOnNext(channel -> {
                    Channel ch = channel.getChannel();
                    if (null != ch && ch.isActive()) {
                        ch.close();
                        channelMap.remove(channelKey);
                        log.info("[客户端: {}]已移除", channelKey);
                    }
                })
                .flatMap(channel -> clientBizFactory.getHandler(channel.getPartner())
                        .doOnNext(handler -> handler.error(channel, flag)))
                .subscribe();
    }

    public void csClientHb(String channelKey) {
        Mono.justOrEmpty(channelMap.get(channelKey))
                .filter(CsClientChannel::isLogin)
                .doOnNext(ch -> log.info("[客户端: {}]-[{}]心跳处理", channelKey, ch.getAppId()))
                .flatMap(channel -> clientBizFactory.getHandler(channel.getPartner())
                        .flatMap(handler -> handler.hb(channel))
                        .doOnNext(buf -> CsClientChannel.channelWrite(channel, buf)))
                .subscribe();
    }

}
