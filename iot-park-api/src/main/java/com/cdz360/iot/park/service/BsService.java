package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.park.po.ParkChannelPo;
import com.cdz360.iot.model.park.vo.*;
import com.cdz360.iot.park.client.BsClient;
import com.cdz360.iot.park.model.bs.BsChargeCoupon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Slf4j
@Service
class BsService {


    @Autowired
    private BsClient bsClient;


    /**
     * 艾视特-停车减免优惠券
     * @param siteId
     * @param carNo
     * @param parkId
     * @param parkSignKey
     * @param parkOrderNo
     * @param duration
     * @return
     */
    public Mono<BaseResponse> setCoupon(String siteId,
                                        String carNo,
                                        Long parkId,
                                        String parkSignKey,
                                        String parkOrderNo,
                                        int duration) {
        BsChargeCoupon coupon = new BsChargeCoupon();
        coupon.setCarNumber(carNo)
                .setOrderId(parkOrderNo)
                .setDuration(duration)
                .setUuid(UUID.randomUUID().toString().replaceAll("-", ""));
//        Long parkId = Long.parseLong(siteId);
        Mono<BsChargeCoupon.Response> res = this.bsClient.setDiscount(parkId, parkSignKey, coupon);
        return res.map(bsRes -> {
            if (bsRes.getState() == 1) {
                return RestUtils.success();
            } else {
                BaseResponse ret = new BaseResponse();
                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                ret.setError(bsRes.getErrmsg());
                return ret;
            }
        });
    }

    /**
     * 抬杆操作
     *
     * @param channelPo 道闸信息
     * @return
     */
    public Mono<Boolean> upLiftRod(ParkChannelPo channelPo, String parkSignKey) {
        return this.bsClient.opLiftRod(channelPo, parkSignKey, true);
    }

    /**
     * 中控-停车减免优惠券
     * @param param
     * @param headerAuth
     * @return
     */
    public Mono<BaseResponse> setCouponZk(ParkCouponZkVo param, String headerAuth) {
        Mono<String> res = this.bsClient.setDiscountZk(param, headerAuth);
        return res.map(bsRes -> {
            log.info("ZK返回结果: {},对应车牌：{}", bsRes, param.getPlate());
            return BaseResponse.success();
//            if (bsRes.getState() == 1) {
//                return RestUtils.success();
//            } else {
//                BaseResponse ret = new BaseResponse();
//                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
//                ret.setError(bsRes.getErrmsg());
//                return ret;
//            }
        });
    }

    /**
     * 宜泊-停车减免优惠券
     * @param param
     * @param parkCode
     * @param paramAuth
     * @return
     */
    public Mono<BaseResponse> setCouponYb(ParkCouponYbVo param, String parkCode, String paramAuth) {
        Mono<String> res = this.bsClient.setDiscountYb(param, parkCode, paramAuth);
        return res.map(bsRes -> {
            log.info("YB返回结果: {},对应车牌：{}", bsRes, param.getCarNo());
            return BaseResponse.success();
        });
    }

    /**
     * 深圳创享智能开发有限公司-停车减免优惠券
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCouponCx(ParkCouponCxVo param) {
        Mono<ParkCouponCxVo.Response> res = this.bsClient.setDiscountCx(param);
        return res.map(bsRes -> {
            log.info("CX返回结果: {},对应车牌：{}", bsRes, param.getPlateNumber());
            if ("1".equals(bsRes.getStatus())) {
                return RestUtils.success();
            } else {
                BaseResponse ret = new BaseResponse();
                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                ret.setError(bsRes.getMessage());
                return ret;
            }
        });
    }

    /**
     * 宁停车-停车减免优惠券
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCouponNtc(ParkCouponNtcVo param, String headerAuth) {
        Mono<ParkCouponNtcVo.Response> res = this.bsClient.setDiscountNtc(param, headerAuth);
        return res.map(bsRes -> {
            log.info("NTC返回结果: {},对应车牌：{}", bsRes, param.getPlateNo());
            if (0 == bsRes.getResCode()) {
                return RestUtils.success();
            } else {
                BaseResponse ret = new BaseResponse();
                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                ret.setError(bsRes.getResMsg());
                return ret;
            }
        });
    }

    /**
     * 捷顺-停车减免优惠券
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCouponJs(ParkJsReqVo<ParkJs3CReqVo<ParkJsCouponReqVo>> param) {
        Mono<String> res = this.bsClient.setDiscountJs(param);
        return res.map(bsRes -> {
            log.info("JS返回结果: {},对应车牌：{}", bsRes, param.getBizContent().getParams().getAttributes().getCarNo());
            return BaseResponse.success();
        });
    }

    /**
     * 苏停车-停车减免优惠券
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCouponStc(ParkCouponStcVo param) {
        Mono<String> res = this.bsClient.setDiscountStc(param);
        return res.map(bsRes -> {
            log.info("STC返回结果: {},对应车牌：{}", bsRes, param.getPlateNumber());
            ParkCouponStcVo.Response response = JsonUtils.fromJson(bsRes, ParkCouponStcVo.Response.class);
            if (000 == response.getCode()) {
                return RestUtils.success();
            } else {
                BaseResponse ret = new BaseResponse();
                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                ret.setError(response.getMessage());
                return ret;
            }
        });
    }

    public Mono<BaseResponse> setCouponPp(ParkCouponPpVo param) {
        Mono<ParkCouponPpVo.Response> res = this.bsClient.setCouponPp(param);
        return res.map(bsRes -> {
            log.info("PP返回结果: {},对应车牌：{}", bsRes, param.getPlate());
            if (1001 == bsRes.getCode()) {
                return RestUtils.success();
            } else {
                BaseResponse ret = new BaseResponse();
                ret.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                ret.setError(bsRes.getMessage());
                return ret;
            }
        });
    }
}
