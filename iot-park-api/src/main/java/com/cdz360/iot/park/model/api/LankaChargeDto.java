package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaChargeDto
 * @Description
 * @Date 2/2/2024 4:21 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaChargeDto {
    @Schema(description = "支付订单号")
    private String payNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结算时间")
    private Date getTime;

    @Schema(description = "支付金额")
    private String payCharge;

    @Schema(description = "支付类型")
    private String payKind;

    @Schema(description = "支付渠道")
    private String payChannel;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "每笔交易生成唯一流水号（支付结果下发）")
    private String transactionId;
}