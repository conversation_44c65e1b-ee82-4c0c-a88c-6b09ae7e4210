package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaOutDataDto
 * @Description
 * @Date 2/2/2024 3:52 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaOutDataDto {
    @Schema(description = "入场记录编号")
    private String orderId;

    @Schema(description = "操作员Id")
    private String operatorId;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "发票号吗")
    private String invoiceNo;

    @Schema(description = "车辆相关信息")
    private LankaCarInfoDto carInfo;

    @Schema(description = "车主信息")
    private LankaUserInfoDto userInfo;

    @Schema(description = "通行信息")
    private LankaPassInfoDto passInfo;

    @Schema(description = "收费统计信息")
    private LankaChargeStatisticsDto chargeStatistics;

    @Schema(description = "收费明细")
    private List<LankaChargeDto> chargeList;

    @Schema(description = "优惠明细")
    private List<LankaProfitDto> profitList;

    @Schema(description = "车位信息")
    private List<LankaPlaceInfoDto> placeInfo;

    @Schema(description = "开闸耗时")
    private String costTime;

    @Schema(description = "空位数")
    private String spaceCount;

    @Schema(description = "其他联动设备图片信息")
    private List<String> imageList;

    @Schema(description = "图片名")
    private String imageName;
}