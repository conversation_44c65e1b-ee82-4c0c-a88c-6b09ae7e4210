package com.cdz360.iot.park.service.cs;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiCsClientEncode;
import com.cdz360.iot.park.service.cs.anneffi.handler.AnneffiClientHandlerFactory;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;

@Slf4j
@Service(value = "anneffiClientHandler")
public class AnneffiClientHandler implements CsClientHandler {

    private static final long HEAD = 0xEF0101EFL;

    @Autowired
    private AnneffiClientCommander clientCommander;

    @Autowired
    private AnneffiClientHandlerFactory clientHandlerFactory;

    @Override
    public CsClientTransBase preCommon(ByteBuf msg) {
        CsClientTransBase base = new CsClientTransBase();
        base.setLen(msg.readUnsignedShortLE());
        base.setCustomerIdBytes(new byte[4]);
        msg.getBytes(msg.readerIndex(), base.getCustomerIdBytes());
        base.setCustomerId(msg.readUnsignedIntLE());
        msg.readUnsignedIntLE(); // 会话密钥(SecKey)都为0，不加密
        return base.setTaskCode(ClientTaskCode.codeOf(msg.readUnsignedByte()))
                .setParam1(msg.readByte())
                .setParam2(msg.readShortLE());
    }

    @Override
    public boolean checkSign(ByteBuf msg) {
        byte[] buf = ByteBufUtil.getBytes(msg, msg.readerIndex(), msg.readableBytes() - 2);
        int dest = acc(buf, buf.length);
        int src = msg.getUnsignedShortLE(msg.readerIndex() + buf.length);
        boolean result = (dest == src);
        if (!result) {
            log.warn("校验和不正确: msg = {}; sign index = {} ; compare = [{} / {}]",
                    ByteBufUtil.hexDump(msg), msg.readableBytes() - 2, dest, src);
        }
        return result;
    }

    public static int acc(byte[] buf, int len) {
        int c = 0;
        for (int i = 0; i < len; i++) {
            c += buf[i] & 0x00FF;
        }
        return (0xFFFF & c);
    }

    @Override
    public Mono<ByteArrayOutputStream> active(CsClientChannel channel) {
        AnneffiCsClientEncode encode = new AnneffiCsClientEncode(channel.getAppId())
                .setAppKey(channel.getAppKey());
        encode.getBase().setTaskCode(ClientTaskCode.LOGIN);
        return clientCommander.process(encode);
    }

    @Override
    public Mono<ByteArrayOutputStream> read(CsClientChannel channel, ByteBuf msg) {
        long head = msg.readUnsignedInt();
        if (HEAD != head) {
            log.error("报文头不匹配: head = {}", head);
            msg.resetReaderIndex();
            return Mono.empty();
        }

        return Mono.justOrEmpty(msg)
                .filter(this::checkSign)
                .switchIfEmpty(Mono.error(new DcArgumentException("校验和校验不通过")))
                .map(this::preCommon)
                .flatMap(base -> this.clientHandlerFactory.getHandler(base.getTaskCode())
                        .switchIfEmpty(Mono.error(new DcServerException("不支持的消息: code = " + base.getTaskCode())))
                        .flatMap(handler -> handler.process(msg, base.setPartner(channel.getPartner())))
                        .doOnNext(x -> channel.setLogin(true)));
    }

    @Override
    public void error(CsClientChannel channel, String flag) {

    }

    @Override
    public Mono<ByteArrayOutputStream> hb(CsClientChannel channel) {
        CsClientEncodeBase encode = new CsClientEncodeBase(channel.getAppId());
        encode.getBase().setTaskCode(ClientTaskCode.HB);
        return clientCommander.process(encode);
    }
}
