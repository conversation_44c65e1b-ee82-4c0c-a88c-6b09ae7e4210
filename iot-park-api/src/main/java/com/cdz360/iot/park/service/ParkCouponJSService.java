package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.*;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import com.cdz360.iot.park.service.cs.common.encode.JieshunSigner;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * @Classname ParkCouponJSService
 * @Description
 * @Date 6/17/2024 3:55 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ParkCouponJSService extends AbstractParkCouponService {

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_JS, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        String cid = "";
        String projectCode = "";
        String abilityCode = "";
        String secret = "";
        String parkId = "";
        if (StringUtils.isNotBlank(data.getParkConfig())) {
            final JsonNode jsonNode = JsonUtils.fromJson(data.getParkConfig());
            cid = jsonNode.get("cid").asText("");
            projectCode = jsonNode.get("projectCode").asText("");
            abilityCode = jsonNode.get("abilityCode").asText("");
            secret = jsonNode.get("secret").asText("");
            parkId = jsonNode.get("parkId").asText("");
        }

        ParkJsCouponCarReqVo couponAttrs = new ParkJsCouponCarReqVo();
        couponAttrs.setIsRepeat(0)
                .setCarNo(formatCarNo(data.getCarNo()))
                .setParkCode(parkId)
                .setCouponType(1)
                .setCouponValue(2);//固定2小时
        ParkJsCouponReqVo couponParam = new ParkJsCouponReqVo();
        couponParam.setAttributes(couponAttrs);
        ParkJs3CReqVo<ParkJsCouponReqVo> extraParam = new ParkJs3CReqVo();
        extraParam.setParams(couponParam)
                .setCid(cid);
        ParkJsReqVo<ParkJs3CReqVo<ParkJsCouponReqVo>> param = new ParkJsReqVo();
        param.setAppId(data.getParkAppId())
                .setMethod("3c.order.discount")
                .setTimestamp((new Date()).getTime())
                .setProjectCode(projectCode)
                .setAbilityCode(abilityCode)
                .setSignType("md5")
                .setFormat("form")
                .setCharset("utf-8")
                .setBizContent(extraParam);
        final String sign = JieshunSigner.buildSignJson(
                JsonUtils.fromJson(JsonUtils.toJsonString(param)), secret);
        param.setSign(sign);
        this.setCoupon(param).subscribe();
        return Mono.just(RestUtils.success());
    }

    /**
     * 停车减免优惠券
     *
     * @param param
     * @return
     */
    public Mono<BaseResponse> setCoupon(ParkJsReqVo<ParkJs3CReqVo<ParkJsCouponReqVo>> param) {
        log.info("js请求参数: {}", JsonUtils.toJsonString(param));
        return bsService.setCouponJs(param);
    }

    private String formatCarNo(String carNo) {
        if (carNo != null && carNo.length() > 1) {
            return carNo.charAt(0) + "-" + carNo.substring(1);
        }
        return carNo;
    }
}