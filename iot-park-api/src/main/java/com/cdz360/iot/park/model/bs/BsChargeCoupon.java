package com.cdz360.iot.park.model.bs;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电桩下发优惠信息")
public class BsChargeCoupon {
    @JsonProperty(value = "car_number")
    @Schema(description = "车牌号", required = true)
    private String carNumber;

    @JsonProperty(value = "order_id", required = true)
    @Schema(description = "订单记录号(车辆在停车场停车唯一订单编号，对应入场订单编号,支持数字 字母 _ )")
    private String orderId;

    @JsonProperty(value = "reduce_amount")
    @Schema(description = "减免金额，单位：元")
    private BigDecimal reduceAmount;


    @Schema(description = "减免时长，单位：分钟")
    private Integer duration;

    @JsonProperty(value = "deduction_money")
    @Schema(description = "积分抵扣金额，单位：元 (不使用)")
    private Integer deductionMoney;


    @JsonProperty(value = "deduction_time")
    @Schema(description = "积分抵扣时间，单位：分钟 (不使用)")
    private Integer deductionTime;

    @Schema(description = "备注")
    private String remark;

    @JsonProperty(value = "start_charging_time")
    @Schema(description = "开始充电时间")
    private String startChargingTime;

    @JsonProperty(value = "stop_charging_time")
    @Schema(description = "结束充电时间")
    private String stopChargingTime;

    @Schema(description = "唯一值")
    private String uuid;


    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BsResMsg {

        @Schema(description = "唯一值")
        private String uuid;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }
}
