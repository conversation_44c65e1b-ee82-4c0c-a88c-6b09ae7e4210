package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Classname ParkBaseReq
 * @Description
 * @Date 8/16/2022 1:29 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class ParkBaseReq {
    private String siteId;
    private String carNo;
    private String seqNo;
    private String sign;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inTime;
}