package com.cdz360.iot.park.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.model.api.ParkFeeReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponRes;
import com.cdz360.iot.park.model.bs.BsParkOrder;
import com.cdz360.iot.park.model.cs.ParkingLockErrorParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @Classname DataCoreHystrixFeignClientFactory
 * @Description
 * @Date 4/21/2021 10:24 AM
 * @Created by Rafael
 */
@Slf4j
@Component
class DataCoreHystrixFeignClientFactory implements FallbackFactory<DataCoreFeignClient> {

    @Override
    public DataCoreFeignClient create(Throwable cause) {
        return new DataCoreFeignClient() {
            @Override
            public ObjectResponse<String> getParkSignKey(Long parkId) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<String> getParkSignKeyBySiteId(String siteId) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ParkOrderCouponRes> getDiscount(ParkOrderCouponReq param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse uploadOrderParkFee(ParkFeeReq param) {
                log.error("【服务熔断】。Service = {}, 消息推送: = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Boolean> in(BsParkOrder bsParkOrder) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> out(BsParkOrder bsParkOrder) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ParkCouponVo> checkParkCoupon(String orderNo, BigDecimal kwh) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> setParkCouponDuration(String orderNo, Integer duration) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse sendParkingLockError(ParkingLockErrorParam param) {
                log.error("【服务熔断】。Service = {}, 消息推送: = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return RestUtils.serverBusy();
            }
        };
    }
}