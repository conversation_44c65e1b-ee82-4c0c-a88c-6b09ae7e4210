package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @Classname AbstractParkCouponService
 * @Description
 * @Date 5/16/2024 2:37 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class AbstractParkCouponService implements IParkCouponService {

    @Autowired
    protected ParkCouponServiceFactory parkCouponServiceFactory;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    protected ParkCouponVo getParkCoupon(OrderStopRequestV2 orderStopRequestV2) {
        ObjectResponse<ParkCouponVo> parkCouponVoObjectResponse =
            dataCoreFeignClient.checkParkCoupon(orderStopRequestV2.getOrderNo(),
                orderStopRequestV2.getKwh());

        log.info("检查停车减免并推送减免res: {}", JsonUtils.toJsonString(parkCouponVoObjectResponse));

        ParkCouponVo data = null;
        if(parkCouponVoObjectResponse != null && parkCouponVoObjectResponse.getData() != null) {
            data = parkCouponVoObjectResponse.getData();
        }
        return data;
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        return Mono.just(RestUtils.success());
    }
}