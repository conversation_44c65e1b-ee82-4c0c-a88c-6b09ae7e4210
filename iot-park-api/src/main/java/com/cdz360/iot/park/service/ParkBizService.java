/**
 * 艾停车API文档: http://park-api.sciseetech.com/istparking/1742273
 */
package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ParkChannelRoDs;
import com.cdz360.iot.ds.rw.ParkChannelRwDs;
import com.cdz360.iot.ds.rw.ParkDeviceRwDs;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.po.ParkChannelPo;
import com.cdz360.iot.model.park.po.ParkDeviceIp;
import com.cdz360.iot.model.park.po.ParkDevicePo;
import com.cdz360.iot.model.park.type.ParkChannelType;
import com.cdz360.iot.model.park.vo.*;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import com.cdz360.iot.park.feign.ReactiveDataCoreFeignClient;
import com.cdz360.iot.park.model.bs.BsChannelInfo;
import com.cdz360.iot.park.model.bs.BsDeviceInfo;
import com.cdz360.iot.park.model.bs.BsParkOrder;
import com.cdz360.iot.park.model.bs.BsReqMsg;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Service
public class ParkBizService {

    @Autowired
    private ParkChannelRoDs parkChannelRoDs;

    @Autowired
    private ParkChannelRwDs parkChannelRwDs;

    @Autowired
    private ParkDeviceRwDs parkDeviceRwDs;

    @Autowired
    private BsService bsService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ReactiveDataCoreFeignClient reactiveDataCoreFeignClient;

    // 第三方停车对接类型:
    private static final String PARK_TYPE_IST = ParkConstants.PARK_TYPE_IST;//"IST";// IST艾视特
    private static final String PARK_TYPE_ZK = ParkConstants.PARK_TYPE_ZK;//"ZK";// ZK中控
    private static final String PARK_TYPE_YB = ParkConstants.PARK_TYPE_YB;//"YB";// YB宜泊
    private static final String PARK_TYPE_CX = ParkConstants.PARK_TYPE_CX;//"CX";// CX 深圳创享智能开发有限公司(扬中停车场)
    private static final String PARK_API_TYPE_ZK = ParkConstants.PARK_API_TYPE_ZK;//"DINGCHONG";
    private static final int PARK_API_ZK_DURATION_RATE = 30;// 以30分钟为单位

    @Autowired
    private ParkCouponISTService parkCouponISTService;

    /**
     * 艾视特-停车减免优惠券
     * @param siteId
     * @param carNo
     * @param parkId
     * @param parkSignKey
     * @param parkOrderNo
     * @param duration
     * @return
     */
    public Mono<BaseResponse> setCoupon(String siteId,
        String carNo,
        Long parkId,
        String parkSignKey,
        String parkOrderNo,
        int duration) {
        return parkCouponISTService.setCoupon(siteId, carNo, parkId, parkSignKey, parkOrderNo, duration);
    }


    /**
     * 中控-停车减免优惠券
     * @param param
     * @param headerAuth
     * @return
     */
    @Deprecated
    public Mono<BaseResponse> setCouponZk(ParkCouponZkVo param, String headerAuth) {
        log.info("zk请求参数: {}, header: {}", JsonUtils.toJsonString(param), headerAuth);
        return bsService.setCouponZk(param, headerAuth);
    }

    /**
     * 宜泊-停车减免优惠券
     * @param param
     * @param parkCode
     * @param paramAuth
     * @return
     */
    @Deprecated
    public Mono<BaseResponse> setCouponYb(ParkCouponYbVo param, String parkCode, String paramAuth) {
        log.info("yb请求参数: {}, parkCode: {}, paramAuth: {}", param, parkCode, paramAuth);
        return bsService.setCouponYb(param, parkCode, paramAuth);
    }

    /**
     * 深圳创享智能开发有限公司-停车减免优惠券
     * @param param
     * @return
     */
    @Deprecated
    public Mono<BaseResponse> setCouponCx(ParkCouponCxVo param) {
        log.info("cx请求参数: {}", JsonUtils.toJsonString(param));
        return bsService.setCouponCx(param);
    }

    public String getParkSignKey(Long parkId) {
        ObjectResponse<String> parkSignKey = dataCoreFeignClient.getParkSignKey(parkId);

        if(parkSignKey == null || parkSignKey.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            return "";
        } else {
            return parkSignKey.getData();
        }
    }

    public void parkIn(BsParkOrder bsParkOrder) {
        ObjectResponse<Boolean> in = dataCoreFeignClient.in(bsParkOrder);
    }

    public void parkOut(BsParkOrder bsParkOrder) {
        ObjectResponse<Boolean> out = dataCoreFeignClient.out(bsParkOrder);
    }

    @Autowired
    private ParkCouponServiceFactory parkCouponServiceFactory;

    public Mono<BaseResponse> checkCoupon(OrderStopRequestV2 orderStopRequestV2) {

        final ParkCouponVo parkCoupon = parkCouponServiceFactory.getParkCoupon(orderStopRequestV2);

        if(parkCoupon == null) {
            return Mono.just(RestUtils.success());
        }

        final IParkCouponService strategy = parkCouponServiceFactory.getStrategy(
            parkCoupon.getParkType());

        if(strategy == null) {
            return Mono.just(RestUtils.success());
        }
        return strategy.orderStop(orderStopRequestV2, parkCoupon);

//        ObjectResponse<ParkCouponVo> parkCouponVoObjectResponse =
//                dataCoreFeignClient.checkParkCoupon(orderStopRequestV2.getOrderNo(),
//                        orderStopRequestV2.getKwh());
//
//        log.info("检查停车减免并推送减免res: {}", JsonUtils.toJsonString(parkCouponVoObjectResponse));
//
//
//        if(parkCouponVoObjectResponse != null && parkCouponVoObjectResponse.getData() != null) {
//            ParkCouponVo data = parkCouponVoObjectResponse.getData();
//            if(PARK_TYPE_IST.equals(data.getParkType())) {
//                this.setCoupon(data.getSiteId(),
//                                data.getCarNo(),
//                                data.getParkId(),
//                                data.getParkSignKey(),
//                                data.getParkOrderNo(),
//                                data.getDuration())
//                        .doOnSuccess(e -> {
//                            if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
//                                dataCoreFeignClient.setParkCouponDuration(orderStopRequestV2.getOrderNo(),
//                                        parkCouponVoObjectResponse.getData().getDuration());
//
//                            } else {
//                                log.error("第三方道闸返回状态异常: {}", e);
//                            }
//                        })
//                        .doOnError(e -> log.error(e.getMessage(), e))
//                        .subscribe();
//                return Mono.just(RestUtils.success());
//            } else if(PARK_TYPE_ZK.equals(data.getParkType())) {
//                ParkCouponZkMetaVo meta = new ParkCouponZkMetaVo();
//                meta.setName(data.getCarNo())
//                        .setValue(data.getDuration() / PARK_API_ZK_DURATION_RATE);
//
//                ParkCouponZkVo param = new ParkCouponZkVo();
//                param.setApiStoreCode(String.valueOf(data.getParkId())) // FIXME 此处沿用的parkId，应该是parkStoreCode才对
//                        .setApiType(PARK_API_TYPE_ZK)
//                        .setAppId(data.getParkAppId())
//                        .setEventId(orderStopRequestV2.getOrderNo())
//                        .setPlate(data.getCarNo())
//                        .setSubject("车辆" + data.getCarNo() + "停车减免")
//                        .setMeta(meta);
//
//                String headerAuth = getHeaderAuthZk(param, data.getParkAppSecret());
//
//                this.setCouponZk(param, headerAuth).subscribe();
//                return Mono.just(RestUtils.success());
//
////                return null;//!!!
//            } else if(PARK_TYPE_YB.equals(data.getParkType())) {
//                final int freeHours = data.getDuration() / 60;
//                ParkCouponYbVo req = new ParkCouponYbVo();
//                req.setParkCode(String.valueOf(data.getParkId()))
//                        .setCarNo(data.getCarNo())
//                        .setAppId("")
//                        .setIsAddUseFlage(1)
//                        .setFreehours(freeHours)
//                        .setFreeMoney(BigDecimal.ZERO)
//                        .setType(2)
//                        .setStoreName(data.getParkAppId());
//                String paramAuth = getHeaderAuthYb(req, data.getParkAppSecret());
//                this.setCouponYb(req, data.getParkAppId(), paramAuth).subscribe();
//                return Mono.just(RestUtils.success());
//            } else if(PARK_TYPE_CX.equals(data.getParkType())) {
//                ParkCouponCxVo req = new ParkCouponCxVo();
//                req.setParkingId(data.getParkAppId())
//                    .setAppKey(DigestUtils.md5DigestAsHex(data.getParkAppSecret().getBytes(StandardCharsets.UTF_8)).toUpperCase())
//                    .setFavourableDuration(data.getDuration())
//                    .setPlateNumber(data.getCarNo())
//                    .setTimestamp(System.currentTimeMillis());
//                String paramAuth = getHeaderAuthCx(req, data.getParkAppSecret());
//                req.setSign(paramAuth);
//                this.setCouponCx(req)
//                    .doOnSuccess(e -> {
//                        if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
//                            reactiveDataCoreFeignClient.setParkCouponDuration(orderStopRequestV2.getOrderNo(),
//                                parkCouponVoObjectResponse.getData().getDuration()).subscribe();
//
//                        } else {
//                            log.warn("第三方道闸返回状态异常: {}", e);
//                        }
//                    })
//                    .doOnError(e -> log.error(e.getMessage(), e))
//                    .subscribe();
//                return Mono.just(RestUtils.success());
//            }
//        }
//        return Mono.just(RestUtils.success());

    }

    @Deprecated
    private static String getHeaderAuthYb(ParkCouponYbVo param, String appSecret) {
        String jsonStr = JsonUtils.toJsonString(param);
        String encryptStr = jsonStr + appSecret;
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8));
        return s;
    }

    @Deprecated
    private static String getHeaderAuthZk(ParkCouponZkVo param, String appSecret) {
        String jsonStr = JsonUtils.toJsonString(param);
        String encryptStr = jsonStr + "&app_secret=" + appSecret;
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8));
        return s;
    }

    @Deprecated
    private static String getHeaderAuthCx(ParkCouponCxVo param, String appSecret) {
        String encryptStr = "favourableDuration=" + param.getFavourableDuration()
            + "&parkingId=" + param.getParkingId()
            + "&plateNumber=" + param.getPlateNumber()
            + "&timestamp=" + param.getTimestamp() + param.getAppKey();
        String s = DigestUtils.md5DigestAsHex(encryptStr.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        return s;
    }

    public boolean notifyDeviceStatus(BsReqMsg<BsDeviceInfo> reqMsg) {
        try {
            // 将信息存入数据库
            ParkDevicePo devicePo = this.infoMap2DevicePo(reqMsg.getData(), reqMsg.getParkId());

            boolean b = parkDeviceRwDs.upsetParkDevice(devicePo);
            if (!b) {
                log.error("更新设备信息失败(需人工干预): po = {}", JsonUtils.toJsonString(devicePo));
            }
            return true;
        } catch (Exception e) {
            log.error("推送设备状态处理异常(需要手动处理): info = {}; err = {}",
                    JsonUtils.toJsonString(reqMsg.getData()), e.getMessage(), e);
        }

        return false;
    }

    private ParkDevicePo infoMap2DevicePo(BsDeviceInfo bsDeviceInfo, Long parkId) {
        ParkDeviceIp deviceIp = new ParkDeviceIp();
        deviceIp.setAddress(bsDeviceInfo.getIp().getAddress())
                .setPort(bsDeviceInfo.getIp().getPort());

        return new ParkDevicePo()
                .setParkId(parkId)
                .setDeviceId(bsDeviceInfo.getDeviceId())
                .setIp(deviceIp)
                .setEventMsg(bsDeviceInfo.getErrmsg())
                .setEventTime(bsDeviceInfo.getTime())
                .setStatus(bsDeviceInfo.getStatus());
    }

    public Pair<Boolean, String> notifyChannelInfo(BsReqMsg<BsChannelInfo> reqMsg) {
        String err = "";
        try {
            // 将信息存入数据库
            ParkChannelPo channelPo = this.infoMap2ChannelPo(reqMsg.getData(), reqMsg.getParkId());

            // 1新增 2编辑 3删除
            boolean result = false;
            if (null != reqMsg.getOperateType()) {
                switch (reqMsg.getOperateType()) {
                    case 1:
                        boolean b = parkChannelRwDs.insertParkChannel(channelPo);
                        if (!b) {
                            log.error("新增道闸失败(需人工干预): po = {}", JsonUtils.toJsonString(channelPo));
                        }
                        result = true;
                        break;
                    case 3:
                        channelPo.setEnable(false);
                    case 2:
                        ParkChannelPo channel = parkChannelRoDs.findChannel(channelPo);
                        if(channel == null) {
                            log.info("道闸不存在，新增道闸");
                            boolean bl = parkChannelRwDs.insertParkChannel(channelPo);
                            if (!bl) {
                                log.error("新增道闸失败(需人工干预): po = {}", JsonUtils.toJsonString(channelPo));
                            }
                        } else {
                            b = parkChannelRwDs.updateParkChannel(channelPo);
                            if (!b) {
                                log.error("更新道闸失败(需人工干预): po = {}", JsonUtils.toJsonString(channelPo));
                            }
                        }
                        result = true;
                        break;
                    default:
                        err = "操作类型不识别" + reqMsg.getOperateType();
                }
            } else {
                err = "操作类型没有提供";
            }

            return Pair.of(result, err);
        } catch (Exception e) {
            log.error("推送道闸数据处理异常(需要手动处理): info = {}; err = {}",
                    JsonUtils.toJsonString(reqMsg.getData()), e.getMessage(), e);
        }

        return Pair.of(false, err);
    }

    private ParkChannelPo infoMap2ChannelPo(BsChannelInfo bsChannelInfo, Long parkId) {
        return new ParkChannelPo()
                .setChannelId(bsChannelInfo.getChannelId())
                .setChannelName(bsChannelInfo.getChannelName())
                .setDeviceId(bsChannelInfo.getDeviceId())
                .setThirdRecId(String.valueOf(bsChannelInfo.getRecordId()))
                .setPassType(ParkChannelType.valueOf(bsChannelInfo.getPassType()))
                .setParkId(parkId);
    }

    public Mono<List<ParkChannelVo>> getParkChannelBySiteId(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        return Mono.just(siteId)
                .map(parkChannelRoDs::getSiteChannelList);
    }

    public Mono<Boolean> upLiftRod(Long id) {
        if (null == id) {
            throw new DcArgumentException("道闸ID无效");
        }

        ParkChannelPo channelPo = parkChannelRoDs.getById(id);
        if (null == channelPo) {
            throw new DcArgumentException("该道闸不存在");
        }

        return Mono.just(true)
                .map(e -> {
                    String parkSignKey = this.getParkSignKey(channelPo.getParkId());
                    IotAssert.isNotBlank(parkSignKey, "车场签名获取失败");
                    return parkSignKey;
                })
                .flatMap(e -> bsService.upLiftRod(channelPo, e));
//        return bsService.upLiftRod(channelPo, parkSignKey);
    }
}
