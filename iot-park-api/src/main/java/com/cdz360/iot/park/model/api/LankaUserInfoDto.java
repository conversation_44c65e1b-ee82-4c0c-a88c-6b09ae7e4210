package com.cdz360.iot.park.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaUserInfoDto
 * @Description
 * @Date 2/2/2024 3:39 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaUserInfoDto {
    @Schema(description = "证件号码")
    private String idCard;

    @Schema(description = "车主姓名")
    private String userName;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "地址")
    private String address;
}