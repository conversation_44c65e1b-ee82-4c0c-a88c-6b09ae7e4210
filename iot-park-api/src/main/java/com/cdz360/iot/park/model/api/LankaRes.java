package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaRes
 * @Description
 * @Date 2/2/2024 5:02 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaRes {
    @Schema(description = "是否成功状态标志 success/fail")
    private String status;

    @Schema(description = "失败原因")
    @JsonInclude(Include.NON_NULL)
    private String errorCode;

    @Schema(description = "成功时返回")
    private LankaResDataDto datas;

    public static LankaRes getSuccess() {

        long currentTimeMillis = System.currentTimeMillis();

        LankaResDataDto dataDto = new LankaResDataDto();
        dataDto.setTimeStamp(String.valueOf(currentTimeMillis));

        LankaRes ret = new LankaRes();
        return ret.setStatus("success")
            .setDatas(dataDto);
    }
}