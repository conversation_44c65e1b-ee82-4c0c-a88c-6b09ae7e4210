package com.cdz360.iot.park.service.cs.common;

/**
 * @Classname ParkConstant
 * @Description
 * @Date 5/16/2024 2:41 PM
 * @Created by Rafael
 */
public class ParkConstants {
    public static final String PARK_TYPE_IST = "IST";// IST艾视特
    public static final String PARK_TYPE_ZK = "ZK";// ZK中控
    public static final String PARK_TYPE_YB = "YB";// YB宜泊
    public static final String PARK_TYPE_CX = "CX";// CX 深圳创享智能开发有限公司(扬中停车场)
    public static final String PARK_TYPE_NTC = "NTC";// NTC 宁停车(燕子矶停车场)
    public static final String PARK_TYPE_JS = "JS";// JS 捷顺https://jsopen.jslife.com.cn/cloudopen/index.html#/documentCenter?id=467
    public static final String PARK_TYPE_STC = "STC";// STC 苏停车(摩尔广场)
    public static final String PARK_TYPE_PP = "PP";// PP停车
    public static final String PARK_API_TYPE_ZK = "DINGCHONG";
    public static final int PARK_API_ZK_DURATION_RATE = 30;// 以30分钟为单位
}