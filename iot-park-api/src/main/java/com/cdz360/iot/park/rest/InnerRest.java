package com.cdz360.iot.park.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkChannelVo;
import com.cdz360.iot.park.service.ParkBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 停车场系统内部接口
 */
@Slf4j
@RestController
@RequestMapping(value = "/inner", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerRest {

    @Autowired
    private ParkBizService parkBizService;

    @GetMapping(value = {"/setCoupon"})
    public Mono<BaseResponse> setCoupon(@RequestParam(value = "siteId", required = false) String siteId,
                                        @RequestParam(value = "carNo") String carNo,
                                        @RequestParam(value = "parkId") Long parkId,
                                        @RequestParam(value = "parkSignKey", required = false) String parkSignKey,
                                        @RequestParam(value = "parkOrderNo") String parkOrderNo,
                                        @RequestParam(value = "duration") Integer duration) {
        log.info("设置停车减免券 siteId = {}, carNo = {}, parkId = {}, parkOrderNo = {}, duration = {}",
                siteId, carNo, parkId, parkOrderNo, duration);
        return parkBizService.setCoupon(siteId, carNo, parkId, parkSignKey, parkOrderNo, duration);
    }

    @PostMapping(value = "/checkCoupon")
    public Mono<BaseResponse> checkCoupon(@RequestBody OrderStopRequestV2 orderStopRequestV2) {
        log.info("检查停车减免并推送减免 orderStopRequestV2 = {}", orderStopRequestV2);
        return parkBizService.checkCoupon(orderStopRequestV2);
    }

    @Operation(summary = "获取场站道闸信息列表")
    @GetMapping(value = "/channel/getParkChannelBySiteId")
    public Mono<ListResponse<ParkChannelVo>> getParkChannelBySiteId(
            ServerHttpRequest request,
            @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("获取场站道闸信息列表: siteId = {}", siteId);
        return parkBizService.getParkChannelBySiteId(siteId)
                .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "开闸操作")
    @GetMapping(value = "/channel/upLiftRod")
    public Mono<ObjectResponse<Boolean>> upLiftRod(
            ServerHttpRequest request,
            @Parameter(name = "道闸ID(自己系统内ID值)", required = true) @RequestParam(value = "id") Long id) {
        log.info("开闸操作: id = {}", id);
        return parkBizService.upLiftRod(id)
                .map(RestUtils::buildObjectResponse);
    }
}
