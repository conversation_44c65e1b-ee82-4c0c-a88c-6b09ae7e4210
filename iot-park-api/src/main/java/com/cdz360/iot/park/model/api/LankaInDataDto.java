package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaInDataDto
 * @Description
 * @Date 1/26/2024 3:48 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaInDataDto {
    @Schema(description = "车牌")
    private String plate;

    @Schema(description = "无牌车票号")
    private String ticketCode;

    @Schema(description = "车牌颜色")
    private String plateColor;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "入场时间")
    private Date inTime;

    @Schema(description = "入场通道名称")
    private String inChannel;

    @Schema(description = "入场图片名")
    private String inImage;

    @Schema(description = "入场记录编号")
    private String orderId;

    @Schema(description = "访问事由")
    private String visitReason;

    @Schema(description = "放行类型")
    private String openGateMode;

    @Schema(description = "匹配模式")
    private String matchMode;

    @Schema(description = "证件号码")
    private String idCard;

    @Schema(description = "车牌识别可信度, 如100,无信息时填0")
    private Integer confidence;

    @Schema(description = "车类型, 临时车/固定车/预约车")
    private String carType;

    @Schema(description = "车主信息")
    private LankaUserInfoDto userInfo;

    @Schema(description = "车位信息")
    private LankaPlaceInfoDto placeInfo;

    @Schema(description = "是否开闸, 开闸/未开闸")
    private String barriorOpen;

    @Schema(description = "开闸耗时")
    private String costTime;

    @Schema(description = "空位数")
    private String spaceCount;

    @Schema(description = "其他联动设备图片信息")
    private List<String> imageList;

    @Schema(description = "图片名")
    private String imageName;

    @Schema(description = "修改车牌信息")
    private LankaModifyMemoDto modifyMemo;
}