package com.cdz360.iot.park.service.cs.common.encode;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * @Classname JieshunSigner
 * @Description
 * @Date 6/18/2024 10:50 AM
 * @Created by Rafael
 */
@Slf4j
public class JieshunSigner {
    public static String buildSignJson(JsonNode jsonObject, String secret) {
        String sourceStr = buildSourceJSonStr(jsonObject);
        log.info("js请求参数: {}", sourceStr);
        try {
            String sign = RSAUtils.sign(sourceStr.getBytes(), secret);
            return sign;
        } catch (Exception e) {
            log.info("加签失败：", e);
            return null;
        }
    }

    public static String buildSourceJSonStr(JsonNode param) {
        if (param == null) {
            return null;
        }
        if (!param.fieldNames().hasNext()) {
            return null;
        }
        Iterable<String> iterable = param::fieldNames;
        List<String> paramNames = StreamSupport.stream(iterable.spliterator(), false)
                .filter(name -> !"sign".equals(name)) // 剔除 sign 字段
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder paramNameValue = new StringBuilder();
        int i = 0;
        for (String paramName : paramNames) {
//            String val = SignConfig.wrapVal(param.get(paramName));
            JsonNode valueNode = param.get(paramName);
            String val;
            if (valueNode.isObject()) {
                // 如果是对象，使用 JsonUtils 将其转换为字符串
                val = JsonUtils.toJsonString(valueNode);
            } else {
                // 否则，直接获取文本值
                val = valueNode.asText();
            }
            if (i == 0) {
                paramNameValue.append(paramName).append("=").append(val);
            } else {
                paramNameValue.append("&").append(paramName).append("=").append(val);
            }
            i++;
        }
        return paramNameValue.toString();
    }
}
