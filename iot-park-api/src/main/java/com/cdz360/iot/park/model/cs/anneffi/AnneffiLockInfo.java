package com.cdz360.iot.park.model.cs.anneffi;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "安效停地锁信息")
@Data
@Accessors(chain = true)
public class AnneffiLockInfo {

    private String id;

    private Boolean deleted;

    private Date createTime;

    private Date updateTime;

    private String code;

    private Long customerCode;

    private String serialNumber;

    private String devId;

    private Integer status;

    private String type;

    private String address;

    private String parkingSpaceCode;

    private String parkingLotId;

    private String parkingLotName;

    private String operatorId;

    private String operatorName;

    private String phone;

    private BigDecimal electricQuantity;

    private Integer microwaveDistance;

    private Integer parkWaitTime;

    private Integer parkCheckTime;

    private Integer pickCheckTime;

    private Integer pickWaitTime;

    private Integer alarmDuration;

}
