package com.cdz360.iot.park.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = ReactiveDataCoreHystrixFeignClientFactory.class)
public interface ReactiveDataCoreFeignClient {

    @PostMapping("/dataCore/parkOrder/setParkCouponDuration")
    Mono<ObjectResponse<Integer>> setParkCouponDuration(@RequestParam("orderNo") String orderNo,
        @RequestParam("duration") Integer duration);
}
