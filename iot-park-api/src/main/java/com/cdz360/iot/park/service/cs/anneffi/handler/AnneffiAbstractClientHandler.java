package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.service.cs.anneffi.commander.AnneffiClientCommanderFactory;
import com.cdz360.iot.park.service.cs.common.CsClientTransHandler;
import com.cdz360.iot.park.service.cs.comsumer.ParkingLockObserver;
import io.netty.buffer.ByteBuf;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;

@Component
public abstract class AnneffiAbstractClientHandler
        implements CsClientTransHandler {

    @Autowired
    protected AnneffiClientCommanderFactory commanderFactory;

    @Autowired
    protected AnneffiClientHandlerFactory handlerFactory;

    @Autowired
    protected ParkingLockObserver parkingLockObserver;

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        return Mono.empty();
    }
}
