package com.cdz360.iot.park.service.cs.anneffi.commander;

import com.cdz360.iot.common.utils.ByteArrayUtil;
import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiLockOpEncode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class AnneffiRebootCommander extends AnneffiAbstractClientCommander {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.REBOOT;

    @Autowired
    private AnneffiClientCommanderFactory commanderFactory;

    @PostConstruct
    public void init() {
        commanderFactory.addCommander(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> build(CsClientEncodeBase base) {
        AnneffiLockOpEncode data = (AnneffiLockOpEncode) base;
        String lockNo = data.getSerialNumber();
        base.getBase().setLen(12 + 4).setTaskCode(TASK_CODE);
        ByteArrayOutputStream os = super.preCommon(base.getBase());
        ByteArrayUtil.writeInt(os, Integer.parseInt(lockNo));
        super.sign(os);
        return Mono.just(os);
    }
}
