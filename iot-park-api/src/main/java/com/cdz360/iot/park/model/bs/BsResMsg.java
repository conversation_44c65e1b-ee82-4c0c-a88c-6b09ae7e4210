package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BsResMsg {

    @JsonProperty(value = "service_name")
    private String serviceName;



    @JsonProperty(value = "park_id")
    private Long parkId;

    private Integer state;

    private String errmsg;

    public BsResMsg() {
        this.state = 1;
        this.errmsg = "success";
    }


}
