package com.cdz360.iot.park.model.bs;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "远程控制开关闸")
public class BsLiftRod {
    @JsonProperty(value = "device_id")
    @Schema(description = "设备号")
    private String deviceId;

    @JsonProperty(value = "channel_id")
    @Schema(description = "通道编号")
    private Long channelId;

    @JsonProperty(value = "channel_name")
    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "道闸命令: 0-抬杆 1-落杆")
    @JsonProperty(value = "operate")
    private Integer operate;

    @Schema(description = "此次抬杆编号自定义一个uuid")
    @JsonProperty(value = "liftrod_id")
    private String liftRodId;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BsResMsg {

        @JsonProperty(value = "device_id")
        @Schema(description = "设备号")
        private String deviceId;

        @JsonProperty(value = "channel_id")
        @Schema(description = "通道编号")
        private Long channelId;

        @JsonProperty(value = "channel_name")
        @Schema(description = "通道名称")
        private String channelName;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }
}
