package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname ParkOrderCouponRes
 * @Description
 * @Date 8/15/2022 5:15 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class ParkOrderCouponRes {
    private String chargeOrderNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chargeStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chargeStopTime;
    private Integer freeMinutes;
}