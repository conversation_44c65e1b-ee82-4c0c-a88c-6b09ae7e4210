package com.cdz360.iot.park.service.cs;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CsClientBizFactory implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    private final Map<ParkingLockPartner, CsClientHandler> handlers = new HashMap<>();
    private final Map<ParkingLockPartner, CsClientCommander> commanders = new HashMap<>();

    public Mono<CsClientHandler> getHandler(ParkingLockPartner partner) {
        return Mono.justOrEmpty(this.handlers.get(partner))
                .switchIfEmpty(Mono.just(partner.getHandlerBeanName())
                        .doOnNext(name -> handlers.put(partner, (CsClientHandler) applicationContext.getBean(name)))
                        .mapNotNull(x -> this.handlers.get(partner)))
                .doOnError(err -> {
                    log.error("获取处理器异常: err = {}", err.getMessage(), err);
                    throw new DcArgumentException("指定处理器不存在[" + partner + "]");
                })
                .switchIfEmpty(Mono.error(new DcArgumentException("指定处理器不存在[" + partner + "]")));
    }

    public Mono<CsClientCommander> getCommander(ParkingLockPartner partner) {
        return Mono.justOrEmpty(this.commanders.get(partner))
                .switchIfEmpty(Mono.just(partner.getCommanderBeanName())
                        .doOnNext(name -> commanders.put(partner, (CsClientCommander) applicationContext.getBean(name)))
                        .mapNotNull(x -> this.commanders.get(partner)))
                .doOnError(err -> {
                    log.error("获取处理器异常: err = {}", err.getMessage(), err);
                    throw new DcArgumentException("指定处理器不存在[" + partner + "]");
                })
                .switchIfEmpty(Mono.error(new DcArgumentException("指定处理器不存在[" + partner + "]")));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
