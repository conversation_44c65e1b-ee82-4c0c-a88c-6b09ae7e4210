package com.cdz360.iot.park.model.cs;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "地锁微信模板消息发送")
public class ParkingLockErrorParam {

    @Schema(description = "供应商地锁ID(交互使用字段)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serialNumber;

    @Schema(description = "桩编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "充电枪ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugId;

    @Schema(description = "异常信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String errorMsg;

}
