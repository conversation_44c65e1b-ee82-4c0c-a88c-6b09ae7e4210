package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.park.feign.DataCoreFeignClient;
import com.cdz360.iot.park.model.api.ParkBaseReq;
import com.cdz360.iot.park.model.api.ParkBaseRes;
import com.cdz360.iot.park.model.api.ParkFeeReq;
import com.cdz360.iot.park.model.api.ParkFeeRes;
import com.cdz360.iot.park.model.api.ParkOrderCouponReq;
import com.cdz360.iot.park.model.api.ParkOrderCouponRes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @Classname ApiService
 * @Description
 * @Date 8/16/2022 3:30 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class ApiService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public Mono<ParkBaseRes<ParkOrderCouponRes>> getDiscount(ParkOrderCouponReq param) {
        return Mono.just(param)
            .map(e -> {
                IotAssert.isNotNull(param, "请传入参数");
                IotAssert.isNotBlank(param.getSiteId(), "请传入场站唯一编号");
                IotAssert.isNotBlank(param.getCarNo(), "请传入车牌号");
                IotAssert.isNotBlank(param.getSeqNo(), "请传入序列号");
                IotAssert.isNotBlank(param.getSign(), "请传入消息签名");
                    ObjectResponse<String> parkSignKeyRes = dataCoreFeignClient.getParkSignKeyBySiteId(
                        param.getSiteId());
                    FeignResponseValidate.check(parkSignKeyRes);
                final String signKey = parkSignKeyRes.getData();
                IotAssert.isNotBlank(signKey, "场站当前未配置");
                IotAssert.isTrue(checkSign(signKey, param), "签名不正确");

                final ObjectResponse<ParkOrderCouponRes> discountRes = dataCoreFeignClient.getDiscount(
                    param);
                IotAssert.isTrue(discountRes.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS,
                    discountRes.getError());

                final ParkBaseRes<ParkOrderCouponRes> ret = new ParkBaseRes();
                if(discountRes.getData() != null) {
                    ret.setData(discountRes.getData());
                }
                ret.setSeqNo(param.getSeqNo());
                return ret;
            })
            .onErrorResume(e -> {
                ParkBaseRes<ParkOrderCouponRes> parkBaseRes = new ParkBaseRes();
                parkBaseRes.setError(e.getMessage())
                    .setStatus(4000);
                return Mono.just(parkBaseRes);
//                return Mono.just(parkBaseRes);
            })
            .map(e -> {
                log.info("<<");
                return e;
            });
    }

    public Mono<ParkFeeRes> parkFee(ParkFeeReq param) {
        return Mono.just(param)
            .map(e -> {
                IotAssert.isNotNull(param, "请传入参数");
                IotAssert.isNotBlank(param.getSiteId(), "请传入场站唯一编号");
                IotAssert.isNotBlank(param.getCarNo(), "请传入车牌号");
                IotAssert.isNotBlank(param.getSeqNo(), "请传入序列号");
//                IotAssert.isNotBlank(param.getChargeOrderNo(), "请传入充电订单号");
                IotAssert.isNotBlank(param.getParkOrderNo(), "请传入停车订单号");
                IotAssert.isTrue(param.getParkOrderNo().length() <= 16, "停车订单号最长不超过16个字符");
                IotAssert.isNotNull(param.getInTime(), "请传入停车入场时间");
                IotAssert.isNotNull(param.getOutTime(), "请传入停车出场时间");
                IotAssert.isNotNull(param.getFreeMinutes(), "请传入实际减免的免费停车时长");
                IotAssert.isNotNull(param.getParkFee(), "请传入停车费,单位元,保留2位小数");
                IotAssert.isNotBlank(param.getSign(), "请传入消息签名");

                ObjectResponse<String> parkSignKeyRes = dataCoreFeignClient.getParkSignKeyBySiteId(
                    param.getSiteId());
                FeignResponseValidate.check(parkSignKeyRes);
                final String signKey = parkSignKeyRes.getData();
                IotAssert.isNotBlank(signKey, "场站当前未配置");
                IotAssert.isTrue(checkSign(signKey, param), "签名不正确");

                final BaseResponse baseResponse = dataCoreFeignClient.uploadOrderParkFee(param);
                FeignResponseValidate.check(baseResponse);

                return new ParkFeeRes();
            })
            .onErrorResume(e -> {
                ParkFeeRes parkBaseRes = new ParkFeeRes();
                parkBaseRes.setError(e.getMessage())
                    .setStatus(4000);
                return Mono.just(parkBaseRes);
//                return Mono.just(parkBaseRes);
            })
            .map(e -> {
                log.info("<<");
                return e;
            });

    }

    private static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException ex) {
            throw new RuntimeException(ex.getCause());
        }
    }

    public static boolean checkSign(String password, ParkBaseReq req) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
        Map<String, String> map = objectMapper.convertValue(req, new TypeReference<>() {});
        log.info("{}", map);

        if (Objects.isNull(req.getInTime())) {
            map.remove("inTime");
        }

        final String sign = req.getSign();
        IotAssert.isNotBlank(sign, "请传入签名");

        final String queryStr = map.keySet()
            .stream()
            .sorted()
            .filter(e -> !e.equalsIgnoreCase("sign"))
            .map(e -> encodeValue(e) + "=" + (map.get(e) == null ? "" : encodeValue(map.get(e))))
            .collect(Collectors.joining("&"));
        log.info("签名前: {}", queryStr + password);
        String sha1 = SHA1(queryStr + password);
        log.info("期望的签名: {}", sha1);
//        try {
//            log.info("2: {}", sha1(queryStr + password));
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }

        return sign.equalsIgnoreCase(sha1);
    }

    private static String SHA1(String decript) {
        try {
            MessageDigest digest = java.security.MessageDigest.getInstance("SHA-1");
            digest.update(decript.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }
//    static String sha1(String input) throws NoSuchAlgorithmException {
//        MessageDigest mDigest = MessageDigest.getInstance("SHA1");
//        byte[] result = mDigest.digest(input.getBytes());
//        StringBuffer sb = new StringBuffer();
//        for (int i = 0; i < result.length; i++) {
//            sb.append(Integer.toString((result[i] & 0xff) + 0x100, 16).substring(1));
//        }
//
//        return sb.toString();
//    }
}