package com.cdz360.iot.park.client.cs;


import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.park.service.cs.CsClientBizFacade;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ChannelHandler.Sharable
public class CsClientHandler extends ChannelInboundHandlerAdapter {

    @Autowired
    private CsClientBizFacade csClientBizFacade;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        this.csClientBizFacade.csClientActive(this.channelKey(ctx));
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        ByteBuf in = Unpooled.wrappedBuffer((byte[]) msg);
        try {
            this.csClientBizFacade.csClientRead(this.channelKey(ctx), in);
        } catch (Exception e) {
            log.error("处理读取的数据异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        // Close the connection when an exception is raised.
        cause.printStackTrace();
        this.csClientBizFacade.csClientError(this.channelKey(ctx), "EXCEPTION");
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        this.csClientBizFacade.csClientError(this.channelKey(ctx), "INACTIVE");
        ctx.close();
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            // 当客户端开始发送心跳检测时。
            // 说明没有业务请求过来，释放通道数为设定的 CORE_CONNECTIONS
            log.debug("[客户端心跳监测发送] 通道编号：{}", ctx.channel().id());
            Channel channel = ctx.channel();
            if (channel.isActive()) {
                this.csClientBizFacade.csClientHb(this.channelKey(ctx));
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    private String channelKey(ChannelHandlerContext ctx) {
        String key = ctx.channel().remoteAddress().toString();
        String[] split = key.split("/");
        if (StringUtils.isNotBlank(split[0])) {
            return split[0] + ":" + split[1].split(":")[1];
        } else {
            return key.replace("/", "");
        }
    }
}
