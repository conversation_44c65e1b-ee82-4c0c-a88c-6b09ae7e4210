package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BsReqMsg<T> {

    @JsonProperty(value = "operate_type")
    private Integer operateType;

    private T data;


    @JsonProperty(value = "service_name")
    private String serviceName;


    @JsonProperty(value = "park_id")
    private Long parkId;


    private String sign;
}
