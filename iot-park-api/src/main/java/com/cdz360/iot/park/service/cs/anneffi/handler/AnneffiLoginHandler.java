package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class Anneffi<PERSON>oginHand<PERSON> extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.LOGIN;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        if (base.getParam1() == 1) {
            return commanderFactory.getCommander(ClientTaskCode.HB)
                    .flatMap(commander -> commander.build(new CsClientEncodeBase().setBase(base)));
        } else { // 登录失败
            return Mono.error(new DcServerException("登录失败: " + base.getCustomerId()));
        }
    }
}
