package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Classname ParkCouponLKVo
 * @Description
 * @Date 1/11/2024 3:02 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@Schema(description = "停车系统优惠券-蓝卡")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ParkCouponLKVo extends ParkBaseReq {
    private String chargeOrderNo;
    private Integer freeMinutes;

//    public static void main(String[] args) {
//
//        ParkCouponLKVo req = new ParkCouponLKVo();
//        req.setChargeOrderNo("722410480946")
//            .setFreeMinutes(30)
//            .setCarNo("沪AD12345")
//            .setSeqNo("N00011122201")
//            .setSiteId("ABCD1234");
//
//        String password = "QQQWWWEEE";
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        objectMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
//        Map<String, String> map = objectMapper.convertValue(req, new TypeReference<>() {});
//        final String queryStr = map.keySet()
//            .stream()
//            .sorted()
//            .filter(e -> !e.equalsIgnoreCase("sign"))
//            .map(e -> encodeValue(e) + "=" + (map.get(e) == null ? "" : encodeValue(map.get(e))))
//            .collect(Collectors.joining("&"));
//
//        System.out.println(queryStr);
//
//        String sha1 = SHA1(queryStr + password);
//
//        System.out.println(sha1);
//    }

//    private static String encodeValue(String value) {
//        try {
//            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
//        } catch (UnsupportedEncodingException ex) {
//            throw new RuntimeException(ex.getCause());
//        }
//    }
//
//    private static String SHA1(String decript) {
//        try {
//            MessageDigest digest = java.security.MessageDigest.getInstance("SHA-1");
//            digest.update(decript.getBytes());
//            byte messageDigest[] = digest.digest();
//            // Create Hex String
//            StringBuffer hexString = new StringBuffer();
//            // 字节数组转换为 十六进制 数
//            for (int i = 0; i < messageDigest.length; i++) {
//                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
//                if (shaHex.length() < 2) {
//                    hexString.append(0);
//                }
//                hexString.append(shaHex);
//            }
//            return hexString.toString();
//
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        return "";
//    }
}