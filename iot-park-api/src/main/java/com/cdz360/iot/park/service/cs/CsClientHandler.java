package com.cdz360.iot.park.service.cs;

import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import io.netty.buffer.ByteBuf;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;

public interface CsClientHandler {
    default Mono<ByteArrayOutputStream> active(CsClientChannel channel) {
        return Mono.empty();
    }

    Mono<ByteArrayOutputStream> read(CsClientChannel channel, ByteBuf msg);

    void error(CsClientChannel channel, String flag);

    Mono<ByteArrayOutputStream> hb(CsClientChannel channel);

    default CsClientTransBase preCommon(ByteBuf msg) {
        return new CsClientTransBase();
    }

    default boolean checkSign(ByteBuf msg) {
        return true;
    }
}
