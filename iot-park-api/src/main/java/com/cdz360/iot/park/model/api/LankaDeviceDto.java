package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaDeviceDto
 * @Description
 * @Date 1/26/2024 3:36 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaDeviceDto {
    @Schema(description = "通道名称")
    private String name;

    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "ip")
    private String ip;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "上次在线时间")
    private Date lastTime;

    /**
     * 1在线2离线
     */
    @Schema(description = "是否在线")
    private String state;

    /**
     * 1 主机
     * 2 从机
     * 4 补拍
     * 5 外显屏
     * 6 打票机
     * 7 扫描机
     * 10 查询机/显示屏
     * 12 视频对讲机
     * 13 ETC
     * 21 监控设备
     * 23 其他摄像机
     */
    @Schema(description = "设备类型id")
    private Integer deviceTypeId;
}