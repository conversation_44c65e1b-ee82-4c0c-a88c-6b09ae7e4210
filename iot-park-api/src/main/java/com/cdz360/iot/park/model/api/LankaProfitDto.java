package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaProfitDto
 * @Description
 * @Date 2/2/2024 4:40 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaProfitDto {
    @Schema(description = "优惠码")
    private String profitCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "优惠下发时间")
    private Date getTime;

    @Schema(description = "优惠时间 单位：分钟")
    private String profitTime;

    @Schema(description = "优惠金额面值 单位:元")
    private String profitCharge;

    @Schema(description = "生效金额 单位:元")
    private String profitChargeValue;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "商户名称")
    private String shopName;
}