package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import reactor.core.publisher.Mono;

/**
 * @Classname IParkCouponService
 * @Description
 * @Date 5/16/2024 2:31 PM
 * @Created by Rafael
 */
public interface IParkCouponService {
    Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data);
}