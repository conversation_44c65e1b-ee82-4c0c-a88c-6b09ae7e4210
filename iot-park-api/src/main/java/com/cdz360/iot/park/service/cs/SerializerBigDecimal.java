package com.cdz360.iot.park.service.cs;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * @Classname BigDecimalFormatSerializer
 * @Description
 * @Date 8/29/2022 2:31 PM
 * @Created by Rafael
 */

public class SerializerBigDecimal extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers)
        throws IOException {
        if (Objects.isNull(value)) {
            gen.writeNull();
        } else {
            // 这里取floor
            gen.writeNumber(value.setScale(2, RoundingMode.FLOOR));
        }
    }
}
