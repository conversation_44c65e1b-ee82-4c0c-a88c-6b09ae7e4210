package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.EventDataBase;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class AnneffiNotifyCarInHandler extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.NOTIFY_CAR_ARRIVE;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    /**
     * 截取车牌号有效位
     * @param msg 数据源
     * @return 有效长度
     */
    private static int trimZero(ByteBuf msg) {
        int end = msg.readerIndex();
        while (msg.getByte(end++) != 0x00) {
            if (end - msg.readerIndex() > 16) break;
        }
        return end - msg.readerIndex() - 1;
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        long lockId = msg.readUnsignedIntLE();
        byte[] cntBytes = ByteBufUtil.getBytes(msg, msg.readerIndex(), trimZero(msg));
        String carNo = new String(cntBytes, StandardCharsets.UTF_8);
        log.info("通知车牌[{}]: {}", lockId, carNo);
        EventDataBase dataBase = EventDataBase.success(carNo)
                .setTaskCode(TASK_CODE)
                .setPartner(base.getPartner())
                .setDeviceNo("" + lockId);
        parkingLockObserver.addEventMsg(dataBase);
        parkingLockObserver.notifyEvent();
        return commanderFactory.getCommander(TASK_CODE)
                .flatMap(commander -> commander.build(new CsClientEncodeBase().setBase(base)));
    }
}
