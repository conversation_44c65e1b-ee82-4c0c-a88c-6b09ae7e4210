package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.EventDataBase;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class AnneffiNotifyLockExceptionHandler extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.NOTIFY_LOCK_ERR;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        long lockId = msg.readUnsignedIntLE();
        log.info("通知地锁异常[{}]: result = {}", lockId, base.getParam1());
        // 0=该锁无名异常；1=该锁故障；2=该锁超时掉线，3=该锁开闭锁异常；4=该锁电池电量不足
        EventDataBase dataBase = EventDataBase.success(base.getParam1() + "")
                .setTaskCode(TASK_CODE)
                .setPartner(base.getPartner())
                .setDeviceNo("" + lockId);
        parkingLockObserver.addEventMsg(dataBase);
        parkingLockObserver.notifyEvent();
        return commanderFactory.getCommander(TASK_CODE)
                .flatMap(commander -> commander.build(new CsClientEncodeBase().setBase(base)));
    }
}
