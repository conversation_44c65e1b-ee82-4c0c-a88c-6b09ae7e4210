package com.cdz360.iot.park.model.api;

import com.cdz360.iot.park.service.cs.SerializerBigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname ParkFeeReq
 * @Description
 * @Date 8/16/2022 1:28 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ParkFeeReq extends ParkBaseReq {
    // @Schema(title = "充电云返回的充电订单号")
    private String chargeOrderNo;
    // @Schema(title = "停车系统中唯一的停车订单号,最长不超过16个字母")
    private String parkOrderNo;
    // @Schema(title = "停车入场时间,GMT+8时区,格式为 YYYY-MM-DD hh:mm:ss, 如 2022-10-25 20:45:52")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inTime;
    // @Schema(title = "停车出场时间,GMT+8时区,格式为 YYYY-MM-DD hh:mm:ss, 如 2022-10-25 20:45:52")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outTime;
    // @Schema(title = "实际减免的免费停车时长")
    private Integer freeMinutes;
    // @Schema(title = "停车费,单位元,保留2位小数. 用于做对账")
    @JsonSerialize(using = SerializerBigDecimal.class)
    private BigDecimal parkFee;
}