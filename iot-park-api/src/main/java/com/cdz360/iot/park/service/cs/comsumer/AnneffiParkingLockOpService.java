package com.cdz360.iot.park.service.cs.comsumer;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CommanderDatabase;
import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiCommanderData;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiLockOpEncode;
import com.cdz360.iot.park.service.cs.CsClientBizFactory;
import com.cdz360.iot.park.service.cs.job.JobTask;
import com.cdz360.iot.park.service.cs.job.ParkingLockJob;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 向服务端获取地锁信息
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class AnneffiParkingLockOpService extends AbstractParkingLockSubObserver {

    @Autowired
    private CsClientBizFactory clientBizFactory;

    @Autowired
    private ParkingLockObserverService meterObserverProxy;

    @PostConstruct
    public void init() {
        this.meterObserverProxy.addSubObserver(ParkingLockPartner.ANNEFFI, this);
    }

    public <T> Mono<T> process(CsClientChannel channel, CommanderDatabase data) {
        final AnneffiLockOpEncode encodeBase = new AnneffiLockOpEncode(
            channel.getAppId(), data.getDeviceNo());
        encodeBase.getBase().setTaskCode(data.getTaskCode());
        if (ClientTaskCode.SWITCH_LOCK.equals(data.getTaskCode())) {
            encodeBase.setOpen(((AnneffiCommanderData) data).isOpen());
        }

        ParkingLockJob job = new ParkingLockJob(this.clientBizFactory, channel, data.getDeviceNo());
        super.addJob(channel.getPartner(), data.getDeviceNo(), job);
        return Mono.create(sink -> {
            job.setMonoSink(sink);
            job.process(new JobTask(job, encodeBase));
        });
    }
}
