package com.cdz360.iot.park.rest;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.park.model.bs.*;
import com.cdz360.iot.park.service.ParkBizService;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 百胜对接接口
 *
 * http://park-api.sciseetech.com/istparking/1742273
 */
@Slf4j
@RestController
@RequestMapping(value = "/bs", produces = MediaType.APPLICATION_JSON_VALUE)
public class BsRest {

    @Value("${iot.park.bsSignKey:}")
    @Deprecated private String signKey;

    @Autowired
    private ParkBizService parkBizService;

    @PostMapping(value = {"/reigister", "/register"})
    public BsResMsg register(@RequestBody(required = false) String param) {
        log.info("停车云注册车场上传 param = {}", param);
        BsReqMsg<BsParkInfo> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });

        return BsRestUtils.success(req);
    }

    @PostMapping(value = "/parkIn")
    public BsParkIn.BsParkInResponse parkIn(@RequestBody(required = false) String param) {
        log.info("车辆入场数据推送 param = {}", param);
        BsReqMsg<BsParkIn> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });

        this.checkSign(req.getParkId(), param);

        req.getData().setParkId(req.getParkId());

        parkBizService.parkIn(req.getData());
        return BsRestUtils.successParkIn(req);
    }

    @PostMapping(value = "/parkOut")
    public BsParkOut.BsParkOutResponse parkOut(@RequestBody(required = false) String param) {
        log.info("车辆出场数据推送 param = {}", param);
        BsReqMsg<BsParkOut> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });

        this.checkSign(req.getParkId(), param);

        req.getData().setParkId(req.getParkId());

        parkBizService.parkOut(req.getData());
        return BsRestUtils.successParkOut(req);
    }

    @PostMapping(value = "/deviceStatus")
    public BsResMsg notifyDeviceStatus(@RequestBody(required = false) String param) {
        log.info("上传设备在线状态 param = {}", param);
        BsReqMsg<BsDeviceInfo> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });

        return Mono.just(req)
                .doOnNext(r -> this.checkSign(req.getParkId(), param))
                .map(parkBizService::notifyDeviceStatus)
                .map(result -> BsRestUtils.success(req))
                .block(Duration.ofSeconds(30L));
    }

    @PostMapping(value = "/channelStatus")
    public BsResMsg notifyChannelStatus(@RequestBody(required = false) String param) {
        log.info("上传通道信息 param = {}", param);
        BsReqMsg<BsChannelInfo> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });

        return Mono.just(req)
                .doOnNext(r -> this.checkSign(req.getParkId(), param))
                .map(parkBizService::notifyChannelInfo)
                .map(result -> result.getFirst() ?
                        BsRestUtils.success(req) :
                        BsRestUtils.failed(req).setErrmsg(result.getSecond()))
                .block(Duration.ofSeconds(30L));
    }

    @PostMapping(value = "/card")
    public BsResMsg addCard(@RequestBody(required = false) String param) {
        log.info("停车云的月卡上传对接平台 param = {}", param);
        BsReqMsg<BsMonthCard> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });
        return BsRestUtils.success(req);
    }


    @PostMapping(value = "/cardSchema")
    public BsResMsg addCardSchema(@RequestBody(required = false) String param) {
        log.info("上传通道信息 param = {}", param);
        BsReqMsg<BsMonthCardSchema> req = JsonUtils.fromJson(param, new TypeReference<>() {
        });
        return BsRestUtils.success(req);
    }

    private void checkSign(Long parkId, String param) {
        String parkSignKey = parkBizService.getParkSignKey(parkId);
        if(StringUtils.isBlank(parkSignKey)) {
            log.warn("找不到对应signkey,使用默认配置key: {}", signKey);
            parkSignKey = signKey;
        }
        IotAssert.isTrue(BsRestUtils.checkSign(param, parkSignKey), "验签失败");
    }
}
