package com.cdz360.iot.park.service.cs.anneffi.commander;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiCsClientEncode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class AnneffiLoginCommander extends AnneffiAbstractClientCommander {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.LOGIN;

    @Autowired
    private AnneffiClientCommanderFactory commanderFactory;

    @PostConstruct
    public void init() {
        commanderFactory.addCommander(TASK_CODE, this);
    }

    @Override
    public <E extends CsClientEncodeBase> Mono<ByteArrayOutputStream> build(E base) {
        AnneffiCsClientEncode data = (AnneffiCsClientEncode) base;
        byte[] passw = data.getAppKey().getBytes(StandardCharsets.UTF_8);

        base.getBase().setLen(12 + passw.length).setTaskCode(TASK_CODE);
        ByteArrayOutputStream os = super.preCommon(base.getBase());
        os.writeBytes(passw);
        super.sign(os);
        return Mono.justOrEmpty(os);
    }
}
