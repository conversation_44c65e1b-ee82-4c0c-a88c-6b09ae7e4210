package com.cdz360.iot.park.model.bs;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 车辆出场数据推送
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "车辆出场数据推送")
public class BsParkOut extends BsParkOrder {


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    // 为后端提供的字段
    private Long parkId;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    public static class BsParkOutResponse extends BsResMsg {

        public BsParkOutResponse() {
            super();
        }

        @JsonProperty("order_id")
        private String parkOrderId;
        @JsonProperty("car_number")
        private String carNo;

    }
}
