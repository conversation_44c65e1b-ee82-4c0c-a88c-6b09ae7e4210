package com.cdz360.iot.park.model.cs.anneffi;

import lombok.Getter;

// 0=该锁无名异常；1=该锁故障；2=该锁超时掉线，3=该锁开闭锁异常；4=该锁电池电量不足
@Getter
public enum AnneffiLockErrorCode {
    ERROR_NO_CODE(0, "无名异常"),
    ERROR(1, "无名异常"),
    ERROR_TIMEOUT_OFFLINE(2, "超时掉线"),
    ERROR_SWITCH(3, "开闭锁异常"),
    ERROR_LOW_KWH(4, "电池电量不足"),
    ;

    private final int code;
    private final String msg;

    AnneffiLockErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static AnneffiLockErrorCode valueOf(int codeIn) {
        for (AnneffiLockErrorCode code : values()) {
            if (code.code == codeIn) {
                return code;
            }
        }
        return AnneffiLockErrorCode.ERROR_NO_CODE;
    }
}
