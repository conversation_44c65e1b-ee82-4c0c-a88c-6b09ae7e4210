package com.cdz360.iot.park.model.api;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname ParkBaseRes
 * @Description
 * @Date 8/16/2022 10:59 AM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ParkBaseRes<T> extends ObjectResponse<T> {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String seqNo;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T data;
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private T data;
}