package com.cdz360.iot.park.service.cs.anneffi.commander;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class AnneffiLockExceptionCommander extends AnneffiAbstractClientCommander {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.NOTIFY_LOCK_ERR;

    @Autowired
    private AnneffiClientCommanderFactory commanderFactory;

    @PostConstruct
    public void init() {
        commanderFactory.addCommander(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> build(CsClientEncodeBase base) {
        base.getBase().setTaskCode(TASK_CODE);
        return super.build(base);
    }
}
