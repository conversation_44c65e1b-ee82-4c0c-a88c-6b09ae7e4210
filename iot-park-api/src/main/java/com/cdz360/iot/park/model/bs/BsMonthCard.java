package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "停车云的月卡上传对接平台")
public class BsMonthCard {

    @JsonProperty(value = "card_id")
    @Schema(description = "月卡会员唯一编号")
    private String cardId;


    @JsonProperty(value = "create_time")
    @Schema(description = "新建时间(unix时间戳格式)")
    private Long createTime;


    @JsonProperty(value = "update_time")
    @Schema(description = "修改时间(unix时间戳格式)")
    private Long updateTime;



    @JsonProperty(value = "begin_time")
    @Schema(description = "开始时间(unix时间戳格式)")
    private Long beginTime;


    @JsonProperty(value = "end_time")
    @Schema(description = "结束时间(unix时间戳格式)")
    private Long endTime;


    @Schema(description = "月卡套餐编号")
    private String pid;


    @JsonProperty(value = "p_lot")
    @Schema(description = "车位")
    private String pLot;

    @Schema(description = "车主姓名")
    private String name;


    @JsonProperty(value = "car_number")
    @Schema(description = "车牌号")
    private String carNumber;


    @Schema(description = "实收金额")
    private String price;


    @JsonProperty(value = "amount_receivable")
    @Schema(description = "应收金额")
    private String amountReceivable;


    @Schema(description = "电话")
    private String tel;


    @Schema(description = "地址")
    private String address;


    @Schema(description = "备注")
    private String remark;


    @JsonProperty(value = "limit_day_type")
    @Schema(description = "单双日限行. 0 不限制，1限制")
    private Integer limitDayType;


    @JsonProperty(value = "car_type")
    @Schema(description = "车辆类型", example = "月租车")
    private String carType;

}
