package com.cdz360.iot.park.rest;

import com.cdz360.iot.park.model.bs.BsParkIn;
import com.cdz360.iot.park.model.bs.BsParkOut;
import com.cdz360.iot.park.model.bs.BsReqMsg;
import com.cdz360.iot.park.model.bs.BsResMsg;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.DigestUtils;

import java.nio.charset.Charset;

public class BsRestUtils {

    public static BsResMsg success(BsReqMsg<?> bsReq) {
        var msg = new BsResMsg();
        msg.setServiceName(bsReq.getServiceName())
                .setParkId(bsReq.getParkId());
        return msg;
    }

    public static BsResMsg failed(BsReqMsg<?> bsReq) {
        var msg = new BsResMsg();
        msg.setState(0)
                .setServiceName(bsReq.getServiceName())
                .setParkId(bsReq.getParkId());
        return msg;
    }

    public static BsResMsg failed() {
        var msg = new BsResMsg();
        msg.setState(0).setErrmsg("");
        return msg;
    }

    public static BsParkIn.BsParkInResponse successParkIn(BsReqMsg<? extends BsParkIn> bsReq) {
        var msg = new BsParkIn.BsParkInResponse();
        msg//.setCarNo(bsReq.getData().getCarNumber())
                .setParkOrderId(bsReq.getData().getOrderId())
                .setServiceName(bsReq.getServiceName())
                .setParkId(bsReq.getParkId());
        return msg;
    }

    public static BsParkOut.BsParkOutResponse successParkOut(BsReqMsg<? extends BsParkOut> bsReq) {
        var msg = new BsParkOut.BsParkOutResponse();
        msg.setCarNo(bsReq.getData().getCarNumber())
                .setParkOrderId(bsReq.getData().getOrderId())
                .setServiceName(bsReq.getServiceName())
                .setParkId(bsReq.getParkId());
        return msg;
    }

    /**
     * 上行入参数验签
     * @param reqStr
     * @param sign
     * @return
     */
    public static boolean checkSign(String reqStr, String sign) {
        ObjectMapper deserializeMapper = new ObjectMapper();
        deserializeMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        deserializeMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);

        try {
            JsonNode jsonNode = deserializeMapper.readTree(reqStr);
            String dataParam = jsonNode.get("data").toString();
            String signParam = jsonNode.get("sign").asText();

            String origStr = dataParam + "key=" + sign;
            return DigestUtils.md5DigestAsHex(origStr.getBytes(Charset.forName("utf-8")))
                    .toUpperCase().equals(signParam);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return false;
        }
    }
}
