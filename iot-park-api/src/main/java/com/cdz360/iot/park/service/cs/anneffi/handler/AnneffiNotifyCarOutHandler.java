package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.model.cs.EventDataBase;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component
public class AnneffiNotifyCarOutHandler extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.NOTIFY_CAR_LEAVE;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }

    @Override
    public Mono<ByteArrayOutputStream> process(ByteBuf msg, CsClientTransBase base) {
        long lockId = msg.readUnsignedIntLE();
        log.info("通知车离开[{}]", lockId);
        EventDataBase dataBase = EventDataBase.success()
                .setTaskCode(TASK_CODE)
                .setPartner(base.getPartner())
                .setDeviceNo("" + lockId);
        parkingLockObserver.addEventMsg(dataBase);
        parkingLockObserver.notifyEvent();
        return commanderFactory.getCommander(TASK_CODE)
                .flatMap(commander -> commander.build(new CsClientEncodeBase().setBase(base)));
    }
}
