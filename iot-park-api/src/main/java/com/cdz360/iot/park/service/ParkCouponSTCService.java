package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.park.vo.ParkCouponStcVo;
import com.cdz360.iot.model.park.vo.ParkCouponVo;
import com.cdz360.iot.park.service.cs.common.ParkConstants;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
public class ParkCouponSTCService extends AbstractParkCouponService {

    /**
     * 签名算法
     */
    private static final String ALGORITHM = "RSA";
    /**
     * 签名算法
     */
    private static final String SIGN_ALGORITHMS = "SHA1WithRSA";

    @Autowired
    private BsService bsService;

    @PostConstruct
    public void init() {
        this.parkCouponServiceFactory.addStrategy(ParkConstants.PARK_TYPE_STC, this);
    }

    @Override
    public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        ParkCouponStcVo parkCouponStcVo = getHeaderAuthStc(orderStopRequestV2, data);
        this.setCouponStc(parkCouponStcVo)
                .doOnSuccess(e -> {
                    if (e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.info("第三方道闸STC返回成功");
                    } else {
                        log.warn("第三方道闸STC返回状态异常: {}", e);
                    }
                })
                .doOnError(e -> log.error(e.getMessage(), e))
                .subscribe(
                        res -> log.info("第三方道闸STC executed successfully"),
                        err -> log.error("第三方道闸STC Error executing : {}", err.getMessage())
                );
        return Mono.just(RestUtils.success());
    }

    public Mono<BaseResponse> setCouponStc(ParkCouponStcVo param) {
        log.info("STC请求参数: {}", JsonUtils.toJsonString(param));
        return bsService.setCouponStc(param);
    }

    public static String sign(String content, String privateKey) throws Exception {
        PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(Base64Utils.decodeFromString(privateKey));
        KeyFactory keyf = KeyFactory.getInstance(ALGORITHM);
        PrivateKey priKey = keyf.generatePrivate(priPKCS8);
        Signature signature = Signature.getInstance(SIGN_ALGORITHMS);
        signature.initSign(priKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return Base64Utils.encodeToString(signed);
    }

    public ParkCouponStcVo getHeaderAuthStc(OrderStopRequestV2 orderStopRequestV2, ParkCouponVo data) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timestamp = now.format(formatter);

        LocalDateTime endOfDay = now.plusDays(1);
        String timestampEndOfDay = endOfDay.format(formatter);

        ParkCouponStcVo param = new ParkCouponStcVo();
        param.setAppId(data.getParkAppId());
        param.setMethod("coupon.send");
        param.setTimestamp(timestamp);
        param.setParkId(String.format("%06d", data.getParkId()));
        param.setPlateNumber(data.getCarNo());
        param.setCouponType(2);
        param.setDisHours(2);
        param.setStartTime(timestamp);
        param.setEndTime(timestampEndOfDay);

        String privateKey = "";
        String publicKey = "";
        if (StringUtils.isNotBlank(data.getParkConfig())) {
            final JsonNode jsonNode = JsonUtils.fromJson(data.getParkConfig());
            privateKey = jsonNode.get("privateKey").asText("");
            publicKey = jsonNode.get("publicKey").asText("");
        }

        //参数名称按照字母从小到大
        String content = "appId=" + param.getAppId() + "&" + "couponType=" + param.getCouponType() + "&" + "disHours=" + param.getDisHours() +
                "&" + "endTime=" + param.getEndTime() + "&" + "method=" + param.getMethod() + "&" + "parkId=" + param.getParkId() +
                "&" + "plateNumber=" + param.getPlateNumber() + "&" + "startTime=" + param.getStartTime() + "&" + "timestamp=" + param.getTimestamp();

        String sign = "";
        try {
            sign = sign(content, privateKey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("STC签名: {}", sign);
        param.setSign(sign);
        return param;
    }
}