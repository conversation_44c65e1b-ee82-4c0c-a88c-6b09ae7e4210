package com.cdz360.iot.park.service.cs.comsumer;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.park.model.cs.EventDataBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Service
public class ParkingLockObserverService implements ParkingLockObserver, Runnable {

    private Map<ParkingLockPartner, ParkingLockSubObserver> observerMap = new ConcurrentHashMap<>();

    private Queue<EventDataBase> msgQ = new ConcurrentLinkedQueue<>();


    @PostConstruct
    public void init() {
        new Thread(this).start();
    }

    public void addSubObserver(ParkingLockPartner partner, ParkingLockSubObserver observer) {
        this.observerMap.put(partner, observer);
    }

    @Override
    public void notifyEvent() {
        synchronized (this) {
            this.notifyAll();
        }
    }

    @Override
    public void addEventMsg(EventDataBase msg) {
        msgQ.add(msg);
    }

    private void processMsg() {
        while (!msgQ.isEmpty()) {
            EventDataBase lockMsg = msgQ.remove();
            this.processMsg(lockMsg);
        }
    }

    private void processMsg(EventDataBase lockMsg) {
        ParkingLockSubObserver observer = this.observerMap.get(lockMsg.getPartner());
        if (observer != null) {
            observer.notify(lockMsg);
        } else {
            log.warn("不支持的数据标识: msg = {}, partner = {}", lockMsg, lockMsg.getPartner());
        }
    }


    @Override
    public void run() {
        while (true) {
            try {
                synchronized (this) {
                    this.processMsg();
                    this.wait();
                }
            } catch (Exception e) {
                log.error("error = {}", e.getMessage(), e);
            }
        }
    }
}
