package com.cdz360.iot.park.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import com.cdz360.iot.park.service.ParkingLockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "地锁相关接口")
@RestController
@Slf4j
@RequestMapping("/park/parkingLock")
public class ParkingLockRest {

    @Autowired
    private ParkingLockService parkingLockService;

    @Operation(summary = "添加车牌到全局白名单")
    @GetMapping(value = "/addGlobalWhite")
    public Mono<BaseResponse> addGlobalWhite(
            @Parameter(name = "车牌号", required = true) @RequestParam String carNo) {
        log.info("添加车牌到全局白名单: carNo = {}", carNo);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "添加车牌到地锁专属白名单里")
    @GetMapping(value = "/addLocalWhite")
    public Mono<BaseResponse> addLocalWhite(
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId,
            @Parameter(name = "车牌号", required = true) @RequestParam String carNo) {
        log.info("添加车牌到地锁专属白名单里: lockId = {}, carNo = {}", lockId, carNo);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "开闭锁")
    @GetMapping(value = "/switchLock")
    public Mono<BaseResponse> switchLock(
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId,
            @Parameter(name = "打开(true)/关闭(false)", required = true) @RequestParam Boolean open) {
        log.info("开闭锁: lockId = {}, open = {}", lockId, open);
        return parkingLockService.switchLock(lockId, open);
    }

    @Operation(summary = "远程断电锁(断电以防水淹事故)")
    @GetMapping(value = "/cutPower")
    public Mono<BaseResponse> cutPower(
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("远程断电锁: lockId = {}", lockId);
        return parkingLockService.cutPower(lockId);
    }

    @Operation(summary = "远程重启锁")
    @GetMapping(value = "/rebootLock")
    public Mono<BaseResponse> rebootLock(
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("远程重启锁: lockId = {}", lockId);
        return parkingLockService.rebootLock(lockId);
    }

    @Operation(summary = "向地锁云查询地锁")
    @GetMapping(value = "/lookForLock")
    public Mono<ObjectResponse<ParkingLockVo>> lookForLock(
            @Parameter(name = "场站ID", required = true) @RequestParam String siteId,
            @Parameter(name = "地锁ID(地锁云记录ID)", required = true) @RequestParam String remoteLockId) {
        log.info("向地锁云查询地锁: siteId = {}, remoteLockId = {}", siteId, remoteLockId);
        return parkingLockService.lookForLock(siteId, remoteLockId)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "锁状态查询/地锁状态同步")
    @GetMapping(value = "/lockStatusInTime")
    public Mono<ObjectResponse<ParkingLockStatus>> lockStatusInTime(
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("锁状态查询/地锁状态同步: lockId = {}", lockId);
        return parkingLockService.fetchLockStatus(lockId)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "同步地锁信息")
    @GetMapping(value = "/fetchLockInfo")
    public Mono<BaseResponse> fetchLockInfo(
            @Parameter(name = "场站ID", required = true) @RequestParam String siteId) {
        log.info("同步地锁信息: siteId = {}", siteId);
        return parkingLockService.fetchLockInfo(siteId);
    }
}
