package com.cdz360.iot.park.model.cs;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.Channel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.ByteArrayOutputStream;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Schema(description = "客户端通道信息")
@Data
@Accessors(chain = true)
@Builder
public class CsClientChannel {

    private Channel channel;

    @Schema(description = "地锁供应商")
    private ParkingLockPartner partner;

    // 地所云提供的客户编码
    private Long appId;

    // 地所云提供的客户密码
    private String appKey;

    // 客户端登录状态
    private boolean login;

    public static void channelWrite(CsClientChannel channel, ByteArrayOutputStream buf) {
        log.info("发送报文: {}", ByteBufUtil.hexDump(buf.toByteArray()));
        channel.getChannel().writeAndFlush(buf.toByteArray());
    }
}
