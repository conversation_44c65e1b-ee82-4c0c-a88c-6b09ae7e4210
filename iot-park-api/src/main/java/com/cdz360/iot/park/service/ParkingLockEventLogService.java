package com.cdz360.iot.park.service;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ParkingLockEventLogRoDs;
import com.cdz360.iot.ds.ro.ParkingLockRoDs;
import com.cdz360.iot.ds.rw.ParkingLockEventLogRwDs;
import com.cdz360.iot.model.park.param.PlugStatusEventLogParam;
import com.cdz360.iot.model.park.po.ParkingLockEventLogPo;
import com.cdz360.iot.model.park.po.ParkingLockPo;
import com.cdz360.iot.model.park.type.ParkingLockEventLogType;
import com.cdz360.iot.model.park.type.ParkingLockPartner;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.cdz360.iot.model.park.vo.ParkingLockEventLogVo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ParkingLockEventLogService {

    // 事件日志关注状态
    private static final Map<ParkingLockStatus, ParkingLockEventLogType> convertMap = new HashMap<>() {{
        put(ParkingLockStatus.NORMAL, ParkingLockEventLogType.LOCK);
        put(ParkingLockStatus.OPEN_CAR_IN, ParkingLockEventLogType.UNLOCK);
        put(ParkingLockStatus.OPEN_NOT_CAR, ParkingLockEventLogType.UNLOCK);
    }};

    // 事件日志关注状态
    private static final Map<PlugStatus, ParkingLockEventLogType> plugStatusMap = new HashMap<>() {{
        put(PlugStatus.CONNECT, ParkingLockEventLogType.PLUG_CONNECT);
        put(PlugStatus.BUSY, ParkingLockEventLogType.CHARGER_START);
        put(PlugStatus.JOIN, ParkingLockEventLogType.CHARGER_START);
        put(PlugStatus.RECHARGE_END, ParkingLockEventLogType.CHARGER_FINISHED);
        put(PlugStatus.IDLE, ParkingLockEventLogType.PLUG_DISCONNECT);
    }};

    @Autowired
    private ParkingLockRoDs parkingLockRoDs;

    @Autowired
    private ParkingLockEventLogRoDs parkingLockEventLogRoDs;

    @Autowired
    private ParkingLockEventLogRwDs parkingLockEventLogRwDs;

    private Map<String, ParkingLockStatus> lockStatusMap = new ConcurrentHashMap<>();

    private static String key(ParkingLockPartner partner, String serialNumber) {
        return partner.name() + ":" + serialNumber;
    }


    @Async
    public void parkingLockStatus(
        ParkingLockPartner partner, String serialNumber, ParkingLockStatus status) {

        try {
            final ParkingLockPo lock = parkingLockRoDs.getByUniqueKey(partner, serialNumber);
            if (null == lock) {
                return;
            }

            if (!convertMap.containsKey(status)) {
                return;
            }

            final String key = key(partner, serialNumber);
            final ParkingLockStatus oldStatus = lockStatusMap.get(key);
            if (null == oldStatus || !oldStatus.equals(status)) {
                lockStatusMap.put(key, status);
                final ParkingLockEventLogPo eventLog = new ParkingLockEventLogPo()
                    .setParkingLockId(lock.getId())
                    .setEventType(convertMap.get(status));
                final boolean b = parkingLockEventLogRwDs.insertParkingLockEventLog(eventLog);
                if (!b) {
                    log.error("地锁插入事件日志失败: {}", eventLog);
                }
            }
        } catch (Exception e) {
            log.error("地锁状态事件日志处理异常: {}", e.getMessage(), e);
        }
    }

    @Async
    public void plugStatusEventLog(PlugStatusEventLogParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号无效");
        IotAssert.isNotNull(param.getPlugId(), "枪头不能为空");

        try {
            // 枪头对应的地锁
            final ParkingLockPo lock = parkingLockRoDs.getByEvseNoAndPlugId(
                param.getEvseNo(), param.getPlugId());
            if (null == lock) {
                return;
            }

            if (!plugStatusMap.containsKey(param.getStatus())) {
                return;
            }

            final ParkingLockEventLogPo eventLog = new ParkingLockEventLogPo()
                .setParkingLockId(lock.getId())
                .setEventType(plugStatusMap.get(param.getStatus()))
                .setOrderNo(param.getOrderNo());
            log.info("枪头状态变更事件日志: {} , {}", param, eventLog);
            final boolean b = parkingLockEventLogRwDs.insertParkingLockEventLog(eventLog);
            if (!b) {
                log.error("地锁插入事件日志失败: {}", eventLog);
            }
        } catch (Exception e) {
            log.error("枪头状态变更事件处理异常: {}", e.getMessage(), e);
        }
    }

    @Async
    public void carInEventLog(ParkingLockPartner partner, String serialNumber, String carNo) {
        try {
            final ParkingLockPo lock = parkingLockRoDs.getByUniqueKey(partner, serialNumber);
            if (null == lock) {
                return;
            }

            final ParkingLockEventLogPo eventLog = new ParkingLockEventLogPo()
                .setParkingLockId(lock.getId())
                .setEventType(ParkingLockEventLogType.CAR_IN)
                .setCarNo(carNo);
            final boolean b = parkingLockEventLogRwDs.insertParkingLockEventLog(eventLog);
            if (!b) {
                log.error("地锁插入事件日志失败: {}", eventLog);
            }
        } catch (Exception e) {
            log.error("地锁车牌识别事件处理异常: {}", e.getMessage(), e);
        }
    }

    @Async
    public void carOutEventLog(ParkingLockPartner partner, String serialNumber) {
        try {
            final ParkingLockPo lock = parkingLockRoDs.getByUniqueKey(partner, serialNumber);
            if (null == lock) {
                return;
            }

            final ParkingLockEventLogPo eventLog = new ParkingLockEventLogPo()
                .setParkingLockId(lock.getId())
                .setEventType(ParkingLockEventLogType.CAR_OUT);
            final boolean b = parkingLockEventLogRwDs.insertParkingLockEventLog(eventLog);
            if (!b) {
                log.error("地锁插入事件日志失败: {}", eventLog);
            }
        } catch (Exception e) {
            log.error("地锁车辆离开事件处理异常: {}", e.getMessage(), e);
        }
    }

    public Mono<List<ParkingLockEventLogVo>> eventLogRecent20(Long parkingLockId) {
        IotAssert.isNotNull(parkingLockId, "地锁ID不能为空");
        return Mono.just(parkingLockEventLogRoDs.eventLogRecent(parkingLockId, 20));
    }

}
