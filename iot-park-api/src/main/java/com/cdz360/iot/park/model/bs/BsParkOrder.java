package com.cdz360.iot.park.model.bs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BsParkOrder {

    @JsonProperty(value = "car_number")
    @Schema(description = "车牌号")
    private String carNumber;

    @JsonProperty(value = "in_time")
    @Schema(description = "进场时间（unix时间戳格式）")
    private Long inTime;

    @JsonProperty(value = "out_time")
    @Schema(description = "出场时间（unix时间戳格式）")
    private Long outTime;

    @Schema(description = "入场收费员编号(支持数字 字母 _ )")
    private String uid;


    @Schema(description = "停车时长(分钟)")
    private Integer duration;


    @JsonProperty(value = "car_type")
    @Schema(description = "车型", example = "小车")
    private String carType;


    @JsonProperty(value = "c_type")
    @Schema(description = "进场类型", example = "通道扫牌")
    private String cType;


    @JsonProperty(value = "out_type")
    @Schema(description = "出场类型", example = "通道扫牌")
    private String outType;


    @JsonProperty(value = "pay_type")
    @Schema(description = "支付类型", example = "cash")
    private String payType;


    @JsonProperty(value = "auth_code")
    @Schema(description = "授权码，支付类型是sweepcode时必传", example = "微信或支付宝支付码")
    private String authCode;


    @JsonProperty(value = "empty_plot")
    @Schema(description = "空闲车位数")
    private Integer emptyPlot;


    @Schema(description = "实时订单金额（不包含已预付、已减免的部分）")
    private String total;


    @JsonProperty(value = "order_id")
    @Schema(description = "订单记录号(车辆在停车场停车唯一订单编号，对应入场订单编号,支持数字 字母 _ )")
    private String orderId;



    @Schema(description = "免费原因")
    private String freereasons;

    @JsonProperty(value = "out_channel_id")
    @Schema(description = "出场通道", example = "B1")
    private String outChannelId;

    @JsonProperty(value = "in_channel_id")
    @Schema(description = "入场通道", example = "A1")
    private String inChannelId;

    @JsonProperty(value = "work_id")
    @Schema(description = "岗亭编号")
    private String workId;

    @JsonProperty(value = "ticket_id")
    @Schema(description = "减免券ID")
    private String ticketId;

    @JsonProperty(value = "reduce_amount")
    @Schema(description = "减免金额")
    private String reduceAmount;

    @JsonProperty(value = "pic_url")
    @Schema(description = "车辆出场图片，如果该车场开启图片上传，才能上传图片链接，链接有过期时间")
    private String picAddr;

    @Schema(description = "数据来源", example = "ist")
    private String source;
}
