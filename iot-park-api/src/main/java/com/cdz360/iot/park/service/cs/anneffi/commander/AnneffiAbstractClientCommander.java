package com.cdz360.iot.park.service.cs.anneffi.commander;

import com.cdz360.iot.common.utils.ByteArrayUtil;
import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import com.cdz360.iot.park.model.cs.CsClientTransBase;
import com.cdz360.iot.park.service.cs.common.CsClientTransCommander;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;

@Component
public abstract class AnneffiAbstractClientCommander implements CsClientTransCommander {

    protected static final byte[] DEFAULT_HEADER = new byte[]{(byte) 0xEF, 0x01, 0x01, (byte) 0xEF}; // 默认使用报文头

    @Autowired
    protected AnneffiClientCommanderFactory commanderFactory;

    @Override
    public <E extends CsClientEncodeBase> Mono<ByteArrayOutputStream> build(E base) {
        base.getBase().setLen(12);
        ByteArrayOutputStream os = this.preCommon(base.getBase());
        this.sign(os);
        return Mono.justOrEmpty(os);
    }

    public ByteArrayOutputStream preCommon(CsClientTransBase base) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        os.writeBytes(DEFAULT_HEADER);
        ByteArrayUtil.writeShort(os, base.getLen());
        os.writeBytes(base.getCustomerIdBytes());
        byte[] sec = {0x00, 0x00, 0x00, 0x00}; // TODO: 需要根据实际调整
        os.writeBytes(sec);
        os.write(base.getTaskCode().getCode());
        os.write(base.getParam1());
        os.writeBytes(new byte[2]);
        return os;
    }

    // 不包含标识头
    @Override
    public void sign(ByteArrayOutputStream os) {
        int len = os.toByteArray().length - 4;
        byte[] buf = new byte[len];
        System.arraycopy(os.toByteArray(), 4, buf, 0, len);
        int crc = acc(buf, buf.length);
        os.write((byte) (crc & 0xFF));
        os.write((byte) (crc >> 8 & 0xFF));
    }

    public static int acc(byte[] buf, int len) {
        int c = 0;
        for (int i = 0; i < len; i++) {
            c += buf[i] & 0x00FF;
        }
        return (0xFFFF & c);
    }
}
