package com.cdz360.iot.park.model.cs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "业务码")
public enum ClientTaskCode {
    LOGIN(1, true, "登录"),
    HB(2, true, "心跳"),
    NOTIFY_CAR_ARRIVE(3, false, "通知车牌"),
    NOTIFY_CAR_LEAVE(4, false, "通知车离开"),
    GLOBAL_WHITE(5, true, "全局白名单"),
    LOCAL_WHITE(6, true, "专属白名单"),
    SWITCH_LOCK(7, true, "开闭锁"),
    GET_LOCK_STATUS(8, true, "锁状态查询"),
    CUT_POWER(9, true, "远程断电"),
    REBOOT(10, true, "远程重启"),
    GET_LOCK_INFO(11, true, "请求指定地锁信息"),
    NOTIFY_NEW_LOCK(12, false, "通知客户新锁安装"),
    NOTIFY_LOCK_ERR(13, false, "通知客户地锁异常"),
//  ==========  👆 安效停 👆  ==========
    ;

    private final int code;
    @Schema(description = "用于判断请求响应处理: true: 主动请求; false: 被动接收")
    private final boolean outflow;
    private final String desc;

    ClientTaskCode(int code, boolean outflow, String desc) {
        this.code = code;
        this.outflow = outflow;
        this.desc = desc;
    }

    public static ClientTaskCode codeOf(int codeIn) {
        for (ClientTaskCode code : ClientTaskCode.values()) {
            if (codeIn == code.getCode()) {
                return code;
            }
        }
        return null;
    }
}
