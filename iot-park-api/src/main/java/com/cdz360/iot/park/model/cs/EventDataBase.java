package com.cdz360.iot.park.model.cs;

import com.cdz360.iot.model.park.type.ParkingLockPartner;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Builder
public class EventDataBase {

    private ParkingLockPartner partner;

    private String deviceNo;

    private ClientTaskCode taskCode;

    private String data;

    private ResultCode resultCode;

    public enum ResultCode {
        SUCCESS,
        FAIL
    }

    public static EventDataBase success(String data) {
        return success().setData(data);
    }

    public static EventDataBase success() {
        return EventDataBase.builder()
                .resultCode(ResultCode.SUCCESS)
                .build();
    }

    public static EventDataBase fail(String data) {
        return fail().setData(data);
    }

    public static EventDataBase fail() {
        return EventDataBase.builder()
                .resultCode(ResultCode.FAIL)
                .build();
    }
}
