package com.cdz360.iot.park.service.cs.job;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.park.type.ParkingLockStatus;
import com.cdz360.iot.model.park.vo.ParkingLockVo;
import com.cdz360.iot.park.model.cs.CsClientChannel;
import com.cdz360.iot.park.model.cs.EventDataBase;
import com.cdz360.iot.park.model.cs.anneffi.AnneffiLockInfo;
import com.cdz360.iot.park.service.cs.CsClientBizFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 地锁信息
 */
@Slf4j
public class ParkingLockJob extends AbstractJob {

    @Getter
    private Object target;

    public ParkingLockJob(CsClientBizFactory bizFactory, CsClientChannel channel, String deviceNo) {
        super(bizFactory, channel, deviceNo);
    }

    @Override
    public void fillData(EventDataBase msgIn) {
        if (EventDataBase.ResultCode.SUCCESS.equals(msgIn.getResultCode())) {
            switch (msgIn.getTaskCode()) {
                case GET_LOCK_INFO:
                    if (StringUtils.isNotBlank(msgIn.getData())) {
                        AnneffiLockInfo lock = JsonUtils.fromJson(
                            msgIn.getData(), AnneffiLockInfo.class);
                        target = new ParkingLockVo()
                            .setPartner(msgIn.getPartner())
                            .setSerialNumber(lock.getSerialNumber())
                            .setStatus(ParkingLockStatus.anneffiSwap(lock.getStatus()))
                            .setType(lock.getType())
                            .setElectricQuantity(lock.getElectricQuantity())
                            .setParkingLotId(lock.getParkingLotId())
                            .setParkingLotName(lock.getParkingLotName())
                            .setParkingSpaceCode(lock.getParkingSpaceCode())
                            .setSrcDetail(msgIn.getData());
                    } else {
                        target = new ParkingLockVo();
                    }
                    break;
                case GET_LOCK_STATUS:
                    target = ParkingLockStatus.anneffiSwap(Integer.parseInt(msgIn.getData()));
                    break;
                case SWITCH_LOCK:
                case CUT_POWER:
                case REBOOT:
                    target = Integer.parseInt(msgIn.getData());
                    break;
                default:
                    throw new DcServiceException("该业务码不支持: " + msgIn.getTaskCode());
            }
        } else {
            throw new DcServiceException(msgIn.getData());
        }
    }

    @SuppressWarnings("all")
    @Override
    public void finish() {
        this.getMonoSink().success(this.target);
    }

    @Override
    public void exception(Throwable ex) {
        this.getMonoSink().error(ex);
    }
}
