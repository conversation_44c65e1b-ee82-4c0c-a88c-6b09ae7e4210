package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname LankaModifyMemoDto
 * @Description
 * @Date 2/2/2024 3:45 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class LankaModifyMemoDto {
    @Schema(description = "新的订单ID")
    @JsonProperty("new_id")
    private String newId;

    @Schema(description = "旧的订单ID")
    @JsonProperty("old_id")
    private String oldId;

    @Schema(description = "新的车牌")
    @JsonProperty("new_plate")
    private String newPlate;

    @Schema(description = "旧的车牌")
    @JsonProperty("old_plate")
    private String oldPlate;
}