package com.cdz360.iot.park.model.api;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/6
 * @description
 */
@Data
@Accessors(chain = true)
public class YzParkReq {

    private String park_id; // 是	车场ID	String	车场唯一标识
    private String park_name; // 是	车场名字	String	车场名字
    private String record_id; // 是	车辆入场时由场库生成的唯一编号	String	车辆入场时由场库生成的唯一编号
    private String plate_number; //	是	车牌号码	String	车牌号码
    private String entrance_time; // 是	进场时间	String	进场时间(时间戳格式)

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String exit_time; // 否	出场时间	String	出场时间(时间戳格式) (入场记录没有)

    private String entrancegate_id; //	是	进场通道id String	进场通道
    private String exitgate_id; //	否	出场通道id	String	出场通道(入场记录没有)
    private Integer release_type; //	否	放行类型	int	0 正常放行 1 收费放行 2 免费放行 3 异常放行
    private String cartype_id; // 是	车类描述id	String	车类描述id
    private String carmodel_id; // 是	车型描述id	String	车型描述id
    private Integer enter_type; // 否	入场类型	int	入场类型(0正常，1过期转临停,2车位占用转临停)
    private Integer plate_color; // 否	车牌类型	Int	0 蓝牌 1 黑牌 2 黄牌 3 新黄牌 4 黄色后牌 5 警车 6 军车 7 新黄色后 8武警9新白牌
    private Integer is_exit; // 是	是否出场	Int	1是出场 0入场

    @JsonIgnore
    private String inimg_picture; // 否	入场图片	String	Base64字符

    @JsonIgnore
    private String inimg_pictures; // 否	入场车牌图片	String	Base64字符

    @JsonIgnore
    private String outimg_picture; // 否	出场图片	String	Base64字符

    @JsonIgnore
    private String outimg_pictures; // 否	出场车牌图片	String	Base64字符
    private String amountTotle; // 否	应缴金额	String
    private String discountAmount; // 否	折扣金额	String
    private String amount; // 否	实缴金额	String	Key是验证
    private String payType; // 否	支付方式	String	支付方式 1现金 2 微信 3 支付宝 4ETC 99其它
}
