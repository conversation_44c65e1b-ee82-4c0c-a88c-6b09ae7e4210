package com.cdz360.iot.park.service.cs.anneffi.handler;

import com.cdz360.iot.park.model.cs.ClientTaskCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Slf4j
@Component
public class AnneffiHbHand<PERSON> extends AnneffiAbstractClientHandler {

    private static final ClientTaskCode TASK_CODE = ClientTaskCode.HB;

    @Autowired
    private AnneffiClientHandlerFactory handlerFactory;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(TASK_CODE, this);
    }
}
