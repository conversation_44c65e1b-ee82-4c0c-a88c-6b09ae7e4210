package com.cdz360.iot.park.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.park.model.api.YzParkReq;
import com.cdz360.iot.park.model.api.YzParkRes;
import com.cdz360.iot.park.model.bs.BsParkIn;
import com.cdz360.iot.park.model.bs.BsParkOrder;
import com.cdz360.iot.park.model.bs.BsParkOut;
import com.cdz360.iot.park.service.ParkBizService;
import com.cdz360.iot.park.service.YzParkService;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2023/4/6
 * @description 车场记录推送记录
 */
@RestController
@RequestMapping("/yzPark")
@Slf4j
@RequiredArgsConstructor
public class YzParkRest {

    @Autowired
    private YzParkService yzParkService;

    @Autowired
    private ParkBizService parkBizService;

    @PostMapping("/record")
    public Mono<YzParkRes> record(@RequestBody YzParkReq param) {
        log.info("车场记录推送记录 = {}", JsonUtils.toJsonString(param));
        if (param.getIs_exit() == 1) { // 出场
            BsParkOut bsParkOrder = new BsParkOut();
            bsParkOrder.setParkId(Long.valueOf("99" + param.getPark_id().substring(0,6)));
            bsParkOrder.setCarNumber(param.getPlate_number())
                .setOutTime(Long.valueOf(param.getExit_time())/1000)
                .setOrderId(param.getRecord_id())
                .setReduceAmount(param.getDiscountAmount())
                .setTotal(param.getAmount())
                .setSource("CX");
            parkBizService.parkOut(bsParkOrder);
        } else if (param.getIs_exit() == 0) { // 进场
            BsParkIn bsParkOrder = new BsParkIn();
            bsParkOrder.setParkId(Long.valueOf("99" + param.getPark_id().substring(0,6)));
            bsParkOrder.setCarNumber(param.getPlate_number())
                .setInTime(Long.valueOf(param.getEntrance_time())/1000)
                .setOrderId(param.getRecord_id())
                .setSource("CX");
            parkBizService.parkIn(bsParkOrder);
        } else {
            log.warn("车场记录推送出入场类型不对, is_exit: {}", param.getIs_exit());
        }
        YzParkRes yzParkRes = new YzParkRes();
        yzParkRes.setStatus("1")
            .setMessage("成功");
        return Mono.just(yzParkRes);
    }
}
