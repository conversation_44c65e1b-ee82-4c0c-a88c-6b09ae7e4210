package com.cdz360.iot.park.model.cs.anneffi;

import com.cdz360.iot.park.model.cs.CsClientEncodeBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AnneffiLockOpEncode extends CsClientEncodeBase {

    @Schema(description = "供应商地锁ID(交互使用字段)")
    private String serialNumber;

    @Schema(description = "开启/关闭")
    private boolean open;

    public AnneffiLockOpEncode() {}

    public AnneffiLockOpEncode(Long appId) {
        super(appId);
    }

    public AnneffiLockOpEncode(Long appId, String serialNumber) {
        super(appId);
        this.serialNumber = serialNumber;
    }
}
