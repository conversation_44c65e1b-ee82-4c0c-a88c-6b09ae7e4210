


plugins {
    id 'java'
    id "com.gorylenko.gradle-git-properties"
}



apply plugin: 'org.springframework.boot'

sourceSets {
    main {
        resources {
            srcDirs "src/main/resources", "src/main/java"
        }
    }
}


springBoot {
    buildInfo()
}

dependencies {

    implementation project(':iot-common')
    implementation project(':iot-model')
    implementation project(':iot-ds')
    implementation project(':iot-device-mgm-client')
    //implementation project(':iot-auth-client')

    implementation("com.cdz360.cloud:dc-data-sync:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-sync-publisher:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-reader:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-writer:${dcCloudVersion}")
    

    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation('org.springframework.cloud:spring-cloud-starter-config')
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')
//    implementation('org.springframework.cloud:spring-cloud-starter-netflix-hystrix')
    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
//    implementation('org.springframework.cloud:spring-cloud-starter-sleuth')
//    implementation('org.springframework.cloud:spring-cloud-sleuth-zipkin')
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
    implementation 'io.github.openfeign:feign-micrometer'
    //implementation("io.springfox:springfox-swagger-ui:${swaggerVersion}")
//    implementation "io.springfox:springfox-boot-starter:${swaggerVersion}"
    implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")
    implementation("org.springdoc:springdoc-openapi-webflux-ui:${springdocVersion}")
    testImplementation('org.springframework.boot:spring-boot-test')
//    implementation('io.netty:netty-all')

    implementation("com.github.pagehelper:pagehelper-spring-boot-starter:${pageHelperStarterVersion}")

    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisSpringVersion}")
    implementation("mysql:mysql-connector-java:${mysqlConnectorVersion}")

    implementation("org.apache.commons:commons-pool2:${commonsPoolVersion}")

    implementation("com.playtika.reactivefeign:feign-reactor-webclient:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-cloud:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-spring-configuration:${feignReactiveVersion}")

    //implementation("org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.1")

    // logstash间接依赖于jaxb, 在java10+以上的环境, 缺少jaxb-api时, logstash无法正常启动
    implementation("org.glassfish.jaxb:jaxb-runtime")


}
