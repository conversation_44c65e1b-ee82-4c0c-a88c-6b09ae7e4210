package com.cdz360.iot.device.mgm.service;

import com.cdz360.iot.device.mgm.feign.MonitorTaskClient;
import com.cdz360.iot.device.mgm.utils.IotResponseValidator;
import com.cdz360.iot.model.base.BaseRpcResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MonitorTaskService {

    @Autowired
    private MonitorTaskClient monitorTaskClient;


    /**
     * 执行网关监控任务
     */
    public void monitorGw() {
        BaseRpcResponse res = monitorTaskClient.monitorGw();
        IotResponseValidator.check(res);
    }


    /**
     * 执行下行指令监控任务
     */
    public void monitorDownstreamRequest() {
        BaseRpcResponse res = monitorTaskClient.monitorDownstreamRequest();
        IotResponseValidator.check(res);
    }

//
//    /**
//     * 获取临时表中的gwno
//     */
//    public List<String> getMonitorGwGroupBy() {
//        ListRpcResponse<String> res = monitorTaskClient.listGwno();
//        IotResponseValidator.check(res);
//        return res.getData();
//    }
}