package com.cdz360.iot.device.mgm.feign;

//
//@FeignClient(value = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM)
//public interface GwInfoClient {
//    /**
//     * 获取场站列表
//     *
//     * @return
//     */
////    @PostMapping(value = DeviceMgmUrl.URL_LIST_GW)
////    ListRpcResponse<GwInfoDto> listSite(@RequestBody ListGwParam param);
////
////    /**
////     * 配置网关信息
////     *
////     * @param oldGwno
////     * @param currGwno
////     * @param siteId
////     * @return
////     */
////    @PostMapping(value = DeviceMgmUrl.URL_GW_CONFIG)
////    BaseRpcResponse configGwRef(@RequestParam(value = "oldGwno") String oldGwno,
////                                @RequestParam(value = "currGwno") String currGwno,
////                                @RequestParam(value = "siteId") long siteId);
//
//
//    /**
//     * 获取东正城市编号
//     *
//     * @param cityName
//     * @return
//     */
//    @PostMapping(value = DeviceMgmUrl.URL_GW_DZ_CITY_CODE)
//    CommonRpcResponse<String> getDzCityCode(@RequestParam(value = "cityName") String cityName);
////
////    /**
////     * 获取当前城市未绑定场站的网关编号
////     *
////     * @param cityCode
////     * @return
////     */
////    @GetMapping(value = DeviceMgmUrl.URL_GET_GWNO)
////    ListRpcResponse<String> getNotBoundGwno(@RequestParam(value = "cityCode") String cityCode);
//
//}
