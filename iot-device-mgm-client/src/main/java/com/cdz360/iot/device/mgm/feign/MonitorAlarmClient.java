package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.iot.device.mgm.cfg.DeviceMgmUrl;
import com.cdz360.iot.model.base.CommonRpcResponse;
import com.cdz360.iot.model.dongzheng.request.SysLeveAlarmReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = DcConstants.KEY_FEIGN_IOT_MONITOR)
public interface MonitorAlarmClient {

    /**
     * 发送告警消息
     *
     * @return
     */
    @PostMapping(value = DeviceMgmUrl.URLSEND_ALARM_SYS_LEVE)
    CommonRpcResponse sendSysLeveAlarm(@RequestBody SysLeveAlarmReq request);

    ;

}
