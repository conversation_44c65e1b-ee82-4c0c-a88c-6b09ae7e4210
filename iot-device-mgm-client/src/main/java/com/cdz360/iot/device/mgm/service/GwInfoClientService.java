package com.cdz360.iot.device.mgm.service;

//import com.cdz360.iot.device.mgm.feign.GwInfoClient;
//import com.cdz360.iot.model.base.BaseRpcResponse;
//import com.cdz360.iot.model.base.CommonRpcResponse;
//import com.cdz360.iot.model.base.ListRpcResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//@Service
//public class GwInfoClientService {
//
//    @Autowired
//    private GwInfoClient gwInfoClient;
//
////    public List<GwInfoDto> listSite(ListGwParam param) {
////        ListRpcResponse<GwInfoDto> res = this.gwInfoClient.listSite(param);
////        IotResponseValidator.check(res);
////        return res.getData();
////    }
//
////    public BaseRpcResponse configGwRef(String oldGwno, String currGwno, long siteId){
////        return this.gwInfoClient.configGwRef(oldGwno,currGwno,siteId);
////    }
//
//    public String getDzCityCode(String cityName) {
//        CommonRpcResponse<String> ret = this.gwInfoClient.getDzCityCode(cityName);
//        return ret.getData();
//    }
//
////    public ListRpcResponse<String> getNotBoundGwno(String cityCode) {
////        ListRpcResponse<String> res = this.gwInfoClient.getNotBoundGwno(cityCode);
////        return res;
////    }
//}
