package com.cdz360.iot.device.mgm.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.iot.device.mgm.cfg.DeviceMgmUrl;
import com.cdz360.iot.model.base.BaseRpcResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM)
public interface MonitorTaskClient {

    /**
     * 执行网关监控任务
     *
     * @return
     */
    @PostMapping(value = DeviceMgmUrl.URL_MONITOR_GW)
    BaseRpcResponse monitorGw();

    /**
     * 执行下行指令监控任务
     *
     * @return
     */
    @PostMapping(value = DeviceMgmUrl.URL_MONITOR_DOWNSTREAM_REQUEST)
    BaseRpcResponse monitorDownstreamRequest();
//
//    /**
//     * 获取监控中的网关列表，列表来自tmp_log的group by
//     *
//     * @return
//     */
//    @PostMapping(value = DeviceMgmUrl.URL_MONITOR_LIST_GWNO)
//    ListRpcResponse<String> listGwno();
}
