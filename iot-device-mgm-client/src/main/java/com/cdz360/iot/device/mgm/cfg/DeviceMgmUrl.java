package com.cdz360.iot.device.mgm.cfg;

public interface DeviceMgmUrl {

//    String URL_LIST_SITE = "/device/mgm/site/listSite";  // 场站列表页面
//    String URL_MODIFY_SITE = "/device/mgm/site/modifySite";  // 场站列表页面
//    String URL_LIST_GW = "/device/mgm/gw/listgw";  // 网关列表页面


    String URL_MONITOR_GW = "/device/mgm/monitor/gw";  // 执行网关监控任务
    String URL_MONITOR_DOWNSTREAM_REQUEST = "/device/mgm/monitor/downstreamRequest";  // 执行下行指令监控任务
    String URL_MONITOR_LIST_GWNO = "/device/mgm/monitor/listGwno";  // 获取监控列表

    String URL_GW_CONFIG = "/device/mgm/gw/configGwRef";  //配置网关信息
    String URL_GW_DZ_CITY_CODE = "/device/mgm/gw/dzCityCode";  //获取城市编号

    String URL_GET_GWNO="/device/mgm/gw/getNotBoundGwno";  //获取当前城市未绑定场站的网关编号

    String URLSEND_ALARM_SYS_LEVE = "/api/alarm/sendSysLeveAlarm";  //推送系统级别告警 网关登陆超时 微服务异常
}
