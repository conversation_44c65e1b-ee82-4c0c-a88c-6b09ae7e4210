package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.iot.model.base.OrderCreateResponseV2;
import com.cdz360.iot.model.evse.*;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultLimitSoc;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultStartCharge;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING, fallbackFactory = BizTradingHystrixFeignClientFactory.class)
public interface BizTradingFeignClient {


    /**
     * 开启充电
     *
     * @param chgEvseReq
     * @return
     */
    @PostMapping(value = "/dc/order/create")
    Mono<ObjectResponse<OrderCreateResponseV2>> orderCreate(@RequestBody ChgEvseRequest chgEvseReq);



    @PostMapping(value = "/dc/order/starting")
    Mono<BaseResponse> orderStarting(@RequestBody IotGwCmdResultStartCharge iotGwCmdResultStartCharge);

    /**
     * 开始充电状态上报
     *
     * @return
     */
    @PostMapping(value = "/dc/order/start")
    Mono<BaseResponse> orderStart(@RequestBody OrderStartRequest orderStartReq);

    /**
     * 充电中数据上报
     *
     * @return
     */
    @PostMapping(value = "/dc/order/update")
    Mono<BaseResponse> orderUpdate(@RequestBody OrderUpdateRequestV2 orderUpdateReq);

    /**
     * 结束充电
     *
     * @return
     */
    @PostMapping(value = "/dc/order/stop")
    Mono<BaseResponse> orderStop(@RequestBody OrderStopRequestV2 orderStopReqV2);


    /**
     * 校验订单是否正常结束
     *
     * @param orderNo
     * @return
     */
    @PostMapping(value = "/api/order/handleErrorOrder")
    Mono<BaseResponse> handleErrorOrder(@RequestParam("orderNo") String orderNo,
                                  @RequestParam(value = "abnormalReason", required = false) OrderAbnormalReason abnormalReason);


    /**
     * 充电续费
     *
     * @return
     */
    @PostMapping(value = "/dc/order/fee/renewal")
    Mono<ObjectResponse<OrderFeeRefreshResponseV2>> orderFeeRefresh(@RequestBody OrderFeeRefreshRequestV2 orderFeeRefreshReq);

    /**
     * 订单超停标记
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/api/order/overtimeParking")
    Mono<BaseResponse> overtimeParking(@RequestParam(value = "orderNo") String orderNo);

    // 急停恢复后处理逻辑
    @PostMapping(value = "/api/pay/crashStopRecovery")
    Mono<BaseResponse> crashStopRecovery(@RequestParam("evseNo") String evseNo,
                                         @RequestParam("plugId") Integer plugId);

    @PostMapping(value = "/soc/limitSoc/feedback")
    Mono<BaseResponse> limitSocFeedback(@RequestBody IotGwCmdResultLimitSoc req);
}
