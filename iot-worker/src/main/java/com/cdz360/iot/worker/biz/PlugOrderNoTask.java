package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.worker.ds.service.PlugService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Service
public class PlugOrderNoTask {


    private Queue<PlugVo> queue = new ConcurrentLinkedQueue<>();

    @Autowired
    private PlugService plugService;


    /**
     * 定期将订单号写入到t_plug表, 缓存丢失时可以从t_plug表恢复
     */
    @Scheduled(fixedRate = 500)
    public void plugOrderNoTask() {
        while (!queue.isEmpty()) {
            PlugVo plug = queue.remove();
            // orderNo = null 表示没有订单.
            String orderNo = StringUtils.isBlank(plug.getOrderNo()) ? null : plug.getOrderNo();
            plugService.updateOrderNo(plug.getEvseNo(), plug.getIdx(), orderNo);
        }
    }


    public void push(PlugVo plug) {
        this.queue.add(plug);
    }
}
