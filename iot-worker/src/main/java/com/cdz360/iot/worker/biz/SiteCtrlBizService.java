package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.base.model.iot.vo.SiteCtrlVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.MqttService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.common.utils.MqttUtils;
import com.cdz360.iot.ds.ro.SiteCtrlCfgRoDs;
import com.cdz360.iot.ds.ro.SiteCtrlRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.SiteCtrlCfgLogRwDs;
import com.cdz360.iot.ds.rw.SiteCtrlRwDs;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.order.type.SiteCtrlCfgStatusType;
import com.cdz360.iot.model.site.ctrl.*;
import com.cdz360.iot.model.site.po.SiteCtrlCfgLogPo;
import com.cdz360.iot.model.site.po.SiteCtrlCfgPo;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.DeviceStatusCodeType;
import com.cdz360.iot.model.type.SiteCtrlCmdType;
import com.cdz360.iot.biz.utils.GwRestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Classname SiteCtrlBizService
 * @Description
 * @Date 4/21/2020 2:35 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class SiteCtrlBizService {
    @Autowired
    private SiteCtrlRoDs siteCtrlRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private SiteCtrlRwDs siteCtrlRwDs;

    @Autowired
    private SiteCtrlCfgRoDs siteCtrlCfgRoDs;

    @Autowired
    private SiteCtrlCfgLogRwDs siteCtrlCfgLogRwDs;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private SequenceRwService sequenceRwService;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @Value("${env}")
    private String env;
    @Value("${iot.gw.mqtt.type}")
    private String mqttType;
    @Value("${iot.gw.mqtt.id}")
    private String mqttId;
    @Value("${iot.gw.mqtt.group}")
    private String mqttGroup;
    @Value("${iot.gw.mqtt.clientUrl}")
    private String mqttClientUrl;
    @Value("${iot.gw.mqtt.topicPrefix}")
    private String mqttTopicPrefix;
    @Value("${iot.gw.mqtt.lastWillTopic}")
    private String mqttLastWillTopic;
    @Value("${iot.gw.mqtt.accessKey}")
    private String mqttAccessKey;
    @Value("${iot.gw.mqtt.accessSecret}")
    private String mqttAccessSecret;
    @Value("${iot.gw.mqtt.username}")
    private String mqttUsername;
    @Value("${iot.gw.mqtt.password}")
    private String mqttPassword;

//    @Autowired
//    private DeviceMgMClient deviceMgMClient;

    @Autowired
    private IotCacheService iotCacheService;

    //    @Autowired
//    private LoginService loginService;
    @Autowired
    private SiteCtrlLoginService siteCtrlLoginService;

    @Autowired
    protected StringRedisTemplate redisTemplate;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private EvseProcessor evseProcessor;

//    @Autowired
//    private MqttService

    /**
     * 注册&登陆
     *
     * @param in
     * @return
     * @throws NoSuchAlgorithmException
     */
    public SiteCtrlRegisterResult doRegister(SiteCtrlReq<SiteCtrlRegisterReq> in) throws NoSuchAlgorithmException {

        SiteCtrlRegisterReq req = in.getData();

        IotAssert.isNotBlank(in.getCtrlNo(), "控制器编号不能为空");
        SiteCtrlPo siteCtrlPo = siteCtrlRoDs.selectByNum(in.getCtrlNo());
        IotAssert.isNotNull(siteCtrlPo, "未找到此控制器");

        if (StringUtils.isNotBlank(req.getMac())) {
            siteCtrlPo.setMac(req.getMac().toUpperCase());
        }
        if (StringUtils.isNotBlank(req.getLanIp())) {
            siteCtrlPo.setLanIp(req.getLanIp());
        }
        siteCtrlPo.setLoginTime(new Date());
        siteCtrlPo.setStatus(SiteCtrlStatusType.ONLINE);

        log.info("更新控制器信息: {}", siteCtrlRwDs.insertOrUpdate(siteCtrlPo));

        String clientId = MqttUtils.getMqttClientId(in.getCtrlNo(), this.mqttGroup, false);

        SiteCtrlRegisterResult ret = new SiteCtrlRegisterResult();
        ret.setN(siteCtrlPo.getNum());
        ret.setPassword(siteCtrlPo.getPasscode());
        ret.setMqClientId(clientId).setMqUrl(this.mqttClientUrl)
                .setMqTopic(MqttUtils.getMqTopic(in.getCtrlNo(), this.env, mqttTopicPrefix, false))
                .setMqttLwt(MqttUtils.getMqLastWillTopic(this.env, mqttTopicPrefix, mqttLastWillTopic, false));
        if (IotConstants.MQTT_TYPE_MOSQUITTO.equalsIgnoreCase(this.mqttType)) {
            ret.setMqUsername(this.mqttUsername)
                    .setMqPassword(this.mqttPassword);
        } else {
            ret.setMqUsername(MqttUtils.getMqttUsername(this.mqttId, this.mqttAccessKey))
                    .setMqPassword(MqttUtils.getMqttPasscode(clientId, this.mqttAccessSecret));
        }
        // 尝试更新设备信息
        String realm = this.iotCacheService.getOrUpdateRealm(in.getCtrlNo());//TODO mixin passcode
        ret.setPassword(realm);

        StringBuilder buf = new StringBuilder();
        buf.append(in.getCtrlNo()).append(":").append(realm);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(buf.toString().getBytes(StandardCharsets.UTF_8));
        String hashedToken = GwRestUtils.bytesToHex(md.digest());
        log.info("updateToken = {}", hashedToken);
        this.iotCacheService.updateToken(in.getCtrlNo(), hashedToken);

        return ret;
    }

    public SiteCtrlCfgRes getCfg(String ctrlNo) {
        log.info("获取场站控制器配置: ctrlNo = {}", ctrlNo);
        SiteCtrlCfgPo siteCtrlCfgPo = siteCtrlCfgRoDs.selectByNum(ctrlNo);
        SiteCtrlCfgRes siteCtrlCfgRes = new SiteCtrlCfgRes();
        IotAssert.isNotNull(siteCtrlCfgPo, "找不到控制器配置信息。");
        BeanUtils.copyProperties(siteCtrlCfgPo, siteCtrlCfgRes);

        SiteCtrlPo siteCtrlPo = new SiteCtrlPo();
        siteCtrlPo.setNum(ctrlNo).setStatus(SiteCtrlStatusType.WORK);
        siteCtrlRwDs.insertOrUpdate(siteCtrlPo);

        log.info("返回配置到控制器: {}", JsonUtils.toJsonString(siteCtrlCfgRes));
        return siteCtrlCfgRes;
    }

    public void updateCfgResult(SiteCtrlReq<SiteCtrlCfgResultReq> req) {

        IotAssert.isNotNull(req.getData(), "数据上报有误，上报结果不能为空");
        SiteCtrlCfgLogPo siteCtrlCfgLogPo = new SiteCtrlCfgLogPo();
        BeanUtils.copyProperties(req.getData(), siteCtrlCfgLogPo);
        siteCtrlCfgLogPo.setCtrlNum(req.getCtrlNo());

        switch (req.getData().getResult()) {
            case SUCCESS:
                siteCtrlCfgLogPo.setStatus(SiteCtrlCfgStatusType.SUCCESS);
                break;
            case FAIL:
                siteCtrlCfgLogPo.setStatus(SiteCtrlCfgStatusType.FAIL);
                break;
            case TIMEOUT:
                siteCtrlCfgLogPo.setStatus(SiteCtrlCfgStatusType.FAIL);
            default:
                siteCtrlCfgLogPo.setStatus(SiteCtrlCfgStatusType.FAIL);
        }

        log.info("更新了配置: {}", this.siteCtrlCfgLogRwDs.insertOrUpdate(siteCtrlCfgLogPo));
    }

    @Deprecated
    public SiteCtrlRegisterResult login(String ctrlNum, int gwVer, String token, String ip) throws NoSuchAlgorithmException {

        var tokenCache = this.iotCacheService.getSiteCtrlToken(ctrlNum);
        if (StringUtils.equalsIgnoreCase(token, tokenCache)) {
            // 如果token一致, 不需要更新
            //loginResult.set(new LoginRes());
            return this.buildLoginRes(ctrlNum, token);
        }
        String realm = this.iotCacheService.getOrUpdateRealm(ctrlNum);
        log.info("网关登录请求. gwno = {}, gw_token = {}, ip = {}, realm = {}, redis_token = {}", ctrlNum, token, ip, realm, tokenCache);

        //loginResult.set(new LoginRes());

        //String clientId = MqttUtils.getMqttClientId(gwno, this.mqttGroup);
        this.siteCtrlLoginService.doLogin(ctrlNum, gwVer, realm, token, ip);

        return this.buildLoginRes(ctrlNum, token);

    }

    @Deprecated
    private SiteCtrlRegisterResult buildLoginRes(String ctrlNum, String token) {
        SiteCtrlRegisterResult loginResult = new SiteCtrlRegisterResult();

        String clientId = MqttUtils.getMqttClientId(ctrlNum, this.mqttGroup, false);
        loginResult.setN(ctrlNum).
                setMqClientId(clientId).setMqUrl(this.mqttClientUrl)
//                .setMqUsername(MqttUtils.getMqttUsername(this.mqttId, this.mqttAccessKey))
//                .setMqPassword(MqttUtils.getMqttPasscode(clientId, this.mqttAccessSecret))
                .setMqTopic(MqttUtils.getMqTopic(ctrlNum, this.env, this.mqttTopicPrefix, false))
                .setMqttLwt(MqttUtils.getMqLastWillTopic(this.env, mqttTopicPrefix, mqttLastWillTopic, false));
        if (IotConstants.MQTT_TYPE_MOSQUITTO.equalsIgnoreCase(this.mqttType)) {
            loginResult.setMqUsername(this.mqttUsername)
                    .setMqPassword(this.mqttPassword);
        } else {
            loginResult.setMqUsername(MqttUtils.getMqttUsername(this.mqttId, this.mqttAccessKey))
                    .setMqPassword(MqttUtils.getMqttPasscode(clientId, this.mqttAccessSecret));
        }
        // 更新最后登陆时间
        this.siteCtrlRwDs.updateLoginTime(ctrlNum);
        // 更新网关登陆TOKEN缓存
        this.iotCacheService.updateToken(ctrlNum, token);
        return loginResult;
    }

    public void doStatus(SiteCtrlReq<SiteCtrlStatus> req) {
        IotAssert.isNotNull(req, "请求体不能为空");
        SiteCtrlStatus siteCtrlStatus = req.getData();
        IotAssert.isNotNull(siteCtrlStatus, "状态信息体不能为空");
        IotAssert.isNotNull(siteCtrlStatus.getCtrlStatus(), "状态不能为空");
        IotAssert.isNotBlank(req.getCtrlNo(), "控制器编号不能为空");

        final String LinkId = UUID.randomUUID().toString();

        SiteCtrlPo siteCtrlPo = siteCtrlRoDs.selectByNum(req.getCtrlNo());
        IotAssert.isNotNull(siteCtrlPo, "找不到控制器: " + req.getCtrlNo());
        IotAssert.isNotBlank(siteCtrlPo.getSiteId(), "控制器对应场站id不能为空");

        SitePo sitePo = siteRoDs.getSite(siteCtrlPo.getSiteId());
        IotAssert.isNotNull(sitePo, "找不到控制器对应的场站: " + sitePo.getSiteId());

        List<EvseReportRequestV2> evseAlertList = siteCtrlStatus.getAlertEVSEs();
        List<EvseReportRequestV2> evseErrorList = siteCtrlStatus.getErrorEVSEs();

        switch (siteCtrlStatus.getCtrlStatus()) {
            case ALERT:
                int alertCode = siteCtrlStatus.getAlertCode() == null ? 0 : siteCtrlStatus.getAlertCode();

                SiteCtrlVo siteCtrlVoAlert = new SiteCtrlVo();
                siteCtrlVoAlert.setCtrlNo(req.getCtrlNo()).setStatus(SiteCtrlStatusType.ALERT).setLoadRatio(siteCtrlStatus.getLoadRatio()).setPwrTemp(siteCtrlStatus.getPwrTemp()).setLinkId(LinkId).setSiteId(siteCtrlPo.getSiteId()).setSiteName(sitePo.getName()).setSiteCommId(sitePo.getCommId());//TODO 此处用商户id不用顶级商户id，可能有问题？？？

                if (alertCode == 0) {
                    // 正常
                }
                if ((alertCode & 0x01) > 0) {
                    // 负载告警
                    log.warn("负载告警");
                    siteCtrlVoAlert.setAlertCode(DeviceStatusCodeType.C2001.getCode());
                    dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoAlert);
                }
                if ((alertCode & 0x02) > 0) {
                    // 配电柜温度告警
                    log.warn("配电柜温度告警");
                    siteCtrlVoAlert.setAlertCode(DeviceStatusCodeType.C2002.getCode());
                    dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoAlert);
                }
                if ((alertCode & 0x04) > 0) {
                    //充电桩烟雾告警
                    //TODO 可能需要推送桩告警
                    log.warn("充电桩烟雾告警");

                }
                if ((alertCode & 0x08) > 0) {
                    //充电桩门禁告警
                    //TODO 可能需要推送桩告警
                    log.warn("充电桩门禁告警");

                }

                break;
            case ERROR:
                int errorCode = siteCtrlStatus.getErrorCode() == null ? 0 : siteCtrlStatus.getErrorCode();

                SiteCtrlVo siteCtrlVoError = new SiteCtrlVo();
                siteCtrlVoError.setCtrlNo(siteCtrlStatus.getCtrlNo()).setName(siteCtrlPo.getName()).setStatus(SiteCtrlStatusType.ERROR).setLoadRatio(siteCtrlStatus.getLoadRatio()).setPwrTemp(siteCtrlStatus.getPwrTemp()).setLinkId(LinkId).setSiteId(siteCtrlPo.getSiteId()).setSiteName(sitePo.getName()).setSiteCommId(sitePo.getCommId());//TODO 此处用商户id不用顶级商户id，可能有问题？？？

                if (errorCode == 0) {
                    // 正常
                }
                if ((errorCode & 0x01) > 0) {
                    log.warn("控制器配置信息异常");
                    siteCtrlVoError.setErrorCode(DeviceStatusCodeType.C2501.getCode());
                    dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoError);
                }
                if ((errorCode & 0x02) > 0) {
                    log.warn("负载异常");
                    siteCtrlVoError.setErrorCode(DeviceStatusCodeType.C2502.getCode());
                    dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoError);
                }
                if ((errorCode & 0x04) > 0) {
                    log.warn("配电柜温度异常");
                    siteCtrlVoError.setErrorCode(DeviceStatusCodeType.C2503.getCode());
                    dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoError);
                }
                if ((errorCode & 0x08) > 0) {
                    //TODO 可能需要推送桩异常
                    log.warn("充电桩功率输出异常");
                }
                if ((errorCode & 0x10) > 0) {
                    //TODO 可能需要推送桩异常
                    log.warn("充电桩离线异常");
                }
                break;
            case STARTUP:
            case WORK:
            case UPGRADE:
            case DEBUG:
                SiteCtrlVo siteCtrlVoStartUp = new SiteCtrlVo();
                siteCtrlVoStartUp.setCtrlNo(siteCtrlStatus.getCtrlNo()).setName(siteCtrlPo.getName()).setStatus(siteCtrlStatus.getCtrlStatus()).setLoadRatio(siteCtrlStatus.getLoadRatio()).setPwrTemp(siteCtrlStatus.getPwrTemp()).setLinkId(LinkId).setSiteId(siteCtrlPo.getSiteId()).setSiteName(sitePo.getName()).setSiteCommId(sitePo.getCommId());//TODO 此处用商户id不用顶级商户id，可能有问题？？？
                dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, siteCtrlVoStartUp);
                break;
            case OFFLINE:
            case ONLINE:
                log.error("目前控制器不会上传此种状态{}，请确认", req.getData().getCtrlStatus().name());
                break;
        }


        if (CollectionUtils.isNotEmpty(evseAlertList)) {
            List<EvseReportRequestV2> evseAlertConPlug = evseAlertList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getPlugs())).collect(Collectors.toList());
            evseAlertConPlug.stream().forEach(e -> {
                e.setLinkId(LinkId).setCtrlNo(req.getCtrlNo());
                evseProcessor.processSiteCtrlPlugReport(e, e.getPlugs(), siteCtrlPo, sitePo, true);
            });

            List<EvseReportRequestV2> evseAlertSinPlug = evseAlertList.stream().filter(e -> CollectionUtils.isEmpty(e.getPlugs())).collect(Collectors.toList());
            evseAlertSinPlug.stream().forEach(e -> e.setLinkId(LinkId).setCtrlNo(req.getCtrlNo()));
            evseProcessor.processSiteCtrlEvseReport(evseAlertSinPlug, siteCtrlPo, sitePo, true);
        }

        if (CollectionUtils.isNotEmpty(evseErrorList)) {
            List<EvseReportRequestV2> evseErrorConPlug = evseErrorList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getPlugs())).collect(Collectors.toList());
            evseErrorConPlug.stream().forEach(e -> {
                e.setLinkId(LinkId).setCtrlNo(req.getCtrlNo());
                evseProcessor.processSiteCtrlPlugReport(e, e.getPlugs(), siteCtrlPo, sitePo, false);
            });

            List<EvseReportRequestV2> evseErrorSinPlug = evseErrorList.stream().filter(e -> CollectionUtils.isEmpty(e.getPlugs())).collect(Collectors.toList());
            evseErrorSinPlug.stream().forEach(e -> e.setLinkId(LinkId).setCtrlNo(req.getCtrlNo()));
            evseProcessor.processSiteCtrlEvseReport(evseErrorSinPlug, siteCtrlPo, sitePo, false);
        }


        SiteCtrlPo param = new SiteCtrlPo();
        param.setNum(req.getCtrlNo()).setProtocolVer(req.getVer()).setStatus(req.getData().getCtrlStatus()).setSwVer(req.getData().getSwVer());
        siteCtrlRwDs.insertOrUpdate(param);
    }

    public void siteCtrlOffline(String ctrlNo) {
        IotAssert.isNotBlank(ctrlNo, "需要提供控制器编号");
        int i = siteCtrlRwDs.insertOrUpdate(new SiteCtrlPo().setNum(ctrlNo).setStatus(SiteCtrlStatusType.OFFLINE));
    }

    public void doReboot(String ctrlNum) {
        SiteCtrlPo po = siteCtrlRoDs.selectByNum(ctrlNum);
        IotAssert.isTrue(po != null && po.getEnable(), "控制器不存在");
        MqCtrlRebootCmd cmd = new MqCtrlRebootCmd();
        cmd.setV(po.getProtocolVer()).setSeq(this.sequenceRwService.getNextOutRequestSeq()).setN(ctrlNum).setData(new RebootTask());
        iotCacheService.removeToken(ctrlNum);
        mqttService.publishMessage(ctrlNum, false, cmd.toJsonString());
    }

    public void doCtrlCfg(String ctrlNum) {
        SiteCtrlPo po = siteCtrlRoDs.selectByNum(ctrlNum);
        IotAssert.isTrue(po != null && po.getEnable(), "控制器不存在");

        SiteCtrlCfgLogPo siteCtrlCfgLogPo = new SiteCtrlCfgLogPo();
        siteCtrlCfgLogPo.setCtrlNum(ctrlNum).setStatus(SiteCtrlCfgStatusType.SENDING);
        log.info("更新配置为SENDING: {}", siteCtrlCfgLogRwDs.insertOrUpdate(siteCtrlCfgLogPo));

        MqCtrlCfgCmd cmd = new MqCtrlCfgCmd();
        cmd.setV(po.getProtocolVer()).setSeq(this.sequenceRwService.getNextOutRequestSeq()).setN(ctrlNum);
        mqttService.publishMessage(ctrlNum, false, cmd.toJsonString());
    }

    /**
     * 云端获取控制器配置
     *
     * @param ctrlNum
     */
    public void cfgGet(String ctrlNum) {
        IotAssert.isNotBlank(ctrlNum, "请提供控制器编号");

        // 获取控制器的配置
        SiteCtrlPo po = siteCtrlRoDs.selectByNum(ctrlNum);
        IotAssert.isNotNull(po, "提供的控制器编号无效");

        // CTRL_GET_CFG
        MqCtrlGetCfgCmd cmd = new MqCtrlGetCfgCmd();
        cmd.setV(po.getProtocolVer());
        cmd.setSeq(sequenceRwService.getNextOutRequestSeq());
        cmd.setN(po.getNum());
        cmd.setCmd(SiteCtrlCmdType.CTRL_GET_CFG);

        mqttService.publishMessage(po.getNum(), false, JsonUtils.toJsonString(cmd));
    }

    /**
     * 控制器检测信息上报
     *
     * @param ctrlNo  控制器编号
     * @param monitor 检测数据
     */
    public void doMonitor(String ctrlNo, SiteCtrlMonitor monitor) {
        IotAssert.isNotBlank(ctrlNo, "控制器编号不能为空");

        // FIXME: 这期数据，负载率和配电柜温度取得最后一个最新的数据
        SiteCtrlPo po = siteCtrlRoDs.selectByNum(ctrlNo);
        IotAssert.isNotNull(po, "控制器编号无效(数据中查询不存在)");

        if (CollectionUtils.isNotEmpty(monitor.getLoadRatio())) {
            po.setLoadRatio(monitor.getLoadRatio().get(monitor.getLoadRatio().size() - 1));
        }

        if (CollectionUtils.isNotEmpty(monitor.getPwrTemp())) {
            po.setPwrTemp(monitor.getPwrTemp().get(monitor.getPwrTemp().size() - 1));
        }

        if (SiteCtrlStatusType.OFFLINE.equals(po.getStatus())) {
            log.info("当前控制器为离线，收到状态上报时修改状态为在线【ONLINE】");
            po.setStatus(SiteCtrlStatusType.ONLINE);
        } else {
            // 不修改原状态
            po.setStatus(null);
        }

        siteCtrlRwDs.insertOrUpdate(po);

        // TODO: 更新超时检测
        // 控制器配置信息
        SiteCtrlCfgPo cfgPo = siteCtrlCfgRoDs.selectByNum(ctrlNo);
        Integer timeout = 60; // 默认超时时间
//        if (cfgPo != null && cfgPo.getInfoUpLoop() != null) {
//            timeout = cfgPo.getInfoUpLoop();
//        }

        IotGwDownCmd downCmd = new IotGwDownCmd();
        downCmd.setTtl(timeout * 2) // 2 两倍周期
                .setMsg(ctrlNo).setCmd(null);
        this.dcEventPublish.publishIotGwDownCmd(downCmd);
    }

    /**
     * 控制器上报配置
     *
     * @param ctrlNo
     * @param data
     */
    public void cfgInfo(String ctrlNo, SiteCtrlInfo data) {
        IotAssert.isNotBlank(ctrlNo, "控制器编号不能为空");

        // 直接存入redis
        String key = SiteCtrlInfo.redisKey(ctrlNo);
        String value = JsonUtils.toJsonString(data);
        redisTemplate.opsForValue().set(key, value, SiteCtrlInfo.REDIS_DDL, TimeUnit.MINUTES);
    }

    /**
     * 检查场站控制器配置是否超出buffer时间(秒)，超出将设置为TIMEOUT
     *
     * @param bufferTime
     */
    public void cfgCheckTimeout(Integer bufferTime) {
        if (bufferTime == null) {
            bufferTime = 60;
        }

        log.info("检测发送中的控制器配置，并更新了{}个条目", siteCtrlCfgLogRwDs.checkTimeout(bufferTime));
    }
}