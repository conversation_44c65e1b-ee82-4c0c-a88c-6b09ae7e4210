package com.cdz360.iot.worker.cfg;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 百度api
 */
@ConfigurationProperties(prefix = "baidu")
public class BaiduProperties {

    /**
     * 账户应用对应的ak
     */
    private String ak;
    /**
     * ip转经纬度的api地址
     */
    private String ipLogLat;
    /**
     * 百度api参数
     */
    private String coor;

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getIpLogLat() {
        return ipLogLat;
    }

    public void setIpLogLat(String ipLogLat) {
        this.ipLogLat = ipLogLat;
    }

    public String getCoor() {
        return coor;
    }

    public void setCoor(String coor) {
        this.coor = coor;
    }


}