package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.EvseBundlePcQueryDs;
import com.cdz360.iot.ds.ro.EvseBundleQueryDs;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.rw.GwInfoRwDs;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.model.evse.EvseBundlePc;
import com.cdz360.iot.model.evse.type.BundleType;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.gw.GwMsg;
import com.cdz360.iot.model.gw.param.AuditMgcStatusParam;
import com.cdz360.iot.model.gw.param.UpgradeParam;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.worker.biz.south.MgcMqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MgcBizService {

    @Autowired
    private MgcMqService mgcMqService;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private GwInfoRwDs gwInfoRwDs;

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    @Autowired
    private EvseBundleQueryDs evseBundleQueryDs;

    @Autowired
    private EvseBundlePcQueryDs evseBundlePcQueryDs;


    /**
     * 仅用于测试环境
     */
    public Mono<BaseResponse> upgradeAllMgc(String jarPathIn) {

        List<GwInfoPo> gwInfoList = gwInfoRoDs.listGw(null, GwMqType.MQ_TYPE_MQTT, null, 0, 999);
        return Flux.fromIterable(gwInfoList)
                .doOnNext(gwInfo -> {
                    this.mgcMqService.sendUpgradeMqMsg(gwInfo, jarPathIn);
                })
                .collectList()
                .map(list -> RestUtils.success());
    }

    public Mono<BaseResponse> upgradeMgc(String gwnoIn, String jarPath) {
        return Mono.just(gwnoIn)
                .map(gwno -> {
                    GwInfoPo gwInfo = gwInfoRoDs.getByGwno(gwno);
                    this.mgcMqService.sendUpgradeMqMsg(gwInfo, jarPath);
                    return RestUtils.success();
                });
    }

    /**
     * 云端主动查询 MGC 状态
     */
    public Flux<BaseResponse> sendAuditStatusMqReq(AuditMgcStatusParam param) {
        return Flux.fromIterable(param.getGwnos())
                .map(gwno -> {
                    GwInfoPo gwInfo = gwInfoRoDs.getByGwno(gwno);
                    this.mgcMqService.sendAuditStatusMqReq(gwInfo);
                    return RestUtils.success();
                });
//        return Mono.just(gwnoIn)
//                .map(gwno -> {
//                    GwInfoPo gwInfo = gwInfoRoDs.getByGwno(gwno);
//                    this.mgcMqService.sendAuditStatusMqReq(gwInfo);
//                    return RestUtils.success();
//                });
    }


    public Mono<ObjectResponse<String>> findMgcGwnoByKeyword(String keyword) {
        return Mono.just(keyword)
                .map(k -> {
                    GwInfoPo gwInfo = gwInfoRoDs.getByKeyword(k);
                    if(gwInfo == null) {
                        return "";
                    } else {
                        return gwInfo.getGwno();
                    }
                })
                .map(RestUtils::buildObjectResponse);
    }

    public Mono<Integer> mgcUpgrade(UpgradeParam param) {
        return Mono.just(param)
                .doOnNext(p -> {
                    if (CollectionUtils.isEmpty(p.getGwnoList())) {
                        throw new DcArgumentException("请指定需要升级的控制编号");
                    }

                    if (null == p.getBundleId() || !(p.getBundleId() > 0)) {
                        throw new DcArgumentException("请选择有效的升级包");
                    }
                })
                .flatMap(p -> {
                    List<EvseBundlePc> bundlePcs = evseBundlePcQueryDs.selectByBundleId(p.getBundleId());
                    if (CollectionUtils.isEmpty(bundlePcs)) {
                        throw new DcArgumentException("升级包记录ID无效");
                    }

                    EvseBundlePc bundlePc = bundlePcs.get(0);
                    if (StringUtils.isBlank(bundlePc.getPath())) {
                        throw new DcArgumentException("升级包下载地址无效");
                    }

                    return Flux.fromIterable(p.getGwnoList())
                            .doOnNext(gwno -> {
                                GwInfoDto gw = gwInfoRwDs.getByGwno(gwno, true);
                                if (null != gw) {
                                    UpgradeLogPo upgradeLogPo = new UpgradeLogPo();
                                    upgradeLogPo.setUpgradeStatus(UpgradeStatus.CMD_SEND)
                                            .setBundleId(p.getBundleId())
                                            .setDownTime(new Date())
                                            .setBundleType(BundleType.MGC_SOFT)
                                            .setDeviceNo(gwno);
                                    upgradeLogRwDs.insertUpgradeLog(upgradeLogPo);

                                    gw.setExpectUpgradeLogId(upgradeLogPo.getId());
                                    gwInfoRwDs.update(this.dtoMap2Po(gw));

                                    this.upgradeMgc(gwno, "/" + bundlePc.getPath()).subscribe();

                                    // 超时检查
                                }
                            })
                            .collectList();
                })
                .map(i -> 1);
    }

    private GwInfoPo dtoMap2Po(GwInfoDto dto) {
        return new GwInfoPo()
                .setVer(dto.getVer())
                .setGwno(dto.getGwno())
                .setExpectUpgradeLogId(dto.getExpectUpgradeLogId());
    }

    public Mono<BaseResponse> mgcAuditStatusRes(GwMsg gwMsg) {
        return Mono.just(gwMsg)
                .map(msg -> {
                    log.info("mgc audit msg = {}", msg);
                    return RestUtils.success();
                });
    }
}
