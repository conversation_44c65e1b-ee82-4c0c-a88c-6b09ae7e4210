package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.EvseModuleDetailRoDs;
import com.cdz360.iot.ds.ro.EvseModuleRoDs;
import com.cdz360.iot.ds.rw.EvseBindHistoryRwDs;
import com.cdz360.iot.ds.rw.EvseModuleDetailRwDs;
import com.cdz360.iot.ds.rw.EvseModuleRwDs;
import com.cdz360.iot.ds.rw.SimRwDs;
import com.cdz360.iot.model.evse.param.ModifyEvseInfoParam;
import com.cdz360.iot.model.evse.po.EvseModuleDetailPo;
import com.cdz360.iot.model.evse.po.EvseModulePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.type.EvseDeviceType;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.SimDeviceType;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.EvseHistoryActionType;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.model.iot.Evse;
import com.cdz360.iot.worker.model.iot.UpdatePlugCacheDto;
import com.cdz360.iot.worker.model.iot.param.BindEvseParam;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseProcessorAsync {
    @Autowired
    private EvseModuleRoDs evseModuleRoDs;
    @Autowired
    private EvseModuleRwDs evseModuleRwDs;
    @Autowired
    private EvseModuleDetailRoDs evseModuleDetailRoDs;
    @Autowired
    private EvseModuleDetailRwDs evseModuleDetailRwDs;
    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private PlugService plugService;
    @Autowired
    private EvseBindHistoryRwDs evseBindHistoryRwDs;
    @Autowired
    private SimRwDs simRwDs;

    /**
     * 绑桩--后续异步操作
     * @param param
     * @param evse
     * @param site
     */
    @Async
    public void afterTheBindEvse2Site(BindEvseParam param,
                                      Evse evse,
                                      SitePo site) {
        log.info("afterTheBindEvse2Site param: {}, evse.plugs.size: {}", param,
                CollectionUtils.isNotEmpty(evse.getPlugs()) ? evse.getPlugs().size() : 0);
        if (param.getSlotNum() != null && StringUtils.isNotBlank(param.getModuleType())) {

            this.disposeEvseModule(param.getEvseNo(), param.getModuleType(), param.getSlotNum());
        }

        this.associateSim(evse);

        List<PlugPo> plugPos = evse.getPlugs();
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugList())) {
            plugPos = this.insertOrUpdatePlug(param.getEvseNo(), param.getPlugList(), evse.getPlugs());
        }

        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(plugPos)) {
            plugPos.forEach(plug -> {
//                PlugPo plug = this.plugService.getPlug(param.getEvseNo(), e.getPlugId(), false);
//                if (plug == null) {
//                    log.error("无法找到枪头信息. evseNo = {}, idx = {}", evse, e.getPlugId());
//                }
                UpdatePlugCacheDto dto = new UpdatePlugCacheDto();
                dto.setEvent(IotEvent.BIND)
                        .setPlugReport(null)
                        .setSite(site)
                        .setEvse(evse)
                        .setPlug(plug)
                        .setConstantCharge(evse.getConstantCharge());
                redisIotUpdateWrapper.updateRedisPlugCache(dto);
            });

            // 建立场站和桩,枪的关系数据缓存
            redisIotUpdateWrapper.bindEvse(param.getSiteId(), param.getEvseNo(),
                    plugPos.stream()
                            .map(p -> PlugNoUtils.formatPlugNo(evse.getEvseId(), p.getPlugId()))
                            .collect(Collectors.toList()));
        }

        evseBindHistoryRwDs.insertEvseAction(EvseHistoryActionType.BIND, param.getEvseNo(), param.getSiteId());
    }

    /**
     * 编辑桩--后续异步操作
     * @param param
     * @param evse
     * @param site
     */
    @Async
    public void afterTheUpdateEvseInfo(ModifyEvseInfoParam param,
                                       Evse evse,
                                       SitePo site) {
        log.info("evseNo: {}, evse.plugs.size: {}", evse.getEvseId(), evse.getPlugs().size());
        if (param.getSlotNum() != null && StringUtils.isNotBlank(param.getModuleType())) {

            this.disposeEvseModule(evse.getEvseId(), param.getModuleType(), param.getSlotNum());
        }

        this.associateSim(evse);

        List<PlugPo> plugPos = evse.getPlugs();
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugVoList())) {
            plugPos = this.insertOrUpdatePlug(evse.getEvseId(), param.getPlugVoList(), evse.getPlugs());
        }

        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(plugPos)) {
            plugPos.forEach(plug -> {
//                PlugPo plug = this.plugService.getPlug(evse.getEvseId(), e.getPlugId(), false);
//                if (plug == null) {
//                    logger.error("无法找到枪头信息. evseNo = {}, idx = {}", evse, e.getPlugId());
//                }
                UpdatePlugCacheDto dto = new UpdatePlugCacheDto();
                dto.setEvent(IotEvent.BIND)
                        .setPlugReport(null)
                        .setSite(site)
                        .setEvse(evse)
                        .setPlug(plug)
                        .setConstantCharge(evse.getConstantCharge());
                redisIotUpdateWrapper.updateRedisPlugCache(dto);
            });
        }
    }

    public void disposeEvseModule(String evseNo, String moduleType, Integer slotNum) {
        EvseModulePo update = new EvseModulePo();
        update.setEvseNo(evseNo)
                .setDeviceName(EvseDeviceType.GYZLCDMK.getDesc())
                .setModuleType(moduleType)
                .setNumber(slotNum);
        evseModuleRwDs.updateModuleTypeAndNumber(update);

        EvseModulePo evseModulePo = evseModuleRoDs.findChargingModule(evseNo);
        Long count = evseModuleDetailRoDs.countByCondition(evseModulePo.getId());
        if (slotNum > count) {
            List<EvseModuleDetailPo> poList = new ArrayList<>();
            for (int i = (count.intValue() + 1); i <= slotNum; i++) {
                EvseModuleDetailPo insert = new EvseModuleDetailPo();
                insert.setModuleId(evseModulePo.getId())
                        .setIdx(i);
                poList.add(insert);
            }
            evseModuleDetailRwDs.insert(poList);
        } else if (count > slotNum) {
            List<Long> idList = evseModuleDetailRoDs.findById(evseModulePo.getId())
                    .stream().sorted(Comparator.comparing(EvseModuleDetailPo::getId).reversed())
                    .limit(count - slotNum).map(EvseModuleDetailPo::getId).collect(Collectors.toList());
            evseModuleDetailRwDs.delete(idList);
        }

    }

    /**
     * 关联SIM卡
     *
     * @param evse
     */
    public void associateSim(Evse evse) {
        try {
            Optional.ofNullable(evse)
                .filter(x -> StringUtils.isNotBlank(x.getIccid()))
                .map(x -> {
                    SimPo po = new SimPo();
                    po.setIccid(x.getIccid())
                        .setSiteId(x.getSiteId())
                        .setDeviceType(SimDeviceType.EVSE);
                    return po;
                })
                .ifPresent(x -> {
                    simRwDs.updateByIccid(x);
                    log.info("关联结束");
                });
        } catch (Exception ex) {
            log.error("关联SIM卡失败 error: {}", ex.getMessage(), ex);
        }
    }

    public List<PlugPo> insertOrUpdatePlug(String evseNo, List<PlugPo> paramPlugList, List<PlugPo> originPlugList) {
        Map<String, PlugPo> map = new HashMap<>();
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(originPlugList)) {
            map = originPlugList.stream()
                    .collect(Collectors.toMap(e -> PlugNoUtils.formatPlugNo(e.getEvseId(), e.getPlugId()), o -> o));
            // 删除原有枪数据
            plugService.removeByEvseId(evseNo);
        }

        List<PlugPo> insertList = new ArrayList<>();

        Map<String, PlugPo> finalMap = map;
        paramPlugList.forEach(e -> {
            PlugPo originPlug = finalMap.get(PlugNoUtils.formatPlugNo(evseNo, e.getPlugId()));
            PlugPo newPo = new PlugPo();
            if (originPlug != null) {
                BeanUtils.copyProperties(originPlug, newPo);
                newPo.setName(e.getName());
            } else {
                newPo.setEvseId(evseNo)
                        .setPlugId(e.getPlugId())
                        .setPlugStatus(PlugStatus.OFFLINE)
                        .setName(e.getName());
            }
            insertList.add(newPo);
        });
        plugService.batchInsert(insertList);
        return insertList;
    }

}
