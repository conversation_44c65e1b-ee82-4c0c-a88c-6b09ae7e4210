package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.ds.ro.TransformerRoDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.model.gw.TransformerUpdateCmd;
import com.cdz360.iot.model.param.TransformerUpdateParam;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.worker.ds.service.EvseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname TransformerService
 * @Description
 * @Date 6/3/2021 3:54 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class TransformerService {

    @Autowired
    private MqService mqService;

    @Autowired
    private SequenceRwService sequenceRwService;

    @Autowired
    private TransformerRoDs transformerRoDs;

//    @Autowired
//    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private EvseService evseService;

    @Autowired
    private GwCacheService gwCacheService;

    /**
     * 下发更新消息
     *
     * @param param
     */
    public void sendUpdateMsg(TransformerUpdateParam param) {
        if (CollectionUtils.isNotEmpty(param.getGwnoList())) {
            for (String gwno : param.getGwnoList()) {
                // 直接放入MQ下发
                IotGwCmdCacheVo<TransformerUpdateCmd> cmd = new IotGwCmdCacheVo<>();

                TransformerUpdateCmd transformerUpdateCmd = new TransformerUpdateCmd() {{
                    setTransformerId(param.getTransformerId());
                }};
                cmd.setGwno(gwno)
                        .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                        .setCmd(IotGwCmdType2.CE_TRANSFORMER_UPDATE)
                        .setData(transformerUpdateCmd);
                GwInfoPo gwInfo = this.gwCacheService.getGwInfo(gwno);
                this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            }
        }
    }
}