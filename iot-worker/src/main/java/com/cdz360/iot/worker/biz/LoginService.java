package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.register.GwLoginResult;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.worker.model.iot.param.GwLoginParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;

@Service
public class LoginService {

    private final Logger logger = LoggerFactory.getLogger(LoginService.class);

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    @Autowired
    private GwInfoService gwInfoService;

    @Transactional
    public GwLoginResult doLogin(
            String realm,
            GwLoginParam param
    ) throws NoSuchAlgorithmException {
        logger.info("doLogin. gwno: {}, realm: {}, token: {}, wanIp: {}",
                param.getGwno(), realm, param.getToken(), param.getWanIp());

        Assert.isTrue(!StringUtils.isEmpty(param.getGwno()), "参数错误,网关编号不能为空");
        Assert.isTrue(!StringUtils.isEmpty(realm), "参数错误,realm不能为空");
        Assert.isTrue(!StringUtils.isEmpty(param.getToken()), "参数错误,token不能为空");
        GwInfoDto gwInfo = this.gwInfoService.getByGwno(param.getGwno(), true);
        Assert.notNull(gwInfo, "网关设备 " + param.getGwno() + " 不存在,请联系系统管理员添加");

        StringBuilder buf = new StringBuilder();
        buf.append(param.getGwno()).append(":").append(gwInfo.getPasscode()).append(":").append(realm);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(buf.toString().getBytes(StandardCharsets.UTF_8));
        String hashedToken = this.bytesToHex(md.digest());
        logger.info("inToken = {}, hashedToken = {}", param.getToken(), hashedToken);
        IotAssert.isTrue(hashedToken.equalsIgnoreCase(param.getToken()), new DcTokenException("网关编号或密码错误"));

        this.updateGwInfo4LoginSuccess(param, gwInfo);
        GwLoginResult result = new GwLoginResult();
        // TODO: 需要改为可配置
        result.setGwno(param.getGwno()).setHb(10).setT(List.of(0, 500, 1000));
        return result;
    }

    /**
     * 检查数据库中的网管信息跟登陆上报的是否一致，有不一致时修改数据库
     *
     * @param param
     * @param gwInfo
     */
    @Transactional
    public void updateGwInfo4LoginSuccess(GwLoginParam param, @Nullable GwInfoDto gwInfo) {
        logger.info("param = {}", param);
        if (gwInfo == null) {
            gwInfo = this.gwInfoService.getByGwno(param.getGwno(), true);
        }
        if (gwInfo == null) {
            return;
        }
        boolean changed = false;
        if (!StringUtils.isEmpty(param.getWanIp())
                && !gwInfo.getIp().equalsIgnoreCase(param.getWanIp())) {
            gwInfo.setIp(param.getWanIp());
            changed = true;
        }
        if (!StringUtils.isEmpty(param.getLanIp())
                && !gwInfo.getLanIp().equalsIgnoreCase(param.getLanIp())) {
            gwInfo.setLanIp(param.getLanIp());
            changed = true;
        }
        if (!StringUtils.isEmpty(param.getMac())
                && !gwInfo.getMac().equalsIgnoreCase(param.getMac())) {
            gwInfo.setMac(param.getMac());
            changed = true;
        }
        if (!Boolean.TRUE.equals(param.getSup())) { // 忽略 mgc supervisor 的如下登录信息
            if (param.getProtocolVer() > 0
                    && gwInfo.getVer() != param.getProtocolVer()) {
                gwInfo.setVer(param.getProtocolVer());
                changed = true;
            }
            if (param.getBootTime() != null) {
                if (gwInfo.getBootTime() == null) {
                    gwInfo.setBootTime(new Date(param.getBootTime() * 1000));
                    changed = true;
                } else if (gwInfo.getBootTime().getTime() / 1000 != param.getBootTime()) {
                    gwInfo.setBootTime(new Date(param.getBootTime() * 1000));
                    changed = true;
                }
            }
            if (StringUtils.isNotBlank(param.getSwVer())
                    && !StringUtils.equals(param.getSwVer(), gwInfo.getSwVer())) {
                gwInfo.setSwVer(param.getSwVer());
                changed = true;
            }
            if (param.getSwVerCode() != null
                    && !NumberUtils.equals(param.getSwVerCode(), gwInfo.getSwVerCode())) {
                gwInfo.setSwVerCode(param.getSwVerCode());
                changed = true;
            }
            if (StringUtils.isNotBlank(param.getGitCommitId())
                    && !StringUtils.equals(param.getGitCommitId(), gwInfo.getSourceCodeVer())) {
                gwInfo.setSourceCodeVer(param.getGitCommitId());

                gwInfo.setActualUpgradeLogId(gwInfo.getExpectUpgradeLogId());
                this.upgradeLog(gwInfo); // 升级日志
                changed = true;
            }
            if (gwInfo.getStatus() != GwStatus.NORMAL) {
                gwInfo.setStatus(GwStatus.NORMAL);
                changed = true;
            }
        }
        if (changed) {
            this.gwInfoService.update(gwInfo);
        }
    }

    private void upgradeLog(GwInfoDto gw) {
        UpgradeLogPo upgradeLogPo = upgradeLogRwDs.getById(gw.getExpectUpgradeLogId(), false);
        if (null != upgradeLogPo) {
            upgradeLogPo.setUpgradeStatus(UpgradeStatus.UPGRADE_SUCCESS);
            upgradeLogPo.setFinishTime(new Date());
            upgradeLogRwDs.updateUpgradeLog(upgradeLogPo);
        } else {
            logger.error("微网控制器中升级记录数据错误: gw = {}", gw);
//            throw new DcArgumentException("微网控制器中升级记录数据错误");
        }
    }

    private String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;

            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }

}
