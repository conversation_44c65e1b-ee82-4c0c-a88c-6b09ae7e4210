package com.cdz360.iot.worker.ds.service;

import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.worker.biz.RedisIotUpdateWrapper;
import com.cdz360.iot.worker.ds.mapper.PlugMapper;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrderService {
    private final Logger logger = LoggerFactory.getLogger(OrderService.class);


    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private PlugMapper plugMapper;

//    @Autowired
//    private DzBChargerOrderClient dcBChargerOrderClient;
    @Autowired
    private RedisIotReadService redisIotReadService;


    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;
//
//    //@Transactional
//    public Mono<OrderCreateResponseV2> tagOrderCreate(ChgEvseRequest orderReport) {
//        logger.info(">> 发起充电 orderReport = {}", orderReport);
//        String evseId = orderReport.getEvseId();
//        Integer plugId = orderReport.getPlugId();
//        //String orderNo = orderReport.getOrderNo();
//
//        IotAssert.isTrue(orderReport.getGwno() != null && orderReport.getGwno() != "", "请求参数Gwno为空");
//        IotAssert.isTrue(evseId != null && evseId != "", "请求参数EvseId为空");
//        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
//        //IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");
//
//
//        // 回写枪头正在充电中订单号
//        PlugVo plug = this.redisIotReadService.getPlugRedisCache(evseId, plugId);
//        //PlugPo plugPo = plugMapper.getPlug(evseId, plugId, false);
//        IotAssert.isNotNull(plug, "枪记录不存在，请检查");
//        logger.info("发起充电枪头信息 plug = {}", plug);
//        // 回写枪头正在充电中订单号
//        if (StringUtils.isNotBlank(plug.getOrderNo()) // && !orderNo.equals(plug.getOrderNo())
//        ) {
//            logger.info("桩发起充电异常订单。plug = {}", plug);
//            // 该枪可能存在异常订单
//            // BaseResponse res = dcBChargerOrderClient.handleErrorOrder(plug.getOrderNo(), null);
//            this.bizTradingReactiveFeignClient.handleErrorOrder(plug.getOrderNo(), null)
//                    .doOnNext(res -> {
//                        if (res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
//                            //IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, res.getError());
//                            logger.error("异常订单处理失败。OrderNo = {}, res = {}", plug.getOrderNo(), res);
//                        }
//                    })
//                    .subscribe();
//
//        }
//
//        orderReport.setEvseName(plug.getEvseName())
//                .setPlugName(plug.getName())
//                .setSiteId(plug.getSiteId())
//                .setSiteCommId(plug.getSiteCommId())
//                .setSupplyType(plug.getSupply())
//                .setPriceCode(plug.getPriceCode());
//
//        // 创建订单
//        logger.info("调用业务平台发起充电。orderReport: {}", orderReport);
//        //ObjectResponse<OrderCreateResponse> res = dcBChargerOrderClient.orderCreate(orderReport);
//
//        return bizTradingReactiveFeignClient.orderCreate(orderReport)
//                .doOnNext(resA -> {
//                    logger.info("调用业务平台发起充电。resA = {}", resA);
//                    //FeignResponseValidate.check(res);
//                    if (AuthConstants.BALANCE_INSUFFICIENT == resA.getStatus()) {
//                        throw new DcServerException(DcConstants.KEY_RES_CODE_BALANCE_ERROR, "余额不足创建订单失败");
//                    } else if (AuthConstants.KEY_RES_CODE_AUTH_FAIL == resA.getStatus()) {
//                        redisIotUpdateWrapper.unbindOrderNo(evseId, plugId);
//                        throw new DcServerException(AuthConstants.KEY_RES_CODE_AUTH_FAIL, "桩端开启充电失败：" + resA.getError());
//                    }
//                    IotAssert.isTrue(resA.getStatus() == 0, "桩端开启充电失败：" + resA.getError());
//
//                })
//                .doOnSuccess(resB -> {
//                    OrderCreateResponseV2 result = resB.getData();
//                    this.plugMapper.updateOrderNo(evseId, plugId, result.getOrderNo());
//                    redisIotUpdateWrapper.bindOrderNo(evseId, plugId, result.getOrderNo());
//                })
//                .map(resC -> resC.getData());
//
//    }
//
//
//    @Deprecated
//    public Mono<BaseResponse> tagOrderStarting(OrderStartingRequest orderReport) {
//        //logger.info(">> orderNo = {}", orderReport.getOrderNo());
//        String evseId = orderReport.getEvseId();
//        Integer plugId = orderReport.getPlugId();
//        String orderNo = orderReport.getOrderNo();
//
//        IotAssert.isTrue(orderReport.getGwno() != null && orderReport.getGwno() != "", "请求参数Gwno为空");
//        IotAssert.isTrue(evseId != null && evseId != "", "请求参数EvseId为空");
//        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
//
//        if (StringUtils.isBlank(orderNo)) {  // 如果网关没有上报订单号, 从枪头缓存中取
//            PlugVo plug = redisIotReadService.getPlugRedisCache(orderReport.getEvseId(), orderReport.getPlugId());
//            orderNo = plug == null ? null : plug.getOrderNo();
//            orderReport.setOrderNo(orderNo);
//        }
//        IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");
//
//        IotGwCmdResultStartCharge iotGwCmdResultStartCharge = new IotGwCmdResultStartCharge();
//        iotGwCmdResultStartCharge.setOrderNo(orderReport.getOrderNo());
//        iotGwCmdResultStartCharge.setEvseNo(
//                StringUtils.isBlank(orderReport.getEvseId()) ? orderReport.getEvseNo() : orderReport.getEvseId()
//        );
//        iotGwCmdResultStartCharge.setPlugId(orderReport.getPlugId());
//        iotGwCmdResultStartCharge.setResult(orderReport.getStartResult());
//        if (orderReport.getStartResult() != 0) {
//            logger.warn("启动充电失败. evseNo = {}, orderNo = {}", orderReport.getEvseId(), orderReport.getOrderNo());
//            // 清除枪头缓存的订单号
//            redisIotUpdateWrapper.unbindOrderNo(evseId, plugId);
//        }
//        //BaseResponse entity = this.dcBChargerOrderClient.orderStarting(iotGwCmdResultStartCharge);    // 无视返回的结果
//        return this.bizTradingReactiveFeignClient.orderStarting(iotGwCmdResultStartCharge)
//                .doOnNext(entity -> {
//                    logger.info("云端开始充电返回结果: {}", entity.toString());
//                });
//
//        //logger.info("<< ret = {}", true);
//        //return true;
//    }

//    public Mono<BaseResponse> tagOrderStart(OrderStartRequest orderReport) {
//        logger.info(">> orderNo = {}", orderReport.getOrderNo());
//
//        IotAssert.isTrue(orderReport.getGwno() != null && orderReport.getGwno() != "", "请求参数Gwno为空");
//        IotAssert.isTrue(orderReport.getEvseId() != null && orderReport.getEvseId() != "", "请求参数EvseId为空");
//        IotAssert.isTrue(orderReport.getPlugId() != null && orderReport.getPlugId() >= 0, "请求参数PlugId为空");
//        IotAssert.isTrue(orderReport.getOrderNo() != null && orderReport.getOrderNo() != "", "请求参数OrderNo为空");
//
//
//        //BaseResponse entity = this.dcBChargerOrderClient.orderStart(orderReport);
//        //IotAssert.isTrue(entity.getStatus() == 0, entity.getError());
//       return  this.bizTradingReactiveFeignClient.orderStart(orderReport)
//                .doOnNext(entity -> {
//                    logger.info("云端开始充电返回结果: {}", entity.toString());
//                });
//
//
//        //logger.info("<< ret = {}", true);
//
//    }

//    public Mono<BaseResponse> tagOrderUpdate(OrderUpdateRequestV2 orderReport) {
//        logger.info(">> orderNo = {}", orderReport.getOrderNo());
//        IotAssert.isTrue(orderReport.getEvseNo() != null && orderReport.getEvseNo() != "", "请求参数EvseNo为空");
//        IotAssert.isTrue(orderReport.getPlugId() != null && orderReport.getPlugId() >= 0, "请求参数PlugId为空");
//        IotAssert.isTrue(orderReport.getOrderNo() != null && orderReport.getOrderNo() != "", "请求参数OrderNo为空");
//
//       return this.bizTradingReactiveFeignClient.orderUpdate(orderReport)
//                .doOnNext(entity -> {
//                    logger.info("云端充电过程数据上报返回结果: {}", entity);
//                    if (entity.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
//                        PlugVo plug = this.redisIotReadService.getPlugRedisCache(orderReport.getEvseNo(), orderReport.getPlugId());
//                        if ((plug.getStatus() == PlugStatus.OFFLINE
//                                || plug.getStatus() == PlugStatus.ERROR
//                                || plug.getStatus() == PlugStatus.BUSY)
//                                && StringUtils.isBlank(plug.getOrderNo())) {
//                            logger.info("重新将订单号 {} 绑定到枪头 {}", orderReport.getOrderNo(), plug.getPlugNo());
//                            redisIotUpdateWrapper.bindOrderNo(orderReport.getEvseNo(), orderReport.getPlugId(), orderReport.getOrderNo());
//                        }
//                    }
//                });
//
//    }

//    public void tagOrderStop(OrderStopRequest orderReport) {
//        logger.info(">> orderNo = {}", orderReport.getOrderNo());
//        String evseId = orderReport.getEvseId();
//        Integer plugId = orderReport.getPlugId();
//        String orderNo = orderReport.getOrderNo();
//
//        IotAssert.isTrue(orderReport.getGwno() != null && orderReport.getGwno() != "", "请求参数Gwno为空");
//        IotAssert.isTrue(evseId != null && evseId != "", "请求参数EvseId为空");
//        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
//        IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");
//
//        // 对象转换v1和v2版本兼容
//        OrderStopRequestV2 stopRequestV2 = this.convertToOrderStopReqV2(orderReport);
//
//        BaseResponse entity = dcBChargerOrderClient.orderStop(stopRequestV2);
//        IotAssert.isTrue(entity.getStatus() == 0, entity.getError());
//        logger.info("云端停止充电返回结果: {}", entity.toString());
//        PlugPo plugPo = plugMapper.getPlug(evseId, plugId, false);
//
//        IotAssert.isNotNull(plugPo, "枪记录不存在，请检查");
//        if (StringUtils.isNotBlank(plugPo.getOrderNo()) && orderNo.equals(plugPo.getOrderNo())) {
//            logger.info("停止充电清空订单。订单号 = {},plugPo = {}", orderNo, plugPo);
//            this.plugMapper.updateOrderNo(evseId, plugId, null);
//
//            // 清除枪头上绑定的订单号
//            // NOTE: 充电完成后不立即清除枪头订单号，待到枪头变为空闲时再做清除
//            // this.redisIotUpdateWrapper.unbindOrderNo(evseId, plugPo.getPlugId());
//        }
//
//        logger.info("<< ret = {}", true);
//        return true;
//    }
//
//    public Mono<BaseResponse> tagOrderStopV2(GwObjReqMsg<OrderStopRequestV2> orderReport) {
//        logger.info(">> orderNo = {}", orderReport.getData().getEvseNo());
//        //TODO raf V2
//        String evseNo = orderReport.getData().getEvseNo();
//        Integer plugId = orderReport.getData().getPlugId();
//        String orderNo = orderReport.getData().getOrderNo();
//
//        IotAssert.isTrue(orderReport.getGwno() != null && orderReport.getGwno() != "", "请求参数Gwno为空");
//        IotAssert.isNotBlank(evseNo, "请求参数EvseId为空");
//        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
//        IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");
//
//        return this.bizTradingReactiveFeignClient.orderStop(orderReport.getData())
//                .doOnNext(entity -> {
//                    logger.info("云端停止充电返回结果: {}", entity.toString());
//                    IotAssert.isTrue(entity.getStatus() == 0, entity.getError());
//                })
//                .doOnSuccess(res -> {
//                    PlugPo plugPo = plugMapper.getPlug(evseNo, plugId, false);
//
//                    IotAssert.isNotNull(plugPo, "枪记录不存在，请检查");
//                    if (StringUtils.isNotBlank(plugPo.getOrderNo()) && orderNo.equals(plugPo.getOrderNo())) {
//                        logger.info("停止充电清空订单。订单号 = {},plugPo = {}", orderNo, plugPo);
//                        this.plugMapper.updateOrderNo(evseNo, plugId, null);
//                    }
//                });
//
//    }


//    public Mono<OrderFeeRefreshResponseV2> orderFeeRenewalV2(GwObjReqMsg<OrderFeeRefreshRequestV2> orderFeeRefreshReq) {
//        //TODO raf V2
//        //ObjectResponse<OrderFeeRefreshResponseV2> res = dcBChargerOrderClient.orderFeeRefresh(orderFeeRefreshReq.getData());
//
//        return this.bizTradingReactiveFeignClient.orderFeeRefresh(orderFeeRefreshReq.getData())
//                .doOnNext(res -> {
//                    IotAssert.isNotNull(res, "请求续费失败");
//                    IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, res.getError());
//                    IotAssert.isNotNull(res.getData(), "请求续费失败");
//                })
//                .map(res2 -> res2.getData());
//        // return res.getData();
//    }

//    /**
//     * @Description: OrderStopRequestV1转换为OrderStopRequestV2
//     * @Author: JLei
//     * @CreateDate: 17:19 2019/11/4
//     */
//    private OrderStopRequestV2 convertToOrderStopReqV2(OrderStopRequest stopReqV1) {
//        OrderStopRequestV2 stopReqV2 = new OrderStopRequestV2();
//        BeanUtils.copyProperties(stopReqV1, stopReqV2);
//        stopReqV2.setEvseNo(stopReqV1.getEvseId());
//        if (stopReqV1.getKwh() != null) {
//            // 0.0001kwh转位kwh
//            stopReqV2.setKwh(DecimalUtils.divide100(stopReqV1.getKwh()));
//        }
//        if (stopReqV1.getServFee() != null) {
//            // 分转位元
//            stopReqV2.setServFee(DecimalUtils.divide100(stopReqV1.getServFee()));
//        }
//        if (stopReqV1.getElecFee() != null) {
//            // 分转位元
//            stopReqV2.setElecFee(DecimalUtils.divide100(stopReqV1.getElecFee()));
//        }
//        if (stopReqV1.getStartMeter() != null) {
//            // 0.01kwh转位kwh
//            stopReqV2.setStartMeter(DecimalUtils.divide100(stopReqV1.getStartMeter()));
//        }
//        if (stopReqV1.getStopMeter() != null) {
//            // 0.01kwh转位kwh
//            stopReqV2.setStopMeter(DecimalUtils.divide100(stopReqV1.getStopMeter()));
//        }
//        List<OrderDetail> detailList = stopReqV1.getDetail();
//        List<OrderDetailV2> v2DetailList = null;
//        if (CollectionUtils.isNotEmpty(detailList)) {
//            v2DetailList = detailList.stream()
//                    .map(orderDetail -> {
//                        OrderDetailV2 orderDetailV2 = new OrderDetailV2();
//                        BeanUtils.copyProperties(orderDetail, orderDetailV2);
//                        BigDecimal kwh = orderDetail.getKwh();
//                        BigDecimal elecFee = orderDetail.getElecFee();
//                        BigDecimal servFee = orderDetail.getServFee();
//                        if (kwh != null) {
//                            // 0.0001kwh转位kwh
//                            orderDetailV2.setKwh(kwh);
//                        }
//                        if (elecFee != null) {
//                            // 分转位元
//                            orderDetailV2.setElecFee(elecFee);
//                        }
//                        if (servFee != null) {
//                            // 分转位元
//                            orderDetailV2.setServFee(servFee);
//                        }
//                        return orderDetailV2;
//                    }).collect(Collectors.toList());
//        }
//        stopReqV2.setDetail(v2DetailList);
//        return stopReqV2;
//    }
}
