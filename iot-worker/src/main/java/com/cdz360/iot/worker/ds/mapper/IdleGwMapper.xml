<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.worker.ds.mapper.IdleGwMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.iot.model.site.po.IdleGwPo">
        insert into
        t_idle_gw(gwno, ip, enable,
        createTime, updateTime, lon, lat, cityCode
        )
        values(#{gwno}, #{ip}, #{enable},
        now(), now(), #{lon}, #{lat}, #{cityCode}
        )
    </insert>

    <update id="update" parameterType="com.cdz360.iot.model.site.po.IdleGwPo">
        update t_idle_gw set gwno=#{gwno},
        ip=#{ip},
        enable=#{enable},
        lon=#{lon},
        lat=#{lat},
        cityCode=#{cityCode},
        updateTime=now()
        where id=#{id}
    </update>

    <update id="delete" parameterType="long">
        update t_idle_gw
        set
        enable=0,
        updateTime=now()
        where id=#{id}
    </update>

    <select id="getByIp" resultType="com.cdz360.iot.model.site.po.IdleGwPo">
        select id,gwno,passcode,ip,lon,lat,cityCode,enable,createTime,updateTime from t_idle_gw where ip=#{ip}
        <if test="lock == true">
            for update
        </if>
    </select>


</mapper>