package com.cdz360.iot.worker.biz.south;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.rw.OfflineCardLogRwDs;
import com.cdz360.iot.model.auth.CusAuthReqEx;
import com.cdz360.iot.model.auth.CusAuthRes;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.base.OrderCreateResponseV2;
import com.cdz360.iot.model.constant.AuthConstants;
import com.cdz360.iot.model.evse.ChgEvseRequest;
import com.cdz360.iot.model.evse.ChgEvseRequestV2;
import com.cdz360.iot.model.evse.OfflineCardLogReq;
import com.cdz360.iot.model.evse.OrderFeeRefreshRequestV2;
import com.cdz360.iot.model.evse.OrderFeeRefreshResponseV2;
import com.cdz360.iot.model.evse.OrderStartRequest;
import com.cdz360.iot.model.evse.OrderStartRequestV2;
import com.cdz360.iot.model.evse.OrderStartingRequest;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.evse.OrderUpdateRequestV2;
import com.cdz360.iot.model.evse.po.OfflineCardLogPo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.type.StopMode;
import com.cdz360.iot.worker.biz.RedisIotUpdateWrapper;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import com.cdz360.iot.worker.feign.BizUserFeignClient;
import com.cdz360.iot.worker.feign.IotParkFeignClient;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultStartCharge;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


/**
 * 桩端订单处理服务
 */
@Slf4j
@Service
public class OrderSouthBizService {


    @Autowired
    private BizUserFeignClient dzUserFeignClient;

    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private PlugService plugService;

    @Autowired
    private OfflineCardLogRwDs offlineCardLogRwDs;

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;

    @Autowired
    private IotParkFeignClient iotParkFeignClient;

    @Autowired
    private EvseModelRoDs evseModelRoDs;

    /**
     * 桩端发起 卡 / VIN 鉴权
     * 鉴权成功会缓存[卡号,余额]到redis
     *
     * @param cusAuthReq
     * @return
     */
    public Mono<CusAuthRes> auth(CusAuthReqEx cusAuthReq) {
        log.info("鉴权卡或vin码的鉴权请求:{}", JsonUtils.toJsonString(cusAuthReq));

        //ObjectResponse<CusAuthRes> authRes =
        return dzUserFeignClient.auth("", cusAuthReq)
                .doOnNext(res1 -> {
                    log.info("鉴权卡或vin码的鉴权结果:{}", res1);

                    if (res1.getStatus() == AuthConstants.BALANCE_INSUFFICIENT) {
                        throw new DcServerException(DcConstants.KEY_RES_CODE_BALANCE_ERROR, "鉴权失败，可用额度不足。");
                    } else if (res1.getStatus() == DcConstants.KEY_RES_CODE_AUTH_FAIL) {
                        throw new DcServerException(DcConstants.KEY_RES_CODE_AUTH_FAIL, "鉴权失败(没有充电权限)");
                    } else if (res1.getStatus() == DcConstants.KEY_RES_CODE_ACCOUNT_ERROR) {
                        throw new DcServerException(DcConstants.KEY_RES_CODE_ACCOUNT_ERROR, "通用账号异常");
                    }

                    IotAssert.isTrue(res1.getStatus() == 0, res1.getError());
                })
                .map(res2 -> res2.getData());


//        return authRes.getData();

    }

    public Mono<BaseGwResponse> addCardLog(OfflineCardLogReq req, String gwno) {
        OfflineCardLogPo po = new OfflineCardLogPo();
        po.setCardNo(req.getCardNo())
                .setEvseNo(req.getEvseNo())
                .setAmount(req.getAmount());
        return Mono.just(po)
                .doOnNext(cardLog -> {
                    this.offlineCardLogRwDs.addOfflineCardLog(cardLog);
                })
                .map(cardLog -> new BaseGwResponse());
    }


    /**
     * 桩端发起(卡/VIN)充电
     *
     * @param json
     * @param gwno
     * @return
     */
    public Mono<OrderCreateResponseV2> createOrder(ChgEvseRequestV2 json, String gwno) {
        log.debug("桩端发起充电请求。 json = {}", json);

        StopMode stopMode = json.getStopMode();

        ChgEvseRequest chgEvseReq = new ChgEvseRequest();
        chgEvseReq.setStartType(json.getStartType()).setAccountNo(json.getAccountNo())
                .setStopMode(stopMode)
                .setEvseNo(json.getEvseNo())
                .setEvseNo(json.getEvseNo())
                .setPlugNo(PlugNoUtils.formatPlugNo(json.getEvseNo(), json.getPlugId()))
                .setPlugId(json.getPlugId()).setGwno(gwno);
        log.debug("桩端发起充电请求。 chgEvseReq = {}", chgEvseReq);

        log.info(">> 发起充电 orderReport = {}", chgEvseReq);
        String evseNo = chgEvseReq.getEvseNo();
        Integer plugId = chgEvseReq.getPlugId();
        //String orderNo = orderReport.getOrderNo();

        IotAssert.isTrue(chgEvseReq.getGwno() != null && chgEvseReq.getGwno() != "", "请求参数Gwno为空");
        IotAssert.isTrue(evseNo != null && evseNo != "", "请求参数evseNo为空");
        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
        //IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");


        Mono<Boolean> mono = Mono.just(Boolean.TRUE);
        EvseVo evseVo = this.redisIotReadService.getEvseRedisCache(evseNo);
        IotAssert.isNotNull(evseVo, "桩记录不存在，请检查");
        if (EvseProtocolType.ACREL == evseVo.getProtocol()) {

            IotAssert.isNotNull(evseVo.getSiteId(), "桩未绑定场站");
            IotAssert.isNotNull(evseVo.getPriceCode(), "桩未下发计费");

            chgEvseReq.setEvseName(evseVo.getName())
//                    .setPlugName(plug.getName())
                    .setSiteId(evseVo.getSiteId())
                    .setSiteCommId(evseVo.getSiteCommId())
                    .setSupplyType(evseVo.getSupplyType())
                    .setPriceCode(evseVo.getPriceCode());

        } else {

            // 回写枪头正在充电中订单号
            PlugVo plug = this.redisIotReadService.getPlugRedisCache(evseNo, plugId);
            //PlugPo plugPo = plugMapper.getPlug(evseId, plugId, false);
            IotAssert.isNotNull(plug, "枪记录不存在，请检查");
            log.info("发起充电枪头信息 plug = {}", plug);
            // 回写枪头正在充电中订单号
            if (StringUtils.isNotBlank(plug.getOrderNo()) // && !orderNo.equals(plug.getOrderNo())
            ) {
                log.info("桩发起充电异常订单。plug = {}", plug);
                // 该枪可能存在异常订单
                // BaseResponse res = dcBChargerOrderClient.handleErrorOrder(plug.getOrderNo(), null);
                mono = this.bizTradingReactiveFeignClient.handleErrorOrder(plug.getOrderNo(), null)
                        .doOnNext(res -> {
                            if (res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                                //IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, res.getError());
                                log.error("异常订单处理失败。OrderNo = {}, res = {}", plug.getOrderNo(), res);
                            }
                        })
                        .map(res -> Boolean.TRUE);
//                    .subscribe();

            }

            chgEvseReq.setEvseName(plug.getEvseName())
                    .setPlugName(plug.getName())
                    .setSiteId(plug.getSiteId())
                    .setSiteCommId(plug.getSiteCommId())
                    .setSupplyType(plug.getSupply())
                    .setPriceCode(plug.getPriceCode());

        }

        // 创建订单
        log.info("调用业务平台发起充电。orderReport: {}", JsonUtils.toJsonString(chgEvseReq));
        //ObjectResponse<OrderCreateResponse> res = dcBChargerOrderClient.orderCreate(orderReport);

        return mono.flatMap(a -> bizTradingReactiveFeignClient.orderCreate(chgEvseReq))
                .doOnNext(resA -> {
                    log.info("调用trading发起充电响应: resA = {}", resA);
                    //FeignResponseValidate.check(res);
                    if (AuthConstants.BALANCE_INSUFFICIENT == resA.getStatus()) {
                        throw new DcServerException(DcConstants.KEY_RES_CODE_BALANCE_ERROR, "余额不足创建订单失败");
                    } else if (AuthConstants.KEY_RES_CODE_AUTH_FAIL == resA.getStatus()) {
                        redisIotUpdateWrapper.unbindOrderNo(evseNo, plugId);
                        throw new DcServerException(AuthConstants.KEY_RES_CODE_AUTH_FAIL,
                                "桩端开启充电失败：" + resA.getError());
                    } else if (AuthConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_UNBOUND == resA.getStatus()) {
                        throw new DcServerException(AuthConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_UNBOUND,
                                "桩端开启充电失败：" + resA.getError());
                    } else if (AuthConstants.KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL == resA.getStatus()) {
                        throw new DcServerException(AuthConstants.KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL,
                                "桩端开启充电失败：" + resA.getError());
                    }
                    IotAssert.isTrue(resA.getStatus() == 0, "桩端开启充电失败：" + resA.getError());

                })
                .doOnSuccess(resB -> {
                    OrderCreateResponseV2 result = resB.getData();
                    if (EvseProtocolType.ACREL == evseVo.getProtocol()) {
                        // 在order/start时写入
                    } else {
                        this.plugService.updateOrderNo(evseNo, plugId, result.getOrderNo());
                        redisIotUpdateWrapper.bindOrderNo(evseNo, plugId, result.getOrderNo());
                    }
                })
                .map(resC -> {
                    OrderCreateResponseV2 ret = resC.getData();
                    ret.setCalcType("MONEY");
                    return ret;
                });

    }

    /**
     * 充电指令下发结果上报
     *
     * @return
     */
    public Mono<BaseGwResponse> orderStarting(OrderStartingRequest orderReq) {
        log.debug(">> orderReq = {}", orderReq);
        IotAssert.isNotNull(orderReq.getGwno(), "JSON 缺少gwno");
        IotAssert.isNotNull(orderReq.getSeq(), "JSON 缺少seq");

        //logger.info(">> orderNo = {}", orderReport.getOrderNo());
        String evseId = orderReq.getEvseNo();
        Integer plugId = orderReq.getPlugId();
        String orderNo = orderReq.getOrderNo();

        IotAssert.isTrue(orderReq.getGwno() != null && orderReq.getGwno() != "", "请求参数Gwno为空");
        IotAssert.isTrue(evseId != null && evseId != "", "请求参数EvseId为空");
        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");

        if (StringUtils.isBlank(orderNo)) {  // 如果网关没有上报订单号, 从枪头缓存中取
            PlugVo plug = redisIotReadService.getPlugRedisCache(orderReq.getEvseNo(), orderReq.getPlugId());
            orderNo = plug == null ? null : plug.getOrderNo();
            orderReq.setOrderNo(orderNo);
        }
        IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");

        IotGwCmdResultStartCharge iotGwCmdResultStartCharge = new IotGwCmdResultStartCharge();
        iotGwCmdResultStartCharge.setOrderNo(orderReq.getOrderNo());
        iotGwCmdResultStartCharge.setEvseNo(
                // StringUtils.isBlank(orderReq.getEvseId()) ?
                orderReq.getEvseNo()
                //: orderReq.getEvseId()
        );
        iotGwCmdResultStartCharge.setPlugId(orderReq.getPlugId());
        iotGwCmdResultStartCharge.setResult(orderReq.getStartResult());
        iotGwCmdResultStartCharge.setPlugNo(PlugNoUtils.formatPlugNo(evseId, plugId));
        if (orderReq.getStartResult() != 0) {
            log.warn("启动充电失败. evseNo = {}, orderNo = {}", orderReq.getEvseNo(), orderReq.getOrderNo());
            // 清除枪头缓存的订单号
            redisIotUpdateWrapper.unbindOrderNo(evseId, plugId);
        }
        //BaseResponse entity = this.dcBChargerOrderClient.orderStarting(iotGwCmdResultStartCharge);    // 无视返回的结果
        return this.bizTradingReactiveFeignClient.orderStarting(iotGwCmdResultStartCharge)
                .doOnNext(entity -> {
                    log.info("云端开始充电返回结果: {}", entity.toString());
                    if (entity.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.error("上报启动充电失败!!! orderNo = {}, res = {}",
                                orderReq.getOrderNo(), entity);
                    }
                })
                .map(res1 -> {

                    BaseGwResponse res = new BaseGwResponse();
                    res.setSeq(orderReq.getSeq());
                    return res;
                });

//        return this.dzBcChargeOrderService.orderStarting(json)
//                ;


    }


    /**
     * 桩端上报充电开始
     *
     * @param orderStartReq
     * @return
     */
    public Mono<BaseGwResponse> orderStarted(GwObjReqMsg<OrderStartRequestV2> orderStartReq) {
        log.debug(">> evseNo = {}, orderNo = {}",
                orderStartReq.getData().getEvseNo(), orderStartReq.getData().getOrderNo());
        OrderStartType startType = orderStartReq.getData().getStartType();
        EvseVo evseVo = this.redisIotReadService.getEvseRedisCache(orderStartReq.getData().getEvseNo());
        IotAssert.isNotNull(evseVo, "桩记录不存在，请检查");
        if (StringUtils.isBlank(evseVo.getSiteId())) {
            if (OrderStartType.EVSE_AUTO == startType || OrderStartType.OFFLINE_CARD == startType) {
                log.warn("桩未绑定到场站. evseNo = {}, order = {}", evseVo.getEvseNo(), orderStartReq.getData());
            } else {
                log.error("桩未绑定到场站. evseNo = {}, order = {}", evseVo.getEvseNo(), orderStartReq.getData());
            }
        }
        OrderStartRequest orderStartRequest = new OrderStartRequest();
        BeanUtils.copyProperties(orderStartReq, orderStartRequest);
        orderStartRequest.setEvseNo(orderStartReq.getData().getEvseNo()).setGwno(orderStartReq.getGwno());
        orderStartRequest.setPlugId(orderStartReq.getData().getPlugId())
                .setPlugNo(PlugNoUtils.formatPlugNo(orderStartReq.getData().getEvseNo(), orderStartReq.getData().getPlugId()))
                .setOrderNo(orderStartReq.getData().getOrderNo());
        orderStartRequest.setStartTime(orderStartReq.getData().getStartTime());
        orderStartRequest.setStartType(orderStartReq.getData().getStartType());
        orderStartRequest.setSoc(orderStartReq.getData().getSoc());
        orderStartRequest.setAccountNo(orderStartReq.getData().getAccountNo());
        orderStartRequest.setVin(orderStartReq.getData().getVin());
        orderStartRequest.setCalcType(orderStartReq.getData().getCalcType());
        if (orderStartReq.getData().getStopMode().getAmount() != null) {
            orderStartRequest.setStopMode(new StopMode().setType(orderStartReq.getData().getStopMode().getType())
                    .setAmount(orderStartReq.getData().getStopMode().getAmount()));
        } else {
            orderStartRequest.setStopMode(new StopMode().setType(orderStartReq.getData().getStopMode().getType()));
        }
        if (orderStartReq.getData().getInsulation() != null) {
            orderStartRequest.setInsulation(new OrderStartRequest.Insulation());
            orderStartRequest.getInsulation().setResult(orderStartReq.getData().getInsulation().getResult())
                    .setPositive(orderStartReq.getData().getInsulation().getPositive())
                    .setNegative(orderStartReq.getData().getInsulation().getNegative())
                    .setVoltage(orderStartReq.getData().getInsulation().getVoltage());
        }
        orderStartRequest.setBms(orderStartReq.getData().getBms())
                .setBattery(orderStartReq.getData().getBattery())
                .setStartResult(orderStartReq.getData().getStartResult())
        ;

        if (orderStartRequest.getStartType() == OrderStartType.EVSE_AUTO
                || orderStartRequest.getStartType() == OrderStartType.EVSE_OFFLINE_CARD
                || orderStartRequest.getStartType() == OrderStartType.OFFLINE_CARD) {
            // 桩端启动的订单要把订单号写入到redis
            PlugVo plug = redisIotReadService.getPlugRedisCache(orderStartRequest.getPlugNo());
            if (plug != null && !StringUtils.equals(plug.getOrderNo(), orderStartRequest.getOrderNo())) {
                log.info("绑定枪头订单号. plugNo = {}, orderNo = {}",
                        orderStartRequest.getPlugNo(), orderStartRequest.getOrderNo());
                redisIotUpdateWrapper.bindOrderNo(orderStartReq.getData().getEvseNo(), orderStartReq.getData().getPlugId(),
                        orderStartRequest.getOrderNo());
            }
        }

        log.info("param = {}", orderStartRequest);
        //BaseResponse entity = this.dcBChargerOrderClient.orderStart(orderReport);
        //IotAssert.isTrue(entity.getStatus() == 0, entity.getError());
        return this.bizTradingReactiveFeignClient.orderStart(orderStartRequest)
                .doOnNext(t -> {
                    FeignResponseValidate.check(t);
                    if (EvseProtocolType.ACREL == evseVo.getProtocol()) {
                        this.plugService.updateOrderNo(orderStartRequest.getEvseNo(), orderStartRequest.getPlugId(), orderStartRequest.getOrderNo());
                        redisIotUpdateWrapper.bindOrderNo(orderStartRequest.getEvseNo(), orderStartRequest.getPlugId(), orderStartRequest.getOrderNo());
                    }
                })
                .map(entity -> {
                    log.info("云端开始充电返回 evseNo = {}, orderNo = {}, 结果 = {}",
                            orderStartRequest.getEvseNo(), orderStartRequest.getOrderNo(), entity);
                    BaseGwResponse res = new BaseGwResponse();
                    res.setSeq(orderStartRequest.getSeq());
                    return res;
                });

//
//        return this.dzBcChargeOrderService.orderStart(json)
//                ;

    }


    /**
     * 充电中数据更新
     *
     * @param json
     * @return
     */
    public Mono<BaseGwResponse> orderUpdate(OrderUpdateRequestV2 json) {
        //log.debug(">> json = {}", json);
        log.info(">> orderNo = {}", json.getOrderNo());
        IotAssert.isTrue(json.getEvseNo() != null && json.getEvseNo() != "", "请求参数EvseNo为空");
        IotAssert.isTrue(json.getPlugId() != null && json.getPlugId() >= 0, "请求参数PlugId为空");
        IotAssert.isTrue(json.getOrderNo() != null && json.getOrderNo() != "", "请求参数OrderNo为空");
        json.setPlugNo(PlugNoUtils.formatPlugNo(json.getEvseNo(), json.getPlugId()));
        return this.bizTradingReactiveFeignClient.orderUpdate(json)
                .doOnNext(entity -> {
                    log.info("云端充电过程数据上报返回结果: {}", entity);
                    if (entity.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        PlugVo plug = this.redisIotReadService.getPlugRedisCache(json.getEvseNo(), json.getPlugId());
                        if (plug == null) {
                            log.error("获取枪头缓存失败,需要确认设备是否登记. evseNo = {}, plugId = {}", json.getEvseNo(), json.getPlugId());
                        } else if ((plug.getStatus() == PlugStatus.OFFLINE
                                || plug.getStatus() == PlugStatus.ERROR
                                || plug.getStatus() == PlugStatus.BUSY)
                                && StringUtils.isBlank(plug.getOrderNo())) {
                            log.info("重新将订单号 {} 绑定到枪头 {}", json.getOrderNo(), plug.getPlugNo());
                            redisIotUpdateWrapper.bindOrderNo(json.getEvseNo(), json.getPlugId(), json.getOrderNo());
                        }
                    }
                })
                .map(res1 -> new BaseGwResponse());
    }


    /**
     * 桩上报充电结束
     *
     * @param jsonx
     * @return
     */
    public Mono<BaseGwResponse> orderStop(GwObjReqMsg jsonx) {
        log.debug(">> json = {}", jsonx);
        IotAssert.isNotNull(jsonx.getGwno(), "JSON 缺少gwno");
        IotAssert.isNotNull(jsonx.getSeq(), "JSON 缺少seq");

        // TODO raf V3
        GwObjReqMsg<OrderStopRequestV2> json = jsonx;

        String evseNo = json.getData().getEvseNo();
        Integer plugId = json.getData().getPlugId();
        String orderNo = json.getData().getOrderNo();

        IotAssert.isTrue(json.getGwno() != null && json.getGwno() != "", "请求参数Gwno为空");
        IotAssert.isNotBlank(evseNo, "请求参数EvseId为空");
        IotAssert.isTrue(plugId != null && plugId >= 0, "请求参数PlugId为空");
        IotAssert.isTrue(orderNo != null && orderNo != "", "请求参数OrderNo为空");
        json.getData().setPlugNo(PlugNoUtils.formatPlugNo(evseNo, plugId));
        Optional.ofNullable(evseModelRoDs.getEvseOpInfoByEvseId(evseNo))
            .ifPresent(opInfo -> json.getData().setEvseBrand(opInfo.getBrand()));

        return this.bizTradingReactiveFeignClient.orderStop(json.getData())
                .doOnNext(entity -> {
                    log.info("云端停止充电返回结果: {}", entity.toString());
                    IotAssert.isTrue(entity.getStatus() == 0, entity.getError());
                })
                .doOnSuccess(res -> {
                    PlugPo plugPo = plugService.getPlug(evseNo, plugId, false);

                    IotAssert.isNotNull(plugPo, "枪记录不存在，请检查");
                    if (StringUtils.isNotBlank(plugPo.getOrderNo()) && orderNo.equals(plugPo.getOrderNo())) {
                        log.info("停止充电清空订单。订单号 = {},plugPo = {}", orderNo, plugPo);
                        this.plugService.updateOrderNo(evseNo, plugId, null);
                    }
                })
                .flatMap(e -> iotParkFeignClient.checkCoupon(json.getData())) // FIXME 此处可优化为异步调用
                .map(res1 -> {
                    BaseGwResponse res = new BaseGwResponse();
                    res.setSeq(json.getSeq());

                    log.debug("<<");
                    return res;
                });


//        return this.dzBcChargeOrderService.orderStopV2(json)
//                ;

    }

    /**
     * 订单续费
     *
     * @param req
     * @return
     */
    public Mono<GwObjResMsg<OrderFeeRefreshResponseV2>> refreshFee(GwObjReqMsg<OrderFeeRefreshRequestV2> req) {
        IotAssert.isNotNull(req.getGwno(), "JSON 缺少gwno");
        IotAssert.isNotNull(req.getSeq(), "JSON 缺少seq");

        return this.bizTradingReactiveFeignClient.orderFeeRefresh(req.getData())
                .doOnNext(res -> {
                    IotAssert.isNotNull(res, "请求续费失败");
                    IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, res.getError());
                    IotAssert.isNotNull(res.getData(), "请求续费失败");
                })
                //.map(res2 -> res2.getData())
                .map(o -> {
                    GwObjResMsg<OrderFeeRefreshResponseV2> ret = new GwObjResMsg(o.getData());
                    ret.setSeq(req.getSeq());
                    return ret;
                });


    }
}
