package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.common.utils.RedisIdGenerator;
import com.cdz360.iot.ds.ro.EvseCfgRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.rw.EvseCfgResultRwDs;
import com.cdz360.iot.ds.rw.EvseCfgRwDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.ds.rw.mapper.GwInfoRwQueryMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseV2;
import com.cdz360.iot.model.evse.cfg.ChargeStopMode;
import com.cdz360.iot.model.evse.cfg.LocalCard;
import com.cdz360.iot.model.evse.cfg.WhiteVin;
import com.cdz360.iot.model.evse.param.ModifyEvseCfgParam;
import com.cdz360.iot.model.evse.param.ModifyEvseOperationParam;
import com.cdz360.iot.model.evse.po.EvseCfgPo;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.type.EvseCfgResult;
import com.cdz360.iot.model.evse.vo.EvseCfgResultVo;
import com.cdz360.iot.model.evse.vo.PriceSchemeSiteVo;
import com.cdz360.iot.model.gw.MqEvseCfgCmd;
import com.cdz360.iot.model.po.SiteAuthCardLogPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.CfgEvseResultType;
import com.cdz360.iot.model.vin.po.SiteAuthVinLogPo;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import com.cdz360.iot.worker.feign.DeviceBusinessFeignClient;
import com.cdz360.iot.worker.model.dongzheng.BoxSettingUpdateRequest;
import com.cdz360.iot.worker.model.dongzheng.BsBoxSettingPo;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgResendDto;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class EvseCfgBizService {

    private static final String EVSE_DIAGNOSTICS_FILE_NAME = "iot:evse:diagnosticsFileName:";

    @Autowired
    private EvseCfgRoDs evseCfgRoDs;
    @Autowired
    private EvseCfgRwDs evseCfgRwDs;
    @Autowired
    private EvseCfgRollbackService evseCfgRollbackService;
    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;
    @Autowired
    private PlugRoDs plugRoDs;
    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;

    @Autowired
    private SequenceRwService sequenceRwService;

    @Autowired
    private MqService mqService;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private GwInfoRwQueryMapper gwInfoRwQueryMapper;

    @Autowired
    private DcEventPublisher dcEventPublish;

    @Autowired
    private EvseCfgRedisService evseCfgRedisService;

    @Autowired
    private GwCacheService gwCacheService;

//    @Autowired
//    private DeviceBussnessFeignClient deviceBussnessFeignClient;

    @Autowired
    private DeviceBusinessFeignClient deviceBusinessReactiveFeignClient;

//    @Autowired
//    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private EvseService evseService;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreReactiveFeignClient;


    // 下发指令超时时间: 单位: 秒
    @Value("${iot.cmd.timeout:60}")
    private Integer timeout;

    private void cfgRedis(String gwno, List<String> evseNoList, CfgEvseV2 cfgEvseV2) {
        this.redisTemplate.execute(new RedisCallback<String>() {
            @Override
            public String doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();

                evseNoList.forEach(evseNo -> {
                    connection.rPush(
                            RedisIdGenerator.getCfgId(gwno, evseNo).getBytes(),
                            JsonUtils.toJsonString(cfgEvseV2).getBytes());
                });

                connection.closePipeline();
                return null;
            }
        });
    }

    private void cfgPairRedis(String gwno, List<Pair<String, CfgEvseV2>> evseNoPairList) {
        this.redisTemplate.execute(new RedisCallback<String>() {
            @Override
            public String doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();

                evseNoPairList.forEach(pair -> {
                    connection.rPush(
                            RedisIdGenerator.getCfgId(gwno, pair.getFirst()).getBytes(),
                            JsonUtils.toJsonString(pair.getSecond()).getBytes());
                });

                connection.closePipeline();
                return null;
            }
        });
    }

    /**
     * 更新/下发桩紧急卡列表
     *
     * @param param
     */
    @Transactional
    public void modifyEvseWhiteCard(ModifyEvseCfgParam param) {
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 需要处理下发的桩编号列表
        Map<String, List<String>> gwnoEvseNoListMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        // 离线的桩
        List<BsBoxSettingPo> requestList = new ArrayList<>();

        // redis 缓存处理一个网关对应一个配置信息
        Map<String, CfgEvseV2> gwnoRedisMap = new HashMap<>();

        // redis 中桩的信息
        Map<String, List<EvseVo>> evseRedisMap = redisIotReadService.getEvseList(param.getEvseNoList()).parallelStream()
                .collect(Collectors.groupingBy(EvseVo::getEvseNo));

        param.getEvseNoList().forEach(evseNo -> {
//            CfgEvseParam mqParam = new CfgEvseParam();
//            mqParam.setEvseIds(List.of(evseNo));
//            mqParam.setCfgEvse(new CfgEvseV2());
            CfgEvseV2 cfgEvseV2 = new CfgEvseV2();

//            EvseVo evse = redisIotReadService.getEvseRedisCache(evseNo);
            EvseVo evse = evseRedisMap.get(evseNo).get(0);

            // 紧急充电卡
            if (CollectionUtils.isNotEmpty(param.getWhiteCards())) {
                cfgEvseV2.setWhiteCards(param.getWhiteCards());
            }

            EvseCfgResultPo cfgStatus = evseCfgResultRwDs.getByEvseNo(evseNo, true);
            if (cfgStatus == null) {
                cfgStatus = new EvseCfgResultPo();
                cfgStatus.setEvseNo(evseNo).setEnable(true);
            }

            if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
                cfgStatus.setWhiteCardResult(EvseCfgResult.EVSE_OFFLINE);
                this.evseCfgResultRwDs.insertOrUpdate(cfgStatus);   // 更新配置下发状态
                log.warn("桩 {} 当前不在线, 发送失败", evseNo);

                // 回写下发状态
                // FIXME: 回写下发状态
                // t_bs_box_setting
                BsBoxSettingPo po = new BsBoxSettingPo();
                po.setBoxOutFactoryCode(evseNo);
                po.setWhiteCardsStatus(2L);

//                deviceBussnessFeignClient.updateBoxSetting(boxSettingUpdateRequest);
                requestList.add(po);
            } else {
                cfgStatus.setWhiteCardResult(EvseCfgResult.SENDING);
                log.info("debug cfgStatus: {}", JsonUtils.toJsonString(cfgStatus));
                this.evseCfgResultRwDs.insertOrUpdate(cfgStatus);   // 更新配置下发状态

                // 下发指令
                boolean hadGw = true;
                if (!gwnoVerMap.containsKey(evse.getGwno())) {
                    GwInfoPo gwInfoDto = gwCacheService.getGwInfo(evse.getGwno());
                    if (null == gwInfoDto) {
                        hadGw = false;
                    } else {
                        gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                        gwnoEvseNoListMap.put(evse.getGwno(), new ArrayList<>());
                        gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                        gwnoDownSeqMap.put(evse.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                        gwnoRedisMap.put(evse.getGwno(), cfgEvseV2);
                    }
                }

                if (hadGw) {
                    gwnoEvseNoListMap.get(evse.getGwno()).add(evseNo);
                } else {
                    BsBoxSettingPo po = new BsBoxSettingPo();
                    po.setBoxOutFactoryCode(evseNo);
                    po.setWhiteCardsStatus(2L);
                    requestList.add(po);
                }

            }
        });

        // 离线桩配置更新

        // BaseResponse response = dataCoreFeignClient.batchUpdateBoxSettingStatus(requestList);

        this.bizDataCoreReactiveFeignClient.batchUpdateBoxSettingStatus(requestList)
                .doOnNext(resY -> {
                    // 组装数据下发
                    gwnoEvseNoListMap.forEach((k, v) -> {

                        this.cfgRedis(k, gwnoEvseNoListMap.get(k),
                                gwnoRedisMap.get(k).setCfgVer(gwnoDownVerMap.get(k)));

                        // 直接放入MQ下发
                        IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
                        MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
                        mqEvseCfgCmd.setEvseNos(v);
                        mqEvseCfgCmd.setCfgVer(gwnoDownVerMap.get(k));
                        cmd.setGwno(k)
                                .setSeq(gwnoDownSeqMap.get(k))
                                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                                .setData(mqEvseCfgCmd);
                        GwInfoPo gwInfo = gwCacheService.getGwInfo(k);
                        this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));

                        // 超时检测消息队列
                        // 用作超时检测
                        param.setEvseNoList(v);
                        IotGwDownCmd downCmd = new IotGwDownCmd();
                        downCmd.setTtl(timeout)
                                .setMsg(JsonUtils.toJsonString(param))
                                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                                .setGwno(k)
                                .setSeq(gwnoDownSeqMap.get(k))
                                .setVer(gwnoVerMap.get(k));

                        this.dcEventPublish.publishIotGwDownCmd(downCmd);

                        log.info("网关 {} 已将数据放入MQ", k);
                    });
                })
                .subscribe(res -> {
                    log.info("<< 紧急卡下发全部处理完成");
                });


    }

    /**
     * 更新/下发桩本地vin鉴权列表
     *
     * @param param
     */
    @Transactional
    public void modifyEvseLocalVin(ModifyEvseCfgParam param) {
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        // 桩号-vin鉴权配置
        Map<String, SiteAuthVinLogPo> evseNoAuthVinMap = param.getSiteAuthVinList()
                .parallelStream()
                .collect(Collectors.toMap(SiteAuthVinLogPo::getEvseId, o -> o, (o, n) -> n));

        // redis 中桩的信息
        Map<String, EvseVo> evseRedisMap = redisIotReadService.getEvseList(new ArrayList<>(evseNoAuthVinMap.keySet()))
                .parallelStream()
                .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o, (o, n) -> n));

        // 网关编号-evse
        Map<String, List<EvseVo>> gwnoEvseMap = new HashMap<>();

        // 桩分类
        evseRedisMap.forEach((evseNo, evse) -> {
            if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
                log.warn("桩 {} 当前不在线, 发送失败", evseNo);
                // TODO 返回下发失败信息？
            } else {
                if (!gwnoVerMap.containsKey(evse.getGwno())) {
                    GwInfoPo gwInfoDto = gwCacheService.getGwInfo(evse.getGwno());
                    if (null == gwInfoDto) {
                        log.warn("桩 {} 找不到对应网关信息, 发送失败", evseNo);
                        // TODO 返回下发失败信息？
                    } else {
                        gwnoEvseMap.put(gwInfoDto.getGwno(), new ArrayList<>());
                        gwnoEvseMap.get(gwInfoDto.getGwno()).add(evse);

                        gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                        gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                        gwnoDownSeqMap.put(evse.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    }
                } else {
                    gwnoEvseMap.get(evse.getGwno()).add(evse);
                }
            }
        });

        gwnoEvseMap.forEach((gwno, evseList) -> {

            // 存入redis，等待网关消费
            this.cfgPairRedis(gwno, evseList.stream()
                    .map(e -> {
                        CfgEvseV2 cfgEvseV2 = new CfgEvseV2();
                        cfgEvseV2.setCfgVer(gwnoDownVerMap.get(gwno));
                        if(evseNoAuthVinMap.get(e.getEvseNo()) != null &&
                                evseNoAuthVinMap.get(e.getEvseNo()).getVins() != null) {
                            cfgEvseV2.setWhiteVinList(evseNoAuthVinMap.get(e.getEvseNo())
                                    .getVins()
                                    .stream()
                                    .map(v -> {
                                        WhiteVin ret = new WhiteVin();
                                        ret.setVin(v);
                                        //ret.setCarNo();// TODO 车牌号
                                        return ret;
                                    }).collect(Collectors.toList()));
                        }

                        return Pair.of(e.getEvseNo(), cfgEvseV2);
                    })
                    .collect(Collectors.toList()));

            List<String> evseNos = evseList.stream().map(EvseVo::getEvseNo).collect(Collectors.toList());

            // 直接放入MQ下发
            IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
            MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
            mqEvseCfgCmd.setEvseNos(evseNos);
            mqEvseCfgCmd.setCfgVer(gwnoDownVerMap.get(gwno));
            cmd.setGwno(gwno)
                    .setSeq(gwnoDownSeqMap.get(gwno))
                    .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                    .setData(mqEvseCfgCmd);
            GwInfoPo gwInfo = gwCacheService.getGwInfo(gwno);
            this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            log.debug("下发到网关: {}-{}", gwInfo, JsonUtils.toJsonString(cmd));

            // 超时检测消息队列
            // 用作超时检测
            param.setEvseNoList(evseNos);
            IotGwDownCmd downCmd = new IotGwDownCmd();
            downCmd.setTtl(timeout)
                    .setMsg(JsonUtils.toJsonString(param))
                    .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                    .setGwno(gwno)
                    .setSeq(gwnoDownSeqMap.get(gwno))
                    .setVer(gwnoVerMap.get(gwno));

            this.dcEventPublish.publishIotGwDownCmd(downCmd);
            log.debug("超时检测: {}", downCmd);

            log.info("网关 {} 已将数据放入MQ", gwno);
        });


    }

    /**
     * 更新/下发桩配置
     *
     * @param param 桩配置参数
     */
    @Transactional
    public void modifyEvseCfg(ModifyEvseCfgParam param) {
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 需要处理下发的桩编号列表
        Map<String, List<String>> gwnoEvseNoListMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        // 离线的桩
        List<BsBoxSettingPo> requestList = new ArrayList<>();

        // redis 缓存处理一个网关对应一个配置信息
        Map<String, CfgEvseV2> gwnoRedisMap = new HashMap<>();

        // redis 中桩的信息
        Map<String, List<EvseVo>> evseRedisMap = redisIotReadService.getEvseList(param.getEvseNoList())
                .parallelStream()
                .collect(Collectors.groupingBy(EvseVo::getEvseNo));

        // 批量更新桩的长效密钥
        Map<String, Long> evsePassCodeMap;
        // 海外平台允许设置长效密钥为空
//        if (StringUtils.isNotBlank(param.getEvsePasscode())) {
            evsePassCodeMap = evseService.batchUpdateEvsePasscode(param.getEvseNoList(), param.getEvsePasscode());
//        } else {
//            evsePassCodeMap = new HashMap<>();
//        }

        // 下发前处理
        param.getEvseNoList().forEach(evseNo -> {
//        CfgEvseParam mqParam = new CfgEvseParam();
//        mqParam.setEvseIds(List.of(evseNo));
//        mqParam.setCfgEvse(new CfgEvseV2());
            CfgEvseV2 cfgEvseV2 = new CfgEvseV2();

            EvseCfgPo cfg = this.evseCfgRwDs.getEvseCfg(evseNo, true);
            if (cfg == null) {
                cfg = new EvseCfgPo();
                cfg.setEvseNo(evseNo).setEnable(true);
            }

            cfg = this.buildEvseCfg(cfg, param, cfgEvseV2);

            // 桩长效密钥    海外平台长效密钥允许为空
//            if (StringUtils.isNotBlank(param.getEvsePasscode())) {
                if (null != evsePassCodeMap.get(evseNo)) {
                    cfgEvseV2.setEvsePasscode(param.getEvsePasscode());
                    cfgEvseV2.setEvsePasscodeVer(evsePassCodeMap.get(evseNo).intValue());
                }
//            }

//            EvseVo evse = redisIotReadService.getEvseRedisCache(evseNo);
            EvseVo evse = evseRedisMap.get(evseNo).get(0);

//            EvseCfgResultPo cfgStatus = evseCfgResultRwDs.getByEvseNo(evseNo, true);
//            if (cfgStatus == null) {
            EvseCfgResultPo cfgStatus = new EvseCfgResultPo();
            cfgStatus.setEvseNo(evseNo).setEnable(true);
//            }

            if (!ObjectUtils.isEmpty(param.getPriceSchemeId())
                && param.getPriceSchemeId() > 0) {
                cfgStatus.setExpectPriceCode(param.getPriceSchemeId());
            } else {
                this.evseCfgRwDs.insertEvseCfg(cfg);    // 桩配置落库
                cfgStatus.setExpectCfgCode(cfg.getId());
            }

            if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
                cfgStatus.setCfgResult(EvseCfgResult.EVSE_OFFLINE);

                if (!ObjectUtils.isEmpty(param.getPriceSchemeId()) && param.getPriceSchemeId() > 0) {
                    cfgStatus.setPriceCodeResult(EvseCfgResult.EVSE_OFFLINE);
                }
                this.evseCfgResultRwDs.insertOrUpdate(cfgStatus);   // 更新配置下发状态
                log.warn("桩 {} 当前不在线, 发送失败", evseNo);

                // 桩配置记录回滚
//                this.evseCfgRwDs.resetEvseCfg(evseNo);

                // 回写下发状态
                // FIXME: 回写下发状态
                // t_bs_box_setting
                BsBoxSettingPo po = new BsBoxSettingPo();
                po.setBoxOutFactoryCode(evseNo);
                po.setStatus(IotConstants.EVSE_SETTING_STATUS_FAIL);

                if (ObjectUtils.isEmpty(param.getPriceSchemeId())) {
                    po.setAdminCodeResult(EvseCfgResult.EVSE_OFFLINE);
                    po.setTriggerResult(EvseCfgResult.EVSE_OFFLINE);

                    if (param.getQrUrl() != null) {
                        po.setQrResult(EvseCfgResult.EVSE_OFFLINE);
                    }
                } else {
                    po.setChargeResult(EvseCfgResult.EVSE_OFFLINE);
                }
                requestList.add(po);

//                deviceBussnessFeignClient.updateBoxSetting(boxSettingUpdateRequest);
            } else {
                if (!ObjectUtils.isEmpty(param.getPriceSchemeId()) && param.getPriceSchemeId() > 0) {
                    cfgStatus.setPriceCodeResult(EvseCfgResult.SENDING);
                } else {
                    cfgStatus.setCfgResult(EvseCfgResult.SENDING);
                }

                this.evseCfgResultRwDs.insertOrUpdate(cfgStatus);   // 更新配置下发状态

                // 初始数据
                boolean hadGw = true;
                if (!gwnoVerMap.containsKey(evse.getGwno())) {
                    GwInfoDto gwInfoDto = gwInfoRwQueryMapper.getByGwno(evse.getGwno(), false);
                    if (null == gwInfoDto) {
                        hadGw = false;
                    } else {
                        gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                        gwnoEvseNoListMap.put(evse.getGwno(), new ArrayList<>());
                        gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                        gwnoDownSeqMap.put(evse.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                        gwnoRedisMap.put(evse.getGwno(), cfgEvseV2);
                    }
                }

                if (hadGw) {
                    gwnoEvseNoListMap.get(evse.getGwno()).add(evseNo);
                } else {
                    BsBoxSettingPo po = new BsBoxSettingPo();
                    po.setBoxOutFactoryCode(evseNo);
                    po.setStatus(IotConstants.EVSE_SETTING_STATUS_FAIL);

                    if (ObjectUtils.isEmpty(param.getPriceSchemeId())) {
                        po.setAdminCodeResult(EvseCfgResult.EVSE_OFFLINE);
                        po.setTriggerResult(EvseCfgResult.EVSE_OFFLINE);

                        if (param.getQrUrl() != null) {
                            po.setQrResult(EvseCfgResult.EVSE_OFFLINE);
                        }
                    } else {
                        po.setChargeResult(EvseCfgResult.EVSE_OFFLINE);
                    }
                    requestList.add(po);
                }

            }
        });

        // 离线桩配置更新
        if (CollectionUtils.isNotEmpty(requestList)) {
            this.evseCfgRollbackService.handleRollback(requestList.parallelStream()
                    .map(BsBoxSettingPo::getBoxOutFactoryCode).collect(Collectors.toList()));
            //BaseResponse response = dataCoreFeignClient.batchUpdateBoxSettingStatus(requestList);
            this.bizDataCoreReactiveFeignClient.batchUpdateBoxSettingStatus(requestList)
                    .subscribe();
        }

        // 组装数据下发
        gwnoEvseNoListMap.forEach((k, v) -> {
            this.cfgRedis(k, gwnoEvseNoListMap.get(k),
                    gwnoRedisMap.get(k).setCfgVer(gwnoDownVerMap.get(k)));

            // 直接放入MQ下发
            IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
            MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
            mqEvseCfgCmd.setEvseNos(v);
            mqEvseCfgCmd.setCfgVer(gwnoDownVerMap.get(k));
            cmd.setGwno(k)
                    .setSeq(gwnoDownSeqMap.get(k))
                    .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                    .setData(mqEvseCfgCmd);

            GwInfoPo gwInfo = gwCacheService.getGwInfo(k);
            this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));

            // 超时检测消息队列
            // 用作超时检测
            param.setEvseNoList(v);
            IotGwDownCmd downCmd = new IotGwDownCmd();
            downCmd.setTtl(timeout)
                    .setMsg(JsonUtils.toJsonString(param))
                    .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                    .setGwno(k)
                    .setSeq(gwnoDownSeqMap.get(k))
                    .setVer(gwnoVerMap.get(k));

            this.dcEventPublish.publishIotGwDownCmd(downCmd);

            log.info("网关 {} 所需要的配置下发成功", k);
        });

        log.info("<< 配置下发全部处理完成");
    }

    /**
     * 桩注册后重发计费和配置
     *
     */
    @Transactional
    public void resendEvseConfig(@NonNull EvseCfgResendDto resendDto,
        @NonNull ModifyEvseCfgParam param) {
        log.info(">> resendEvseConfig resendDto: {}, priceSchemeList.size: {}", resendDto,
            param.getPriceSchemeList() != null ? param.getPriceSchemeList().size() : null);
        String evseNo = resendDto.getEvseNo();
        boolean sendPrice = resendDto.isSendPrice();
        boolean sendConfig = resendDto.isSendConfig();
        if (!sendPrice && !sendConfig) {
            log.info("<< no need to resend");
            return;
        }
        if (sendPrice) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPriceSchemeList()),
                "缺少计费信息");
        }
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 需要处理下发的桩编号列表
        Map<String, List<String>> gwnoEvseNoListMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        // redis 缓存处理一个网关对应一个配置信息
        Map<String, CfgEvseV2> gwnoRedisMap = new HashMap<>();

        // redis中桩的信息
        EvseVo evse = redisIotReadService.getEvseRedisCache(evseNo);

        // 下发前处理
        if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
            log.warn("桩 {} 当前不在线, 发送失败", evseNo);
        } else {
            EvseCfgResultPo cfgStatus = new EvseCfgResultPo();
            cfgStatus.setEvseNo(evseNo).setEnable(true);

            CfgEvseV2 cfgEvseV2 = new CfgEvseV2();
            EvseCfgPo cfg;
            if (sendConfig) {
                if (!resendDto.isFetchConfig() && NumberUtils.gtZero(resendDto.getIotConfigId())) {
                    cfg = this.evseCfgRoDs.queryById(resendDto.getIotConfigId());
                    cfgEvseV2 = this.buildEvseCfg(cfg);
                    log.debug("1 cfgStatus: {}, cfgEvseV2: {}", cfgStatus, cfgEvseV2);
                } else {
                    cfg = new EvseCfgPo();
                    cfg.setEvseNo(evseNo);
                    this.buildEvseCfg(cfg, param, cfgEvseV2);
                    this.evseCfgRwDs.insertEvseCfg(cfg);    // 桩配置落库
                    log.debug("2 cfgStatus: {}, cfgEvseV2: {}", cfgStatus, cfgEvseV2);
                }
                cfgStatus.setExpectCfgCode(cfg.getId())
                    .setCfgResult(EvseCfgResult.SENDING);
            }
            if (sendPrice) {
                Long priceCode = Optional.ofNullable(resendDto.getPriceId())
                    .orElse(param.getPriceSchemeId());
                IotAssert.isTrue(NumberUtils.gtZero(priceCode), "计费ID无效");
                cfgStatus.setExpectPriceCode(priceCode)
                    .setPriceCodeResult(EvseCfgResult.SENDING);
                cfgEvseV2.setPriceCode(priceCode.intValue())
                    .setPrice(param.getPriceSchemeList());
            }
            log.debug("cfgStatus: {}, cfgEvseV2: {}", cfgStatus, cfgEvseV2);
            this.evseCfgResultRwDs.insertOrUpdate(cfgStatus);   // 更新配置下发状态

            // 初始数据
            boolean hadGw = true;
            if (!gwnoVerMap.containsKey(evse.getGwno())) {
                GwInfoDto gwInfoDto = gwInfoRwQueryMapper.getByGwno(evse.getGwno(), false);
                if (null == gwInfoDto) {
                    log.error("找不到桩对应的网关信息 evseNo: {}, gwno: {}", evseNo,
                        evse.getGwno());
                    hadGw = false;
                } else {
                    gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                    gwnoEvseNoListMap.put(evse.getGwno(), new ArrayList<>());
                    gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                    gwnoDownSeqMap.put(evse.getGwno(),
                        this.sequenceRwService.getNextOutRequestSeq());
                    gwnoRedisMap.put(evse.getGwno(), cfgEvseV2);
                }
            }

            if (hadGw) {
                gwnoEvseNoListMap.get(evse.getGwno()).add(evseNo);
            }

        }

        // 组装数据下发
        gwnoEvseNoListMap.forEach((gwno, evseNos) -> {
            String cfgVer = gwnoDownVerMap.get(gwno);
            this.cfgRedis(gwno, evseNos,
                gwnoRedisMap.get(gwno).setCfgVer(cfgVer));

            // 直接放入MQ下发
            IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
            MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
            mqEvseCfgCmd.setEvseNos(evseNos);
            mqEvseCfgCmd.setCfgVer(cfgVer);
            cmd.setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                .setData(mqEvseCfgCmd);

            GwInfoPo gwInfo = gwCacheService.getGwInfo(gwno);
            this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));

/*
            // 超时检测消息队列，用作超时检测
            param.setEvseNoList(evseNos);
            IotGwDownCmd downCmd = new IotGwDownCmd();
            downCmd.setTtl(timeout)
                .setMsg(JsonUtils.toJsonString(param))
                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                .setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setVer(gwnoVerMap.get(gwno));

            this.dcEventPublish.publishIotGwDownCmd(downCmd);
*/

            log.info("网关 {} 所需要的配置下发成功", gwno);
        });

        log.info("<< resendEvseConfig end");
    }


    private EvseCfgPo buildEvseCfg(EvseCfgPo cfg, ModifyEvseCfgParam param, CfgEvseV2 inOutMqParam) {
        if (param.getAdminPassword() != null) {
            cfg.setAdminPassword(param.getAdminPassword());
            inOutMqParam.setAdminCodeA(param.getAdminPassword());
        }
        if (param.getLevel2Password() != null) {
            cfg.setLevel2Password(param.getLevel2Password());
            inOutMqParam.setAdminCodeB(param.getLevel2Password());
        }
        if (param.getDayVolume() != null) {
            cfg.setDayVolume(param.getDayVolume());
            inOutMqParam.setDayVolume(param.getDayVolume());
        }
        if (param.getNightVolume() != null) {
            cfg.setNightVolume(param.getNightVolume());
            inOutMqParam.setNightVolume(param.getNightVolume());
        }
        if (param.getQrUrl() != null) {
            cfg.setQrUrl(param.getQrUrl());
            inOutMqParam.setQrUrl(param.getQrUrl());
        }
        if (param.getInternationalAgreement() != null) {
            cfg.setInternationalAgreement(param.getInternationalAgreement());
            inOutMqParam.setBmsVer(param.getInternationalAgreement());
        }
        if (param.getHeatingVoltage() != null) {
            cfg.setHeatingVoltage(param.getHeatingVoltage());
            inOutMqParam.setHeatingVoltage(param.getHeatingVoltage());
        }
        if (param.getHeating() != null) {
            cfg.setHeating(param.getHeating());
            inOutMqParam.setHeating(param.getHeating());
        }
        if (param.getAvgOrTurnCharge() != null) {
            cfg.setAvgOrTurnCharge(param.getAvgOrTurnCharge());
            inOutMqParam.setAvgOrTurnCharge(param.getAvgOrTurnCharge());
        }
        if (param.getBatteryCheck() != null) {
            cfg.setBatteryCheck(param.getBatteryCheck());
            inOutMqParam.setBatteryCheck(param.getBatteryCheck());
        }
        if (param.getSecurityCheck() != null) {
            cfg.setSecurityCheck(param.getSecurityCheck());
            inOutMqParam.setSecurityCheck(param.getSecurityCheck());
        }
        if (param.getVinDiscover() != null) {
            cfg.setVinDiscover(param.getVinDiscover());
            inOutMqParam.setVinDiscover(param.getVinDiscover());
        }
        if (param.getAccountDisplayType() != null) {
            cfg.setAccountDisplayType(param.getAccountDisplayType());
            inOutMqParam.setAccountDisplayType(param.getAccountDisplayType());
        }
        if (param.getIsCombineCharge() != null) {
            cfg.setIsCombineCharge(param.getIsCombineCharge());
            inOutMqParam.setCombination(NumberUtils.equals(param.getIsCombineCharge(), 1));
        }
        if (param.getIsQueryChargeRecord() != null) {
            cfg.setIsQueryChargeRecord(param.getIsQueryChargeRecord());
            inOutMqParam.setQueryChargeRecord(param.getIsQueryChargeRecord());
        }
        if (param.getConstantCharge() != null) {
            cfg.setConstantCharge(param.getConstantCharge());
            inOutMqParam.setConstantCharge(param.getConstantCharge());
        }
        if (param.getOrderPrivacySetting() != null) {
            cfg.setOrderPrivacySetting(param.getOrderPrivacySetting());
            inOutMqParam.setOrderPrivacySetting(param.getOrderPrivacySetting());
        }
        if (param.getIsTimedCharge() != null) {
            cfg.setIsTimedCharge(param.getIsTimedCharge());
            inOutMqParam.setTimedCharge(param.getIsTimedCharge());
        }
        if (param.getIsNoCardCharge() != null) {
            cfg.setIsNoCardCharge(param.getIsNoCardCharge());
            inOutMqParam.setNoCardCharge(param.getIsNoCardCharge());
        }
        if (param.getIsScanCharge() != null) {
            cfg.setIsScanCharge(param.getIsScanCharge());
            inOutMqParam.setQrCharge(param.getIsScanCharge());
        }
        if (param.getIsVinCharge() != null) {
            cfg.setIsVinCharge(param.getIsVinCharge());
            inOutMqParam.setVin(param.getIsVinCharge());
        }
        if (param.getIsCardCharge() != null) {
            cfg.setIsCardCharge(param.getIsCardCharge());
            inOutMqParam.setCardCharge(param.getIsCardCharge());
        }
        if (param.getIsQuotaEleCharge() != null) {
            cfg.setIsQuotaEleCharge(param.getIsQuotaEleCharge());
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setKwh(param.getIsQuotaEleCharge());
        }
        if (param.getIsQuotaMoneyCharge() != null) {
            cfg.setIsQuotaMoneyCharge(param.getIsQuotaMoneyCharge());
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setAmount(param.getIsQuotaMoneyCharge());
        }
        if (param.getIsQuotaTimeCharge() != null) {
            cfg.setIsQuotaTimeCharge(param.getIsQuotaTimeCharge());
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setTime(param.getIsQuotaTimeCharge());
        }

        // 计费模板
        if (param.getPriceSchemeList() != null && !param.getPriceSchemeList().isEmpty()) {
            inOutMqParam.setPriceCode(param.getPriceSchemeId().intValue());
            inOutMqParam.setPrice(param.getPriceSchemeList());
        }

//        // 长效密钥
//        if (param.getEvsePasscode() != null) {
//            inOutMqParam.setEvsePasscode(param.getEvsePasscode());
//        }
//        if (param.getEvsePasscodeVer() != null) {
//            inOutMqParam.setEvsePasscodeVer(param.getEvsePasscodeVer());
//        }

        // 紧急充电卡
        if (CollectionUtils.isNotEmpty(param.getWhiteCards())) {
            inOutMqParam.setWhiteCards(param.getWhiteCards());
        }

        return cfg;
    }

    private CfgEvseV2 buildEvseCfg(EvseCfgPo cfg) {
        CfgEvseV2 inOutMqParam = new CfgEvseV2();
        if (cfg.getAdminPassword() != null) {
            inOutMqParam.setAdminCodeA(cfg.getAdminPassword());
        }
        if (cfg.getLevel2Password() != null) {
            inOutMqParam.setAdminCodeB(cfg.getLevel2Password());
        }
        if (cfg.getDayVolume() != null) {
            inOutMqParam.setDayVolume(cfg.getDayVolume());
        }
        if (cfg.getNightVolume() != null) {
            inOutMqParam.setNightVolume(cfg.getNightVolume());
        }
        if (cfg.getQrUrl() != null) {
            inOutMqParam.setQrUrl(cfg.getQrUrl());
        }
        if (cfg.getInternationalAgreement() != null) {
            inOutMqParam.setBmsVer(cfg.getInternationalAgreement());
        }
        if (cfg.getHeatingVoltage() != null) {
            inOutMqParam.setHeatingVoltage(cfg.getHeatingVoltage());
        }
        if (cfg.getHeating() != null) {
            inOutMqParam.setHeating(cfg.getHeating());
        }
        if (cfg.getAvgOrTurnCharge() != null) {
            inOutMqParam.setAvgOrTurnCharge(cfg.getAvgOrTurnCharge());
        }
        if (cfg.getBatteryCheck() != null) {
            inOutMqParam.setBatteryCheck(cfg.getBatteryCheck());
        }
        if (cfg.getSecurityCheck() != null) {
            inOutMqParam.setSecurityCheck(cfg.getSecurityCheck());
        }
        if (cfg.getVinDiscover() != null) {
            inOutMqParam.setVinDiscover(cfg.getVinDiscover());
        }
        if (cfg.getAccountDisplayType() != null) {
            inOutMqParam.setAccountDisplayType(cfg.getAccountDisplayType());
        }
        if (cfg.getIsCombineCharge() != null) {
            inOutMqParam.setCombination(NumberUtils.equals(cfg.getIsCombineCharge(), 1));
        }
        if (cfg.getIsQueryChargeRecord() != null) {
            inOutMqParam.setQueryChargeRecord(cfg.getIsQueryChargeRecord());
        }
        if (cfg.getConstantCharge() != null) {
            inOutMqParam.setConstantCharge(cfg.getConstantCharge());
        }
        if (cfg.getOrderPrivacySetting() != null) {
            inOutMqParam.setOrderPrivacySetting(cfg.getOrderPrivacySetting());
        }
        if (cfg.getIsTimedCharge() != null) {
            inOutMqParam.setTimedCharge(cfg.getIsTimedCharge());
        }
        if (cfg.getIsNoCardCharge() != null) {
            inOutMqParam.setNoCardCharge(cfg.getIsNoCardCharge());
        }
        if (cfg.getIsScanCharge() != null) {
            inOutMqParam.setQrCharge(cfg.getIsScanCharge());
        }
        if (cfg.getIsVinCharge() != null) {
            inOutMqParam.setVin(cfg.getIsVinCharge());
        }
        if (cfg.getIsCardCharge() != null) {
            inOutMqParam.setCardCharge(cfg.getIsCardCharge());
        }
        if (cfg.getIsQuotaEleCharge() != null) {
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setKwh(cfg.getIsQuotaEleCharge());
        }
        if (cfg.getIsQuotaMoneyCharge() != null) {
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setAmount(cfg.getIsQuotaMoneyCharge());
        }
        if (cfg.getIsQuotaTimeCharge() != null) {
            if (inOutMqParam.getStopMode() == null) {
                inOutMqParam.setStopMode(new ChargeStopMode());
            }
            inOutMqParam.getStopMode().setTime(cfg.getIsQuotaTimeCharge());
        }
        return inOutMqParam;
    }

    /**
     * 修改桩计费模板-用于不支持计费下发的桩
     *
     * @param param
     */
    @Transactional
    public void modifyEvsePrice(ModifyEvseCfgParam param) {

        param.getEvseNoList().forEach(evseNo -> {
            EvsePo evsePoUpdate = new EvsePo();
            evsePoUpdate.setEvseId(evseNo);
            evsePoUpdate.setPriceCode(param.getPriceSchemeId());
            evseRwQueryMapper.updateByEvseId(evsePoUpdate);

            //同步redis中evse/plug的计费模板id
            List<String> plugNoList = this.plugRoDs.getPlugNoList(evseNo);
            this.redisIotRwService.updatePriceCode(evseNo, plugNoList, param.getPriceSchemeId());

            // 推送桩计费变动的消息
            dcEventPublish.publishEvseInfo(IotEvent.CFG_CHANGE,
                    redisIotRwService.getEvseRedisCache(evseNo));
            log.info("evseNo = {}, priceCode = {}", evseNo, param.getPriceSchemeId());
        });
    }

    /**
     * 获取计费模板下发信息
     *
     * @param priceSchemeIdList 计费模板Id
     * @return
     */
    public List<PriceSchemeSiteVo> getPriceSchemeSiteInfo(List<Long> priceSchemeIdList) {
        log.info(">> 获取计费模板场站下发信息: priceSchemeIdList = {}", priceSchemeIdList);
        // FIXME: 如果返回数据太大会存在问题
        List<EvseCfgResultVo> voList = evseCfgResultRwDs.getByPriceSchemeId(priceSchemeIdList, true)
                .stream().filter(i -> i.getSiteId() != null).collect(Collectors.toList());

        Map<String, List<EvseCfgResultVo>> map2VoList = voList.stream().collect(Collectors.groupingBy(EvseCfgResultVo::getSiteId));

        List<PriceSchemeSiteVo> result = new ArrayList<>();
        map2VoList.forEach((k, v) -> {
            // 下发成功的场站
            long successCnt = v.stream().filter(item -> item.getPriceCodeResult() == EvseCfgResult.SUCCESS).count();
            if (successCnt > 0) {
                result.add(new PriceSchemeSiteVo()
                        .setSiteId(k)
                        .setSiteName(v.get(0).getSiteName())
                        .setStatus(EvseCfgResult.SUCCESS));
            }

            // 下发中的场站
            long sendingCnt = v.stream().filter(item -> item.getPriceCodeResult() == EvseCfgResult.SENDING).count();
            if (sendingCnt > 0) {
                result.add(new PriceSchemeSiteVo()
                        .setSiteId(k)
                        .setSiteName(v.get(0).getSiteName())
                        .setStatus(EvseCfgResult.SENDING));
            }
        });

        log.info("<< size = {}", result.size());
        return result;
    }

    /**
     * <p>更新桩配置下发状态</p>
     * 结果上报/超时使用
     */
    public void resetEvseCfgAndUpdateCfgResult(@NonNull List<String> evseNoList,
        CfgEvseResultReqV2 evseResult, String gwno) {
        evseNoList.forEach(evseNo -> {
            log.info(">> 桩[{}]配置下发状态回退", evseNo);

            // 删除redis缓存
            CfgEvseAllV2 cache = evseCfgRedisService.getCfgEvseAll(
                RedisIdGenerator.getCfgId(gwno, evseNo));
            if (null != cache) {
                EvseCfgResultPo cfgStatus = evseCfgResultRwDs.getByEvseNo(evseNo, true);
                if (cfgStatus == null) {
                    log.warn("获取桩配置下发结果失败。evseNo = {}", evseNo);
                    return;
                }
                this.evseCfgResultHandler(evseNo, evseResult, cfgStatus)
                    .subscribe();
            }

            log.info("<<");
        });
    }

    /**
     * 配置下发结果更新
     * @param evseNo
     * @param req
     * @param originCfgResult
     * @return
     */
    public Mono<BaseResponse> evseCfgResultHandler(String evseNo, CfgEvseResultReqV2 req,
        EvseCfgResultPo originCfgResult) {
        evseCfgRedisService.delFromLimited(evseNo); // 解除桩注册配置下发的限制

        boolean changed = false;
        List<String> needRollbackEvseNo = new ArrayList<>();

        BoxSettingUpdateRequest boxSettingUpdate = new BoxSettingUpdateRequest();
        boxSettingUpdate.setBoxOutFactoryCode(evseNo);
        boxSettingUpdate.setStatus(CfgEvseResultType.SUCCESS.equals(req.getResult())
            ? IotConstants.EVSE_SETTING_STATUS_SUCCESS
            : IotConstants.EVSE_SETTING_STATUS_FAIL);

        EvseCfgResultPo evseCfgUpdate = new EvseCfgResultPo();
        evseCfgUpdate.setEvseNo(evseNo);

        // 桩配置下发
        boolean cfgDeliverResponse = req.getAdminCodeResult() != null
            || req.getTriggerResult() != null || req.getQrResult() != null
            || CfgEvseResultType.TIMEOUT.equals(req.getResult());
        if (NumberUtils.equals(originCfgResult.getCfgResult(), EvseCfgResult.SENDING)
            && cfgDeliverResponse) {
            // 下发中则需要更新状态
            changed = true;
            Integer result = EvseCfgResult.parse(req.getResult());
            evseCfgUpdate.setCfgResult(result);

            boxSettingUpdate.setAdminCodeResult(
                Optional.ofNullable(req.getAdminCodeResult()).orElse(result));
            boxSettingUpdate.setTriggerResult(
                Optional.ofNullable(req.getTriggerResult()).orElse(result));
            boxSettingUpdate.setQrResult(
                Optional.ofNullable(req.getQrResult()).orElse(result));

            if (CfgEvseResultType.SUCCESS.equals(req.getResult())) {
                evseCfgUpdate.setActualCfgCode(originCfgResult.getExpectCfgCode());
            } else {
                // 回退桩配置
                needRollbackEvseNo.add(evseNo);
            }
        }

        // 计费模板下发
        boolean priceDeliverResponse = req.getChargeResult() != null
            || CfgEvseResultType.TIMEOUT.equals(req.getResult());
        if (NumberUtils.equals(originCfgResult.getPriceCodeResult(), EvseCfgResult.SENDING)
            && priceDeliverResponse) {
            // 下发中则需要配置计费模板的状态
            changed = true;
            Integer result = EvseCfgResult.parse(req.getResult());
            evseCfgUpdate.setPriceCodeResult(result);

            boxSettingUpdate.setChargeResult(
                Optional.ofNullable(req.getChargeResult()).orElse(result));

            if (CfgEvseResultType.SUCCESS.equals(req.getResult())) {
                Long priceCode = originCfgResult.getExpectPriceCode();

                evseCfgUpdate.setActualPriceCode(priceCode);
                evseCfgUpdate.setPriceCodeEffectiveTime(new Date()); // 计费模板生效时间

                //同步DB中t_evse的计费模板id
                EvsePo evsePoUpdate = new EvsePo();
                evsePoUpdate.setEvseId(evseNo);
                evsePoUpdate.setPriceCode(priceCode);
                evseRwQueryMapper.updateByEvseId(evsePoUpdate);

                //同步redis中evse/plug的计费模板id
                List<String> plugNoList = this.plugRoDs.getPlugNoList(evseNo);
                this.redisIotRwService.updatePriceCode(evseNo, plugNoList, priceCode);

                // 推送桩计费变动的消息
                dcEventPublish.publishEvseInfo(IotEvent.CFG_CHANGE,
                    redisIotRwService.getEvseRedisCache(evseNo));
                log.info("evseNo = {}, priceCode = {}", evseNo, priceCode);
            }
        }

        // 紧急充电卡下发
        boolean whiteCardDeliverResponse = req.getWhiteCardsResult() != null
            || CfgEvseResultType.TIMEOUT.equals(req.getResult());
        if (NumberUtils.equals(originCfgResult.getWhiteCardResult(), EvseCfgResult.SENDING)
            && whiteCardDeliverResponse) {
            changed = true;
            evseCfgUpdate.setWhiteCardResult(EvseCfgResult.parse(req.getResult()));

            // 紧急充电卡下发结果
            boxSettingUpdate.setWhiteCardsStatus(
                NumberUtils.isZero(req.getWhiteCardsResult()) ? 1L : 2L);

            if (CfgEvseResultType.SUCCESS.equals(req.getResult())) {
                evseCfgUpdate.setActualWhiteCardCode(originCfgResult.getExpectWhiteCardCode());
            }
        }

        if (NumberUtils.isZero(req.getGetDiagnosticsResult()) && StringUtils.isNotBlank(
            req.getGetDiagnosticsFileName())) {
            String key = generateEvseDiagnosticsFileNameKey(evseNo);
            redisTemplate.opsForValue()
                .set(key, req.getGetDiagnosticsFileName(), 60, TimeUnit.MINUTES);
        }

        if (CollectionUtils.isNotEmpty(needRollbackEvseNo)) {
            // 回退桩配置
            this.evseCfgRollbackService.handleRollback(needRollbackEvseNo);
        }

        if (changed) {
            this.evseCfgResultRwDs.updateByEvseNo(evseCfgUpdate);
            return deviceBusinessReactiveFeignClient.updateBoxSetting(boxSettingUpdate);
        } else {
            log.warn("桩配置下发结果未发生变化, 不进行更新. req = {}, originCfgResult = {}",
                JsonUtils.toJsonString(req), JsonUtils.toJsonString(originCfgResult));
            return Mono.just(RestUtils.success());
        }
    }

    private String generateEvseDiagnosticsFileNameKey(String evseNo) {
        return EVSE_DIAGNOSTICS_FILE_NAME + evseNo;
    }

    public EvseCfgResultPo getEvseCfgResult(String evseNo) {
        return evseCfgResultRwDs.getByEvseNo(evseNo, false);
    }

    /**
     * 更新/下发桩本地card鉴权列表
     *
     * @param param
     */
    @Transactional
    public void modifyEvseLocalCard(ModifyEvseCfgParam param) {
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        // 桩号-card鉴权配置
        Map<String, SiteAuthCardLogPo> evseNoAuthCardMap = param.getSiteAuthCardList()
            .parallelStream()
            .collect(Collectors.toMap(SiteAuthCardLogPo::getEvseId, o -> o, (o, n) -> n));

        // redis 中桩的信息
        Map<String, EvseVo> evseRedisMap = redisIotReadService.getEvseList(new ArrayList<>(evseNoAuthCardMap.keySet()))
            .parallelStream()
            .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o, (o, n) -> n));

        // 网关编号-evse
        Map<String, List<EvseVo>> gwnoEvseMap = new HashMap<>();

        // 桩分类
        evseRedisMap.forEach((evseNo, evse) -> {
            if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
                log.warn("桩 {} 当前不在线, 发送失败", evseNo);
                // TODO 返回下发失败信息？
            } else {
                if (!gwnoVerMap.containsKey(evse.getGwno())) {
                    GwInfoPo gwInfoDto = gwCacheService.getGwInfo(evse.getGwno());
                    if (null == gwInfoDto) {
                        log.warn("桩 {} 找不到对应网关信息, 发送失败", evseNo);
                        // TODO 返回下发失败信息？
                    } else {
                        gwnoEvseMap.put(gwInfoDto.getGwno(), new ArrayList<>());
                        gwnoEvseMap.get(gwInfoDto.getGwno()).add(evse);

                        gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                        gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                        gwnoDownSeqMap.put(evse.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    }
                } else {
                    gwnoEvseMap.get(evse.getGwno()).add(evse);
                }
            }
        });

        gwnoEvseMap.forEach((gwno, evseList) -> {

            // 存入redis，等待网关消费
            this.cfgPairRedis(gwno, evseList.stream()
                .map(e -> {
                    CfgEvseV2 cfgEvseV2 = new CfgEvseV2();
                    cfgEvseV2.setCfgVer(gwnoDownVerMap.get(gwno));
                    if(evseNoAuthCardMap.get(e.getEvseNo()) != null &&
                        evseNoAuthCardMap.get(e.getEvseNo()).getCardNos() != null) {
                        cfgEvseV2.setLocalCards(evseNoAuthCardMap.get(e.getEvseNo())
                            .getCardNos()
                            .stream()
                            .map(v -> {
                                LocalCard ret = new LocalCard();
                                ret.setCardNumber(v.getCardNumber());
                                ret.setVisibleNumber(v.getVisibleNumber());
                                return ret;
                            }).collect(Collectors.toList()));
                    } else {
                        cfgEvseV2.setLocalCards(new ArrayList<>());
                    }

                    return Pair.of(e.getEvseNo(), cfgEvseV2);
                })
                .collect(Collectors.toList()));

            List<String> evseNos = evseList.stream().map(EvseVo::getEvseNo).collect(Collectors.toList());

            // 直接放入MQ下发
            IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
            MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
            mqEvseCfgCmd.setEvseNos(evseNos);
            mqEvseCfgCmd.setCfgVer(gwnoDownVerMap.get(gwno));
            cmd.setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                .setData(mqEvseCfgCmd);
            GwInfoPo gwInfo = gwCacheService.getGwInfo(gwno);
            this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            log.debug("下发到网关: {}-{}", gwInfo, JsonUtils.toJsonString(cmd));

            // 超时检测消息队列
            // 用作超时检测
            param.setEvseNoList(evseNos);
            IotGwDownCmd downCmd = new IotGwDownCmd();
            downCmd.setTtl(timeout)
                .setMsg(JsonUtils.toJsonString(param))
                .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                .setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setVer(gwnoVerMap.get(gwno));

            this.dcEventPublish.publishIotGwDownCmd(downCmd);
            log.debug("超时检测: {}", downCmd);

            log.info("网关 {} 已将数据放入MQ", gwno);
        });
    }

    /**
     * OCPP云端操作
     *
     * @param param
     */
    @Transactional
    public BaseResponse modifyEvseOperation(ModifyEvseOperationParam param) {
        // 网关编号 : 下发版本号 ver
        Map<String, String> gwnoDownVerMap = new HashMap<>();
        // 网关编号 : 下发序列号 seq
        Map<String, String> gwnoDownSeqMap = new HashMap<>();

        // 网关编号 : 网关版本
        Map<String, Integer> gwnoVerMap = new HashMap<>();

        List<String> evseNoList = param.getEvseNoList();
        // redis 中桩的信息
        Map<String, EvseVo> evseRedisMap = redisIotReadService.getEvseList(evseNoList)
            .parallelStream().collect(Collectors.toMap(EvseVo::getEvseNo, o -> o, (o, n) -> n));

        // 网关编号-evse
        Map<String, List<EvseVo>> gwnoEvseMap = new HashMap<>();

        // 桩分类
        evseRedisMap.forEach((evseNo, evse) -> {
            if (evse == null || evse.getStatus() == EvseStatus.OFFLINE) {
                log.warn("桩 {} 当前不在线, 发送失败", evseNo);
                // TODO 返回下发失败信息？
            } else {
                if (!gwnoVerMap.containsKey(evse.getGwno())) {
                    GwInfoPo gwInfoDto = gwCacheService.getGwInfo(evse.getGwno());
                    if (null == gwInfoDto) {
                        log.warn("桩 {} 找不到对应网关信息, 发送失败", evseNo);
                        // TODO 返回下发失败信息？
                    } else {
                        gwnoEvseMap.put(gwInfoDto.getGwno(), new ArrayList<>());
                        gwnoEvseMap.get(gwInfoDto.getGwno()).add(evse);

                        gwnoVerMap.put(evse.getGwno(), gwInfoDto.getVer());
                        gwnoDownVerMap.put(evse.getGwno(), this.sequenceRwService.getNextCfgVer());
                        gwnoDownSeqMap.put(evse.getGwno(),
                            this.sequenceRwService.getNextOutRequestSeq());
                    }
                } else {
                    gwnoEvseMap.get(evse.getGwno()).add(evse);
                }
            }
        });

        IotGwCmdType2 cmdType = IotGwCmdType2.CE_OCPP_CLOUD_OPERATIONS;
        gwnoEvseMap.forEach((gwno, evseList) -> {
            // 存入redis，等待网关消费
            this.cfgPairRedis(gwno, evseList.stream().map(e -> {
                CfgEvseV2 cfgEvseV2 = new CfgEvseV2();
                cfgEvseV2.setCfgVer(gwnoDownVerMap.get(gwno));
                cfgEvseV2.setOperationDto(param.getDto());

                return Pair.of(e.getEvseNo(), cfgEvseV2);
            }).collect(Collectors.toList()));

            List<String> evseNos = evseList.stream().map(EvseVo::getEvseNo)
                .collect(Collectors.toList());

            // 直接放入MQ下发
            IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
            MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
            mqEvseCfgCmd.setEvseNos(evseNos);
            mqEvseCfgCmd.setCfgVer(gwnoDownVerMap.get(gwno));
            cmd.setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setCmd(cmdType)
                .setData(mqEvseCfgCmd);
            GwInfoPo gwInfo = gwCacheService.getGwInfo(gwno);
            this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            log.debug("下发到网关: {}-{}", gwInfo, JsonUtils.toJsonString(cmd));

            // 超时检测消息队列
            // 用作超时检测
            param.setEvseNoList(evseNos);
            IotGwDownCmd downCmd = new IotGwDownCmd();
            downCmd.setTtl(timeout)
                .setMsg(JsonUtils.toJsonString(param))
                .setCmd(cmdType)
                .setGwno(gwno)
                .setSeq(gwnoDownSeqMap.get(gwno))
                .setVer(gwnoVerMap.get(gwno));

            this.dcEventPublish.publishIotGwDownCmd(downCmd);
            log.debug("超时检测: {}", downCmd);

            log.info("网关 {} 已将数据放入MQ", gwno);
        });
        return RestUtils.success();
    }

}
