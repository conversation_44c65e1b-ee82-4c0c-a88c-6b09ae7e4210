package com.cdz360.iot.worker.biz;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.rw.SimRwDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.type.NetworkVendor;
import com.cdz360.iot.model.sim.type.SimDeviceType;
import com.cdz360.iot.worker.ds.service.EvseService;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EvseBatchHandler implements BatchHandler<EvsePo> {

    private final Logger logger = LoggerFactory.getLogger(EvseBatchHandler.class);

    @Autowired
    private EvseService evseService;
    @Autowired
    private SimRwDs simRwDs;

    @Override
    public void save(List<EvsePo> list) {
        logger.info("桩状态同步到数据库。list: {}",
            list.stream().map(EvsePo::getEvseId).collect(Collectors.toList()));
        final boolean b = this.evseService.batchUpdate(list);

        this.associateSim(list);

        logger.info("桩状态同步到数据库完成。{}", b);
    }

    /**
     * 关联SIM卡
     *
     * @param list
     */
    public void associateSim(List<EvsePo> list) {
        try {
            Optional.ofNullable(list)
                .map(x -> x.stream().filter(e -> StringUtils.isNotBlank(e.getIccid()))
                    .map(e -> {
                        SimPo po = new SimPo();
                        po.setIccid(e.getIccid())
                            .setSiteId(e.getSiteId())
                            .setDeviceType(SimDeviceType.EVSE);
                        return po;
                    }).collect(Collectors.toList()))
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(x -> {
                    simRwDs.batchUpdate(x, NetworkVendor.CU); // 通过iccid字段更新
                    logger.info("关联结束");
                });
        } catch (Exception ex) {
            logger.error("关联SIM卡失败 error: {}", ex.getMessage(), ex);
        }
    }

}
