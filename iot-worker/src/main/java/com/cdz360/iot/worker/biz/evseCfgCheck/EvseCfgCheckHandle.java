package com.cdz360.iot.worker.biz.evseCfgCheck;

import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseCfgRoDs;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CustomWarningDesc;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgCheckDto;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Service
public class EvseCfgCheckHandle {

    private final Logger logger = LoggerFactory.getLogger(EvseCfgCheckHandle.class);

    @Autowired
    private BusinessService businessService;
    @Autowired
    private EvseCfgRoDs evseCfgRoDs;
    @Autowired
    private BizDataCoreFeignClient dataCoreFeignClient;

    public Optional<EvseCfgCheckDto> preCheck(String traceId, String evseNo) {
        logger.info("[{}] check evseNo: {}", traceId, evseNo);
        Optional<Pair<Integer, CfgEvseAllV2>> cfgOptional = businessService.getCfgAndProtocol(
            evseNo);
        logger.debug("[{}] evseNo: {}, Redis-cfg: {}", traceId, evseNo, cfgOptional.toString());
        if (cfgOptional.isEmpty()) {
            logger.info("[{}] 未获取到redis中桩配置 evseNo: {}", traceId, evseNo);
            return Optional.empty();
        }
        CfgEvseAllV2 cfgDB = evseCfgRoDs.queryByEvseNo(evseNo);
        logger.debug("[{}] evseNo: {}, DB-cfg: {}", traceId, evseNo, cfgDB);
        if (cfgDB == null) {
            logger.info("[{}] 未获取到库中桩配置 evseNo: {}", traceId, evseNo);
            return Optional.empty();
        }
        EvseCfgCheckDto dto = new EvseCfgCheckDto();
        dto.setPair(cfgOptional.get());
        dto.setCfgDB(cfgDB);
        return Optional.of(dto);
    }

    public Mono<Boolean> check(String traceId, String evseNo, EvseCfgCheckDto dto) {
        IotAssert.isTrue(dto != null && dto.getPair() != null && dto.getCfgDB() != null, "入参不能为空");

        List<Field> ignoreFields = ignoreFields(dto.getPair().getFirst());

        List<String> warnDescList = List.of(CfgEvseAllV2.class.getDeclaredFields()).stream()
                .filter(fd -> {
                    try {
                        if (ignoreFields.contains(fd)) return false;

                        fd.trySetAccessible();
                        Object ofd = fd.get(dto.getPair().getSecond());
                        Object dfo = fd.get(dto.getCfgDB());
                        ofd = ofd instanceof String ? ((String) ofd).trim() : ofd;
                        dfo = dfo instanceof String ? ((String) dfo).trim() : dfo;

                        if (ofd != null && dfo != null && !ofd.equals(dfo)) {
                            logger.info("[{}] 检测到配置项不一致 evseNo: {}, ofd: {}, dfo: {}", traceId, evseNo, ofd, dfo);

                            return true;
                        }
                        fd.setAccessible(false);
                        return false;
                    } catch (Exception ex) {
                        logger.error("[{}] 比对桩配置时出错 evseNo: {}, error: {}", traceId, evseNo, ex.getMessage(), ex);
                        return false;
                    }
                }).map(fd -> {
                    return Arrays.stream(fd.getAnnotations()).filter(tag -> tag instanceof CustomWarningDesc)
                            .map(tag -> ((CustomWarningDesc) tag).desc())
                            .findFirst();
                }).filter(op -> op.isPresent())
                .map(op -> op.get())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warnDescList)) {
            logger.info("[{}] 检测到配置项不一致 evseNo = {}, 差异内容 = {}", traceId, evseNo, warnDescList);
            return dataCoreFeignClient.sendEvseCfgAlarm(evseNo, warnDescList.get(0))    // 暂时先只报第一个配置不同的配置项
                    .map(res -> Boolean.TRUE);
        } else {
            return Mono.just(Boolean.FALSE);
        }
    }

    private static List<Field> ignoreFields(int protocolVer) {
        try {
            if (protocolVer >= IotConstants.PROTOCOL_VERSION_370) {
                return List.of(CfgEvseAllV2.class.getDeclaredField("autoStop"));
            } else {
                return List.of(CfgEvseAllV2.class.getDeclaredField("bmsVer"),
                        CfgEvseAllV2.class.getDeclaredField("autoStop"),
                        CfgEvseAllV2.class.getDeclaredField("balanceMode"),
                        CfgEvseAllV2.class.getDeclaredField("combination"),
                        CfgEvseAllV2.class.getDeclaredField("heating"),
                        CfgEvseAllV2.class.getDeclaredField("batteryCheck")
                );
            }
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

}

