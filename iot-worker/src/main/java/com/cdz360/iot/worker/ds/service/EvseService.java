package com.cdz360.iot.worker.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.iot.dto.EvseFullDto;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.ds.ro.SiteTopologyRefRoDs;
import com.cdz360.iot.ds.ro.mapper.SiteRoMapper;
import com.cdz360.iot.ds.rw.EvsePasscodeRwDs;
import com.cdz360.iot.ds.rw.mapper.EvseOfflineRwMapper;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.ListEvseParam;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.param.ModifyEvseInfoParam;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.upgrade.UpdateTaskStatusEnum;
import com.cdz360.iot.model.evse.vo.OfflineEvseVo;
import com.cdz360.iot.model.order.type.EvseDebugMethod;
import com.cdz360.iot.model.param.OfflineEvseParam;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.type.TopologyType;
import com.cdz360.iot.worker.model.iot.Evse;
import com.cdz360.iot.worker.model.iot.EvseExt;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class EvseService {


    @Autowired
    private PlugService plugService;

    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;

    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private EvseOfflineRwMapper evseOfflineRwMapper;
    @Autowired
    private SiteRoMapper siteRoMapper;
    // @Autowired
    // private PlugMapper plugMapper;

    @Autowired
    private SiteTopologyRefRoDs siteTopologyRefRoDs;

    @Autowired
    private EvsePasscodeRwDs evsePasscodeRwDs;

    @Transactional
    public boolean addEvse(Evse evse) {
        log.info(">> evse = {}", evse);
        if (evse.getName() == null) {
            evse.setName("");
        }
        var ret = this.insert(evse);

        return ret;
    }

    public boolean update(EvsePo evse) {
        return this.evseRwQueryMapper.update(evse) > 0;
    }

    public boolean updateById(EvsePo evse) {
        return this.evseRwQueryMapper.updateById(evse) > 0;
    }

    @Transactional
    public boolean batchUpdate(List<EvsePo> evses) {
        return this.evseRwQueryMapper.batchUpdate(evses) > 0;
    }

    public boolean batchAddOfflineEvse(List<OfflineEvseVo> list) {
        return this.evseOfflineRwMapper.batchAddOfflineEvse(list) > 0;
    }

    public boolean updateOfflineBatch(ModifyEvseInfoParam param) {
        return this.evseOfflineRwMapper.updateOfflineBatch(param) > 0;
    }

    public boolean removeOfflineEvse(List<OfflineEvseParam> param) {
        return this.evseOfflineRwMapper.removeOfflineEvse(param) > 0;
    }

    public boolean unBindSite(EvsePo evse) {
        return this.evseRwQueryMapper.unBindSite(evse) > 0;
    }

    private boolean insert(EvsePo evse) {
        return this.evseRwQueryMapper.insert(evse) > 0;
    }

    public boolean removeByEvseId(String evseId) {
        return this.evseRwQueryMapper.removeByEvseId(evseId) > 0;
    }

    public EvsePo getEvseById(Long id) {
        return this.evseRoDs.getEvseById(id);
    }

    public EvsePo getEvsePo(String evseId, boolean lock) {
        return this.evseRwQueryMapper.getEvsePo(evseId, lock);
    }

    @Transactional
    public Evse getEvse(String evseId, boolean lock) {
        var evsePo = this.evseRwQueryMapper.getEvsePo(evseId, lock);
        if (evsePo == null) {
            return null;
        }
        Evse evse = new Evse();
        BeanUtils.copyProperties(evsePo, evse);
        var plugs = this.plugService.listPlug(evse.getEvseId(), null,
                null, null, lock);
        evse.setPlugs(plugs);
        return evse;
    }

    public EvsePo getByIccid(String iccid) {
        return this.evseRoDs.getByIccid(iccid);
    }

    public PlugPo getPlugPo(String evseId, int plugId, boolean lock) {
        return this.plugService.getPlug(evseId, plugId, lock);
    }

    /**
     * 根据场站ID查询桩列表. 仅列出状态 != "OFF" 的桩
     */
    public List<EvsePo> listBySiteId(String siteId) {
        return this.evseRwQueryMapper.listBySiteId(siteId);
    }

    public List<EvsePo> listEvse(List<EvseStatus> statusList, long start, int size) {
        return this.evseRwQueryMapper.listEvse(statusList, start, size);
    }

    public ListResponse<EvseFullDto> getEvseList(ListEvseParam param) {
        List<EvsePo> list = evseRwQueryMapper.getEvseList(param);
        List<EvseFullDto> res = new ArrayList<>();
        for (EvsePo temp : list) {
            EvseFullDto vo = new EvseFullDto();
            vo.setPlugList(new ArrayList<>())
                    .setEvseNo(temp.getEvseId())
                    .setSiteCommId(temp.getCommId())
                    .setSiteId(temp.getSiteId())
                    .setGwno(temp.getGwno())
                    .setStatus(temp.getEvseStatus())
                    .setBizStatus(temp.getBizStatus())
                    .setName(temp.getName())
                    .setPlugNum(temp.getPlugNum())
                    .setPriceCode(temp.getPriceCode())
                    .setSupplyType(temp.getSupply())
                    .setModelName(temp.getModel())
                    .setPower(temp.getPower())
                    .setVoltage(temp.getVoltage())
                    .setCurrent(temp.getCurrent())
                    .setProtocolVer(temp.getProtocolVer())
                    .setProtocol(temp.getProtocol())
                    .setFirmwareVer(temp.getFirmwareVer())
                    .setPc01Ver(temp.getPc01Ver())
                    .setPc02Ver(temp.getPc02Ver())
                    .setPc03Ver(temp.getPc03Ver())
                    .setUpdateTime(temp.getUpdateTime());
            SitePo sitePo = siteRoMapper.getSite(temp.getSiteId());
            if (sitePo != null) {
                vo.setSiteCommId(sitePo.getCommId());
            }
            res.add(vo);
        }
        if (Boolean.TRUE.equals(param.getWithPlugList())
                && CollectionUtils.isNotEmpty(list)) {
            // 返回枪头列表信息
            List<String> evseNos = list.stream().map(EvsePo::getEvseId).collect(Collectors.toList());
            ListPlugParam plugParam = new ListPlugParam();
            plugParam.setEvseNoList(evseNos).setStart(0L).setSize(2000);
            ListResponse<PlugVo> plugRes = plugService.getPlugList(plugParam);
            Map<String, EvseFullDto> evseMap = res.stream()
                    .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o));
            for (PlugVo p : plugRes.getData()) {
                EvseFullDto evse = evseMap.get(p.getEvseNo());
                evse.getPlugList().add(p);
            }

        }
        return new ListResponse<>(res);
    }

    public ListResponse<EvseExt> getEvseExtList(ListEvseParam param) {
        List<EvsePo> list = evseRwQueryMapper.getEvseList(param);
        List<EvseExt> res = new ArrayList<>();
        for (EvsePo temp : list) {
            EvseExt vo = new EvseExt();
            vo.setPlugList(new ArrayList<>())
                .setEvseNo(temp.getEvseId())
                .setSiteCommId(temp.getCommId())
                .setSiteId(temp.getSiteId())
                .setGwno(temp.getGwno())
                .setStatus(temp.getEvseStatus())
                .setBizStatus(temp.getBizStatus())
                .setName(temp.getName())
                .setPlugNum(temp.getPlugNum())
                .setPriceCode(temp.getPriceCode())
                .setSupplyType(temp.getSupply())
                .setModelName(temp.getModel())
                .setPower(temp.getPower())
                .setVoltage(temp.getVoltage())
                .setCurrent(temp.getCurrent())
                .setProtocolVer(temp.getProtocolVer())
                .setProtocol(temp.getProtocol())
                .setFirmwareVer(temp.getFirmwareVer())
                .setPc01Ver(temp.getPc01Ver())
                .setPc02Ver(temp.getPc02Ver())
                .setPc03Ver(temp.getPc03Ver())
                .setUpdateTime(temp.getUpdateTime());
            vo.setProduceDate(temp.getProduceDate());
            vo.setOpenForBusinessDate(temp.getOpenForBusinessDate());
            vo.setAccountNumber(temp.getAccountNumber());
            vo.setProductSN(temp.getPhysicalNo());
            SitePo sitePo = siteRoMapper.getSite(temp.getSiteId());
            if (sitePo != null) {
                vo.setSiteCommId(sitePo.getCommId());
            }
            res.add(vo);
        }
        if (Boolean.TRUE.equals(param.getWithPlugList())
            && CollectionUtils.isNotEmpty(list)) {
            // 返回枪头列表信息
            List<String> evseNos = list.stream().map(EvsePo::getEvseId).collect(Collectors.toList());
            ListPlugParam plugParam = new ListPlugParam();
            plugParam.setEvseNoList(evseNos).setStart(0L).setSize(2000);
            ListResponse<PlugVo> plugRes = plugService.getPlugList(plugParam);
            Map<String, EvseFullDto> evseMap = res.stream()
                .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o));
            for (PlugVo p : plugRes.getData()) {
                EvseFullDto evse = evseMap.get(p.getEvseNo());
                evse.getPlugList().add(p);
            }

        }
        return new ListResponse<>(res);
    }

    /**
     * 分页查询在线且未激活的桩列表
     *
     * @param param
     * @return
     */
    public ListResponse<EvseVo> getOnlineAndNotBoundEvseList(BaseListParam param) {
        List<EvsePo> list = evseRwQueryMapper.getOnlineAndNotBoundEvseList(param);
        Long total = evseRwQueryMapper.getOnlineAndNotBoundEvseListCount();
        List<EvseVo> res = new ArrayList<>();
        for (EvsePo temp : list) {
            EvseVo vo = new EvseVo();
            vo.setEvseNo(temp.getEvseId())
                    .setSiteCommId(temp.getCommId())
                    .setSiteId(temp.getSiteId())
                    .setGwno(temp.getGwno())
                    .setStatus(temp.getEvseStatus())
                    .setName(temp.getName())
                    .setPlugNum(temp.getPlugNum())
                    .setPriceCode(temp.getPriceCode())
                    .setSupplyType(temp.getSupply())
                    .setPower(temp.getPower())
                    .setProtocolVer(temp.getProtocolVer())
                    .setProtocol(temp.getProtocol())
                    .setFirmwareVer(temp.getFirmwareVer())
                    .setPc01Ver(temp.getPc01Ver())
                    .setPc02Ver(temp.getPc02Ver())
                    .setPc03Ver(temp.getPc03Ver())
                    .setUpdateTime(temp.getUpdateTime());
            SitePo sitePo = siteRoMapper.getSite(temp.getSiteId());
            if (sitePo != null) {
                vo.setSiteCommId(sitePo.getCommId());
            }
            res.add(vo);
        }
        return new ListResponse<EvseVo>(res, total);
    }

    public List<EvsePo> listBySiteIdForUpgrade(ListEvseParam param) {
        List<EvsePo> list = evseRwQueryMapper.listBySiteIdForUpgrade(param);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, UpdateTaskStatusEnum> temp = evseRwQueryMapper.getUpgradeStatus(
                    list.stream().map(EvsePo::getEvseId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(EvsePo::getEvseId, EvsePo::getUpgradeStatus));
            list.forEach(e -> {
                UpdateTaskStatusEnum upgradeStatus = temp.get(e.getEvseId());
                if (upgradeStatus != null) {
                    e.setUpgradeStatus(upgradeStatus);
                }
            });
        }
        return list;
    }

    public int updateDebugTag(String evseId, EvseDebugMethod evseDebugMethod) {
        Boolean debugTag = null;
        switch (evseDebugMethod) {
            case ON:
                debugTag = true;
                break;
            case OFF:
                debugTag = false;
                break;
        }
        return evseRwQueryMapper.updateDebugTag(evseId, debugTag);
    }

    public boolean clearSimByIccid(String iccid) {
        return evseRwQueryMapper.clearSimByIccid(iccid) > 0;
    }

    /**
     * 批量更新桩长效密钥
     *
     * @param evseNoList
     * @param passcode
     * @return Map: key -- 桩编号，value -- 长效密钥版本号
     */
    public Map<String, Long> batchUpdateEvsePasscode(List<String> evseNoList, String passcode) {
        Map<String, Long> result = new HashMap<>();

        evseNoList.parallelStream().forEach(
                evseNo -> result.put(evseNo, evsePasscodeRwDs.updateEvsePasscode(evseNo, passcode)));

        return result;
    }

    @Transactional
    public List<EvsePo> getEvseList4Transformer(ListEvseParam param) {
        List<SiteTopologyRefPo> refList = this.siteTopologyRefRoDs.getByUpIdList(TopologyType.TRANSFORMER, List.of(param.getTfmId()));
        List<Long> evseIdList = new ArrayList<>();
        Set<Long> meterIdList = new HashSet<>();
        for (SiteTopologyRefPo ref : refList) {
            if (TopologyType.EVSE == ref.getDownType()) {
                evseIdList.add(ref.getDownId());
            } else if (TopologyType.METER == ref.getDownType()) {
                meterIdList.add(ref.getDownId());
            } else {
                log.info("下级类型 {} 忽略. tfmId = {}", ref.getDownType(), ref.getUpId());
            }
        }
        if(CollectionUtils.isNotEmpty(meterIdList)) {
            List<SiteTopologyRefPo> refMeterList = this.siteTopologyRefRoDs.getByUpIdList(TopologyType.METER,
                    meterIdList.stream().collect(Collectors.toList()));
            for (SiteTopologyRefPo ref : refMeterList) {
                if (TopologyType.EVSE == ref.getDownType()) {
                    evseIdList.add(ref.getDownId());
                }  else {
                    log.info("下级类型 {} 忽略. tfmId = {}", ref.getDownType(), ref.getUpId());
                }
            }
        }
        if (CollectionUtils.isEmpty(evseIdList)) {
            return new ArrayList<>();
        }

        com.cdz360.iot.model.evse.param.ListEvseParam paramx = new com.cdz360.iot.model.evse.param.ListEvseParam();
        paramx.setSiteIdList(param.getSiteIdList())
                .setEvseIdList(evseIdList)
                .setStart(param.getStart())
                .setSize(param.getSize());
        List<EvsePo> evseList = evseRoDs.getEvseList(paramx);


        return evseList;
    }
}
