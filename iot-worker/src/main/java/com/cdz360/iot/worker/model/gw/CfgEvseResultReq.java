package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.type.CfgEvseResultType;

public class CfgEvseResultReq extends CfgEvseReq {
    CfgEvseResultType result;
    private Integer whiteCardsResult;//紧急充电卡配置结果,0x00: 成功 其他表示失败
    private Integer adminCodeResult;//管理员账号配置结果,0x00: 成功 其他表示失败
    private Integer triggerResult;//各种开关项配置结果,0x00: 成功 其他表示失败
    private Integer chargeResult;//电价配置结果,0x00: 成功 其他表示失败
    private Integer qrResult;//二维码配置结果,0x00: 成功 其他表示失败

    public CfgEvseResultType getResult() {
        return result;
    }

    public CfgEvseResultReq setResult(CfgEvseResultType result) {
        this.result = result;
        return this;
    }

    public Integer getWhiteCardsResult() {
        return whiteCardsResult;
    }

    public void setWhiteCardsResult(Integer whiteCardsResult) {
        this.whiteCardsResult = whiteCardsResult;
    }

    public Integer getAdminCodeResult() {
        return adminCodeResult;
    }

    public void setAdminCodeResult(Integer adminCodeResult) {
        this.adminCodeResult = adminCodeResult;
    }

    public Integer getTriggerResult() {
        return triggerResult;
    }

    public void setTriggerResult(Integer triggerResult) {
        this.triggerResult = triggerResult;
    }

    public Integer getChargeResult() {
        return chargeResult;
    }

    public void setChargeResult(Integer chargeResult) {
        this.chargeResult = chargeResult;
    }

    public Integer getQrResult() {
        return qrResult;
    }

    public void setQrResult(Integer qrResult) {
        this.qrResult = qrResult;
    }
}
