package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "桩状态上报请求 v2")
public class EvseStatusReqV2 {
    @Schema(description = "桩编号", example = "01012345678901")
    private String evseNo;

    @Schema(description = "桩状态", example = "IDLE")
    private EvseStatus evseStatus;

    @Schema(description = "桩温度", example = "86")
    private Integer temp;

    @Schema(description = "桩异常问题编码", example = "12")
    private Integer errorCode;

    @Schema(description = "桩异常问题描述", example = "桩门异常")
    private String error;

    @Data
    @Accessors(chain = true)
    @Schema(description = "枪头状态信息")
    public static class PlugStatusReq {

        @Schema(description = "枪头序号", example = "3")
        private int plugId;

        @Schema(description = "枪状态", example = "IDLE")
        private PlugStatus plugStatus;

        @Schema(description = "枪头温度", example = "86")
        private Integer temp;

        @Schema(description = "枪头异常问题编码", example = "12")
        private Integer errorCode;

        @Schema(description = "枪头异常问题描述", example = "枪锁异常")
        private String error;

    }
}
