package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonProperty;

public class RegisterReq extends BaseObject {


    private String seq;

    @JsonProperty(value = "n")
    private String gwno;

    private String mac;

    private String lanIp;

    public String getSeq() {
        return seq;
    }

    public RegisterReq setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getGwno() {
        return gwno;
    }

    public RegisterReq setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public String getMac() {
        return mac;
    }

    public RegisterReq setMac(String mac) {
        this.mac = mac;
        return this;
    }

    public String getLanIp() {
        return lanIp;
    }

    public RegisterReq setLanIp(String lanIp) {
        this.lanIp = lanIp;
        return this;
    }
}
