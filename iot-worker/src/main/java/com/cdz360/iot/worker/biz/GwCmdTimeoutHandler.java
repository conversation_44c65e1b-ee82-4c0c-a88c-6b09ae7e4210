package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.model.iot.IotGwCmdTimeout;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.evse.param.ModifyEvseCfgParam;
import com.cdz360.iot.model.evse.type.EvseCfgResult;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class GwCmdTimeoutHandler {

    @Autowired
    private EvseCfgBizService evseCfgBizService;

    @Autowired
    private SiteCtrlBizService siteCtrlBizService;

    @Autowired
    private EssCfgService essCfgService;

    @Autowired
    private EssUpgradeService essUpgradeService;

    public void handleTimeout(IotGwCmdTimeout event) {
        log.warn("网关下行指令响应超时. 指令: {}", event);

        // 配置下发超时检测
        if (event.getCmd() == IotGwCmdType2.CE_MODIFY_CFG) {
            // 更新下发结果
            log.info(">>下发桩配置超时处理 msg = {}", event.getMsg());

            // 变更下发的配置信息
            ModifyEvseCfgParam param = JsonUtils.fromJson(event.getMsg(), ModifyEvseCfgParam.class);
            Integer result = EvseCfgResult.TIMEOUT;
            CfgEvseResultReqV2 evseResult = new CfgEvseResultReqV2();
            if (CollectionUtils.isNotEmpty(param.getWhiteCards())) {
                evseResult.setWhiteCardsResult(result);
            } else {
                if (ObjectUtils.isEmpty(param.getPriceSchemeId())) {
                    evseResult.setAdminCodeResult(result);
                    evseResult.setTriggerResult(result);
                    evseResult.setQrResult(result);
                } else {
                    evseResult.setChargeResult(result);
                }
            }
            evseCfgBizService.resetEvseCfgAndUpdateCfgResult(param.getEvseNoList(),
                evseResult, event.getGwno());
            log.info("<<");
        }

        // 场站控制器离线监控
        if (null == event.getCmd()) {
            // 通知超时
            String ctrlNo = event.getMsg();
            siteCtrlBizService.siteCtrlOffline(ctrlNo);
        } else if (IotGwCmdType2.EMU_D_MODIFY_CFG.equals(event.getCmd())) {
            log.info(">>户储配置超时处理 msg = {}", event.getMsg());
            essCfgService.cfgDeliveryTimeout(event.getMsg());
        } else if (IotGwCmdType2.ESS_D_UPGRADE_OP.equals(event.getCmd())) {
            log.info(">>户储升级超时处理 msg = {}", event.getMsg());
            essUpgradeService.upgradeTimeout(event.getMsg());
        }
    }
}
