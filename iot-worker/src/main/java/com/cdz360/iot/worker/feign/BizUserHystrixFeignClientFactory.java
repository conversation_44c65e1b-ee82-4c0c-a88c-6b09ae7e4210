package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.auth.CusAuthReqEx;
import com.cdz360.iot.model.auth.CusAuthRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class BizUserHystrixFeignClientFactory implements FallbackFactory<BizUserFeignClient> {

    @Override
    public BizUserFeignClient apply(Throwable throwable) {
        //log.error("【服务熔断】。Service = {}, message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage());
        //log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable);
        return new BizUserFeignClient() {
            @Override
            public Mono<ObjectResponse<CusAuthRes>> auth( String passcode,
                                                          CusAuthReqEx cusAuthReq) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, BizUserFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super BizUserFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }
}
