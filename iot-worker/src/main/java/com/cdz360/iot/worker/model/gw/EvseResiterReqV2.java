package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.base.vo.RangeVo;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.dto.PcVerDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "网关上报桩注册请求")
public class EvseResiterReqV2 {

    @Schema(description = "桩编号", example = "01012345678901")
    private String evseNo;

    @Schema(description = "桩协议类型", example = "DC")
    private EvseProtocolType protocol;

    @Schema(description = "桩协议版本号", example = "305")
    private Integer protocolVer;

    @Schema(description = "桩额定功率", example = "40.12")
    private BigDecimal power;

    @Schema(description = "电价模板编号", example = "1015")
    private Long priceCode;

    @Schema(description = "供电类型", example = "AC")
    private SupplyType supplyType;

    @Schema(description = "网络类型", example = "GSM")
    private NetType netType;

    @Schema(description = "DTU类型", example = "1", format = "java.lang.Integer")
    private DtuType dtuType;

    @Schema(description = "桩IP", example = "***************")
    private String evseIp;

    @Schema(description = "SIM卡号", example = "123456789A123456789B")
    private String iccid;

    @Schema(description = "国际移动用户识别码,15位数字", example = "123456789012345")
    private String imsi;

    @Schema(description = "移动设备国际识别码,15位数字", example = "123456789012345")
    private String imei;

    @Schema(description = "上电标志位(注册原因)", example = "1")
    private EvseRegisterReason registerReason;

    @Schema(description = "枪头数量", example = "4")
    private Integer plugNum;

    @Schema(description = "桩接入密码版本", example = "37")
    private Long passcodeVer;

    @Schema(description = "固件版本")
    private String firmwareVer;

    @Schema(description = "PC板版本信息")
    private List<PcVerDto> pcVer;

    @Schema(description = "枪头信息")
    private List<PlugRegisterReq> plugs;

    @Data
    @Accessors(chain = true)
    @Schema(description = "枪头注册信息")
    public static class PlugRegisterReq {

        @Schema(description = "枪头序号", example = "3")
        private int plugId;

        @Schema(description = "枪头功率", example = "40.12")
        private BigDecimal power;

        @Schema(description = "枪头最低/最高电压")
        private RangeVo voltage;

        @Schema(description = "枪头最低/最高电流")
        private RangeVo current;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
