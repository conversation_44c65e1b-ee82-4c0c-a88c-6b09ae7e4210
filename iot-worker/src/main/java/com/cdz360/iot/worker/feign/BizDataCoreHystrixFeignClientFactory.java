package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.BsBoxPo;
import com.cdz360.iot.model.evse.cfg.ChargeV2;
import com.cdz360.iot.model.evse.param.ModifyEvseCfgParam;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.model.dongzheng.BsBoxSettingPo;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgResendDto;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class BizDataCoreHystrixFeignClientFactory implements
    FallbackFactory<BizDataCoreFeignClient> {

    @Override
    public BizDataCoreFeignClient apply(Throwable throwable) {
        //log.error("【服务熔断】。Service = {}, message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, throwable.getMessage());
        //log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, throwable);
        return new BizDataCoreFeignClient() {
            @Override
            public Mono<ObjectResponse<OssStsDto>> getSts() {
                log.error("【服务熔断】。Service = {}, 获取文件上传的STS信息",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<OssStsDto>> getArchiveSts() {
                log.error("【服务熔断】。Service = {}, 获取文件上传的STS信息",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> batchUpdateBoxSettingStatus(List<BsBoxSettingPo> poList) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> updateBsBox(BsBoxPo bsBox) {
                log.error("【服务熔断】。Service = {}, bsBox = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, bsBox);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<ChargePriceVo>> getChargePrice(Long priceId) {
                log.error("【服务熔断】。Service = {}, priceId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, priceId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ChargeV2>> getChargeV2List(List<Long> priceIdList) {
                log.error("【服务熔断】。Service = {}, priceIdList.size = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, priceIdList.size());
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> sendPriceSchema(String evseNo) {
                log.error("【服务熔断】。Service = {}, evseNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, evseNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> priceTempDownDefault(String evseNo) {
                log.error("【服务熔断】。Service = {}, evseNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, evseNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ModifyEvseCfgParam>> getResendEvseCfgParam(
                EvseCfgResendDto dto) {
                log.error("【服务熔断】。Service = {}, dto = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> downSettingByEvse(String evseNo) {
                log.error("【服务熔断】。Service = {}, evseNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, evseNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> downLastSettingByEvse(String evseNo) {
                log.error("【服务熔断】。Service = {}, evseNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, evseNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> sendEvseCfgAlarm(String evseNo, String customWarningDesc) {
                log.error("【服务熔断】。Service = {}, evseNo = {}, customWarningDesc = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, evseNo, customWarningDesc);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> setPlugOutTime(String orderNo) {
                log.error("【服务熔断】。Service = {}, orderNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, orderNo);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, BizDataCoreFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super BizDataCoreFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}