package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = IotDeviceMgmHystrixFeignClientFactory.class)
public interface IotDeviceMgmFeignClient {

    @PostMapping("/device/upgrade/upgradeEvseResult")
    Mono<BaseResponse> upgradeEvseResult(@RequestBody UpgradeEvseResultRequest req);
}
