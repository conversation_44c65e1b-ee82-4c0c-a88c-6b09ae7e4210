package com.cdz360.iot.worker.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.CommonRpcResponse;
import com.cdz360.iot.worker.biz.SiteCtrlBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Classname SiteCtrlBusinessController
 * @Description
 * @Date 4/22/2020 1:42 PM
 * @Created by Rafael
 */
@Slf4j
@RestController
@Tag(name = "物联网场站控制器相关接口", description = "场站控制器服务")
@RequestMapping("/iot/siteCtrl")
public class SiteCtrlBusinessController {

    @Autowired
    private SiteCtrlBizService siteCtrlBizService;

    @PostMapping(value = "/cfg/get")
    @Operation(summary = "云端获取控制器配置")
    public BaseResponse cfgGet(@RequestParam(value = "ctrlNum") String ctrlNum) {
        log.info("云端获取控制器配置。ctrlNum: {}", ctrlNum);
        siteCtrlBizService.cfgGet(ctrlNum);
        log.info("<<");
        return RestUtils.success();
    }

    @PostMapping(value = "/cfg/send")
    @Operation(summary = "云端下发控制器配置")
    public BaseRpcResponse cfgSend(@RequestParam String ctrlNum) {
        log.info("云端下发控制器配置。ctrlNum: {}", ctrlNum);
        IotAssert.isNotBlank(ctrlNum, "控制器编号不能为空");
        siteCtrlBizService.doCtrlCfg(ctrlNum);
        log.info("<<");
        return BaseRpcResponse.newInstance();
    }

    @GetMapping(value = "/cfg/getInfo")
    @Operation(summary = "云端从缓存获取控制器配置")
    public CommonRpcResponse<Object> cfgGetInfo(@RequestParam String ctrlNum) {
        log.info("云端从缓存获取控制器配置。ctrlNum: {}", ctrlNum);
        //TODO raf 待实现
        log.info("<<");
        return new CommonRpcResponse();
    }


    @PostMapping(value = "/reboot")
    @Operation(summary = "云端重启控制器")
    public BaseResponse reboot(@RequestParam(value = "ctrlNum") String ctrlNum) {
        log.info("云端重启控制器。ctrlNum: {}", ctrlNum);
        IotAssert.isNotBlank(ctrlNum, "控制器编号不能为空");
        siteCtrlBizService.doReboot(ctrlNum);
        log.info("<<");
        return BaseResponse.success();
    }

    @PostMapping(value = "/cfg/checkTimeout")
    BaseResponse siteCtrlCfgTimeout(@RequestParam("bufferTime") Integer bufferTime) {
        log.info("检查控制器配置下发后状态。bufferTime: {}", bufferTime);
        siteCtrlBizService.cfgCheckTimeout(bufferTime);
        log.info("<<");
        return BaseResponse.success();
    }

}