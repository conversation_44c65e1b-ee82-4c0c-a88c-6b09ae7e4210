package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.type.CfgEvseResultType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
public class CfgEvseResultReqV2 extends BaseObject {

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "配置内容版本号")
    private String cfgVer;

    @Schema(description = "配置更新结果. SUCCESS: 成功; FAIL: 失败; TIMEOUT: 超时")
    private CfgEvseResultType result;

    @Schema(description = "管理员账号配置结果,0x00: 成功 其他表示失败")
    private Integer adminCodeResult;//管理员账号配置结果,0x00: 成功 其他表示失败

    @Schema(description = "各种开关项配置结果,0x00: 成功 其他表示失败")
    private Integer triggerResult;//各种开关项配置结果,0x00: 成功 其他表示失败

    @Schema(description = "紧急充电卡配置结果,0x00: 成功 其他表示失败")
    private Integer whiteCardsResult;//紧急充电卡配置结果,0x00: 成功 其他表示失败

    @Schema(description = "电价配置结果,0x00: 成功 其他表示失败")
    private Integer chargeResult;//电价配置结果,0x00: 成功 其他表示失败

    @Schema(description = "二维码配置结果,0x00: 成功 其他表示失败")
    private Integer qrResult;//二维码配置结果,0x00: 成功 其他表示失败

    // OCPP 云端配置下发结果
    @Schema(description = "获取配置结果,0x00: 成功 其他表示失败")
    private Integer getConfigResult;
    
    @Schema(description = "获取到的配置")
    private OcppConfigResult ocppConfigResult;
    
    @Schema(description = "更改配置结果,0x00: 成功 其他表示失败")
    private Integer changeConfigResult;

    @Schema(description = "获取诊断日志结果,0x00: 成功 其他表示失败")
    private Integer getDiagnosticsResult;

    @Schema(description = "诊断日志文件名")
    private String getDiagnosticsFileName;
    
    @Schema(description = "变更桩可用性结果,0x00: 成功 其他表示失败")
    private Integer changeAvailabilityResult;

    @Schema(description = "清除缓存结果,0x00: 成功 其他表示失败")
    private Integer clearCacheResult;
    
    @Schema(description = "触发消息结果,0x00: 成功 其他表示失败")
    private Integer triggerMessageResult;
    
    @Schema(description = "解锁桩结果,0x00: 成功 其他表示失败")
    private Integer unlockConnectorResult;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
