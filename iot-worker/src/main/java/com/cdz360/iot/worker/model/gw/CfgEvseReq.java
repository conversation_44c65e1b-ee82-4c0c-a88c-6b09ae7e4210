package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.base.IotPackageType;

public class CfgEvseReq extends BaseObject {
    private IotPackageType type = IotPackageType.REQ;

    private String seq;

    private String method = "EVSE_CFG";

    private String evseId;

    private String cfgVer;

    public IotPackageType getType() {
        return type;
    }

    public void setType(IotPackageType type) {
        this.type = type;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getEvseId() {
        return evseId;
    }

    public void setEvseId(String evseId) {
        this.evseId = evseId;
    }

    public String getCfgVer() {
        return cfgVer;
    }

    public void setCfgVer(String cfgVer) {
        this.cfgVer = cfgVer;
    }
}
