package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonProperty;

public class RegisterRes extends BaseObject {

    @JsonProperty(value = "n")
    private String gwno;

    @JsonProperty(value = "password")
    private String passcode;

//    private String mqClientId;
//
//    private String mqUrl;
//
//    private String mqUsername;
//
//    @JsonProperty(value = "mqPassword")
//    private String mqPasscode;
//
//    private String mqTopic;


    public String getGwno() {
        return gwno;
    }

    public RegisterRes setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public String getPasscode() {
        return passcode;
    }

    public RegisterRes setPasscode(String passcode) {
        this.passcode = passcode;
        return this;
    }

//    public String getMqClientId() {
//        return mqClientId;
//    }
//
//    public RegisterRes setMqClientId(String mqClientId) {
//        this.mqClientId = mqClientId;
//        return this;
//    }
//
//    public String getMqUrl() {
//        return mqUrl;
//    }
//
//    public RegisterRes setMqUrl(String mqUrl) {
//        this.mqUrl = mqUrl;
//        return this;
//    }
//
//    public String getMqUsername() {
//        return mqUsername;
//    }
//
//    public RegisterRes setMqUsername(String mqUsername) {
//        this.mqUsername = mqUsername;
//        return this;
//    }
//
//    public String getMqPasscode() {
//        return mqPasscode;
//    }
//
//    public RegisterRes setMqPasscode(String mqPasscode) {
//        this.mqPasscode = mqPasscode;
//        return this;
//    }
//
//    public String getMqTopic() {
//        return mqTopic;
//    }
//
//    public RegisterRes setMqTopic(String mqTopic) {
//        this.mqTopic = mqTopic;
//        return this;
//    }
}
