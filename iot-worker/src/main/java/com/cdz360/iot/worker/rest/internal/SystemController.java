package com.cdz360.iot.worker.rest.internal;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.base.ListResponse;
import com.cdz360.iot.worker.biz.QueueInfo;
import com.cdz360.iot.worker.biz.QueueMonitor;
import com.cdz360.iot.worker.biz.RedisIotUpdateWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "系统相关接口", description = "系统")
@RequestMapping(value = "/sys", produces = MediaType.APPLICATION_JSON_VALUE)
public class SystemController {
    private final Logger logger = LoggerFactory.getLogger(SystemController.class);

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;

    @Autowired
    QueueMonitor queueMonitor;

    @GetMapping(value = "/healthCheck.json")
    public CommonResponse<String> healthCheck(@RequestParam(required = false) String param,
                                              @RequestParam(required = false) Long sleep) {
        logger.info(">> param = {}", param);
        StringBuilder buf = new StringBuilder();
        buf.append("gewljgelwjglew");
        if (param != null) {
            buf.append(": ").append(param);
        }
        if (sleep != null && sleep > 0L) {
            try {
                Thread.sleep(sleep * 1000);
            } catch (InterruptedException e) {
                logger.error("error = {}", e.getMessage(), e);
            }
        }
        var res = new CommonResponse<>(buf.toString());
        logger.info("<<");
        return res;
    }


    @GetMapping(value = "/refreshRedis.json")
    public BaseResponse refreshRedis(@RequestParam(required = false) List<String> siteIds) {
        logger.warn("重置redis缓存");
        this.redisIotUpdateWrapper.buildRedisCache(siteIds);
        return new BaseResponse();
    }

    @PostMapping(value = "/test")
    public ObjectResponse<String> test() {
        return new ObjectResponse<>("aaa");
    }

    @GetMapping(value = "/dumpQueue")
    public ListResponse<QueueInfo> dumpQueue() {
        List<QueueInfo> list = queueMonitor.dump();
        return new ListResponse<>(list);
    }
}
