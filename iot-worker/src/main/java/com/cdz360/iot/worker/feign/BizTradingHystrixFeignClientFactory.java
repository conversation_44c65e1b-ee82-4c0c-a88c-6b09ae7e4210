package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.base.OrderCreateResponseV2;
import com.cdz360.iot.model.evse.*;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultLimitSoc;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultStartCharge;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;


@Slf4j
@Component
public class BizTradingHystrixFeignClientFactory implements FallbackFactory<BizTradingFeignClient> {

    @Override
    public BizTradingFeignClient apply(Throwable throwable) {
        //log.error("【服务熔断】。Service = {}, message = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING, throwable.getMessage());
        //log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING, throwable);
        return new BizTradingFeignClient() {
            /**
             * 开启充电
             *
             * @param chgEvseReq
             * @return
             */
            @Override
            public Mono<ObjectResponse<OrderCreateResponseV2>> orderCreate(ChgEvseRequest chgEvseReq) {
                log.error("【服务熔断】。Service = {}, 桩端创建订单 evseNo = {}, plugId = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, chgEvseReq.getEvseNo(), chgEvseReq.getPlugId());
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }


            @Override
            public Mono<BaseResponse> orderStarting(IotGwCmdResultStartCharge iotGwCmdResultStartCharge) {
                log.error("【服务熔断】。Service = {}, 充电启动中上报 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, iotGwCmdResultStartCharge.getOrderNo());
                return Mono.just(RestUtils.serverBusy());
            }

            /**
             * 开始充电状态上报
             *
             * @return
             */
            @Override
            public Mono<BaseResponse> orderStart(OrderStartRequest orderStartReq) {
                log.error("【服务熔断】。Service = {}, 充电开始上报 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderStartReq.getOrderNo());
                return Mono.just(RestUtils.serverBusy());
            }

            /**
             * 充电中数据上报
             *
             * @return
             */
            @Override
            public Mono<BaseResponse> orderUpdate(OrderUpdateRequestV2 orderUpdateReq) {
                log.error("【服务熔断】。Service = {}, 订单信息更新 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderUpdateReq.getOrderNo());
                return Mono.just(RestUtils.serverBusy());
            }

            /**
             * 结束充电
             *
             * @return
             */
            @Override
            public Mono<BaseResponse> orderStop(OrderStopRequestV2 orderStopReqV2) {
                log.error("【服务熔断】。Service = {} 停止充电 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderStopReqV2.getOrderNo());
                return Mono.just(RestUtils.serverBusy());
            }


            /**
             * 校验订单是否正常结束
             *
             * @param orderNo
             * @return
             */
            @Override
            public Mono<BaseResponse> handleErrorOrder(String orderNo,
                                                       OrderAbnormalReason abnormalReason) {
                log.error("【服务熔断】。Service = {}, 异常订单处理 orderNo = {}, abnormalReason = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderNo, abnormalReason);
                return Mono.just(RestUtils.serverBusy());
            }


            /**
             * 充电续费
             *
             * @return
             */
            @Override
            public Mono<ObjectResponse<OrderFeeRefreshResponseV2>> orderFeeRefresh(OrderFeeRefreshRequestV2 orderFeeRefreshReq) {
                log.error("【服务熔断】。Service = {} 订单续费 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderFeeRefreshReq.getOrderNo());
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            /**
             * 订单超停标记
             *
             * @param orderNo
             * @return
             */
            @Override
            public Mono<BaseResponse> overtimeParking(String orderNo) {
                log.error("【服务熔断】。Service = {} 标记超停 orderNo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, orderNo);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> crashStopRecovery(String evseNo, Integer plugId) {
                log.error("【服务熔断】。Service = {} 急停恢复后处理逻辑 evseNo = {}, plugId = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, evseNo, plugId);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> limitSocFeedback(IotGwCmdResultLimitSoc req) {
                log.error("【服务熔断】。Service = {} 急停恢复后处理逻辑 req = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_TRADING, req);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, BizTradingFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super BizTradingFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING);
        return null;
    }
}
