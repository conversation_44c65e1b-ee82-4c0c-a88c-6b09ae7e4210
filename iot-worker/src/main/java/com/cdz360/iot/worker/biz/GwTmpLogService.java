package com.cdz360.iot.worker.biz;

import com.cdz360.iot.worker.ds.mapper.GwLogTmpMapper;
import com.cdz360.iot.worker.model.iot.po.GwLogPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GwTmpLogService {
    private final Logger logger = LoggerFactory.getLogger(GwTmpLogService.class);

    @Autowired
    GwLogTmpMapper gwLogTmpMapper;

    public List<String> getGroupGwno() {
        return gwLogTmpMapper.getGroupGwno();
    }

    public List<GwLogPo> getLastTmpLog(String gwno) {
        logger.info("getLastTmpLog. gwno: {}", gwno);

        return gwLogTmpMapper.getLastTmpLog(gwno);
    }

    public int deleteBefore(String gwno, Long before) {
        logger.info("deleteBefore. gwno: {}, before: {}", gwno, before);
        return gwLogTmpMapper.deleteBefore(gwno, before);
    }

    // 从临时表移出数据到永久表
    public int copyOut(String gwno, Long before) {
        logger.info("copyOut. gwno: {}, before: {}", gwno, before);
        return gwLogTmpMapper.copyOut(gwno, before);
    }
}
