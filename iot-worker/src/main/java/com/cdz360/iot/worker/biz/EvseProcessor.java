package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.iot.type.ODMType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseMeterRoDs;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.ro.EvseReportModuleRoDs;
import com.cdz360.iot.ds.ro.SimRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.ro.SiteTopologyRefRoDs;
import com.cdz360.iot.ds.rw.EvseBindHistoryRwDs;
import com.cdz360.iot.ds.rw.EvseCfgResultRwDs;
import com.cdz360.iot.ds.rw.EvseCfgRwDs;
import com.cdz360.iot.ds.rw.EvseModelRwDs;
import com.cdz360.iot.ds.rw.EvseModuleRwDs;
import com.cdz360.iot.ds.rw.EvseReportModuleDetailRwDs;
import com.cdz360.iot.ds.rw.EvseReportModuleRwDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.evse.param.ModifyEvseInfoParam;
import com.cdz360.iot.model.evse.param.ReportModuleParam;
import com.cdz360.iot.model.evse.po.EvseCfgPo;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.evse.po.EvseReportModulePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.evse.vo.OfflineEvseVo;
import com.cdz360.iot.model.meter.po.DeviceMeterPo;
import com.cdz360.iot.model.param.AddEvseParam;
import com.cdz360.iot.model.param.EditEvseParam;
import com.cdz360.iot.model.param.OfflineEvseParam;
import com.cdz360.iot.model.sim.param.ListSimParam;
import com.cdz360.iot.model.sim.po.SimPo;
import com.cdz360.iot.model.sim.vo.SimTinyVo;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.topology.po.SiteTopologyRefPo;
import com.cdz360.iot.model.type.DeviceStatusCodeType;
import com.cdz360.iot.model.type.EvseHistoryActionType;
import com.cdz360.iot.model.type.TopologyType;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import com.cdz360.iot.worker.model.iot.Evse;
import com.cdz360.iot.worker.model.iot.UpdateEvseCacheDto;
import com.cdz360.iot.worker.model.iot.param.BindEvseParam;
import com.cdz360.iot.worker.model.iot.param.UnbindEvseParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

@Service
public class EvseProcessor {

    private static final List<PlugStatus> CRASH_STOP_PLUG_STATUS = List.of(
        PlugStatus.IDLE, PlugStatus.CONNECT);
    private static final List<Integer> CRASH_STOP_CODE = List.of(
        DeviceStatusCodeType.C1005.getCode(),
        DeviceStatusCodeType.C05.getCode());
    private static final Set<PlugStatus> NON_BUSY_STATUS = Set.of(PlugStatus.IDLE,
        PlugStatus.OFFLINE, PlugStatus.ERROR);
    private final Logger logger = LoggerFactory.getLogger(EvseProcessor.class);
    @Autowired
    private EvseBindHistoryRwDs evseBindHistoryRwDs;
    @Autowired
    private EvseService evseService;
    @Autowired
    private EvseProcessorAsync evseProcessorAsync;
    @Autowired
    private PlugService plugService;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private SimRoDs simRoDs;
    @Autowired
    private EvseModelRoDs evseModelRoDs;
    @Autowired
    private EvseModuleRwDs evseModuleRwDs;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;

//    @Autowired
//    private DzBChargerOrderClient dzBChargerOrderClient;
    @Autowired
    private DcEventPublisher dcEventPublish;
    private EvseQueue evseQueue;
    private PlugQueue plugQueue;
    @Autowired
    private EvseBatchHandler evseBatchHandler;
    @Autowired
    private PlugBatchHandler plugBatchHandler;
    @Autowired
    private QueueMonitor queueMonitor;
    @Autowired
    private EvseCfgRwDs evseCfgRwDs;
    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;
    @Autowired
    private BizDataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;
    @Autowired
    private EvseMeterRoDs evseMeterRoDs;
    @Autowired
    private SiteTopologyRefRoDs siteTopologyRefRoDs;
    @Autowired
    private EvseReportModuleRoDs evseReportModuleRoDs;
    @Autowired
    private EvseReportModuleRwDs evseReportModuleRwDs;
    @Autowired
    private EvseReportModuleDetailRwDs evseReportModuleDetailRwDs;
    @Autowired
    private GwCacheService gwCacheService;
    @Autowired
    private EvseModelRwDs evseModelRwDs;

    @PostConstruct
    public void init() {
        evseQueue = new EvseQueue();
        plugQueue = new PlugQueue();

        EvseQueueConsumer consumer = new EvseQueueConsumer(evseQueue, evseBatchHandler);
        PlugQueueConsumer queueConsumer = new PlugQueueConsumer(plugQueue, plugBatchHandler);

        new Thread(consumer).start();
        new Thread(queueConsumer).start();

        queueMonitor.addQueue(evseQueue);
        queueMonitor.addQueue(plugQueue);
    }


    public void processSiteCtrlEvseReport(List<EvseReportRequestV2> evseReportRequestV2,
        SiteCtrlPo siteCtrlPo,
        SitePo sitePo,
        boolean isAlert) {
        logger.debug(
            "来自控制器的桩报告: evseReportRequestV2-{}, siteCtrlPo-{}, sitePo-{}, isAlert-{}",
            evseReportRequestV2,
            siteCtrlPo,
            sitePo,
            isAlert);
        evseReportRequestV2.stream().forEach(e -> {
            EvsePo evsePo = null;
            EvseVo evseCache = redisIotReadService.getEvseRedisCache(e.getEvseNo());
            if (evseCache == null) {
                evsePo = evseService.getEvsePo(e.getEvseNo(), false);
                if (evsePo == null) {   // 数据库没有匹配到
                    logger.error("DB中没有找到该桩信息: evseNo = {}", e.getEvseNo());
                    return;
                }

                evseCache = new EvseVo();

                if (isAlert) {
                    evseCache.setAlertCode(e.getErrorCode());
                } else {
                    evseCache.setErrorCode(e.getErrorCode());
                }

                evseCache.setStatus(e.getEvseStatus())
                    .setName(evsePo.getName())
                    .setEvseNo(e.getEvseNo())
                    .setSiteCommId(sitePo.getCommId())
                    .setName(evsePo.getName())
                    .setSiteId(sitePo.getSiteId())
                    .setSiteName(sitePo.getName())
                    .setTemperature(e.getTemp());
            } else {
                evseCache.setStatus(e.getEvseStatus())
                    .setTemperature(e.getTemp())
                    .setSiteName(sitePo.getName())
                    .setSiteId(sitePo.getSiteId())
                    .setSiteCommId(sitePo.getCommId());
            }
            this.dcEventPublish.publishEvseInfo(IotEvent.STATE_CHANGE, evseCache, e.getLinkId(),
                e.getCtrlNo());
        });
    }

    public void processSiteCtrlPlugReport(EvseReportRequestV2 evseReportRequestV2,
        List<EvseReportRequestV2.Plug> plugReports,
        SiteCtrlPo siteCtrlPo,
        SitePo sitePo,
        boolean isAlert) {
        logger.debug(
            "来自控制器的枪报告: evseReportRequestV2-{}, plugReports-{}, siteCtrlPo-{}, sitePo-{}, isAlert-{}",
            evseReportRequestV2,
            plugReports,
            siteCtrlPo,
            sitePo,
            isAlert);
        if (CollectionUtils.isEmpty(plugReports) || StringUtils.isBlank(
            evseReportRequestV2.getEvseNo())) {
            return;
        }

        EvseVo evseRedisCache = redisIotReadService.getEvseRedisCache(
            evseReportRequestV2.getEvseNo());

        plugReports.stream().forEach(plugReport -> {

            PlugVo plugVo = new PlugVo();
            plugVo.setEvseNo(evseReportRequestV2.getEvseNo())
//                        .setGwno(evseReportRequestV2.getCtrlNo().getGwno())
                .setSiteId(sitePo.getSiteId())
                .setIdx(plugReport.getPlugId())
                .setPlugNo(PlugNoUtils.formatPlugNo(evseReportRequestV2.getEvseNo(),
                    plugReport.getPlugId()))
                .setErrorMsg(plugReport.getError())
                .setErrorCode(plugReport.getErrorCode())
                .setStatus(plugReport.getPlugStatus())
                .setTemperature(plugReport.getTemp());

            if (evseRedisCache != null) {
                plugVo.setEvseName(evseRedisCache.getName());
            }

            if (isAlert) {
                plugVo.setAlertCode(Optional.ofNullable(plugReport.getAlertCode())
                    .orElse(plugReport.getErrorCode()));
            }

            if (sitePo != null) {
                plugVo.setSiteName(sitePo.getName())
                    .setSiteCommId(sitePo.getCommId());
            }
            PlugMqDto plugMqDto = new PlugMqDto();
            if (plugVo != null) {
                BeanUtils.copyProperties(plugVo, plugMqDto);
            }
            this.dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE,
                plugMqDto,
                evseReportRequestV2.getLinkId(),
                evseReportRequestV2.getCtrlNo());
        });
    }


    @Transactional(isolation = Isolation.READ_COMMITTED)
    public Mono<Boolean> processEvseNormalStatusV2(EvseReportRequestV2 report, String gwno) {
        logger.info("桩状态上报。evse : {}", report);
        String evseNo = report.getEvseNo();
        EvseStatus evseStatus = report.getEvseStatus();
        IotAssert.isNotNull(evseNo, "桩号不能为空");
        IotAssert.isNotNull(evseStatus, "桩状态不能为空");
        GwInfoPo gwInfo = this.gwCacheService.getGwInfo(gwno);

        if (evseStatus != EvseStatus.ERROR && evseStatus != EvseStatus.OFFLINE) {
            IotAssert.isNotNull(report.getPlugs(), "状态正常时，充电枪数量不能为空");
        }

        List<PlugVo> plugList = null;
        //EvsePo evsePo = null;
        EvseVo evseCache = redisIotReadService.getEvseRedisCache(report.getEvseNo());
        if (evseCache == null) {
            evseCache = this.initEvseCacheData(report.getEvseNo());
        }
        if (evseCache == null) {
            logger.error("桩信息不存在, 无法处理桩状态上报. statusReport = {}", report);
            return Mono.just(Boolean.FALSE);
        }

        EvsePo evseUpdate = new EvsePo();
        evseUpdate.setEvseId(evseCache.getEvseNo())
            .setName(evseCache.getName())
            .setSiteId(evseCache.getSiteId())
            .setSupply(evseCache.getSupplyType())
            .setFirmwareVer(evseCache.getFirmwareVer())
            .setProtocolVer(evseCache.getProtocolVer())
            .setEvseStatus(report.getEvseStatus())
            .setGwno(evseCache.getGwno());

        if (null == evseCache.getBizStatus()) {
            evseCache.setBizStatus(EvseBizStatus.NORMAL);
            evseUpdate.setBizStatus(EvseBizStatus.NORMAL);
        }

        if (StringUtils.isBlank(evseUpdate.getGwno())) {
            evseCache.setGwno(gwno);
            evseUpdate.setGwno(gwno);
        }

        evseCache.setStatus(report.getEvseStatus()).setTemperature(report.getTemp());

        if (report.getSupply() == null) {
            evseUpdate.setSupply(report.getSupply());
        }
        //logger.info("更新桩状态到数据库。evse: {}", evsePo);
        //evseQueue.offer(evsePo);

        this.updateEvseV2(evseUpdate, null, null);

        if (StringUtils.isNotBlank(report.getLinkId())) {
            redisIotUpdateWrapper.updateRedisEvseCacheV2(IotEvent.STATE_CHANGE, evseCache,
                report.getLinkId(), report.getCtrlNo());
        } else {
            redisIotUpdateWrapper.updateRedisEvseCacheV2(gwInfo, IotEvent.STATE_CHANGE, evseCache);
        }

        // TODO 暂时这么改
        this.dcEventPublish.publishEvseInfo(IotEvent.STATE_CHANGE, evseCache, report.getLinkId(),
            report.getCtrlNo());

        return this.addOrUpdatePlugsV2(gwInfo, report, evseCache);


    }


    /**
     * 从数据库获取桩信息，填充到缓存数据结构里（仅3.4协议网关需要)
     *
     * @param evseNo
     * @return
     */
    private EvseVo initEvseCacheData(String evseNo) {
        EvsePo dbEvse = this.evseService.getEvse(evseNo, false);
        if (dbEvse == null) {
            return null;
        }
        EvseVo cache = new EvseVo();
        cache.setEvseNo(evseNo)
            .setBizStatus(dbEvse.getBizStatus())
            .setSiteCommId(dbEvse.getCommId())
            .setSiteId(dbEvse.getSiteId())
            .setGwno(dbEvse.getGwno())
            .setStatus(dbEvse.getEvseStatus())
            .setName(dbEvse.getName())
            .setPlugNum(dbEvse.getPlugNum())
            .setPriceCode(dbEvse.getPriceCode())
            .setSupplyType(dbEvse.getSupply())
            .setModelName(dbEvse.getModelName())
            .setPower(dbEvse.getPower())
            .setVoltage(dbEvse.getVoltage())
            .setCurrent(dbEvse.getCurrent())
            .setProtocolVer(dbEvse.getProtocolVer())
            .setProtocol(dbEvse.getProtocol())
            .setFirmwareVer(dbEvse.getFirmwareVer())
            .setPc01Ver(dbEvse.getPc01Ver())
            .setPc02Ver(dbEvse.getPc02Ver())
            .setPc03Ver(dbEvse.getPc03Ver())
            .setErrorCode(0)
            .setAlertCode(0);
        if (cache.getProtocolVer() == null) {
            cache.setProtocolVer(304);  // 3.5协议网关会在网关层写入redis缓存
            logger.info("桩协议不存在， 设置为 304");
        }
        if (StringUtils.isNotBlank(dbEvse.getSiteId())) {
            var site = this.siteRoDs.getSite(dbEvse.getSiteId());
            if (site != null) {
                cache.setSiteName(site.getName());
            } else {
                logger.error("场站不存在. siteId = {}", dbEvse.getSiteId());
            }
        }
        return cache;
    }


    private void updateEvseV2(EvsePo evse, SitePo site, String firmwareVer) {
        logger.info("修改桩状态。evse: {}", evse);
        if (NetType.UNKNOWN == evse.getNet()) {
            evse.setNet(null);  // 未知时不更新
        }
        if (EvseStatus.UNKNOWN == evse.getEvseStatus()) {
            evse.setEvseStatus(null);  // 未知时不更新
        }
        if (SupplyType.UNKNOWN == evse.getSupply()) {
            evse.setSupply(null);   // 未知时不更新
        }
        //logger.info("更新桩状态到数据库。evse: {}", evse);
        evseQueue.offer(evse);


    }


    public void removeByEvseId(String evseId) {
        logger.info("删除桩。 evseId: {}", evseId);
        // 删除桩信息
        this.evseService.removeByEvseId(evseId);
        // 删除枪信息
        this.plugService.removeByEvseId(evseId);
    }


    private Mono<Boolean> addOrUpdatePlugsV2(GwInfoPo gwInfo,
        EvseReportRequestV2 report, //@Nullable SitePo site,
        EvseVo evse) {
        logger.info("新增或者更新枪的状态。 report: {}", report);
        List<PlugVo> resultList = new ArrayList<>();
        List<PlugPo> newPlugList = new ArrayList<>();
        Mono<Boolean> mono = Mono.just(Boolean.TRUE);
        if (com.cdz360.iot.common.utils.CollectionUtils.isEmpty(report.getPlugs())) {
            logger.warn("枪头列表为空.  report = {}", report);
            return mono;
        }

        List<PlugPo> plugs = plugService.getPlugByEvseId(report.getEvseNo(), false);

        Map<Integer, PlugPo> plugMap = plugs.stream().collect(
            Collectors.toMap(PlugPo::getPlugId, plugPo -> plugPo, (key1, key2) -> key1));

        List<String> plugNoList = plugs.stream()
            .map(e -> PlugNoUtils.formatPlugNo(e.getEvseId(), e.getPlugId()))
            .collect(Collectors.toList());

        // 3.5 可以不关注 redis 中的数据
        List<PlugVo> redisPlug = redisIotReadService.getPlugList(plugNoList);

        Map<Integer, PlugVo> redisPlugMap = redisPlug.stream()
            .filter(p -> p != null && p.getIdx() != null && p.getIdx() > 0)   // 过滤掉redis里不存在的枪头
            .collect(Collectors.toMap(PlugVo::getIdx, e -> e));

        report.getPlugs().stream()
            .filter(p -> p.getPlugId() != null && p.getPlugId() > 0)    // 过滤掉枪头号为0的数据
            .forEach(plugEntity -> {
                PlugVo result = null;
                PlugPo plug = plugMap.get(plugEntity.getPlugId());
                PlugVo plugCache = redisPlugMap.get(plugEntity.getPlugId());
                if (plug == null) {
                    logger.info("枪头不存在。plug: {}", plugEntity);

                    plug = new PlugPo();
                    plug.setPlugId(plugEntity.getPlugId()).setName("")
                        .setPlugStatus(plugEntity.getPlugStatus())
                        .setEvseId(report.getEvseNo())
                        .setAlertCode(plugEntity.getAlertCode())
                        .setErrorMsg(plugEntity.getError())
                        .setErrorCode(plugEntity.getErrorCode())
                        .setGwno(evse.getGwno());
                    newPlugList.add(plug);

                    // 推送创建事件
                    result = redisIotUpdateWrapper.updateRedisPlugCacheStatusV2(IotEvent.CREATE,
                        plugEntity,
                        gwInfo,
                        evse,
                        plug,
                        report.getLinkId(),
                        report.getCtrlNo());

                } else if (((gwInfo != null && gwInfo.getMqType() == GwMqType.MQ_TYPE_MQTT)
                    || evse.getProtocolVer() < IotConstants.SERVFEE_TIME_DIVISION_PROTOVER)
                    // 3.5 中数据库和 redis 中的状态会不一致，需要判断
                    && plugCache != null
                    && plugCache.getStatus() != plugEntity.getPlugStatus()) {
                    // 之前使用数据库枪头状态，现在改成使用redis枪头状态
                    logger.info("枪头状态有变更。plug: {}", plug);

                    if (StringUtils.isBlank(plug.getGwno())) {
                        plug.setGwno(evse.getGwno());
                    }

                    // 急停结束
//                        this.postCrashStop(plug, plugCache, plugEntity);

                    plug.setPlugStatus(plugEntity.getPlugStatus())
                        .setAlertCode(plugEntity.getAlertCode())
                        .setErrorMsg(plugEntity.getError())
                        .setErrorCode(plugEntity.getErrorCode());

                    if (StringUtils.isNotBlank(plugCache.getOrderNo()) &&
                        (plugEntity.getPlugStatus() == PlugStatus.OFFLINE
                            || plugEntity.getPlugStatus() == PlugStatus.ERROR
                        )) {    // 新的状态为离线或异常，仅将超停计费结束

                        // 订单超停标记处理逻辑
                        // dzBChargerOrderClient.overtimeParking(redisPlugMap.get(plug.getPlugId()).getOrderNo());
                        PlugVo redisPlugX = redisPlugMap.get(plug.getPlugId());

                        this.bizTradingReactiveFeignClient.overtimeParking(redisPlugX.getOrderNo())
                            .subscribe();

                    } else if (StringUtils.isNotBlank(plugCache.getOrderNo()) &&
                        (plugEntity.getPlugStatus() == PlugStatus.IDLE
                            || plugEntity.getPlugStatus() == PlugStatus.CONNECT)) {

                        PlugVo redisPlugX = redisPlugMap.get(plug.getPlugId());

                        this.bizTradingReactiveFeignClient.overtimeParking(redisPlugX.getOrderNo())
                            .subscribe();

                        // 记录拔枪时间
                        this.dataCoreFeignClient.setPlugOutTime(redisPlugX.getOrderNo())
                            .subscribe();

                        // 清除枪头订单号
                        redisIotUpdateWrapper.unbindOrderNo(plug.getEvseId(), plug.getPlugId());
                    }

                    plugQueue.offer(plug);

                    // 推送创建更新事件
                    result = redisIotUpdateWrapper.updateRedisPlugCacheStatusV2(
                        IotEvent.STATE_CHANGE,
                        plugEntity,
                        gwInfo,
                        evse,
                        plug,
                        report.getLinkId(),
                        report.getCtrlNo());

                    this.exceptionOrderHandlerV2(evse.getEvseNo(), plugEntity, plugCache);
                } else if (plugMap.get(plug.getPlugId()) == null ||
                    plugMap.get(plug.getPlugId()).getPlugStatus() != plugEntity.getPlugStatus()
                ) {

//                     || !NumberUtils.equals(plugMap.get(plug.getPlugId()).getErrorCode(),
//                        plugEntity.getErrorCode()) // 故障码有变化，需要推送
                    logger.info("数据库枪头状态有变更。plug: {}", plug);

                    // 急停恢复处理
//                        this.postCrashStop(plug, plugEntity);

                    if (StringUtils.isBlank(plug.getGwno())) {
                        plug.setGwno(evse.getGwno());
                    }

                    if (plugCache == null) {
                        // redis 中缓存的枪头不存在
                        plugCache = redisIotUpdateWrapper.updateRedisPlugCacheStatusV2(
                            IotEvent.STATE_CHANGE,
                            plugEntity, gwInfo, evse, plug, report.getLinkId(), null);
                    }

                    // 急停结束
//                        this.postCrashStop(plug, plugCache, plugEntity);

                    plug.setPlugStatus(plugEntity.getPlugStatus())
                        .setAlertCode(plugEntity.getAlertCode())
                        .setErrorMsg(plugEntity.getError())
                        .setErrorCode(plugEntity.getErrorCode());

                    if (plugCache != null) {
                        plugCache.setStatus(plugEntity.getPlugStatus())
                            .setAlertCode(plugEntity.getAlertCode())
                            .setErrorMsg(plugEntity.getError())
                            .setErrorCode(plugEntity.getErrorCode());
                    }

                    if (plugCache != null && StringUtils.isNotBlank(plugCache.getOrderNo()) &&
                        (plugEntity.getPlugStatus() == PlugStatus.OFFLINE
                            || plugEntity.getPlugStatus() == PlugStatus.ERROR
                        )) {    // 新的状态为离线或异常，仅将超停计费结束

                        PlugVo redisPlugX = redisPlugMap.get(plug.getPlugId());

                        this.bizTradingReactiveFeignClient.overtimeParking(redisPlugX.getOrderNo())
                            .subscribe();

                    } else if (plugCache != null && StringUtils.isNotBlank(plugCache.getOrderNo())
                        &&
                        plugEntity.getPlugStatus() == PlugStatus.IDLE) {

                        PlugVo redisPlugX = redisPlugMap.get(plug.getPlugId());
                        this.bizTradingReactiveFeignClient.overtimeParking(redisPlugX.getOrderNo())
                            .subscribe();

                        // 记录拔枪时间
                        this.dataCoreFeignClient.setPlugOutTime(redisPlugX.getOrderNo())
                            .subscribe();

                        // 清除枪头订单号
                        redisIotUpdateWrapper.unbindOrderNo(plug.getEvseId(), plug.getPlugId());
                    }

                    // 仅仅更新桩状态值，其他字段没有更新
                    plugQueue.offer(plug);

                    result = plugCache;
                    //添加设备型号、桩软件
                    PlugMqDto plugMqDto = new PlugMqDto();
                    if (result != null) {
                        BeanUtils.copyProperties(result, plugMqDto);
                    }
                    plugMqDto.setModelName(evse.getModelName())
                        .setFirmwareVer(evse.getFirmwareVer());

                    if (StringUtils.isNotBlank(report.getLinkId())) {
                        // 推送枪头状态变动的消息
                        this.dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE, plugMqDto);
                    } else {
                        // 推送来自控制器 枪头状态变动的消息
                        this.dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE,
                            plugMqDto,
                            report.getLinkId(),
                            report.getCtrlNo());
                    }

                    this.exceptionOrderHandlerV2(evse.getEvseNo(), plugEntity, plugCache);

                } else if (evse.getProtocolVer() >= IotConstants.SERVFEE_TIME_DIVISION_PROTOVER) {
                    logger.info("3.5桩推送变更消息。plug: {}",
                        plug);// 数据库状态没变更，但是errorCode可能变更，所以要推送
                    result = redisPlugMap.get(plugEntity.getPlugId());
                    PlugMqDto plugMqDto = new PlugMqDto();
                    if (result != null) {
                        BeanUtils.copyProperties(result, plugMqDto);
                    }
                    plugMqDto.setFirmwareVer(evse.getFirmwareVer())
                        .setModelName(evse.getModelName());

                    if (StringUtils.isBlank(plug.getGwno())) {
                        plug.setGwno(evse.getGwno());
                    }

                    if (StringUtils.isNotBlank(report.getLinkId())) {
                        // 推送枪头状态变动的消息
                        this.dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE, plugMqDto);
                    } else {
                        // 推送来自控制器 枪头状态变动的消息
                        this.dcEventPublish.publishPlugInfo(IotEvent.STATE_CHANGE,
                            plugMqDto,
                            report.getLinkId(),
                            report.getCtrlNo());
                    }
                } else {
                    logger.info("枪头状态无变化。plug: {}", plug);
                }

                if (result != null) {
                    resultList.add(result);
                }
            });
        if (!CollectionUtils.isEmpty(plugs) && !CollectionUtils.isEmpty(newPlugList)) {
            this.plugService.batchInsert(newPlugList);
        }

        return Mono.just(Boolean.TRUE);
    }

    private void postCrashStop(PlugPo mysql, PlugVo cache, EvseReportRequestV2.Plug plug) {
        logger.debug("status in mysql = {}, error code = {}, report status = {}",
            mysql.getPlugStatus(), cache.getErrorCode(), plug.getPlugStatus());
        if (PlugStatus.ERROR.equals(mysql.getPlugStatus()) &&
//                cache.getErrorCode() != null &&
//                CRASH_STOP_CODE.contains(cache.getErrorCode()) &&
            null != plug.getPlugStatus() &&
            CRASH_STOP_PLUG_STATUS.contains(plug.getPlugStatus())) {
            this.bizTradingReactiveFeignClient.crashStopRecovery(cache.getEvseNo(), cache.getIdx())
                .subscribe();
        }
    }


    private void exceptionOrderHandlerV2(String evseNo,
        EvseReportRequestV2.Plug plugIn,
        PlugVo plugCache) {

        if (StringUtils.isNotEmpty(plugCache.getOrderNo())
            && NON_BUSY_STATUS.contains(plugIn.getPlugStatus())) {
            OrderAbnormalReason abnormalReason = null;
            if (plugIn.getPlugStatus() == PlugStatus.IDLE) {
                abnormalReason = OrderAbnormalReason.ORDER_UPDATE_TIMEOUT;
            } else if (plugIn.getPlugStatus() == PlugStatus.OFFLINE) {
                abnormalReason = OrderAbnormalReason.HB_TIMEOUT;
            } else if (plugIn.getPlugStatus() == PlugStatus.ERROR) {
                abnormalReason = OrderAbnormalReason.ORDER_ABNORMAL_STOP;
            }
            this.bizTradingReactiveFeignClient.handleErrorOrder(plugCache.getOrderNo(),
                    abnormalReason)
                .doOnNext(res -> {

                    if (res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS
                        && plugIn.getPlugStatus() != PlugStatus.OFFLINE) {
                        logger.info("桩状态上报清除异常订单。orderNo：{}", plugCache.getOrderNo());
                        plugService.updateOrderNo(evseNo, plugIn.getPlugId(), null);
                    }
                })
                .subscribe(
                    res -> logger.info("异常处理完成. orderNo = {}, res = {}",
                        plugCache.getOrderNo(),
                        res));
//                    .subscribe();

        }
//        return Mono.just(Boolean.TRUE);
    }

    /**
     * 桩新增
     */
    @Transactional
    public void addEvse(AddEvseParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotNull(param.getPower(), "桩额定功率不能为空");
        IotAssert.isNotNull(param.getPlugNum(), "枪头数不能为空");
        Evse evse = evseService.getEvse(param.getEvseNo(), true);
        IotAssert.isTrue(evse == null, "桩编号已存在");
        evse = new Evse();
        evse.setEvseId(param.getEvseNo())
            .setPower(param.getPower())
            .setPlugNum(param.getPlugNum())
            .setEvseStatus(EvseStatus.OFFLINE)
            .setSiteId("")
            .setProtocolVer(0)
            .setFirmwareVer("")
            .setGwno("");
        if (param.getProduceNo() != null) {
            evse.setProduceNo(param.getProduceNo());
        }
        if (param.getProduceDate() != null) {
            evse.setProduceDate(param.getProduceDate());
        }
        if (param.getExpireDate() != null) {
            evse.setExpireDate(param.getExpireDate());
        }
        evseService.addEvse(evse);

        List<PlugPo> paramPlugList = new ArrayList<>();
        for (int i = 1; i <= param.getPlugNum(); i++) {
            PlugPo plugPo = new PlugPo();
            plugPo.setPlugId(i)
                .setName(i + "枪");
            paramPlugList.add(plugPo);
        }

        evseProcessorAsync.insertOrUpdatePlug(param.getEvseNo(), paramPlugList, null);
    }

    /**
     * 桩编辑
     */
    public BaseResponse editEvse(EditEvseParam param) {
        IotAssert.isNotNull(param.getId(), "桩ID不能为空");
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotNull(param.getPower(), "桩额定功率不能为空");
        IotAssert.isNotNull(param.getPlugNum(), "枪头数不能为空");

        EvsePo evseDB = evseService.getEvseById(param.getId());
        IotAssert.isTrue(evseDB != null, "桩ID不存在");
        if (!evseDB.getEvseId().equals(param.getEvseNo())) {
            Evse temp = evseService.getEvse(param.getEvseNo(), true);
            IotAssert.isTrue(temp == null, "桩编号已存在");
        }

        EvsePo update = new EvsePo();
        update.setEvseId(param.getEvseNo())
            .setPower(param.getPower())
            .setPlugNum(param.getPlugNum())
            .setId(param.getId());
        if (param.getProduceNo() != null) {
            update.setProduceNo(param.getProduceNo());
        }
        if (param.getProduceDate() != null) {
            update.setProduceDate(param.getProduceDate());
        }
        if (param.getExpireDate() != null) {
            update.setExpireDate(param.getExpireDate());
        }
        boolean res = evseService.updateById(update);

        // step1 根据原evseNo删除枪数据
        plugService.removeByEvseId(evseDB.getEvseId());

        // step2 拼装新的枪数据，并入库
        List<PlugPo> paramPlugList = new ArrayList<>();
        for (int i = 1; i <= param.getPlugNum(); i++) {
            PlugPo plugPo = new PlugPo();
            plugPo.setEvseId(param.getEvseNo())
                .setPlugId(i)
                .setName(i + "枪")
                .setPlugStatus(PlugStatus.OFFLINE);
            paramPlugList.add(plugPo);
        }
        plugService.batchInsert(paramPlugList);
        return res ? RestUtils.success()
            : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVER_ERROR, "操作失败");
    }

    /**
     * 桩激活. 桩绑定到场站
     */
    @Transactional
    public void bindEvse2Site(BindEvseParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");
        IotAssert.isNotNull(param.getModelId(), "设备型号不能为空");
        List<Integer> tempList = param.getPlugList().stream().map(PlugPo::getPlugId).distinct()
            .collect(Collectors.toList());
        IotAssert.isTrue(param.getPlugList().size() == tempList.size(), "枪头标识不能重复");
        Evse evse = this.evseService.getEvse(param.getEvseNo(), true);
        if (StringUtils.isNotEmpty(param.getIccid())) {
            logger.info("设定sim卡：{}", param.getIccid());
            SimPo simPo = simRoDs.getByIccid(param.getIccid());
            IotAssert.isNotNull(simPo, "sim不存在");
            EvsePo evsePo = evseService.getByIccid(param.getIccid());
            if (evsePo != null) {
                String alertText = "SIM卡已绑定，桩：" + evsePo.getEvseId();
                if (StringUtils.isNotBlank(evsePo.getSiteId())) {
                    SitePo site = siteRoDs.getSite(evsePo.getSiteId());
                    if (site != null) {
                        alertText = alertText + "，场站：" + site.getName();
                    }
                }
                IotAssert.isTrue(false, alertText);
            }
        }
        if (evse == null) {
            logger.info("桩不存在，初始化桩信息 evseNo = {}", param.getEvseNo());
            evse = new Evse();
            evse.setEvseId(param.getEvseNo())
                .setBizStatus(EvseBizStatus.NORMAL) // 默认绑定都是正常
                .setEvseStatus(EvseStatus.OFFLINE)
                .setSiteId("")
                .setProtocolVer(0)
                .setFirmwareVer("")
                .setGwno("");
            evseService.addEvse(evse);
        }
        if (StringUtils.equals(evse.getSiteId(), param.getSiteId())
            && StringUtils.equals(evse.getName(), param.getName())) {
            logger.info("<< 重复的请求. evseNo = {}", param.getEvseNo());
            return;
        } else if (StringUtils.isNotBlank(evse.getSiteId()) && !param.getSiteId()
            .equals(evse.getSiteId())) {
            throw new DcServerException("该桩已绑定在别的场站,请不要使用重复的桩编号");
        }
        SitePo site = this.siteRoDs.getSite(param.getSiteId());
        IotAssert.isNotNull(site, "无法找到该场站. 场站ID = " + param.getSiteId());
        evse.setSiteId(param.getSiteId());
        evse.setPower(param.getPower());// 桩体额定功率，单位: 0.01kw
        evse.setName(param.getName());
        evse.setBizType(param.getBizType());
        EvseModelPo evseModelPo = evseModelRoDs.findById(param.getModelId());
        IotAssert.isNotNull(evseModelPo, "无法找到该设备型号. 设备型号 = " + param.getModelId());
        evse.setModel(evseModelPo.getModel());
        evse.setModelId(param.getModelId());
        evse.setSupply(evseModelPo.getSupply());
        evse.setConnSupport(evseModelPo.isConnSupport());
        evse.setConstantCharge(evseModelPo.isConstantCharge());
        evse.setIccid(param.getIccid());
        evse.setImsi(param.getImsi());
        evse.setImei(param.getImei());

        if (param.getProduceDate() != null) {
            evse.setProduceDate(param.getProduceDate());
        }
        if (param.getExpireDate() != null) {
            evse.setExpireDate(param.getExpireDate());
        }
        if (param.getProduceNo() != null) {
            evse.setProduceNo(param.getProduceNo());
        }
        evse.setUseSiteCfg(param.getUseSiteSetting());
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugList())) {
            evse.setPlugNum(param.getPlugList().size());
        }

        this.evseService.update(evse);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.BIND)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);    // 更新场站的redis缓存

        evseCfgResultRwDs.resetBaseCfgAndPriceCfg(evse.getEvseId()); // 重置桩配置结果（桩注册后会重新下发）

        evseProcessorAsync.afterTheBindEvse2Site(param, evse, site);
    }

    /**
     * 海外版，桩激活. 桩绑定到场站
     */
    @Transactional
    public void bindEssEvse2Site(BindEvseParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");
        // 海外版允许新增型号，因此不校验modelId，去校验型号、品牌、电桩类型
        IotAssert.isNotBlank(param.getModel(), "设备型号不能为空");
        IotAssert.isNotBlank(param.getBrand(), "品牌不能为空");
        IotAssert.isNotNull(param.getSupply(), "电桩类型不能为空");

        // 型号最后2/3位字符校验
        ODMType odmType = this.getODMTypeByModel(param.getModel());
        IotAssert.isNotNull(odmType, "请输入正确的设备型号");

        List<Integer> tempList = param.getPlugList().stream().map(PlugPo::getPlugId).distinct()
            .collect(Collectors.toList());
        IotAssert.isTrue(param.getPlugList().size() == tempList.size(), "枪头标识不能重复");
        Evse evse = this.evseService.getEvse(param.getEvseNo(), true);
        if (StringUtils.isNotEmpty(param.getIccid())) {
            logger.info("设定sim卡：{}", param.getIccid());
            SimPo simPo = simRoDs.getByIccid(param.getIccid());
            IotAssert.isNotNull(simPo, "sim不存在");
            EvsePo evsePo = evseService.getByIccid(param.getIccid());
            if (evsePo != null) {
                String alertText = "SIM卡已绑定，桩：" + evsePo.getEvseId();
                if (StringUtils.isNotBlank(evsePo.getSiteId())) {
                    SitePo site = siteRoDs.getSite(evsePo.getSiteId());
                    if (site != null) {
                        alertText = alertText + "，场站：" + site.getName();
                    }
                }
                IotAssert.isTrue(false, alertText);
            }
        }
        if (evse == null) {
            logger.info("桩不存在，初始化桩信息 evseNo = {}", param.getEvseNo());
            evse = new Evse();
            evse.setEvseId(param.getEvseNo())
                .setBizStatus(EvseBizStatus.NORMAL) // 默认绑定都是正常
                .setEvseStatus(EvseStatus.OFFLINE)
                .setSiteId("")
                .setProtocolVer(0)
                .setFirmwareVer("")
                .setGwno("");
            evseService.addEvse(evse);
        }
        if (StringUtils.equals(evse.getSiteId(), param.getSiteId())
            && StringUtils.equals(evse.getName(), param.getName())) {
            logger.info("<< 重复的请求. evseNo = {}", param.getEvseNo());
            return;
        } else if (StringUtils.isNotBlank(evse.getSiteId()) && !param.getSiteId()
            .equals(evse.getSiteId())) {
            throw new DcServerException("该桩已绑定在别的场站,请不要使用重复的桩编号");
        }
        SitePo site = this.siteRoDs.getSite(param.getSiteId());
        IotAssert.isNotNull(site, "无法找到该场站. 场站ID = " + param.getSiteId());

        EvseModelPo evseModelPo = new EvseModelPo();
        // 海外版，把传过来的类型进行保存，尽量少的影响之后的逻辑
        evseModelPo.setModel(param.getModel())
            .setPower(param.getPower())
            .setStatus(true)
            .setBrand(param.getBrand())
            .setFlags(List.of(3, 5))
            .setEnable(true)
            .setPlugNum(
                CollectionUtils.isEmpty(param.getPlugList()) ? 0 : param.getPlugList().size())
            // 系列，海外版不显示，但是数据库是必填的，因此此处暂时写品牌
            .setSeries(odmType.getDesc())
            .setSupply(param.getSupply());

        EvseModelPo savedEvseModelPo = evseModelRoDs.findByExactFields(evseModelPo.getModel(),
            evseModelPo.getBrand(),
            evseModelPo.getSeries(),
            evseModelPo.getSupply(),
            evseModelPo.getPower(),
            evseModelPo.getPlugNum());
        if (savedEvseModelPo != null && savedEvseModelPo.getId() != null) {
            logger.info(
                "海外版，此次新增桩时填写的充电桩型号相关信息已经在evse_model表中存在了，因此不新增，直接使用, param={}",
                JsonUtils.toJsonString(evseModelPo));
        } else {
            logger.info("海外版，新增桩时自动新增充电桩型号，, param={}",
                JsonUtils.toJsonString(param));
            evseModelRwDs.insertOrUpdate(evseModelPo);
            savedEvseModelPo = evseModelRoDs.findByExactFields(evseModelPo.getModel(),
                evseModelPo.getBrand(),
                evseModelPo.getSeries(),
                evseModelPo.getSupply(),
                evseModelPo.getPower(),
                evseModelPo.getPlugNum());
            IotAssert.isTrue(savedEvseModelPo != null && savedEvseModelPo.getId() != null,
                "操作失败");
        }

        evse.setSiteId(param.getSiteId());
        evse.setPower(param.getPower());// 桩体额定功率，单位: 0.01kw
        evse.setName(param.getName());
        evse.setBizType(param.getBizType());
        evse.setModel(param.getModel());
        evse.setModelId(savedEvseModelPo.getId());
        evse.setSupply(param.getSupply());
        evse.setConnSupport(savedEvseModelPo.isConnSupport());
        evse.setConstantCharge(savedEvseModelPo.isConstantCharge());
        evse.setIccid(param.getIccid());
        evse.setImsi(param.getImsi());
        evse.setImei(param.getImei());
        evse.setOdm(odmType);

        if (param.getProduceDate() != null) {
            evse.setProduceDate(param.getProduceDate());
        }
        if (param.getExpireDate() != null) {
            evse.setExpireDate(param.getExpireDate());
        }
        if (param.getProduceNo() != null) {
            evse.setProduceNo(param.getProduceNo());
        }
        evse.setUseSiteCfg(param.getUseSiteSetting());
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugList())) {
            evse.setPlugNum(param.getPlugList().size());
        }

        this.evseService.update(evse);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.BIND)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);    // 更新场站的redis缓存

        evseCfgResultRwDs.resetBaseCfgAndPriceCfg(evse.getEvseId()); // 重置桩配置结果（桩注册后会重新下发）

        evseProcessorAsync.afterTheBindEvse2Site(param, evse, site);
    }

    @Transactional
    public void updateEvseInfo(ModifyEvseInfoParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotBlank(param.getName(), "桩名称不能为空");
        IotAssert.isNotNull(param.getModelId(), "设备型号不能为空");
        List<Integer> tempList = param.getPlugVoList().stream().map(PlugPo::getPlugId).distinct()
            .collect(Collectors.toList());
        IotAssert.isTrue(param.getPlugVoList().size() == tempList.size(), "枪头标识不能重复");

        Evse evse = this.evseService.getEvse(param.getEvseNo(), true);
        if (evse == null) {
            logger.warn("<< 桩不存在. evseNo = {}", param.getEvseNo());
            throw new DcArgumentException("请输入正确的桩编号");
        }

        if (StringUtils.isNotBlank(param.getIccid()) && !param.getIccid().equals(evse.getIccid())) {
            logger.info("修改桩SIM卡信息: {} -> {}", evse.getIccid(), param.getIccid());
            SimPo simPo = simRoDs.getByIccid(param.getIccid());
            IotAssert.isNotNull(simPo, "SIM卡不存在");
            EvsePo evsePo = this.evseService.getByIccid(param.getIccid());
            if (evsePo != null) {
                String alertText = "SIM卡已绑定，桩：" + evsePo.getEvseId();
                if (StringUtils.isNotBlank(evsePo.getSiteId())) {
                    SitePo site = siteRoDs.getSite(evsePo.getSiteId());
                    if (site != null) {
                        alertText = alertText + "，场站：" + site.getName();
                    }
                }
                IotAssert.isTrue(false, alertText);
            }
            logger.info("修改桩sim信息: imsi: {} -> {}, imei: {} -> {}",
                evse.getImsi(), simPo.getImsi(), evse.getImei(), simPo.getImei());
//            evse.setImsi(simPo.getImsi());
//            evse.setImei(simPo.getImei());
        }

        if (param.getName() != null) {
            evse.setName(param.getName());
        }

        SitePo site = this.siteRoDs.getSite(evse.getSiteId());
        // 桩体额定功率，单位: 0.01kw
        if (null != param.getPower()) {
            evse.setPower(param.getPower());
        }
        EvseModelPo evseModelPo = evseModelRoDs.findById(param.getModelId());
        IotAssert.isNotNull(evseModelPo, "无法找到该设备型号. 设备型号 = " + param.getModelId());
        evse.setModel(evseModelPo.getModel())
            .setModelId(param.getModelId())
            .setSupply(evseModelPo.getSupply())
            .setConnSupport(evseModelPo.isConnSupport())
            .setProduceDate(param.getProduceDate())
            .setExpireDate(param.getExpireDate())
            .setProduceNo(param.getProduceNo())
            .setFirmwareVer(param.getFirmwareVer())
            .setIccid(param.getIccid())
            .setImei(param.getImei())
            .setPhysicalNo(param.getPhysicalNo())
            .setImsi(param.getImsi());
        evse.setUseSiteCfg(param.getUseSiteSetting());
        evse.setConstantCharge(evseModelPo.isConstantCharge());
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugVoList())) {
            evse.setPlugNum(param.getPlugVoList().size());
        }

        this.evseService.update(evse);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.BIND)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);    // 更新场站的redis缓存

        evseProcessorAsync.afterTheUpdateEvseInfo(param, evse, site);
    }

    @Transactional
    public void updateEssEvseInfo(ModifyEvseInfoParam param) {
        IotAssert.isNotBlank(param.getEvseNo(), "桩编号不能为空");
        IotAssert.isNotBlank(param.getName(), "桩名称不能为空");
        // 海外版允许新增型号，因此不校验modelId，去校验型号、品牌、电桩类型
        IotAssert.isNotBlank(param.getModel(), "设备型号不能为空");
        IotAssert.isNotBlank(param.getBrand(), "品牌不能为空");
        IotAssert.isNotNull(param.getSupply(), "电桩类型不能为空");

        // 型号最后2/3位字符校验
        ODMType odmType = this.getODMTypeByModel(param.getModel());
        IotAssert.isNotNull(odmType, "请输入正确的设备型号");

        List<Integer> tempList = param.getPlugVoList().stream().map(PlugPo::getPlugId).distinct()
            .toList();
        IotAssert.isTrue(param.getPlugVoList().size() == tempList.size(), "枪头标识不能重复");

        Evse evse = this.evseService.getEvse(param.getEvseNo(), true);
        if (evse == null) {
            logger.warn("<< 桩不存在. evseNo = {}", param.getEvseNo());
            throw new DcArgumentException("请输入正确的桩编号");
        }

        if (StringUtils.isNotBlank(param.getIccid()) && !param.getIccid().equals(evse.getIccid())) {
            logger.info("修改桩SIM卡信息: {} -> {}", evse.getIccid(), param.getIccid());
            SimPo simPo = simRoDs.getByIccid(param.getIccid());
            IotAssert.isNotNull(simPo, "SIM卡不存在");
            EvsePo evsePo = this.evseService.getByIccid(param.getIccid());
            if (evsePo != null) {
                String alertText = "SIM卡已绑定，桩：" + evsePo.getEvseId();
                if (StringUtils.isNotBlank(evsePo.getSiteId())) {
                    SitePo site = siteRoDs.getSite(evsePo.getSiteId());
                    if (site != null) {
                        alertText = alertText + "，场站：" + site.getName();
                    }
                }
                IotAssert.isTrue(false, alertText);
            }
            logger.info("修改桩sim信息: imsi: {} -> {}, imei: {} -> {}",
                evse.getImsi(), simPo.getImsi(), evse.getImei(), simPo.getImei());
//            evse.setImsi(simPo.getImsi());
//            evse.setImei(simPo.getImei());
        }

        if (param.getName() != null) {
            evse.setName(param.getName());
        }

        SitePo site = this.siteRoDs.getSite(evse.getSiteId());
        // 桩体额定功率，单位: 0.01kw
        if (null != param.getPower()) {
            evse.setPower(param.getPower());
        }

        EvseModelPo evseModelPo = new EvseModelPo();
        // 海外版，把传过来的类型进行保存，尽量少的影响之后的逻辑
        evseModelPo.setModel(param.getModel())
            .setPower(param.getPower())
            .setStatus(true)
            .setBrand(param.getBrand())
            .setFlags(List.of(3, 5))
            .setEnable(true)
            .setPlugNum(
                CollectionUtils.isEmpty(param.getPlugVoList()) ? 0 : param.getPlugVoList().size())
            // 系列，海外版不显示，但是数据库是必填的，因此此处暂时写品牌
            .setSeries(odmType.getDesc())
            .setSupply(param.getSupply());

        EvseModelPo savedEvseModelPo = evseModelRoDs.findByExactFields(evseModelPo.getModel(),
            evseModelPo.getBrand(),
            evseModelPo.getSeries(),
            evseModelPo.getSupply(),
            evseModelPo.getPower(),
            evseModelPo.getPlugNum());
        if (savedEvseModelPo != null && savedEvseModelPo.getId() != null) {
            logger.info(
                "海外版，此次编辑桩时填写的充电桩型号相关信息已经在evse_model表中存在了，因此不新增，直接使用, param={}",
                JsonUtils.toJsonString(evseModelPo));
        } else {
            logger.info("海外版，编辑桩时自动新增充电桩型号，, param={}",
                JsonUtils.toJsonString(param));
            evseModelRwDs.insertOrUpdate(evseModelPo);
            savedEvseModelPo = evseModelRoDs.findByExactFields(evseModelPo.getModel(),
                evseModelPo.getBrand(),
                evseModelPo.getSeries(),
                evseModelPo.getSupply(),
                evseModelPo.getPower(),
                evseModelPo.getPlugNum());
            IotAssert.isTrue(savedEvseModelPo != null && savedEvseModelPo.getId() != null,
                "操作失败");
        }

        evse.setModel(savedEvseModelPo.getModel())
            .setModelId(savedEvseModelPo.getId())
            .setSupply(savedEvseModelPo.getSupply())
            .setConnSupport(savedEvseModelPo.isConnSupport())
            .setProduceDate(param.getProduceDate())
            .setExpireDate(param.getExpireDate())
            .setProduceNo(param.getProduceNo())
            .setFirmwareVer(param.getFirmwareVer())
            .setIccid(param.getIccid())
            .setImei(param.getImei())
            .setPhysicalNo(param.getPhysicalNo())
            .setImsi(param.getImsi())
            .setOdm(odmType);
        evse.setUseSiteCfg(param.getUseSiteSetting());
        evse.setConstantCharge(evseModelPo.isConstantCharge());
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugVoList())) {
            evse.setPlugNum(param.getPlugVoList().size());
        }

        this.evseService.update(evse);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.BIND)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);    // 更新场站的redis缓存

        evseProcessorAsync.afterTheUpdateEvseInfo(param, evse, site);
    }

    public void updateEvseInfoList(List<ModifyEvseInfoParam> param) {
        param.forEach(this::updateEvseInfo);
    }

    @Transactional
    public void batchImport(List<EvseModelVo> param) {

        List<SimTinyVo> simPoList = null;
        List<Long> idList = param.stream().map(EvseModelVo::getSimId)
            .filter(Objects::nonNull).collect(Collectors.toList());
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(idList)) {
            ListSimParam req = new ListSimParam();
            req.setIdList(idList)
                .setSize(idList.size());
            simPoList = simRoDs.getTinyList(req);
        }
        Optional<Map<Long, SimTinyVo>> simMapOpt = !CollectionUtils.isEmpty(simPoList)
            ? Optional.of(simPoList.stream().collect(Collectors.toMap(SimTinyVo::getId, o -> o)))
            : Optional.empty();

        param.forEach(e -> {
            BindEvseParam tempParam = new BindEvseParam();

            EvseModelPo evseModelPo = evseModelRoDs.findById(e.getModelId());
            IotAssert.isNotNull(evseModelPo, "无法找到该设备型号. 设备型号 = " + e.getModel());
            List<PlugPo> plugList = new ArrayList<>();
            for (int i = 1; i <= evseModelPo.getPlugNum(); i++) {
                PlugPo temp = new PlugPo();
                temp.setPlugId(i)
                    .setName(i + "枪");
                plugList.add(temp);
            }

            simMapOpt.ifPresent(simMap -> {
                if (e.getSimId() != null) {
                    SimTinyVo sim = simMap.get(e.getSimId());
                    if (sim != null) {
                        tempParam.setIccid(sim.getIccid())
                            .setImsi(sim.getImsi())
                            .setImei(sim.getImei());
                    }
                }
            });

            tempParam.setEvseNo(e.getEvseId())
                .setSiteId(e.getSiteId())
                .setBizType(e.getBizType())
                .setPower(evseModelPo.getPower())
                .setName(e.getName())
                .setModelId(e.getModelId())
                .setUseSiteSetting(e.getUseSiteCfg())
                .setProduceNo(e.getProduceNo())
                .setPlugList(plugList)
            ;
            this.bindEvse2Site(tempParam);
        });
    }

    @Transactional
    public void batchUpdateEvseInfo(ModifyEvseInfoParam param) {
        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getEvseNoList()),
            "桩列表不能为空");
        List<Integer> tempList = param.getPlugVoList().stream().map(PlugPo::getPlugId).distinct()
            .collect(Collectors.toList());
        IotAssert.isTrue(param.getPlugVoList().size() == tempList.size(), "枪头标识不能重复");

        param.getEvseNoList().forEach(evseNo -> {

            Evse evse = this.evseService.getEvse(evseNo, true);
            if (evse == null) {
                logger.warn("<< 桩不存在. evseNo = {}", evseNo);
                throw new DcArgumentException("请输入正确的桩编号");
            }
            if (param.getUseSiteSetting() != null) {
                evse.setUseSiteCfg(param.getUseSiteSetting());
            }
            if (param.getModelId() != null) {
                EvseModelPo evseModelPo = evseModelRoDs.findById(param.getModelId());
                IotAssert.isNotNull(evseModelPo,
                    "无法找到该设备型号. 设备型号 = " + param.getModelId());
                evse.setConstantCharge(evseModelPo.isConstantCharge());
                evse.setModel(evseModelPo.getModel())
                    .setModelId(param.getModelId())
                    .setConnSupport(evseModelPo.isConnSupport())
                    .setSupply(evseModelPo.getSupply());
            }
            if (param.getPower() != null) {
                evse.setPower(param.getPower());
            }
            if (param.getProduceDate() != null) {
                evse.setProduceDate(param.getProduceDate());
            }
            if (param.getExpireDate() != null) {
                evse.setExpireDate(param.getExpireDate());
            }
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getPlugVoList())) {
                evse.setPlugNum(param.getPlugVoList().size());
            }

            SitePo site = this.siteRoDs.getSite(evse.getSiteId());

            this.evseService.update(evse);

            UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
            dto.setEvent(IotEvent.BIND)
                .setSite(site)
                .setEvse(evse)
                .setLinkId(null);
            redisIotUpdateWrapper.updateRedisEvseCache(dto);    // 更新场站的redis缓存

            evseProcessorAsync.afterTheUpdateEvseInfo(param, evse, site);

        });

    }

    /**
     * 批量新增脱机桩
     *
     * @param param
     * @return
     */
    public BaseResponse batchAddOfflineEvse(List<OfflineEvseVo> param) {
        boolean boo = this.evseService.batchAddOfflineEvse(param);
        return boo ? RestUtils.success() : RestUtils.serverBusy();
    }

    public BaseResponse updateOfflineEvse(ModifyEvseInfoParam param) {
        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getEvseNoList()),
            "桩编号不能为空");
        boolean boo = this.evseService.updateOfflineBatch(param);
        return boo ? RestUtils.success() : RestUtils.serverBusy();
    }

    public BaseResponse removeOfflineEvse(List<OfflineEvseParam> param) {
        boolean boo = this.evseService.removeOfflineEvse(param);
        return boo ? RestUtils.success() : RestUtils.serverBusy();
    }

    /**
     * 桩从场站解绑
     */
    public void unbindEvse2Site(UnbindEvseParam param) {
        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getEvseNoList()),
            "桩编号不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");

        param.getEvseNoList().forEach(evseNo -> {
            this.unbindEvse2Site(evseNo, param.getSiteId());
        });

    }

    /**
     * 桩从场站解绑
     */
    public void unbindEvse2Site(String evseNo, String siteId) {
        EvsePo evse = this.evseService.getEvse(evseNo, true);
        if (evse == null) {
            logger.warn("<< 桩不存在. evseNo = {}", evseNo);
            throw new DcArgumentException("请输入正确的桩编号");
        }
        if (!StringUtils.equals(evse.getSiteId(), siteId)) {
            logger.warn("场站ID不匹配!!! evse.siteId = {}, siteId = {}", evse.getSiteId(), siteId);
            throw new DcArgumentException("桩当前未绑定在该场站");
        }
        if (EvseStatus.BUSY.equals(evse.getEvseStatus())) {
            logger.warn("桩正在充电中，不可操作解绑, evse = {}", JsonUtils.toJsonString(evse));
            throw new DcArgumentException("桩正在充电中，不可操作解绑");
        }

        List<DeviceMeterPo> byEvseIdInList = evseMeterRoDs.getByEvseIdInList(List.of(evseNo));
        IotAssert.isTrue(CollectionUtils.isEmpty(byEvseIdInList),
            "解绑失败：电桩已绑定电表，请解绑后重试。");

        SiteTopologyRefPo bindRef = siteTopologyRefRoDs.getBindRefByDownIdAndType(evse.getId(),
            TopologyType.EVSE);
        IotAssert.isNull(bindRef, "解绑失败：电桩已绑定变压器，请解绑后重试。");

        // t_evse_cfg
        EvseCfgPo cfg = this.evseCfgRwDs.getEvseCfg(evseNo, true);
        if (null != cfg) {
            this.evseCfgRwDs.disableEvseCfg(evseNo);
        }

        // evse.setSiteId("");
        this.evseService.unBindSite(evse);
        List<PlugPo> plugList = this.plugService.listPlug(evseNo, null, 0L, 99, false);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.UNBIND)
            .setSite(null)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);
        if (plugList != null) {
            redisIotUpdateWrapper.unbindEvse(siteId, evseNo,
                plugList.stream()
                    .map(p -> PlugNoUtils.formatPlugNo(evse.getEvseId(), p.getPlugId()))
                    .collect(Collectors.toList()));  // 更新redis缓存的桩/场站绑定关系
        }

        evseBindHistoryRwDs.insertEvseAction(EvseHistoryActionType.UNBIND, evseNo, siteId);
    }

    public Mono<Boolean> reportModule(ReportModuleParam param) {
        IotAssert.isNotNull(param.getEvseNo(), "桩编号不能为空");

        EvseReportModulePo insertPo = new EvseReportModulePo();
        insertPo.setEvseNo(param.getEvseNo())
            .setModuleType(param.getDcModuleModel())
            .setNumber(param.getDcModuleTotal());
        return Mono.just(insertPo)
            .map(modulePo -> {
                boolean res;
                if (StringUtils.isNotBlank(modulePo.getModuleType())) {
                    res = evseReportModuleRwDs.insertOrUpdate(modulePo);
                } else {
                    res = evseReportModuleRwDs.updateNumber(modulePo);
                }

                IotAssert.isTrue(res, "上报器件失败");
                return evseReportModuleRoDs.getByEvseNo(modulePo.getEvseNo());
            })
            .map(modulePo -> {
                evseReportModuleDetailRwDs.removeByModuleId(modulePo.getId());

                param.getDcModuleInfoList().forEach(e -> {
                    e.setModuleId(modulePo.getId());
                });
                return evseReportModuleDetailRwDs.batchInsert(param.getDcModuleInfoList());
            });
    }


    /**
     *根据型号的最后2/3位字符做ODM供应商判断
     *
     * @param model
     * @return
     */
    private ODMType getODMTypeByModel(String model) {
        if (StringUtils.isBlank(model)) {
            return null;
        }
        for (ODMType odmType : ODMType.values()) {
            if (model.endsWith("-" + odmType.getCode())) {
                return odmType;
            }
        }
        return null;
    }

}
