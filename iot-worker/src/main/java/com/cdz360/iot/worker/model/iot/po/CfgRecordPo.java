package com.cdz360.iot.worker.model.iot.po;

import com.cdz360.iot.model.base.DbObject;

public class CfgRecordPo extends DbObject {
    private String cfgVer;
    private Integer total;
    private Integer success;
    private Integer fail;
    private Integer timeout;

    public String getCfgVer() {
        return cfgVer;
    }

    public CfgRecordPo setCfgVer(String cfgVer) {
        this.cfgVer = cfgVer;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public CfgRecordPo setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public Integer getSuccess() {
        return success;
    }

    public CfgRecordPo setSuccess(Integer success) {
        this.success = success;
        return this;
    }

    public Integer getFail() {
        return fail;
    }

    public CfgRecordPo setFail(Integer fail) {
        this.fail = fail;
        return this;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public CfgRecordPo setTimeout(Integer timeout) {
        this.timeout = timeout;
        return this;
    }
}
