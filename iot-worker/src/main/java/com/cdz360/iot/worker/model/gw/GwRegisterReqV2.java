package com.cdz360.iot.worker.model.gw;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "网关注册请求 v2")
public class GwRegisterReqV2 {
    @Schema(description = "网关的局域网IP地址", example = "**************")
    private String lanIp;

    @Schema(description = "网关的设备MAC地址", example = "AB:CD:EF:01:02:03")
    private String mac;
}
