package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * @Classname IotParkFeignClient
 * @Description
 * @Date 4/23/2021 5:02 PM
 * @Created by Rafael
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_PARK,
        fallbackFactory = IotParkHystrixFeignClientFactory.class)
public interface IotParkFeignClient {
    @PostMapping(value = "/inner/checkCoupon")
    Mono<BaseResponse> checkCoupon(@RequestBody OrderStopRequestV2 orderStopRequestV2);
}