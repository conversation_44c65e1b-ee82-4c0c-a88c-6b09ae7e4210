package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.worker.model.dongzheng.BoxSettingUpdateRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, fallbackFactory = DeviceBusinessHystrixFeignClientFactory.class)
public interface DeviceBusinessFeignClient {

    @PostMapping("/api/boxsetting/updateByEvseId")
    Mono<BaseResponse> updateBoxSetting(BoxSettingUpdateRequest request);

//    @PostMapping("/api/box/bindTemplateToConnector")
//    Mono<ObjectResponse<Long>> bindTemplateToConnector(@RequestParam("evseNo") String evseNo);

    // 给桩下发场站默认配置信息
    @GetMapping("/api/boxsetting/downSetting2Evse")
    Mono<BaseResponse> downSetting2Evse(@RequestParam("evseNo") String evseNo,
        @RequestParam("siteId") String siteId);

}
