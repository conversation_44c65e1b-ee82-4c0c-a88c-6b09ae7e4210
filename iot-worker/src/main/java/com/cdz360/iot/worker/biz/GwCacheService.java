package com.cdz360.iot.worker.biz;

import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class GwCacheService {
    private Map<String, GwInfoPo> gws = new ConcurrentHashMap<>();


    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @PostConstruct
    public void init() {
        long num = this.refreshAll(null);
        log.info("获取到 {} 个网关", num);
    }

    public synchronized long refreshAll(Date lastUpdate) {
        long start = 0;
        int size = 100;
        long num = 100;
        long retNum = 0L;
        while (!(num < size)) {
            List<GwInfoPo> gwList = gwInfoRoDs.listGw(null, null, lastUpdate, start, size);
            num = gwList.size();
            start = start + num;
            retNum = retNum + num;
            gwList.stream().forEach(gw -> gws.put(gw.getGwno(), gw));
        }
        return retNum;
    }

    public void refreshGwInfo(String gwno) {
        GwInfoDto gwInfo = this.gwInfoRoDs.getByGwno(gwno);
        if (gwInfo == null) {
            log.error("获取网关信息失败. gwno = {}", gwno);
        } else {
            this.gws.put(gwno, gwInfo);
        }
    }

    public GwInfoPo getGwInfo(String gwno) {
        return gws.get(gwno);
    }
}
