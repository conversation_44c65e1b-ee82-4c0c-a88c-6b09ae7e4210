package com.cdz360.iot.worker.biz.south;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.model.iot.vo.SiteCtrlVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseBundleQueryDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.EvseCfgResultRwDs;
import com.cdz360.iot.ds.rw.EvseCfgRwDs;
import com.cdz360.iot.ds.rw.GwInfoRwDs;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.model.evse.BsBoxPo;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.EvseReportRequest;
import com.cdz360.iot.model.evse.dto.PcVerDto;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.type.DeviceStatusCodeType;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.worker.biz.EvseBatchHandler;
import com.cdz360.iot.worker.biz.EvseCfgBizService;
import com.cdz360.iot.worker.biz.EvseCfgRedisService;
import com.cdz360.iot.worker.biz.EvseQueue;
import com.cdz360.iot.worker.biz.EvseQueueConsumer;
import com.cdz360.iot.worker.biz.PlugBatchHandler;
import com.cdz360.iot.worker.biz.QueueMonitor;
import com.cdz360.iot.worker.biz.RedisEquipRtDataService;
import com.cdz360.iot.worker.biz.RedisEvseRegisterService;
import com.cdz360.iot.worker.biz.RedisIotUpdateWrapper;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import com.cdz360.iot.worker.feign.DeviceBusinessFeignClient;
import com.cdz360.iot.worker.model.gw.EvseResiterReqV2;
import com.cdz360.iot.worker.model.gw.MgcAlert;
import com.cdz360.iot.worker.model.gw.MgcUpgradeResultReq;
import com.cdz360.iot.worker.model.iot.Evse;
import com.cdz360.iot.worker.model.iot.UpdateEvseCacheDto;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgResendDto;
import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class IotSouthBizService {

    private final long PRICE_CODE_RESEND_DELAY_SECOND = 5L; // 桩电价重发延迟秒数

    @Autowired
    private EvseService evseService;

    @Autowired
    private PlugService plugService;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwInfoRwDs gwInfoRwDs;

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    @Autowired
    private EvseBundleQueryDs bundleQueryDs;

    @Autowired
    private EvseCfgRwDs evseCfgRwDs;

    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;

    @Autowired
    private BizDataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DeviceBusinessFeignClient deviceBusinessFeignClient;


    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;

    @Autowired
    private EvseBatchHandler evseBatchHandler;

    @Autowired
    private PlugBatchHandler plugBatchHandler;

    @Autowired
    private QueueMonitor queueMonitor;


    private EvseQueue evseQueue;

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private RedisEquipRtDataService redisEquipRtDataService;

    @Autowired
    private RedisEvseRegisterService redisEvseRegisterService;

    @Autowired
    private EvseCfgRedisService evseCfgRedisService;

    @Autowired
    private EvseCfgBizService evseCfgBizService;


    @PostConstruct
    public void init() {
        evseQueue = new EvseQueue();

        EvseQueueConsumer consumer = new EvseQueueConsumer(evseQueue, evseBatchHandler);

        new Thread(consumer).start();

        queueMonitor.addQueue(evseQueue);

    }


    /**
     * 桩注册
     *
     * @param report
     * @param gwno
     */
    public void evseRegister(EvseResiterReqV2 report, String gwno) {
        if (report.getDtuType() == null) {
            report.setDtuType(DtuType.UNKNOWN);
        }
        EvsePo evsePo = evseService.getEvsePo(report.getEvseNo(), false);

        if (evsePo == null) {   // 数据库没有匹配到
            this.registerNewEvse(report, gwno);
        } else {
            this.registerExistEvse(report, evsePo, gwno);
            Mono.delay(Duration.ofSeconds(PRICE_CODE_RESEND_DELAY_SECOND))
                .doOnNext(e -> {
                    this.configRelatedOperations(report, evsePo);
                })
                .subscribe();
        }
        log.info("<< evseRegister");
    }

    private void registerNewEvse(EvseResiterReqV2 report,// Evse evse,
        String gwno) {
        log.info("桩注册新增桩:{}", report);

        Evse evse = new Evse();
        evse.setPlugs(new ArrayList<>())
            .setEvseId(report.getEvseNo())
            .setBizStatus(EvseBizStatus.NORMAL)
            .setNet(report.getNetType())
            .setDtuType(report.getDtuType())
            .setIp(report.getEvseIp())
            .setIccid(report.getIccid())
            .setImsi(report.getImsi())
            .setImei(report.getImei())
            .setPlugNum(report.getPlugNum())
            .setSupply(report.getSupplyType())
            .setProtocolVer(report.getProtocolVer())
            .setProtocol(report.getProtocol())
            .setEvseStatus(EvseStatus.IDLE)
            .setPasscodeVer(report.getPasscodeVer())
            .setFirmwareVer(this.formatEvseSwVer(report))
            .setGwno(gwno);
        if (report.getPower() != null) {
            evse.setPower(report.getPower().intValue());
        }
        fillEvsePoPcXV2(evse, report.getPcVer());

        EvseReportRequest reportRequest = new EvseReportRequest();
        reportRequest.setEvseId(report.getEvseNo());
        //封装枪的对象
        List<EvseReportRequest.Plug> plugs = new ArrayList<>();
        for (EvseResiterReqV2.PlugRegisterReq plug : report.getPlugs()) {
            plugs.add(new EvseReportRequest.Plug().setPlugId(plug.getPlugId())
                .setPlugStatus(PlugStatus.UNKNOWN));
        }
        reportRequest.setEvseVer(report.getProtocolVer());
        reportRequest.setNet(report.getNetType());
        reportRequest.setPlugNum(report.getPlugNum());
        reportRequest.setPlugs(plugs);
        reportRequest.setProtocol(report.getProtocol());
        reportRequest.setSupply(report.getSupplyType());
        reportRequest.setGwno(gwno);
        reportRequest.setEvseSoftware(evse.getFirmwareVer());

        //deviceManagerFeignClient.register()内部会校验下面的逻辑
        IotAssert.isNotNull(report.getProtocolVer(), "桩端协议版本号不能为空");
        IotAssert.isNotNull(report.getNetType(), "网络类型不能为空");
        IotAssert.isNotNull(report.getPlugNum(), "充电枪数量不能为空");
        IotAssert.isTrue(report.getPlugNum() > 0, "充电枪数量必须大于0");
        if (report.getProtocol() == null) {
            report.setProtocol(EvseProtocolType.DC);//默认鼎充的桩
            evse.setProtocol(EvseProtocolType.DC);
        }
        SitePo site = null;
        if (StringUtils.isNotBlank(evse.getSiteId())) {
            site = siteRoDs.getSite(evse.getSiteId());
        }
        log.info("更新iot数据库: {}", evse);
        this.evseService.addEvse(evse);

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.CREATE)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        redisIotUpdateWrapper.updateRedisEvseCache(dto);

        this.addOrUpdatePlugsResisterV2(report, site, evse);

        log.debug("桩注册完成");
    }

    private void registerExistEvse(EvseResiterReqV2 report, //Evse evse,
        EvsePo evsePo, String gwno) {
        // 调整桩绑定的网关: redis 中的数据会在下面做调整的
        if (!gwno.equals(evsePo.getGwno())) {
            evsePo.setGwno(gwno);
        }

        evsePo.setNet(report.getNetType())
            .setSupply(report.getSupplyType())
            .setDtuType(report.getDtuType())
            .setProtocolVer(report.getProtocolVer())
            .setProtocol(report.getProtocol())
            .setEvseStatus(EvseStatus.IDLE)
            .setPasscodeVer(report.getPasscodeVer())
            .setFirmwareVer(this.formatEvseSwVer(report))
            .setPlugNum(report.getPlugNum()).setGwno(gwno);
        if (StringUtils.isNotBlank(report.getEvseIp())) {
            evsePo.setIp(report.getEvseIp());
        }
        if (StringUtils.isNotBlank(report.getIccid())) {

            if (!report.getIccid().equals(evsePo.getIccid())) {
                // 防止重复绑定
                evseService.clearSimByIccid(report.getIccid());
            }
            evsePo.setIccid(report.getIccid());
        }
        if (StringUtils.isNotBlank(report.getImsi())) {
            evsePo.setImsi(report.getImsi());
        }
        if (StringUtils.isNotBlank(report.getImei())) {
            evsePo.setImei(report.getImei());
        }
//        if(report.getPriceCode() != null) {
//            evsePo.setPriceCode(report.getPriceCode());
//        }
        // 功率
        if (null != report.getPower() && report.getPower().intValue() > 0 &&
            (evsePo.getPower() == null || evsePo.getPower() <= 0)) {
            evsePo.setPower(report.getPower().intValue());
            BsBoxPo bsBoxPo = new BsBoxPo();
            bsBoxPo.setEvseNo(report.getEvseNo())
                .setSiteId(evsePo.getSiteId())
                .setPower(report.getPower().intValue())
//                    .setPriceCode(evsePo.getPriceCode())
                .setUseSiteSetting(Boolean.FALSE);
            this.dataCoreFeignClient.updateBsBox(bsBoxPo)
                .subscribe(resX -> {
                    log.info("桩同步功率到bsbox表: {}, 结果: {}", JsonUtils.toJsonString(bsBoxPo),
                        resX);
                });
        }

        fillEvsePoPcXV2(evsePo, report.getPcVer());

        SitePo site = null;
        if (StringUtils.isNotBlank(evsePo.getSiteId())) {
            site = siteRoDs.getSite(evsePo.getSiteId());
        }

        this.updateEvseV2(evsePo, site, evsePo.getFirmwareVer());//修改t_evse表

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.CREATE)
            .setSite(site)
            .setEvse(evsePo)
            .setLinkId(null)
            .setRegisterReason(report.getRegisterReason());
        redisIotUpdateWrapper.updateRedisEvseCache(dto);

        this.addOrUpdatePlugsResisterV2(report, site, evsePo);
        log.debug("桩注册完成");
    }

    private String formatEvseSwVer(EvseResiterReqV2 report) {
        if (EvseProtocolType.OCPP.equals(report.getProtocol())) {
            return report.getFirmwareVer();
        }

        StringBuilder stringBuilder = new StringBuilder();
        if (com.cdz360.iot.common.utils.CollectionUtils.isNotEmpty(report.getPcVer())) {
            for (int i = 0; i < report.getPcVer().size(); i++) {
                if (i != report.getPcVer().size() - 1) {
                    stringBuilder.append(report.getPcVer().get(i).getSwVer()).append("-");
                } else {
                    stringBuilder.append(report.getPcVer().get(i).getSwVer());
                }
            }
        }
        return stringBuilder.toString();
    }

    private void updateEvseV2(EvsePo evse, SitePo site, String firmwareVer) {
        log.info("修改桩状态。evse: {}", evse);
        if (NetType.UNKNOWN == evse.getNet()) {
            evse.setNet(null);  // 未知时不更新
        }
        if (EvseStatus.UNKNOWN == evse.getEvseStatus()) {
            evse.setEvseStatus(null);  // 未知时不更新
        }
        if (SupplyType.UNKNOWN == evse.getSupply()) {
            evse.setSupply(null);   // 未知时不更新
        }
        //logger.info("更新桩状态到数据库。evse: {}", evse);
        evseQueue.offer(evse);


    }

    private void fillEvsePoPcXV2(final EvsePo evsePo, List<PcVerDto> pcVerList) {
        //如果上传的版本号不是3个 则认为有问题
        if (pcVerList == null || pcVerList.size() < 3) {
            return;
        }
//        if (pcVerList != null) {
        log.info("更新桩pc板的软件版本: {}", pcVerList);
        evsePo.setPc01Ver(
            pcVerList.get(0).getHwVer() + "-" + pcVerList.get(0).getSwVer() + "-" + pcVerList.get(0)
                .getVendorCode());
        evsePo.setPc02Ver(
            pcVerList.get(1).getHwVer() + "-" + pcVerList.get(1).getSwVer() + "-" + pcVerList.get(1)
                .getVendorCode());
        evsePo.setPc03Ver(
            pcVerList.get(2).getHwVer() + "-" + pcVerList.get(2).getSwVer() + "-" + pcVerList.get(2)
                .getVendorCode());
//        }
    }

    private void addOrUpdatePlugsResisterV2(EvseResiterReqV2 report, @Nullable SitePo site,
        EvsePo evse) {

        log.info("新增枪V2。 report: {}", report);
        if (NumberUtils.equals(evse.getProtocolVer(), IotConstants.PROTOCOL_VERSION_200)
            || NumberUtils.equals(evse.getProtocolVer(), IotConstants.PROTOCOL_VERSION_320)) {
            // 2.0协议3.2协议适配
            List<String> plugNoList = plugService.getPlugNoList(report.getEvseNo());
            if (CollectionUtils.isEmpty(plugNoList)) {
                return;
            }
            List<PlugVo> plugVoList = redisIotReadService.getPlugList(plugNoList);
            if (CollectionUtils.isEmpty(plugVoList)) {
                return;
            }
            plugVoList.forEach(plugVo -> {
                if (!evse.getGwno().equals(plugVo.getGwno())) {
                    PlugVo plugCache = new PlugVo();
                    // 重置枪头缓存里的网关编号
                    plugCache.setEvseNo(plugVo.getEvseNo())
                        .setBizStatus(evse.getBizStatus())
                        .setPlugNo(plugVo.getPlugNo())
                        .setGwno(evse.getGwno());
                    redisIotRwService.updatePlugRedisCache(plugCache);
                }
            });

        }

        // 3.7协议设置枪头功率，如果未填写或传入0时，根据现有数据计算功率值
//        if (NumberUtils.equals(evse.getProtocolVer(), IotConstants.PROTOCOL_VERSION_370)) {
//            if (!CollectionUtils.isEmpty(report.getPlugs())) {
//                report.getPlugs().stream().forEach(e -> {
//                    if ((e.getPower() == null || e.getPower().equals(BigDecimal.ZERO)) &&
//                            e.getCurrent() != null && e.getCurrent().getMax() != null &&
//                            e.getVoltage() != null && e.getVoltage().getMax() != null) {
//                        e.setPower(e.getVoltage().getMax().multiply(e.getCurrent().getMax()));
//                    }
//                });
//            }
//        }

        if (null != report.getPlugNum() &&
            report.getPlugNum() > 0 &&
            !CollectionUtils.isEmpty(report.getPlugs())) {
            List<PlugPo> plugList = new ArrayList<>();
            List<PlugPo> plugListForUpdate = new ArrayList<>();
            List<PlugPo> plugs = plugService.getPlugByEvseId(report.getEvseNo(), false);
            Map<Integer, PlugPo> map = plugs.stream().collect(
                Collectors.toMap(PlugPo::getPlugId, plugPo -> plugPo, (key1, key2) -> key1));
            report.getPlugs().forEach(p -> {
                if (p.getPlugId() < 1) {
                    log.warn("枪头编号存在问题: {}", JsonUtils.toJsonString(p));
                    return;
                }

                PlugPo plug = map.get(p.getPlugId());
                if (plug == null) {
                    log.info("枪头不存在。plug: {}", p);
                    plug = new PlugPo();
                    plug.setPlugId(p.getPlugId()).setCurrentMax(p.getCurrent().getMax())
                        .setCurrentMin(p.getCurrent().getMin())
                        .setVoltageMax(p.getVoltage().getMax())
                        .setVoltageMin(p.getVoltage().getMin()).setPower(p.getPower())
                        .setName(p.getPlugId() + "枪")
                        .setEvseId(report.getEvseNo())
                        .setPlugStatus(PlugStatus.UNKNOWN)
                        .setGwno(evse.getGwno());
                    plugList.add(plug);

                    // 推送创建事件
                    redisIotUpdateWrapper.updateRedisPlugCacheV2(IotEvent.CREATE, site, evse, plug);

                } else {
                    PlugPo plugForupdate = new PlugPo();
                    plugForupdate.setPlugStatus(plug.getPlugStatus())
                        .setPower(p.getPower())
                        .setId(plug.getId());
                    if (p.getPower() != null && BigDecimal.ZERO.compareTo(p.getPower()) != 0) {
                        plugForupdate.setPower(p.getPower());
                    }
                    if (p.getVoltage() != null) {
                        if (p.getVoltage().getMin() != null && BigDecimal.ZERO.compareTo(p.getVoltage().getMin()) != 0) {
                            plugForupdate.setVoltageMin(p.getVoltage().getMin());
                        }
                        if (p.getVoltage().getMax() != null && BigDecimal.ZERO.compareTo(p.getVoltage().getMax()) != 0) {
                            plugForupdate.setVoltageMax(p.getVoltage().getMax());
                        }
                    }
                    if (p.getCurrent() != null) {
                        if (p.getCurrent().getMin() != null) {
                            plugForupdate.setCurrentMin(p.getCurrent().getMin());
                        }
                        if (p.getCurrent().getMax() != null && BigDecimal.ZERO.compareTo(p.getCurrent().getMax()) != 0) {
                            plugForupdate.setCurrentMax(p.getCurrent().getMax());
                        }
                    }

                    if (StringUtils.isBlank(plug.getGwno())) {
                        plugForupdate.setGwno(evse.getGwno());
                    }

                    plugListForUpdate.add(plugForupdate);

                    plug.setPower(p.getPower());
                    if (p.getVoltage() != null) {
                        if (p.getVoltage().getMin() != null && BigDecimal.ZERO.compareTo(p.getVoltage().getMin()) != 0) {
                            plug.setVoltageMin(p.getVoltage().getMin());
                        }
                        if (p.getVoltage().getMax() != null && BigDecimal.ZERO.compareTo(p.getVoltage().getMax()) != 0) {
                            plug.setVoltageMax(p.getVoltage().getMax());
                        }
                    }
                    if (p.getCurrent() != null) {
                        if (p.getCurrent().getMin() != null) {
                            plug.setCurrentMin(p.getCurrent().getMin());
                        }
                        if (p.getCurrent().getMax() != null && BigDecimal.ZERO.compareTo(p.getCurrent().getMax()) != 0) {
                            plug.setCurrentMax(p.getCurrent().getMax());
                        }
                    }

                    // 推送状态变更事件
                    redisIotUpdateWrapper.updateRedisPlugCacheV2(IotEvent.STATE_CHANGE, site, evse,
                        plug);
                }
            });
            if (!CollectionUtils.isEmpty(plugList)) {
                this.plugService.batchInsert(plugList);
            }
            if (!CollectionUtils.isEmpty(plugListForUpdate)) {
                this.plugService.batchUpdate(plugListForUpdate);
            }
        } else if (null != report.getPlugNum() &&
            report.getPlugNum() > 0 &&
            (NumberUtils.equals(evse.getProtocolVer(), IotConstants.PROTOCOL_VERSION_345) ||
                NumberUtils.equals(evse.getProtocolVer(), IotConstants.PROTOCOL_VERSION_304)
            )) {
            log.info("3.4协议网关注册时不带枪头信息，此处进更新redis网关编号");
            List<PlugPo> plugs = plugService.getPlugByEvseId(report.getEvseNo(), false);
            if (plugs != null) {
                log.info("桩上报枪头个数:{}, 数据库存在的枪头个数:{}", report.getPlugNum(),
                    plugs.size());
                if (report.getPlugNum() != plugs.size()) {
                    if (site != null && "2002182018715249684".equals(site.getSiteId())) {
                        // 上海鼎充体验站充电站, 不打印 error 级日志
                        log.info(
                            "新桩枪头数注册时与原先不一致: evseNo = {}, 云端枪头数 = {}, 桩上报枪头数 = {}, 场站 = {},{}",
                            report.getEvseNo(), plugs.size(), report.getPlugNum(),
                            site.getName(),
                            site.getSiteId());
                    } else {
                        log.error(
                            "新桩枪头数注册时与原先不一致: evseNo = {}, 云端枪头数 = {}, 桩上报枪头数 = {}, 场站 = {},{}",
                            report.getEvseNo(), plugs.size(), report.getPlugNum(),
                            site == null ? "" : site.getName(),
                            site == null ? "" : site.getSiteId());
                    }
                }
                plugs.forEach(plugPo -> {
                    plugPo.setGwno(evse.getGwno());
                    redisIotUpdateWrapper.updateRedisPlugCacheV2(IotEvent.STATE_CHANGE, site, evse,
                        plugPo);
                });
            }
        }
    }

    /**
     * 桩配置相关操作
     *
     * @param report
     * @param evsePo
     */
    public void configRelatedOperations(EvseResiterReqV2 report, EvsePo evsePo) {
        if (!evseCfgRedisService.configDeliveredEnabled()) {
            return;
        }
        log.info(">> configRelatedOperations. evseNo: {}", report.getEvseNo());
        boolean deliverOrNot = evseCfgRedisService.configCanBeDelivered(report.getEvseNo());
        if (!deliverOrNot) {
            log.info("桩频繁注册，不执行桩配置相关操作。evseNo: {}", report.getEvseNo());
            return;
        }

        EvseCfgResultPo cfgResultPo = evseCfgResultRwDs.getByEvseNo(evsePo.getEvseId(),
            Boolean.FALSE);
        EvseCfgResendDto resendDto = new EvseCfgResendDto();
        resendDto.setEvseNo(evsePo.getEvseId());
        this.comparePriceCodeAndResendPrice(report, evsePo, cfgResultPo, resendDto);
        this.deliverConfigByConditions(report, evsePo, cfgResultPo, resendDto);
        this.resendEvseConfig(resendDto);
        log.info("<< configRelatedOperations end. evseNo: {}", report.getEvseNo());
    }

    /**
     * 桩电价重发
     *
     * @param report
     * @param evsePo
     * @param cfgResultPo
     * @return 是否下发上次计费
     */
    private void comparePriceCodeAndResendPrice(final EvseResiterReqV2 report,
        final EvsePo evsePo,
        final EvseCfgResultPo cfgResultPo,
        EvseCfgResendDto resendDto) {

        if (evsePo == null || StringUtils.isBlank(evsePo.getSiteId())) {
            resendDto.setSendPrice(false);
            return;
        }

        boolean needRefresh = true;

        if (report.getPriceCode() != null) {
            // 若桩上报的计费模板 与 平台期望计费模板一致，则无需重发
            Long priceCode = cfgResultPo != null ? cfgResultPo.getExpectPriceCode()
                : evsePo.getPriceCode();
            needRefresh = !NumberUtils.equals(report.getPriceCode(), priceCode);
        }

        if (needRefresh) {
            resendDto.setSendPrice(true);
            String evseNo = report.getEvseNo();
            try {
                evseCfgRedisService.addToLimited(evseNo);
                Optional.ofNullable(cfgResultPo)
                    .filter(e -> NumberUtils.gtZero(e.getExpectPriceCode()))
                    .ifPresentOrElse(e -> {
                        log.info("桩下发上次计费. evseNo: {}", evseNo);
                        resendDto.setPriceId(e.getExpectPriceCode());
                        resendDto.setFetchPirce(true);
                    }, () -> {
                        log.info("桩下发场站默认计费. evseNo: {}", evseNo);
                        resendDto.setFetchPirce(true);
                    });
            } catch (Exception e) {
                log.warn("桩注册重新下发计费异常. evseNo: {}, error: {}", evseNo,
                    e.getMessage(), e);
            }
        }
    }

    /**
     * 桩配置重发
     *
     * @param report
     * @param evsePo
     * @param cfgResultPo
     * @return 是否下发上次配置
     */
    private void deliverConfigByConditions(final EvseResiterReqV2 report,
        final EvsePo evsePo,
        final EvseCfgResultPo cfgResultPo,
        EvseCfgResendDto resendDto) {

        if (evsePo == null || StringUtils.isBlank(evsePo.getSiteId())) {
            resendDto.setSendConfig(false);
            return;
        }
        EvseProtocolType protocol = Optional.ofNullable(report.getProtocol())
            .orElse(evsePo.getProtocol());
        if (EvseProtocolType.CCTIA.equals(protocol)) {
            // CCTIA桩不支持配置项下发
            resendDto.setSendConfig(false);
            return;
        }

        String evseNo = evsePo.getEvseId();
        try {
            evseCfgRedisService.addToLimited(evseNo);
            resendDto.setSendConfig(true);
            Optional.ofNullable(cfgResultPo)
                .filter(e -> NumberUtils.gtZero(e.getExpectCfgCode()))
                .ifPresentOrElse(e -> {
                    log.info("桩下发上次配置. evseNo: {}", evseNo);
                    resendDto.setFetchConfig(false);
                    resendDto.setIotConfigId(e.getExpectCfgCode());
                }, () -> {
                    log.info("桩下发场站默认配置. evseNo: {}", evseNo);
                    resendDto.setFetchConfig(true);
                });
        } catch (Exception e) {
            log.warn("桩注册重新下发配置异常. evseNo: {}, error: {}", evseNo,
                e.getMessage(), e);
        }
    }

    private void resendEvseConfig(@NonNull EvseCfgResendDto resendDto) {
        if (!resendDto.isSendConfig() && !resendDto.isSendPrice()) {
            log.debug("无需重发配置 evseNo: {}", resendDto.getEvseNo());
            return;
        }
        dataCoreFeignClient.getResendEvseCfgParam(resendDto)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .doOnNext(x -> evseCfgBizService.resendEvseConfig(resendDto, x))
            .subscribe();
    }

    public Mono<Integer> mgcUpgradeStatus(MgcUpgradeResultReq req, String gwno) {
        return Mono.just(gwno)
            .map(no -> gwInfoRwDs.getByGwno(gwno, true))
            .map(gw -> {
                UpgradeLogPo upgradeLogPo = upgradeLogRwDs.getById(gw.getExpectUpgradeLogId(),
                    false);
                if (null == upgradeLogPo) {
                    log.error("微网控制器中升级记录数据错误: gw = {}", gw);
                    throw new DcArgumentException("微网控制器中升级记录数据错误");
                }

                upgradeLogPo.setUpgradeStatus(req.getUpgradeStatus());
                switch (req.getUpgradeStatus()) {
                    case CMD_RECEIVE:
                        upgradeLogPo.setReceiveTime(new Date());
                        break;
                    case UPGRADE_FAIL:
                        upgradeLogPo.setRemark(req.getMsg());
                        upgradeLogPo.setFinishTime(new Date());
                        break;
                    case UPGRADE_SUCCESS:
                        upgradeLogPo.setFinishTime(new Date());

                        // 更新控制器信息
                        gw.setActualUpgradeLogId(gw.getExpectUpgradeLogId());
                        gwInfoRwDs.update(this.dtoMap2Po(gw));
                        break;
                }

                upgradeLogRwDs.updateUpgradeLog(upgradeLogPo);
                return 1;
            });
    }

    private GwInfoPo dtoMap2Po(GwInfoDto dto) {
        return new GwInfoPo()
            .setVer(dto.getVer())
            .setGwno(dto.getGwno())
            .setCityCode(dto.getCityCode())
            .setSwVer(dto.getSwVer())
            .setSwVerCode(dto.getSwVerCode())
            .setSourceCodeVer(dto.getSourceCodeVer())
            .setExpectUpgradeLogId(dto.getExpectUpgradeLogId())
            .setActualUpgradeLogId(dto.getActualUpgradeLogId());
    }

    /**
     * 微网控制器资源监控
     *
     * @param data 上报数据
     * @param gwno 微网控制器编号
     */
    public void gwAlert(MgcAlert data, String gwno) {
        if (StringUtils.isBlank(gwno)) {
            log.error("微网控制器编号无效: gwno = {}", gwno);
            return;
        }

        if (null == data.getCpu() &&
            null == data.getDisk() &&
            null == data.getMemory() &&
            null == data.getTemperature()) {
            log.warn("告警对象全为空，不做处理");
            return;
        }

        GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(gwno, true);
        if (null == gw) {
            log.error("微网控制器无效: gwno = {}", gwno);
            return;
        }

        // 控制器本身状态
        if ((null != data.getCpu() && EquipAlertStatus.ABNORMAL.equals(data.getCpu().getAlert())) ||
            (null != data.getTemperature() && EquipAlertStatus.ABNORMAL.equals(
                data.getTemperature().getAlert())) ||
            (null != data.getDisk() && EquipAlertStatus.ABNORMAL.equals(data.getDisk().getAlert()))
            ||
            (null != data.getMemory() && EquipAlertStatus.ABNORMAL.equals(
                data.getMemory().getAlert()))) {
            gwInfoRwDs.updateStatus(gwno, GwStatus.ERROR);
        } else {
            gwInfoRwDs.updateStatus(gwno, GwStatus.NORMAL);
        }

        redisEquipRtDataService.pushMgcData(gwno, data);

        // FIXME: 暂时使用原有逻辑，但逻辑不符合，需要调整
        // 调整传递告警列表
        SiteCtrlVo alert = new SiteCtrlVo();
        alert.setCtrlNo(gwno)
            .setStatus(SiteCtrlStatusType.ALERT)
            .setSiteId(gw.getSiteId())
            .setSiteName(gw.getSiteName())
            .setSiteCommId(gw.getSiteCommId())
            .setAlertCode(DeviceStatusCodeType.C3600.getCode());
        dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, alert);
    }
}
