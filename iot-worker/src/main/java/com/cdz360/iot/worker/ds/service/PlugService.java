package com.cdz360.iot.worker.ds.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.worker.ds.mapper.PlugMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;

@Slf4j
@Service
public class PlugService {

    @Autowired
    private PlugMapper plugMapper;

    @Autowired
    private PlugRoDs plugRoDs;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchInsert(List<PlugPo> plugs) {
//        if (CollectionUtils.isEmpty(plugs)) {
//            return;
//        }
        try {
            this.plugMapper.batchInsert(plugs);
        } catch (Exception e) {
            log.error("batchInsert error: {}", e.getMessage(), e);
            if (e instanceof SQLIntegrityConstraintViolationException) {
                plugs.stream().forEach(plug -> this.update(plug));
            }
        }
    }

    public boolean update(PlugPo plugPo) {
        return this.plugMapper.update(plugPo) > 0;
    }

    @Transactional
    public boolean batchUpdate(List<PlugPo> plugPos) {
//        if (CollectionUtils.isEmpty(plugPos)) {
//            return false;
//        }
        return this.plugMapper.batchUpdate(plugPos) > 0;
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean updateOrderNo(String evseNo, int plugIdx, String orderNo) {
        return this.plugMapper.updateOrderNo(evseNo, plugIdx, orderNo) > 0;
    }

    public boolean removeByEvseId(String evseId) {
        return this.plugMapper.removeByEvseId(evseId) > 0;
    }


    public boolean batchUpdateOrderNo(PlugPo plugPo) {
        return this.plugMapper.batchUpdateOrderNo(plugPo) > 0;
    }

    public PlugPo getPlug(String evseId, int plugId, boolean lock) {
        return this.plugMapper.getPlug(evseId, plugId, lock);
    }

    public List<PlugPo> getPlugByEvseId(String evseId, boolean lock) {
        return this.plugMapper.getPlugByEvseId(evseId, lock);
    }

    public ListResponse<PlugVo> getPlugList(ListPlugParam param) {
        return plugRoDs.getPlugList(param);
    }

    public List<PlugPo> listPlug(@Nullable String evseId,
                                 @Nullable List<PlugStatus> statusList,
                                 @Nullable Long start,
                                 @Nullable Integer size,
                                 boolean lock) {
        return this.plugMapper.listPlug(evseId, statusList, start, size, lock);
    }
//
//    public ListResponse<PlugVo> getPlugList(ListPlugParam param) {
//        return new ListResponse<PlugVo>(plugMapper.getPlugList(param));
//    }

    public List<String> getPlugNoList(String evseNo) {
        return plugRoDs.getPlugNoList(evseNo);
    }

}
