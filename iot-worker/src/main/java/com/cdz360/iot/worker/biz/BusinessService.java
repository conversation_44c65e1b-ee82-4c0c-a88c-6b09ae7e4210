package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.common.utils.RedisIdGenerator;
import com.cdz360.iot.ds.ro.EvseReportModuleRoDs;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.rw.EvseCfgResultRwDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.BaseSshRequest;
import com.cdz360.iot.model.base.CollectEvseStatusRequest;
import com.cdz360.iot.model.base.CommonRpcResponse;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.cfg.CfgEvseAll;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseParam;
import com.cdz360.iot.model.evse.cfg.CfgTime;
import com.cdz360.iot.model.evse.cfg.ChargeV2;
import com.cdz360.iot.model.evse.cfg.PriceSchema;
import com.cdz360.iot.model.evse.cfg.WhiteCardV2;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.po.EvseReportModulePo;
import com.cdz360.iot.model.evse.upgrade.UpgradeEvseResultRequest;
import com.cdz360.iot.model.gw.MqDebugEvseCmd;
import com.cdz360.iot.model.gw.MqEvseCfgCmd;
import com.cdz360.iot.model.gw.MqRebootEvseCmd;
import com.cdz360.iot.model.gw.MqUpgradeEvseCmd;
import com.cdz360.iot.model.order.type.EvseDebugMethod;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.type.GwRequestMethod;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.model.type.SshStatus;
import com.cdz360.iot.worker.feign.DeviceBusinessFeignClient;
import com.cdz360.iot.worker.feign.IotDeviceMgmFeignClient;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import com.cdz360.iot.worker.model.gw.UpgradeReq;
import com.cdz360.iot.worker.model.iot.UpgradeRequest;
import com.cdz360.iot.worker.rest.internal.BusinessController;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;


@Service
public class BusinessService {
    private final Logger logger = LoggerFactory.getLogger(BusinessController.class);
    private final Integer MAX_EVSE_CFG_COUNT = 500;
    @Autowired
    private MqService mqService;
    //    @Autowired
//    private GwInfoRwQueryMapper gwInfoRwQueryMapper;
    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private DeviceBusinessFeignClient deviceBusinessReactiveFeignClient;

    @Autowired
    private EvseRwQueryMapper evseRwQueryMapper;
    @Autowired
    private SequenceRwService sequenceRwService;
    @Autowired
    private EvseCfgRedisService evseCfgRedisService;
    @Autowired
    private IotDeviceMgmFeignClient deviceMgMClient;
    @Autowired
    private RedisIotRwService redisIotRwService;
    @Autowired
    private DcEventPublisher dcEventPublish;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private EvseCfgResultRwDs evseCfgResultRwDs;

    @Autowired
    private EvseCfgBizService evseCfgBizService;

    @Autowired
    private GwCacheService gwCacheService;

    @Autowired
    private PlugRoDs plugRoDs;
    @Autowired
    private EvseReportModuleRoDs evseReportModuleRoDs;

    public BaseRpcResponse upgradeEvse(UpgradeRequest request) {

        IotAssert.isNotNull(request, "请传入升级请求参数");
        IotAssert.isNotNull(request.getEvseIds(), "请传入升级桩列表");
        IotAssert.isTrue(!request.getEvseIds().isEmpty(), "升级桩列表不能为空");

        List<EvsePo> evsePos = evseRwQueryMapper.getEvsePos(
                request.getEvseIds()
                        .stream()
                        .distinct()
                        .collect(Collectors.toList()
                        ), false);

        Map<String, List<String>> map = new HashMap<>();

        //TODO: 校验网关是否离线
        //TODO: 去重
        //TODO: 参数校验等

        //分组聚合
        evsePos.forEach(x -> {
            if (map.containsKey(x.getGwno())) {
                map.get(x.getGwno()).add(x.getEvseId());
            } else {
                List<String> list = new ArrayList<>();
                list.add(x.getEvseId());
                map.put(x.getGwno(), list);
            }
        });

        map.forEach((key, value) -> {

            GwInfoPo gwInfoDto = gwCacheService.getGwInfo(key);
            IotAssert.isNotNull(gwInfoDto, "找不到网关:" + key);
            IotAssert.isTrue(gwInfoDto.getStatus() == GwStatus.NORMAL,
                    String.format("网关%s当前状态异常: %s", key, gwInfoDto.getStatus().name()));


            this.upgradeEvseV2(request, gwInfoDto, value);

        });


        return new BaseRpcResponse();
    }


    private void upgradeEvseV2(UpgradeRequest request, GwInfoPo gwInfo, List<String> evseNoList) {

        MqUpgradeEvseCmd req = new MqUpgradeEvseCmd();
        req.setDownloadType(request.getDownloadType());
        req.setDownloadUsername(request.getDownloadUsername());
        req.setDownloadPasscode(request.getDownloadPasscode());
        req.setEvseBundle(request.getEvseBundle());

        IotGwCmdCacheVo<MqUpgradeEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(gwInfo.getGwno())
                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                .setCmd(IotGwCmdType2.CE_UPGRADE)
                .setPlugId(0)
                .setData(req);
        evseNoList.stream().forEach(evseNo -> {
            String seq = sequenceRwService.getNextOutRequestSeq();
            req.setEvseNo(evseNo);
            req.setTaskNo(seq);
            cmd.setEvseNo(evseNo);
            cmd.setSeq(seq);
            //mqttService.publishMessage(gwno, JsonUtils.toJsonString(cmd));
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
        });

    }

    public CommonRpcResponse<Integer> startGwTunnel(String gwno, String serverAddr, int serverPort, int localPort, int expire) {
        logger.info("startGwTunnel. gwno: {}, serverAddr: {}, serverPort: {}, localPort: {}, expire: {}", gwno, serverAddr, serverPort, localPort, expire);
        CommonRpcResponse<Integer> res = new CommonRpcResponse<>();
        GwInfoDto po = this.gwInfoRoDs.getByGwnoAndEnable(gwno);
        if (po == null) {
            logger.error("网关编号={} 不存在，无法开启ssh通道", gwno);
            //            throw new IotServerException(String.format("网关编号:[%s]不存在，无法开启ssh通道", gwno));
            res.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
            res.setError(String.format("网关编号:[%s]不存在，无法开启ssh通道", gwno));
            return res;
        }

        int remoteTunnelPort = new Random().nextInt(10000) + 40000;

//        if (po.getStatus() == GwStatus.NORMAL) {
        BaseSshRequest.builder builder = new BaseSshRequest.builder();
        builder.setData(GwRequestMethod.TUNNEL, SshStatus.START, serverAddr,
                        serverPort, remoteTunnelPort,
                        localPort, expire)
                .setGwno(gwno);
        BaseSshRequest.REQ req = builder.build();
        mqService.publishMessage(po,false, req.toString()); // TODO: MGC升级后可移除
        mqService.publishMessage(po,true, req.toString());
        logger.info("MQTT PUBLISH gwno:{} mes:{}", gwno, req.toString());

        res.setData(remoteTunnelPort);
        return res;
    }

    public BaseRpcResponse stopGwTunnel(String gwno) {
        logger.info("stopGwTunnel. gwno: {}", gwno);

        BaseRpcResponse res = BaseRpcResponse.newInstance();
        GwInfoDto dto = this.gwInfoRoDs.getByGwnoAndEnable(gwno);
        if (dto == null) {
            logger.error("网关编号={} 不存在，无法关闭ssh通道", gwno);
            //            throw new IotServerException(String.format("网关编号:[%s]不存在，无法关闭ssh通道", gwno));
            res.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
            res.setError(String.format("网关编号:[%s]不存在，无法开启ssh通道", gwno));
            return res;
        }

        BaseSshRequest.builder builder = new BaseSshRequest.builder();
        builder.setData(GwRequestMethod.TUNNEL, SshStatus.STOP)
                .setGwno(gwno);
        BaseSshRequest.REQ req = builder.build();
        mqService.publishMessage(dto, false, req.toString());
        mqService.publishMessage(dto, true, req.toString());
        logger.info("MQTT PUBLISH gwno:{} msg:{}", gwno, req.toString());
        return res;
    }

    @Transactional
    public BaseRpcResponse modifyEvseCfg(CfgEvseParam cfgEvseParam) {
        logger.debug("modifyEvseCfg. cfgEvseParam: {}", JsonUtils.toJsonString(cfgEvseParam));

        IotAssert.isTrue(cfgEvseParam.getEvseIds() != null &&
                !cfgEvseParam.getEvseIds().isEmpty(), "请输入桩列表");

        IotAssert.isTrue(cfgEvseParam.getEvseIds().size() <= MAX_EVSE_CFG_COUNT, "超出单次最大桩数");

        //价格配置项->价格编码 priceSchemaList code-> list<code>
        if (cfgEvseParam.getCfgEvse().getPrice() != null) {
            var priceSchemaListCodeList = cfgEvseParam.getCfgEvse().getPrice().stream()
                    .map(ChargeV2::getCode)
                    .collect(Collectors.toList());

            priceSchemaListCodeList.forEach(e -> {
                IotAssert.isTrue(0 <= e && e <= 255, "价格编码, 取值范围 0 ~ 255");
            });

            var timeListTimeList = cfgEvseParam.getCfgEvse().getPrice().stream()
                    .map(m -> {
                        CfgTime obj = new CfgTime();
                        obj.setStartTime(m.getStartTime()).setStopTime(m.getStopTime());
                        return obj;
                    }).collect(Collectors.toList());
            //获取timeList 中StartTime 为List,并计算去重后的值
            var timeListStartTimeListCount = cfgEvseParam.getCfgEvse().getPrice().stream()
                    .map(ChargeV2::getStartTime)
                    .collect(Collectors.toList()).stream().distinct().count();
            //获取timeList 中StopTime 为List,并计算去重后的值
            var timeListStopTimeListCount = cfgEvseParam.getCfgEvse().getPrice().stream()
                    .map(ChargeV2::getStopTime)
                    .collect(Collectors.toList()).stream().distinct().count();
            //若不相等，则存在重复
            IotAssert.isTrue(timeListStartTimeListCount == timeListTimeList.size() &&
                    timeListStopTimeListCount == timeListTimeList.size(), "timeList 中时间段存在重复");

            // 判断按时段电价的时间区间是否正确
            cfgEvseParam.getCfgEvse().getPrice()
                    .forEach(e -> {
                        IotAssert.isTrue(priceSchemaListCodeList.contains(e.getCode()), "按时段电价编码必须在 priceSchemaList 里存在");
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
                        try {
                            Date maxTime = simpleDateFormat.parse("24:00");
                            long maxTimeStamp = maxTime.getTime();
                            Date startTime = simpleDateFormat.parse(e.getStartTime());
                            long startTimeStamp = startTime.getTime();
                            Date stopTime = simpleDateFormat.parse(e.getStopTime());
                            long stopTimeStamp = stopTime.getTime();

                            IotAssert.isTrue(startTimeStamp < stopTimeStamp, "计价开始时间大于或等于结束时间");
                            IotAssert.isTrue(startTimeStamp < maxTimeStamp && stopTimeStamp <= maxTimeStamp, "计价开始时间或结束时间错误");

                            timeListTimeList.forEach(f -> {
                                try {
                                    Date fStartTime = simpleDateFormat.parse(f.getStartTime());
                                    long fStartTimeStamp = fStartTime.getTime();
                                    Date fStopTime = simpleDateFormat.parse(f.getStopTime());
                                    long fStopTimeStamp = fStopTime.getTime();
                                    if (!(startTimeStamp == fStartTimeStamp && stopTimeStamp == fStopTimeStamp)) {
                                        IotAssert.isTrue(startTimeStamp < fStartTimeStamp || startTimeStamp >= fStopTimeStamp, "timeList 中时间段重叠");
                                        IotAssert.isTrue(stopTimeStamp <= fStartTimeStamp || stopTimeStamp > fStopTimeStamp, "timeList 中时间段重叠");
                                    }
                                } catch (ParseException e1) {
                                    throw new DcArgumentException(e1.getMessage());
                                }
                            });
                        } catch (ParseException e1) {
                            throw new DcArgumentException(e1.getMessage());
                        }
                    });

        }


        //查找请求参数中重复的桩
        var duplicateEvses = cfgEvseParam
                .getEvseIds()
                .stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .filter(e -> e.getValue() > 1)
                //                .collect(Collectors.toList())//.anyMatch(e -> e.getValue() > 1);
                //                .stream()
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        IotAssert.isTrue(duplicateEvses.isEmpty(), String.format("存在重复的桩编号: %s", duplicateEvses));

        //筛选，若有不存在于数据库的桩，则报错
        List<EvsePo> evsePos = evseRwQueryMapper.getEvsePos(cfgEvseParam.getEvseIds(), false);
        List<String> invalidEvses = new ArrayList<>(cfgEvseParam.getEvseIds());
        invalidEvses.removeAll(evsePos.stream().map(EvsePo::getEvseId).collect(Collectors.toList()));
        IotAssert.isTrue(invalidEvses.isEmpty(), String.format("找不到桩: %s", invalidEvses));

        //查找数据库中离线的网关
        List<String> gwnos = new ArrayList<>(evsePos
                .stream()
                .map(EvsePo::getGwno)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .keySet());


        evseCfgRedisService.rPushCfgEvseParam(cfgEvseParam);
        //不要报错  防止重复下发
        try {
            modifyEvseCfgJob();
        } catch (Exception e) {
            logger.error("配置下发请求报错>>{},{}", e.getMessage(), e);
        }
        return new BaseRpcResponse();
    }


    @Transactional
    public void modifyEvseCfgJob() {
        CfgEvseParam cfgEvseParam = evseCfgRedisService.lPopCfgEvseParam();
        if (cfgEvseParam == null) {
            return;
        }
        List<EvsePo> evsePos = evseRwQueryMapper.getEvsePos(cfgEvseParam.getEvseIds(), false);
        //走配置下发流程
        Map<String, List<EvsePo>> gwnoMap = new ConcurrentHashMap<>();// key: gwno, val: List<EvsePo>
        evsePos.forEach(e -> {
            if (null == gwnoMap.get(e.getGwno())) {
                gwnoMap.put(e.getGwno(), new CopyOnWriteArrayList<>());
            }
            gwnoMap.get(e.getGwno()).add(e);
        });

        // TODO: 网关不做位下发判定条件，需要根据桩配置下发的状态约定

        final String CfgVer = sequenceRwService.getNextCfgVer();
        cfgEvseParam.getCfgEvse().setCfgVer(CfgVer);


        Map<String, GwInfoDto> gwInfoDtoMap = new HashMap<>();
        gwnoMap.forEach((key, value) -> {

            if (gwInfoDtoMap.get(key) == null) {
                GwInfoDto gwInfoDto = gwInfoRoDs.getByGwno(key);
                gwInfoDtoMap.put(key, gwInfoDto);
            }


        });
        // cache cfg to redis
        evsePos.forEach(e -> {
            GwInfoDto gwInfoDto = gwInfoDtoMap.get(e.getGwno());
            Assert.notNull(gwInfoDto, "找不到网关: " + e.getGwno());
            evseCfgRedisService.appendV2(RedisIdGenerator.getCfgId(e.getGwno(), e.getEvseId()), cfgEvseParam.getCfgEvse());

        });

        // publish gw by mqtt
        gwnoMap.forEach((key, value) -> {
            GwInfoDto gwInfoDto = gwInfoDtoMap.get(key);
            if (gwInfoDto.getVer() == IotConstants.IOT_GW_VER_1) {
                throw new DcServiceException("废弃的逻辑", Level.ERROR);

            } else if (gwInfoDto.getVer() == IotConstants.IOT_GW_VER_2) {
                IotGwCmdCacheVo<MqEvseCfgCmd> cmd = new IotGwCmdCacheVo<>();
                MqEvseCfgCmd mqEvseCfgCmd = new MqEvseCfgCmd();
                mqEvseCfgCmd.setEvseNos(value.stream().map(EvsePo::getEvseId).collect(Collectors.toList()));
                mqEvseCfgCmd.setCfgVer(CfgVer);
                cmd.setGwno(key)
                        .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                        .setCmd(IotGwCmdType2.CE_MODIFY_CFG)
                        .setData(mqEvseCfgCmd);
                //mqttService.publishMessage(key, JsonUtils.toJsonString(cmd));

                mqService.publishMessage(gwInfoDto, false, JsonUtils.toJsonString(cmd));
            }
        });
    }


    @Transactional
    public Mono<BaseResponse> evseCfgResult(CfgEvseResultReqV2 req, String gwno) {
        logger.info("配置下发返回结果: {}", JsonUtils.toJsonString(req));
        if (StringUtils.isEmpty(req.getCfgVer())) {
            logger.info("网关返回下发结果版本为空，不做操作");
            return Mono.just(RestUtils.success());
        }

        // 配置下发结果更新
        EvseCfgResultPo originCfgResult = evseCfgResultRwDs.getByEvseNo(req.getEvseNo(), true);
        logger.info("originCfgResult = {}", originCfgResult);
        if (originCfgResult == null) {
            logger.warn("获取桩配置下发结果失败。evseNo = {}", req.getEvseNo());
            return Mono.just(RestUtils.success());
        }

        // 更新桩配置下发状态信息
        return evseCfgBizService.evseCfgResultHandler(req.getEvseNo(), req, originCfgResult)
            .map(o -> o);
    }

    public Mono<BaseResponse> upgradeResult(UpgradeReq req) {
        logger.info("固件升级结果上报。request: {}", req);

        Assert.isTrue(!StringUtils.isEmpty(req.getTaskNo()), "固件升级上报失败。taskNo为空。");
        Assert.isTrue(!StringUtils.isEmpty(req.getEvseId()), "固件升级上报失败。evseId为空。");


        UpgradeEvseResultRequest upgradeEvseResultRequest = new UpgradeEvseResultRequest();
        upgradeEvseResultRequest.setEvseId(req.getEvseId());
        upgradeEvseResultRequest.setResult(req.getResult());
        upgradeEvseResultRequest.setTaskNo(req.getTaskNo());

//        logger.info("固件升级结果上报: {}", deviceBussnessFeignClient.upgradeEvseResult(upgradeEvseResultRequest));

        return deviceMgMClient.upgradeEvseResult(upgradeEvseResultRequest)
                .doOnNext(res -> {
                    logger.info("固件升级结果上报: {}", res);
                });
    }

    public void evseCfgInfo(List<CfgEvseAll> cfgList) {

        List<CfgEvseAllV2> cfgEvseAllV2List = cfgList.stream().map(e -> {
            CfgEvseAllV2 cfgEvseAllV2 = new CfgEvseAllV2();
            cfgEvseAllV2.setEvseNo(e.getEvseId());
//            cfgEvseAllV2.setEvsePasscodeVer(e.get);
            cfgEvseAllV2.setAdminCodeA(e.getAdminCodeA());
            cfgEvseAllV2.setAdminCodeB(e.getAdminCodeB());
            if (e.getCharge() != null && e.getCharge().getChargeId() != null) {
                cfgEvseAllV2.setPriceCode(e.getCharge().getChargeId().intValue());


                Map<Integer, PriceSchema> priceSchemaMap = CollectionUtils.isEmpty(e.getCharge().getPriceSchemaList()) ?
                        new HashMap<>() :
                        e.getCharge().getPriceSchemaList().stream().collect(Collectors.toMap(PriceSchema::getCode,
                                Function.identity()));

                if (!priceSchemaMap.isEmpty() && CollectionUtils.isNotEmpty(e.getCharge().getTimeList())) {
                    cfgEvseAllV2.setPrice(
                            e.getCharge()
                                    .getTimeList()
                                    .stream()
                                    .map(ex -> {
                                        ChargeV2 chargeV2 = new ChargeV2();
                                        chargeV2.setCode(ex.getCode());
                                        PriceSchema priceSchema = priceSchemaMap.get(ex.getCode());
                                        if (ex.getCode() != null && priceSchema != null && priceSchema.getElecPrice() != null) {
                                            chargeV2.setElecPrice((new BigDecimal(priceSchema.getElecPrice()).movePointLeft(4)));
                                        }
                                        if (e.getCharge().getServPrice() != null) {
                                            chargeV2.setServPrice((new BigDecimal(e.getCharge().getServPrice()).movePointLeft(4)));
                                        }
                                        chargeV2.setStartTime(ex.getStartTime());
                                        chargeV2.setStopTime(ex.getStopTime());

                                        return chargeV2;
                                    }).collect(Collectors.toList()));

                }

            }
            cfgEvseAllV2.setVin(e.getVin());
            cfgEvseAllV2.setQrUrl(e.getQrUrl());
            if (e.getWhiteCards() != null && !e.getWhiteCards().isEmpty()) {
                cfgEvseAllV2.setWhiteCards(e.getWhiteCards().stream().map(ex -> {
                    WhiteCardV2 whiteCardV2 = new WhiteCardV2();
                    whiteCardV2.setCardNumber(ex.getCardNumber());
                    whiteCardV2.setPasscode(ex.getPasscode());
                    return whiteCardV2;
                }).collect(Collectors.toList()));
            }
            cfgEvseAllV2.setBmsVer(e.getBmsVer());
            cfgEvseAllV2.setAutoStop(e.getAutoStop());
            cfgEvseAllV2.setBalanceMode(e.getBalanceMode());
            cfgEvseAllV2.setCombination(e.getCombination());
            cfgEvseAllV2.setHeating(e.getHeating());
            cfgEvseAllV2.setBatteryCheck(e.getBatteryCheck());
            cfgEvseAllV2.setIsolation(e.getIsolation());
            cfgEvseAllV2.setManualMode(e.getManualMode());
            cfgEvseAllV2.setQueryChargeRecord(e.getQueryChargeRecord());
            cfgEvseAllV2.setQrCharge(e.getQrCharge());
            cfgEvseAllV2.setCardCharge(e.getCardCharge());
            cfgEvseAllV2.setNoCardCharge(e.getNoCardCharge());
            cfgEvseAllV2.setTimedCharge(e.getTimedCharge());
            cfgEvseAllV2.setDayVolume(e.getDayVolume());
            cfgEvseAllV2.setNightVolume(e.getNightVolume());
            cfgEvseAllV2.setStopMode(e.getStopMode());
            return cfgEvseAllV2;
        }).collect(Collectors.toList());

        evseCfgInfoV2(cfgEvseAllV2List);
    }

    public void evseCfgInfoV2(List<CfgEvseAllV2> cfgList) {
        for (CfgEvseAllV2 e : cfgList) {
            IotAssert.isNotBlank(e.getEvseNo(), "桩编号不能为空");
            CfgEvseAllV2 cfgEvseAllV2 = evseCfgRedisService.lastCfgEvseAllV2(e.getEvseNo());
            if (cfgEvseAllV2 == null) {
                evseCfgRedisService.appendCfgEvseAllV2(e.getEvseNo(), e);
            } else {
                // 使用reflection，两个对象做字段非空融合
                List.of(CfgEvseAllV2.class.getDeclaredFields()).stream().forEach(fd -> {
                    try {
                        fd.trySetAccessible();
                        Object ofd = fd.get(cfgEvseAllV2);
                        Object dfo = fd.get(e);
                        if (ofd != null && dfo != null) {
                            logger.warn("同时不为空的桩配置项: {}, {}", ofd, dfo);
                        } else {
                            fd.set(cfgEvseAllV2, ofd == null ? dfo : ofd);
                        }
                        fd.setAccessible(false);
                    } catch (IllegalAccessException ex) {
                        logger.error("{}", ex.getMessage());
                    }
                });
                evseCfgRedisService.appendCfgEvseAllV2(e.getEvseNo(), cfgEvseAllV2);
            }

        }
    }

    public CfgEvseAllV2 evseGetCfgInfo(String evseId) {
        EvsePo evsePo = evseRwQueryMapper.getEvsePo(evseId, false);
        Assert.notNull(evsePo, "充电桩不存在");
        CfgEvseAllV2 cfgEvseAllV2 = null;

        cfgEvseAllV2 = this.evseCfgRedisService.getCfgEvseAll(evseId);

        return cfgEvseAllV2;
    }

    public Optional<Pair<Integer, CfgEvseAllV2>> getCfgAndProtocol(String evseNo) {
        EvsePo evsePo = evseRwQueryMapper.getEvsePo(evseNo, false);
        Assert.notNull(evsePo, "充电桩不存在");

        CfgEvseAllV2 cfgEvseAllV2 = this.evseCfgRedisService.getCfgEvseAll(evseNo);
        if (cfgEvseAllV2 == null) return Optional.empty();

        return Optional.of(Pair.of(evsePo.getProtocolVer(), cfgEvseAllV2));
    }

    public void evseGetCfg(List<String> evseIds) {
        this.evseGetCfg(evseIds, Boolean.TRUE);
    }

    public void evseGetCfg(List<String> evseIds, boolean isLog) {
        if (isLog) {
            logger.info("evseGetCfg. evseNos = {}", evseIds);
        }

        IotAssert.isTrue(CollectionUtils.isNotEmpty(evseIds), "请输入桩列表");

        IotAssert.isTrue(evseIds.size() <= MAX_EVSE_CFG_COUNT, "超出单次最大桩数");
        //查找请求参数中重复的桩
        var duplicateEvses = evseIds
                .stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .filter(e -> e.getValue() > 1)
                //                .collect(Collectors.toList())//.anyMatch(e -> e.getValue() > 1);
                //                .stream()
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        IotAssert.isTrue(duplicateEvses.isEmpty(), String.format("存在重复的桩编号: %s", duplicateEvses));

        //查找不存在于数据库的桩
        List<EvsePo> evsePos = evseRwQueryMapper.getEvsePos(evseIds, false);

        List<String> invalidEvses = new ArrayList<>(evseIds);
        invalidEvses.removeAll(evsePos.stream().map(EvsePo::getEvseId).collect(Collectors.toList()));

        IotAssert.isTrue(invalidEvses.isEmpty(), String.format("找不到桩: %s", invalidEvses));

        //查找数据库中离线的网关
        List<String> gwnos = new ArrayList<>(evsePos
                .stream()
                .map(EvsePo::getGwno)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .keySet());

        List<GwInfoDto> gwnoList = gwInfoRoDs.getGwList(gwnos, null);

        Map<String, GwInfoDto> gwnoAllMap = gwnoList
                .stream()
                .collect(Collectors.toMap(GwInfoDto::getGwno, Function.identity()));

        Map<String, List<String>> gwnoMap = new ConcurrentHashMap<>();// key: gwno, val: String:evseId

        for (EvsePo e : evsePos) {
            gwnoMap.computeIfAbsent(e.getGwno(), k -> new LinkedList<String>());
            gwnoMap.get(e.getGwno()).add(e.getEvseId());
        }
        // publish gw by mqtt
        gwnoMap.forEach((key, value) -> {
            GwInfoDto gwInfo = gwnoAllMap.get(key);
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);

                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                        || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    value.forEach(e -> {
                        IotGwCmdCacheVo<String> cmd = new IotGwCmdCacheVo<>();
                        cmd.setGwno(key)
                                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                                .setCmd(IotGwCmdType2.CE_GET_CFG)
                                .setData(e);
                        //mqttService.publishMessage(key, JsonUtils.toJsonString(cmd));

                        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
                    });
                } else {
                    logger.error("找不到桩: {}，无法下发获取桩配置指令。", key);
                }
            }
        });
    }


    public void collectEvseStatus() {
        long start = 0;
        int size = 999;
        List<GwInfoPo> gwList = this.gwInfoRoDs.listGw(List.of(GwStatus.NORMAL), null, null, start, size);
        gwList.forEach(gw -> {
            CollectEvseStatusRequest.REQ req = new CollectEvseStatusRequest.REQ(gw.getGwno());
//            req.setSeq(sequenceRwService.getNextOutRequestSeq());
            mqService.publishMessage(gw, false, JsonUtils.toJsonString(req));
        });
    }


    public void rebootEvse(String evseNo) {
        logger.info(" evseNo = {}", evseNo);
        List<EvseStatus> temp = List.of(EvseStatus.BUSY, EvseStatus.OFFLINE);

        EvseVo evse = this.redisIotRwService.getEvseRedisCache(evseNo);
        if (evse == null) {
            logger.warn("evse is offline. evseNo = {}", evseNo);
            throw new DcServiceException("桩" + evseNo + "未在线");
        } else if (temp.contains(evse.getStatus())) {
            throw new DcServiceException("桩充电中和离线时无法重启");
        }
        MqRebootEvseCmd req = new MqRebootEvseCmd();
        req.setEvseNo(evseNo);
        req.setTaskNo(UUID.randomUUID().toString());    // TODO: 需要填入任务号
        IotGwCmdCacheVo<MqRebootEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(evse.getGwno())
                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                .setCmd(IotGwCmdType2.CE_REBOOT)
                .setEvseNo(evse.getEvseNo())
                .setPlugId(0)
                .setData(req);
        //this.mqttService.publishMessage(evse.getGwno(), JsonUtils.toJsonString(cmd));
        GwInfoDto gwInfo = this.gwInfoRoDs.getByGwno(evse.getGwno());
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
        //this.redisIotUpdateWrapper.addIotGwCmd(cmd);    // 把下行指令放到 redis 缓存
        //return res;
    }

    public void evseDebug(String evseNo, EvseDebugMethod debugMethod, String msg) {
        logger.info(" evseNo = {}", evseNo);
        EvseVo evse = this.redisIotRwService.getEvseRedisCache(evseNo);
        if (evse == null) {
            logger.warn("evse is offline. evseNo = {}", evseNo);
            throw new DcServiceException("桩" + evseNo + "未在线");
        }
        MqDebugEvseCmd req = new MqDebugEvseCmd();
        req.setEvseNo(evseNo);
        req.setDebugMethod(debugMethod);
        req.setMsg(msg);
        IotGwCmdCacheVo<MqDebugEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(evse.getGwno())
                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                .setCmd(IotGwCmdType2.CE_DEBUG)
                .setEvseNo(evse.getEvseNo())
                .setPlugId(0)
                .setData(req);
        //this.mqttService.publishMessage(evse.getGwno(), JsonUtils.toJsonString(cmd));

        GwInfoDto gwInfo = this.gwInfoRoDs.getByGwno(evse.getGwno());
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
    }

    public void moduleQuery(String evseNo) {
        EvseVo evse = this.redisIotRwService.getEvseRedisCache(evseNo);
        if (evse == null) {
            logger.warn("evse is offline. evseNo = {}", evseNo);
            throw new DcServiceException("桩" + evseNo + "未在线");
        }
        EvseReportModulePo reportModulePo = evseReportModuleRoDs.getByEvseNo(evseNo);
        IotAssert.isTrue(reportModulePo != null
            && StringUtils.isNotBlank(reportModulePo.getModuleType()), "桩启动时未上报模块信息，可重启充电桩再次上报");
        IotGwCmdCacheVo<String> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(evse.getGwno())
            .setSeq(this.sequenceRwService.getNextOutRequestSeq())
            .setCmd(IotGwCmdType2.CE_GET_MODULE)
            .setData(evse.getEvseNo());
        GwInfoDto gwInfo = this.gwInfoRoDs.getByGwno(evse.getGwno());
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
    }

}
