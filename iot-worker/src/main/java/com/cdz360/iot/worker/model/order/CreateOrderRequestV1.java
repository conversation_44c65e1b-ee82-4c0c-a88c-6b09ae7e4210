package com.cdz360.iot.worker.model.order;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.type.StopMode;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "创建充电订单请求")
public class CreateOrderRequestV1 extends BaseObject {


//    @Schema(description = "网关编号", hidden=true)
//    private String gwno;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "充电桩编号")
    private String evseId;

    @Schema(description = "充电枪编号")
    private int plugId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private SupplyType supply = SupplyType.UNKNOWN;

    @Schema(description = "账户总可用余额, 单位'分'")
    private Integer balance;

    @Schema(description = "开启类型")
    private OrderStartType startType;

    @Schema(description = "账号, 此处为卡号 (逻辑卡号) 或 17位 VIN 码")
    private String accountNo;

    @Schema(description = "可实时扣费金额, 单位'分'")
    private Integer amount;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String seq;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String method = "ORDER";

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String cmd;

    private StopMode stopMode;

    public String getOrderNo() {
        return orderNo;
    }

    public CreateOrderRequestV1 setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public CreateOrderRequestV1 setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public int getPlugId() {
        return plugId;
    }

    public CreateOrderRequestV1 setPlugId(int plugId) {
        this.plugId = plugId;
        return this;
    }

    public SupplyType getSupply() {
        return supply;
    }

    public CreateOrderRequestV1 setSupply(SupplyType supply) {
        this.supply = supply;
        return this;
    }

    public Integer getBalance() {
        return balance;
    }

    public CreateOrderRequestV1 setBalance(Integer balance) {
        this.balance = balance;
        return this;
    }

    public OrderStartType getStartType() {
        return startType;
    }

    public CreateOrderRequestV1 setStartType(OrderStartType startType) {
        this.startType = startType;
        return this;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public CreateOrderRequestV1 setAccountNo(String accountNo) {
        this.accountNo = accountNo;
        return this;
    }

    public Integer getAmount() {
        return amount;
    }

    public CreateOrderRequestV1 setAmount(Integer amount) {
        this.amount = amount;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public CreateOrderRequestV1 setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public CreateOrderRequestV1 setMethod(String method) {
        this.method = method;
        return this;
    }

    public String getCmd() {
        return cmd;
    }

    public CreateOrderRequestV1 setCmd(String cmd) {
        this.cmd = cmd;
        return this;
    }

    public StopMode getStopMode() {
        return stopMode;
    }

    public CreateOrderRequestV1 setStopMode(StopMode stopMode) {
        this.stopMode = stopMode;
        return this;
    }
}
