package com.cdz360.iot.worker.cfg;

public interface ApiUrl {

    // 网关初始化接口
    String URL_GW_REGISTER = "/iot/register?gwno={gwno}&mac={mac}&ip={ip}&wanIp={wanIp}";

    String URL_DO_LOGIN = "/iot/doLogin?gwno={gwno}&realm={realm}&token={token}&ip={ip}";

    String URL_SAVE_OR_GET_IDLE_GW = "/iot/saveOrGetIdleGw?gwno={gwno}&ip={ip}";

    String URL_GW_STATUS_TASK = "/iot/gwstatustask?sec={sec}";

    String URL_EVSE_STATUS_REPORT = "/iot/report/evse";

    String URL_ORDER_STATUS_REPORT = "/iot/report/order?gwno={gwno}";

    String URL_CMD_LIST_GWNO = "/iot/cmd/listGwno";

    String URL_CMD_LIST_CMD = "/iot/cmd/listCmd?gwno={gwno}";

    String URL_CMD_TAG_SENT = "/iot/cmd/tagSent?gwno={gwno}&seqList={seqList}";

    String URL_CARD_AUTH = "/iot/auth/card";

    String URL_CFG_EVSE = "/iot/cfg/evse?gwno={gwno}&evseId={evseId}&cfgVer={cfgVer}";

    String URL_CFG_EVSE_RESULT = "/iot/cfg/evse/result?evseId={evseId}&cfgVer={cfgVer}&result={result}&whiteCardsResult={whiteCardsResult}";

    String URL_ORDER_STATUS_REPORT_CREATE = "/iot/order/create?gwno={gwno}";

    String URL_ORDER_STATUS_REPORT_START = "/iot/order/start?gwno={gwno}";

    String URL_ORDER_STATUS_REPORT_UPDATE = "/iot/order/update?gwno={gwno}";

    String URL_ORDER_STATUS_REPORT_STOP = "/iot/order/stop?gwno={gwno}";

    String URL_ORDER_FEE_REFRESH = "/iot/order/fee/refresh?gwno={gwno}";
}
