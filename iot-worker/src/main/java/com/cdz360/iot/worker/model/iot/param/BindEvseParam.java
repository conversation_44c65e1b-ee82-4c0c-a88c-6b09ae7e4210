package com.cdz360.iot.worker.model.iot.param;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.evse.type.EvseBizType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BindEvseParam {

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "桩名称")
    private String name;

    @Schema(description = "运营模式")
    private EvseBizType bizType;

    @Schema(description = "设备型号ID")
    private Long modelId;

    @Schema(description = "额定功率", example = "123")
    private Integer power;

//    @Schema(description = "桩型号", example = "TN-QCZ02-A10")
//    private String model;

    // 目前仅海外版使用
    @Schema(description = "桩型号", example = "慧充-A")
    private String model;

    // 目前仅海外版使用
    @Schema(description = "品牌")
    private String brand;

    // 目前仅海外版使用
    @Schema(description = "电桩类型")
    private SupplyType supply;

    @Schema(description = "桩出厂日期", example = "2020-01-01")
    private Date produceDate;

    @Schema(description = "质保到期日", example = "2020-01-01")
    private Date expireDate;

    @Schema(description = "桩出厂编号", example = "T17223584")
    private String produceNo;

    @Schema(description = "SIM卡号")
    private String iccid;

    @Schema(description = "IMSI")
    private String imsi;

    @Schema(description = "IMEI")
    private String imei;

    @Schema(description = "模块类型")
    private String moduleType;

//    @Schema(description = "模块数量")
//    private Integer moduleNum;

    @Schema(description = "槽位数")
    private Integer slotNum;

    @Schema(description = "是否使用场站配置", example = "true")
    private Boolean useSiteSetting;

    private List<PlugPo> plugList;

}
