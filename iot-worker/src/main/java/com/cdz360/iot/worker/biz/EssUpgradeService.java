package com.cdz360.iot.worker.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.model.ess.mqtt.EssUpgradeReq;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssUpgradeService {

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    public void upgradeTimeout(String msg) {
        try {
            EssUpgradeReq req = JsonUtils.fromJson(msg, EssUpgradeReq.class);
            String dno = req.getDno();
            if (StringUtils.isNotBlank(dno)) {
                EssPo ess = essRoDs.getByDno(dno);
                if (null != ess) {
                    Long upgradeLogId = ess.getExpectUpgradeLogId();
                    UpgradeLogPo upgradeLog = upgradeLogRwDs.getById(upgradeLogId, true);
                    if (UpgradeStatus.UPGRADE_SUCCESS.equals(upgradeLog.getUpgradeStatus())) {
                        log.warn("升级已经成功，不需要重新调整记录状态");
                        return;
                    } else if (UpgradeStatus.UPGRADE_FAIL.equals(upgradeLog.getUpgradeStatus())) {
                        log.warn("升级已经失败，不需要重新调整记录状态");
                        return;
                    } else if (UpgradeStatus.UPGRADE_TIMEOUT.equals(
                        upgradeLog.getUpgradeStatus())) {
                        log.warn("升级已超时，不需要重新调整记录状态: {}", upgradeLog.getRemark());
                        return;
                    }

                    UpgradeLogPo updateLog = new UpgradeLogPo().setId(upgradeLogId)
                        .setUpgradeStatus(UpgradeStatus.UPGRADE_TIMEOUT)
                        .setRemark("云端监控下发超时");
                    upgradeLogRwDs.updateUpgradeLog(updateLog);
                }
            }
        } catch (Exception e) {
            log.warn("处理户储升级超时异常: {}", e.getMessage(), e);
        }
    }
}