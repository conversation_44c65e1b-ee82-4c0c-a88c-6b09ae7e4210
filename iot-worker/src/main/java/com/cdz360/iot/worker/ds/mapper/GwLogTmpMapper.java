package com.cdz360.iot.worker.ds.mapper;
import com.cdz360.iot.worker.model.iot.po.GwLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GwLogTmpMapper {
//    void batchInsert(@Param("logs") List<GwLogPo> logs);
    List<String> getGroupGwno();
    List<GwLogPo> getLastTmpLog(String gwno);
    int deleteBefore(@Param("gwno") String gwno, @Param("beforeTime") Long before);
    int copyOut(@Param("gwno") String gwno, @Param("beforeTime") Long before);
}
