package com.cdz360.iot.worker.cfg;

import org.springframework.context.annotation.Configuration;

@Configuration
//@EnableSwagger2
public class SwaggerConfig {

//    @Bean
//    public Docket petApi() {
//
//        final String desc = "物联网服务";
//        final ApiInfo apiInfo = new ApiInfo("iot-worker", desc,
//                "0.0.1", "", null,
//                "", "", Collections.emptyList());
//
//        return new Docket(DocumentationType.SWAGGER_2).select()
//                .apis(RequestHandlerSelectors.basePackage("com.cdz360.iot.worker.rest")) // 仅显示 com.cdz360.iot.worker.api 目录下的接口
//                .build().apiInfo(apiInfo);
//    }


}
