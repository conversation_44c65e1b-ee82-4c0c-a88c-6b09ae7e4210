package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.BsBoxPo;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.model.dongzheng.BsBoxSettingPo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class OpenHlhtRestHystrixFeignClientFactory implements FallbackFactory<OpenHlhtRestFeignClient> {

    @Override
    public OpenHlhtRestFeignClient apply(Throwable throwable) {
        return new OpenHlhtRestFeignClient() {
            @Override
            public Mono<ListResponse<Pair<Long,BigDecimal>>> queryOrderlyPowerList(List<Long> transformerIds) {
                log.error("【服务熔断】。Service = {}, 获取有序充电信息",
                        DcConstants.KEY_FEIGN_OPEN_HLHT_REST);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, OpenHlhtRestFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT_REST);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super OpenHlhtRestFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT_REST);
        return null;
    }
}