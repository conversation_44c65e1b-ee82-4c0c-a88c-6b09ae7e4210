package com.cdz360.iot.worker.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.gw.param.AuditMgcStatusParam;
import com.cdz360.iot.model.gw.param.UpgradeParam;
import com.cdz360.iot.worker.biz.MgcBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "微网控制器相关接口", description = "微网控制器")
@RequestMapping(value = "/mgc", produces = MediaType.APPLICATION_JSON_VALUE)
public class MgcRest {

    @Autowired
    private MgcBizService mgcBizService;

    @GetMapping(value = "/upgradeMgc.json")
    public Mono<BaseResponse> upgradeMgc(@RequestParam(value = "gwno") String gwno,
                                         @RequestParam(value = "jarPath") String jarPath) {
        log.info("升级微网控制器 gwno = {}, jarPath = {}", gwno, jarPath);
        return this.mgcBizService.upgradeMgc(gwno, jarPath);

    }


    /**
     * 仅用于测试环境
     */
    @GetMapping(value = "/upgradeAllMgc.json")
    public Mono<BaseResponse> upgradeAllMgc(@RequestParam(value = "jarPath") String jarPath) {
        log.info("升级全部微网控制器 jarPath = {}", jarPath);
        return this.mgcBizService.upgradeAllMgc(jarPath);
    }

    /**
     * 云端主动查询 MGC 状态
     */
    @PostMapping(value = "/sendAuditStatusMqReq.json")
    public Flux<BaseResponse> sendAuditStatusMqReq(@RequestBody AuditMgcStatusParam param) {
        log.info("云端主动查询 MGC 状态 param = {}", param);
        return this.mgcBizService.sendAuditStatusMqReq(param);

    }

    @GetMapping(value = "/findMgcGwnoByKeyword")
    public Mono<ObjectResponse<String>> findMgcGwnoByKeyword(@RequestParam(value = "keyword") String keyword) {
        log.info("查询控制器编号 jarPath = {}", keyword);
        return this.mgcBizService.findMgcGwnoByKeyword(keyword);
    }

    @Operation(summary = "微网控制器升级")
    @PostMapping(value = "/upgrade")
    public Mono<BaseResponse> mgcUpgrade(@RequestBody UpgradeParam param) {
        log.info("微网控制器升级: param = {}", JsonUtils.toJsonString(param));
        return this.mgcBizService.mgcUpgrade(param)
                .map(r -> RestUtils.success());
    }
}
