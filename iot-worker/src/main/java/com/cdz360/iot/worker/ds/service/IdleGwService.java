package com.cdz360.iot.worker.ds.service;

import com.cdz360.iot.model.site.po.IdleGwPo;
import com.cdz360.iot.worker.ds.mapper.IdleGwMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
public class IdleGwService {
    private final Logger logger = LoggerFactory.getLogger(IdleGwService.class);

    @Autowired
    private IdleGwMapper idleGwMapper;

    public IdleGwPo getByIp(String ip, boolean lock) {
        return this.idleGwMapper.getByIp(ip, lock);
    }

    public boolean save(IdleGwPo uninitGwPo) {
        int result = 0;
        IdleGwPo idleGwPoDB = idleGwMapper.getByIp(uninitGwPo.getIp(), false);
        if (ObjectUtils.isEmpty(idleGwPoDB)) {
            result = this.idleGwMapper.insert(uninitGwPo);
        } else {
            result = this.idleGwMapper.update(uninitGwPo);
        }
        return result > 0;
    }
}
