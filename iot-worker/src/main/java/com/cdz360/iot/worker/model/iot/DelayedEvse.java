package com.cdz360.iot.worker.model.iot;

import lombok.Data;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
public class DelayedEvse implements Delayed {

    private String evseNo;
    private Long availableTime;

    public DelayedEvse(String evseNo, Long delayTime) {
        this.evseNo = evseNo;
        this.availableTime = System.currentTimeMillis() + delayTime;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        //判断avaibleTime是否大于当前系统时间，并将结果转换成MILLISECONDS
        long diffTime = availableTime - System.currentTimeMillis();
        return unit.convert(diffTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        return (int) (this.availableTime - ((DelayedEvse) o).getAvailableTime());
    }
}
