package com.cdz360.iot.worker.rest.external;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.gw.GwMsg;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.biz.IotService;
import com.cdz360.iot.worker.biz.MgcBizService;
import com.cdz360.iot.worker.biz.north.OssBizService;
import com.cdz360.iot.worker.biz.south.IotSouthBizService;
import com.cdz360.iot.worker.model.gw.GwRegisterReqV2;
import com.cdz360.iot.worker.model.gw.LoginRes;
import com.cdz360.iot.worker.model.gw.MgcAlert;
import com.cdz360.iot.worker.model.gw.RegisterRes;
import com.cdz360.iot.worker.model.iot.param.GwLoginParam;
import com.cdz360.iot.worker.utils.IpUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.security.NoSuchAlgorithmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "MGC设备接口", description = "南向-网关接口")
@RequestMapping("/iot/")
public class IotMgcRest extends IotRestBase {
    @Autowired
    private IotService iotService;

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private OssBizService ossBizService;


    @Autowired
    private MgcBizService mgcBizService;

    @Autowired
    private IotSouthBizService iotService2;

    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }

    @Operation(summary = "网关初始化接口")
    @PostMapping(value = "/gw/register", params = {"v=3"})
    public GwObjResMsg<RegisterRes> register(
        ServerHttpRequest request,
        @RequestParam(value = "n") String gwno,
        @RequestParam(value = "v") int ver,
        @RequestBody GwObjReqMsg<GwRegisterReqV2> req) {
        log.info(">> 网关初始化接口。req = {}", req);
        long startTime = System.nanoTime();    // debug 性能问题
        IotAssert.isNotNull(req, "请求参数不能为空");
        IotAssert.isNotNull(req.getData(), "请求参数不能为空.");

        String wanIp = IpUtils.getIpAddress(request);
        RegisterRes ret = this.iotService.register(gwno, req.getData(), wanIp, ver);
        log.info("<<");
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "register", "网关初始化", startTime);
        //debugPerformance("网关初始化", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), ret);
    }

    @Operation(summary = "网关登录接口")
    @PostMapping(value = "/gw/login", params = {"v=3"})
    public GwObjResMsg<LoginRes> login(
        ServerHttpRequest request,
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwMsg req) throws NoSuchAlgorithmException {

        // log.info(LoggerHelper.formatEnterLog(request));

        log.info(">> 网关登录接口。gwno = {}, authHd = {}", gwno, authHd);
        long startTime = System.nanoTime();    // debug 性能问题

        String ip = IpUtils.getIpAddress(request);

        var token = super.getToken(authHd);
        if (StringUtils.isBlank(token)) {
            // tian
            log.info("<< no authHd");
            throw new DcTokenException(gwno, "没有 token");
        }
        GwLoginParam param = new GwLoginParam();
        param.setGwno(gwno)
            .setSup(req.getSup())
            .setProtocolVer(ver)
            .setToken(token)
            .setWanIp(ip)
            .setLanIp(req.getLanIp())
            .setMac(req.getMac())
            .setBootTime(req.getBootTime())
            .setSwVer(req.getSwVer())
            .setSwVerCode(req.getSwVerCode())
            .setGitCommitId(req.getGitCommitId());
        LoginRes ret = this.iotService.login(param);
        log.info("<<");
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "login", "网关登录", startTime);
        //debugPerformance("网关登录", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), ret);
    }
    @Operation(summary = "获取文件上传的STS信息")
    @PostMapping(value = "/gw/oss/detail", params = {"v=3"})
    public Mono<CommonResponse<OssStsDto>> getOssSts(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno) {
        super.checkToken(authHd, gwno);
        return ossBizService.getOssSts();
    }

    @Operation(summary = "MGC 状态上报", description = "设备收到云端下发status audit后上报状态给云端")
    @PostMapping(value = "/gw/status", params = {"v=3"})
    public Mono<BaseResponse> mgcStatusAudit(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwMsg req) {
        super.checkToken(authHd, gwno);
        return mgcBizService.mgcAuditStatusRes(req);
    }

    @Operation(summary = "告警上报")
    @PostMapping(value = {"/gw/alert"}, params = {"v=3"})
    public BaseResponse alert(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<MgcAlert> req) {
        log.info(">> 微网控制器告警上报 authHd = {}, gwno = {},req = {}", authHd, gwno, JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题
//        checkToken(authHd, gwno);
        iotService2.gwAlert(req.getData(), gwno);
        LogHelper.logLatency(log, IotGwChargeRestV3.class.getSimpleName(),
            "alert", "告警上报", startTime);
        return GwRestUtils.buildResponse(req.getSeq());
    }
}
