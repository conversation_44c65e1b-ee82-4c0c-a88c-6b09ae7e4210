package com.cdz360.iot.worker.model.iot;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigInteger;
import java.util.List;
import lombok.Data;


@Schema(description = "价格配置")
@Data
public class PriceStrategy {

    @Schema(description = "要生效的桩ID列表")
    private List<String> evseIds;

    @Schema(description = "要生效的场站ID列表(东正)")
    private List<BigInteger> dzSiteIds;

    @Schema(description = "服务费单价, 单位'分'", required = true)
    private int servPrice;

    @Schema(description = "分时计费的价格数组", required = true)
    private List<PriceItem> items;

    @Data
    public static class PriceItem {

        @Schema(description = "开始时间, 单位'秒'.", required = true)
        int startTime;


        @Schema(description = "结束时间, 单位'秒'.", required = true)
        int endTime;

        @Schema(description = "电价, 单位'分'", required = true)
        int elecPrice;
    }
}
