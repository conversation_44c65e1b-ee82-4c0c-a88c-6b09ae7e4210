package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.auth.CusAuthReqEx;
import com.cdz360.iot.model.auth.CusAuthRes;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = BizUserHystrixFeignClientFactory.class)
public interface BizUserFeignClient {

    @RequestMapping(value = "/api/cus/auth", method = RequestMethod.POST)
    Mono<ObjectResponse<CusAuthRes>> auth(@RequestParam(value = "passcode", required = false) String passcode,
                                          @RequestBody CusAuthReqEx cusAuthReq);
}
