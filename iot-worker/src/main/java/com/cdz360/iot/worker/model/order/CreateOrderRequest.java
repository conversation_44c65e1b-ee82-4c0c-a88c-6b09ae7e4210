package com.cdz360.iot.worker.model.order;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.type.StopMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "创建充电订单请求")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class CreateOrderRequest extends BaseObject {


//    @Schema(description = "网关编号", hidden=true)
//    private String gwno;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "充电桩编号")
    private String evseNo;

    @Schema(description = "充电枪编号")
    private String plugNo;

    @Schema(description = "充电枪编号")
    private int plugId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private SupplyType supply = SupplyType.UNKNOWN;

    @Schema(description = "账户总可用余额, 单位'元'")
    private BigDecimal balance;

    @Schema(description = "开启类型")
    private OrderStartType startType;

    @Schema(description = "账号, 此处为卡号 (逻辑卡号) 或 17位 VIN 码")
    private String accountNo;

    @Schema(description = "可实时扣费金额, 单位'元'")
//    private Integer amount;
    private BigDecimal amount;
    @Schema(description = "可实时扣费金额, 单位'元'")
    private BigDecimal frozenAmount;
    @Schema(description = "账户总可用余额, 单位'元'")
    private BigDecimal totalAmount;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String seq;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String method = "ORDER";

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String cmd;

    @Schema(description = "限制的最大SOC")
    @JsonProperty("soc")
    private Integer limitSoc;

    private StopMode stopMode;

    // 扣款账户类型: 个人账户, 集团账户、权益账户、即充即退
    private Integer defaultPayType;

    // 扣款账户类型
    private PayAccountType accountType;

    // 停充码
    private String orderStopCode;

    // 差异化价格信息
    private ChargePriceVo price;

//    public Integer getLimitSoc() {
//        return limitSoc;
//    }

//    public CreateOrderRequest setLimitSoc(Integer limitSoc) {
//        this.limitSoc = limitSoc;
//        return this;
//    }

    public String getOrderNo() {
        return orderNo;
    }

    public CreateOrderRequest setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getEvseNo() {
        return evseNo;
    }

    public CreateOrderRequest setEvseNo(String evseNo) {
        this.evseNo = evseNo;
        return this;
    }

//    public int getPlugId() {
//        return plugId;
//    }
//
//    public CreateOrderRequest setPlugId(int plugId) {
//        this.plugId = plugId;
//        return this;
//    }

    public SupplyType getSupply() {
        return supply;
    }

    public CreateOrderRequest setSupply(SupplyType supply) {
        this.supply = supply;
        return this;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public CreateOrderRequest setBalance(BigDecimal balance) {
        this.balance = balance;
        return this;
    }

    public OrderStartType getStartType() {
        return startType;
    }

    public CreateOrderRequest setStartType(OrderStartType startType) {
        this.startType = startType;
        return this;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public CreateOrderRequest setAccountNo(String accountNo) {
        this.accountNo = accountNo;
        return this;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public CreateOrderRequest setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public CreateOrderRequest setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public CreateOrderRequest setMethod(String method) {
        this.method = method;
        return this;
    }

    public String getCmd() {
        return cmd;
    }

    public CreateOrderRequest setCmd(String cmd) {
        this.cmd = cmd;
        return this;
    }

    public StopMode getStopMode() {
        return stopMode;
    }

    public CreateOrderRequest setStopMode(StopMode stopMode) {
        this.stopMode = stopMode;
        return this;
    }

    public Integer getDefaultPayType() {
        return defaultPayType;
    }

    public CreateOrderRequest setDefaultPayType(Integer defaultPayType) {
        this.defaultPayType = defaultPayType;
        return this;
    }

    public PayAccountType getAccountType() {
        return accountType;
    }

    public void setAccountType(PayAccountType accountType) {
        this.accountType = accountType;
    }
}
