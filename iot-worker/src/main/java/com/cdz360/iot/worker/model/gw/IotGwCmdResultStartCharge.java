package com.cdz360.iot.worker.model.gw;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Classname IotGwCmdResultStartCharge
 * @Description
 * @Date 11/20/2019 4:22 PM
 * @Created by Rafael
 */
@Data
public class IotGwCmdResultStartCharge {
    @Schema(description = "订单号")
    private String orderNo;//	是	唯一的订单号
    @Schema(description = "订单号")
    private String evseNo;//	是	桩编号

    @Schema(description = "枪编号")
    private String plugNo;

    @Schema(description = "枪编号")
    private Integer plugId;//	是	充电枪ID
    /**
     * 0: 成功
     * 2001: 枪未连接
     * 2002: 枪口已有进行中的订单;
     * 2003: 已经有预约的线下充电
     * 2004: 桩故障
     * 2256: 桩离线
     * 2257: 桩未响应
     * 以网关协议为准
     */
    @Schema(description = "启动充电的返回结果")
    private Integer result;
}