package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.model.ess.mqtt.DeliverUserEssCfgReq;
import com.cdz360.iot.model.ess.po.EssPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssCfgService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    public void cfgDeliveryTimeout(String msg) {
        try {
            DeliverUserEssCfgReq req = JsonUtils.fromJson(msg,
                DeliverUserEssCfgReq.class);
            String dno = req.getDno();
            if (StringUtils.isNotBlank(dno)) {
                EssPo ess = essRoDs.getByDno(dno);
                if (null != ess) {
                    if (null != req.getCfgId() && null != ess.getDeliverCfgId() &&
                        req.getCfgId().equals(ess.getDeliverCfgId())) {
                        essRwDs.updateEss(new EssPo().setDno(dno)
                            .setCfgStatus(EquipCfgStatus.SEND_TIMEOUT));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理户储下发超时异常: {}", e.getMessage(), e);
        }
    }
}