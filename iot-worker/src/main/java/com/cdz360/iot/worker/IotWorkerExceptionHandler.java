package com.cdz360.iot.worker;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.*;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.biz.IotCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;

@ControllerAdvice(annotations = RestController.class)
public class IotWorkerExceptionHandler {
    private final Logger logger = LoggerFactory.getLogger(IotWorkerExceptionHandler.class);

    @Autowired
    private IotCacheService iotCacheService;

    /**
     * 处理 token 异常, 返回 http 401 并带一个 WWW-Authenticate 的头
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({DcTokenException.class})
    public ResponseEntity handleTokenException(DcTokenException ex,
                                               ServerHttpRequest request) {
        String gwno;

        if (StringUtils.isNotBlank(ex.getGwno())) {
            gwno = ex.getGwno();
        } else {
            gwno = request.getQueryParams().getFirst("n");
        }

        if (StringUtils.isBlank(gwno)) {
            logger.error("token异常时网关编号为空，需要接入调查", ex);
        }

        logger.info("handleTokenException, gwno = {}", gwno);
        logger.info(ex.getMessage());

        StringBuilder buf = new StringBuilder();
        buf.append("Basic realm=\"").append(this.iotCacheService.getOrUpdateRealm(gwno))
                .append("\"");

        ResponseEntity res = ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .header("WWW-Authenticate", buf.toString()).build();
        return res;

    }


    @ExceptionHandler({DcServiceException.class,
            DcArgumentException.class, DcGwException.class})
    public ResponseEntity<BaseGwResponse> handleServiceException(DcException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());

        if (ex instanceof DcGwException) {
            ResponseEntity<BaseGwResponse> res = ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            return res;
        } else {
            ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
            return res;
        }
    }


    @ExceptionHandler
    public ResponseEntity<BaseGwResponse> handle(Exception ex) {
        logger.error("error = {}", ex.getMessage(), ex);
        // TODO: 需要按异常类型做处理, 返回不同的status和error
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        }
        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
        return res;
    }

//    @ExceptionHandler({DcServiceException.class})
//    public ResponseEntity<BaseGwResponse> handleServiceException(DcServiceException ex) {
//        logger.warn(ex.getMessage(), ex);
//        BaseGwResponse result = new BaseGwResponse();
//        result.setStatus(ex.getStatus());
//        result.setError(ex.getMessage());
//        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
//        return res;
//    }

    @ExceptionHandler({DcServerException.class})
    public ResponseEntity<BaseGwResponse> handleServerException(DcServerException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
        return res;
    }

//    @ExceptionHandler({DcArgumentException.class})
//    public ResponseEntity<BaseGwResponse> handleIotArgumentException(DcArgumentException ex) {
//        logger.warn(ex.getMessage(), ex);
//        BaseGwResponse result = new BaseGwResponse();
//        result.setStatus(ex.getStatus());
//        result.setError(ex.getMessage());
//        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
//        return res;
//    }
//
//    @ExceptionHandler({DcGwException.class})
//    public ResponseEntity<BaseGwResponse> handleIotGwException(DcGwException ex) {
//        logger.warn(ex.getMessage(), ex);
//        BaseGwResponse result = new BaseGwResponse();
//        result.setStatus(ex.getStatus());
//        result.setError(ex.getMessage());
//        ResponseEntity<BaseGwResponse> res = ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
//        return res;
//    }


}
