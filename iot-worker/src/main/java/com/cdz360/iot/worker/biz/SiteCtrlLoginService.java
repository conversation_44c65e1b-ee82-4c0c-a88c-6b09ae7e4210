package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.SiteCtrlRoDs;
import com.cdz360.iot.ds.rw.SiteCtrlRwDs;
import com.cdz360.iot.model.site.po.SiteCtrlPo;
import com.cdz360.iot.biz.utils.GwRestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Classname SiteCtrlLoginService
 * @Description
 * @Date 4/24/2020 2:18 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class SiteCtrlLoginService {


//    @Autowired
//    private GwInfoService gwInfoService;

    @Autowired
    private SiteCtrlRoDs siteCtrlRoDs;
    @Autowired
    private SiteCtrlRwDs siteCtrlRwDs;

    @Transactional
    public void doLogin(String ctrlNum, int ver, String realm, String token, String ip) throws NoSuchAlgorithmException {
        log.info("doLogin. ctrlNum: {}, realm: {}, token: {}, ip: {}", ctrlNum, realm, token, ip);

        Assert.isTrue(!StringUtils.isEmpty(ctrlNum), "参数错误,网关编号不能为空");
        Assert.isTrue(!StringUtils.isEmpty(realm), "参数错误,realm不能为空");
        Assert.isTrue(!StringUtils.isEmpty(token), "参数错误,token不能为空");
        SiteCtrlPo ctrlInfo = this.siteCtrlRoDs.selectByNum(ctrlNum);
        Assert.notNull(ctrlInfo, "网关设备 " + ctrlNum + " 不存在,请联系系统管理员添加");

        StringBuilder buf = new StringBuilder();
        buf.append(ctrlNum).append(":").append(ctrlInfo.getPasscode()).append(":").append(realm);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(buf.toString().getBytes(StandardCharsets.UTF_8));
        String hashedToken = GwRestUtils.bytesToHex(md.digest());
        log.info("inToken = {}, hashedToken = {}", token, hashedToken);
        IotAssert.isTrue(hashedToken.equalsIgnoreCase(token), new DcTokenException(ctrlNum, "网关编号或密码错误"));

        boolean changed = false;
        if (StringUtils.isNotBlank(ip) && !ip.equalsIgnoreCase(ctrlInfo.getLanIp())) {
            ctrlInfo.setLanIp(ip);
            changed = true;

        }
        if (ver > 0 && ver != ctrlInfo.getProtocolVer()) {
            ctrlInfo.setProtocolVer(ver);
            changed = true;
        }
        if (changed) {
            this.siteCtrlRwDs.insertOrUpdate(ctrlInfo);
        }

    }



}