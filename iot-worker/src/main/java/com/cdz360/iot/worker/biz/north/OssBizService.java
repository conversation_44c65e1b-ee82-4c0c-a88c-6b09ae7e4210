package com.cdz360.iot.worker.biz.north;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.common.utils.FeignResponseValidate;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.pv.dto.OssStsDto;
import com.cdz360.iot.worker.feign.BizDataCoreFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OssBizService {

    @Autowired
    private BizDataCoreFeignClient dataCoreFeignClient;

    // 获取文件上传的STS信息
    public Mono<CommonResponse<OssStsDto>> getOssSts() {
        return dataCoreFeignClient.getArchiveSts()
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .map(CommonResponse::new);
    }

}
