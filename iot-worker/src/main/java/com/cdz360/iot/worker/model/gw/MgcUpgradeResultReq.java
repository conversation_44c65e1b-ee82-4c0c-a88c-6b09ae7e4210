package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MgcUpgradeResultReq extends BaseObject {
    @Schema(description = "升级状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UpgradeStatus upgradeStatus;

    @Schema(description = "附加信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String msg;
}
