package com.cdz360.iot.worker.model.gw;

import com.cdz360.iot.model.base.BaseObject;
import com.cdz360.iot.model.base.IotPackageType;
import com.cdz360.iot.model.type.StopMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 桩端发起充电响应
 *
 * <AUTHOR>
 * @date Create on 2019/04/19
 */
public class ChgEvseRes extends BaseObject {
    @Schema(description = "消息方法, 请求/响应")
    private IotPackageType type = IotPackageType.RES;

    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "余额")
    private Integer balance;

    @Schema(description = "可实时扣费金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer amount;

    @Schema(description = "停止方式")
    private StopMode stopMode;

    public String getOrderNo() {
        return orderNo;
    }

    public ChgEvseRes setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public StopMode getStopMode() {
        return stopMode;
    }

    public ChgEvseRes setStopMode(StopMode stopMode) {
        this.stopMode = stopMode;
        return this;
    }

    public Integer getBalance() {
        return balance;
    }

    public ChgEvseRes setBalance(Integer balance) {
        this.balance = balance;
        return this;
    }

    public Integer getAmount() {
        return amount;
    }

    public ChgEvseRes setAmount(Integer amount) {
        this.amount = amount;
        return this;
    }
}
