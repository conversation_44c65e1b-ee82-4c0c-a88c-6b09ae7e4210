package com.cdz360.iot.worker.biz.evseCfgCheck;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.worker.model.iot.DelayedEvse;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgCheckDto;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.DelayQueue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class DelayedQueueConsumer implements Runnable {

    private DelayQueue<DelayedEvse> delayQueue;

    private EvseCfgCheckHandle handle;

    private final Object lock = Integer.parseInt("1");

    public DelayedQueueConsumer(DelayQueue<DelayedEvse> delayQueue, EvseCfgCheckHandle handle) {
        this.delayQueue = delayQueue;
        this.handle = handle;
    }

    @Override
    public void run() {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        do {
            try {
                synchronized (lock) {
                    boolean ret = process(traceId);
                    if (ret) {
                        log.info("[{}] lock.wait", traceId);
                        lock.wait(30 * 1000);   // 最长等待30秒
                    }
                }
            } catch (Exception e) {
                log.error("[{}] 比对桩配置时出错 error: {}", traceId, e.getMessage(), e);
            }
        } while (CollectionUtils.isNotEmpty(delayQueue));
    }

    private boolean process(String traceId) {
        if (CollectionUtils.isEmpty(delayQueue)) {
            log.info("[{}] 队列为空", traceId);
            return false;
        }
        try {
            DelayedEvse de = delayQueue.take();
            Optional<EvseCfgCheckDto> optional = handle.preCheck(traceId, de.getEvseNo());
            if (optional.isPresent()) {
                handle.check(traceId, de.getEvseNo(), optional.get())
                    .doOnError(
                        e -> log.error("[{}] evseNo = {}, error = {}", traceId, de.getEvseNo(),
                            e.getMessage(), e))
                    .doFinally(e -> {
                        log.info("[{}] notify", traceId);
                        lock.notify();
                    })
                    .subscribe();
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("[{}] 比对桩配置时出错 error: {}", traceId, e.getMessage(), e);
            lock.notify();
        }
        return false;

    }

}
