package com.cdz360.iot.worker.model.order;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname OrderLimitSocRequest
 * @Description
 * @Date 6/2/2021 1:23 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class OrderLimitSocRequest {
    private String orderNo;
    private Integer limitSoc;
    private String plugNo;
    private String evseNo;
    private Integer plugId;
}