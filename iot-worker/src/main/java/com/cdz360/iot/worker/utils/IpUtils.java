package com.cdz360.iot.worker.utils;

import com.cdz360.base.utils.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;

@Slf4j
public class IpUtils {
//    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);


    private static final List<String> IP_HEADER_LIST = Arrays.asList("X-Forwarded-For",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR");

    private IpUtils() {

    }

    /**
     * 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址;
     *
     * @param request
     * @return
     */
    public static String getIpAddress(ServerHttpRequest request) {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址

        String ip = "";
        Iterator<String> iterHeader = IpUtils.IP_HEADER_LIST.iterator();
        while (iterHeader.hasNext()) {
            String headerName = iterHeader.next();

            ip = request.getHeaders().getFirst(headerName);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                log.debug("{} = {}", headerName, ip);
                break;
            }
        }
        if (StringUtils.isBlank(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
            log.debug("remote addr = {}", ip);
        } else {
            String[] toks = ip.split(",");
            for (int index = 0; index < toks.length && toks.length > 1; index++) {
                String strIp = toks[index];
                if (!"unknown".equalsIgnoreCase(strIp)) {
                    ip = strIp;
                    break;
                }
            }
        }
        if (StringUtils.isBlank(ip)) {
            log.error("无法获取请求方的IP");
        }
        return ip;
    }

    public static String getIpAddress4Servlet(HttpServletRequest request) {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址

        String ip = "";
        Iterator<String> iterHeader = IpUtils.IP_HEADER_LIST.iterator();
        while (iterHeader.hasNext()) {
            String header = iterHeader.next();
            ip = request.getHeader(header);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                log.debug("{} = {}", header, ip);
                break;
            }
        }
        if (StringUtils.isBlank(ip)) {
            ip = request.getRemoteAddr();
            log.debug("remote addr = {}", ip);
        } else {
            String[] toks = StringUtils.split(ip, ",");
            for (int index = 0; index < toks.length && toks.length > 1; index++) {
                String strIp = toks[index];
                if (!"unknown".equalsIgnoreCase(strIp)) {
                    ip = strIp;
                    break;
                }
            }
        }
        if (StringUtils.isBlank(ip)) {
            log.error("无法获取请求方的IP");
        }
        return ip;
    }
}
