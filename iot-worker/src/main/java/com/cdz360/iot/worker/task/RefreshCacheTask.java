package com.cdz360.iot.worker.task;

import com.cdz360.iot.worker.biz.GwCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Component
public class RefreshCacheTask {


    @Autowired
    private GwCacheService gwCacheService;

    @Scheduled(initialDelay = 15 * 1000, fixedRate = 60 * 1000 * 1)
    public void refreshGwInfo() {

        try {
            Calendar calUpdateTime = Calendar.getInstance();
            calUpdateTime.add(Calendar.MINUTE, -2);
            Date updateTime = calUpdateTime.getTime();

            gwCacheService.refreshAll(updateTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
