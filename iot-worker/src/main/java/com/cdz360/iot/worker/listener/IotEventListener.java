package com.cdz360.iot.worker.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.IotGwCmdTimeoutEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.worker.biz.GwCmdTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IotEventListener {

    @Autowired
    private GwCmdTimeoutHandler gwCmdTimeoutHandler;

    /**
     * 网关下行指令响应超时的通知
     * @param msg 超时的指令
     */
    @RabbitHandler
    @RabbitListener(queues = IotConstants.IOT_GW_CMD_TIMEOUT_QUEUE_NAME)
    public void iotGwCmdTimeoutListener(String msg) {
        log.info(">> msg = {}", msg);
        try {
            IotGwCmdTimeoutEvent event = JsonUtils.fromJson(msg, IotGwCmdTimeoutEvent.class);
            this.gwCmdTimeoutHandler.handleTimeout(event.getData());
        } catch (Exception e) {
            log.warn("message:{}", e.getMessage());
            log.warn("收到未识别的消息. msg = {}", msg);
        }

        log.info("<<");
    }


}
