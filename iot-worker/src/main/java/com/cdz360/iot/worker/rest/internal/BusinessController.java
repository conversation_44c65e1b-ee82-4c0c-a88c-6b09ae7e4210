package com.cdz360.iot.worker.rest.internal;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.iot.dto.EvseFullDto;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.PlugRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.ListEvseParam;
import com.cdz360.iot.model.evse.ListPlugParam;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseParam;
import com.cdz360.iot.model.evse.param.ModifyEvseCfgParam;
import com.cdz360.iot.model.evse.param.ModifyEvseInfoParam;
import com.cdz360.iot.model.evse.param.ModifyEvseOperationParam;
import com.cdz360.iot.model.evse.po.EvseCfgResultPo;
import com.cdz360.iot.model.evse.vo.EvseModelVo;
import com.cdz360.iot.model.evse.vo.OfflineEvseVo;
import com.cdz360.iot.model.evse.vo.PriceSchemeSiteVo;
import com.cdz360.iot.model.order.type.EvseDebugMethod;
import com.cdz360.iot.model.param.AddEvseParam;
import com.cdz360.iot.model.param.EditEvseParam;
import com.cdz360.iot.model.param.OfflineEvseParam;
import com.cdz360.iot.model.param.TransformerUpdateParam;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.biz.EvseCfgBizService;
import com.cdz360.iot.worker.biz.EvseProcessor;
import com.cdz360.iot.worker.biz.EvseRefreshService;
import com.cdz360.iot.worker.biz.PlugBizService;
import com.cdz360.iot.worker.biz.TransformerService;
import com.cdz360.iot.worker.biz.north.OrderNorthBizService;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.model.iot.EvseExt;
import com.cdz360.iot.worker.model.iot.UpgradeRequest;
import com.cdz360.iot.worker.model.iot.param.BindEvseParam;
import com.cdz360.iot.worker.model.iot.param.UnbindEvseParam;
import com.cdz360.iot.worker.model.order.CreateOrderRequest;
import com.cdz360.iot.worker.model.order.OrderLimitSocRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "物联网服务相关接口", description = "北向-物联网服务")
@RequestMapping("/iot/biz")
public class BusinessController {

    @Autowired
    private EvseProcessor evseProcessor;
    @Autowired
    private OrderNorthBizService orderBizService;
    @Autowired
    private BusinessService businessService;

    @Autowired
    private EvseRefreshService evseRefreshService;
    @Autowired
    private EvseService evseService;
    @Autowired
    private PlugBizService plugBizService;
    @Autowired
    private PlugService plugService;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private PlugRoDs plugRoDs;
    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private EvseCfgBizService evseCfgBizService;

    @Autowired
    private TransformerService transformerService;

//    @GetMapping(value = "/evse/ddd")
//    private BaseRpcResponse ddd() {
//        evseProcessor.debug();
//        return null;
//    }

    @PostMapping(value = "/evse/upgrade")
    public BaseRpcResponse upgradeEvseFirmware(@RequestBody UpgradeRequest request) {
        log.info("升级桩固件。request: {}", request);
        BaseRpcResponse res = businessService.upgradeEvse(request);
        log.info("<<");
        return res;
    }

    @Operation(summary = "创建/开始充电订单")
    @PostMapping(value = "/order/create")
    public BaseResponse createOrder(@RequestBody CreateOrderRequest req) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("创建/开始充电订单。req = {}", req);
        IotAssert.isNotNull(req.getBalance(), "账户总可用余额不能为空，请确认后再尝试。");
        req.setTotalAmount(req.getBalance());
        IotAssert.isNotNull(req.getFrozenAmount(), "账户无冻结金额，请确认后再尝试。");
        req.setFrozenAmount(req.getFrozenAmount());//可实时扣费金额 就是冻结金额

        var gwno = this.orderBizService.createOrder(req);
        log.info("<< gwno = {}", gwno);
        LogHelper.logLatency(log, BusinessController.class.getSimpleName(),
            "createOrder", "创建充电订单", startTime);
        return BaseResponse.success(); // 一样的订单号只会存在同一台桩，再次发起一样的订单号，认为已经成功开启
    }

    @Operation(summary = "终止充电")
    @PostMapping(value = "/order/stop")
    public BaseResponse stopOrder(@Parameter(name = "桩编号") @RequestParam String evseNo,
        @Parameter(name = "枪头编号") @RequestParam String plugNo,
        @Parameter(name = "订单号") @RequestParam String orderNo) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("终止充电。evseNo = {}, plugNo = {}, orderNo = {}", evseNo, plugNo, orderNo);
        String gwno = this.orderBizService.stopOrder(evseNo, plugNo, orderNo);
        log.info("<< gwno = {}", gwno);
        LogHelper.logLatency(log, BusinessController.class.getSimpleName(),
            "stopOrder", "停止充电指令", startTime);
        return BaseResponse.success();
    }

    @Operation(summary = "桩新增(用于桩管家)")
    @PostMapping(value = "/evse/addEvse")
    public BaseResponse addEvse(@RequestBody AddEvseParam param) {
        log.info("桩新增。param = {}", param);
        evseProcessor.addEvse(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "桩编辑(用于桩管家)")
    @PostMapping(value = "/evse/editEvse")
    public BaseResponse editEvse(@RequestBody EditEvseParam param) {
        log.info("桩编辑。param = {}", param);
        return evseProcessor.editEvse(param);
    }

    @Operation(summary = "桩绑定到场站")
    @PostMapping(value = "/evse/bindEvse2Site")
    public BaseResponse bindEvse2Site(@RequestBody BindEvseParam param) {
        log.info("桩绑定到场站。param = {}", param);
        evseProcessor.bindEvse2Site(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "海外版，桩绑定到场站")
    @PostMapping(value = "/evse/commercial/bindEvse2Site")
    public BaseResponse bindEssEvse2Site(@RequestBody BindEvseParam param) {
        log.info("海外版，桩绑定到场站。param = {}", param);
        evseProcessor.bindEssEvse2Site(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "桩和场站解绑")
    @PostMapping(value = "/evse/unbindEvse2Site")
    public BaseResponse unbindEvse2Site(@RequestBody UnbindEvseParam param) {
        log.info("桩和场站解绑。param = {}", param);
        evseProcessor.unbindEvse2Site(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "更新桩信息")
    @PostMapping(value = "/evse/updateEvseInfo")
    public BaseResponse updateEvseInfo(@RequestBody ModifyEvseInfoParam param) {
        log.info("更新桩信息。param = {}", param);
        evseProcessor.updateEvseInfo(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "海外版，更新桩信息")
    @PostMapping(value = "/evse/commercial/updateEvseInfo")
    public BaseResponse updateEssEvseInfo(@RequestBody ModifyEvseInfoParam param) {
        log.info("海外版，更新桩信息。param = {}", param);
        evseProcessor.updateEssEvseInfo(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "更新桩信息列表")
    @PostMapping(value = "/evse/updateEvseInfoList")
    public BaseResponse updateEvseInfoList(@RequestBody List<ModifyEvseInfoParam> param) {
        log.info("更新桩信息列表。param = {}", JsonUtils.toJsonString(param));
        evseProcessor.updateEvseInfoList(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "批量导入桩")
    @PostMapping(value = "/evse/batchImport")
    public BaseResponse batchImport(@RequestBody List<EvseModelVo> param) {
        log.info("batchImport param = {}", param);
        evseProcessor.batchImport(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "批量编辑桩信息")
    @PostMapping(value = "/evse/batchUpdateEvseInfo")
    public BaseResponse batchUpdateEvseInfo(@RequestBody ModifyEvseInfoParam param) {
        log.info("更新桩信息。param = {}", param);
        evseProcessor.batchUpdateEvseInfo(param);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "批量新增脱机桩")
    @PostMapping(value = "/evse/batchAddOfflineEvse")
    public BaseResponse batchAddOfflineEvse(@RequestBody List<OfflineEvseVo> param) {
        log.info("更新脱机桩信息。param = {}", param);
        return evseProcessor.batchAddOfflineEvse(param);
    }

    @Operation(summary = "更新脱机桩信息")
    @PostMapping(value = "/evse/updateOfflineEvse")
    public BaseResponse updateOfflineEvse(@RequestBody ModifyEvseInfoParam param) {
        log.info("更新脱机桩信息。param = {}", param);
        return evseProcessor.updateOfflineEvse(param);
    }

    @Operation(summary = "移除脱机桩")
    @PostMapping(value = "/evse/removeOfflineEvse")
    public BaseResponse removeOfflineEvse(@RequestBody List<OfflineEvseParam> param) {
        log.info("移除脱机桩。param.size = {}", param.size());
        return evseProcessor.removeOfflineEvse(param);
    }

    @Operation(summary = "修改枪头信息")
    @PostMapping(value = "/plug/updatePlugInfo")
    public BaseResponse updatePlugInfo(@Parameter(name = "桩号") @RequestParam String evseNo,
        @Parameter(name = "枪头序号") @RequestParam Integer plugIdx,
        @Parameter(name = "枪头名称") @RequestParam String plugName) {
        log.info("修改枪头信息。evseNo = {}, plugIdx = {}, plugName = {}", evseNo, plugIdx, plugName);
        this.plugBizService.modifyPlugName(evseNo, plugIdx, plugName);
        log.info("<<");
        return BaseResponse.success();
    }

    @Operation(summary = "移除桩")
    @PostMapping(value = "/evse/remove")
    public BaseResponse removeEvse(
//@Parameter(name = "场站ID(东正)") @RequestParam BigInteger dzSiteId,
        @Parameter(name = "桩号") @RequestParam String evseId) {
        log.info("移除桩。evseId = {}", evseId);
        evseProcessor.removeByEvseId(evseId);
        log.info("<<");
        return BaseResponse.success();
    }

    @Deprecated
    @Operation(summary = "修改桩配置")
    @PostMapping(value = "/evse/modifyEvseCfg")
    public BaseRpcResponse modifyEvseCfg(@RequestBody CfgEvseParam cfgEvseParam) {
        log.info("修改桩配置。modifyEvseCfg = {}", cfgEvseParam);
        BaseRpcResponse res = businessService.modifyEvseCfg(cfgEvseParam);
        log.info("<<");
        return res;
    }

    @Operation(summary = "修改/下发桩配置")
    @PostMapping(value = "/evse/modifyEvseCfgV2", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResponse modifyEvseCfg(ServerHttpRequest request,
        @RequestBody ModifyEvseCfgParam param) {
        log.info(">> param = {}", param);
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()), "参数错误, 桩编号不能为空");

        if (CollectionUtils.isNotEmpty(param.getWhiteCards())) { // 紧急卡下发
            this.evseCfgBizService.modifyEvseWhiteCard(param);
        } else if (CollectionUtils.isNotEmpty(param.getSiteAuthVinList())) { // VIN本地鉴权
            this.evseCfgBizService.modifyEvseLocalVin(param);
        } else if (CollectionUtils.isNotEmpty(param.getSiteAuthCardList())) { // CARD本地鉴权
            this.evseCfgBizService.modifyEvseLocalCard(param);
        } else { // 桩配置下发
            this.evseCfgBizService.modifyEvseCfg(param);
        }

        return RestUtils.success();
    }

    @Operation(summary = "OCPP云端操作")
    @PostMapping(value = "/evse/modifyEvseOperation", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse modifyEvseOperation(ServerHttpRequest request,
        @RequestBody ModifyEvseOperationParam param) {
        log.info(">> modifyEvseOperation. param = {}", JsonUtils.toJsonString(param));
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()),
            "参数错误, 桩编号不能为空");
        Assert.isTrue(param.getDto() != null, "参数错误, 操作参数不能为空");
        Assert.isTrue(param.getDto().validate(), "参数错误, 操作参数无效");
        if (param.getDto().getGetDiagnosticsVo() != null) {
            Assert.isTrue(
                StringUtils.isNotBlank(param.getDto().getGetDiagnosticsVo().getLocation()),
                "参数错误, 诊断日志位置不能为空");
        }

        return this.evseCfgBizService.modifyEvseOperation(param);
    }

    @Operation(summary = "修改桩计费模板-用于不支持计费下发的桩")
    @PostMapping(value = "/evse/modifyEvsePrice")
    public BaseResponse modifyEvsePrice(ServerHttpRequest request,
        @RequestBody ModifyEvseCfgParam param) {
        log.info(">>modifyEvsePrice param = {}", param);
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()), "参数错误, 桩编号不能为空");
        IotAssert.isNotNull(param.getPriceSchemeId(), "参数错误, 计费模板不能为空");

        this.evseCfgBizService.modifyEvsePrice(param);

        return RestUtils.success();
    }

    @Operation(summary = "修改桩配置job")
    @PostMapping(value = "/evse/modifyEvseCfgJob")
    public BaseRpcResponse modifyEvseCfgJob() {
        log.info("修改桩配置job");
        businessService.modifyEvseCfgJob();
        BaseRpcResponse res = new BaseRpcResponse();
        log.info("<<");
        return res;
    }

    @Deprecated
    @PostMapping(value = "/evse/modifyEvseCfgX")    // TODO: 临时方案, 后续移除
    public BaseRpcResponse modifyEvseCfgX(@RequestParam(value = "cfgParam") String jsonBody,
        @RequestBody(required = false) CfgEvseParam cfgEvseParamX) {
        log.info("修改桩配置param。modifyEvseCfg = {}", jsonBody);
        log.info("修改桩配置body。modifyEvseCfg = {}", JsonUtils.toJsonString(cfgEvseParamX));
        CfgEvseParam cfgEvseParam;
        //优先使用body
        if (cfgEvseParamX != null) {
            cfgEvseParam = cfgEvseParamX;
        } else {
            cfgEvseParam = JsonUtils.fromJson(jsonBody, CfgEvseParam.class);
        }
        BaseRpcResponse res = businessService.modifyEvseCfg(cfgEvseParam);
        log.info("<<");
        return res;
    }

    @Operation(summary = "开启网关的ssh反向隧道")
    @PostMapping(value = "/gw/tunnel/start")
    public BaseRpcResponse startGwTunnel(
        @Parameter(name = "网关编号.") @RequestParam(required = false) String gwno,
        @Parameter(name = "ssh跳板机地址") @RequestParam String serverAddr,
        @Parameter(name = "ssh跳板机端口") @RequestParam int serverPort,
        @Parameter(name = "本地端口号", required = false, example = "22") @RequestParam(required = false, defaultValue = "22") int localPort,
        @Parameter(name = "有效期, 单位分钟", required = false, example = "60") @RequestParam(required = false, defaultValue = "60") int expire) {
        //网关编号需存在并状态正常
        log.info("开启网关的ssh反向隧道。gwno = {}, serverAddr = {}, serverPort = {}, expire = {}",
            gwno, serverAddr, serverPort, expire);
        BaseRpcResponse res = this.businessService.startGwTunnel(gwno, serverAddr, serverPort,
            localPort, expire);
        log.info("<<");
        return res;
    }

    @Operation(summary = "关闭网关的ssh反向隧道")
    @PostMapping(value = "/gw/tunnel/stop")
    public BaseRpcResponse stopGwTunnel(
        @Parameter(name = "网关编号.") @RequestParam(required = false) String gwno) {
        log.info("关闭网关的ssh反向隧道。gwno = {}", gwno);
        BaseRpcResponse res = this.businessService.stopGwTunnel(gwno);
        log.info("<<");
        return res;
    }

    @Operation(summary = "云端向桩查询桩配置")
    @PostMapping(value = "/cfg/evse/getCfg")
    public BaseRpcResponse cfgEvseGetCfg(
        @Parameter(name = "桩在云平台的唯一ID") @RequestParam(value = "evseIds") List<String> evseIds) {
        log.info("云端向桩查询桩配置 evseNos: {}", evseIds);
        businessService.evseGetCfg(evseIds);
        var res = new BaseRpcResponse();
        return res;
    }

    @Operation(summary = "云端从redis获取桩配置")
    @PostMapping(value = "/cfg/evse/getCfgInfo")
    public ObjectResponse<CfgEvseAllV2> cfgEvseGetCfgInfo(
        @Parameter(name = "桩在云平台的唯一ID") @RequestParam(value = "evseId") String evseId) {
        log.info("云端从redis获取桩配置 evseNo = {}", evseId);
        CfgEvseAllV2 cfgEvseAllV2 = businessService.evseGetCfgInfo(evseId);
        IotAssert.isNotNull(cfgEvseAllV2, "桩配置获取失败");
        return RestUtils.buildObjectResponse(cfgEvseAllV2);
    }

    /**
     * 发下行指令给网关, 要求网关上报所有桩/枪的状态
     *
     * @return
     */
    @PostMapping(value = "/collectEvseStatus")
    public BaseResponse collectEvseStatus() {
        log.info("收集桩状态");
        this.businessService.collectEvseStatus();
        return BaseResponse.success();
    }

    @PostMapping(value = "/refreshEvseStatus")
    public BaseResponse refreshEvseStatus() {
        log.warn("同步缓存的桩状态到数据库");
        this.evseRefreshService.refreshEvseStatus();
        return BaseResponse.success();
    }

    @PostMapping(value = "/refreshPlugStatus")
    public BaseResponse refreshPlugStatus() {

        log.warn("同步缓存的枪头状态到数据库");
        this.evseRefreshService.refreshPlugStatus();
        return BaseResponse.success();
    }

    /**
     * 查询桩信息通用接口(可拓展)
     *
     * @param param
     * @return
     */
    @PostMapping("/getEvseList")
    public ListResponse<EvseFullDto> getEvseList(@RequestBody ListEvseParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        return evseService.getEvseList(param);
    }

    /**
     * 查询桩信息通用接口(后面可能随时增加字段)
     *
     * @param param
     * @return
     */
    @PostMapping("/getEvseExtList")
    public ListResponse<EvseExt> getEvseExtList(@RequestBody ListEvseParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        return evseService.getEvseExtList(param);
    }

    /**
     * 分页查询在线且未激活的桩列表
     *
     * @param param
     * @return
     */
    @PostMapping("/getOnlineAndNotBoundEvseList")
    public ListResponse<EvseVo> getOnlineAndNotBoundEvseList(@RequestBody BaseListParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        return evseService.getOnlineAndNotBoundEvseList(param);
    }

    /**
     * 查询枪信息通用接口(可拓展)
     *
     * @param param
     * @return
     */
    @PostMapping("/getPlugList")
    public ListResponse<PlugVo> getPlugList(@RequestBody ListPlugParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        List<String> plugNos = plugRoDs.getPlugNos(param);
        if (CollectionUtils.isNotEmpty(plugNos)) {
            List<PlugVo> plugList = redisIotReadService.getPlugList(plugNos);
            return RestUtils.buildListResponse(plugList);
        }
        return RestUtils.buildListResponse(null);
    }
//
//    /**
//     * 查询场站信息通用接口(可拓展)
//     *
//     * @param siteId
//     * @return
//     */
//    @Deprecated
//    @GetMapping("/getSite")
//    public ObjectResponse<Site> getSite(@RequestParam("siteId") String siteId) {
//        log.error("deprecated!!! getSite");
//        return businessService.getSite(siteId);
//    }

    /**
     * @Description: 桩升级场站下桩列表
     * @Author: JLei
     * @CreateDate: 18:50 2019/9/23
     */
    @PostMapping("/evse/upgrade/listBySiteId")
    public ListResponse<EvsePo> listBySiteIdForUpgrade(@RequestBody ListEvseParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        Assert.notNull(param, "参数不能为空");
        Assert.notNull(param.getSiteId(), "场站ID不能为空");
        List<EvsePo> evsePos = evseService.listBySiteIdForUpgrade(param);
        return new ListResponse<>(evsePos, (long) evsePos.size());
    }


    @PostMapping(value = "/evse/reboot")
    @Operation(summary = "桩远程重启")
    public BaseResponse rebootEvse(@RequestParam String evseNo) {
        log.info(">> 重启桩。evseNo = {}", evseNo);
        businessService.rebootEvse(evseNo);
        log.info("<<");
        return RestUtils.success();
    }

    @PostMapping(value = "/evse/debug")
    @Operation(summary = "发送桩远程DEBUG指令")
    public BaseResponse evseDebug(@RequestParam String evseNo,
        @RequestParam EvseDebugMethod debugMethod,
        @RequestParam(required = false) String msg) {
        log.info(">> 发送桩远程DEBUG指令。evseNo = {}, debugMethod = {}, msg = {}",
            evseNo, debugMethod, msg);
        businessService.evseDebug(evseNo, debugMethod, msg);
        // 下发到Mqtt修改DebugTag标志位
        this.evseService.updateDebugTag(evseNo, debugMethod);
        log.info("<<");
        return RestUtils.success();
    }

    @PostMapping(value = "/evse/moduleQuery")
    @Operation(summary = "云端查询桩器件信息指令")
    public BaseResponse moduleQuery(@RequestParam(value = "evseNo") String evseNo) {
        log.info(">> 云端查询桩器件信息指令。evseNo = {}", evseNo);
        businessService.moduleQuery(evseNo);
        log.info("<<");
        return RestUtils.success();
    }

    @PostMapping(value = "/evseCfg/siteInfo")
    @Operation(summary = "获取计费模板下发场站信息")
    public ListResponse<PriceSchemeSiteVo> getPriceSchemeSiteInfo(
        @RequestBody List<Long> idList) {
        log.info(">> 获取计费模板下发场站信息: priceSchemeId = {}", idList);
        List<PriceSchemeSiteVo> siteVoList = this.evseCfgBizService.getPriceSchemeSiteInfo(idList);
        log.info("<< size = {}", siteVoList.size());
        return RestUtils.buildListResponse(siteVoList);
    }

    @GetMapping(value = "/evseCfg/getEvseCfg")
    @Operation(summary = "获取桩配置结果")
    public ObjectResponse<EvseCfgResultPo> getEvseCfgResult(
        @RequestParam String evseNo) {
        log.info(">> 获取桩配置结果: evseNo = {}", evseNo);
        return RestUtils.buildObjectResponse(this.evseCfgBizService.getEvseCfgResult(evseNo));
    }

    @Operation(summary = "调整SOC限制")
    @PostMapping(value = "/order/changeLimitSoc")
    public ObjectResponse<String> changeLimitSoc(@RequestBody OrderLimitSocRequest req) {
        log.info("调整订单SOC限制。req = {}", req);

        String seq = this.orderBizService.changeLimitSoc(req);
        log.info("<< seq = {}", seq);
        return new ObjectResponse<>(seq);
    }

    @Operation(summary = "发送变压器更新信息")
    @PostMapping(value = "/transformer/sendUpdateMsg")
    public BaseResponse sendUpdateMsg(@RequestBody TransformerUpdateParam req) {
        log.info("发送变压器更新信息。req = {}", req);

        transformerService.sendUpdateMsg(req);

        log.info("<<");
        return BaseResponse.success();
    }

}
