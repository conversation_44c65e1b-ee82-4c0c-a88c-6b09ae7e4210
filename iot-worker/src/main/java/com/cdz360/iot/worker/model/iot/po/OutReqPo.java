package com.cdz360.iot.worker.model.iot.po;

import com.cdz360.iot.model.base.DbObject;
import com.cdz360.iot.model.type.GwRequestMethod;
import com.cdz360.iot.worker.type.OutReqStatus;

public class OutReqPo extends DbObject {

    private GwRequestMethod method;
    private String seq;
    private String evseId;
    private int plugId = -1;
    private OutReqStatus status = OutReqStatus.INIT;
    private String context;
    private Integer balance;


    public GwRequestMethod getMethod() {
        return method;
    }

    public OutReqPo setMethod(GwRequestMethod method) {
        this.method = method;
        return this;
    }

    public String getSeq() {
        return seq;
    }

    public OutReqPo setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public OutReqPo setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public int getPlugId() {
        return plugId;
    }

    public OutReqPo setPlugId(int plugId) {
        this.plugId = plugId;
        return this;
    }

    public OutReqStatus getStatus() {
        return status;
    }

    public OutReqPo setStatus(OutReqStatus status) {
        this.status = status;
        return this;
    }

    public String getContext() {
        return context;
    }

    public OutReqPo setContext(String context) {
        this.context = context;
        return this;
    }

    public Integer getBalance() {
        return balance;
    }

    public OutReqPo setBalance(Integer balance) {
        this.balance = balance;
        return this;
    }
}
