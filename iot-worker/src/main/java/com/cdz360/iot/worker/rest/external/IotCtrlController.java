package com.cdz360.iot.worker.rest.external;

import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.site.ctrl.*;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.worker.biz.SiteCtrlBizService;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.worker.utils.IpUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import java.security.NoSuchAlgorithmException;

/**
 * @Classname IotCtrlController
 * @Description
 * @Date 4/20/2020 7:38 PM
 * @Created by Rafael
 */

@RestController
@Tag(name = "场站控制器服务器端API", description = "南向-控制盒接口")
@RequestMapping("/iot/ctrl")
@Slf4j
public class IotCtrlController {

    @Autowired
    private SiteCtrlBizService siteCtrlBizService;

    @Autowired
    private IotCacheService iotCacheService;

    @Operation(summary = "初始化")
    @PostMapping(value = "/register")
    public SiteCtrlRes<SiteCtrlRegisterResult> register(
            @RequestBody SiteCtrlReq<SiteCtrlRegisterReq> req
    ) throws NoSuchAlgorithmException {
        log.info("控制器初始化: {}", JsonUtils.toJsonString(req));
//        req.getData().setCtrlNum(req.getCtrlNo());
        return new SiteCtrlRes<>(req.getSeq(), siteCtrlBizService.doRegister(req));
    }

    @Deprecated
    @Operation(summary = "登陆")
    @PostMapping(value = "/login")
    public SiteCtrlRes<SiteCtrlRegisterResult> login(
            ServerHttpRequest request,
            @RequestBody SiteCtrlReq req,
            @RequestHeader(value = "Authorization", required = false) String authHd)
            throws NoSuchAlgorithmException {
        log.info("控制器登陆: {}", JsonUtils.toJsonString(req));

        String ip = IpUtils.getIpAddress(request);

        String token = GwRestUtils.getToken(authHd);
        if (StringUtils.isBlank(token)) {
            // tian
            log.info("<< no authHd");
            throw new DcTokenException(req.getCtrlNo(), "没有 token");
        }

        SiteCtrlRegisterResult loginRes = siteCtrlBizService.login(req.getCtrlNo(), req.getVer(), token, ip);

        return new SiteCtrlRes<>(req.getSeq(), loginRes);
    }

    @Operation(summary = "控制器获取配置信息")
    @PostMapping(value = "/cfg")
    public SiteCtrlRes<SiteCtrlCfgRes> getCfg(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestBody SiteCtrlReq req) {
        log.info("控制获取配置: req = {}", JsonUtils.toJsonString(req));
        IotAssert.isNotBlank(req.getCtrlNo(), "请提供控制器编号(ctrlNo)");

        checkToken(authHd, req.getCtrlNo());

        SiteCtrlCfgRes res = siteCtrlBizService.getCfg(req.getCtrlNo());
        return SiteCtrlRes.success(req.getSeq(), res);
    }

    @Operation(summary = "上报结果")
    @PostMapping(value = "/cfg/result")
    public SiteCtrlRes cfgResult(@RequestHeader(value = "Authorization", required = false) String authHd,
                                 @RequestBody SiteCtrlReq<SiteCtrlCfgResultReq> req) {
        log.info("控制上报配置结果: req = {}", JsonUtils.toJsonString(req));
        IotAssert.isNotBlank(req.getCtrlNo(), "请提供控制器编号(ctrlNo)");

        checkToken(authHd, req.getCtrlNo());

        siteCtrlBizService.updateCfgResult(req);

        return SiteCtrlRes.success(req.getSeq());
    }

    @Operation(summary = "上报状态")
    @PostMapping(value = "/status")
    public SiteCtrlRes status(@RequestHeader(value = "Authorization", required = false) String authHd,
                              @RequestBody SiteCtrlReq<SiteCtrlStatus> req) {
        log.info(">> 控制器状态上报: req = {}", JsonUtils.toJsonString(req));
//        checkToken(authHd, req.getCtrlNo());
        siteCtrlBizService.doStatus(req);
        return SiteCtrlRes.success(req.getSeq());
    }

    @Operation(summary = "上报检测数据")
    @PostMapping(value = "/monitor")
    public SiteCtrlRes monitor(@RequestHeader(value = "Authorization", required = false) String authHd,
                               @RequestBody SiteCtrlReq<SiteCtrlMonitor> req) {
        log.info(">> 控制器监测信息上报: req = {}", JsonUtils.toJsonString(req));
//        checkToken(authHd, req.getCtrlNo());
        siteCtrlBizService.doMonitor(req.getCtrlNo(), req.getData());
        log.info("<< ");
        return SiteCtrlRes.success(req.getSeq());
    }

    @Operation(summary = "上报配置")
    @PostMapping(value = "/cfg/info")
    public SiteCtrlRes cfgInfo(@RequestHeader(value = "Authorization", required = false) String authHd,
                               @RequestBody SiteCtrlReq<SiteCtrlInfo> req) {
        log.info(">> 控制器上报配置: req = {}", JsonUtils.toJsonString(req));
        checkToken(authHd, req.getCtrlNo());
        siteCtrlBizService.cfgInfo(req.getCtrlNo(), req.getData());
        log.info("<< ");
        return SiteCtrlRes.success(req.getSeq());
    }

    private void checkToken(String authHd, String gwno) {
        IotAssert.isTrue(!StringUtils.isBlank(gwno), "参数错误,网关编号不能为空");
        var tokenCache = this.iotCacheService.getToken(gwno);
        var tokenHd = GwRestUtils.getToken(authHd);
        if (StringUtils.isBlank(tokenCache) || StringUtils.isBlank(tokenHd)
                || !tokenCache.equalsIgnoreCase(tokenHd)) {
            log.warn("token 校验失败. gwno = {}, 网关发送 token = {}, 缓存 token = {}", gwno, tokenHd, tokenCache);
            throw new DcTokenException(gwno, "登录已过期");
        }
    }


}