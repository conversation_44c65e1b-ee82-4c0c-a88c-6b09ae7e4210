package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.EvseModelRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.EvseReportRequest;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.evse.po.EvseModelPo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.model.iot.UpdateEvseCacheDto;
import com.cdz360.iot.worker.model.iot.UpdatePlugCacheDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisIotUpdateWrapper {

    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private EvseModelRoDs evseModelRoDs;
    @Autowired
    private EvseService evseService;
    @Autowired
    private PlugService plugService;

    @Autowired
    private DcEventPublisher dcEventPublish;
    @Autowired
    private PlugOrderNoTask plugOrderNoTask;

    /**
     * 桩绑定到场站
     */
    public void bindEvse(String siteId, String evseNo, List<String> plugNoList) {
        redisIotRwService.bindEvse(siteId, evseNo, plugNoList);
    }

    /**
     * 桩和场站解绑
     */
    public void unbindEvse(String siteId, String evseNo, List<String> plugNoList) {
        this.redisIotRwService.unbindEvse(siteId, evseNo, plugNoList);
    }

    /**
     * 枪头绑定订单号
     */
    public void bindOrderNo(String evseNo, int plugIdx, String orderNo) {
        PlugVo plugVo = new PlugVo();
        plugVo.setOrderNo(orderNo).setEvseNo(evseNo).setIdx(plugIdx)
            .setPlugNo(PlugNoUtils.formatPlugNo(evseNo, plugIdx));
        log.info("枪头绑定订单号. plug = {}", plugVo);
        this.redisIotRwService.updatePlugRedisCache(plugVo);
        plugOrderNoTask.push(plugVo);
        log.info("更新redis成功");  // debug 性能问题
    }

    /**
     * 清除枪头上绑定的订单号
     */
    public void unbindOrderNo(String evseNo, int plugIdx) {
        PlugVo plugVo = new PlugVo();
        plugVo.setOrderNo("").setEvseNo(evseNo).setIdx(plugIdx)
            .setPlugNo(PlugNoUtils.formatPlugNo(evseNo, plugIdx));
        log.info("枪头解绑订单号. plug = {}", plugVo);
        this.redisIotRwService.updatePlugRedisCache(plugVo);
        plugOrderNoTask.push(plugVo);
    }

    public void updateRedisEvseCache(UpdateEvseCacheDto dto) {
        IotAssert.isNotNull(dto.getEvse(), "必填参数不能为空");
        IotEvent event = dto.getEvent();
        SitePo site = dto.getSite();
        EvsePo evse = dto.getEvse();
        String linkId = dto.getLinkId();
        EvseRegisterReason registerReason = dto.getRegisterReason();

        EvseVo evseVo = new EvseVo();

        if (evse != null && evse.getProtocol() != null) {
            evseVo.setProtocol(evse.getProtocol());
        } else {
            evseVo.setProtocol(EvseProtocolType.DC);//默认作为鼎充的桩
        }

        evseVo.setEvseNo(evse.getEvseId())
            .setBizStatus(evse.getBizStatus())
            .setSupplyType(evse.getSupply())
            .setSiteId(evse.getSiteId())
            .setPriceCode(evse.getPriceCode())
            .setGwno(evse.getGwno())
            .setStatus(evse.getEvseStatus())
            .setName(evse.getName())
            .setProtocolVer(evse.getProtocolVer())
            .setFirmwareVer(evse.getFirmwareVer())
            .setPlugNum(evse.getPlugNum())
            .setModelName(evse.getModel())
            .setVoltage(evse.getVoltage())
            .setCurrent(evse.getCurrent())
            .setConnSupport(evse.getConnSupport())
            .setRegisterReason(registerReason) //只用于推送，不存入redis
            .setUseSiteCfg(evse.getUseSiteCfg());
        //        if (StringUtils.isBlank(evse.getSiteId())) {
        //            evseVo.setSiteCommId(0L);
        //        } else
        if (evse.getPower() != null && evse.getPower().intValue() > 0) {
            evseVo.setPower(evse.getPower());
        }
        if (site != null && site.getCommId() != null && site.getCommId() > 0L) {
            evseVo.setSiteCommId(site.getCommId());
        }
        if (evse.getProtocolVer() == null) {
            evse.setProtocolVer(304);
        }
        if (!(evse.getProtocolVer() != null
            && evse.getProtocolVer() >= IotConstants.SERVFEE_TIME_DIVISION_PROTOVER
            && event == IotEvent.STATE_CHANGE)) {
            log.info("更新redis桩信息. evse = {}", evseVo);
            this.redisIotRwService.updateEvseRedisCache(evseVo);
        }
        this.dcEventPublish.publishEvseInfo(event, evseVo);    // 推送枪头状态变动的消息
    }

    public void updateRedisEvseCacheV2(GwInfoPo gwInfo, IotEvent event, EvseVo evseVo) {
//        log.info("更新redis桩信息. evse = {}", evseVo);
//        this.redisIotRwService.updateEvseRedisCache(evseVo);
//        this.dcEventPublish.publishEvseInfo(event, evseVo);    // 推送枪头状态变动的消息
        //log.info("<< 更新完成00000");
        if ((gwInfo != null && GwMqType.MQ_TYPE_MQTT == gwInfo.getMqType())
            || evseVo.getProtocolVer() == null
            || evseVo.getProtocolVer() < IotConstants.SERVFEE_TIME_DIVISION_PROTOVER) {
            this.updateRedisEvseCacheV2(event, evseVo, null, null);
        }
    }

    public void updateRedisEvseCacheV2(IotEvent event, EvseVo evseVo, String linkId,
        String ctrlNo) {
        log.info("更新redis桩信息. evse = {}", evseVo);
        this.redisIotRwService.updateEvseRedisCache(evseVo);
//        this.dcEventPublish.publishEvseInfo(event, evseVo, linkId, ctrlNo);    // 推送来自控制器 枪头状态变动的消息
        //log.info("<< 更新完成00000");
    }

    public PlugVo updateRedisPlugCache(UpdatePlugCacheDto dto) {
        IotEvent event = dto.getEvent();
        EvseReportRequest.Plug plugReport = dto.getPlugReport();
        SitePo site = dto.getSite();
        EvsePo evse = dto.getEvse();
        PlugPo plug = dto.getPlug();
        IotAssert.isTrue(event != null && evse != null && plug != null, "必填参数不能为空");

        PlugVo plugVo = new PlugVo();
        plugVo.setEvseNo(plug.getEvseId())
            .setPlugNo(PlugNoUtils.formatPlugNo(plug.getEvseId(), plug.getPlugId()))
            .setProtocol(evse.getProtocol())
            .setGwno(evse.getGwno())
            .setSiteId(evse.getSiteId())
            .setIdx(plug.getPlugId())
            .setName(plug.getName())
            .setEvseName(evse.getName())
            .setStatus(plug.getPlugStatus())
            .setSupply(evse.getSupply())
            .setOrderNo(plug.getOrderNo())
            .setConstantCharge(dto.getConstantCharge())
            .setPriceCode(evse.getPriceCode())
            .setBizStatus(evse.getBizStatus());

        if (evse.getConnSupport() != null) {
            plugVo.setConnSupport(evse.getConnSupport());
        }
        if (plugReport != null) {
            plugVo.setErrorCode(plugReport.getErrorCode())
                .setAlertCode(plugReport.getAlertCode())
                .setErrorMsg(plugReport.getError())
                .setTemperature(plugReport.getTemp());
        }
        if (site != null) {
            plugVo.setSiteName(site.getName())
                .setSiteCommId(site.getCommId())
                .setTopCommId(site.getTopCommId());
        }
        log.info("更新redis枪头信息. plug = {}", plugVo);

        //if (plugReport != null) {
        // 告警短信缺失字段, 需要通过mq传递
//            plugVo.setErrorCode(plugReport.getErrorCode())
//                    .setAlertCode(plugReport.getAlertCode())
//                    .setErrorMsg(plugReport.getError())
//                    .setTemperature(plugReport.getTemp());

        //}
        PlugVo result = this.redisIotRwService.updatePlugRedisCache(plugVo);

        //添加设备型号，软件版本
        PlugMqDto plugMqDto = new PlugMqDto();
        if (result != null) {
            BeanUtils.copyProperties(result, plugMqDto);
        }
        plugMqDto.setFirmwareVer(evse.getFirmwareVer())
            .setModelName(evse.getModelName());

        this.dcEventPublish.publishPlugInfo(event, plugMqDto);    // 推送枪头状态变动的消息
        return result;
    }

    public PlugVo updateRedisPlugCacheStatusV2(@NonNull IotEvent event,
        @Nullable EvseReportRequestV2.Plug plugReport,
        GwInfoPo gwInfo,
        //@Nullable SitePo site,
        @NonNull EvseVo evse,
        @NonNull PlugPo plug,
        @Nullable String linkId,
        @Nullable String ctrlNo) {
        log.info("状态上报枪头信息。 evseNo={}, plugReport = {}",
            evse.getEvseNo(),
            plugReport);
        PlugVo plugVo = redisIotRwService.getPlugRedisCache(evse.getEvseNo(),
            plugReport.getPlugId());
        if (plugVo == null) {
            plugVo = new PlugVo();
            plugVo.setEvseNo(evse.getEvseNo())
                .setBizStatus(evse.getBizStatus())
                .setPlugNo(PlugNoUtils.formatPlugNo(evse.getEvseNo(), plugReport.getPlugId()))
                .setProtocol(evse.getProtocol())
                .setGwno(evse.getGwno())
                .setSiteId(evse.getSiteId())
                .setIdx(plugReport.getPlugId())
                .setName(plug.getName())
                .setSupply(evse.getSupplyType())
                .setOrderNo(plug.getOrderNo())
                .setAlertCode(plug.getAlertCode())
                .setErrorMsg(plug.getErrorMsg())
                .setErrorCode(plug.getErrorCode());
        }
        plugVo.setTemperature(plugReport.getTemp()).setStatus(plugReport.getPlugStatus());
        if (plugReport != null) {
            plugVo.setErrorCode(plugReport.getErrorCode())
                .setAlertCode(plugReport.getAlertCode())
                .setErrorMsg(plugReport.getError())
                .setTemperature(plugReport.getTemp());
        }
        //if (site != null) {
        plugVo.setSiteName(evse.getSiteName())
            .setSiteCommId(evse.getSiteCommId());
        //}

        if (StringUtils.isBlank(plugVo.getGwno())) {
            plugVo.setGwno(evse.getGwno());
        }
        if (StringUtils.isBlank(plugVo.getEvseNo())) {  // 避免redis里取出来的枪头数据缺少桩号信息
            plugVo.setEvseNo(evse.getEvseNo());
        }

        if (null == plugVo.getIdx()) {
            plugVo.setIdx(plugReport.getPlugId());
        }

        log.info("更新redis枪头信息. plug = {}", plugVo);

        if (PlugStatus.IDLE.equals(plugVo.getStatus()) &&
            StringUtils.isNotBlank(plugVo.getOrderNo())) {
            // 枪头存在订单，且上报了IDLE状态
            log.info("清除订单: {}", plugVo.getOrderNo());
            plugVo.setOrderNo("");
        }
//        if(PlugStatus.IDLE.equals(plugVo.getStatus())) {
        plugVo.setVin(plugReport.getVin());
//        } else {
//
//        }

        //if (plugReport != null) {
        // 告警短信缺失字段, 需要通过mq传递
        plugVo.setErrorCode(plugReport.getErrorCode())
            .setAlertCode(plugReport.getAlertCode())
            .setErrorMsg(plugReport.getError())
            .setTemperature(plugReport.getTemp());

        // }
        PlugVo result;

        if (gwInfo != null && gwInfo.getMqType() == GwMqType.MQ_TYPE_MQTT) {
            result = this.redisIotRwService.updatePlugRedisCache(plugVo);
        } else if (!(evse.getProtocolVer() != null
            && evse.getProtocolVer() >= IotConstants.SERVFEE_TIME_DIVISION_PROTOVER
            && event == IotEvent.STATE_CHANGE)) {
            result = this.redisIotRwService.updatePlugRedisCache(plugVo);
        } else {
            result = this.redisIotRwService.getPlugRedisCache(plugVo.getEvseNo(), plugVo.getIdx());
        }

        //添加软件版本，设备型号
        PlugMqDto plugMqDto = new PlugMqDto();
        if (result != null) {
            BeanUtils.copyProperties(result, plugMqDto);
        }
        plugMqDto.setModelName(evse.getModelName())
            .setFirmwareVer(evse.getFirmwareVer());

        if (StringUtils.isNotBlank(linkId)) {
            this.dcEventPublish.publishPlugInfo(event, plugMqDto);    // 推送枪头状态变动的消息
        } else {
            this.dcEventPublish.publishPlugInfo(event, plugMqDto, linkId,
                ctrlNo);    // 推送来自控制器 枪头状态变动的消息
        }
        return result;
    }

    public PlugVo updateRedisPlugCacheV2(@NonNull IotEvent event,
        @Nullable SitePo site,
        @NonNull EvsePo evse,
        @NonNull PlugPo plug) {
        log.info("桩注册上报枪头信息。plug = {}", plug);
        PlugVo plugVo = new PlugVo();
        plugVo.setEvseNo(plug.getEvseId())
            .setPlugNo(PlugNoUtils.formatPlugNo(plug.getEvseId(), plug.getPlugId()))
            .setProtocol(evse.getProtocol())
            .setGwno(evse.getGwno())
            .setSiteId(evse.getSiteId())
            .setIdx(plug.getPlugId())
            .setName(plug.getName())
            .setStatus(plug.getPlugStatus())
            .setSupply(evse.getSupply())
            .setOrderNo(plug.getOrderNo())
            .setErrorCode(plug.getErrorCode())
            .setErrorMsg(plug.getErrorMsg())
            .setAlertCode(plug.getAlertCode())
            .setMaxCurrent(plug.getCurrentMax())
            .setMaxVoltage(plug.getVoltageMax())
            .setMinCurrent(plug.getCurrentMin())
            .setMinVoltage(plug.getVoltageMin())
            .setGwno(plug.getGwno());
        if (site != null) {
            plugVo.setSiteName(site.getName())
                .setSiteCommId(site.getCommId())
                .setPriceCode(site.getPriceCode());
        }
        PlugVo result;
        if (!(evse.getProtocolVer() != null
            && evse.getProtocolVer() >= IotConstants.SERVFEE_TIME_DIVISION_PROTOVER
            && event == IotEvent.STATE_CHANGE)) {
            log.info("更新redis枪头信息. plug = {}", plugVo);
            result = this.redisIotRwService.updatePlugRedisCache(plugVo);
        } else {
            result = this.redisIotRwService.getPlugRedisCache(evse.getEvseId(), plugVo.getIdx());
        }

        if (null == result) {
            log.error("枪头是异常: {}", evse.getEvseId());
            return null;
        }
        //添加 软件版本，设备型号
        PlugMqDto plugMqDto = new PlugMqDto();
        BeanUtils.copyProperties(result, plugMqDto);
        plugMqDto.setFirmwareVer(evse.getFirmwareVer())
            .setModelName(evse.getModelName());

        this.dcEventPublish.publishPlugInfo(event, plugMqDto);    // 推送枪头状态变动的消息
        return result;
    }

    /**
     * 重建redis缓存
     */
    public synchronized void buildRedisCache(@Nullable List<String> siteIds) {
        if (CollectionUtils.isNotEmpty(siteIds)) {
            siteIds.stream().forEach(siteId -> {
                SitePo site = siteRoDs.getSite(siteId);
                this.buildSiteCache(site);
            });
        } else {
            long start = 0;
            int size = 10;
            List<SitePo> siteList = siteRoDs.listSite(start, size);
            while (CollectionUtils.isNotEmpty(siteList)) {
                start = start + size;
                siteList.stream().forEach(this::buildSiteCache);
                siteList = siteRoDs.listSite(start, size);
            }
        }
    }

    /**
     * 重建场站相关的桩,枪缓存
     */
    public void buildSiteCache(SitePo site) {
        List<EvsePo> evseList = evseService.listBySiteId(site.getSiteId());
        Map<Long, EvseModelPo> modelMap = new HashMap<>();
        List<Long> modelIdList = evseList.stream().filter(e -> e.getModelId() != null)
            .map(EvsePo::getModelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(modelIdList)) {
            modelMap = evseModelRoDs.getList(modelIdList).stream()
                .collect(Collectors.toMap(EvseModelPo::getId, o -> o));
        }
        Map<Long, EvseModelPo> finalModelMap = modelMap;
        evseList.stream().forEach(e -> buildEvseCache(site, e, finalModelMap.get(e.getModelId())));
    }

    private void buildEvseCache(@Nullable SitePo site, EvsePo evse, EvseModelPo evseModelPo) {

        UpdateEvseCacheDto dto = new UpdateEvseCacheDto();
        dto.setEvent(IotEvent.UNKNOWN)
            .setSite(site)
            .setEvse(evse)
            .setLinkId(null);
        this.updateRedisEvseCache(dto);//重建缓存认为是更新
        List<PlugPo> plugList = this.plugService.listPlug(evse.getEvseId(), null,
            null, null, false);
        plugList.stream().forEach(p -> {
            if (p.getOrderNo() == null) {
                p.setOrderNo("");
            }
            UpdatePlugCacheDto tempDto = new UpdatePlugCacheDto();
            tempDto.setEvent(IotEvent.UNKNOWN)
                .setPlugReport(null)
                .setSite(site)
                .setEvse(evse)
                .setPlug(p);

            if (null != evseModelPo) {
                tempDto.setConstantCharge(evseModelPo.isConstantCharge());
            }
            updateRedisPlugCache(tempDto);//重建缓存认为是更新
        });
        if (site != null) {
            this.bindEvse(site.getSiteId(), evse.getEvseId(),
                plugList.stream()
                    .map(p -> PlugNoUtils.formatPlugNo(p.getEvseId(), p.getPlugId()))
                    .collect(Collectors.toList()));
        }
    }


    /**
     * 将发给iot网关的下行指令缓存到redis
     *
     * @param gwCmd 网关指令
     */
    public void addIotGwCmd(IotGwCmdCacheVo gwCmd) {
        this.redisIotRwService.addIotGwCmd(gwCmd);
    }


    /**
     * 收到网关的上行确认后, 将redis里缓存的下行指令删除
     *
     * @param gwno 网关编号
     * @param seq  指令的序列号
     */
    public void deleteIotGwCmd(String gwno, String seq) {
        this.redisIotRwService.deleteIotGwCmd(gwno, seq);
    }

}
