package com.cdz360.iot.worker.model.iot;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.upgrade.EvseVersion;
import lombok.Data;

import java.util.List;

@Data
public class UpgradeRequest {
    private String taskNo;
    private List<String> evseIds;
    private List<EvseVersion> evseBundle;
    private String downloadType;
    private String downloadUsername;
    private String downloadPasscode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
