package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * @Classname IotParkHystrixFeignClientFactory
 * @Description
 * @Date 4/23/2021 5:14 PM
 * @Created by Rafael
 */
@Slf4j
@Component
public class IotParkHystrixFeignClientFactory implements FallbackFactory<IotParkFeignClient> {
    @Override
    public IotParkFeignClient apply(Throwable throwable) {
        return new IotParkFeignClient() {
            @Override
            public Mono<BaseResponse> checkCoupon(OrderStopRequestV2 orderStopRequestV2) {
                log.error("【服务熔断】。orderStopRequestV2 = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, orderStopRequestV2);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, IotParkFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super IotParkFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }
}