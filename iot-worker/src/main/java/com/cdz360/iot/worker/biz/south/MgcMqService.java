package com.cdz360.iot.worker.biz.south;

import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.model.config.UpgDownloadProperties;
import com.cdz360.iot.model.gw.mqtt.MgcStatusReq;
import com.cdz360.iot.model.gw.mqtt.MgcUpgradeReq;
import com.cdz360.iot.model.site.po.GwInfoPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MgcMqService {


    @Autowired
    private UpgDownloadProperties ftp;

//    private String ftpBaseUrl;

    @Autowired
    private MqService mqService;

    public String sendUpgradeMqMsg(GwInfoPo gwInfo, String jarPath) {
        String fullPath = ftp.getSchema() + "://" + ftp.getHost() + ":" + ftp.getPort() + jarPath;
        log.info("升级 mgc. gwno = {}, ftpUrl = {}",
                gwInfo.getGwno(), fullPath);
        MgcUpgradeReq.REQ req = new MgcUpgradeReq.REQ(gwInfo.getGwno());
        req.getData().setFtpUrl(fullPath)
                .setFtpUsername(ftp.getUsername())
                .setFtpPassword(ftp.getPassword());
        mqService.publishMessage(gwInfo, true, req.toString());
        return req.getSeq();
    }


    /**
     * 云端主动查询 MGC 状态
     */
    public String sendAuditStatusMqReq(GwInfoPo gwInfo) {
        log.info("audit mgc. gwno = {}", gwInfo.getGwno());
        MgcStatusReq.REQ req = new MgcStatusReq.REQ(gwInfo.getGwno());
        mqService.publishMessage(gwInfo, false, req.toString());    // MGC 业务程序
        mqService.publishMessage(gwInfo, true, req.toString());// MGC 监控程序
        return req.getSeq();
    }
}
