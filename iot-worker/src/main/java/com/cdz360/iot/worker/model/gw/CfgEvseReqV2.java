package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CfgEvseReqV2 extends BaseObject {

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "配置内容版本号. 该信息从云端下发的'桩配置更新通知'请求中获取")
    private String cfgVer;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
