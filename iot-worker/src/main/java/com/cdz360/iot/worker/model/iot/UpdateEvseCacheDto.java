package com.cdz360.iot.worker.model.iot;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.site.po.SitePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UpdateEvseCacheDto {

    private IotEvent event;

    private SitePo site;

    private EvsePo evse;

    private String linkId;

    private EvseRegisterReason registerReason;
}
