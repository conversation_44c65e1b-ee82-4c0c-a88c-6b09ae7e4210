package com.cdz360.iot.worker.model.dongzheng;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname BoxSettingUpsertRequest
 * @Description
 * @Date 2019/6/13 18:00
 * @Created by tang<PERSON>yu
 */
public class BoxSettingUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键ID
    private Long id;
    // 批量配置下发时 批量修改桩配置
    private String ids;
    // 桩设备ID
    private String boxCode;
    // 设备序列号/桩号
    private String boxOutFactoryCode;
    // 计费模板id
    private Long chargeId;
    // 1下发成功2失败3下发中
    private Integer status;
    // 管理员密码
    private String adminPassword;
    // 二级管理员密码
    private String level2Password;
    // 白天音量
    private Integer dayVolume;
    // 夜晚音量
    private Integer nightVolume;
    // 二维码url
    private String url;
    // 是否支持充电记录查询 （1是0否）
    private Integer isQueryChargeRecord;
    // 是否支持定时充电 （1是0否）
    private Integer isTimedCharge;
    // 是否支持无卡充电 （1是0否）
    private Integer isNoCardCharge;
    // 是否支持扫码充电 （1是0否）
    private Integer isScanCharge;
    // 是否支持Vin码充电 （1是0否）
    private Integer isVinCharge;
    // 是否支持刷卡充电 （1是0否）
    private Integer isCardCharge;
    // 是否支持定额电量充电 （1是0否）
    private Integer isQuotaEleCharge;
    // 是否支持固定金额充电 （1是0否）
    private Integer isQuotaMoneyCharge;
    // 是否支持固定时长充电 （1是0否）
    private Integer isQuotaTimeCharge;
    // 国际协议
    private String internationalAgreement;
    // 自动停充 （1是0否）
    private Integer isAutoStopCharge;
    // 均/轮充设置 0均充 1轮充
    private Integer avgOrTurnCharge;
    // 合充开关 （1开0关）
    private Integer isCombineCharge;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 操作人id
    private Long updateByUserid;
    // 紧急充电卡下发状态1下发成功2失败3下发中
    private Long whiteCardsStatus;
    // 紧急充电卡列表，用,分隔
    private String whiteCardList;

    private Integer adminCodeResult;//管理员账号配置结果,0x00: 成功 其他表示失败
    private Integer triggerResult;//各种开关项配置结果,0x00: 成功 其他表示失败
    private Integer chargeResult;//电价配置结果,0x00: 成功 其他表示失败
    private Integer qrResult;//二维码配置结果,0x00: 成功 其他表示失败

    public Integer getAdminCodeResult() {
        return adminCodeResult;
    }

    public void setAdminCodeResult(Integer adminCodeResult) {
        this.adminCodeResult = adminCodeResult;
    }

    public Integer getTriggerResult() {
        return triggerResult;
    }

    public void setTriggerResult(Integer triggerResult) {
        this.triggerResult = triggerResult;
    }

    public Integer getChargeResult() {
        return chargeResult;
    }

    public void setChargeResult(Integer chargeResult) {
        this.chargeResult = chargeResult;
    }

    public Integer getQrResult() {
        return qrResult;
    }

    public void setQrResult(Integer qrResult) {
        this.qrResult = qrResult;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getBoxOutFactoryCode() {
        return boxOutFactoryCode;
    }

    public void setBoxOutFactoryCode(String boxOutFactoryCode) {
        this.boxOutFactoryCode = boxOutFactoryCode;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAdminPassword() {
        return adminPassword;
    }

    public void setAdminPassword(String adminPassword) {
        this.adminPassword = adminPassword;
    }

    public String getLevel2Password() {
        return level2Password;
    }

    public void setLevel2Password(String level2Password) {
        this.level2Password = level2Password;
    }

    public Integer getDayVolume() {
        return dayVolume;
    }

    public void setDayVolume(Integer dayVolume) {
        this.dayVolume = dayVolume;
    }

    public Integer getNightVolume() {
        return nightVolume;
    }

    public void setNightVolume(Integer nightVolume) {
        this.nightVolume = nightVolume;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getIsQueryChargeRecord() {
        return isQueryChargeRecord;
    }

    public void setIsQueryChargeRecord(Integer isQueryChargeRecord) {
        this.isQueryChargeRecord = isQueryChargeRecord;
    }

    public Integer getIsTimedCharge() {
        return isTimedCharge;
    }

    public void setIsTimedCharge(Integer isTimedCharge) {
        this.isTimedCharge = isTimedCharge;
    }

    public Integer getIsNoCardCharge() {
        return isNoCardCharge;
    }

    public void setIsNoCardCharge(Integer isNoCardCharge) {
        this.isNoCardCharge = isNoCardCharge;
    }

    public Integer getIsScanCharge() {
        return isScanCharge;
    }

    public void setIsScanCharge(Integer isScanCharge) {
        this.isScanCharge = isScanCharge;
    }

    public Integer getIsVinCharge() {
        return isVinCharge;
    }

    public void setIsVinCharge(Integer isVinCharge) {
        this.isVinCharge = isVinCharge;
    }

    public Integer getIsCardCharge() {
        return isCardCharge;
    }

    public void setIsCardCharge(Integer isCardCharge) {
        this.isCardCharge = isCardCharge;
    }

    public Integer getIsQuotaEleCharge() {
        return isQuotaEleCharge;
    }

    public void setIsQuotaEleCharge(Integer isQuotaEleCharge) {
        this.isQuotaEleCharge = isQuotaEleCharge;
    }

    public Integer getIsQuotaMoneyCharge() {
        return isQuotaMoneyCharge;
    }

    public void setIsQuotaMoneyCharge(Integer isQuotaMoneyCharge) {
        this.isQuotaMoneyCharge = isQuotaMoneyCharge;
    }

    public Integer getIsQuotaTimeCharge() {
        return isQuotaTimeCharge;
    }

    public void setIsQuotaTimeCharge(Integer isQuotaTimeCharge) {
        this.isQuotaTimeCharge = isQuotaTimeCharge;
    }

    public String getInternationalAgreement() {
        return internationalAgreement;
    }

    public void setInternationalAgreement(String internationalAgreement) {
        this.internationalAgreement = internationalAgreement;
    }

    public Integer getIsAutoStopCharge() {
        return isAutoStopCharge;
    }

    public void setIsAutoStopCharge(Integer isAutoStopCharge) {
        this.isAutoStopCharge = isAutoStopCharge;
    }

    public Integer getAvgOrTurnCharge() {
        return avgOrTurnCharge;
    }

    public void setAvgOrTurnCharge(Integer avgOrTurnCharge) {
        this.avgOrTurnCharge = avgOrTurnCharge;
    }

    public Integer getIsCombineCharge() {
        return isCombineCharge;
    }

    public void setIsCombineCharge(Integer isCombineCharge) {
        this.isCombineCharge = isCombineCharge;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateByUserid() {
        return updateByUserid;
    }

    public void setUpdateByUserid(Long updateByUserid) {
        this.updateByUserid = updateByUserid;
    }

    public Long getWhiteCardsStatus() {
        return whiteCardsStatus;
    }

    public void setWhiteCardsStatus(Long whiteCardsStatus) {
        this.whiteCardsStatus = whiteCardsStatus;
    }

    public String getWhiteCardList() {
        return whiteCardList;
    }

    public void setWhiteCardList(String whiteCardList) {
        this.whiteCardList = whiteCardList;
    }
}
