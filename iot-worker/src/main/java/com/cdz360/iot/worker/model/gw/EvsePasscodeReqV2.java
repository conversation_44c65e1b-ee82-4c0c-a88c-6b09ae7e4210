package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.model.base.type.EvseProtocolType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "请求桩端长效密钥 v2")
public class EvsePasscodeReqV2 {

    @Schema(description = "协议类型")
    private EvseProtocolType protocol;

    @Schema(description = "桩编号", example = "0166666666")
    private String evseNo;

    @Schema(description = "密钥版本号", example = "123456789")
    private Long passcodeVer;
}
