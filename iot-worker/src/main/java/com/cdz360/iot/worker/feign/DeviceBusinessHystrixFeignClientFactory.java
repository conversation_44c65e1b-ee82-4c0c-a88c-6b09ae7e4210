package com.cdz360.iot.worker.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.worker.model.dongzheng.BoxSettingUpdateRequest;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DeviceBusinessHystrixFeignClientFactory implements FallbackFactory<DeviceBusinessFeignClient> {

    @Override
    public DeviceBusinessFeignClient apply(Throwable throwable) {
        //log.error("【服务熔断】。Service = {}, message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable.getMessage());
        //log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable);
        return new DeviceBusinessFeignClient() {

            @Override
            public Mono<BaseResponse> updateBoxSetting(BoxSettingUpdateRequest request) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

//            @Override
//            public Mono<ObjectResponse<Long>> bindTemplateToConnector(String evseNo) {
//                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST);
//                return Mono.just(RestUtils.serverBusy4ObjectResponse());
//            }


            @Override
            public Mono<BaseResponse> downSetting2Evse(String evseNo, String siteId) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST);
                return Mono.just(RestUtils.serverBusy());
            }

        };
    }

    @Override
    public <V> Function<V, DeviceBusinessFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super DeviceBusinessFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST);
        return null;
    }
}
