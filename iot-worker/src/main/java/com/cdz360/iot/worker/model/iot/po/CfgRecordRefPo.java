package com.cdz360.iot.worker.model.iot.po;

import com.cdz360.iot.model.base.DbObject;
import com.cdz360.iot.worker.type.CfgResultType;

public class CfgRecordRefPo extends DbObject {
    private Long recordId;
    private String evseId;
    private CfgResultType result;

    public Long getRecordId() {
        return recordId;
    }

    public CfgRecordRefPo setRecordId(Long recordId) {
        this.recordId = recordId;
        return this;
    }

    public String getEvseId() {
        return evseId;
    }

    public CfgRecordRefPo setEvseId(String evseId) {
        this.evseId = evseId;
        return this;
    }

    public CfgResultType getResult() {
        return result;
    }

    public CfgRecordRefPo setResult(CfgResultType result) {
        this.result = result;
        return this;
    }
}
