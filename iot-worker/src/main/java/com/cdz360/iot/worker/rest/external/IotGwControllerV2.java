package com.cdz360.iot.worker.rest.external;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.auth.CusAuthReqEx;
import com.cdz360.iot.model.auth.CusAuthReqV2;
import com.cdz360.iot.model.auth.CusAuthResBaseV2;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.base.EvseCfgResponse;
import com.cdz360.iot.model.base.OrderCreateResponseV2;
import com.cdz360.iot.model.evse.ChgEvseRequestV2;
import com.cdz360.iot.model.evse.DebugRequest;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.evse.OrderFeeRefreshRequestV2;
import com.cdz360.iot.model.evse.OrderFeeRefreshResponseV2;
import com.cdz360.iot.model.evse.OrderStartRequestV2;
import com.cdz360.iot.model.evse.OrderStartingRequest;
import com.cdz360.iot.model.evse.OrderStopRequestV2;
import com.cdz360.iot.model.evse.OrderUpdateRequestV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseV2;
import com.cdz360.iot.model.evse.po.EvsePasscodePo;
import com.cdz360.iot.model.gw.GwAuthReqMsg;
import com.cdz360.iot.model.gw.GwMsg;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.gw.GwObjResMsg;
import com.cdz360.iot.model.gw.GwResMsg;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.biz.EvseProcessor;
import com.cdz360.iot.worker.biz.IotService;
import com.cdz360.iot.worker.biz.south.IotSouthBizService;
import com.cdz360.iot.worker.biz.south.OrderSouthBizService;
import com.cdz360.iot.worker.model.gw.CfgEvseReqV2;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import com.cdz360.iot.worker.model.gw.EvsePasscodeReqV2;
import com.cdz360.iot.worker.model.gw.EvseResiterReqV2;
import com.cdz360.iot.worker.model.gw.GwRegisterReqV2;
import com.cdz360.iot.worker.model.gw.IotGwCmdResultReqV2;
import com.cdz360.iot.worker.model.gw.LoginRes;
import com.cdz360.iot.worker.model.gw.RegisterRes;
import com.cdz360.iot.worker.model.gw.UpgradeReq;
import com.cdz360.iot.worker.model.iot.param.GwLoginParam;
import com.cdz360.iot.worker.utils.IpUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "iot网关接入API (v2)", description = "南向-网关接口")
@RequestMapping("/iot/")
public class IotGwControllerV2 {

    @Autowired
    private IotService iotService;

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private IotSouthBizService iotService2;

    @Autowired
    private OrderSouthBizService orderService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private EvseProcessor evseProcessor;


    @Operation(summary = "网关初始化接口")
    @PostMapping(value = "/gw/register", params = {"v=2"})
    public GwObjResMsg<RegisterRes> register(
            ServerHttpRequest request,
            @RequestParam(value = "n") String gwno,
            @RequestParam(value = "v") int ver,
            @RequestBody GwObjReqMsg<GwRegisterReqV2> req) {
        log.info(">> 网关初始化接口。req = {}", req);
        long startTime = System.nanoTime();    // debug 性能问题
        IotAssert.isNotNull(req, "请求参数不能为空");
        IotAssert.isNotNull(req.getData(), "请求参数不能为空.");

        String wanIp = IpUtils.getIpAddress(request);
        RegisterRes ret = this.iotService.register(gwno, req.getData(), wanIp, ver);
        log.info("<<");
        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "register", "网关初始化", startTime);
        //debugPerformance("网关初始化", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), ret);
    }

    @Operation(summary = "网关登录接口")
    @PostMapping(value = "/gw/login", params = {"v=2"})
    public GwObjResMsg<LoginRes> login(
            ServerHttpRequest request,
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "v") int ver,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwMsg req) throws NoSuchAlgorithmException {

        // log.info(LoggerHelper.formatEnterLog(request));

        log.info(">> 网关登录接口。gwno = {}, authHd = {}", gwno, authHd);
        long startTime = System.nanoTime();    // debug 性能问题

        String ip = IpUtils.getIpAddress(request);

        var token = this.getToken(authHd);
        if (StringUtils.isBlank(token)) {
            // tian
            log.info("<< no authHd");
            throw new DcTokenException(gwno, "没有 token");
        }
        GwLoginParam param = new GwLoginParam();
        param.setGwno(gwno)
                .setProtocolVer(ver)
                .setToken(token)
                .setWanIp(ip)
                .setLanIp(req.getLanIp())
                .setMac(req.getMac())
                .setBootTime(req.getBootTime())
                .setSwVer(req.getSwVer())
                .setSwVerCode(req.getSwVerCode())
                .setGitCommitId(req.getGitCommitId());
        LoginRes ret = this.iotService.login(param);
        log.info("<<");
        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "login", "网关登录", startTime);
        //debugPerformance("网关登录", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), ret);
    }


    /**
     * 解析 Authorization 头, 将token部分返回.
     * <p>
     * `Authorization: Basic c6697be63855fdfc675bb11a5b622feb91c2955241efce332c739ef95d6e1807`
     * </p>
     *
     * @param authHd
     * @return
     */
    private String getToken(String authHd) {
        if (StringUtils.isBlank(authHd)) {
            return null;
        }
        String ret = GwRestUtils.getToken(authHd);
        if (StringUtils.isNotBlank(ret)) {
            return ret;
        } else {
            log.error("get token failed. {}", authHd);
            return null;
        }
    }

    @Operation(summary = "桩注册")
    @PostMapping(value = "/evse/register", params = {"v=2"})
    public BaseResponse evseRegister(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "v") int ver,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<EvseResiterReqV2> req) {
        log.info(">> authHd = {}, gwno = {},req = {}", authHd, gwno, JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        iotService2.evseRegister(req.getData(), gwno);
        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "evseRegister", "桩注册", startTime);
        //debugPerformance("桩注册", startTime);
        return GwRestUtils.buildResponse(req.getSeq());
    }

    @Operation(summary = "获取桩密钥")
    @PostMapping(value = "/evse/passcode", params = {"v=2"})
    public GwObjResMsg<EvsePasscodePo> getEvsePasscode(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "v") int ver,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<EvsePasscodeReqV2> req) {
        log.info(">> getEvsePasscode = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        req.setGwno(gwno);
        EvsePasscodePo evsePasscodePo = this.iotService.getEvsePasscode(req.getData());
        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "getEvsePasscode", "获取桩密钥", startTime);
        //debugPerformance("获取桩密钥", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), evsePasscodePo);
    }

    @Operation(summary = "下行指令执行反馈")
    @PostMapping(value = "/cmd/result", params = {"v=2"})
    public Mono<GwResMsg> cmdResult(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<IotGwCmdResultReqV2> req) {

        log.info(">> authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

//        checkToken(authHd, gwno);
        req.setGwno(gwno);
        return this.iotService.processCmdResult(req.getData(), gwno)
                .doOnNext(res1 -> {

                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "cmdResult", "下行指令执行反馈", startTime);
                })
                .map(res2 -> GwRestUtils.buildResponse(req.getSeq()));
        //debugPerformance("下行指令执行反馈", startTime);
        //return ;
    }


    @Operation(summary = "获取桩配置")
    @PostMapping(value = "/cfg/evse", params = {"v=2"})
    public GwObjResMsg<CfgEvseV2> cfgEvse(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<CfgEvseReqV2> req) throws IOException {

        log.info("获取桩配置。>> authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);

        GwObjResMsg<CfgEvseV2> ret = this.iotService.getCfgEvse(gwno, req);

        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "cfgEvse", "获取桩配置", startTime);
        //debugPerformance("获取桩配置", startTime);
        return ret;
    }

    @Operation(summary = "桩配置更新结果上报")
    @PostMapping(value = "/cfg/evse/result", params = {"v=2"})
    public Mono<GwObjResMsg> cfgEvseResult(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<CfgEvseResultReqV2> req) throws IOException {
        log.info("桩配置更新结果上报。 authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.cfgEvseResultV2(req, gwno)
                .doOnNext(res1 -> {
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "cfgEvseResult", "桩配置更新结果上报", startTime);
                });
        //debugPerformance("桩配置更新结果上报", startTime);
        //return ret;
    }


    @Operation(summary = "充电桩/枪的状态上报")
    @PostMapping(value = "/evse/status", params = {"v=2"})
    public Mono<BaseGwResponse> evseStatus(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<EvseReportRequestV2> req
    ) throws IOException {
        log.info("充电桩/枪的状态上报。authHd = {}, gwno = {},req = {}", authHd, gwno, JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        //Mono<BaseGwResponse> ret =
        req.setGwno(gwno);
        //iotService.processEvseStatusReport(gwno, req);
        return evseProcessor.processEvseNormalStatusV2(req.getData(), gwno)
                .map(res -> {
                    log.info("处理完成");
                    BaseGwResponse ret = new BaseGwResponse();
                    ret.setSeq(req.getSeq());
                    return ret;
                })
                .doOnNext(res -> {
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "evseStatus", "充电桩/枪的状态上报", startTime);
                });
    }


    @Operation(summary = "鉴权")
    @PostMapping(value = "/cus/auth", params = {"v=2"})
    public Mono<GwAuthReqMsg<CusAuthResBaseV2>> auth(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<CusAuthReqV2> req) throws IOException {   // TODO: 参数类型要定义

        log.info("鉴权。authHd = {}, gwno = {},req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);

        CusAuthReqEx cusAuthReqEx = new CusAuthReqEx();
        //BeanUtils.copyProperties(req, cusAuthReqEx);
        cusAuthReqEx.setGwno(gwno);
        cusAuthReqEx.setSeq(req.getSeq());
        cusAuthReqEx.setAccountNo(req.getData().getAccountNo());
        cusAuthReqEx.setAuthType(req.getData().getAuthType());
        cusAuthReqEx.setEvseId(req.getData().getEvseNo());
        cusAuthReqEx.setPlugId(req.getData().getPlugId());
        cusAuthReqEx.setSave2Redis(true);   // 兼容旧版行为
//        CusAuthRes result =
        return orderService.auth(cusAuthReqEx)
                .map(res1 -> {
                    //丢弃不必要的字段
                    CusAuthResBaseV2 cusAuthResBase = new CusAuthResBaseV2();
                    cusAuthResBase.setAmount(res1.getBalance())
                            .setPower(res1.getPower())
                            .setCarNo(res1.getCarNo());
                    GwAuthReqMsg<CusAuthResBaseV2> res = new GwAuthReqMsg<>(cusAuthResBase);
                    res.setSeq(req.getSeq());
                    return res;
                })
                .doOnNext(res2 -> {
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "auth", "鉴权", startTime);

                });
        //debugPerformance("鉴权", startTime);
        //log.info("<<");
        //return res;
    }

    //@Operation(summary = "桩端发起充电请求")
    @PostMapping(value = "/order/create", params = {"v=2"})
    public Mono<CommonResponse<OrderCreateResponseV2>> orderCreate(
            ServerHttpResponse response,
            //@Parameter(name = "鉴权")
            @RequestHeader(value = "Authorization", required = false) String authHd,
            //@Parameter(name = "网关编号")
            @RequestParam(value = "n") String gwno,
            //@Parameter(name = "订单开启请求")
            @RequestBody GwObjReqMsg<ChgEvseRequestV2> chgEvseReq
    ) throws IOException {

        log.info("充电桩/桩端发起充电请求。authHd = {}, gwno = {}, req = {}", authHd, gwno, chgEvseReq);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        if (StringUtils.isBlank(chgEvseReq.getGwno())) {
            chgEvseReq.setGwno(gwno);
        }
        return this.orderService.createOrder(chgEvseReq.getData(), gwno)
                .map(ret -> {
                    CommonResponse<OrderCreateResponseV2> res = new CommonResponse<>(ret);
                    res.setSeq(chgEvseReq.getSeq());
                    response.getHeaders().put(IotConstants.HTTP_HEADER_SEQ, List.of(chgEvseReq.getSeq()));
                    log.info("<< ret = {}", ret);
                    //debugPerformance("桩端发起充电请求", startTime);
                    return res;
                })
                .doOnNext(res2 -> {
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderCreate", "桩端发起充电请求", startTime);
                });

    }

    @Operation(summary = "充电指令下发结果上报")
    @PostMapping(value = "/order/starting", params = {"v=2"})
    public Mono<BaseGwResponse> orderStarting(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody OrderStartingRequest orderStartReq
    ) throws IOException {
        log.info("充电指令下发结果上报。authHd = {}, gwno = {}, req = {}", authHd, gwno, orderStartReq);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStartReq.getGwno())) {
            orderStartReq.setGwno(gwno);
        }
        // IOT订单表数据插入
        orderStartReq.setEvseNo(orderStartReq.getEvseNo());
        //BaseGwResponse ret =
        return this.orderService.orderStarting(orderStartReq)
                .doOnNext(res1 -> {

                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderStarting", "充电指令下发结果上报", startTime);
                });
        //log.info("<<");
        //debugPerformance("充电指令下发结果上报", startTime);
        //return ret;
    }

    @Operation(summary = "充电开始状态上报")
    @PostMapping(value = "/order/start", params = {"v=2"})
    public Mono<BaseGwResponse> orderStart(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<OrderStartRequestV2> orderStartReq
    ) throws IOException {
        log.info("充电桩/充电开始状态上报。authHd = {}, gwno = {}, req = {}", authHd, gwno, orderStartReq);
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStartReq.getGwno())) {
            orderStartReq.setGwno(gwno);
        }
        IotAssert.isNotNull(orderStartReq.getGwno(), "JSON 缺少gwno");
        IotAssert.isNotNull(orderStartReq.getSeq(), "JSON 缺少seq");

        IotAssert.isTrue(orderStartReq.getGwno() != null && orderStartReq.getGwno() != "", "请求参数Gwno为空");
        IotAssert.isTrue(StringUtils.isNotBlank(orderStartReq.getData().getEvseNo()), "请求参数EvseNo为空");
        IotAssert.isTrue(orderStartReq.getData().getPlugId() != null && orderStartReq.getData().getPlugId() >= 0, "请求参数PlugId为空");
        IotAssert.isTrue(StringUtils.isNotBlank(orderStartReq.getData().getOrderNo()), "请求参数OrderNo为空");


        // IOT订单表数据插入
//        BaseGwResponse ret =
        return this.orderService.orderStarted(orderStartReq)
                .doOnNext(res1 -> {

                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderStart", "充电开始状态上报", startTime);
                });
//        log.info("<<");
//        //debugPerformance("充电开始状态上报", startTime);
//        return ret;
    }


    @Operation(summary = "充电中状态更新")
    @PostMapping(value = "/order/update", params = {"v=2"})
    public Mono<BaseGwResponse> orderUpdate(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<OrderUpdateRequestV2> orderUpdateReq
    ) throws IOException {

        log.info("充电中状态更新。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(orderUpdateReq));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderUpdateReq.getGwno())) {
            orderUpdateReq.setGwno(gwno);
        }
        // Mono<BaseGwResponse> ret = Mono.just(new BaseGwResponse(this.getSeq(jsonBody)));
        //BaseGwResponse ret =
        return this.orderService.orderUpdate(orderUpdateReq.getData())
                .doOnNext(res1 -> {
                    res1.setSeq(orderUpdateReq.getSeq());
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderUpdate", "充电中状态更新", startTime);
                });

//        log.info("<<");
//        //debugPerformance("充电中状态更新", startTime);
//        return ret;
    }

    @Operation(summary = "充电完成状态上报")
    @PostMapping(value = "/order/stop", params = {"v=2"})
    public Mono<BaseGwResponse> orderStop(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<OrderStopRequestV2> orderStopReqV2
    ) throws IOException {

        log.info("充电完成状态上报。authHd = {}, gwno = {}, req = {}", authHd, gwno, JsonUtils.toJsonString(orderStopReqV2));
        long startTime = System.nanoTime();    // debug 性能问题

        checkToken(authHd, gwno);
        if (StringUtils.isBlank(orderStopReqV2.getGwno())) {
            orderStopReqV2.setGwno(gwno);
        }
//        BaseGwResponse ret =
        return this.orderService.orderStop(orderStopReqV2)
                .doOnNext(res1 -> {

                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderStop", "充电完成状态上报", startTime);
                });
        //log.info("<<");
        //debugPerformance("充电完成状态上报", startTime);
        //return ret;
    }

    @Operation(summary = "充电中订单续费")
    @PostMapping(value = "/order/fee/refresh", params = {"v=2"})
    public Mono<GwObjResMsg<OrderFeeRefreshResponseV2>> orderFeeRefresh(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<OrderFeeRefreshRequestV2> req
    ) throws IOException {
        log.info(">> 桩端请求订单续费。authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        if (StringUtils.isBlank(req.getGwno())) {
            req.setGwno(gwno);
        }

        // 参数校验
        if (StringUtils.isBlank(req.getData().getOrderNo()) ||
                StringUtils.isBlank(req.getData().getEvseNo()) ||
                null == req.getData().getPlugId()) {
            log.info("<< 桩端请求订单续费参数异常, 订单编号/桩编号/枪编号不存在: req={}", req);
            throw new DcArgumentException("桩端请求订单续费, 订单编号/桩编号/枪编号参数异常");
        }

        // 需要对接具体业务服务

        // GwObjResMsg<OrderFeeRefreshResponseV2> res =
        return this.orderService.refreshFee(req)
                .doOnNext(res -> {
                    log.info("<< 桩端请求订单续费结果: res={}", res);
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "orderFeeRefresh", "充电中订单续费", startTime);
                });
//
//        log.info("<< 桩端请求订单续费结果: res={}", res);
//        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
//                "orderFeeRefresh", "充电中订单续费", startTime);
//        //debugPerformance("充电中订单续费", startTime);
//        return res;
    }

    @Operation(summary = "上传桩端DEBUG数据")
    @PostMapping(value = "/evse/debug", params = {"v=2"})
    public BaseGwResponse evseDebug(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody GwObjReqMsg<DebugRequest> req
    ) throws IOException {
        log.info(">> 上传桩端DEBUG数据。authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        //TODO raf V2
        return BaseGwResponse.newInstance();
    }

    private void checkToken(String authHd, String gwno) {
        IotAssert.isTrue(!StringUtils.isBlank(gwno), "参数错误,网关编号不能为空");
        var tokenCache = this.iotCacheService.getToken(gwno);
        var tokenHd = this.getToken(authHd);
        if (StringUtils.isBlank(tokenCache) || StringUtils.isBlank(tokenHd)
                || !tokenCache.equalsIgnoreCase(tokenHd)) {
            log.warn("token 校验失败. gwno = {}, 网关发送 token = {}, 缓存 token = {}", gwno, tokenHd, tokenCache);
            throw new DcTokenException(gwno, "登录已过期");
        }
    }


    @Operation(summary = "上传桩配置信息")
    @PostMapping(value = "/cfg/evse/info", params = {"v=2"})
    public BaseGwResponse cfgEvseInfo(@RequestHeader(value = "Authorization", required = false) String authHd,
                                      @RequestParam(value = "n") String gwno,
                                      @Parameter(name = "配置内容版本号.") @RequestBody GwObjReqMsg<List<CfgEvseAllV2>> cfgEvseInfoRequest) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("上传桩配置信息. gwno = {}, msg = {}", gwno, cfgEvseInfoRequest);
        String seq = cfgEvseInfoRequest.getSeq();
        List<CfgEvseAllV2> cfgList = cfgEvseInfoRequest.getData();
        log.info(">> 桩上报配置 evse cfg info: {}, {}", seq, cfgList);
        checkToken(authHd, gwno);
        var res = new BaseGwResponse(seq);
        if (cfgList.isEmpty()) {
            return res;
        }
        businessService.evseCfgInfoV2(cfgList);
        //iotWorkerFeignClient.cfgEvseInfo(seq, cfgList);
        log.info("<< 桩上报配置 ok: {}", res);
        LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                "cfgEvseInfo", "上传桩配置信息", startTime);
        //debugPerformance("上传桩配置信息", startTime);
        return res;
    }


    @Operation(summary = "桩上报固件升级结果")
    @PostMapping(value = "/upgrade/evse/result", params = {"v=2"})//TODO:这个路由地址需要确认是否合理
    public Mono<EvseCfgResponse> upgradeResult(
            @RequestHeader(value = "Authorization", required = false) String authHd,
            @RequestParam(value = "n") String gwno,
            @RequestBody UpgradeReq req) throws IOException {
        log.info("桩固件升级结果上报。 authHd = {}, gwno = {}, req = {}", authHd, gwno, req);
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        return this.iotService.upgradeResult(req)
                .doOnNext(res1 -> {
                    LogHelper.logLatency(log, IotGwControllerV2.class.getSimpleName(),
                            "upgradeResult", "桩上报固件升级结果", startTime);

                });
        //debugPerformance("桩上报固件升级结果", startTime);
        //return ret;
    }


}
