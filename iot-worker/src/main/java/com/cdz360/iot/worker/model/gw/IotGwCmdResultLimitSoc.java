package com.cdz360.iot.worker.model.gw;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Classname IotGwCmdResultLimitSoc
 * @Description
 * @Date 6/2/2021 2:18 PM
 * @Created by Rafael
 */
@Data
public class IotGwCmdResultLimitSoc {
    private String orderNo;
    private String seq;

    /**
     * 0: 成功
     * 其他: 失败
     */
    @Schema(description = "调整SOC限制返回结果")
    private Integer result;
}