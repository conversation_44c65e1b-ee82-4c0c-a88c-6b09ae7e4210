package com.cdz360.iot.worker.biz;

import com.cdz360.base.utils.NumberUtils;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisEvseRegisterService {

    private final long LIMITED_SLOT = 5; // 统计时间，单位分钟
    private final long LIMITED_MAX_TIMES = 10; // 统计时间内，允许的注册次数

    private final String EVSE_LOGIN_TIMES_PREFIX = "iot:evse:login:times:"; // 桩统计时间内，注册的次数

    private final String EVSE_LOGIN_LIMITED = "iot:evse:login:limited"; // 已被限制配置下发的桩

    private final long UNLIMITED_MSEC = 10 * 60 * 1000; // 10分钟（每个桩限制的时间，单位毫秒）

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 判断桩是否可执行注册后桩配置相关操作
     *
     * @param evseNo
     * @return
     */
    public boolean limitOrNot(String evseNo) {
        Object obj = stringRedisTemplate.opsForHash().get(EVSE_LOGIN_LIMITED, evseNo);

        boolean limitOrNot = false;
        try {
            if (obj != null) {
                long currentTimeMillis = System.currentTimeMillis();
                long limitTimestamp = Long.parseLong(obj.toString());

                if ((currentTimeMillis - limitTimestamp) < UNLIMITED_MSEC) {
                    limitOrNot = true;
                } else {
                    this.unlimitEvse(evseNo);
                }
            }

            if (!limitOrNot) {
                long alreadyTimes = this.getTimes(evseNo);
                if (alreadyTimes >= LIMITED_MAX_TIMES) {
                    this.limitEvse(evseNo);
                    limitOrNot = true;
                } else {
                    this.addTimes(evseNo);
                    limitOrNot = false;
                }
            }
        } catch (Exception ex) {
            log.error("limitOrNot error: {}", ex.getMessage(), ex);
        }
        return limitOrNot;
    }

    /**
     * 增加桩注册次数
     *
     * @param evseNo
     */
    private void addTimes(String evseNo) {
        String key = EVSE_LOGIN_TIMES_PREFIX + evseNo;

        long times = NumberUtils.parseLong(stringRedisTemplate.opsForValue().get(key), 0);
        if (times > 0) {
            // 维持过期时间，只更新次数
            stringRedisTemplate.boundValueOps(key).increment(1);
        } else {
            stringRedisTemplate.opsForValue().set(key, "1", LIMITED_SLOT, TimeUnit.MINUTES);
        }
    }

    /**
     * 获取桩注册次数
     *
     * @param evseNo
     * @return
     */
    private long getTimes(String evseNo) {
        String key = EVSE_LOGIN_TIMES_PREFIX + evseNo;
        return NumberUtils.parseLong(stringRedisTemplate.opsForValue().get(key), 0);
    }

    /**
     * 限制此桩的配置下发
     *
     * @param evseNo
     */
    private void limitEvse(String evseNo) {
        stringRedisTemplate.delete(EVSE_LOGIN_TIMES_PREFIX + evseNo);
        stringRedisTemplate.opsForHash()
            .put(EVSE_LOGIN_LIMITED, evseNo, String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 解除此桩的限制
     *
     * @param evseNo
     */
    private void unlimitEvse(String evseNo) {
        stringRedisTemplate.opsForHash()
            .delete(EVSE_LOGIN_LIMITED, evseNo);
    }

}
