package com.cdz360.iot.worker.biz;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.RedisUtil;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.model.evse.cfg.CfgEvse;
import com.cdz360.iot.model.evse.cfg.CfgEvseAllV2;
import com.cdz360.iot.model.evse.cfg.CfgEvseParam;
import com.cdz360.iot.model.evse.cfg.CfgEvseV2;
import com.cdz360.iot.model.evse.cfg.CfgTime;
import com.cdz360.iot.model.evse.cfg.Charge;
import com.cdz360.iot.model.evse.cfg.PriceSchema;
import com.cdz360.iot.model.evse.cfg.WhiteCard;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseCfgRedisService {

    private final String EVSE_REGISTER_CONFIG_SWITCH = "iot:evse:register:config:switch"; // 桩注册后配置下发功能开关
    private final String EVSE_REGISTER_CONFIG_LIMITED = "iot:evse:register:config:limited"; // 已被限制配置下发的桩
    private final static long TIMEOUT = 10;
    private final static long TIMEOUTCFGEVSEALL = 5;
    private final Logger logger = LoggerFactory.getLogger(EvseCfgRedisService.class);

    @Value("${evse.cfg.registerLimitMinutes:120}")
    private Long REGISTER_LIMIT_MINUTES; // 桩注册下发配置限制的最大分钟数

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Deprecated
    public void append(String redisId, CfgEvse cfgEvse) {
        redisUtil.rightPushList(redisId, cfgEvse.toJsonString());
        redisUtil.expire(redisId, TIMEOUT, TimeUnit.MINUTES);
    }

    public void appendV2(String redisId, CfgEvseV2 cfgEvse) {
        redisUtil.rightPushList(redisId, cfgEvse.toJsonString());
        redisUtil.expire(redisId, TIMEOUT, TimeUnit.MINUTES);
    }


    public CfgEvseAllV2 lastCfgEvseAllV2(String redisId) {
        String cfgEvseAllV2Str = redisUtil.leftPopList(redisId);
        CfgEvseAllV2 cfgEvseAllV2 = null;
        if (StringUtils.isNotBlank(cfgEvseAllV2Str)) {
            cfgEvseAllV2 = JsonUtils.fromJson(cfgEvseAllV2Str, CfgEvseAllV2.class);
        }
        return cfgEvseAllV2;
    }

    public void appendCfgEvseAllV2(String redisId, CfgEvseAllV2 cfgEvse) {
        redisUtil.rightPushList(redisId, cfgEvse.toJsonString());
        redisUtil.expire(redisId, TIMEOUTCFGEVSEALL, TimeUnit.MINUTES);
    }

    public Long rPushCfgEvseParam(CfgEvseParam cfgEvseParam) {
        return redisUtil.rightPushList(IotConstants.EVSE_CONFIG_LIST,
            JsonUtils.toJsonString(cfgEvseParam));
    }

    public CfgEvseParam lPopCfgEvseParam() {
        String cfgEvseString = redisUtil.leftPopList(IotConstants.EVSE_CONFIG_LIST);
        if (StringUtils.isBlank(cfgEvseString)) {
            return null;
        }
        return JsonUtils.fromJson(cfgEvseString, CfgEvseParam.class);
    }

    public CfgEvse get(String redisId, String cfgVer) {

        //TODO redis transactional

        CfgEvseV2 cfgEvseV2 = getV2(redisId, cfgVer);

        // convert: v2 -> v1
        if (cfgEvseV2 == null) {
            return null;
        }
        CfgEvse cfgEvse = new CfgEvse();
        cfgEvse.setAdminCodeA(cfgEvseV2.getAdminCodeA());
        cfgEvse.setAdminCodeB(cfgEvseV2.getAdminCodeB());
        cfgEvse.setVin(cfgEvseV2.getVin());
        cfgEvse.setQrUrl(cfgEvseV2.getQrUrl());
        cfgEvse.setQueryChargeRecord(cfgEvseV2.getQueryChargeRecord());
        cfgEvse.setQrCharge(cfgEvseV2.getQrCharge());
        cfgEvse.setCardCharge(cfgEvseV2.getCardCharge());
        cfgEvse.setNoCardCharge(cfgEvseV2.getNoCardCharge());
        cfgEvse.setTimedCharge(cfgEvseV2.getTimedCharge());
        cfgEvse.setDayVolume(cfgEvseV2.getDayVolume());
        cfgEvse.setNightVolume(cfgEvseV2.getNightVolume());
        cfgEvse.setStopMode(cfgEvseV2.getStopMode());
        cfgEvse.setCfgVer(cfgEvseV2.getCfgVer());

        cfgEvse.setStopMode(cfgEvseV2.getStopMode());
        // convert white card: v2 -> v1
        if (cfgEvseV2.getWhiteCards() != null && !cfgEvseV2.getWhiteCards().isEmpty()) {
            cfgEvse.setWhiteCards(cfgEvseV2.getWhiteCards().stream().map(e -> {
                WhiteCard whiteCard = new WhiteCard();
                whiteCard.setCardNumber(e.getCardNumber());
                whiteCard.setPasscode(e.getPasscode());
                return whiteCard;
            }).collect(Collectors.toList()));
        }

        // convert price list: v2 -> v1
        if (cfgEvseV2.getPrice() != null && !cfgEvseV2.getPrice().isEmpty()) {
            Charge charge = new Charge();
            charge.setChargeId(cfgEvseV2.getPriceCode().longValue());
            // 此处的服务非单价使用计费方案的第一个元素的服务费
            charge.setServPrice(
                cfgEvseV2.getPrice().get(0).getServPrice().movePointRight(2).longValue());
            charge.setDefaultCode(cfgEvseV2.getPrice().get(0).getCode());

            charge.setPriceSchemaList(cfgEvseV2.getPrice().stream().map(e -> {
                PriceSchema priceSchema = new PriceSchema();
                priceSchema.setCode(e.getCode());
                priceSchema.setElecPrice(e.getElecPrice().movePointRight(4).intValue());
                return priceSchema;
            }).collect(collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparing(pc -> pc.getCode()))),
                ArrayList::new)));

            charge.setTimeList(cfgEvseV2.getPrice().stream().map(e -> {
                CfgTime cfgTime = new CfgTime();
                cfgTime.setCode(e.getCode());
                cfgTime.setStartTime(e.getStartTime());
                cfgTime.setStopTime(e.getStopTime());
                return cfgTime;
            }).collect(Collectors.toList()));

            cfgEvse.setCharge(charge);
        }

        return cfgEvse;

    }

    public CfgEvseV2 getV2(String redisId, String cfgVer) {
        //TODO redis transactional

        final String key = redisId;
        Long size = redisUtil.sizeList(key);
        logger.info("redis list size: {}", size);

        while (size > 0) {
            String evseCfgJson = redisUtil.leftPopList(key);
            if (evseCfgJson == null) {
                logger.warn("evseCfgJson is null. redisId = {}, cfgVer = {}", redisId, cfgVer);
                return null;
            }
            ObjectMapper mapper = new ObjectMapper();
            CfgEvseV2 actualObj = null;
            try {
                actualObj = mapper.readValue(evseCfgJson, CfgEvseV2.class);
                if (cfgVer.equalsIgnoreCase(actualObj.getCfgVer())) {
                    logger.info("redis list size: {}", redisUtil.sizeList(key));
                    return actualObj;
                } else {
                    appendV2(key, actualObj);
                }
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
                if (actualObj != null) {
                    appendV2(key, actualObj);
                } else {
                    logger.warn("redis null json detected: {}", evseCfgJson);
                }
            }
            size--;
        }

        return null;
    }

    public CfgEvseAllV2 getCfgEvseAll(String redisId) {
        logger.info("getCfgEvseAll. redisId: {}", redisId);

        final String key = redisId;
        Long size = redisUtil.sizeList(key);
        logger.info("getCfgEvseAll. redisId: {}, redis list size: {}", redisId, size);

        while (size > 0) {
            String evseCfgJson = redisUtil.leftPopList(key);
//            ObjectMapper mapper = new ObjectMapper();
//            CfgEvseAllV2 cfgEvseAllV2 = null;
//            try {
//                cfgEvseAllV2 = mapper.readValue(evseCfgJson, CfgEvseAllV2.class);
//                return cfgEvseAllV2;
//            } catch (IOException e) {
//                logger.error(e.getMessage(), e);
//                if (cfgEvseAllV2 != null) {
//                    appendCfgEvseAllV2(key, cfgEvseAllV2);
//                } else {
//                    logger.warn("redis null json detected: {}", evseCfgJson);
//                }
//            }
            try {
                return JsonUtils.fromJson(evseCfgJson, CfgEvseAllV2.class);
            } catch (RuntimeException e) {
                logger.warn("反序列化异常: {}", evseCfgJson);
            }
            size--;
        }

        return null;
    }

    /**
     * 桩注册后配置下发功能是否启用
     *
     * @return
     */
    public boolean configDeliveredEnabled() {
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(EVSE_REGISTER_CONFIG_SWITCH))) {
            return Boolean.parseBoolean(
                stringRedisTemplate.opsForValue().get(EVSE_REGISTER_CONFIG_SWITCH));
        } else {
            return true;
        }
    }

    /**
     * 判断桩是否可执行注册后桩配置相关操作
     *
     * @param evseNo
     * @return
     */
    public boolean configCanBeDelivered(String evseNo) {
        boolean deliverOrNot = true;
        if (Boolean.TRUE.equals(
            stringRedisTemplate.opsForHash().hasKey(EVSE_REGISTER_CONFIG_LIMITED, evseNo))) {
            try {
                Object obj = stringRedisTemplate.opsForHash()
                    .get(EVSE_REGISTER_CONFIG_LIMITED, evseNo);
                if (obj != null) {
                    long currentTimeMillis = System.currentTimeMillis();
                    long limitTimestamp = Long.parseLong(obj.toString());

                    long msec = REGISTER_LIMIT_MINUTES * 60 * 1000; // 分钟转毫秒
                    if ((currentTimeMillis - limitTimestamp) < msec) {
                        deliverOrNot = false;
                    } else {
                        this.delFromLimited(evseNo);
                    }
                }
            } catch (Exception ex) {
                log.error("configCanBeDelivered error: {}", ex.getMessage(), ex);
            }
        }
        return deliverOrNot;
    }

    /**
     * 桩加入限制集合
     *
     * @param evseNo
     */
    public void addToLimited(String evseNo) {
        stringRedisTemplate.opsForHash()
            .put(EVSE_REGISTER_CONFIG_LIMITED, evseNo, String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 桩移出限制集合
     *
     * @param evseNo
     */
    public void delFromLimited(String evseNo) {
        if (Boolean.TRUE.equals(
            stringRedisTemplate.opsForHash().hasKey(EVSE_REGISTER_CONFIG_LIMITED, evseNo))) {
            stringRedisTemplate.opsForHash().delete(EVSE_REGISTER_CONFIG_LIMITED, evseNo);
        }
    }

}
