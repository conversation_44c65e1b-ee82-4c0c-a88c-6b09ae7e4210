package com.cdz360.iot.worker.ds.mapper;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.iot.model.evse.po.PlugPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface PlugMapper {

    int batchInsert(@Param("plugs") List<PlugPo> plugs);

    int update(PlugPo plugPo);

    int batchUpdate(List<PlugPo> plugPos);

    int updateOrderNo(@Param("evseId") String evseId, @Param("plugId") int plugId, @Param("orderNo") String orderNo);

    int batchUpdateOrderNo(PlugPo plugPo);

    PlugPo getPlug(@Param("evseId") String evseId, @Param("plugId") int plugId,
                   @Param("lock") boolean lock);

    List<PlugPo> getPlugByEvseId(@Param("evseId") String evseId, @Param("lock") boolean lock);

    List<PlugPo> listPlug(@Nullable @Param("evseId") String evseId,
                          @Nullable @Param("statusList") List<PlugStatus> statusList,
                          @Nullable @Param("start") Long start,
                          @Nullable @Param("size") Integer size,
                          @Param("lock") boolean lock);

    int removeByEvseId(@Param("evseId") String evseId);

    //List<PlugVo> getPlugList(ListPlugParam param);
}
