package com.cdz360.iot.worker.model.gw;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.base.BaseObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LoginRes extends BaseObject {

    @JsonProperty(value = "n")
    private String gwno;

    private String mqttUrl;
    private String mqttClientId;
    private String mqttUsername;
    private String mqttPassword;
    private String mqttTopic;
    private String mqttLwt; // last will topic

//    public String getGwno() {
//        return gwno;
//    }
//
//    public LoginRes setGwno(String gwno) {
//        this.gwno = gwno;
//        return this;
//    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
