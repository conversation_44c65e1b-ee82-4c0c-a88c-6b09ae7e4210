package com.cdz360.iot.worker.model.iot;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.EvseReportRequest;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.model.site.po.SitePo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UpdatePlugCacheDto {

    private IotEvent event;

    private EvseReportRequest.Plug plugReport;

    private SitePo site;

    private EvsePo evse;

    private PlugPo plug;

    private Boolean constantCharge;
}
