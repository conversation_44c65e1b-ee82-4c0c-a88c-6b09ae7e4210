package com.cdz360.iot.worker.biz;

import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.worker.ds.service.PlugService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PlugBatchHandler implements BatchHandler<PlugPo> {

    private final Logger logger = LoggerFactory.getLogger(PlugBatchHandler.class);

    @Autowired
    private PlugService plugService;

    @Override
    public void save(List<PlugPo> list) {
        logger.info("枪状态同步到数据库。" +
                "list = {}", list.stream().collect(Collectors.toMap(PlugPo::getId, PlugPo::getPlugStatus, (o, n) -> n)));
        if(CollectionUtils.isNotEmpty(list)) {
            plugService.batchUpdate(list);
        }
    }
}
