package com.cdz360.iot.worker.biz.north;

import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisChargeOrderReadService;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.model.evse.OrderCmdRequestV2;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.worker.biz.GwCacheService;
import com.cdz360.iot.worker.biz.RedisIotUpdateWrapper;
import com.cdz360.iot.worker.ds.mapper.PlugMapper;
import com.cdz360.iot.worker.ds.service.EvseService;
import com.cdz360.iot.worker.ds.service.OrderService;
import com.cdz360.iot.worker.feign.BizTradingFeignClient;
import com.cdz360.iot.worker.model.order.CreateOrderRequest;
import com.cdz360.iot.worker.model.order.OrderLimitSocRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 云端订单处理服务
 */
@Slf4j
@Service
public class OrderNorthBizService {


    @Autowired
    private OrderService orderService;

    @Autowired
    private EvseService evseService;

    @Autowired
    private SequenceRwService sequenceRwService;


    @Autowired
    private MqService mqService;

    @Value("${iot.fee.min}")
    private BigDecimal IOT_FEE_MIN;//充电启动金额
    //即充即退最小充值金额（分)
    @Value("${iot.fee.prepayMin:100}")
    private BigDecimal prepayMinAmount;

    @Autowired
    private PlugMapper plugMapper;

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private RedisChargeOrderReadService redisChargeOrderReadService;


    @Autowired
    private GwCacheService gwCacheService;

    @Autowired
    private BizTradingFeignClient bizTradingReactiveFeignClient;

    //@Transactional(propagation = Propagation.SUPPORTS)
    public String createOrder(CreateOrderRequest req) {
        log.info("createOrder. orderNo = {}", req.getOrderNo());
        if (req.getDefaultPayType() != null && req.getDefaultPayType().intValue() == PayAccountType.PREPAY.getCode()) {
            IotAssert.isTrue(req.getFrozenAmount().compareTo(prepayMinAmount) >= 0,
                    "冻结金额必须大于即充即退最小金额");
        } else {
            IotAssert.isTrue(req.getFrozenAmount().compareTo(IOT_FEE_MIN) >= 0,
                    "冻结金额必须大于启动金额");
        }


        log.debug("获取桩信息。 evseId: {}", req.getEvseNo());
        var evse = redisIotReadService.getEvseRedisCache(req.getEvseNo());
        log.debug("获取桩信息。 evse: {}", evse);
        IotAssert.isNotNull(evse, "充电桩编号参数错误,桩编号 " + req.getEvseNo() + " 未配置");

        PlugVo plugCache = redisIotReadService.getPlugRedisCache(req.getPlugNo());
        log.info("枪头信息: {}", plugCache);
        IotAssert.isNotNull(plugCache, "充电终端未在线，请检查");
        if (plugCache.getStatus() == PlugStatus.OFFLINE) {
            throw new DcServiceException("充电终端已经离线，请检查");
        } else if (plugCache.getStatus() == PlugStatus.BUSY
                && StringUtils.equals(plugCache.getOrderNo(), req.getOrderNo())) {
            log.info("<< 枪头已在充电中");
            return evse.getGwno();
        } else if (Boolean.FALSE.equals(plugCache.getConnSupport())) {
            // 二代桩不支持插枪状态，放过校验
        } else if (plugCache.getStatus() == PlugStatus.RECHARGE_END && Boolean.TRUE.equals(plugCache.getConstantCharge())) {
            // 二次充电不校验枪状态、枪头订单
            this.redisIotUpdateWrapper.unbindOrderNo(plugCache.getEvseNo(), plugCache.getIdx());
        } else if (plugCache.getStatus() != PlugStatus.CONNECT) {
            throw new DcServiceException("枪状态异常（不是空占状态），请检查");
        }
        try {
            req.setPlugId(plugCache.getIdx() == null ? req.getPlugId() : plugCache.getIdx());
        } catch (Exception e) {
            log.warn("缓存中没有idx 且 plugNo获取不到idx");
        }
        // 回写枪头正在充电中订单号
        if (StringUtils.isNotBlank(plugCache.getOrderNo()) && !req.getOrderNo().equals(plugCache.getOrderNo())) {
            log.info("云端发起充电异常订单。plugPo==>{},req=={}",
                    JsonUtils.toJsonString(plugCache), req.toJsonString());
            // 该枪可能存在异常订单
            //BaseResponse res = dcBChargerOrderClient.handleErrorOrder(plugCache.getOrderNo(), null);
            // IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, res.getError());
            this.bizTradingReactiveFeignClient.handleErrorOrder(plugCache.getOrderNo(), null)
                    .subscribe(res -> {
                        log.info("云端发起充电异常订单处理完成。OrderNo = {}, res = {}", plugCache.getOrderNo(), res);
                    });

        }
        this.redisIotUpdateWrapper.bindOrderNo(req.getEvseNo(), req.getPlugId(), req.getOrderNo());

        this.sendCreateOrderMq(req, evse); // 发送mq消息

        log.info("createOrder. gwno = {}, orderNo = {}", evse.getGwno(), req.getOrderNo());
        return evse.getGwno();
    }


    private void sendCreateOrderMq(CreateOrderRequest req, EvseVo evse) {
        GwInfoPo gwInfo = gwCacheService.getGwInfo(evse.getGwno());
        IotAssert.isNotNull(gwInfo, "找不到对应的网关, gwno: " + evse.getGwno());
        if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
            throw new DcServiceException("废弃的逻辑", Level.ERROR);
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2) {
            IotGwCmdCacheVo<CreateOrderRequest> cmd = new IotGwCmdCacheVo<>();
            cmd.setGwno(evse.getGwno())
                    .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                    .setCmd(IotGwCmdType2.CE_CHARGE_START)
                    .setEvseNo(req.getEvseNo())
                    .setPlugId(req.getPlugId())
                    .setData(req);  // 有部分字段冗余, 后续版本可以去掉
            //this.mqttService.publishMessage(evse.getGwno(), JsonUtils.toJsonString(cmd));

            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            this.redisIotUpdateWrapper.addIotGwCmd(cmd);    // 把下行指令放到 redis 缓存
        } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
            IotGwCmdCacheVo<CreateOrderRequest> cmd = new IotGwCmdCacheVo<>();

            String orderStopCode = redisChargeOrderReadService.getOrderStopCodeString(req.getOrderNo());
            if (StringUtils.isNotBlank(orderStopCode)) {
                // 下发停充码
                req.setOrderStopCode(orderStopCode);
            }

            cmd.setGwno(evse.getGwno())
                    .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                    .setCmd(IotGwCmdType2.CE_CHARGE_START)
                    .setEvseNo(req.getEvseNo())
                    .setPlugId(req.getPlugId())
                    .setData(req);  // 有部分字段冗余, 后续版本可以去掉
            mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
            this.redisIotUpdateWrapper.addIotGwCmd(cmd);    // 把下行指令放到 redis 缓存
        } else {
            throw new DcServerException("不支持的网关协议版本: " + gwInfo.getVer() + ", gwno = " + gwInfo.getGwno());
        }
    }


    // @Transactional
    public String stopOrder(String evseNo, String plugNo, String orderNo) {
        log.info("stopOrder. orderNo = {}", orderNo);

        var plug = redisIotReadService.getPlugRedisCache(plugNo);

        log.info("获取桩信息。  plug: {}", plug);

        IotAssert.isNotNull(plug, "该订单对应的桩: " + evseNo + " 不存在");
        if (plug.getStatus() == PlugStatus.OFFLINE) {
            log.warn("桩异常或已离线，若要继续充电请等待，否则请在桩端点击“停止”按钮。 evseNo = {}", evseNo);
            throw new DcServiceException("桩异常或已离线，若要继续充电请等待，否则请在桩端点击“停止”按钮。", Level.WARN);
        }


        IotGwCmdCacheVo<OrderCmdRequestV2> cmd = new IotGwCmdCacheVo<>();
        OrderCmdRequestV2 orderCmdRequestV2 = new OrderCmdRequestV2();
        orderCmdRequestV2.setEvseNo(evseNo);
        orderCmdRequestV2.setPlugId(plug.getIdx());
        orderCmdRequestV2.setOrderNo(orderNo);
        cmd.setGwno(plug.getGwno())
                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                .setCmd(IotGwCmdType2.CE_CHARGE_STOP)
                .setData(orderCmdRequestV2);  // 有部分字段冗余, 后续版本可以去掉
        GwInfoPo gwInfo = this.gwCacheService.getGwInfo(plug.getGwno());
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
        log.info("<< v2 stopOrder. ");

        return plug.getGwno();
    }

    public String changeLimitSoc(OrderLimitSocRequest req) {
        IotAssert.isNotBlank(req.getOrderNo(), "请传入订单编号");
        IotAssert.isNotNull(req.getLimitSoc(), "请传入soc限制值");
        IotAssert.isNotNull(req.getPlugNo(), "请传入枪头编号");

        PlugVo plugCache = redisIotReadService.getPlugRedisCache(req.getPlugNo());

        IotGwCmdCacheVo<OrderLimitSocRequest> cmd = new IotGwCmdCacheVo<>();
        OrderCmdRequestV2 orderCmdRequestV2 = new OrderCmdRequestV2();
        orderCmdRequestV2.setEvseNo(plugCache.getEvseNo());
        orderCmdRequestV2.setPlugId(plugCache.getIdx());
        orderCmdRequestV2.setOrderNo(req.getOrderNo());

        req.setEvseNo(plugCache.getEvseNo());
        req.setPlugId(plugCache.getIdx());
        cmd.setGwno(plugCache.getGwno())
                .setSeq(this.sequenceRwService.getNextOutRequestSeq())
                .setCmd(IotGwCmdType2.CE_CHARGE_SOC_CTRL)
                .setData(req);
        GwInfoPo gwInfo = this.gwCacheService.getGwInfo(plugCache.getGwno());
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
        return cmd.getSeq();
    }


}
