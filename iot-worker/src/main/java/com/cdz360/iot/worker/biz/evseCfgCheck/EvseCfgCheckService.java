package com.cdz360.iot.worker.biz.evseCfgCheck;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.ds.ro.EvseRoDs;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.model.iot.DelayedEvse;
import java.util.List;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseCfgCheckService {

    private DelayQueue<DelayedEvse> delayQueue = new DelayQueue<>();

    @Autowired
    private EvseRoDs evseRoDs;
    @Autowired
    private BusinessService businessService;
    @Autowired
    private EvseCfgCheckHandle evseCfgCheckHandle;

    public BaseResponse checkEvseCfg() {
        AtomicLong delayTime = new AtomicLong(60 * 1000); // 1分钟后队列开始消费（等待桩端上报配置信息）

        boolean hasNext = true;
        long start = 0;
        final int size = 200;

        do {
            List<String> evseNoList = evseRoDs.getNeedCheckCfgEvse(IotConstants.superTopCommId, start, size);
            if (CollectionUtils.isNotEmpty(evseNoList)) {

                try {
                    businessService.evseGetCfg(evseNoList, Boolean.FALSE); // 下发获取桩配置指令

                    delayQueue.addAll(evseNoList.stream().map(e -> new DelayedEvse(e, delayTime.get()))
                            .collect(Collectors.toList()));

                } catch (Exception e) {
                    log.warn("checkEvseCfg error: {}", e.getMessage(), e);
                }
            }

            start = start + size;
            if (evseNoList.size() < size) {
                hasNext = false;
            }
        } while (hasNext);

        log.info("delayQueue.size: {}", delayQueue.size());
        if (delayQueue.size() < 1) {
            log.info("没有可获取配置的桩");
            return RestUtils.success();
        }
        DelayedQueueConsumer consumer = new DelayedQueueConsumer(delayQueue, evseCfgCheckHandle);
        new Thread(consumer).start();

        return RestUtils.success();
    }

}
