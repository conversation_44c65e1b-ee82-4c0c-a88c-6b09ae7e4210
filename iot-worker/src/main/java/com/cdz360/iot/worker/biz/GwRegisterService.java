package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.exception.DcGwException;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.model.register.GwRegisterResult;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.type.GwStatus;
import org.apache.commons.lang.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class GwRegisterService {
    private final Logger logger = LoggerFactory.getLogger(GwRegisterService.class);

    @Autowired
    private GwInfoService gwInfoService;

    @Autowired
    private SequenceRwService sequenceRwService;

//    @Autowired
//    private BaiduService baiduService;

//    @Autowired
//    private GwInfoClientService gwInfoClientService;

    // @Autowired
    // private GwInfoClient gwInfoClient;

    @Value("${iot.gw.idle.gwno}")
    private String idleGwno;
    @Value("${iot.gw.idle.passcode}")
    private String idlePasscode;
//    @Value("${iot.gw.idle.realm}")
//    private String idleRealm;

    /**
     * 网关初始化
     *
     * @param gwno
     * @param mac
     * @param ip
     * @param wanIp
     * @param delay 延迟执行地理信息获取，若大于0，将延迟响应的毫秒数异步执行，否则同步执行
     * @return
     */
    @Transactional
    public GwRegisterResult doRegister(String gwno, String mac, String ip, String wanIp, int ver, long delay) {
        logger.info("doRegister. gwno = {}, mac = {}, ip = {}, wanIp = {}", gwno, mac, ip, wanIp);

        GwRegisterResult ret = new GwRegisterResult();

        logger.info("默认网关编号: {}, 收到的网关: {}", idleGwno, gwno);
        if (idleGwno.equals(gwno)) {
            // 出厂默认网关编号

            List<GwInfoDto> gwInfoPos = gwInfoService.getByMac(macTrim(mac), true);
            logger.info("get gw by mac: {}", gwInfoPos);
            if (gwInfoPos.isEmpty()) {
                // 数据库未匹配到MAC地址
                ret = createNewGw(mac, ip, wanIp, ver, delay);
            } else if (gwInfoPos.size() > 1) {
                logger.error("查找到多个网关: {}", mac);
                throw new DcGwException(String.format("查找到多个网关: mac: [%s]", mac));
            } else {
                // 返回已存在的网关
                GwInfoDto gwInfoDto = gwInfoPos.get(0);
                if (gwInfoDto.getStatus() == GwStatus.NORMAL) {
                    logger.error("网关当前在线，发送告警");
                    throw new DcGwException(String.format("网关当前在线: gwno: [%s]", gwInfoDto.getGwno()));
                } else {
                    ret.setGwno(gwInfoDto.getGwno()).setPasscode(gwInfoDto.getPasscode());
                }
            }
            logger.info("gw info size: {}", gwInfoPos.size());
            return ret;
        } else {
            GwInfoDto gwInfoDto = gwInfoService.getByGwno(gwno, false);
            if (gwInfoDto == null) {
                logger.error("未匹配到网关编号: {}，发送告警", gwno);
                throw new DcGwException(String.format("未匹配到网关编号: [%s]", gwno));
            } else if (mac.equalsIgnoreCase(gwInfoDto.getMac())) {
                ret.setGwno(gwInfoDto.getGwno()).setPasscode(gwInfoDto.getPasscode());
            } else {
                ret = createNewGw(mac, ip, wanIp, ver, delay);
                logger.error("网关编号已存在: {}，生成新的网关编号: {}，发送告警", gwno, ret.getGwno());
            }
            return ret;
        }
    }

    private GwRegisterResult createNewGw(String mac, String ip, String wanIp, int ver, long delay) {
        logger.info("createNewGw. mac: {}, ip: {}, wanIp: {}, delay: {}", mac, ip, wanIp, delay);

        GwRegisterResult ret = new GwRegisterResult();
        GwInfoPo gwInfoPo = new GwInfoPo();
        gwInfoPo.setIp(wanIp).
                setCityCode("").
                setGwno(sequenceRwService.getNextGwno()).
                setMqType(GwMqType.MQ_TYPE_RABBITMQ).
                setEnable(true).
                setLanIp(ip).
                setMac(macTrim(mac)).
                setPasscode(RandomStringUtils.randomAlphabetic(16))
//                setSiteId(0L)
                .setVer(ver);

        int ins = gwInfoService.insert(gwInfoPo);
        if (ins > 0) {
            //异步确定网关地理信息
            //asyncUpdateLocation(gwInfoPo.getGwno(), gwInfoPo.getIp(), delay);

            ret.setGwno(gwInfoPo.getGwno()).setPasscode(gwInfoPo.getPasscode());
        } else {
            logger.error("网关入库失败: {}", gwInfoPo);
            throw new DcGwException("网关入库失败: " + gwInfoPo.getGwno());
        }
        return ret;
    }


    private String macTrim(String mac) {
        return mac.toUpperCase();//mac.replaceAll("-", "").toUpperCase();
    }
}
