package com.cdz360.iot.worker.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.worker.biz.evseCfgCheck.EvseCfgCheckService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "桩配置检查相关接口", description = "桩配置检查")
public class EvseCfgCheckRest {

    @Autowired
    private EvseCfgCheckService service;

    @GetMapping(value = "/iot/evseCfgCheck/start")
    public BaseResponse checkEvseCfg() {
        log.info("checkEvseCfg start");
        return service.checkEvseCfg();
    }

}
