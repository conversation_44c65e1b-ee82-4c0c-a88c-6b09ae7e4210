package com.cdz360.iot.worker.biz;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class QueueConsumer<T> implements Runnable {

    private IotQueue<T> queue;
    private List<T> list;
    private BatchHandler<T> batchHandler;

    QueueConsumer(IotQueue<T> queue, BatchHandler<T> batchHandler) {
        this.queue = queue;
        this.batchHandler = batchHandler;
        this.list = new ArrayList<>();
    }

    @Override
    public void run() {
        do {
            try {
                this.threadLoop();
            } catch (Exception e) {
                log.warn("error = {}", e.getMessage(), e);
            }
        } while (true);
    }

    private void threadLoop() throws Exception {

        T pl = queue.poll(1, TimeUnit.SECONDS);
        if (pl == null) {
            return;
        }
        list.clear();
        list.add(pl);
        this.pollMore(list);

        batchHandler.save(list);
    }

    private void pollMore(List<T> list) {
        T pl = null;
        int max = 100;
        do {
            pl = queue.poll();
            if (pl == null) {
                break;
            }
            list.add(pl);
            max--;
        } while (max > 0);
    }
}
