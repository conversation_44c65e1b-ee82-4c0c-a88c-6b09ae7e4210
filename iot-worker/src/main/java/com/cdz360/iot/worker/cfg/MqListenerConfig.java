package com.cdz360.iot.worker.cfg;

import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.iot.common.base.IotConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {


    @Bean
    public Queue iotCmdTimeoutQueue() {
        return new Queue(IotConstants.IOT_GW_CMD_TIMEOUT_QUEUE_NAME, true, false, false);
    }

    @Bean
    public DirectExchange exchangeIotCmd() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD, true, false);
    }

    @Bean
    public Binding bindingExchangeIotCmdTimeout(Queue iotCmdTimeoutQueue, DirectExchange exchangeIotCmd) {

        return BindingBuilder.bind(iotCmdTimeoutQueue).to(exchangeIotCmd).with(DcMqConstants.MQ_ROUTING_KEY_GW_CMD_TIMEOUT);
    }
}
