package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.site.param.GwParam;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.worker.model.iot.po.GwLogPo;
import com.cdz360.iot.worker.type.MqttEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.cdz360.iot.common.base.IotConstants.MQTT_TMP_LOG_DELAY;

@Service
public class GwStatusProcessor {
    private final Logger logger = LoggerFactory.getLogger(GwStatusProcessor.class);

    @Autowired
    private GwTmpLogService gwTmpLogService;

    @Autowired
    private GwInfoService gwInfoService;

    @Transactional
    public void updateGwStatus(GwParam param) {

        logger.info("updateGwStatus. param: {}", param);

        List<GwLogPo> gwLogPoList = gwTmpLogService.getLastTmpLog(param.getGwno());

        if (!gwLogPoList.isEmpty()) {

            if (gwLogPoList.size() != 1) {
                logger.error("多个最新的记录 {}", gwLogPoList);
                //                throw new IotArgumentException("多个最新的记录");
            }

            GwLogPo gwLogPo = gwLogPoList.get(0);

            MqttEventType eventType;

            try {
                eventType = MqttEventType.valueOf(gwLogPo.getEventType().toUpperCase());
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                throw new DcArgumentException(String.format("无法转换mqtt日志类型: %s", gwLogPo.getEventType()));
            }

            logger.info("GW status: " + gwLogPo.getEventType());

            switch (eventType) {
                case CONNECT:
                    // 更新网关状态为上线
                    // TODO 目前没有地方告警，更新网关信息表
                    gwInfoService.updateStatus(gwLogPo.getGwno(), GwStatus.NORMAL);
                    logger.error("网关[{}]上线, 此处可能需要推送更新信息, 发送告警", gwLogPo.getGwno());
                    gwTmpLogService.copyOut(gwLogPo.getGwno(), gwLogPo.getTime());
                    gwTmpLogService.deleteBefore(gwLogPo.getGwno(), gwLogPo.getTime());
                    break;
                case DISCONNECT:
                case TCPCLEAN:
                    if (System.currentTimeMillis() - gwLogPo.getTime() > MQTT_TMP_LOG_DELAY) {
                        // 超过30秒，更新网关状态为离线
                        // TODO 目前没有地方告警，更新网关信息表
                        gwInfoService.updateStatus(gwLogPo.getGwno(), GwStatus.OFFLINE);
                        logger.error("网关[{}]离线, 此处可能需要推送更新信息, 发送告警", gwLogPo.getGwno());
                        gwTmpLogService.copyOut(gwLogPo.getGwno(), gwLogPo.getTime());
                        gwTmpLogService.deleteBefore(gwLogPo.getGwno(), gwLogPo.getTime());
                    } else {
                        // 不超过30秒，继续保留在临时表
                    }
                    break;
            }
        } else {
            logger.info("没有新的临时日志记录");
        }
        logger.info("<<");
    }
}
