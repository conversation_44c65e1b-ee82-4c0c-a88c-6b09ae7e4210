package com.cdz360.iot.worker.rest.internal;

import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.ListRpcResponse;
import com.cdz360.iot.model.site.param.GwParam;
import com.cdz360.iot.model.site.param.ListGwLogParam;
import com.cdz360.iot.worker.biz.GwStatusProcessFacade;
import com.cdz360.iot.worker.biz.GwTmpLogService;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.gw.GwTimeoutPo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Tag(name = "网关状态监控相关接口", description = "监控服务")
@RequestMapping("/iot/monitor")
public class GwStatusMonitorController {
    private final Logger logger = LoggerFactory.getLogger(GwStatusMonitorController.class);

    @Autowired
    private GwStatusProcessFacade gwStatusProcessFacade;

    @Autowired
    private GwTmpLogService gwTmpLogService;

    @Autowired
    private GwInfoService gwInfoService;

    @Operation(summary = "获取网关列表接口")
    @PostMapping(value = "/listgwno", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @Deprecated
    public ListRpcResponse<String> listgwno(
            @Parameter(name = "获取场站列表参数") @RequestBody ListGwLogParam param) {
        logger.info("获取网关列表接口。param = {}", param);
        List<String> gwList = gwTmpLogService.getGroupGwno();
        ListRpcResponse<String> res = new ListRpcResponse<>(gwList);
        return res;
    }

    @Operation(summary = "通知更新网关状态")
    @PostMapping(value = "/notify", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseRpcResponse notifi(@RequestBody GwParam param) {
        logger.info("通知更新网关状态。param = {}", param);
        BaseRpcResponse res = BaseRpcResponse.newInstance();
//        gwTmpLogService.getLastTmpLog(param);
        gwStatusProcessFacade.updateGwStatus(param);
        return res;
    }

    @Operation(summary = "获取所有登陆超时网关列表")
    @PostMapping(value = "/listGwTimeout", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ListRpcResponse<GwTimeoutPo> listGwTimeout(@Parameter(name = "网关登陆超时时间（分钟）")Integer timeout) {
        logger.info("获取所有登陆超时网关列表【开始】");
        // 超时时间为空默认70分钟
        timeout = timeout == null ? 70 : timeout;
        List<GwTimeoutPo> gwTimeoutPos = gwInfoService.listGwTimeout(timeout);
        ListRpcResponse<GwTimeoutPo> res = new ListRpcResponse<>(gwTimeoutPos);
        logger.info("获取所有登陆超时网关列表【结束】。gwTimeoutPos = {}",gwTimeoutPos);
        return res;
    }

}
