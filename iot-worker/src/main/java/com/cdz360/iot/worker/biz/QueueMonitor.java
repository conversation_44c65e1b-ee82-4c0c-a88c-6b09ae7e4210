package com.cdz360.iot.worker.biz;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class QueueMonitor {
    List<IotQueue> queueList = new ArrayList<>();

    public void addQueue(IotQueue q) {
        this.queueList.add(q);
    }

    public List<QueueInfo> dump() {
        List<QueueInfo> list = new ArrayList<>();
        queueList.forEach(q -> {
            QueueInfo info = new QueueInfo();
            info.setName(q.name())
                    .setCurSize(q.size());
            list.add(info);
        });
        return list;
    }
}
