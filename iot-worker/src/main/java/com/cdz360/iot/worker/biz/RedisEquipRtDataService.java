package com.cdz360.iot.worker.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.ess.dto.RedisMgcRtData;
import com.cdz360.iot.worker.model.gw.MgcAlert;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * @Classname RedisEquipRtDataService
 * @Description
 * @Date 10/14/2021 6:42 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class RedisEquipRtDataService {
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRE_MGC_REDIS_KEY = "mgc:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 微网控制器信息压入
     * @param gwno
     * @param data
     */
    public void pushMgcData(String gwno, MgcAlert data) {
        String key = formatMgcKey(gwno, LocalDate.now());

        redisTemplate.opsForList()
                .rightPush(key, JsonUtils.toJsonString(new RedisMgcRtData<MgcAlert>()
                        .setData(data)
                        .setTime(LocalDateTime.now())));
        redisTemplate.expire(key, 2, TimeUnit.DAYS); // 近2 * 24h可用
    }

    public RedisMgcRtData<MgcAlert> gwSrcInfo(String gwno) {
        return JsonUtils.fromJson(this.dateRtData(gwno, LocalDate.now()), new TypeReference<>() {
        });
    }

    public String dateRtData(String gwno, LocalDate date) {
        String key = formatMgcKey(gwno, date);
        return this.dateRtData(key);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param key redis中key
     * @return
     */
    public String dateRtData(String key) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return value;
    }

    private static String formatMgcKey(String gwno, LocalDate date) {
        return PRE_MGC_REDIS_KEY + gwno + ":" + date.format(DATE_FORMATTER);
    }
}