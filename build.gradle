plugins {
    id 'org.springframework.boot' version '3.2.9'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'java'
    id 'jacoco'
    id 'com.github.kt3k.coveralls' version '2.8.2'
    id "com.gorylenko.gradle-git-properties" version "2.3.2"
    id "org.sonarqube" version "3.5.0.2730"
}

ext {
    dcCloudVersion = "20250820_1049_DEV-SNAPSHOT"
    springCloudVersion = "2023.0.3"
//	swaggerVersion = '3.0.0'
    springdocVersion = '1.6.15'
    jacksonVersion = "2.17.2"
    lombokVersion = '1.18.34'
    mysqlConnectorVersion = '8.0.11'    // mysql:mysql-connector-java
    mybatisSpringVersion = '3.0.3'  // org.mybatis.spring.boot:mybatis-spring-boot-starter
    commonsPoolVersion = '2.11.1'
    commonsIoVersion = '2.11.0'
    //apache commons pool. redis pool 需要 https://commons.apache.org/proper/commons-pool/
    zip4jVersion = "1.3.2"
    validationApiVersion = "2.0.1.Final"
    pagehelperVersion = '5.1.6'
    pageHelperStarterVersion = '2.1.0'
    feignReactiveVersion = '4.2.1'
    mybatisJsr310Version = '1.0.2'
    okhttpVersion = '3.8.1'
    junitVersion = '5.9.1'
    hutoolVersion= '5.8.30'
}

apply plugin: 'idea'
//apply plugin: 'eclipse'


allprojects {
    group = 'com.cdz360.iot'
    version = '0.0.1-SNAPSHOT'
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    //apply plugin: 'maven'
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17

    compileJava {
        options.encoding = "UTF-8"
    }
    processResources {
        duplicatesStrategy = DuplicatesStrategy.INCLUDE
    }

    test.onlyIf { !Boolean.getBoolean('skipTests') }

    repositories {
        maven {
            url 'https://repo.iot.renwochong.com/repository/maven-snapshots/'
            mavenContent {
                snapshotsOnly()
            }
        }
        maven { url 'https://maven.aliyun.com/repository/central' }
        //maven { url 'https://maven.aliyun.com/repository/jcenter' }
        //jcenter()
        mavenCentral()
    }

    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }

}

sonarqube {
    properties {
        property "sonar.sourceEncoding", "UTF-8"
    }
}

subprojects {

    apply plugin: 'jacoco'
    apply plugin: 'maven-publish'

//    task javadocJar(type: Jar) {
//        archiveClassifier = 'javadoc'
//        from javadoc
//    }
//
//    task sourcesJar(type: Jar) {
//        archiveClassifier = 'sources'
//        from sourceSets.main.allSource
//    }

    java {
        withJavadocJar()
        withSourcesJar()
    }

    javadoc {
        failOnError = false
    }

    tasks.sourcesJar {
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }

//    tasks.named("sourcesJar") {
//        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
//    }

    artifacts {
        archives javadocJar, sourcesJar
    }

    jacocoTestReport {
        reports {
            xml.required = true
            //xml.destination file("${buildDir}/jacocoXml")
        }
    }

    test.finalizedBy jacocoTestReport

    test {
        useJUnitPlatform()
    }
    dependencies {

        implementation("com.cdz360.cloud:dc-base-model:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-base-utils:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-data-cache:${dcCloudVersion}")
        //       implementation("com.cdz360.cloud:dc-data-cache-reader:${dcCloudVersion}")

        implementation('org.springframework.boot:spring-boot-starter-amqp')
        //implementation("org.springframework.boot:spring-boot-starter-data-rest:${springBootVersion}")
        //implementation("org.springframework.boot:spring-boot-starter-data-redis:${springBootVersion}")

        //implementation('org.springframework.boot:spring-boot-starter-security')
        //implementation('org.springframework.cloud:spring-cloud-bus')
        //implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')
        implementation('net.logstash.logback:logstash-logback-encoder:8.0')
        //implementation('ch.qos.logback:logback-classic:1.2.3')
        implementation('com.fasterxml.jackson.core:jackson-core')
        implementation('com.fasterxml.jackson.core:jackson-annotations')
        implementation('com.fasterxml.jackson.core:jackson-databind')
        //implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:${jacksonVersion}")
//        implementation("io.springfox:springfox-core:${swaggerVersion}")
        //implementation("io.springfox:springfox-swagger2:${swaggerVersion}")
//        implementation('io.swagger:swagger-annotations:1.6.2')
        implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")
        implementation("org.apache.commons:commons-lang3:3.7")
        implementation("com.alibaba.mq-amqp:mq-amqp-client:1.0.3")
        implementation("javax.validation:validation-api:${validationApiVersion}")

        compileOnly group: 'com.google.code.findbugs', name: 'jsr305', version: '3.0.1'

        compileOnly("org.projectlombok:lombok:${lombokVersion}")
        annotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testAnnotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testCompileOnly("org.projectlombok:lombok:${lombokVersion}")

        testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")
        testImplementation('org.junit.platform:junit-platform-launcher:1.5.2')

        testImplementation("org.springframework.boot:spring-boot-starter-test") {
            exclude group: 'junit'
        }
    }

    sonarqube {
        properties {
            property "sonar.sources", "src"
            property "sonar.exclusions", "**/test/**/*.java"
            property "sonar.projectKey", "dc-iot-server"
            property "sonar.projectName", "dc-iot-server"
        }
    }
}


dependencies {

}
