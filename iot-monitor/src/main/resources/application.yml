app:
  name: iot-monitor
server:
  port: 8898
  use-forward-headers: true
  compression.enabled: true


spring:
  application:
    name: iot-monitor
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc-monitor,mongodb-monitor,redis,zipkin
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      label: test01
  mail:
    username: <EMAIL>
    password: gc2019
    fromAdress:
    default-encoding: UTF-8
    host: smtp.ym.163.com
    port: 994
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            enable: true


#spring-cloud服务配置
eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>/eureka/,http://aaa:<EMAIL>/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000

ribbon:
  #请求处理的超时时间
  ReadTimeout: 20000
  #请求连接的超时时间
  ConnectTimeout: 20000

#mybatis:
#  config-location: classpath:mybatis/mybatis-config.xml
#  mapper-locations: classpath:mapper/*.xml
#  type-aliases-package: tk.mybatis.springboot.model

#mybatis-plus:
#  configLocation: classpath:xml/mybatis-config.xml
#  mapper-locations: classpath:com/chargerlink/**/mapper/**/*Mapper.xml
#  type-aliases-package: com.chargerlink.device.entity



#自定义配置
#chargerlink:
#  #文件存储地址
#  resource: /mnt/xvdb/chargerlink-resource/


#自定义配置
#_chargerlink:
#  #短信发送 云片鼎充API KEY
#  sms_apikey: bb5a99b242b37acd4378ef1aca88563d
#  #告警短信模板
#  alarm:
#    alarm_sms_module_id: 2840792
#    alarm_sms_gwtout_module_id: 3165230
#    alarm_sms_micro_service_module_id: 3173450
#    alarm_sms_trans_finish_module_id: 3264118
#  #云片 官方地址
#  message_center:
#    message_push_sms_url: https://sms.yunpian.com/v2/sms/single_send.json
## 权限中心地址
#auth:
#  center:
#    url: https://test01-dongzheng-unified-gateway.iot.renwochong.com/auth


management:
  context-path: /admin
  security:
    enabled: false
  endpoints:
    web:
      exposure:
        include: info,health,shutdown,metrics,beans
  endpoint:
    shutdown:
      enabled: true