package com.cdz360.iot.monitor.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 站点详情
 *
 * <AUTHOR>
 * @Date Create on 2018/8/2 17:14
 */
@Data
public class SiteDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 站点id
     */
    private String id;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 站点名称
     */
    private String name;
    /**
     * 站点地址
     **/
    private String address;
    /**
     * 站点状态
     */
    private Integer status;
    /**
     * 充电桩数量
     */
    private Integer boxCount;
    /**
     * 插座数量
     */
    private Integer connectorCount;
    /**
     * 代理商ID
     */
    private String commId;
    /**
     * 代理商名称
     */
    private String commName;
    /**
     * 代理商联系人
     */
    private String commContacts;
    /**
     * 代理商联系电话
     */
    private String commPhone;
    /**
     * 插座状态统计，插座状态
     */
    private Map<String, Integer> connectorStatusMap;
}
