package com.cdz360.iot.monitor.entity.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 桩端上报的告警消息
 *
 * <AUTHOR>
 * Create on 2018.9.13 19:46
 */
@Data
public class SimAlarmInfoRequest implements Serializable {

    private static final long serialVersionUID = -5507811459070458133L;

    /**
     * 充电桩编码deviceId
     */
    private String deviceId;
    /**
     * 充电桩设备序列号serialNumber
     */
    private String serialNumber;
    /**
     * 业务端的告警代码
     */
    private String alarmCode;

    /**
     * 消息类型，0-消息类型为“开始告警”，1-消息类型为“结束告警”
     */
    private Integer infoType;
    /**
     * 所属商户ID
     */
    private String businessId;
    /**
     * 所属站点ID
     */
    private String siteId;
    /**
     * 所属站点名称
     */
    private String siteName;
    /**
     * 告警消息内容
     */
    private String detail;

}
