package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.EssVo.ErrorEquip;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.feign.biz.BizDataCoreFeignClient;
import com.cdz360.iot.model.alarm.param.GetAlarmListParam;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.monitor.constant.AlarmEventTypeEnum;
import com.cdz360.iot.monitor.constant.AlarmTypeEnum;
import com.cdz360.iot.monitor.constant.DeviceStatusCodeType;
import com.cdz360.iot.monitor.constant.SysWarnCodeContant;
import com.cdz360.iot.monitor.dao.AlarmRecordDao;
import com.cdz360.iot.monitor.entity.po.AlarmCodeCfgPo;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.request.MonitorAlarmRequest;
import com.cdz360.iot.monitor.entity.request.MonitorEssRequest;
import com.cdz360.iot.monitor.entity.request.MonitorEvseRequest;
import com.cdz360.iot.monitor.entity.request.MonitorPvGtiRequest;
import com.cdz360.iot.monitor.entity.request.MonitorSiteCtrlRequest;
import com.cdz360.iot.monitor.entity.request.SysAlarmMsgRequest;
import com.cdz360.iot.monitor.entity.request.TransFinshAlarmRequest;
import com.cdz360.iot.monitor.entity.result.WarningDetailVo;
import com.cdz360.iot.monitor.entity.type.PvMode;
import com.cdz360.iot.monitor.mapper.AlarmCodeCfgMapper;
import com.cdz360.iot.monitor.utils.GenerateIdByRedisAtomicUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 桩端上报告警数据实现类
 *
 * <AUTHOR> Create on 2018/09/13 19:41
 */
@Slf4j
@Service
public class AlarmRecordDs implements WWarningRecordService {

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    private AlarmRecordDao alertRecordDao;

    @Autowired
    private SendAlarmMessageService sendAlarmMessageService;

    @Autowired
    private WarningDetailService warningDetailService;

    @Autowired
    private AlarmCodeCfgMapper alarmCodeCfgMapper;

    @Autowired
    private BizDataCoreFeignClient dataCoreFeignClient;


    public List<AlarmRecord> getAlarmList(GetAlarmListParam paramIn) {
        if (CollectionUtils.isEmpty(paramIn.getSiteIds())) {
            log.warn("参数错误,场站ID列表不能为空");
            throw new DcArgumentException("参数错误,场站ID列表不能为空");
        }
        List<AlarmRecord> alarms = this.alertRecordDao.getAlarmList(paramIn);
        return alarms;
    }


    @Async
    @Override
    public void insertWWarningRecordByDevice(MonitorAlarmRequest monitorAlarmRequest)
        throws DcServiceException {
        log.info(">> 桩端上报故障告警处理开始。monitorAlarmRequest={}",
            JsonUtils.toJsonString(monitorAlarmRequest));
        // 2-处理告警中数据或解除告警信息
        if (monitorAlarmRequest.getEndTime() == null) {
            // 告警中
            log.info("桩端上报执行告警开始业务逻辑。evseId = {}, plugIdx = {}",
                monitorAlarmRequest.getEvseId(), monitorAlarmRequest.getConnectorId());
            this.startAlarmAndSendCommercialLifeNumber(monitorAlarmRequest//, boxSimpleInfoVo
            );
        } else if (monitorAlarmRequest.getInfoType()
            != AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue()) {
            // 处理桩端上报告警或故障
            log.info("桩端上报执行告警结束业务逻辑。evseId = {}, plugIdx = {}",
                monitorAlarmRequest.getEvseId(), monitorAlarmRequest.getConnectorId());
            this.stopAlarmAndSendCommercialLifeNumber(monitorAlarmRequest
//, boxSimpleInfoVo.getSiteId()
            );
        }
        log.info("<< 桩端上报故障告警处理结束。monitorAlarmRequest={}, evseId = {}, plugIdx = {}",
            JsonUtils.toJsonString(monitorAlarmRequest),
            monitorAlarmRequest.getEvseId(),
            monitorAlarmRequest.getConnectorId());
    }

    @Override
    public void insertWWarningSiteCtrlRecordByDevice(MonitorSiteCtrlRequest monitorSiteCtrlRequest)
        throws DcServiceException {
        log.info(">> 场站控制器上报故障告警处理开始。monitorAlarmRequest={}",
            JsonUtils.toJsonString(monitorSiteCtrlRequest));

        if (monitorSiteCtrlRequest.getEndTime() == null) {
            // 告警中
            log.info("场站控制器上报执行告警开始业务逻辑。ctrlNo = {}",
                monitorSiteCtrlRequest.getCtrlNo());

            this.startSiteCtrlAlarm(monitorSiteCtrlRequest);
        } else /*if (monitorSiteCtrlRequest.getInfoType() != AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())*/ {
            // 处理桩端上报告警或故障
            log.info("场站控制器上报执行告警结束业务逻辑。ctrlNo = {}",
                monitorSiteCtrlRequest.getCtrlNo());

            this.stopSiteCtrlAlarm(monitorSiteCtrlRequest);
        }
        log.info("<< 场站控制器上报故障告警处理结束。monitorAlarmRequest={}, ctrlNo = {}",
            JsonUtils.toJsonString(monitorSiteCtrlRequest),
            monitorSiteCtrlRequest.getCtrlNo());
    }

    @Override
    public void insertWWarningEvseRecordByDevice(MonitorEvseRequest request)
        throws DcServiceException {
        log.info(">> 桩上报故障告警处理开始。monitorAlarmRequest={}",
            JsonUtils.toJsonString(request));

        if (request.getEndTime() == null) {
            // 告警中
            log.info("桩上报执行告警开始业务逻辑。evseNo = {}",
                request.getEvseNo());

            this.startEvseAlarm(request);
        } else /*if (monitorSiteCtrlRequest.getInfoType() != AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())*/ {
            // 处理桩端上报告警或故障
            log.info("桩上报执行告警结束业务逻辑。evseNo = {}",
                request.getEvseNo());

            this.stopEvseAlarm(request);
        }
        log.info("<< 桩上报故障告警处理结束。monitorAlarmRequest={}, evseNo = {}",
            JsonUtils.toJsonString(request),
            request.getEvseNo());
    }

    @Override
    public void insertWWarningEvseRecordByPv(MonitorPvGtiRequest request)
        throws DcServiceException {
        log.info(">> 逆变器告警上报处理开始。request={}", JsonUtils.toJsonString(request));
        this.startPvAlarm(request);
    }

    @Override
    public void insertWWarningEvseRecordBySrs(MonitorPvGtiRequest request)
        throws DcServiceException {
        log.info(">> 辐射仪告警上报处理开始。request={}", JsonUtils.toJsonString(request));
        this.startSrsAlarm(request);
    }

    @Override
    public void insertWWarningEvseRecordBySim(MonitorPvGtiRequest request)
        throws DcServiceException {
        log.info(">> SIM卡告警上报处理开始。request={}", JsonUtils.toJsonString(request));
        this.startSimAlarm(request);
    }

    @Override
    public void insertWWarningEssRecordByEssEquip(MonitorEssRequest request)
        throws DcServiceException {
        log.info("储能ESS告警执行新增。request={}", JsonUtils.toJsonString(request));
        this.startEssAlarm(request);
    }


    public List<AlarmRecord> addEquipAlarmRecord(EssEquipType equipType, ErrorEquip equip,
        List<Long> alarmCodes,
        EssVo<?> ess, LocalDateTime lts, String tz) {
        log.info("新增设备告警记录。equipType = {}, dno = {}, alarmCodes = {}, lts = {}, tz = {}",
            equipType, equip.getDno(), alarmCodes, lts, tz);
        AlarmRecord record = new AlarmRecord();
        record.setSiteId(ess.getSiteId())
            .setSiteName(ess.getSiteName())
            .setBusinessId(ess.getSiteCommId().toString())
            .setDeviceId(ess.getGwno())
            .setBoxOutFactoryCode(ess.getGwno())
            .setEvseName(equip.getEquipName())
            .setDno(equip.getDno())
//            .setSn(equip.getSn())
//            .setPlugName(request.getName())
            .setEquipType(equipType.getCode())
            .setEquipId(equip.getEquipId())
            .setEssEquipName(equip.getEquipName())
            .setStartTime(new Date())
            .setLst(lts)    // 发生告警的时间， 设备本地时间
            .setTz(tz)  // 发生告警设备的时区
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setWarningType(AlarmEventTypeEnum.ALARM_ESS_WARNING.getValue())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());

        List<AlarmRecord> records = new ArrayList<>();
        for (Long code : alarmCodes) {
            AlarmCodeCfgPo alarmCfg = this.alarmCodeCfgMapper.getAlarmCodeCfg(equipType, code);
            record.setWarningCode(code.toString())
                .setErrorCode(code);
            if (alarmCfg != null) {
                record.setWarningName(alarmCfg.getName())
                    .setWarningInstructions(alarmCfg.getDesc());
            }
            alertRecordDao.insertWarningRecord(record);
            records.add(record);
        }
        return records;
    }

    @Override
    public void updateWWarningEvseRecordByPv(List<Long> warningIdList) throws DcServiceException {
        log.info(">> 逆变器告警结束。request={}", JsonUtils.toJsonString(warningIdList));
        this.stopPvGtiAlarm(warningIdList);
    }

    @Override
    public void updateWWarningEvseRecordBySrs(List<Long> warningIdList) throws DcServiceException {
        log.info(">> 辐射仪告警结束。request={}", JsonUtils.toJsonString(warningIdList));
        this.stopSrsAlarm(warningIdList);
    }

    @Override
    public void updateWWarningEvseRecordBySim(List<Long> warningIdList) throws DcServiceException {
        log.info(">> SIM卡告警结束。request={}", JsonUtils.toJsonString(warningIdList));
        this.stopSimAlarm(warningIdList);
    }

    @Override
    public void updateWWarningEssRecordByEssEquip(List<Long> warningIdList)
        throws DcServiceException {
        log.info(">> 储能ESS告警结束。request={}", JsonUtils.toJsonString(warningIdList));
        this.stopEssAlarm(warningIdList);
    }

    @Override
    public void stopEquipAlarms(EssEquipType equipType, String dno, List<Long> alarmCodes,
        LocalDateTime lts) {
        log.info(">> 批量关闭设备告警。equipType = {}, dno = {}, alarmCodes = {}, lts = {}",
            equipType, dno, alarmCodes, lts);
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(Calendar.getInstance().getTime());
        wWarningRecord.setWarningUpdateTime(Calendar.getInstance().getTime());
        wWarningRecord.setLet(lts); // 告警结束时间 - 设备本地时间
        alertRecordDao.batchStopAlarms(wWarningRecord, dno, alarmCodes);
    }

    List<AlarmRecord> getActiveRecords(long start,
        int size) {
        return this.alertRecordDao.getActiveRecords(start, size);
    }

    /**
     * 临时通过redis存取值避免告警并发问题
     *
     * @param redisKey
     * @return
     */
    @Synchronized
    private Object getRedisObject(String redisKey) {
        long nowTime = System.currentTimeMillis();
        Object redisObject = redisTemplate.opsForValue().get(redisKey);
        if (redisObject != null) {
            log.info("桩端上报执行告警业务进行中{}", redisObject.toString());
            return redisObject;
        }
        log.info("加入告警处理中数据{}时间{}", redisKey, nowTime);
        //加入告警处理中数据，暂定10秒处理时间
        redisTemplate.opsForValue().set(redisKey, nowTime, 10, TimeUnit.SECONDS);
        return null;
    }

    /**
     * 产生桩端告警数据
     *
     * @param monitorAlarmRequest 桩端告警消息体
     */
    private void startAlarmAndSendCommercialLifeNumber(MonitorAlarmRequest monitorAlarmRequest
//, BoxSimpleInfoVo bsBox
    ) {
        String redisKey =
            monitorAlarmRequest.getAlarmCode() + "+" + monitorAlarmRequest.getEvseId();
        //先判断是否正在操作此告警
        if (getRedisObject(redisKey) != null) {
            log.info("桩端上报执行告警业务进行中{}", redisKey);
            return;
        }
        try {
            // 1-插入桩端告警记录
            AlarmRecord wWarningRecord = getWWarningSiteCtrlRecordData(monitorAlarmRequest
//, bsBox
            );
            if (wWarningRecord == null) {
                redisTemplate.delete(redisKey);
                return;
            }
            alertRecordDao.insertWarningRecord(wWarningRecord);
            log.info("桩端上报告警插入告警数据成功。wWarningRecord = {}", wWarningRecord.toString());
            //调用Data-Core发送模板消息
            dataCoreFeignClient.sendMsg(wWarningRecord)
                .block(Duration.ofSeconds(30L));

            redisTemplate.delete(redisKey);
            // 告警结束时间存在不发送通知
            if (null != monitorAlarmRequest.getEndTime()) {
                return;
            }
            //组织告警发送人和告警内容，发送告警短信
//            getAndSendAlarmToMessage(wWarningRecord, monitorAlarmRequest.getSiteId(),
//                monitorAlarmRequest.getConnectorId());
        } catch (Exception e) {
            log.info(">>>>>{}桩端上报执行告警开始业务插入告警数据异常{}",
                monitorAlarmRequest.getEvseId(), e);
            //告警处理完成删除告警处理中数据
            redisTemplate.delete(redisKey);
        }
    }

    private void startSiteCtrlAlarm(MonitorSiteCtrlRequest monitorSiteCtrlRequest) {
        String redisKey =
            monitorSiteCtrlRequest.getAlarmCode() + "+" + monitorSiteCtrlRequest.getCtrlNo();
        //先判断是否正在操作此告警
        if (getRedisObject(redisKey) != null) {
            log.info("场站控制器上报执行告警业务进行中{}", redisKey);
            return;
        }
        try {
            // 1-插入场站控制器告警记录
            AlarmRecord wWarningRecord = this.getWWarningSiteCtrlRecordData(
                monitorSiteCtrlRequest);
            if (wWarningRecord == null) {
                redisTemplate.delete(redisKey);
                return;
            }
            alertRecordDao.insertWarningRecord(wWarningRecord);
            log.info("场站控制器上报告警插入告警数据成功。wWarningRecord = {}",
                wWarningRecord.toString());
            redisTemplate.delete(redisKey);

            //控制器告警发送微信模板消息
            wWarningRecord.setCtrlNo(monitorSiteCtrlRequest.getCtrlNo());
            dataCoreFeignClient.sendMsg(wWarningRecord)
                .block(Duration.ofSeconds(30L));

            // 告警结束时间存在不发送通知
            if (null != monitorSiteCtrlRequest.getEndTime()) {
                return;
            }
            //组织告警发送人和告警内容，发送告警短信

        } catch (Exception e) {
            log.info(">>>>>{}场站控制器上报执行告警开始业务插入告警数据异常{}",
                monitorSiteCtrlRequest.getCtrlNo(), e);
            //告警处理完成删除告警处理中数据
            redisTemplate.delete(redisKey);
        }
    }

    private void startEvseAlarm(MonitorEvseRequest request) {
        String redisKey = request.getAlarmCode() + "+EVSE+" + request.getCtrlNo();
        //先判断是否正在操作此告警
        if (getRedisObject(redisKey) != null) {
            log.info("场站控制器上报执行告警业务进行中{}", redisKey);
            return;
        }
        try {
            // 1-插入场站控制器告警记录
            AlarmRecord wWarningRecord = this.getWWarningEvseRecordData(request);
            if (wWarningRecord == null) {
                redisTemplate.delete(redisKey);
                return;
            }
            alertRecordDao.insertWarningRecord(wWarningRecord);
            log.info("场站控制器上报告警插入告警数据成功。wWarningRecord = {}",
                wWarningRecord.toString());

            redisTemplate.delete(redisKey);

            //桩告警发送模板消息
            dataCoreFeignClient.sendMsg(wWarningRecord)
                .block(Duration.ofSeconds(30L));
            // 告警结束时间存在不发送通知
            if (null != request.getEndTime()) {
                return;
            }
            //组织告警发送人和告警内容，发送告警短信

        } catch (Exception e) {
            log.info(">>>>>{}场站控制器上报执行告警开始业务插入告警数据异常{}", request.getEvseNo(),
                e);
            //告警处理完成删除告警处理中数据
            redisTemplate.delete(redisKey);
        }
    }

    /**
     * 逆变器故障新增
     *
     * @param request
     */
    private void startPvAlarm(MonitorPvGtiRequest request) {
        AlarmRecord record = new AlarmRecord();
        record.setSiteId(request.getSiteId())
            .setSiteName(request.getSiteName())
            .setBusinessId(request.getSiteCommId().toString())
            .setDeviceId(request.getGwno())
            .setBoxOutFactoryCode(request.getGwno())
            .setEvseName(request.getGwName())
            .setConnectorId(Integer.valueOf(request.getRecId()))
            .setPlugName(request.getPvName())
            .setRtStatus(request.getRtStatus())
            .setWarningCode(request.getWarningCode())
            .setWarningName(request.getWarningName())
            .setWarningInstructions(request.getWarningInstruction())
            .setStartTime(request.getStartTime())
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setWarningType(AlarmEventTypeEnum.ALARM_PV_WARNING.getValue())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
        alertRecordDao.insertWarningRecord(record);
    }

    /**
     * 辐射仪故障新增
     *
     * @param request
     */
    private void startSrsAlarm(MonitorPvGtiRequest request) {
        AlarmRecord record = new AlarmRecord();
        record.setSiteId(request.getSiteId())
            .setSiteName(request.getSiteName())
            .setBusinessId(request.getSiteCommId().toString())
            .setDeviceId(request.getGwno())
            .setBoxOutFactoryCode(request.getGwno())
            .setSourceNo(request.getDno())
            .setEvseName(request.getPvName())
            .setConnectorId(Integer.valueOf(request.getRecId()))
            .setPlugName(request.getPvName())
//                .setRtStatus(request.getRtStatus())
            .setWarningCode(request.getWarningCode())
            .setWarningName(request.getWarningName())
            .setWarningInstructions(request.getWarningInstruction())
            .setStartTime(request.getStartTime())
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setWarningType(AlarmEventTypeEnum.ALARM_SRS_WARNING.getValue())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
        alertRecordDao.insertWarningRecord(record);
    }

    /**
     * 辐射仪故障新增
     *
     * @param request
     */
    private void startSimAlarm(MonitorPvGtiRequest request) {
        AlarmRecord record = new AlarmRecord();
        record.setSiteId(request.getSiteId())
            .setSiteName(request.getSiteName())
            .setBusinessId(
                request.getSiteCommId() != null ? request.getSiteCommId().toString() : null)
//                .setDeviceId(request.getGwno())
            .setBoxOutFactoryCode(request.getDno())
            .setSourceNo(request.getDno())
//                .setEvseName(request.getPvName())
            .setConnectorId(Integer.valueOf(request.getRecId()))
//                .setPlugName(request.getPvName())
//                .setRtStatus(request.getRtStatus())
            .setWarningCode(request.getWarningCode())
            .setWarningName(request.getWarningName())
            .setWarningInstructions(request.getWarningInstruction())
            .setStartTime(request.getStartTime())
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setWarningType(AlarmEventTypeEnum.ALARM_SIM_WARNING.getValue())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
        alertRecordDao.insertWarningRecord(record);
    }

    private void startEssAlarm(MonitorEssRequest request) {
        AlarmRecord record = new AlarmRecord();
        record.setSiteId(request.getSiteId())
            .setSiteName(request.getSiteName())
            .setBusinessId(
                request.getSiteCommId() != null ? request.getSiteCommId().toString() : null)
            .setDeviceId(request.getGwno())
            .setBoxOutFactoryCode(request.getGwno())
            .setEvseName(request.getGwName())
            .setDno(request.getDno())
            .setSn(request.getSn())
            .setPlugName(request.getName())
            .setEquipType(request.getEquipType() != null ? request.getEquipType().getCode() : null)
            .setEquipId(request.getEquipId())
            .setEssEquipName(request.getEquipName())
            .setWarningCode(request.getWarningCode())
            .setWarningName(request.getWarningName())
            .setStartTime(request.getStartTime())
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setWarningType(AlarmEventTypeEnum.ALARM_ESS_WARNING.getValue())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());

        if (StringUtils.isNotEmpty(
            DeviceStatusCodeType.getByCode(
                    Math.toIntExact(Long.parseLong(request.getWarningCode())))
                .getInstruction())) {
            record.setWarningInstructions(DeviceStatusCodeType.getByCode(
                Math.toIntExact(Long.parseLong(request.getWarningCode()))).getInstruction());
        } else {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(request.getEquipName());
            if (null != request.getClusterNo()) {
                stringBuilder.append(";").append(request.getClusterNo()).append("号电池簇");
            }
            if (null != request.getPackNo()) {
                stringBuilder.append(";").append(request.getPackNo()).append("号电池组");
            }
            record.setWarningInstructions(stringBuilder.toString());
        }
        alertRecordDao.insertWarningRecord(record);
    }

//    /**
//     * @param wWarningRecord 告警
//     * @param siteId         站点id
//     * @param connectorId
//     */
//    private void getAndSendAlarmToMessage(WWarningRecord wWarningRecord, String siteId,
//        Integer connectorId) {
//        log.info("开始准备发送消息{}", siteId);
//        WarningSendConfigVo warningSendConfigVo = new WarningSendConfigVo() {{
//            setWarningCode(wWarningRecord.getWarningCode());
//            setBusinessId(wWarningRecord.getBusinessId());
//        }};
//        // 获取告警发送配置列表
//        List<WarningSendConfigVo> warningSendConfigVoList = warningSendConfigService.queryWarningSendConfig(
//            warningSendConfigVo);
//        if (warningSendConfigVoList == null || warningSendConfigVoList.size() == 0) {
//            log.info("告警发送配置列表为空。warningCode = {},businessId = {}",
//                wWarningRecord.getWarningCode(), wWarningRecord.getBusinessId());
//            return;
//        }
//        if (warningSendConfigVoList.size() > 1) {
//            log.info("告警发送配置列表有多条。warningSendConfigVoList = {}",
//                JsonUtils.toJsonString(warningSendConfigVoList));
//            return;
//        }
//
//        if (warningSendConfigVoList.get(0).getSms() == 0) {
//            log.info("告警发送配置,告警码 = {},配置信息 = {},发送短信关闭",
//                wWarningRecord.getWarningCode(),
//                JsonUtils.toJsonString(warningSendConfigVoList.get(0)));
//            return;
//        }
//        //获取当前时间是周几  0是周日  1-6 为周一到周六
//        Calendar cal = Calendar.getInstance();
//        cal.setTime(new Date());
//        Integer week = cal.get(Calendar.DAY_OF_WEEK) - 1;
//        //设置日期格式:小时分钟格式
//        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//        //获取当前小时分钟组合  如 08:00
//        String hAndS = sdf.format(cal.getTime());
//        log.info("获取值班人员参数。siteId = {},week = {},hAndS = {}", siteId, week, hAndS);
//        // 根据站点id得到站点值班人员
//        ListResponse<SiteDutyInfo> siteDutyResponse = commercialFeignClient.getSiteDutyInfo(siteId,
//            week.toString(), hAndS);
//        if (siteDutyResponse.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException(
//                "获取值班人员信息失败。ERROR = " + siteDutyResponse.getError());
//        }
//        List<SiteDutyInfo> siteDutyList = siteDutyResponse.getData();
//        log.info("值班人员列表结果。siteDutyList = {}", siteDutyList);
//        //组织值班人员并发送短信
//        if (siteDutyList != null && siteDutyList.size() > 0) {
//            //值班人员手机号
//            List<String> phones = new ArrayList<>();
//            siteDutyList.stream().forEach(sdi -> phones.add(sdi.getTel()));
//            // 发送告警短信
//            sendAlarmToMessage(phones, wWarningRecord, connectorId);
//        } else {
//            log.error("告警发送消息失败获取值班人员为空。siteId = {},siteDutyList = {}", siteId,
//                siteDutyList);
//        }
//
//    }

//    /**
//     * t 短信告警消息（断电告警短信）
//     *
//     * @param wWarningRecord
//     * @param connectorId
//     */
//    private void sendAlarmToMessage(List<String> phones, WWarningRecord wWarningRecord,
//        Integer connectorId) {
//        log.info("桩端上报执行告警业务开始，值班人员phones = {}", JsonUtils.toJsonString(phones));
//        SendMessageRequest sendMessageRequest = new SendMessageRequest();
//        sendMessageRequest.setPhones(phones);
//        sendMessageRequest.setStation(wWarningRecord.getSiteName());
//        sendMessageRequest.setPile(wWarningRecord.getBoxOutFactoryCode());
//        if (connectorId != null && connectorId != 0) {
//            sendMessageRequest.setSocket(connectorId);
//        }
//        sendMessageRequest.setWarningName(wWarningRecord.getWarningName());
//        sendMessageRequest.setWarningType(wWarningRecord.getWarningType());
//        sendAlarmMessageService.sendAlarmMobileMessage(sendMessageRequest);
//        log.info("桩端上报执行告警业务结束，值班人员phones = {}", JsonUtils.toJsonString(phones));
//    }

    /**
     * 组织告警消息，如果重复告警更新告警时间返回null
     *
     * @param monitorAlarmRequest 桩端上报消息体
     * @return 告警消息
     */
    private AlarmRecord getWWarningSiteCtrlRecordData(MonitorAlarmRequest monitorAlarmRequest
//, BoxSimpleInfoVo bsBox
    ) throws DcServiceException {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        //List<String> deviceNoList = new ArrayList<>();
        //deviceNoList.add(monitorAlarmRequest.getEvseId());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        // 根据告警码查询桩所有未结束告警
        AlarmRecord warningRecord = alertRecordDao.getWarningRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorAlarmRequest.getAlarmCode().toString(), monitorAlarmRequest.getEvseId(),
            monitorAlarmRequest.getConnectorId(), typeList);

        log.info("桩：{},未结束告警记录列表：{}", monitorAlarmRequest.getEvseId(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null // && wWarningRecordList.size() > 0
        ) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            //for (WWarningRecord warningRecord : wWarningRecordList) {
            if (StringUtils.isNotBlank(monitorAlarmRequest.getError())) {
                warningRecord.setError(monitorAlarmRequest.getError());
            }
            if (monitorAlarmRequest.getTemp() != null && monitorAlarmRequest.getTemp() != 0) {
                warningRecord.setTemp(monitorAlarmRequest.getTemp());
            }
            //添加设备型号、软件版本
            warningRecord.setModelName(monitorAlarmRequest.getModelName());
            warningRecord.setFirmwareVer(monitorAlarmRequest.getFirmwareVer());
            warningRecord.setEvseName(monitorAlarmRequest.getEvseName());
            //订单号
            if (StringUtils.isNotEmpty(monitorAlarmRequest.getOrderNo())) {
                warningRecord.setOrderNo(monitorAlarmRequest.getOrderNo());
            }
            // TODO 需要考虑非桩端上报的离线告警处理
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}桩号={},插座号={},告警编码={}",
                    warningRecord.getWarningId(),
                    monitorAlarmRequest.getEvseId(),
                    monitorAlarmRequest.getConnectorId(),
                    monitorAlarmRequest.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            //}
            alertRecordDao.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", monitorAlarmRequest.getEvseId());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(monitorAlarmRequest.getAlarmCode().toString());
            }};

            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (warningDetailVos == null || warningDetailVos.size() == 0) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}",
                    monitorAlarmRequest.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            AlarmRecord wWarningRecord = new AlarmRecord();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(monitorAlarmRequest.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
            if (monitorAlarmRequest.getConnectorId() != null
                && monitorAlarmRequest.getConnectorId() != 0) {
                wWarningRecord.setConnectorId(monitorAlarmRequest.getConnectorId());
            }
            wWarningRecord.setStartTime(monitorAlarmRequest.getStartTime());
            wWarningRecord.setWarningUpdateTime(monitorAlarmRequest.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            wWarningRecord.setWarningType(monitorAlarmRequest.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setDeviceId(monitorAlarmRequest.getEvseId());
            // 上报设备ID
            wWarningRecord.setSourceNo(monitorAlarmRequest.getSourceNo());
            wWarningRecord.setBoxOutFactoryCode(monitorAlarmRequest.getEvseId());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(monitorAlarmRequest.getSiteCommId()));
            // 站点id
            wWarningRecord.setSiteId(monitorAlarmRequest.getSiteId());
            //设备型号
            wWarningRecord.setModelName(monitorAlarmRequest.getModelName());
            //软件版本
            wWarningRecord.setFirmwareVer(monitorAlarmRequest.getFirmwareVer());
            //桩名称
            wWarningRecord.setEvseName(monitorAlarmRequest.getEvseName());
            //充电订单号
            if (StringUtils.isNotEmpty(monitorAlarmRequest.getOrderNo())) {
                wWarningRecord.setOrderNo(monitorAlarmRequest.getOrderNo());
            }
            //枪头名称
            if (StringUtils.isNotEmpty(monitorAlarmRequest.getPlugName())) {
                wWarningRecord.setPlugName(monitorAlarmRequest.getPlugName());
            }
            // 站点名称
            wWarningRecord.setSiteName(monitorAlarmRequest.getSiteName());
            if (StringUtils.isNotBlank(monitorAlarmRequest.getError())) {
                wWarningRecord.setError(monitorAlarmRequest.getError());
            }
            if (monitorAlarmRequest.getTemp() != null && monitorAlarmRequest.getTemp() != 0) {
                wWarningRecord.setTemp(monitorAlarmRequest.getTemp());
            }
            if (StringUtils.isNotBlank(monitorAlarmRequest.getLinkId())) {
                wWarningRecord.setLinkId(monitorAlarmRequest.getLinkId());
            }
            return wWarningRecord;
        }
    }

    private AlarmRecord getWWarningEvseRecordData(MonitorEvseRequest request)
        throws DcServiceException {
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());

        // 根据告警码查询控制器所有未结束告警
        AlarmRecord warningRecord = alertRecordDao.getWarningEvseRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            request.getAlarmCode().toString(),
            request.getEvseNo(),
            typeList);

        log.info("桩：{},未结束告警记录列表：{}", request.getEvseNo(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            if (StringUtils.isNotBlank(request.getError())) {
                warningRecord.setError(request.getError());
            }
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}, evseNo={}, 告警编码={}",
                    warningRecord.getWarningId(),
                    request.getEvseNo(),
                    request.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            alertRecordDao.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", request.getEvseNo());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(request.getAlarmCode().toString());
            }};

            //TODO 每次都读数据库，需要优化
            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (CollectionUtils.isEmpty(warningDetailVos)) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}", request.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            AlarmRecord wWarningRecord = new AlarmRecord();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(request.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
//            if (monitorSiteCtrlRequest.getConnectorId() != null && monitorSiteCtrlRequest.getConnectorId() != 0) {
            wWarningRecord.setConnectorId(0);
//            }
            wWarningRecord.setStartTime(request.getStartTime());
            wWarningRecord.setWarningUpdateTime(request.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            if (NumberUtils.equals(request.getInfoType(),
                AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
            } else if (NumberUtils.equals(request.getInfoType(),
                AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
            } else {
                log.error("未知的infoType: {}", request.getInfoType());
            }
            wWarningRecord.setWarningType(request.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setBoxOutFactoryCode(request.getEvseNo());
            // 上报设备ID
            wWarningRecord.setSourceNo(request.getCtrlNo());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(request.getSiteCommId()));
            //桩名称
            wWarningRecord.setEvseName(request.getEvseName());
            // 站点id
            wWarningRecord.setSiteId(request.getSiteId());
            // 站点名称
            wWarningRecord.setSiteName(request.getSiteName());
            if (StringUtils.isNotBlank(request.getError())) {
                wWarningRecord.setError(request.getError());
            }
            if (StringUtils.isNotBlank(request.getLinkId())) {
                wWarningRecord.setLinkId(request.getLinkId());
            }
            return wWarningRecord;
        }
    }

    private AlarmRecord getWWarningSiteCtrlRecordData(
        MonitorSiteCtrlRequest monitorSiteCtrlRequest) throws DcServiceException {

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());

        // 根据告警码查询控制器所有未结束告警
        AlarmRecord warningRecord = alertRecordDao.getWarningSiteCtrlRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorSiteCtrlRequest.getAlarmCode().toString(),
            monitorSiteCtrlRequest.getCtrlNo(),
            typeList);

        log.info("场站控制器：{},未结束告警记录列表：{}", monitorSiteCtrlRequest.getCtrlNo(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            //for (WWarningRecord warningRecord : wWarningRecordList) {
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getError())) {
                warningRecord.setError(monitorSiteCtrlRequest.getError());
            }
            if (monitorSiteCtrlRequest.getPwrTemp() != null
                && monitorSiteCtrlRequest.getPwrTemp() != 0) {
                warningRecord.setPwrTemp(monitorSiteCtrlRequest.getPwrTemp());
            }
            if (monitorSiteCtrlRequest.getLoadRatio() != null
                && monitorSiteCtrlRequest.getLoadRatio() != 0) {
                warningRecord.setLoadRatio(monitorSiteCtrlRequest.getLoadRatio());
            }
            // TODO 需要考虑非桩端上报的离线告警处理
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}, 场站控制器={}, 告警编码={}",
                    warningRecord.getWarningId(),
                    monitorSiteCtrlRequest.getCtrlNo(),
                    monitorSiteCtrlRequest.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            alertRecordDao.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", monitorSiteCtrlRequest.getCtrlNo());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(monitorSiteCtrlRequest.getAlarmCode().toString());
            }};

            //TODO 每次都读数据库，需要优化
            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (warningDetailVos == null || warningDetailVos.size() == 0) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}",
                    monitorSiteCtrlRequest.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            AlarmRecord wWarningRecord = new AlarmRecord();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(monitorSiteCtrlRequest.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
//            if (monitorSiteCtrlRequest.getConnectorId() != null && monitorSiteCtrlRequest.getConnectorId() != 0) {
//                wWarningRecord.setConnectorId(monitorSiteCtrlRequest.getConnectorId());
//            }
            wWarningRecord.setStartTime(monitorSiteCtrlRequest.getStartTime());
            wWarningRecord.setWarningUpdateTime(monitorSiteCtrlRequest.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getLinkId())) {
                // 有LinkId时，认为是控制器的告警
                Assert.notNull(monitorSiteCtrlRequest.getInfoType(), "infoType为null");
                if (NumberUtils.equals(monitorSiteCtrlRequest.getInfoType(),
                    AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue())) {
                    monitorSiteCtrlRequest.setInfoType(
                        AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
                } else if (NumberUtils.equals(monitorSiteCtrlRequest.getInfoType(),
                    AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue())) {
                    monitorSiteCtrlRequest.setInfoType(
                        AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
                } else {
                    log.error("未知的infoType: {}", monitorSiteCtrlRequest.getInfoType());
                }
            }
            wWarningRecord.setWarningType(monitorSiteCtrlRequest.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setBoxOutFactoryCode(monitorSiteCtrlRequest.getCtrlNo());
            // 上报设备ID
            wWarningRecord.setSourceNo(monitorSiteCtrlRequest.getCtrlNo());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(monitorSiteCtrlRequest.getSiteCommId()));
            // 站点id
            wWarningRecord.setSiteId(monitorSiteCtrlRequest.getSiteId());
            // 站点名称
            wWarningRecord.setSiteName(monitorSiteCtrlRequest.getSiteName());
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getError())) {
                wWarningRecord.setError(monitorSiteCtrlRequest.getError());
            }
            if (monitorSiteCtrlRequest.getPwrTemp() != null
                && monitorSiteCtrlRequest.getPwrTemp() != 0) {
                wWarningRecord.setPwrTemp(monitorSiteCtrlRequest.getPwrTemp());
            }
            if (monitorSiteCtrlRequest.getLoadRatio() != null
                && monitorSiteCtrlRequest.getLoadRatio() != 0) {
                wWarningRecord.setLoadRatio(monitorSiteCtrlRequest.getLoadRatio());
            }
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getLinkId())) {
                wWarningRecord.setLinkId(monitorSiteCtrlRequest.getLinkId());
            }
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getCtrlName())) {
                wWarningRecord.setCtrlName(monitorSiteCtrlRequest.getCtrlName());
            }
            return wWarningRecord;
        }
    }


    /**
     * 停止告警
     *
     * @param monitorAlarmRequest 桩端消息体
     */
    private void stopAlarmAndSendCommercialLifeNumber(MonitorAlarmRequest monitorAlarmRequest
//, String siteId
    ) {

        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorAlarmRequest.getEvseId());
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        //查询当前设备所有未结束的桩端上报告警或故障

        List<AlarmRecord> list = alertRecordDao.getWarningRecord(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            null, deviceNoList,
            monitorAlarmRequest.getConnectorId(), typeList, null);

        List<Long> warningRecordList = getIdList(list);
        if (warningRecordList == null || warningRecordList.isEmpty()) {
            log.info("未找到告警信息>>>boxCode={},connectorId={},alarmCode={}",
                monitorAlarmRequest.getEvseId(), monitorAlarmRequest.getConnectorId(),
                monitorAlarmRequest.getAlarmCode());
            return;
        }

        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(monitorAlarmRequest.getEndTime());
        alertRecordDao.updateBatchStatusById(warningRecordList, wWarningRecord);
        //告警结束，发送微信模板消息
        list.forEach(e -> {
            e.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
            e.setEndTime(monitorAlarmRequest.getEndTime());
            dataCoreFeignClient.sendMsg(e)
                .block(Duration.ofSeconds(30L));
        });

        log.info(">>>>>桩端上报执行告警结束业务更新告警记录{}>>>>recordId={}>",
            monitorAlarmRequest.getEvseId(), JsonUtils.toJsonString(warningRecordList));
    }

    /**
     * 3.7协议桩重新注册时，结束上次离线的告警
     *
     * @param evseNo
     * @param registerReason
     */
    public void clearRecentlyAlarmConnectLost(String evseNo, EvseRegisterReason registerReason) {

        Date currTime = Calendar.getInstance().getTime();

        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(evseNo);
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        //查询当前设备所有未结束的桩端上报告警或故障
        List<AlarmRecord> list = alertRecordDao.getWarningRecord(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            AlarmTypeEnum.ALARM_CONNECT_LOST.getValue(),
            deviceNoList,
            -1, //忽略枪号
            typeList,
            null);

        List<Long> warningRecordList = getIdList(list);
        if (CollectionUtils.isEmpty(warningRecordList)) {
            log.info("未找到告警信息>>>boxCode={}", evseNo);
            return;
        }

        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(currTime);
        if (registerReason != null) {
            wWarningRecord.setWarningInstructions(this.convertToInstruction(registerReason));
        }
        alertRecordDao.updateBatchStatusById(warningRecordList, wWarningRecord);
        //告警结束，发送微信模板消息
        list.forEach(e -> {
            e.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
            e.setEndTime(currTime);
            dataCoreFeignClient.sendMsg(e)
                .block(Duration.ofSeconds(30L));
        });

        log.info(">>>>>桩端上报执行告警结束业务更新告警记录{}>>>>recordId={}>", evseNo,
            JsonUtils.toJsonString(warningRecordList));
    }

    private String convertToInstruction(EvseRegisterReason reason) {
        String str = "离线原因：";
        switch (reason) {
            case UNKNOWN:
                str = str.concat("其它");
                break;
            case POWER_ON:
                str = str.concat("上电重启");
                break;
            case OFFLINE:
                str = str.concat("失去连接");
                break;
            case SIGN_MISMATCH:
                str = str.concat("报文签名不匹配");
                break;
            default:
                log.info("default reason: {}", reason);
                break;
        }
        return str;
    }

    private void stopSiteCtrlAlarm(MonitorSiteCtrlRequest monitorSiteCtrlRequest) {
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorSiteCtrlRequest.getCtrlNo());
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        //查询当前设备所有未结束的桩端上报告警或故障

        List<AlarmRecord> list = alertRecordDao.getWarningSiteCtrlRecord(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            null,
            deviceNoList,
            typeList);

        List<Long> warningRecordList = getIdList(list);
        if (warningRecordList == null || warningRecordList.isEmpty()) {
            log.info("未找到告警信息>>>boxCode={}, alarmCode={}",
                monitorSiteCtrlRequest.getCtrlNo(),
                monitorSiteCtrlRequest.getAlarmCode());
            return;
        }
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(monitorSiteCtrlRequest.getEndTime());
        alertRecordDao.updateBatchStatusById(warningRecordList, wWarningRecord);

        //告警结束，发送微信模板消息
        list.forEach(e -> {
            e.setCtrlNo(monitorSiteCtrlRequest.getCtrlNo());
            e.setEndTime(monitorSiteCtrlRequest.getEndTime());
            dataCoreFeignClient.sendMsg(e)
                .block(Duration.ofSeconds(30L));
        });

        log.info(">>>>>桩端上报执行告警结束业务更新告警记录{}>>>>recordId={}>",
            monitorSiteCtrlRequest.getCtrlNo(),
            JsonUtils.toJsonString(warningRecordList));

    }

    private void stopEvseAlarm(MonitorEvseRequest request) {
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(request.getEvseNo());
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());
        //查询当前设备所有未结束的桩端上报告警或故障

        List<AlarmRecord> list = alertRecordDao.getWarningRecord(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            null,
            deviceNoList,
            0,
            typeList,
            null);

        List<Long> warningRecordList = getIdList(list);
        if (warningRecordList == null || warningRecordList.isEmpty()) {
            log.info("未找到告警信息>>>evseNo={}, alarmCode={}", request.getEvseNo(),
                request.getAlarmCode());
            return;
        }
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(request.getEndTime());
        alertRecordDao.updateBatchStatusById(warningRecordList, wWarningRecord);

        //发送微信模板消息
        list.forEach(e -> {
            e.setEndTime(request.getEndTime());
            dataCoreFeignClient.sendMsg(e)
                .block(Duration.ofSeconds(30L));
        });

        log.info(">>>>>桩端上报执行告警结束业务更新告警记录{}>>>>recordId={}>", request.getEvseNo(),
            JsonUtils.toJsonString(warningRecordList));
    }

    private void stopPvGtiAlarm(List<Long> warningIdList) {
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setRtStatus(PvMode.NORMAL.getCode());
        wWarningRecord.setEndTime(Calendar.getInstance().getTime());
        wWarningRecord.setWarningUpdateTime(Calendar.getInstance().getTime());
        alertRecordDao.updateBatchStatusById(warningIdList, wWarningRecord);
    }

    private void stopSrsAlarm(List<Long> warningIdList) {
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
//        wWarningRecord.setRtStatus(PvMode.NORMAL.getCode());
        wWarningRecord.setEndTime(Calendar.getInstance().getTime());
        wWarningRecord.setWarningUpdateTime(Calendar.getInstance().getTime());
        alertRecordDao.updateBatchStatusById(warningIdList, wWarningRecord);
    }

    private void stopSimAlarm(List<Long> warningIdList) {
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
//        wWarningRecord.setRtStatus(PvMode.NORMAL.getCode());
        wWarningRecord.setEndTime(Calendar.getInstance().getTime());
        wWarningRecord.setWarningUpdateTime(Calendar.getInstance().getTime());
        alertRecordDao.updateBatchStatusById(warningIdList, wWarningRecord);
    }

    private void stopEssAlarm(List<Long> warningIdList) {
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(Calendar.getInstance().getTime());
        wWarningRecord.setWarningUpdateTime(Calendar.getInstance().getTime());
        alertRecordDao.updateBatchStatusById(warningIdList, wWarningRecord);

    }


    //得到ID列表
    private List<Long> getIdList(List<AlarmRecord> wWarningRecordList) {
        List<Long> recordIdList = new ArrayList<>();
        for (AlarmRecord wWarningRecord : wWarningRecordList) {
            if (null != wWarningRecord.getWarningId()) {
                recordIdList.add(wWarningRecord.getWarningId());
            }
        }
        return recordIdList;
    }


    @Override
    public long notEndWarningRecordNum(MonitorAlarmRequest monitorAlarmRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorAlarmRequest.getEvseId());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        // 根据告警码查询桩所有未结束告警
        long count = alertRecordDao.getWarningRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorAlarmRequest.getAlarmCode() != null ? monitorAlarmRequest.getAlarmCode()
                .toString() : null, deviceNoList,
            monitorAlarmRequest.getConnectorId(), typeList, null);
        return count;
    }

    public long notEndWarningEvseRecordNum(MonitorEvseRequest monitorEvseRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorEvseRequest.getEvseNo());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());

        // 根据告警码查询桩所有未结束告警
        long count = alertRecordDao.getWarningRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorEvseRequest.getAlarmCode() != null ? monitorEvseRequest.getAlarmCode().toString()
                : null, deviceNoList,
            0, typeList, null);
        return count;
    }

    public List<AlarmRecord> getRecentPvGtiAlert(MonitorPvGtiRequest request) {
        return alertRecordDao.getRecentPvGtiAlert(request.getGwno(), request.getRecId());
    }

    public List<AlarmRecord> getRecentSrsAlert(MonitorPvGtiRequest request) {
        return alertRecordDao.getRecentSrsAlert(request.getGwno(), request.getRecId());
    }

    public List<AlarmRecord> getRecentSimAlert(MonitorPvGtiRequest request) {
        return alertRecordDao.getRecentSimAlert(request.getDno(), request.getRecId());
    }

    public List<AlarmRecord> getRecentEssAlert(String gwno, String dno, Long essEquipId) {
        return alertRecordDao.getRecentEssAlert(gwno, dno, essEquipId);
    }

    public long notEndWarningSiteCtrlRecordNum(MonitorSiteCtrlRequest monitorSiteCtrlRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorSiteCtrlRequest.getCtrlNo());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());

        // 根据告警码查询控制器生成的所有未结束告警
        long count = alertRecordDao.getWarningSiteCtrlRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorSiteCtrlRequest.getAlarmCode() != null ? monitorSiteCtrlRequest.getAlarmCode()
                .toString() : null,
            deviceNoList,
            typeList);
        return count;
    }

    @Override
    public void sendSysLeveAlarmToMassage(SysAlarmMsgRequest request) {
        // 查询告警详情
        String warningCode = request.getWarningCode();
        if (StringUtils.isBlank(warningCode)) {
            log.error("【系统级别告警】告警码不能为空。");
            return;
        }
        WarningDetailVo warningDetailQuery = new WarningDetailVo()
            .setWarningCode(warningCode);
        List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
            warningDetailQuery);
        if (CollectionUtils.isEmpty(warningDetailVos)) {
            log.error("【系统级别告警】未获取到告警详情。告警码 = {}", warningCode);
            return;
        }
        String warningName = warningDetailVos.get(0).getWarningName();
        request.setWarningName(warningName);

        // 获取所有告警码未结束告警
        List<AlarmRecord> noEndWRecordAll = alertRecordDao.getSysLeveWarningRecord(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(), warningCode, null, null);
        log.info("【系统级别告警】当前未结束告警列表 = {}", noEndWRecordAll);

        Boolean sendMsgFlag = false;
        switch (warningCode) {
            // 网管登录超时
            case SysWarnCodeContant.WARN_CODE_GW_LOGIN_TIMEOUT:
                List<SysAlarmMsgRequest.GwTimeoutPo> gwTimeoutList = request.getGwTimeoutList();
                sendMsgFlag = this.upsertGwTimeoutWarningRecord(gwTimeoutList, warningName,
                    noEndWRecordAll);
                if (CollectionUtils.isEmpty(request.getGwTimeoutList())) {
                    log.info("【登录超时告警】无登录超时网关为空，不发送告警短信");
                    return;
                }
                // 是否发送告警短信
                if (!this.validateSendSmsFlag(sendMsgFlag, request.getPhones())) {
                    return;
                }
                // 登录超时发送告警短信
                this.sendAlarmMessageService.sendGwnoTimeoutAlarmMsg(request);
                break;
            // 微服务掉线
            case SysWarnCodeContant.WARN_CODE_MICRO_APPS_UPDOWN:
                List<SysAlarmMsgRequest.AppAlert> alertsList = request.getAlerts();
                sendMsgFlag = this.upsertEurekaAppWarningRecord(alertsList, warningName,
                    noEndWRecordAll);
                if (CollectionUtils.isEmpty(request.getAlerts())) {
                    log.info("【微服务告警】无异常微服务，不发送告警短信");
                    return;
                }
                // 是否发送告警短信
                if (!this.validateSendSmsFlag(sendMsgFlag, request.getPhones())) {
                    return;
                }
                this.sendAlarmMessageService.sendMicroServiceAlarmMsg(request);
                break;
        }
    }

    /**
     * 登录超时告警数据插入数据库
     *
     * @throws DcServiceException
     */
    private Boolean upsertGwTimeoutWarningRecord(List<SysAlarmMsgRequest.GwTimeoutPo> gwTimeoutList,
        String warningName,
        List<AlarmRecord> noEndWRecordAll) throws DcServiceException {
        AtomicReference<Boolean> sendMsgFlag = new AtomicReference<>(false);
        // 网管登录超时持续告警告警ID列表
        List<Long> warnIds = new ArrayList<>();
        gwTimeoutList.forEach(gwTimeoutPo -> {
            if (!gwTimeoutPo.getGwno().startsWith("G")) {    // 网关编号为G开头的云端网关,其他的都忽略
                return;
            }
            // 获取网关未结束登录超时告警
            List<AlarmRecord> gwTimeoutWRecord = alertRecordDao.getSysLeveWarningRecord(
                AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
                SysWarnCodeContant.WARN_CODE_GW_LOGIN_TIMEOUT, gwTimeoutPo.getGwno(), null);

            if (CollectionUtils.isNotEmpty(gwTimeoutWRecord)) {
                log.info("【网关登录超时】持续告警。gwno = {}", gwTimeoutPo.getGwno());
                AlarmRecord wWarningRecord = gwTimeoutWRecord.get(0);
                alertRecordDao.updateBatchStatusById(
                    Arrays.asList(wWarningRecord.getWarningId()), wWarningRecord);
                warnIds.add(wWarningRecord.getWarningId());
            } else {
                log.info("【网关登录超时】新增告警。gwno = {}", gwTimeoutPo.getGwno());
                AlarmRecord wWarningRecord = new AlarmRecord();
                wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
                wWarningRecord.setWarningCode(AlarmTypeEnum.ALARM_GW_LOGIN_TIME_OUT.getValue());
                wWarningRecord.setWarningName(warningName);
                wWarningRecord.setWarningType(AlarmEventTypeEnum.ALARM_SYS_WARNING.getValue());

                wWarningRecord.setStartTime(new Date());
                wWarningRecord.setWarningUpdateTime(new Date());
                wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
                // 告警等级
                wWarningRecord.setLevel(AlarmTypeEnum.ALARM_GW_LOGIN_TIME_OUT.getLevel());
                String warningInstructions = String.format("场站数: %s,最后登录时间: %s",
                    gwTimeoutPo.getSiteNum(),
                    gwTimeoutPo.getLoginTime());
                wWarningRecord.setWarningInstructions(warningInstructions);
                // 商户id 管理员可见
                wWarningRecord.setBusinessId("0");
                wWarningRecord.setGwno(gwTimeoutPo.getGwno());

                List<AlarmRecord> wWarningRecordList = new ArrayList<>();
                wWarningRecordList.add(wWarningRecord);
                alertRecordDao.insertWWarningRecordsList(wWarningRecordList);
                sendMsgFlag.set(true);
            }
        });
        // 处理已结束的告警记录
        this.dualEndWarningRecord(warnIds, noEndWRecordAll);
        return sendMsgFlag.get();
    }

    /**
     * 登录超时告警数据插入数据库
     *
     * @throws DcServiceException
     */
    private Boolean upsertEurekaAppWarningRecord(List<SysAlarmMsgRequest.AppAlert> alerts,
        String warningName,
        List<AlarmRecord> noEndWRecordAll) throws DcServiceException {
        AtomicReference<Boolean> sendMsgFlag = new AtomicReference<>(false);
        List<Long> warnIds = new ArrayList<>();
        alerts.forEach(alert -> {
            // 获取网关未结束登录超时告警
            List<AlarmRecord> alertWRecord = alertRecordDao.getSysLeveWarningRecord(
                AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
                SysWarnCodeContant.WARN_CODE_MICRO_APPS_UPDOWN, null, alert.getAppName());

            if (CollectionUtils.isNotEmpty(alertWRecord)) {
                log.info("【微服务告警】持续告警。alertWRecord = {}", alertWRecord);
                AlarmRecord wWarningRecord = alertWRecord.get(0);
                alertRecordDao.updateBatchStatusById(
                    Arrays.asList(wWarningRecord.getWarningId()), wWarningRecord);
                warnIds.add(wWarningRecord.getWarningId());
            } else {
                log.info("【微服务告警】新增告警。alert = {}", alert);
                AlarmRecord wWarningRecord = new AlarmRecord();
                wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
                wWarningRecord.setWarningCode(AlarmTypeEnum.ALARM_MICRO_APPS_UPDOWN.getValue());
                wWarningRecord.setWarningName(warningName);
                wWarningRecord.setWarningType(AlarmEventTypeEnum.ALARM_SYS_WARNING.getValue());

                wWarningRecord.setStartTime(new Date());
                wWarningRecord.setWarningUpdateTime(new Date());
                wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
                // 告警等级
                wWarningRecord.setLevel(AlarmTypeEnum.ALARM_MICRO_APPS_UPDOWN.getLevel());

                String warningInstructions = String.format("应用名: %s,实例ID: %s,告警提示: %s",
                    alert.getAppName(),
                    alert.getInstanceId(), alert.getMessage());
                wWarningRecord.setWarningInstructions(warningInstructions);
                // 商户id 管理员可见
                wWarningRecord.setBusinessId("0");
                wWarningRecord.setAppName(alert.getAppName());

                List<AlarmRecord> wWarningRecordList = new ArrayList<>();
                wWarningRecordList.add(wWarningRecord);
                alertRecordDao.insertWWarningRecordsList(wWarningRecordList);
                sendMsgFlag.set(true);
            }
        });
        // 处理已结束的告警记录
        this.dualEndWarningRecord(warnIds, noEndWRecordAll);
        return sendMsgFlag.get();
    }

    /**
     * 处理已结束的告警记录
     *
     * @param warnIds 持续告警记录ID列表
     */
    public void dualEndWarningRecord(List<Long> warnIds, List<AlarmRecord> noEndWRecordAll) {

        // 未上报的网关认为已登录 修改为结束状态
        if (CollectionUtils.isNotEmpty(noEndWRecordAll)) {
            if (CollectionUtils.isNotEmpty(warnIds)) {
                noEndWRecordAll = noEndWRecordAll.stream()
                    .filter(g -> !warnIds.contains(g.getWarningId())).collect(Collectors.toList());
            }
            noEndWRecordAll.forEach(g -> {
                AlarmRecord wWarningRecord = new AlarmRecord();
                wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
                wWarningRecord.setEndTime(new Date());
                alertRecordDao.updateBatchStatusById(Arrays.asList(g.getWarningId()),
                    wWarningRecord);
            });
        }
    }

    public List<AlarmRecord> evseErrorAlarms(Date startTime,
        Date endTime,
        Integer plugId,
        String evseNo,
        String siteId) {
        return alertRecordDao.evseErrorAlarms(startTime, endTime, plugId, evseNo, siteId);
    }

    @Override
    public void sendTransFinshAlarmToMassage(TransFinshAlarmRequest request) {
        // 查询告警详情
        String warningCode = request.getWarningCode();
        if (StringUtils.isBlank(warningCode)) {
            log.error("【资金周转告警】告警码不能为空。");
            return;
        }
        WarningDetailVo warningDetailQuery = new WarningDetailVo()
            .setWarningCode(warningCode);
        List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
            warningDetailQuery);
        if (CollectionUtils.isEmpty(warningDetailVos)) {
            log.error("【资金周转告警】未获取到告警详情。告警码 = {}", warningCode);
            return;
        }
        String warningName = warningDetailVos.get(0).getWarningName();
        request.setWarningName(warningName);

        log.info("【资金周转告警】新增告警。tradeNo = {},bankTradeNo = {}", request.getTradeNo(),
            request.getBankTradeNo());
        // 新增告警数据
        AlarmRecord wWarningRecord = new AlarmRecord();
        wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
        wWarningRecord.setWarningCode(AlarmTypeEnum.ALARM_TRANS_FINISH.getValue());
        wWarningRecord.setWarningName(warningName);
        wWarningRecord.setWarningType(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());

        wWarningRecord.setStartTime(new Date());
        wWarningRecord.setWarningUpdateTime(new Date());
        wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
        wWarningRecord.setEndTime(new Date());
        wWarningRecord.setError(request.getError());
        // 告警等级
        wWarningRecord.setLevel(AlarmTypeEnum.ALARM_TRANS_FINISH.getLevel());
        String warningInstructions = String.format("消息ID：%s,交易号：%s,银行交易号：%s,商户ID：%s",
            request.getMsgId(), request.getTradeNo(), request.getBankTradeNo(),
            request.getCommId());
        wWarningRecord.setWarningInstructions(warningInstructions);

        List<AlarmRecord> wWarningRecordList = new ArrayList<>();
        wWarningRecordList.add(wWarningRecord);
        alertRecordDao.insertWWarningRecordsList(wWarningRecordList);
        // 发送告警短信
        this.sendAlarmMessageService.sendTranFinishAlarmMsg(request);

    }

    public Boolean validateSendSmsFlag(Boolean sendSmsFlag, List<String> phones) {
        if (!sendSmsFlag) {
            log.info("持续告警,无新增告警记录，不发送告警消息");
            return false;
        }
        if (CollectionUtils.isEmpty(phones)) {
            log.info("手机号列表为空，不发送告警短信");
            return false;
        }
        return true;
    }
}
