package com.cdz360.iot.monitor.entity.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Schema(description = "储能设备告警记录表")
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Document(collection = "ess_alarms")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EssAlarmPo extends com.cdz360.base.model.es.po.EssAlarmPo {


    @Id
    public Long getId() {
        return super.getId();
    }

}
