package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 桩端上报的告警消息
 *
 * <AUTHOR> Create on 2018.9.13 19:46 Update ben 2019.04.17
 */
@Data
@Accessors(chain = true)
public class MonitorAlarmRequest implements Serializable {


    /**
     * 充电桩编码deviceId
     */
    private String boxCode;
    /**
     * 充电桩桩号
     */
    private String evseId;
    /**
     * 充电桩名称
     */
    private String evseName;
    /**
     * 电桩型号, 如: G4-001
     */
    private String modelName;
    /**
     * 桩固件(软件)版本
     */
    private String firmwareVer;
    /**
     * 可以通过boxCode+connectorId+告警编码确定唯一一条告警记录<br> 当且仅当告警消息为充电接口告警时，该字段才有值
     */
    private Integer connectorId;

    /**
     * 业务端的告警代码 故障：1-255 告警：2000+ 业务端故障：1000+
     */
    private Integer alarmCode;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     */
    private Integer infoType;

    /**
     * 告警开始时间
     */
    private Date startTime;

    /**
     * 告警结束时间
     */
    private Date endTime;

    /**
     * 告警消息体
     */
    private String jsonAlarmInfo;

    /**
     * 异常问题编码
     */
    private Integer errorCode;
    /**
     * 充电枪异常问题编码,告警码
     */
    private Integer alertCode;
    /**
     * 桩或枪错误描述
     */
    private String error;
    /**
     * 桩或枪温度异常温度数
     */
    private Integer temp;
    /**
     * 枪状态
     */
    private PlugStatus plugStatus;

    /**
     * 枪名称
     */
    private String plugName;

    private String siteId;

    /**
     * 场站商户ID
     */
    private Long siteCommId;

    /**
     * 场站名称
     */
    private String siteName;

    private String linkId;

    private String sourceNo;

    private String orderNo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
