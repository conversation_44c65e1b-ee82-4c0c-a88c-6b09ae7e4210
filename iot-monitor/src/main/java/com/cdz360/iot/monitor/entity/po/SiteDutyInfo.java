package com.cdz360.iot.monitor.entity.po;

import lombok.Data;

import java.io.Serializable;

/**
 * @Classname SiteDutyInfo
 * @Description 值班人员信息
 * @Date 2019/7/5 14:13
 * @Created by <PERSON><PERSON>ei
 * @Email <EMAIL>
 */
@Data
public class SiteDutyInfo implements Serializable {

    /**
     * 值班表Id
     */
    private String dutyId;

    /**
     * 站点Id
     */
    private String siteId;

    /**
     * 周几（0是周日,1-6 为周一到周六）
     */
    private String week;

    /**
     * 0白班，1夜班
     */
    private String day;

    /**
     * 值班人姓名
     */
    private String name;

    /**
     * 值班人电话
     */
    private String tel;

    /**
     * 有效标志
     */
    private String yxBz;

    /**
     * 值班开始时间（HH：mm）
     */
    private String startTime;

    /**
     * 值班结束时间（HH：mm）
     */
    private String endTime;
}
