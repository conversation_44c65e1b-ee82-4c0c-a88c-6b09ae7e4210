package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Classname SysLeveAlarmMsgRequest
 * @Description 系统级别告警请求（微服务DOWN掉 网关登录超时）
 * <AUTHOR>
 * @Date 2019/8/20 17:23
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysAlarmMsgRequest extends BaseAlarmMsgRequest {
    /**
     * 网关登录超时告警
     */
    List<GwTimeoutPo> gwTimeoutList;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GwTimeoutPo extends BaseObject {
        /**
         * 网关编号
         */
        private String gwno;
        /**
         * 场站数目
         */
        private int siteNum;
        /**
         * 最后登录时间
         */
        private String loginTime;
    }

    /**
     * 微服务异常告警
     */
    List<AppAlert> alerts;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class AppAlert extends BaseObject {
        /**
         * 应用名
         */
        private String appName;
        /**
         * 实例ID
         */
        private String instanceId;
        /**
         * 信息
         */
        private String message;
    }
}

