package com.cdz360.iot.monitor.listener;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.event.DcBaseQEvent;
import com.cdz360.data.sync.event.EssAlarmEvent;
import com.cdz360.data.sync.event.EssAlarmEvent2;
import com.cdz360.data.sync.event.EvseInfoEvent;
import com.cdz360.data.sync.event.MqEventSubType;
import com.cdz360.data.sync.event.PlugInfoEvent;
import com.cdz360.data.sync.event.PvGtiInfoEvent;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.monitor.entity.request.MonitorAlarmRequest;
import com.cdz360.iot.monitor.entity.request.MonitorEvseRequest;
import com.cdz360.iot.monitor.entity.request.MonitorPvGtiRequest;
import com.cdz360.iot.monitor.service.AlarmRecordDs;
import com.cdz360.iot.monitor.service.AlarmServiceImpl;
import com.cdz360.iot.monitor.service.EssAlarmService;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = IotConstants.MQ_IOT_QUEUE_ESS_INFO_MONITOR)
public class IotEventListener {

    /**
     * 忽略部分桩上报的故障码,如 BMS 主动停充
     */
    private static final Set<Integer> INGORE_EVSE_ALERT_CODES = Set.of(47   // BMS 主动停充
    );
    @Autowired
//    private AlarmService alarmService;
    private AlarmServiceImpl alarmService;
    @Autowired
    private AlarmRecordDs wWarningRecordService;
    @Autowired
    private EssAlarmService essAlarmService;

    @RabbitHandler
    public void plugInfoListener(String msg) {
        log.info(">> msg = {}", msg);
        try {

            JsonNode json = JsonUtils.fromJson(msg);
            String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();

            if (MqEventSubType.MQ_PLUG.name().equals(subMqType)) {

                PlugInfoEvent event = JsonUtils.fromJson(msg, PlugInfoEvent.class);
                PlugMqDto plugVo = event.getData();
                if (plugVo != null && plugVo.getErrorCode() != null
                    && INGORE_EVSE_ALERT_CODES.contains(plugVo.getErrorCode())) {
                    log.info("<< 忽略告警,不做处理. plugNo= {}, errorCode= {}, errorMsg= {}",
                        plugVo.getPlugNo(),
                        plugVo.getErrorCode(),
                        plugVo.getErrorMsg());
                    return;
                }

                MonitorAlarmRequest request = new MonitorAlarmRequest()
                    .setEvseId(plugVo.getEvseNo())
                    .setEvseName(plugVo.getEvseName())
                    .setConnectorId(plugVo.getIdx())
                    .setAlertCode(plugVo.getAlertCode())
                    .setErrorCode(plugVo.getErrorCode())
                    .setTemp(plugVo.getTemperature())
                    .setError(plugVo.getErrorMsg())
                    .setPlugStatus(plugVo.getStatus())
                    .setSiteId(plugVo.getSiteId())
                    .setSiteCommId(plugVo.getSiteCommId())
                    .setSiteName(plugVo.getSiteName())
                    .setModelName(plugVo.getModelName())
                    .setFirmwareVer(plugVo.getFirmwareVer())
                    .setLinkId(event.getLinkId())
                    .setOrderNo(plugVo.getOrderNo())
                    .setPlugName(plugVo.getName())
                    .setSourceNo(event.getCtrlNo());

                // 告警短信，处理告警
                log.info("枪-处理告警开始。request = {}", request);
                this.alarmService.dualAlarmChargeMsg(request, plugVo);
                log.info("枪-处理告警结束。request = {}", request);

            } else if (MqEventSubType.MQ_PV_GTI.name().equals(subMqType)) {
                log.info("处理告警-逆变器");
                PvGtiInfoEvent event = JsonUtils.fromJson(msg, PvGtiInfoEvent.class);
                PvGtiVo pvGtiVo = event.getData();
                MonitorPvGtiRequest request = new MonitorPvGtiRequest()
                    .setGwno(pvGtiVo.getGwno())
                    .setGwName(pvGtiVo.getGwName())
                    .setPvName(pvGtiVo.getName())
                    .setRecId(pvGtiVo.getRecId())
                    .setSiteId(pvGtiVo.getSiteId())
                    .setSiteName(pvGtiVo.getSiteName())
                    .setSiteCommId(pvGtiVo.getSiteCommId())
                    .setRtStatus(pvGtiVo.getRtStatus())
                    .setErrorCodeList(pvGtiVo.getErrorCodeList());
                log.info("逆变器-处理告警开始。request = {}", request);
                this.alarmService.dualAlarmPvGtiMsg(request);
                log.info("逆变器-处理告警结束。request = {}", request);

                // 同一状态忽略
            } else if (MqEventSubType.MQ_SRS.name().equals(subMqType)) {
//            else if (BizConstants.MQ_EVENT_SUB_TYPE_SRS.equals(subMqType)) {
                log.info("处理告警-辐射仪");
                String data = json.get("data").toString();
                PvGtiVo srsVo = JsonUtils.fromJson(data, PvGtiVo.class);
                MonitorPvGtiRequest request = new MonitorPvGtiRequest()
                    .setGwno(srsVo.getGwno())
                    .setGwName(srsVo.getGwName())
                    .setPvName(srsVo.getName())
                    .setRecId(srsVo.getRecId())
                    .setDno(srsVo.getDno())
                    .setSiteId(srsVo.getSiteId())
                    .setSiteName(srsVo.getSiteName())
                    .setSiteCommId(srsVo.getSiteCommId())
//                        .setRtStatus(srsVo.getRtStatus())
                    .setErrorCodeList(srsVo.getErrorCodeList());
                log.info("辐射仪-处理告警开始。request = {}", request);
                this.alarmService.dualAlarmSrsMsg(request);
                log.info("辐射仪-处理告警结束。request = {}", request);

                // 同一状态忽略
            } else if (MqEventSubType.MQ_ESS_ALARM.name()
                .equals(subMqType)) {  // 此处仅户储, 工商储已挪到 MQ_ESS_ALARM2
                EssAlarmEvent event = JsonUtils.fromJson(msg, EssAlarmEvent.class);
                essAlarmService.essAlarm(event.getData());
            } else if (MqEventSubType.MQ_ESS_ALARM2.name().equals(subMqType)) {
                // 处理工商储故障告警
                EssAlarmEvent2 event = JsonUtils.fromJson(msg, EssAlarmEvent2.class);
                essAlarmService.processEssAlarms(event.getData());
            }
//            else if (MqEventSubType.MQ_ESS.name().equals(subMqType)) {
//                log.info(">>>>>>储能ESS告警<<<<<<");
//                EssInfoEvent event = JsonUtils.fromJson(msg, EssInfoEvent.class);
//                EssVo essVo = event.getData();
//                MonitorEssRequest request = new MonitorEssRequest()
//                    .setGwno(essVo.getGwno())
//                    .setGwName(essVo.getGwName())
//                    .setDno(essVo.getDno())
//                    .setSn(essVo.getSn())
//                    .setName(essVo.getName())
//                    .setErrorList(JsonUtils.fromJson(
//                        JsonUtils.toJsonString(essVo.getErrorList()), new TypeReference<>() {
//                        }))
//                    .setSiteId(essVo.getSiteId())
//                    .setSiteName(essVo.getSiteName())
//                    .setSiteCommId(essVo.getSiteCommId());
//
//                if (EssEquipType.EMS == essVo.getEssEquipType()) {
//                    this.alarmService.processEssAlarms(essVo);
//                } else {
//                    log.info("储能ESS-处理告警开始。request = {}", request);
//                    this.alarmService.dualAlarmEssMsg(request);
//                    log.info("储能ESS-处理告警结束。request = {}", request);
//                }
//            }
//            else if (MqEventSubType.MQ_CTRL.name().equals(subMqType)) {
//                log.info("处理告警-场站控制器");
//
//                IotCtrlInfoEvent iotCtrlInfoEvent = JsonUtils.fromJson(msg, IotCtrlInfoEvent.class);
//                SiteCtrlVo siteCtrlVo = iotCtrlInfoEvent.getData();
//
//                MonitorSiteCtrlRequest request = new MonitorSiteCtrlRequest()
//                    .setCtrlNo(siteCtrlVo.getCtrlNo())
//                    .setCtrlName(siteCtrlVo.getName())
//                    .setAlertCode(siteCtrlVo.getAlertCode())
//                    .setErrorCode(siteCtrlVo.getErrorCode())
//                    .setPwrTemp(siteCtrlVo.getPwrTemp())
//                    .setLoadRatio(siteCtrlVo.getLoadRatio())
//                    .setSiteId(siteCtrlVo.getSiteId())
//                    .setSiteCommId(siteCtrlVo.getSiteCommId())
//                    .setSiteName(siteCtrlVo.getSiteName())
//                    .setStatus(siteCtrlVo.getStatus())
//                    .setLinkId(siteCtrlVo.getLinkId());
//
//                log.info("控制器-处理告警开始。request = {}", request);
//                this.alarmService.dualAlarmSiteCtrlMsg(request);
//                log.info("控制器-处理告警结束。request = {}", request);
//
//            }
            else if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {
                log.info("处理告警-桩");

                EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
                EvseVo evseVo = evseInfoEvent.getData();

                if (StringUtils.isNotBlank(evseInfoEvent.getLinkId())) {
                    MonitorEvseRequest request = new MonitorEvseRequest()
                        .setEvseNo(evseVo.getEvseNo())
                        .setEvseName(evseVo.getName())
                        .setStatus(evseVo.getStatus())
                        .setCtrlNo(evseInfoEvent.getCtrlNo())
                        .setErrorCode(evseVo.getErrorCode())
                        .setAlertCode(evseVo.getAlertCode())
                        .setSiteId(evseVo.getSiteId())
                        .setSiteCommId(evseVo.getSiteCommId())
                        .setSiteName(evseVo.getSiteName())
                        .setLinkId(evseInfoEvent.getLinkId());

                    //TODO 处理告警-桩
                    log.info("桩-处理告警开始。request = {}", request);
                    this.alarmService.dualAlarmEvseMsg(request);
                    log.info("桩-处理告警结束。request = {}", request);
                } else if (evseVo.getProtocolVer() >= IotConstants.PROTOCOL_VERSION_370
                    && IotEvent.CREATE == evseInfoEvent.getEventType()) {

                    // 3.7协议桩重新注册时，结束上次离线的告警
                    this.wWarningRecordService.clearRecentlyAlarmConnectLost(evseVo.getEvseNo(),
                        evseVo.getRegisterReason());
                }

            } else if (MqEventSubType.MQ_SIM.name().equals(subMqType)) {
                log.info("处理告警-SIM卡");
                String data = json.get("data").toString();
                PvGtiVo simVo = JsonUtils.fromJson(data, PvGtiVo.class);
                MonitorPvGtiRequest request = new MonitorPvGtiRequest()
//                        .setGwno(simVo.getGwno())
//                        .setGwName(simVo.getGwName())
//                        .setPvName(simVo.getName())
                    .setRecId(simVo.getRecId())
                    .setDno(simVo.getDno())
                    .setSiteId(simVo.getSiteId())
                    .setSiteName(simVo.getSiteName())
                    .setSiteCommId(simVo.getSiteCommId())
//                        .setRtStatus(simVo.getRtStatus())
                    .setErrorCodeList(simVo.getErrorCodeList());
                log.info("SIM卡-处理告警开始。request = {}", request);
                this.alarmService.dualAlarmSimMsg(request);
                log.info("SIM卡-处理告警结束。request = {}", request);

                // 同一状态忽略
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.warn("收到未识别的消息. msg = {}", msg);
        }

        log.info("<<");
    }
}
