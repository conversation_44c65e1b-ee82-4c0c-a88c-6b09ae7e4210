package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 发送短信的枚举类型
 *
 * <AUTHOR>
 * Create on 2018/10/29 14:46
 */
@Getter
public enum MessageTypeEnum {
    POWER_OFF_ALARM(1, "断电告警短信"),
    CONNECTOR_ABNORMAL_ALARM(2, "插座异常告警短信"),
    SMOKE_DETECTOR_ALARM(3, "烟感告警短信"),
    ;

    private int value;
    private String label;

    MessageTypeEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

}

