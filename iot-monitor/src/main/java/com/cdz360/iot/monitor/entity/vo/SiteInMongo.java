package com.cdz360.iot.monitor.entity.vo;

import com.cdz360.iot.model.alarm.po.AlarmRecordPo;
import com.cdz360.iot.model.site.po.SitePo;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 告警记录表
 *
 * <AUTHOR>
 * Create on 2018.8.13 9:44
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Document(collection = "site_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiteInMongo extends SitePo implements Serializable {



}
