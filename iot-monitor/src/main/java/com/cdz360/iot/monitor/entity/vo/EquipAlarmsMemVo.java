package com.cdz360.iot.monitor.entity.vo;

import com.cdz360.base.model.iot.type.EssEquipType;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 在内存中的故障告警列表
 */
@Data
@Accessors(chain = true)
public class EquipAlarmsMemVo {
    private EssEquipType equipType;

    private String dno;

    /**
     * key: 告警码
     */
    private Map<Long, AlarmRecordMemVo> alarms = new ConcurrentHashMap<>();
}
