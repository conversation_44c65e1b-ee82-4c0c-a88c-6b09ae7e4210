package com.cdz360.iot.monitor.entity.result;

//
///**
// * 告警记录
// *
// * @ClassName WarningRecordVo
// * <AUTHOR>
// * @Description
// * @Date 2019.4.16
// */
//@Data
//public class WarningRecordVo {
//
//
//    private Long id;
//    /**
//     * 告警编码
//     */
//    private String warningCode;
//    /**
//     * 告警名
//     */
//    private String warningName;
//    /**
//     * 设备id
//     */
//    private String boxCode;
//    /**
//     * 充电接口序号
//     */
//    private Integer connectorId;
//    /**
//     * 站点id
//     */
//    private String siteId;
//    /**
//     * 站点名称
//     */
//    private String siteName;
//    /**
//     * 设备代理商(商户)ID
//     */
//    private String businessId;
//    /**
//     * 告警等级
//     */
//    private Integer level;
//    /**
//     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
//     */
//    private Integer warningType;
//    /**
//     * 告警开始时间
//     */
//    private Date startTime;
//    /**
//     * 告警结束时间
//     */
//    private Date endTime;
//    /**
//     * 告警最后上报时间
//     */
//    private Date warningUpdateTime;
//    /**
//     * 数据创建时间
//     */
//    private String createTime;
//    /**
//     * 数据更新时间
//     */
//    private String updateTime;
//    /**
//     * 告警状态（0未结束,1自动结束,2手动结束)
//     */
//    private Integer status;
//    /**
//     * 告警描述说明
//     */
//    private String detail;
//    /**
//     * 备注
//     */
//    private String remark;
//    /**
//     * 持续时长,单位秒
//     */
//    private Integer duration;
//    /**
//     * 告警描述id
//     */
//    private Long warningDetailId;
//
//}
