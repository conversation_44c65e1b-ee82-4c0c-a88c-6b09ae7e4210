package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 返回给前端充电桩状态
 *
 * <AUTHOR>
 * @Date Create on 2018/8/2 000217:27
 */
@Getter
public enum BoxStatusVoEnum {

    STATUS_ONLINE(0, "在线", BoxStatusEnum.STATUS_ONLINE),
    STATUS_UNACTIVITY(10, "离线", BoxStatusEnum.STATUS_UNACTIVITY),
    ;

    private int value;
    private String label;
    private BoxStatusEnum boxStatusEnum;

    BoxStatusVoEnum(int value, String label, BoxStatusEnum boxStatusEnum) {
        this.value = value;
        this.label = label;
        this.boxStatusEnum = boxStatusEnum;
    }

}
