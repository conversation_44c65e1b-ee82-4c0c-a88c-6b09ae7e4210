package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.model.iot.type.EssEquipType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 储能ESS上报告警信息
 */
@Data
@Accessors(chain = true)
@Schema(title = "储能信息")
public class MonitorEssRequest {

    /**
     * 微网控制器编号
     */
    private String gwno;

    /**
     * 微网控制器名称
     */
    private String gwName;

    /**
     * 储能ESS唯一编号
     */
    private String dno;

    /**
     * 设备名称
     */
    private String name;

    /**
     * EMS设备的SN
     */
    private String sn;
    @Schema(
        title = "错误代码",
        description = "一次可能出现多种故障"
    )

    private List<MonitorDevRequest> errorList;

    /**
     * 场站所属商户
     */
    private Long siteCommId;

    /**
     * 场站ID
     */
    private String siteId;

    /**
     * 场站名称
     */
    private String siteName;

    /**
     * 告警开始时间
     */
    private Date startTime;

    /**
     * 告警结束时间
     */
    private Date endTime;

    /**
     * 异常问题编码
     */
    private String warningCode;

    /**
     * 告警名称
     */
    private String warningName;

    /**
     * 设备ID
     */
    private Long equipId;

    private EssEquipType equipType;
    /**
     * 下属设备名称
     */
    private String equipName;

    /**
     * 电池簇号 “第N簇电池信息”
     */
    private Long clusterNo;
    /**
     * 电池组号
     */
    private Long packNo;
}

