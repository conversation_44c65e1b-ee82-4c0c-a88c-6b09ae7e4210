package com.cdz360.iot.monitor.entity.request;
//
//import com.chargerlink.device.common.param.BaseRequest;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.List;
//
///**
// * 站点列表查询
// *
// * <AUTHOR>
// * @date Create on 2018/7/31 14:00
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class SiteRequest extends BaseRequest {
//    /**
//     * 商户ID
//     */
//    private Long commercialId;
//    /**
//     * 商户ID列表
//     */
//    private List<Long> commercialIds;
//    /**
//     * 省编码
//     */
//    private String province;
//    /**
//     * 市编码
//     */
//    private String city;
//    /**
//     * 地理位置hash值
//     */
//    private String geoHash;
//    /**
//     * 站点状态
//     */
//    private Integer siteStatus;
//    /**
//     * 账号id
//     */
//    private Long merchantId;
//
//}
