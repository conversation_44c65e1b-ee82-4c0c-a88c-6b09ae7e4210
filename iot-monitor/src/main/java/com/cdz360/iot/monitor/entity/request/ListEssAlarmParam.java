package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取储能设备告警参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEssAlarmParam extends BaseListParam {

    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType deviceType;

    /**
     * @deprecated 使用 equipType i替换
     */
    @Deprecated
    @Schema(description = "子设备类型(告警目标设备)")
    private WarnSubDeviceType subDeviceType;

    @Schema(description = "子设备类型(告警目标设备)")
    private EssEquipType equipType;

    @Schema(description = "子设备告警对象编号或名称(告警目标设备)")
    private String subTargetNoOrNameLike;

    @Schema(description = "查询设备编号列表(不传则认为汇总当前用户所有设备)")
    @JsonInclude(Include.NON_NULL)
    private List<String> dnoList;

    @Schema(description = "告警编号")
    private Long warningId;

    @Schema(description = "告警发生时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter2 happenTimeFilter;

    @Schema(description = "告警结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter2 stopTimeFilter;

    @Schema(description = "告警对象编号或名称")
    private String targetNoOrNameLike;

    @Schema(description = "告警代码")
    private Long warnCode;

    @Schema(description = "告警编码")
    private List<String> codeList;

    @Schema(description = "告警代码列表")
    private List<Long> warnCodeList;

    @Schema(description = "告警名称(支持模糊)")
    private String warnNameLike;

    @Schema(description = "告警代码/名称(支持模糊)")
    private String warnCodeOrNameLike;

    @Schema(description = "告警状态（0：未结束，进行中,1：自动结束,2：手动结束）")
    private List<AlarmStatusEnum> statusList;

    @Schema(description = "补充说明")
    private String noteLike;

    @Schema(description = "所属国家地区代码(关联用户)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    @JsonInclude(Include.NON_NULL)
    private List<String> countryCodeList;

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;
}
