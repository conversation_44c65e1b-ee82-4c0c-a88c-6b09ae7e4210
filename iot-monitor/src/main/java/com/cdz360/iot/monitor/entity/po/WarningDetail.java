package com.cdz360.iot.monitor.entity.po;

import java.util.Date;
import lombok.Data;

/**
 * 告警描述说明配置表
 *
 * @ClassName WarningDetail
 * <AUTHOR>
 * @Description
 * @Date 2019.4.16
 */
@Data
//@TableName("w_warning_detail")
public class WarningDetail {

    //    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 告警编码
     */
//    @TableField(value = "warning_code")
    private String warningCode;
    /**
     * 告警名
     */
//    @TableField(value = "warning_name")
    private String warningName;
    /**
     * 告警处理说明
     */
//    @TableField(value = "warning_instructions")
    private Integer warningInstructions;
    /**
     * 告警等级
     */
//    @TableField(value = "warning_level")
    private Integer warningLevel;
    /**
     * 创建时间
     */
//    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
//    @TableField(value = "update_time")
    private Date updateTime;

}
