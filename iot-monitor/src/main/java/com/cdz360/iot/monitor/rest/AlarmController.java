package com.cdz360.iot.monitor.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.model.alarm.param.GetAlarmListParam;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.po.WWarningSummaryRecord;
import com.cdz360.iot.monitor.entity.request.SysAlarmMsgRequest;
import com.cdz360.iot.monitor.entity.request.TransFinshAlarmRequest;
import com.cdz360.iot.monitor.entity.request.UpdateWarningYwParam;
import com.cdz360.iot.monitor.entity.request.WarningBiParam;
import com.cdz360.iot.monitor.entity.request.WarningSummaryParam;
import com.cdz360.iot.monitor.service.AlarmServiceImpl;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 告警控制类
 *
 * <AUTHOR> Create on 2018.8.9 11:37
 */
@Slf4j
@RestController
@RequestMapping("/api/alarm")
public class AlarmController //extends BaseController
{

    @Autowired
    private AlarmServiceImpl alarmService;


    @PostMapping(value = "/getAlarmList")
    public ListResponse<AlarmRecord> getAlarmList(
        @RequestBody GetAlarmListParam paramIn) {
        log.debug("获取告警数据列表 paramIn: {}", JsonUtils.toJsonString(paramIn));
        return RestUtils.buildListResponse(alarmService.getAlarmList(paramIn));
    }


    /**
     * 系统级别告警 网关登录超时 微服务掉线
     */
    @PostMapping(value = "/sendSysLeveAlarm")
    public BaseResponse sendSysLeveAlarm(@RequestBody SysAlarmMsgRequest request) {
        log.info("发送系统级别告警【开始】。request= {}", request);
        alarmService.saveSysLeveAlarmMsg(request);
        log.info("发送系统级别告警【结束】");
        return new BaseResponse();
    }

    /**
     * 资金周转告警
     */
    @PostMapping(value = "/sendTransFinshAlarm")
    public BaseResponse sendTransFinshAlarm(@RequestBody TransFinshAlarmRequest request) {
        log.info("发送资金周转告警【开始】。request= {}", request);
        alarmService.sendTransFinshAlarm(request);
        log.info("发送资金周转告警【结束】");
        return new BaseResponse();
    }

    /**
     * 获取枪头告警信息
     */
    @PostMapping(value = "/evseErrorAlarm")
    public ListResponse<AlarmRecord> evseErrorAlarm(@RequestParam("startTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @RequestParam("endTime")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
        @RequestParam(name = "plugId", required = false) Integer plugId,
        @RequestParam(name = "evseNo", required = false) String evseNo,
        @RequestParam("siteId") String siteId) {
        log.info("siteId = {}", siteId);
        List<AlarmRecord> ret = alarmService.evseErrorAlarms(startTime, endTime, plugId, evseNo,
            siteId);
        return new ListResponse<>(ret);
    }

    /**
     * 提供device-task生成长时间离线告警
     */
    // @Override
    @PostMapping("/alarmOffline8h")
    public ObjectResponse alarmOffline8h() {
        log.info("提供device-task生成长时间离线告警");
        alarmService.alarmOffline8h();
        return new ObjectResponse<>(true);
    }


    /**
     * 根据条件获取告警列表
     */
    @PostMapping(value = "/getWarningBiList")
    public ListResponse<AlarmRecord> getWarningBiList(
        @RequestBody WarningBiParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        ListResponse<AlarmRecord> res = alarmService.getWarningBiList(param);
        log.info("<<");
        return res;
    }

    /**
     * 生成运维工单，通过桩获取最近未完成的告警信息
     */
    @PostMapping(value = "/getEvseWarningList")
    public ListResponse<AlarmRecord> getEvseWarningList(
        @RequestBody WarningBiParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return alarmService.getEvseWarningList(param);
    }

    /**
     * 获取桩最近告警信息,同类则去重
     */
    @PostMapping(value = "/getRecentWarning")
    public ObjectResponse<Map<String, List<AlarmRecord>>> getRecentWarning(
        @RequestBody WarningBiParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            throw new DcServiceException("桩列表不能为空");
        }
        return alarmService.getRecentWarning(param);
    }

    /**
     * 根据条件获取告警分类列表
     */
    @PostMapping(value = "/getWarningSummaryList")
    public ListResponse<WWarningSummaryRecord> getWarningSummaryList(
        @RequestBody WarningSummaryParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return alarmService.getWarningSummaryList(param);
    }

    /**
     * 检查查询结果是否超过10万条
     */
    @PostMapping(value = "/checkWarningBiListCount")
    public BaseResponse checkWarningBiListCount(
        @RequestBody WarningBiParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return alarmService.checkWarningBiListCount(param);
    }

    /**
     * 修改未结束的桩 绑定的运维工单
     */
    @PostMapping(value = "/updateYwOrderByEvseList")
    public BaseResponse updateYwOrderByEvseList(
        @RequestBody UpdateWarningYwParam param) {
        log.info("告警绑定运维工单，param: {}", JsonUtils.toJsonString(param));
        return alarmService.updateYwOrderByEvseList(param);
    }

    /**
     * 定时任务-自动结束离线告警记录
     *
     * @return
     */
    @PostMapping(value = "/finishOfflineWarning")
    public BaseResponse finishOfflineWarning(
        @RequestParam(name = "days", required = false, defaultValue = "1") Integer days) {
        log.info("finishOfflineWarning days: {}", days);
        return alarmService.finishOfflineWarning(days);
    }

}
