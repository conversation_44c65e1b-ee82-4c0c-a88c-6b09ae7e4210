package com.cdz360.iot.monitor.entity.request;
//
//import com.chargerlink.device.common.param.BaseRequest;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//
///**
// * 站点查询
// *
// * <AUTHOR>
// * @date Create on 2018/7/31 14:00
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class SiteDeviceRequest extends BaseRequest implements Serializable {
//    private static final long serialVersionUID = 1L;
//    /**
//     * 站点id
//     */
//    private String id;
//    /**
//     * 状态
//     */
//    private Integer status;
//
//}
