package com.cdz360.iot.monitor.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 储能ESS上报告警信息
 */
@Data
@Accessors(chain = true)
@Schema(title = "储能信息")
public class MonitorErrorDevRequest {

    /**
     * 设备ID
     */
    private Long equipId;
    /**
     * 下属设备名称
     */
    private String equipName;
    @Schema(
        title = "电池簇号",
        description = "第N簇电池信息 register adrress = 第0簇电池信息 register adrress + 50* N)\nN代表簇号N(0,ClusterNum) ClusterNum参考583"
    )
    private Long clusterNo;
    @Schema(
        title = "电池组号",
        description = "电池箱X-Y register adrress = 电池箱0-0 register address + (X*LMUnum+Y)*40 )\nX代表簇号 X(0,ClusterNum),Y代表电池号Y(0,LMUnum) ClusterNum参考583, LMUnum参考584+A42"
    )
    private Long packNo;
    @Schema(
        title = "错误代码",
        description = "一次可能出现多种故障"
    )
    private List<Long> errorCodeList;

}

