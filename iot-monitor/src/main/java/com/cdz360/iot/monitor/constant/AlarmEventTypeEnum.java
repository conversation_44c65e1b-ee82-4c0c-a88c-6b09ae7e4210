package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 桩端告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
 * Created by ben on 2019.4.17.
 */
@Getter
public enum AlarmEventTypeEnum {
    UNKONWN(999, "未知", "未知"),
    ALARM_WARNING(0, "桩上报告警", "充电桩"),
    ALARM_MALFUNCTION(1, "桩上报故障", "充电桩"),
    ALARM_PLATFORM_WARNING(2, "告警逻辑生成告警", "系统"),
    ALARM_SYS_WARNING(3, "系统级别告警类型", "系统"),
    ALARM_SITE_CTRL_WARNING(4, "控制器上报告警", "控制器"),
    ALARM_SITE_CTRL_MALFUNCTION(5, "控制器上报故障", "控制器"),
    ALARM_PV_WARNING(6, "逆变器上报故障", "逆变器"),
    ALARM_ESS_WARNING(7, "储能ESS上报故障", "储能ESS"),
    ALARM_SRS_WARNING(8, "辐射仪上报故障", "辐射仪"),
    ALARM_SIM_WARNING(9, "SIM卡上报故障", "SIM卡"),
    ;
    private int value;
    private String label;
    private String source;

    AlarmEventTypeEnum(int value, String label, String source) {
        this.value = value;
        this.label = label;
        this.source = source;
    }
}
