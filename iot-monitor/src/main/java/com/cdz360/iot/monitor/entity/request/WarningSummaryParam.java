package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(title = "告警分类查询入参")
public class WarningSummaryParam extends BaseListParam {

    @Schema(title = "所属场站")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(title = "筛选告警发生时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startTime;

    @Schema(title = "筛选告警结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date endTime;

    @Schema(title = "告警代码")
    private List<String> warningCodeList;

    @Schema(title = "设备型号")
    private List<String> modelNameList;

    @Schema(title = "软件版本")
    private List<String> firmwareVerList;

    @Schema(title = "分组字段")
    private String groupBy;

    @Schema(title = "排序字段")
    private String orderBy;



}
