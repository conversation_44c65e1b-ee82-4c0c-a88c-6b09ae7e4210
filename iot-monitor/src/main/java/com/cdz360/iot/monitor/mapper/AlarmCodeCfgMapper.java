package com.cdz360.iot.monitor.mapper;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.common.base.IotCacheConstants;
import com.cdz360.iot.monitor.entity.po.AlarmCodeCfgPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;

@Mapper
public interface AlarmCodeCfgMapper {


    @Cacheable(cacheNames = IotCacheConstants.IOT_ALARM_CODE_CFG_KEY)
    AlarmCodeCfgPo getAlarmCodeCfg(@Param("equipType") EssEquipType equipType,
        @Param("alarmCode") Long alarmCode);


}
