package com.cdz360.iot.monitor.interceptor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * <p>
 * 全局异常拦截器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-03-06
 */
@Slf4j
@RestControllerAdvice(annotations = RestController.class)
public class GlobalExceptionInterceptor {

    private final Logger logger = LoggerFactory.getLogger(GlobalExceptionInterceptor.class);


    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex) {
        logger.error(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误（device-monitor）");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }

    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }


}
