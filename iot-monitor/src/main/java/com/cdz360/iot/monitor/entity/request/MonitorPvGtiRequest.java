package com.cdz360.iot.monitor.entity.request;

import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 桩端上报的告警消息
 *
 * <AUTHOR> Create on 2018.9.13 19:46 Update ben 2019.04.17
 */
@Data
@Accessors(chain = true)
public class MonitorPvGtiRequest implements Serializable {

    /**
     * 告警ID
     */
    private Long warningId;
    /**
     * 微网控制器编号
     */
    private String gwno;
    /**
     * 微网控制器名称
     */
    private String gwName;

    /**
     * 逆变器记录ID
     */
    private String recId;

    private String dno;

    /**
     * 设备名称
     */
    private String pvName;

    /**
     * 告警开始时间
     */
    private Date startTime;

    /**
     * 告警结束时间
     */
    private Date endTime;

    /**
     * 异常问题编码
     */
    private String warningCode;

    // 故障码列表
    private List<Long> errorCodeList;

    /**
     * 告警名称
     */
    private String warningName;

    /**
     * 告警描述
     */
    private String warningInstruction;

    /**
     * 运行状态:未知(0);正常(1);异常(2);待机(3)
     */
    private Integer rtStatus;

    /**
     * 场站ID
     */
    private String siteId;

    /**
     * 场站商户ID
     */
    private Long siteCommId;

    /**
     * 场站名称
     */
    private String siteName;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
