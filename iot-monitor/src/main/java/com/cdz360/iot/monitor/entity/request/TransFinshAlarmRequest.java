package com.cdz360.iot.monitor.entity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * @ClassName： TransFinshMessage
 * @Description: 资金划转交易完成消息
 * @Email: <EMAIL>
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateDate: 2019/9/5 9:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransFinshAlarmRequest extends BaseAlarmMsgRequest {
    private String tradeNo;
    private String bankTradeNo;
    private String commId;
    private Long msgId;
    private String error;
}
