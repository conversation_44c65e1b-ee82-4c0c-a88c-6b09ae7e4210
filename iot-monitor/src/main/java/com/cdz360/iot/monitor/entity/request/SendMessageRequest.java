package com.cdz360.iot.monitor.entity.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发送短信消息体的请求参数对象
 *
 * <AUTHOR>
 * Create on 2018/10/29 15:11
 */
@Data
public class SendMessageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    String phone;
    /**
     * 一次发送多个的手机号
     */
    List<String> phones;

    /**
     * 站点名称
     */
    String station;

    /**
     * 桩
     */
    String pile;

    /**
     * 插座
     */
    Integer socket;

    /**
     * 别名
     */
    String mark;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     * {@link com.cdz360.iot.monitor.constant.AlarmEventTypeEnum}
     */
    Integer warningType;
    /**
     * 发生的告警名
     */
    String warningName;
    /**
     * 应急码
     */
    String emergencyCode;
}
