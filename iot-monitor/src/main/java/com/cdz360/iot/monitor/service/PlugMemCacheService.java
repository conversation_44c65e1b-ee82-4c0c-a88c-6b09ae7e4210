package com.cdz360.iot.monitor.service;

import com.cdz360.iot.monitor.entity.vo.PlugMemCacheVo;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class PlugMemCacheService {


    private Map<String, PlugMemCacheVo> plugs = new ConcurrentHashMap<>();

    public PlugMemCacheVo getPlug(String plugNo) {
        return this.plugs.get(plugNo);
    }

    /**
     * 更新缓存里的枪头信息
     *
     * @param plug
     * @return 更新成功返回true. 如果更新的seq比原来的要旧,返回 false
     */
    @Synchronized
    public boolean updatePlug(PlugMemCacheVo plug) {
        long seq = plug.getSeq() + 1L;
        PlugMemCacheVo old = this.getPlug(plug.getPlugNo());
        if (old != null && seq < old.getSeq()) {
            log.error("序号不匹配. oldPlug = {}, newPlug = {}", old, plug);
            return false;
        }
        if (seq > Long.MAX_VALUE - 100L) {
            seq = 0L;
        }
        plug.setSeq(seq);
        plugs.put(plug.getPlugNo(), plug);
        return true;
    }
}
