package com.cdz360.iot.monitor.utils;

import com.cdz360.base.utils.StringUtils;
import java.util.regex.Pattern;

public class NumUtils {

    // 非负整数
    private static final Pattern NUMBER_NON_NEGATIVE_INT_PATTERN = Pattern.compile(
        "^[0-9]*[0-9]*$");

    /**
     * 是否为非负整数
     *
     * @param txt 目标文本
     * @return 非负整数为true, 否则false
     */
    public static boolean nonNegativeInt(String txt) {
        return StringUtils.isNotBlank(txt) &&
            NUMBER_NON_NEGATIVE_INT_PATTERN.matcher(txt).matches();
    }

}
