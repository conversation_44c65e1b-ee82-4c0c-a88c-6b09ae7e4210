package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarmNotify.ErrorObj;
import com.cdz360.base.model.es.dto.EssAlarmTinyDto;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.DehAlarmCode;
import com.cdz360.base.model.es.type.FfsAlarmCode;
import com.cdz360.base.model.es.type.LiquidAlarmCode;
import com.cdz360.base.model.es.type.PcsAlarmCode;
import com.cdz360.base.model.es.type.UpsAlarmCode;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.cdz360.base.model.es.vo.EssAlarmVo;
import com.cdz360.base.model.es.vo.WarnExtraData;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.monitor.entity.po.AlarmRecordInMongo;
import com.cdz360.iot.monitor.entity.po.EssAlarmPo;
import com.cdz360.iot.monitor.entity.request.ListEssAlarmParam;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssAlarmService {

    private static final Map<Integer, AlarmItem> EMS_DI_CNT_MAP = new HashMap<>() {
        {
            put(1, new AlarmItem("水浸报警", WarnSubDeviceType.EMS, "触发EMS水浸保护"));
            put(2, new AlarmItem("消防报警", WarnSubDeviceType.EMS, "触发EMS消防保护"));
            put(3, new AlarmItem("浪涌保护器报警", WarnSubDeviceType.EMS, "触发EMS浪涌保护"));
            put(4, new AlarmItem("急停故障", WarnSubDeviceType.EMS, "触发EMS急停保护"));
            put(5, new AlarmItem("行程开关报警", WarnSubDeviceType.EMS, "柜门开启告警"));
        }
    };
    // FIXME: 添加FAQ说明内容
    private static final HashMap<Integer, AlarmItem> versionMap = new HashMap<>() {{
        put(0x1048, new AlarmItem("电网电压异常", WarnSubDeviceType.GRID));
        put(0x1049, new AlarmItem("电网频率异常", WarnSubDeviceType.GRID));
        put(0x104A, new AlarmItem("电网电压反序", WarnSubDeviceType.GRID));
        put(0x104B, new AlarmItem("电网电压缺相", WarnSubDeviceType.GRID));
        put(0x104C, new AlarmItem("输出电压异常", null));
        put(0x104D, new AlarmItem("输出频率异常", null));
        put(0x104E, new AlarmItem("零线异常", null));
        put(0x104F, new AlarmItem("环境温度过高", null));
        put(0x1050, new AlarmItem("散热器温度过高", WarnSubDeviceType.RADIATOR));
        put(0x1051, new AlarmItem("绝缘故障", null));
        put(0x1052, new AlarmItem("漏电保护故障", null));
        put(0x1053, new AlarmItem("辅助电源故障", null));
        put(0x1054, new AlarmItem("风扇故障", WarnSubDeviceType.FAN));
        put(0x1055, new AlarmItem("机型容量故障", null));
        put(0x1056, new AlarmItem("防雷器异常", null));
        put(0x1057, new AlarmItem("孤岛保护", null));
        put(0x1058, new AlarmItem("电池1未接", WarnSubDeviceType.BATTERY));
        put(0x1059, new AlarmItem("电池1过压", WarnSubDeviceType.BATTERY));
        put(0x105A, new AlarmItem("电池1欠压", WarnSubDeviceType.BATTERY));
        put(0x105B, new AlarmItem("电池1放电终止", WarnSubDeviceType.BATTERY));
        put(0x105C, new AlarmItem("电池1反接", WarnSubDeviceType.BATTERY));
        put(0x105D, new AlarmItem("电池2未接", WarnSubDeviceType.BATTERY));
        put(0x105E, new AlarmItem("电池2过压", WarnSubDeviceType.BATTERY));
        put(0x105F, new AlarmItem("电池2欠压", WarnSubDeviceType.BATTERY));
        put(0x1060, new AlarmItem("电池2放电终止", WarnSubDeviceType.BATTERY));
        put(0x1061, new AlarmItem("电池2反接", WarnSubDeviceType.BATTERY));
        put(0x1062, new AlarmItem("光伏1未接入", WarnSubDeviceType.PV));
        put(0x1063, new AlarmItem("光伏1过压", WarnSubDeviceType.PV));
        put(0x1064, new AlarmItem("光伏1均流异常", WarnSubDeviceType.PV));
        put(0x1065, new AlarmItem("光伏2未接入", WarnSubDeviceType.PV));
        put(0x1066, new AlarmItem("光伏2过压", WarnSubDeviceType.PV));
        put(0x1067, new AlarmItem("光伏2均流异常", WarnSubDeviceType.PV));
        put(0x1068, new AlarmItem("直流母线过压", WarnSubDeviceType.DC_BUS));
        put(0x1069, new AlarmItem("直流母线欠压", WarnSubDeviceType.DC_BUS));
        put(0x106A, new AlarmItem("直流母线电压不平衡", WarnSubDeviceType.DC_BUS));
        put(0x106B, new AlarmItem("光伏1功率管故障", WarnSubDeviceType.PV));
        put(0x106C, new AlarmItem("光伏2功率管故障", WarnSubDeviceType.PV));
        put(0x106D, new AlarmItem("电池1功率管故障", WarnSubDeviceType.BATTERY));
        put(0x106E, new AlarmItem("电池2功率管故障", WarnSubDeviceType.BATTERY));
        put(0x106F, new AlarmItem("逆变器功率管故障", WarnSubDeviceType.INVERTER));
        put(0x1070, new AlarmItem("系统输出过载", null));
        put(0x1071, new AlarmItem("逆变器过载", WarnSubDeviceType.INVERTER));
        put(0x1072, new AlarmItem("逆变器过载超时", WarnSubDeviceType.INVERTER));
        put(0x1073, new AlarmItem("电池1过载超时", WarnSubDeviceType.BATTERY));
        put(0x1074, new AlarmItem("电池2过载超时", WarnSubDeviceType.BATTERY));
        put(0x1075, new AlarmItem("逆变器软启动失败", WarnSubDeviceType.INVERTER));
        put(0x1076, new AlarmItem("电池1软启动失败", WarnSubDeviceType.BATTERY));
        put(0x1077, new AlarmItem("电池2软启动失败", WarnSubDeviceType.BATTERY));
        put(0x1078, new AlarmItem("DSP1参数设置故障", WarnSubDeviceType.DSP));
        put(0x1079, new AlarmItem("DSP2参数设置故障", WarnSubDeviceType.DSP));
        put(0x107A, new AlarmItem("DSP版本兼容故障", WarnSubDeviceType.DSP));
        put(0x107B, new AlarmItem("CPLD版本兼容故障", null));
        put(0x107C, new AlarmItem("CPLD通讯故障", null));
        put(0x107D, new AlarmItem("DSP通讯故障", WarnSubDeviceType.DSP));
        put(0x107E, new AlarmItem("输出电压直流量超限", null));
        put(0x107F, new AlarmItem("输出电流直流量超限", null));
        put(0x1080, new AlarmItem("继电器自检不通过", null));
        put(0x1081, new AlarmItem("逆变器异常", WarnSubDeviceType.INVERTER));
        put(0x1082, new AlarmItem("接地不良", null));
        put(0x1083, new AlarmItem("光伏1软起动失败", WarnSubDeviceType.PV));
        put(0x1084, new AlarmItem("光伏2软起动失败", WarnSubDeviceType.PV));
        put(0x1085, new AlarmItem("平衡电路过载超时", null));
        put(0x1086, new AlarmItem("光伏1过载超时", WarnSubDeviceType.PV));
        put(0x1087, new AlarmItem("光伏2过载超时", WarnSubDeviceType.PV));
        put(0x1088, new AlarmItem("PCB过温", null));
        put(0x1089, new AlarmItem("直流变换器过温", WarnSubDeviceType.DC_CONVERTERS));
        put(0x108A, new AlarmItem("母线慢过压", null));
        put(0x108B, new AlarmItem("离网输出电压异常", null));
        put(0x108C, new AlarmItem("硬件母线过压", null));
        put(0x108D, new AlarmItem("硬件过流", null));
        put(0x108E, new AlarmItem("直流变换器过压", WarnSubDeviceType.DC_CONVERTERS));
        put(0x108F, new AlarmItem("直流变换器硬件过压", WarnSubDeviceType.DC_CONVERTERS));
        put(0x1090, new AlarmItem("直流变换器过流", WarnSubDeviceType.DC_CONVERTERS));
        put(0x1091, new AlarmItem("直流变换器硬件过流", WarnSubDeviceType.DC_CONVERTERS));
        put(0x1092, new AlarmItem("直流变换器谐振腔过流", WarnSubDeviceType.DC_CONVERTERS));
        put(0x1093, new AlarmItem("光伏1反接", WarnSubDeviceType.PV));
        put(0x1094, new AlarmItem("光伏2反接", WarnSubDeviceType.PV));
        put(0x1095, new AlarmItem("电池1功率不足", WarnSubDeviceType.BATTERY));
        put(0x1096, new AlarmItem("电池2功率不足", WarnSubDeviceType.BATTERY));
        put(0x1097, new AlarmItem("电池1禁止充电", WarnSubDeviceType.BATTERY));
        put(0x1098, new AlarmItem("电池1禁止放电", WarnSubDeviceType.BATTERY));
        put(0x1099, new AlarmItem("电池2禁止充电", WarnSubDeviceType.BATTERY));
        put(0x109A, new AlarmItem("电池2禁止放电", WarnSubDeviceType.BATTERY));
        put(0x109B, new AlarmItem("电池1充满", WarnSubDeviceType.BATTERY));
        put(0x109C, new AlarmItem("电池1放电终止", WarnSubDeviceType.BATTERY));
        put(0x109D, new AlarmItem("电池2充满", WarnSubDeviceType.BATTERY));
        put(0x109E, new AlarmItem("电池2放电终止", WarnSubDeviceType.BATTERY));
        put(0x109F, new AlarmItem("负载功率过载", null));
        put(0x10A0, new AlarmItem("漏电自检异常", null));
        put(0x10A1, new AlarmItem("逆变过温告警", WarnSubDeviceType.INVERTER));
        put(0x10A2, new AlarmItem("逆变器过温", WarnSubDeviceType.INVERTER));
        put(0x10A3, new AlarmItem("直流变换器过温告警", WarnSubDeviceType.DC_CONVERTERS));
        put(0x10A4, new AlarmItem("并机通信告警", null));
        put(0x10A5, new AlarmItem("系统降额运行", null));
        put(0x10A6, new AlarmItem("逆变继电器开路", WarnSubDeviceType.INVERTER_RELAYS));
        put(0x10A7, new AlarmItem("逆变继电器短路", WarnSubDeviceType.INVERTER_RELAYS));
        put(0x10A8, new AlarmItem("光伏接入方式错误告警", WarnSubDeviceType.PV));
        put(0x10A9, new AlarmItem("并机模块缺失", null));
        put(0x10AA, new AlarmItem("并机模块机号重复", null));
        put(0x10AB, new AlarmItem("并机模块参数冲突", null));
        put(0x10AC, new AlarmItem("光伏直流电弧故障", null));
        put(0x10AD, new AlarmItem("电表接入异常", null));
        put(0x10AE, new AlarmItem("逆变器封脉冲", WarnSubDeviceType.INVERTER));
        put(0x10AF, new AlarmItem("光伏3未接入", WarnSubDeviceType.PV));
        put(0x10B0, new AlarmItem("光伏3过压", WarnSubDeviceType.PV));
        put(0x10B1, new AlarmItem("光伏3均流异常", WarnSubDeviceType.PV));
        put(0x10B2, new AlarmItem("光伏4未接入", WarnSubDeviceType.PV));
        put(0x10B3, new AlarmItem("光伏4过压", WarnSubDeviceType.PV));
        put(0x10B4, new AlarmItem("光伏4均流异常", WarnSubDeviceType.PV));
        put(0x10B5, new AlarmItem("光伏3功率管故障", WarnSubDeviceType.PV));
        put(0x10B6, new AlarmItem("光伏4功率管故障", WarnSubDeviceType.PV));
        put(0x10B7, new AlarmItem("光伏3软起动失败", WarnSubDeviceType.PV));
        put(0x10B8, new AlarmItem("光伏4软起动失败", WarnSubDeviceType.PV));
        put(0x10B9, new AlarmItem("光伏3过载超时", WarnSubDeviceType.PV));
        put(0x10BA, new AlarmItem("光伏4过载超时", WarnSubDeviceType.PV));
        put(0x10BB, new AlarmItem("光伏3反接", WarnSubDeviceType.PV));
        put(0x10BC, new AlarmItem("光伏4反接", WarnSubDeviceType.PV));
        put(0x10BD, new AlarmItem("油机电压异常", WarnSubDeviceType.OIL_ENGINE));
        put(0x10BE, new AlarmItem("油机频率异常", WarnSubDeviceType.OIL_ENGINE));
        put(0x10BF, new AlarmItem("油机电压反序", WarnSubDeviceType.OIL_ENGINE));
        put(0x10C0, new AlarmItem("油机电压缺相", WarnSubDeviceType.OIL_ENGINE));
        put(0x10C1, new AlarmItem("铅蓄电池温度异常", WarnSubDeviceType.LEAD_ACID_BATTERIES));
        put(0x10C2, new AlarmItem("电池接入方式错误", WarnSubDeviceType.BATTERY));
        put(0x10C3, new AlarmItem("预留告警5", WarnSubDeviceType.BATTERY));
        put(0x10C4, new AlarmItem("电池1达到备电SoC", WarnSubDeviceType.BATTERY));
        put(0x10C5, new AlarmItem("电池2达到备电SoC", WarnSubDeviceType.BATTERY));
        put(0x10C6, new AlarmItem("电网过载", WarnSubDeviceType.BATTERY));
        put(0x10C7, new AlarmItem("油机过载", WarnSubDeviceType.BATTERY));
        put(0x10C8, new AlarmItem("母线软起失败", WarnSubDeviceType.BATTERY));
        put(0x10C9, new AlarmItem("电网继电器开路", WarnSubDeviceType.BATTERY));
        put(0x10CA, new AlarmItem("电网继电器短路", WarnSubDeviceType.BATTERY));
    }};
    @Autowired
    private AlarmRecordDs alarmRecordDs;
    @Autowired
    private EssAlarmRecordDs essAlarmRecordDs;

    public Mono<ListResponse<EssAlarmVo>> getEssAlarmRecordList(ListEssAlarmParam param) {
        return essAlarmRecordDs.getEssAlarmRecordList(param);
    }

    public ListResponse<EssAlarmPo> getEssAlarmList(ListEssAlarmParam param) {
        return essAlarmRecordDs.getEssAlarmList(param);
    }


    public void commEssAlarm(EssAlarmNotify data) {
        log.info("处理工商储能ESS告警: data = {}", JsonUtils.toJsonString(data));
        if (StringUtils.isBlank(data.getDno())) {
            return;
        }

        // 还存在的告警
        List<AlarmRecordInMongo> activeRecords = essAlarmRecordDs.getRecentEssAlert(
            data.getGwno(), data.getDno(), data.getSubDeviceType());
        log.info("依然存在记录数: size = {}", activeRecords.size());
        if (CollectionUtils.isNotEmpty(activeRecords)) {
            // 自动修改告警
            Set<Long> report = data.getErrorList().stream()
                .map(ErrorObj::getWarnCode)
                .collect(Collectors.toSet());

            // 已经停止的告警
            essAlarmRecordDs.stopAlarmRecord(activeRecords.stream()
                .filter(rec -> !report.contains(rec.getWarnCode()))
                .collect(Collectors.toList()), data.getTz());

            // 依然存在告警 -> 暂时不需要处理
            List<AlarmRecordInMongo> remainsWarns = activeRecords.stream()
                .filter(x -> report.contains(x.getWarnCode()))
                .collect(Collectors.toList());
            essAlarmRecordDs.updateAlarmRecord(data, remainsWarns);

            // 新的告警
            List<Long> remainsWarnCodes = remainsWarns.stream()
                .map(AlarmRecordInMongo::getWarnCode)
                .collect(Collectors.toList());
            data.getErrorList().stream()
                .filter(x -> !remainsWarnCodes.contains(x.getWarnCode()))
                .forEach(err -> {
                    AlarmRecordInMongo rec = this.startEssAlarm(data, err);
                    rec.setDeviceType(data.getDeviceType());
                    essAlarmRecordDs.insertAlarmRecord(rec);
                });
        } else {
            data.getErrorList().forEach(err -> {
                AlarmRecordInMongo rec = this.startEssAlarm(data, err);
                rec.setDeviceType(data.getDeviceType());
                essAlarmRecordDs.insertAlarmRecord(rec);
            });
        }
    }

    // 盛宏协议
//    0x1048	1	电网电压异常	0正常，1异常	M+S	告警页面
//    0x1049	1	电网频率异常	0正常，1异常	M+S	告警页面
//    0x104A	1	电网电压反序	0正常，1异常	M+S	告警页面
//    0x104B	1	电网电压缺相	0正常，1异常	M+S	告警页面
//    0x104C	1	输出电压异常	0正常，1异常	M+S	告警页面
//    0x104D	1	输出频率异常	0正常，1异常	M+S	告警页面
//    0x104E	1	零线异常	0正常，1异常	M+S	告警页面
//    0x104F	1	环境温度过高	0正常，1异常	M+S	告警页面
//    0x1050	1	散热器温度过高	0正常，1异常	M+S	告警页面
//    0x1051	1	绝缘故障	0正常，1异常	M+S	告警页面
//    0x1052	1	漏电保护故障	0正常，1异常	M+S	告警页面
//    0x1053	1	辅助电源故障	0正常，1异常	M+S	告警页面
//    0x1054	1	风扇故障	0正常，1异常	M+S	告警页面
//    0x1055	1	机型容量故障	0正常，1异常	M+S	告警页面
//    0x1056	1	防雷器异常	0正常，1异常	M+S	告警页面
//    0x1057	1	孤岛保护	0正常，1异常	M+S	告警页面
//    0x1058	1	电池1未接	0正常，1异常	M+S	告警页面
//    0x1059	1	电池1过压	0正常，1异常	M+S	告警页面
//    0x105A	1	电池1欠压	0正常，1异常	M+S	告警页面
//    0x105B	1	电池1放电终止	0正常，1异常	M+S	告警页面
//    0x105C	1	电池1反接	0正常，1异常	M+S	告警页面
//    0x105D	1	电池2未接	0正常，1异常	M+S	告警页面
//    0x105E	1	电池2过压	0正常，1异常	M+S	告警页面
//    0x105F	1	电池2欠压	0正常，1异常	M+S	告警页面
//    0x1060	1	电池2放电终止	0正常，1异常	M+S	告警页面
//    0x1061	1	电池2反接	0正常，1异常	M+S	告警页面
//    0x1062	1	光伏1未接入	0正常，1异常	M+S	告警页面
//    0x1063	1	光伏1过压	0正常，1异常	M+S	告警页面
//    0x1064	1	光伏1均流异常	0正常，1异常	M+S	告警页面
//    0x1065	1	光伏2未接入	0正常，1异常	M+S	告警页面
//    0x1066	1	光伏2过压	0正常，1异常	M+S	告警页面
//    0x1067	1	光伏2均流异常	0正常，1异常	M+S	告警页面
//    0x1068	1	直流母线过压	0正常，1异常	M+S	告警页面
//    0x1069	1	直流母线欠压	0正常，1异常	M+S	告警页面
//    0x106A	1	直流母线电压不平衡	0正常，1异常	M+S	告警页面
//    0x106B	1	光伏1功率管故障	0正常，1异常	M+S	告警页面
//    0x106C	1	光伏2功率管故障	0正常，1异常	M+S	告警页面
//    0x106D	1	电池1功率管故障	0正常，1异常	M+S	告警页面
//    0x106E	1	电池2功率管故障	0正常，1异常	M+S	告警页面
//    0x106F	1	逆变器功率管故障	0正常，1异常	M+S	告警页面
//    0x1070	1	系统输出过载	0正常，1异常	M+S	告警页面
//    0x1071	1	逆变器过载	0正常，1异常	M+S	告警页面
//    0x1072	1	逆变器过载超时	0正常，1异常	M+S	告警页面
//    0x1073	1	电池1过载超时	0正常，1异常	M+S	告警页面
//    0x1074	1	电池2过载超时	0正常，1异常	M+S	告警页面
//    0x1075	1	逆变器软启动失败	0正常，1异常	M+S	告警页面
//    0x1076	1	电池1软启动失败	0正常，1异常	M+S	告警页面
//    0x1077	1	电池2软启动失败	0正常，1异常	M+S	告警页面
//    0x1078	1	DSP1参数设置故障	0正常，1异常	M+S	告警页面
//    0x1079	1	DSP2参数设置故障	0正常，1异常	M+S	告警页面
//    0x107A	1	DSP版本兼容故障	0正常，1异常	M+S	告警页面
//    0x107B	1	CPLD版本兼容故障	0正常，1异常	M+S	告警页面
//    0x107C	1	CPLD通讯故障	0正常，1异常	M+S	告警页面
//    0x107D	1	DSP通讯故障	0正常，1异常	M+S	告警页面
//    0x107E	1	输出电压直流量超限	0正常，1异常	M+S	告警页面
//    0x107F	1	输出电流直流量超限	0正常，1异常	M+S	告警页面
//    0x1080	1	继电器自检不通过	0正常，1异常	M+S	告警页面
//    0x1081	1	逆变器异常	0正常，1异常	M+S	告警页面
//    0x1082	1	接地不良	0正常，1异常	M+S	告警页面
//    0x1083	1	光伏1软起动失败	0正常，1异常	M+S	告警页面
//    0x1084	1	光伏2软起动失败	0正常，1异常	M+S	告警页面
//    0x1085	1	平衡电路过载超时	0正常，1异常	M+S	告警页面
//    0x1086	1	光伏1过载超时	0正常，1异常	M+S	告警页面
//    0x1087	1	光伏2过载超时	0正常，1异常	M+S	告警页面
//    0x1088	1	PCB过温	0正常，1异常	M+S	告警页面
//    0x1089	1	直流变换器过温	0正常，1异常	M+S	告警页面
//    0x108A	1	母线慢过压	0正常，1异常	M+S	告警页面
//    0x108B	1	离网输出电压异常	0正常，1异常	M+S	告警页面
//    0x108C	1	硬件母线过压	0正常，1异常	M+S	告警页面
//    0x108D	1	硬件过流	0正常，1异常	M+S	告警页面
//    0x108E	1	直流变换器过压	0正常，1异常	M+S	告警页面
//    0x108F	1	直流变换器硬件过压	0正常，1异常	M+S	告警页面
//    0x1090	1	直流变换器过流	0正常，1异常	M+S	告警页面
//    0x1091	1	直流变换器硬件过流	0正常，1异常	M+S	告警页面
//    0x1092	1	直流变换器谐振腔过流	0正常，1异常	M+S	告警页面
//    0x1093	1	光伏1反接	0正常，1异常	M+S	告警页面
//    0x1094	1	光伏2反接	0正常，1异常	M+S	告警页面
//    0x1095	1	电池1功率不足	0正常，1异常	M+S	告警页面
//    0x1096	1	电池2功率不足	0正常，1异常	M+S	告警页面
//    0x1097	1	电池1禁止充电	0正常，1异常	M+S	告警页面
//    0x1098	1	电池1禁止放电	0正常，1异常	M+S	告警页面
//    0x1099	1	电池2禁止充电	0正常，1异常	M+S	告警页面
//    0x109A	1	电池2禁止放电	0正常，1异常	M+S	告警页面
//    0x109B	1	电池1充满	0正常，1异常	M+S	告警页面
//    0x109C	1	电池1放电终止	0正常，1异常	M+S	告警页面
//    0x109D	1	电池2充满	0正常，1异常	M+S	告警页面
//    0x109E	1	电池2放电终止	0正常，1异常	M+S	告警页面
//    0x109F	1	负载功率过载	0正常，1异常	M+S	告警页面
//    0x10A0	1	漏电自检异常	0正常，1异常	M+S	告警页面
//    0x10A1	1	逆变过温告警	0正常，1异常	M+S	告警页面
//    0x10A2	1	逆变器过温	0正常，1异常	M+S	告警页面
//    0x10A3	1	直流变换器过温告警	0正常，1异常	M+S	告警页面
//    0x10A4	1	并机通信告警	0正常，1异常	M+S	告警页面
//    0x10A5	1	系统降额运行	0正常，1异常	M+S	告警页面
//    0x10A6	1	逆变继电器开路	0正常，1异常	M+S	告警页面
//    0x10A7	1	逆变继电器短路	0正常，1异常	M+S	告警页面
//    0x10A8	1	光伏接入方式错误告警	0正常，1异常	M+S	告警页面
//    0x10A9	1	并机模块缺失	0正常，1异常	M+S	告警页面
//    0x10AA	1	并机模块机号重复	0正常，1异常	M+S	告警页面
//    0x10AB	1	并机模块参数冲突	0正常，1异常	M+S	告警页面
//    0x10AC	1	预留告警4	0正常，1异常	M+S	告警页面
//    0x10AD	1	预留告警5	0正常，1异常	M+S	告警页面
//    0x10AE	1	逆变器封脉冲	0正常，1异常	M+S	告警页面
//    0x10AF	1	光伏3未接入	0正常，1异常	M+S	告警页面
//    0x10B0	1	光伏3过压	0正常，1异常	M+S	告警页面
//    0x10B1	1	光伏3均流异常	0正常，1异常	M+S	告警页面
//    0x10B2	1	光伏4未接入	0正常，1异常	M+S	告警页面
//    0x10B3	1	光伏4过压	0正常，1异常	M+S	告警页面
//    0x10B4	1	光伏4均流异常	0正常，1异常	M+S	告警页面
//    0x10B5	1	光伏3功率管故障	0正常，1异常	M+S	告警页面
//    0x10B6	1	光伏4功率管故障	0正常，1异常	M+S	告警页面
//    0x10B7	1	光伏3软起动失败	0正常，1异常	M+S	告警页面
//    0x10B8	1	光伏4软起动失败	0正常，1异常	M+S	告警页面
//    0x10B9	1	光伏3过载超时	0正常，1异常	M+S	告警页面
//    0x10BA	1	光伏4过载超时	0正常，1异常	M+S	告警页面
//    0x10BB	1	光伏3反接	0正常，1异常	M+S	告警页面
//    0x10BC	1	光伏4反接	0正常，1异常	M+S	告警页面
//    0x10BD	1	油机电压异常	0正常，1异常	M+S	告警页面
//    0x10BE	1	油机频率异常	0正常，1异常	M+S	告警页面
//    0x10BF	1	油机电压反序	0正常，1异常	M+S	告警页面
//    0x10C0	1	油机电压缺相	0正常，1异常	M+S	告警页面
//    0x10C1	1	铅蓄电池温度异常	0正常，1异常	M+S	告警页面
//    0x10C2	1	电池接入方式错误	0正常，1异常	M+S	告警页面

    public void processEssAlarms(EssAlarms data) {
        log.info("处理工商储能ESS告警: data = {}", JsonUtils.toJsonString(data));
        if (data == null || StringUtils.isBlank(data.getEquipDno())) {
            return;
        }

        // 还存在的告警
        List<EssAlarmPo> activeRecords = essAlarmRecordDs.getActiveAlarms(
            data.getEssDno(), data.getEquipType(), data.getEquipDno());
        log.info("equipType= {}, equipDno= {}, essDno= {}, 未结束的告警数数: size = {}",
            data.getEquipType(), data.getEquipDno(), data.getEssDno(),
            activeRecords.size());
        if (CollectionUtils.isNotEmpty(activeRecords)) {
            // 自动修改告警
            Set<String> report = data.getAlarms() == null ? new HashSet<>()
                : data.getAlarms().stream()
                    .map(EssAlarmTinyDto::getCode)
                    .collect(Collectors.toSet());

            // 已经停止的告警
            essAlarmRecordDs.stopAlarmRecord2(data, activeRecords.stream()
                .filter(rec -> !report.contains(rec.getCode()))
                .collect(Collectors.toList()));

            // 依然存在告警 -> 暂时不需要处理
            List<EssAlarmPo> remainsWarns = activeRecords.stream()
                .filter(x -> report.contains(x.getCode()))
                .collect(Collectors.toList());
//            essAlarmRecordDs.updateAlarmRecord(data, remainsWarns);

            // 新的告警
            List<String> remainsWarnCodes = remainsWarns.stream()
                .map(EssAlarmPo::getCode)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(data.getAlarms())) {
                data.getAlarms().stream()
                    .filter(x -> !remainsWarnCodes.contains(x.getCode()))
                    .forEach(err -> {
                        EssAlarmPo rec = this.buildEssAlarmPo4NewAlarm(data, err);

                        essAlarmRecordDs.insertAlarmRecord(rec);
                    });
            }
        } else if (CollectionUtils.isNotEmpty(data.getAlarms())) {
            data.getAlarms().forEach(err -> {
                EssAlarmPo rec = this.buildEssAlarmPo4NewAlarm(data, err);
                essAlarmRecordDs.insertAlarmRecord(rec);
            });
        }
    }

    /**
     * 构建 EssAlarmPo 用于新发生的告警
     */
    private EssAlarmPo buildEssAlarmPo4NewAlarm(EssAlarms data, EssAlarmTinyDto alarmIn) {
        EssAlarmPo po = new EssAlarmPo();
        BeanUtils.copyProperties(data, po);
        po.setAlarmType(alarmIn.getAlarmType())
            .setCode(alarmIn.getCode())
            .setLevel(alarmIn.getLevel())
            .setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setStartTime(alarmIn.getStartTime());
        return po;

    }

    public void essAlarm(EssAlarmNotify data) {
        if (WarnDeviceType.USER_ESS.equals(data.getDeviceType())) {
            this.userEssAlarm(data);
        } else if (WarnDeviceType.COMM_ESS.equals(data.getDeviceType())) {
            this.commEssAlarm(data);
        }
    }

//    public void processEssAlarms(EssAlarms alarms) {
//        this.pro
//    }

    public void userEssAlarm(EssAlarmNotify data) {
        log.info("处理户用储能ESS告警: data = {}", JsonUtils.toJsonString(data));
        if (StringUtils.isBlank(data.getDno())) {
            return;
        }

        // 还存在的告警
        List<AlarmRecordInMongo> activeRecords = essAlarmRecordDs.getRecentEssAlert(
            data.getGwno(), data.getDno(), null);
        log.info("依然存在记录数: size = {}", activeRecords.size());
        if (CollectionUtils.isNotEmpty(activeRecords)) {
            // 自动修改告警
            Set<Long> report = data.getErrorList().stream()
                .map(ErrorObj::getWarnCode)
                .collect(Collectors.toSet());

            // 已经停止的告警
            essAlarmRecordDs.stopAlarmRecord(activeRecords.stream()
                .filter(rec -> !report.contains(rec.getWarnCode()))
                .collect(Collectors.toList()), data.getTz());

            // 依然存在告警 -> 暂时不需要处理
            List<AlarmRecordInMongo> remainsWarns = activeRecords.stream()
                .filter(x -> report.contains(x.getWarnCode()))
                .collect(Collectors.toList());
            essAlarmRecordDs.updateAlarmRecord(data, remainsWarns);

            // 新的告警
            List<Long> remainsWarnCodes = remainsWarns.stream()
                .map(AlarmRecordInMongo::getWarnCode)
                .collect(Collectors.toList());
            data.getErrorList().stream()
                .filter(x -> !remainsWarnCodes.contains(x.getWarnCode()))
                .forEach(err -> {
                    AlarmRecordInMongo rec = this.startUserEssAlarm(data, err);
                    rec.setDeviceType(data.getDeviceType());
                    essAlarmRecordDs.insertAlarmRecord(rec);
                });
        } else {
            data.getErrorList().forEach(err -> {
                AlarmRecordInMongo rec = this.startUserEssAlarm(data, err);
                rec.setDeviceType(data.getDeviceType());
                essAlarmRecordDs.insertAlarmRecord(rec);
            });
        }
    }

    private AlarmRecordInMongo startEssAlarm(EssAlarmNotify data, ErrorObj err) {
        AlarmRecordInMongo record = new AlarmRecordInMongo();

        // 附件信息
        WarnExtraData extraData = new WarnExtraData();
        extraData.setGwno(data.getGwno())
            .setGwName(data.getGwName())
            .setSiteId(data.getSiteId())
            .setSiteName(data.getSiteName())
            .setCountryCode(data.getCountryCode());
        record.setExtraData(extraData);

        record.setDno(data.getDno())
            .setDeviceName(data.getDeviceName())
            .setHappenTime(err.getLts())
            .setUpdateTime(LocalDateTime.now())
            .setWarnType(err.getWarnType())
            .setWarnCode(err.getWarnCode())
            .setWarnStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setVersion(data.getVersion());

        // 版本映射关系
        AlarmItem warnInfo = null;
        if (null != data.getSubDeviceType()) {
            switch (data.getSubDeviceType()) {
                case PCS:
                    PcsAlarmCode pcsErrorCode = PcsAlarmCode.valueOf(err.getWarnCode());
                    if (null != pcsErrorCode) {
                        warnInfo = new AlarmItem(
                            pcsErrorCode.getMsg(), WarnSubDeviceType.PCS, pcsErrorCode.getDesc());
                    }
                    break;
                case EMS:
                    warnInfo = EMS_DI_CNT_MAP.get(err.getWarnCode().intValue());
                    break;
                case BATTERY:
                    break;
                case BMS:
                    BmsAlarmCode bmsErrorCode = BmsAlarmCode.valueOf(err.getWarnCode());
                    if (null != bmsErrorCode) {
                        warnInfo = new AlarmItem(
                            bmsErrorCode.getMsg(), WarnSubDeviceType.BMS, bmsErrorCode.getDesc());
                    }
                    break;
                case LIQUID:
                    LiquidAlarmCode liquidErrorCode = LiquidAlarmCode.valueOf(err.getWarnCode());
                    if (null != liquidErrorCode) {
                        warnInfo = new AlarmItem(
                            liquidErrorCode.getMsg(), WarnSubDeviceType.LIQUID,
                            liquidErrorCode.getDesc());
                    }
                    break;
                case FIRE_FIGHTING:
                    FfsAlarmCode ffsErrorCode = FfsAlarmCode.valueOf(err.getWarnCode());
                    if (null != ffsErrorCode) {
                        warnInfo = new AlarmItem(
                            ffsErrorCode.getMsg(), WarnSubDeviceType.FIRE_FIGHTING,
                            ffsErrorCode.getDesc());
                    }
                    break;
                case AIR_SYS:
                    DehAlarmCode dehErrorCode = DehAlarmCode.valueOf(err.getWarnCode());
                    if (null != dehErrorCode) {
                        warnInfo = new AlarmItem(
                            dehErrorCode.getMsg(), WarnSubDeviceType.AIR_SYS,
                            dehErrorCode.getDesc());
                    }
                    break;
                case UPS:
                    UpsAlarmCode upsWarnCode = UpsAlarmCode.valueOf(err.getWarnCode());
                    if (null != upsWarnCode) {
                        warnInfo = new AlarmItem(upsWarnCode.getMsg(), WarnSubDeviceType.UPS)
                            .setName(upsWarnCode.getDesc());
                    }
                    break;
            }
        } else {
            warnInfo = EMS_DI_CNT_MAP.get(err.getWarnCode().intValue());
        }

        if (null != warnInfo) {
            record.setSubDeviceType(warnInfo.getDeviceType())
                .setWarnName(warnInfo.getName())
                .setWarnNote(warnInfo.getNote());
        } else {
            record.setSubDeviceType(data.getSubDeviceType())
                .setWarnName(err.getWarnCode().intValue() == -1 ? "离线" : "未知");
        }

        // DI数据使用
        record.setSubDeviceName(data.getSubDeviceName())
            .setSubDeviceDno(data.getSubDeviceDno());

        return record;
    }

    private AlarmRecordInMongo startUserEssAlarm(EssAlarmNotify data, ErrorObj err) {
        AlarmRecordInMongo record = new AlarmRecordInMongo();

        // 附件信息
        WarnExtraData extraData = new WarnExtraData();
        extraData.setGwno(data.getGwno())
            .setGwName(data.getGwName())
            .setCountryCode(data.getCountryCode());
        record.setExtraData(extraData);

        record.setDno(data.getDno())
            .setDeviceName(data.getDeviceName())
            .setHappenTime(err.getLts())
            .setUpdateTime(LocalDateTime.now())
            .setWarnType(err.getWarnType())
            .setWarnCode(err.getWarnCode())
            .setWarnStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())
            .setVersion(data.getVersion());

        // 版本映射关系
        AlarmItem warnInfo = versionMap.get(err.getWarnCode().intValue());
        if (null != warnInfo) {
            record.setSubDeviceType(warnInfo.getDeviceType())
                .setWarnName(warnInfo.getName())
                .setWarnNote(warnInfo.getNote());
        }

        return record;
    }

    @Data
    @Accessors(chain = true)
    public static class AlarmItem {

        private String name; // 告警名称
        private WarnSubDeviceType deviceType; // 告警目标设备
        private String note; // 附加信息

        public AlarmItem(String name, WarnSubDeviceType deviceType) {
            this.name = name;
            this.deviceType = deviceType;
        }

        public AlarmItem(String name, WarnSubDeviceType deviceType, String note) {
            this.name = name;
            this.deviceType = deviceType;
            this.note = note;
        }
    }


}
