package com.cdz360.iot.monitor.entity.result;

//
///**
// * 设备简单信息
// *
// * <AUTHOR>
// * @date Create on 2018/7/5 23:12
// */
//@Data
//public class BoxSimpleInfoVo implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 设备ID
//     */
//    private String deviceId;
//    /**
//     * 桩号/设备序列号/设备出厂编号
//     */
//    private String serialNumber;
//    /**
//     * 设备类型
//     */
//    private Integer deviceType;
//    /**
//     * 设备状态
//     */
//    private Integer status;
//    /**
//     * 最后心跳时间
//     */
//    private Long lastHeartbeatTime;
//    /**
//     * 所属站点ID
//     */
//    private String siteId;
//    /**
//     * 所属商户ID
//     */
//    private String businessId;
//    /**
//     * 站点名称
//     */
//    private String siteName;
//
//}
