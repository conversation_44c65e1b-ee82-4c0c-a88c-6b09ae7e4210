package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 前端需要的充电插座状态
 *
 * <AUTHOR>
 * @date Create on 2017/11/21 15:07
 */
@Getter
public enum ConnectorStatusVoEnum {

    STATUS_IDLE(0, "空闲", ConnectorStatusEnum.STATUS_IDLE),
    STATUS_CHARGING(10, "充电中", ConnectorStatusEnum.STATUS_CHARGING),
    STATUS_ONLINE_OCCUPY(20, "充电接口占用", ConnectorStatusEnum.STATUS_ONLINE_OCCUPY),
    STATUS_UNACTIVITY_OR_OFFLINE(30, "离线", ConnectorStatusEnum.STATUS_UNACTIVITY_OR_OFFLINE),
    STATUS_ONLINE_OCCUPY_COMPLETE(31, "充电完成", ConnectorStatusEnum.STATUS_ONLINE_OCCUPY_COMPLETE),
    STATUS_ONLINE_BREAK_DOWN(-100, "充电接口故障", ConnectorStatusEnum.STATUS_ONLINE_BREAK_DOWN);

    private int value;
    private String label;
    /**
     * 对应实际充电插座状态
     */
    private ConnectorStatusEnum statusEnum;

    ConnectorStatusVoEnum(int value, String label, ConnectorStatusEnum statusEnum) {
        this.value = value;
        this.label = label;
        this.statusEnum = statusEnum;
    }

}
