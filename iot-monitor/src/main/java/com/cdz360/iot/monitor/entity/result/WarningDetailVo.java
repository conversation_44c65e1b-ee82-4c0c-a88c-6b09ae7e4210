package com.cdz360.iot.monitor.entity.result;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * WarningDetailVo
 *
 * @ClassName WarningDetailVo
 * <AUTHOR>
 * @Description
 * @Date 2019.4.16
 */
@Data
@Accessors(chain = true)
public class WarningDetailVo {
    private Long id;
    /**
     * 告警编码
     */
    private String warningCode;
    /**
     * 告警名
     */
    private String warningName;
    /**
     * 告警处理说明
     */
    private String warningInstructions;
    /**
     * 告警等级
     */
    private Integer warningLevel;
}
