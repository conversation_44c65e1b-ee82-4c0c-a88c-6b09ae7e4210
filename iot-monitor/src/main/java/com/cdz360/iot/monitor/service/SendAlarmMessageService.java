package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.monitor.entity.request.SysAlarmMsgRequest;
import com.cdz360.iot.monitor.entity.request.TransFinshAlarmRequest;

import java.util.List;
import java.util.Map;


/**
 * 短信发送告警消息以及其他消息推送接口
 *
 * <AUTHOR>
 * @date Create on 2019/03/26
 */
public interface SendAlarmMessageService {

    /**
     * 发送告警消息到手机
     *
     * @param sendMessageRequest 请求发送告警消息参数
     */
//    void sendAlarmMobileMessage(SendMessageRequest sendMessageRequest) throws DcServiceException;

    /**
     * 登录超时告警
     *
     * @param sysAlarmMsgRequest
     * @throws DcServiceException
     */
    void sendGwnoTimeoutAlarmMsg(SysAlarmMsgRequest sysAlarmMsgRequest) throws DcServiceException;

    /**
     * 微服务掉线告警
     *
     * @param sysAlarmMsgRequest
     * @throws DcServiceException
     */
    void sendMicroServiceAlarmMsg(SysAlarmMsgRequest sysAlarmMsgRequest) throws DcServiceException;

    void sendTranFinishAlarmMsg(TransFinshAlarmRequest request) throws DcServiceException;

    /**
     * 发送手机消息
     *
     * @param mapExtras  模板中参数
     * @param phones     收消息手机号列表
     * @throws DcServiceException
     */
    void sendMobileMsg(Map<String, String> mapExtras, List<String> phones) throws DcServiceException;
}
