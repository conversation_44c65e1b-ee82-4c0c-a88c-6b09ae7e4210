package com.cdz360.iot.monitor.entity.result;

import com.cdz360.iot.monitor.entity.request.BasePageRquest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 告警消息发送配置
 *
 * @ClassName WarningSendConfigVo
 * <AUTHOR>
 * @Description
 * @Date 2019.4.16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WarningSendConfigVo extends BasePageRquest {
    private Long id;
    /**
     * 告警编码
     */
    private String warningCode;
    /**
     * 告警等级
     */
    private Integer warningLevel;
    /**
     * 告警名称
     */
    private String warningName;
    /**
     * 发送邮件,0不发送1发送
     */
    private Integer email;
    /**
     * 发送短信,0不发送1发送
     */
    private Integer sms;
    /**
     * 发送到大屏,0不发送1发送
     */
    private Integer screen;
    /**
     * 代理商(商户)ID
     */
    private String businessId;
}
