package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.vo.AlarmRecordMemVo;
import com.cdz360.iot.monitor.entity.vo.EquipAlarmsMemVo;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 设备告警的内存缓存
 */
@Slf4j
@Service
public class AlarmMemCacheService {


    @Autowired
    private AlarmRecordDs alarmRecordDs;

    private Map<String, EquipAlarmsMemVo> equipments = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                buildCacheFromMongoDb();
            }
        }, 3000L);
    }

    public synchronized void addRecord(EssEquipType equipType, String dno,
        AlarmRecord record) {
        String key = this.buildKey(equipType, dno);
        EquipAlarmsMemVo equip = this.equipments.get(key);
        if (equip == null) {
            equip = new EquipAlarmsMemVo();
            equip.setEquipType(equipType)
                .setDno(dno);
            this.equipments.put(key, equip);
        }
        AlarmRecordMemVo memRec = new AlarmRecordMemVo();
        memRec.setWarningId(record.getWarningId())
            .setCode(record.getErrorCode());
        equip.getAlarms().put(record.getErrorCode(), memRec);
    }

    public synchronized EquipAlarmsMemVo getAlarmEquip(EssEquipType equipType, String dno) {
        return this.equipments.get(this.buildKey(equipType, dno));
//        return null;
    }

    public synchronized void removeAlarms(EssEquipType equipType, String dno,
        List<Long> alarmCodes) {
        String key = this.buildKey(equipType, dno);
        EquipAlarmsMemVo equip = this.equipments.get(key);
        for (Long code : equip.getAlarms().keySet()) {
            equip.getAlarms().remove(code);
        }
        if (equip.getAlarms().isEmpty()) {
            this.equipments.remove(key);
        }
    }


    private void addRecord(AlarmRecord recIn) {
        EssEquipType equipType = EssEquipType.valueOf(recIn.getEquipType());
        String key = this.buildKey(equipType, recIn.getDno());
        EquipAlarmsMemVo equip = this.equipments.get(key);
        if (equip == null) {
            equip = new EquipAlarmsMemVo();
            equip.setEquipType(equipType)
                .setDno(recIn.getDno());
            this.equipments.put(key, equip);
        }
        try {
            Long errorCode = recIn.getErrorCode();
            if (!equip.getAlarms().containsKey(errorCode)) {
                AlarmRecordMemVo memRec = new AlarmRecordMemVo();
                memRec.setCode(errorCode)
                    .setWarningId(recIn.getWarningId());
                equip.getAlarms().put(memRec.getCode(), memRec);
            }
        } catch (Exception e) {
            log.error("recIn = {}", recIn, e);
        }
    }

    private String buildKey(EssEquipType equipType, String dno) {
        return equipType.name() + "-" + dno;
    }


    private synchronized void buildCacheFromMongoDb() {
        List<AlarmRecord> list;
        long start = 0L;
        int size = 1000;
        do {
            list = alarmRecordDs.getActiveRecords(start, size);
            log.info("从mongo获取到 {} 个未关闭的告警记录", list.size());
            for (AlarmRecord rec : list) {
                if (rec.getEquipType() == null || StringUtils.isEmpty(rec.getDno())) {
                    continue;
                }
                this.addRecord(rec);
            }
            start = start + list.size();
        } while (list.size() >= size);
        log.info("在内存创建的告警设备数为 {} 个", this.equipments.size());
    }


}
