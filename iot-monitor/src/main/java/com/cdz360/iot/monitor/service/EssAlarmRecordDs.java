package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.cdz360.base.model.es.vo.EssAlarmVo;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.TimeZoneParser;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.monitor.entity.po.AlarmRecordInMongo;
import com.cdz360.iot.monitor.entity.po.EssAlarmPo;
import com.cdz360.iot.monitor.entity.request.ListEssAlarmParam;
import com.cdz360.iot.monitor.utils.GenerateIdByRedisAtomicUtils;
import com.cdz360.iot.monitor.utils.NumUtils;
import com.mongodb.client.result.UpdateResult;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssAlarmRecordDs {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss");

    @Autowired
    private MongoTemplate mongoTemplate;

    public List<AlarmRecordInMongo> getRecentEssAlert(String gwno, String dno,
        WarnSubDeviceType subDeviceType) {
        Query query = new Query();
        query.addCriteria(Criteria.where("dno").is(dno)
            .and("warnStatus").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));

        if (StringUtils.isNotBlank(gwno)) {
            query.addCriteria(Criteria.where("extraData.gwno").is(gwno));
        }

        if (null != subDeviceType) {
            query.addCriteria(Criteria.where("subDeviceType").is(subDeviceType));
        }

        return mongoTemplate.find(query, AlarmRecordInMongo.class);
    }

    /**
     * 查询未结束的告警
     */
    public List<EssAlarmPo> getActiveAlarms(@Nullable String essDno,
        @Nullable EssEquipType equipType,
        String equipDno) {
        Query query = new Query();
        query.addCriteria(Criteria.where("equipDno").is(equipDno)
            .and("status").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));

        if (StringUtils.isNotBlank(essDno)) {
            query.addCriteria(Criteria.where("essDno").is(essDno));
        }

        if (null != equipType) {
            query.addCriteria(Criteria.where("equipType").is(equipType));
        }

        return mongoTemplate.find(query, EssAlarmPo.class);
    }


    public void insertAlarmRecord(EssAlarmPo alarm) {
        if (null == alarm.getCreateTime()) {
            alarm.setCreateTime(LocalDateTime.now());
        }
        if (null == alarm.getUpdateTime()) {
            alarm.setUpdateTime(LocalDateTime.now());
        }
        if (null == alarm.getId()) {
            alarm.setId(GenerateIdByRedisAtomicUtils.generateLongKey2());
        }
        log.info("创建新的告警记录 alarm= {}", JsonUtils.toJsonString(alarm));
        mongoTemplate.insert(alarm);
    }


    public void insertAlarmRecord(AlarmRecordInMongo rec) {
        if (null == rec.getHappenTime()) {
            rec.setHappenTime(LocalDateTime.now());
        }
        if (null == rec.getUpdateTime()) {
            rec.setUpdateTime(LocalDateTime.now());
        }
        if (null == rec.getWarningId()) {
            rec.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey2());
        }
        mongoTemplate.insert(rec);
    }

    /**
     * 结束告警
     * @param stopWarns
     * @param tz 时区（例如：GMT+08）
     */
    public void stopAlarmRecord(List<AlarmRecordInMongo> stopWarns, @Nullable String tz) {
        Query query = new Query();
        query.addCriteria(Criteria.where("warningId")
            .in(stopWarns.stream().map(AlarmRecordInMongo::getWarningId)
                .collect(Collectors.toList())));

        // 只有未结束的记录才可以结束
        query.addCriteria(
            Criteria.where("warnStatus").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));

        LocalDateTime nowTz = null;
        if (StringUtils.isNotBlank(tz)) {
            nowTz = TimeZoneParser.nowByTz(tz);
        }
        LocalDateTime now = LocalDateTime.now();
        Update update = new Update();
        update.set("stopTime", Optional.ofNullable(nowTz).orElse(now));
        update.set("updateTime", now);
        update.set("warnStatus", AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());

        UpdateResult updateResult = mongoTemplate.updateMulti(query, update,
            AlarmRecordInMongo.class);
        log.info("告警自动结束: {}", updateResult.getMatchedCount());
    }


    /**
     * 执行告警结束的落库修改动作
     *
     * @param alarms2Stop 待标记停止的告警列表
     */
    public void stopAlarmRecord2(EssAlarms data, List<EssAlarmPo> alarms2Stop) {
        /**
         * 告警停止时间使用设备本地时间
         * 告警更新时间，使用服务器时间
         */
        LocalDateTime endTime = LocalDateTime.ofEpochSecond(data.getTs(), 0,
            ZoneOffset.of(data.getTz()));
        LocalDateTime updateTime = LocalDateTime.now();
        List<Long> ids = new ArrayList<>();
        for (var alarm : alarms2Stop) {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(alarm.getId()));
            Update update = new Update();
            update.set("endTime", endTime);    // 设备本地时间
            update.set("updateTime", updateTime);   // 云端服务器时间
            update.set("status", AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
            UpdateResult updateResult = mongoTemplate.updateFirst(query, update,
                EssAlarmPo.class);
            if (updateResult.getModifiedCount() > 0L) {
                ids.add(alarm.getId());
            }
        }
        log.info("已结束的告警IDs = {}", JsonUtils.toJsonString(ids));
    }


    public Mono<ListResponse<EssAlarmVo>> getEssAlarmRecordList(ListEssAlarmParam param) {
        Assert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");

        Query query = this.listEssAlarmParamToQuery(param);

        long total = 0;
        if (Boolean.TRUE.equals(param.getTotal())) {
            total = mongoTemplate.count(query, AlarmRecordInMongo.class);
        }

        query.with(Sort.by(Sort.Direction.DESC, "warningId"));
        query.skip(param.getStart()).limit(param.getSize());
        return Mono.just(
            RestUtils.buildListResponse(mongoTemplate.find(query, AlarmRecordInMongo.class)
                .stream().map(x -> {
                    EssAlarmVo vo = new EssAlarmVo();
                    BeanUtils.copyProperties(x, vo);
                    return vo;
                })
                .collect(Collectors.toList()), total));
    }


    public ListResponse<EssAlarmPo> getEssAlarmList(ListEssAlarmParam param) {
        Assert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");

        Query query = this.listEssAlarmParamToQuery2(param);

        long total = 0;
        if (Boolean.TRUE.equals(param.getTotal())) {
            total = mongoTemplate.count(query, EssAlarmPo.class);
        }

        query.with(Sort.by(Sort.Direction.DESC, "id"));
        query.skip(param.getStart()).limit(param.getSize());
        List<EssAlarmPo> list = mongoTemplate.find(query, EssAlarmPo.class);
        return RestUtils.buildListResponse(list, total);
    }


    private Query listEssAlarmParamToQuery2(ListEssAlarmParam param) {
        Query query = new Query();
        if (param.getHappenTimeFilter() != null) {
            query.addCriteria(Criteria.where("startTime")
                .gte(param.getHappenTimeFilter().getStartTime())
                .lt(param.getHappenTimeFilter().getEndTime()));
        }

        if (param.getStopTimeFilter() != null) {
            query.addCriteria(Criteria.where("stopTime")
                .gte(param.getStopTimeFilter().getStartTime())
                .lt(param.getStopTimeFilter().getEndTime()));
        }

        if (param.getDeviceType() != null) {
            query.addCriteria(Criteria.where("equipCategory").is(param.getDeviceType()));
        }

        if (param.getEquipType() != null) {
            query.addCriteria(Criteria.where("equipType").is(param.getEquipType()));
        }

        if (StringUtils.isNotBlank(param.getSubTargetNoOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getSubTargetNoOrNameLike());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("equipName").regex(pattern),
                    Criteria.where("equipDno").regex(pattern)
                ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (param.getWarningId() != null) {
            query.addCriteria(Criteria.where("id").is(param.getWarningId()));
        }

        if (StringUtils.isNotBlank(param.getWarnCodeOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getWarnCodeOrNameLike());

            List<Criteria> temp = NumUtils.nonNegativeInt(param.getWarnCodeOrNameLike()) ?
                new ArrayList<>(
                    List.of(
                        Criteria.where("alarmId")
                            .is(Long.parseLong(param.getWarnCodeOrNameLike())),
                        Criteria.where("alarmCode").regex(pattern)
                    )) :
                new ArrayList<>(
                    List.of(
                        Criteria.where("alarmId").regex(param.getWarnCodeOrNameLike()),
                        Criteria.where("alarmCode").regex(pattern)
                    ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (StringUtils.isNotBlank(param.getTargetNoOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getTargetNoOrNameLike());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("equipDno").regex(pattern),
                    Criteria.where("equipName").regex(pattern)
                ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (null != param.getWarnCode()) {
            query.addCriteria(Criteria.where("alarmId").is(param.getWarnCode()));
        }

        if (CollectionUtils.isNotEmpty(param.getCodeList())) {
            query.addCriteria(Criteria.where("code").in(param.getCodeList()));
        }

        if (StringUtils.isNotEmpty(param.getWarnNameLike())) {
            Pattern pattern = Pattern.compile(param.getWarnNameLike());
            query.addCriteria(Criteria.where("alarmCode").regex(pattern));
        }

        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            query.addCriteria(Criteria.where("status").in(param.getStatusList().stream()
                .map(AlarmStatusEnum::getValue).collect(Collectors.toList())));
        }

//        if (StringUtils.isNotEmpty(param.getNoteLike())) {
//            Pattern pattern = Pattern.compile(param.getNoteLike());
//            query.addCriteria(Criteria.where("extraData.note").regex(pattern));
//        }

        if (CollectionUtils.isNotEmpty(param.getCountryCodeList())) {
            query.addCriteria(Criteria.where("cc")
                .in(param.getCountryCodeList()));
        }

        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            query.addCriteria(Criteria.where("siteId")
                .in(param.getSiteIdList()));
        }

        if (CollectionUtils.isNotEmpty(param.getDnoList())) {
            query.addCriteria(Criteria.where("equipDno").in(param.getDnoList()));
        }

        return query;
    }


    private Query listEssAlarmParamToQuery(ListEssAlarmParam param) {
        Query query = new Query();
        if (param.getHappenTimeFilter() != null) {
            query.addCriteria(Criteria.where("happenTime")
                .gte(param.getHappenTimeFilter().getStartTime())
                .lt(param.getHappenTimeFilter().getEndTime()));
        }

        if (param.getStopTimeFilter() != null) {
            query.addCriteria(Criteria.where("stopTime")
                .gte(param.getStopTimeFilter().getStartTime())
                .lt(param.getStopTimeFilter().getEndTime()));
        }

        if (param.getDeviceType() != null) {
            query.addCriteria(Criteria.where("deviceType").is(param.getDeviceType()));
        }

        if (param.getSubDeviceType() != null) {
            query.addCriteria(Criteria.where("subDeviceType").is(param.getSubDeviceType()));
        }

        if (StringUtils.isNotBlank(param.getSubTargetNoOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getSubTargetNoOrNameLike());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("subDeviceName").regex(pattern),
                    Criteria.where("subDeviceDno").regex(pattern)
                ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (param.getWarningId() != null) {
            query.addCriteria(Criteria.where("warningId").is(param.getWarningId()));
        }

        if (StringUtils.isNotBlank(param.getWarnCodeOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getWarnCodeOrNameLike());

            List<Criteria> temp = NumUtils.nonNegativeInt(param.getWarnCodeOrNameLike()) ?
                new ArrayList<>(
                    List.of(
                        Criteria.where("warnCode")
                            .is(Long.parseLong(param.getWarnCodeOrNameLike())),
                        Criteria.where("warnName").regex(pattern)
                    )) :
                new ArrayList<>(
                    List.of(
                        Criteria.where("warnCode").regex(param.getWarnCodeOrNameLike()),
                        Criteria.where("warnName").regex(pattern)
                    ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (StringUtils.isNotBlank(param.getTargetNoOrNameLike())) {
            Pattern pattern = Pattern.compile(param.getTargetNoOrNameLike());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("dno").regex(pattern),
                    Criteria.where("deviceName").regex(pattern)
                ));

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        }

        if (null != param.getWarnCode()) {
            query.addCriteria(Criteria.where("warnCode").is(param.getWarnCode()));
        }

        if (CollectionUtils.isNotEmpty(param.getWarnCodeList())) {
            query.addCriteria(Criteria.where("warnCode").in(param.getWarnCodeList()));
        }

        if (StringUtils.isNotEmpty(param.getWarnNameLike())) {
            Pattern pattern = Pattern.compile(param.getWarnNameLike());
            query.addCriteria(Criteria.where("warnName").regex(pattern));
        }

        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            query.addCriteria(Criteria.where("warnStatus").in(param.getStatusList().stream()
                .map(AlarmStatusEnum::getValue).collect(Collectors.toList())));
        }

        if (StringUtils.isNotEmpty(param.getNoteLike())) {
            Pattern pattern = Pattern.compile(param.getNoteLike());
            query.addCriteria(Criteria.where("extraData.note").regex(pattern));
        }

        if (CollectionUtils.isNotEmpty(param.getCountryCodeList())) {
            query.addCriteria(Criteria.where("extraData.countryCode")
                .in(param.getCountryCodeList()));
        }

        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            query.addCriteria(Criteria.where("extraData.siteId")
                .in(param.getSiteIdList()));
        }

        if (CollectionUtils.isNotEmpty(param.getDnoList())) {
            query.addCriteria(Criteria.where("dno").in(param.getDnoList()));
        }

        return query;
    }

    public void updateAlarmRecord(EssAlarmNotify data, List<AlarmRecordInMongo> remainsWarns) {
        Query query = new Query();
        query.addCriteria(Criteria.where("warningId")
            .in(remainsWarns.stream().map(AlarmRecordInMongo::getWarningId)
                .collect(Collectors.toList())));

        LocalDateTime now = LocalDateTime.now();
        Update update = new Update();
        update.set("updateTime", now);

        if (StringUtils.isNotBlank(data.getSiteId())) {
            update.set("extraData.siteId", data.getSiteId());
        }
        if (StringUtils.isNotBlank(data.getSiteName())) {
            update.set("extraData.siteName", data.getSiteName());
        }
        if (StringUtils.isNotBlank(data.getCountryCode())) {
            update.set("extraData.countryCode", data.getCountryCode());
        }

        UpdateResult ur = mongoTemplate.updateMulti(query, update, AlarmRecordInMongo.class);
        log.info("告警记录更新: {} / {}", query, ur.getMatchedCount());
    }
}
