package com.cdz360.iot.monitor.dao;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.alarm.param.GetAlarmListParam;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.po.WWarningSummaryRecord;
import com.cdz360.iot.monitor.entity.request.UpdateWarningYwParam;
import com.cdz360.iot.monitor.entity.request.WarningBiParam;
import com.cdz360.iot.monitor.entity.request.WarningSummaryParam;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * 告警数据的dao层接口
 *
 * <AUTHOR> on 2018.8.13 9:48
 */
public interface AlarmRecordDao {

    /**
     * 查询告警列表
     */
    List<AlarmRecord> getAlarmList(GetAlarmListParam paramIn);

    /**
     * 批量插入
     *
     * @param wWarningRecordsList 告警列表
     */
    void insertWWarningRecordsList(
        @Param("wWarningRecords") List<AlarmRecord> wWarningRecordsList);

    void insertWarningRecord(@Param("warningRecord") AlarmRecord warningRecord);


    /**
     * 批量更新告警状态，如果值为空不更新
     *
     * @param recordIdList   告警记录ID
     * @param wWarningRecord
     * @param
     */
    void updateBatchStatusById(List<Long> recordIdList, AlarmRecord wWarningRecord);

    void batchStopAlarms(AlarmRecord record, String dno, List<Long> alarmCodes);


    List<AlarmRecord> getActiveRecords(@Param("start") long start,
        @Param("size") int size);


    /**
     * 获取告警记录
     *
     * @param alarmStatus     告警状态
     * @param warningCode     告警码
     * @param evseNoList      设备Id列表
     * @param connectorId     枪头序号
     * @param warningTypeList 告警类型代码
     * @return 告警记录
     */
    List<AlarmRecord> getWarningRecord(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("evseNoList") List<String> evseNoList,
        @Param("connectorId") Integer connectorId,
        @Param("warningTypeList") List<Integer> warningTypeList,
        @Param("sourceNo") List<String> sourceNo);

    List<AlarmRecord> getWarningSiteCtrlRecord(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("ctrlNoList") List<String> ctrlNoList,
        @Param("warningTypeList") List<Integer> warningTypeList);

    AlarmRecord getWarningRecordX(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("evseNoList") String evseNo,
        @Param("connectorId") Integer connectorId,
        @Param("warningTypeList") List<Integer> warningTypeList);

    AlarmRecord getWarningSiteCtrlRecordX(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("ctrlNo") String ctrlNo,
        @Param("warningTypeList") List<Integer> warningTypeList);

    AlarmRecord getWarningEvseRecordX(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("evseNo") String evseNo,
        @Param("warningTypeList") List<Integer> warningTypeList);

    long getWarningRecordCount(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("evseNoList") List<String> evseNoList,
        @Param("connectorId") Integer connectorId,
        @Param("warningTypeList") List<Integer> warningTypeList,
        @Param("sourceNo") List<String> sourceNo);

    List<AlarmRecord> getRecentPvGtiAlert(@Param("gwno") String gwno,
        @Param("recId") String recId);

    List<AlarmRecord> getRecentSrsAlert(@Param("gwno") String gwno,
        @Param("recId") String recId);

    List<AlarmRecord> getRecentSimAlert(@Param("dno") String dno,
        @Param("recId") String recId);

    List<AlarmRecord> getRecentEssAlert(@Param("gwno") String gwno,
        @Param("dno") String dno,
        @Param("essEquipId") Long essEquipId);

    long getWarningSiteCtrlRecordCount(@Param("alarmStatus") Integer alarmStatus,
        @Param("warningCode") String warningCode,
        @Param("ctrlNoList") List<String> ctrlNoList,
        @Param("warningTypeList") List<Integer> warningTypeList);


    /**
     * 获取系统级别告警记录 微服务Down掉 网关登录超时
     *
     * @param status      告警状态
     * @param warningCode 告警码
     * @param gwno        网关编号
     * @return 告警记录
     */
    List<AlarmRecord> getSysLeveWarningRecord(@Param("alarmStatus") Integer status,
        @Param("warningCode") String warningCode,
        @Param("gwno") String gwno,
        @Param("appName") String appName);

    List<AlarmRecord> evseErrorAlarms(@Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("plugId") Integer plugId,
        @Param("evseNo") String evseNo,
        @Param("siteId") String siteId);

    ListResponse<AlarmRecord> getWarningBiList(WarningBiParam param);

    ListResponse<AlarmRecord> getEvseWarningList(WarningBiParam param);

    List<AlarmRecord> getUnfinishedOfflineRecords(int days, int start, int size);

    ObjectResponse<Map<String, List<AlarmRecord>>> getRecentWarning(WarningBiParam param);

    ListResponse<WWarningSummaryRecord> getWarningSumaryList(WarningSummaryParam param);

    BaseResponse updateYwOrderByEvseList(UpdateWarningYwParam param);

    BaseResponse checkWarningBiListCount(WarningBiParam param);
}
