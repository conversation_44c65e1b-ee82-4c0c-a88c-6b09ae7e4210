package com.cdz360.iot.monitor.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: B端用户信息和关联的商户id
 * @date: 2019/03/22
 */
@Data
public class MerchantAndCommercial implements Serializable {

    /**
     * 编号
     **/
    private Long id;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 名称
     */
    private String username;
    /**
     * 所属商户id
     */
    private Long comId;
    /**
     * 角色id
     */
    private Integer commercialRole;
    /**
     * 所属商户id及子商户id列表
     */
    private List<Long> comIds;

}