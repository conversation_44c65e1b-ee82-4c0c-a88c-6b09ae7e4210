package com.cdz360.iot.monitor.constant;

//
//import com.chargerlink.device.common.constant.IErrorType;
//
///**
// * <AUTHOR>
// * @date Create on 2018/11/27 10:37
// */
//public enum MonitorErrorTypeEnum implements IErrorType {
//    SESSION_INVALID(3, "会话失效"),
//    REQUEST_ERROR(11, "服务请求报错");
//    private Integer errorCode;
//    private String errorMessage;
//
//    MonitorErrorTypeEnum(Integer errorCode, String errorMessage) {
//        this.errorCode = errorCode;
//        this.errorMessage = errorMessage;
//    }
//
//    @Override
//    public Integer getErrorCode() {
//        return errorCode;
//    }
//
//    @Override
//    public String getErrorMessage() {
//        return errorMessage;
//    }
//}
