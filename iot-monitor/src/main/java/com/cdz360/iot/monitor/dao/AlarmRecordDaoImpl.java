package com.cdz360.iot.monitor.dao;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipAlarmLangRoDs;
import com.cdz360.iot.ds.ro.ess.mapper.EssEquipAlarmLangRoMapper;
import com.cdz360.iot.model.alarm.dto.EssEquipAlarmLangDto;
import com.cdz360.iot.model.alarm.param.GetAlarmListParam;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.model.ess.param.ListAlarmLangParam;
import com.cdz360.iot.monitor.constant.AlarmEventTypeEnum;
import com.cdz360.iot.monitor.constant.AlarmTypeEnum;
import com.cdz360.iot.monitor.constant.DeviceStatusCodeType;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.po.WWarningSummaryRecord;
import com.cdz360.iot.monitor.entity.request.UpdateWarningYwParam;
import com.cdz360.iot.monitor.entity.request.WarningBiParam;
import com.cdz360.iot.monitor.entity.request.WarningSummaryParam;
import com.mongodb.client.result.UpdateResult;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 告警数据的dao层实现类
 *
 * <AUTHOR> Create on 2018.8.13 9:48
 */
@Component
@Slf4j
public class AlarmRecordDaoImpl implements AlarmRecordDao {

//    private Logger log = LoggerFactory.getLogger(AlarmRecordDaoImpl.class);

    @Autowired
    //@Qualifier(value = "basicMongoTemplate")
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedisIotReadService redisIotReadService;
    private Map<Integer, String> uploadDevice = new HashMap<>();// 上报设备对应文案

    @PostConstruct
    public void initRepos() {
        Arrays.asList(AlarmEventTypeEnum.values())
            .stream()
            .forEach(e -> uploadDevice.put(e.getValue(), e.getSource()));
    }


    /**
     * 查询告警列表
     */
    public List<AlarmRecord> getAlarmList(GetAlarmListParam paramIn) {
        Query query = new Query();
        if (CollectionUtils.isNotEmpty(paramIn.getAlarmStatusList())) {
            query.addCriteria(Criteria.where("status").in(paramIn.getAlarmStatusList()
                .stream().map(AlarmStatusEnum::getValue).collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(paramIn.getSiteIds())) {
            query.addCriteria(Criteria.where("siteId").in(paramIn.getSiteIds()));
        }
        if (CollectionUtils.isNotEmpty(paramIn.getEquipTypes())) {   // 设备类型
            query.addCriteria(Criteria.where("equipType").in(paramIn.getEquipTypes()
                .stream().map(EssEquipType::getCode).collect(Collectors.toList())));
        }
        query.with(Sort.by(Direction.DESC, "lst"));
        query.skip(paramIn.getStart()).limit(paramIn.getSize());
        log.debug("查询告警列表 query = {}", query);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    /**
     * 条件告警状态过滤条件
     *
     * @param alarmStatus 告警状态
     * @param query       查询
     */
    private void addAlarmStatusCondition(Integer alarmStatus, Query query) {
        if (alarmStatus.equals(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (!alarmStatus.equals(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue())) {
            List<Integer> integerList = new ArrayList<>();
            integerList.add(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
            integerList.add(AlarmStatusEnum.ALARM_STATUS_END_HAND.getValue());
            query.addCriteria(Criteria.where("status").in(integerList));
        }
    }


    @Override
    public void insertWWarningRecordsList(List<AlarmRecord> wWarningRecordsList) {
        wWarningRecordsList.stream().forEach(wWarningRecord -> {
            wWarningRecord.setStartTime(this.dual2MongoDate(wWarningRecord.getStartTime()));
            wWarningRecord.setEndTime(this.dual2MongoDate(wWarningRecord.getEndTime()));
            wWarningRecord.setWarningUpdateTime(
                this.dual2MongoDate(wWarningRecord.getWarningUpdateTime()));
        });
        mongoTemplate.insert(wWarningRecordsList, AlarmRecord.class);
    }

    @Override
    public void insertWarningRecord(@Param("warningRecord") AlarmRecord warningRecord) {
        // wWarningRecordsList.stream().forEach(wWarningRecord -> {
        warningRecord.setStartTime(this.dual2MongoDate(warningRecord.getStartTime()));
        warningRecord.setEndTime(this.dual2MongoDate(warningRecord.getEndTime()));
        warningRecord.setWarningUpdateTime(
            this.dual2MongoDate(warningRecord.getWarningUpdateTime()));
        //});
        mongoTemplate.insert(warningRecord);
        log.debug("新增告警记录 {}", JsonUtils.toJsonString(warningRecord));
    }


    @Override
    public void updateBatchStatusById(List<Long> recordIdList, AlarmRecord wWarningRecord) {
        if (recordIdList == null || recordIdList.size() == 0) {
            log.error("更新条件recordIdList为空不更新");
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("warningId").in(recordIdList));
        Update update = new Update();
        if (wWarningRecord.getStatus() != null) {
            //状态为未结束才允许结束
            query.addCriteria(
                Criteria.where("status").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
            update.set("status", wWarningRecord.getStatus());
        }
        if (wWarningRecord.getEndTime() != null) {
            update.set("endTime", this.dual2MongoDate(wWarningRecord.getEndTime()));
        }
        if (wWarningRecord.getUpdateBy() != null) {
            update.set("updateBy", wWarningRecord.getUpdateBy());
        }
        if (wWarningRecord.getError() != null) {
            update.set("error", wWarningRecord.getError());
        }
        if (wWarningRecord.getTemp() != null && wWarningRecord.getTemp() != 0) {
            update.set("temp", wWarningRecord.getTemp());
        }
        //逆变器状态
        if (wWarningRecord.getRtStatus() != null) {
            update.set("rtStatus", wWarningRecord.getRtStatus());
        }
        //设备型号
        if (wWarningRecord.getModelName() != null) {
            update.set("modelName", wWarningRecord.getModelName());
        }
        //软件版本
        if (wWarningRecord.getFirmwareVer() != null) {
            update.set("firmwareVer", wWarningRecord.getFirmwareVer());
        }
        //订单号
        if (StringUtils.isNotEmpty(wWarningRecord.getOrderNo())) {
            update.set("orderNo", wWarningRecord.getOrderNo());
        }

        if (StringUtils.isNotEmpty(wWarningRecord.getPlugName())) {
            update.set("plugName", wWarningRecord.getPlugName());
        }

        if (wWarningRecord.getEvseName() != null) {
            update.set("evseName", wWarningRecord.getEvseName());
        }
        if (StringUtils.isNotBlank(wWarningRecord.getWarningInstructions())) {
            update.set("warningInstructions", wWarningRecord.getWarningInstructions());
        }
        if (StringUtils.isNotBlank(wWarningRecord.getEvseName())) {
            update.set("evseName", wWarningRecord.getEvseName());
        }
        if (StringUtils.isNotBlank(wWarningRecord.getCtrlName())) {
            update.set("ctrlName", wWarningRecord.getCtrlName());
        }
        update.set("warningUpdateTime", this.dual2MongoDate(new Date()));
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, AlarmRecord.class);
        log.info("条件 {} 修改完成 {}", query,
            updateResult.getMatchedCount());
    }


    public void batchStopAlarms(AlarmRecord record, String dno, List<Long> alarmCodes) {
        if (alarmCodes == null || alarmCodes.size() == 0) {
            log.error("更新条件alarmCodes为空不更新");
            return;
        }
        Query query = new Query();
        if (CollectionUtils.isNotEmpty(alarmCodes)) {
            query.addCriteria(Criteria.where("errorCode")
                .in(alarmCodes));
        }
        query.addCriteria(Criteria.where("dno").is(dno));
        Update update = new Update();
//        if (wWarningRecord.getStatus() != null) {
        //状态为未结束才允许结束
        query.addCriteria(
            Criteria.where("status").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
        update.set("status", record.getStatus());
//        }
//        if (wWarningRecord.getEndTime() != null) {
        update.set("endTime", this.dual2MongoDate(record.getEndTime()));
        update.set("let", record.getLet());
//        }
        if (record.getUpdateBy() != null) {
            update.set("updateBy", record.getUpdateBy());
        }

        update.set("warningUpdateTime", this.dual2MongoDate(new Date()));
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, AlarmRecord.class);
        log.info("条件 {} 修改完成{}", query,
            updateResult.getMatchedCount());
    }

    /**
     * 获取未关闭的故障列表
     */
    public List<AlarmRecord> getActiveRecords(@Param("start") long start,
        @Param("size") int size) {
        // 查询条件
        Criteria criteria = Criteria.where("status")
            .is(Integer.valueOf(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
        Query query = new Query();
        query.addCriteria(criteria);
        query.with(Sort.by(Direction.ASC, "startTime"));
        query.skip(start).limit(size);
        return mongoTemplate.find(query, AlarmRecord.class);
    }


    @Override
    public List<AlarmRecord> getWarningRecord(Integer alarmStatus,
        String alarmCode,
        List<String> evseNoList,
        Integer connectorId,
        List<Integer> warningTypeList,
        List<String> sourceNo) {
        Query query = new Query();
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (null != evseNoList) {
            query.addCriteria(Criteria.where("boxOutFactoryCode").in(evseNoList));
        }
        if (null != connectorId) {
            if (connectorId.intValue() >= 0) {
                query.addCriteria(Criteria.where("connectorId").is(connectorId));
            }
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (warningTypeList != null && warningTypeList.size() > 0) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        if (null != sourceNo) {
            query.addCriteria(Criteria.where("sourceNo").in(sourceNo));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    @Override
    public List<AlarmRecord> getWarningSiteCtrlRecord(Integer alarmStatus,
        String warningCode,
        List<String> ctrlNoList,
        List<Integer> warningTypeList) {
        return this.getWarningRecord(alarmStatus, warningCode, null, -1, warningTypeList,
            ctrlNoList);
    }

    @Override
    public AlarmRecord getWarningRecordX(Integer alarmStatus,
        String alarmCode,
        String evseNo,
        Integer connectorId,
        List<Integer> warningTypeList) {
        Query query = new Query();

        //if (null != evseNo) {
        query.addCriteria(Criteria.where("boxOutFactoryCode").is(evseNo));
        //}
        if (null != connectorId) {
            query.addCriteria(Criteria.where("connectorId").is(connectorId));
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (warningTypeList != null && warningTypeList.size() > 0) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.findOne(query, AlarmRecord.class);
    }

    @Override
    public AlarmRecord getWarningSiteCtrlRecordX(Integer alarmStatus,
        String warningCode,
        String ctrlNo,
        List<Integer> warningTypeList) {
        return this.getWarningRecordX(alarmStatus, warningCode, ctrlNo, null, warningTypeList);
    }

    @Override
    public AlarmRecord getWarningEvseRecordX(Integer alarmStatus, String warningCode,
        String evseNo, List<Integer> warningTypeList) {
        return this.getWarningRecordX(alarmStatus, warningCode, evseNo, 0, warningTypeList);
    }

    @Override
    public long getWarningRecordCount(Integer alarmStatus,
        String alarmCode,
        List<String> evseNoList,
        Integer connectorId,
        List<Integer> warningTypeList,
        List<String> sourceNo) {
        Query query = new Query();
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (CollectionUtils.isNotEmpty(evseNoList)) {
            query.addCriteria(Criteria.where("boxOutFactoryCode").in(evseNoList));
        }
        if (null != connectorId) {
            if (connectorId.intValue() >= 0) {
                query.addCriteria(Criteria.where("connectorId").is(connectorId));
            }
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (CollectionUtils.isNotEmpty(warningTypeList)) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        if (CollectionUtils.isNotEmpty(sourceNo)) {
            query.addCriteria(Criteria.where("sourceNo").in(sourceNo));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.count(query, AlarmRecord.class);
    }

    public List<AlarmRecord> getRecentPvGtiAlert(String gwno, String recId) {
        // 查询条件
        Criteria criteria = Criteria.where("deviceId").is(gwno)
            .and("connectorId").is(Integer.valueOf(recId))
            .and("warningType").is(AlarmEventTypeEnum.ALARM_PV_WARNING.getValue())
            .and("status").is(Integer.valueOf(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
        Query query = new Query();
        query.addCriteria(criteria);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    public List<AlarmRecord> getRecentSrsAlert(String gwno, String recId) {
        // 查询条件
        Criteria criteria = Criteria.where("deviceId").is(gwno)
            .and("connectorId").is(Integer.valueOf(recId))
            .and("warningType").is(AlarmEventTypeEnum.ALARM_SRS_WARNING.getValue())
            .and("status").is(Integer.valueOf(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
        Query query = new Query();
        query.addCriteria(criteria);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    public List<AlarmRecord> getRecentSimAlert(String dno, String recId) {
        // 查询条件
        Criteria criteria = Criteria.where("boxOutFactoryCode").is(dno)
            .and("connectorId").is(Integer.valueOf(recId))
            .and("warningType").is(AlarmEventTypeEnum.ALARM_SIM_WARNING.getValue())
            .and("status").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
        Query query = new Query();
        query.addCriteria(criteria);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    public List<AlarmRecord> getRecentEssAlert(String gwno, String dno, Long essEquipId) {
        // 查询条件
        Criteria criteria = Criteria.where("deviceId").is(gwno)
            .and("dno").is(dno)
            .and("warningType").is(AlarmEventTypeEnum.ALARM_ESS_WARNING.getValue())
            .and("equipId").is(Integer.valueOf(Math.toIntExact(essEquipId)))
            .and("status").is(Integer.valueOf(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
        Query query = new Query();
        query.addCriteria(criteria);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    @Override
    public long getWarningSiteCtrlRecordCount(Integer alarmStatus,
        String warningCode,
        List<String> ctrlNoList,
        List<Integer> warningTypeList) {
        return this.getWarningRecordCount(alarmStatus,
            warningCode,
            null,
            -1,//忽略枪号
            warningTypeList,
            ctrlNoList);

    }

    @Override
    public List<AlarmRecord> getSysLeveWarningRecord(Integer status, String warningCode,
        String gwno, String appName) {
        Query query = new Query();
        if (null != status) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (null != warningCode && !"0".equals(warningCode)) {
            query.addCriteria(Criteria.where("warningCode").is(warningCode));
        }
        if (StringUtils.isNotBlank(gwno)) {
            query.addCriteria(Criteria.where("gwno").is(gwno));
        }
        if (StringUtils.isNotBlank(appName)) {
            query.addCriteria(Criteria.where("appName").is(appName));
        }
        log.info(">>getGwTimeoutWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    @Override
    public List<AlarmRecord> evseErrorAlarms(Date startTime, Date endTime, Integer plugId,
        String evseNo, String siteId) {
        log.info(
            ">> 从mongodb中获取告警数据: startTime={},EndTime={},plugId={},evseNo={},siteId={}",
            startTime,
            endTime,
            plugId,
            evseNo,
            siteId);

        // 查询条件
        Criteria criteria = Criteria.where("siteId").is(siteId)
//                .and("boxOutFactoryCode").is(evseNo)
            .and("startTime").gte(startTime).lt(endTime);

        if (plugId != null) {
            criteria = criteria.and("connectorId").is(plugId);
        }

        if (StringUtils.isNotEmpty(evseNo)) {
            criteria = criteria.and("boxOutFactoryCode").is(evseNo);
        }

        log.info("从mongodb中获取告警数据的查询条件: {}", JsonUtils.toJsonString(criteria));

        List<AlarmRecord> wWarningRecordInMongo = mongoTemplate.find(new Query(criteria),
            AlarmRecord.class);

        if (CollectionUtils.isEmpty(wWarningRecordInMongo)) {
            log.info("数据不存在.");
        } else {
            log.debug("<< 查询结果: wWarningRecordInMongo = {}",
                JsonUtils.toJsonString(wWarningRecordInMongo));
        }
        return wWarningRecordInMongo;
    }

    public Date dual2MongoDate(Date date) {
        return date;
    }

    @Override
    public ListResponse<AlarmRecord> getWarningBiList(WarningBiParam param) {
        Assert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");

        Query query = this.warningBiParamToQuery(param);

        long total = mongoTemplate.count(query, AlarmRecord.class);
        log.info("total= {}", total);
        query.with(Sort.by(Direction.DESC, "startTime"));
        query.skip(param.getStart()).limit(param.getSize());
        List<AlarmRecord> list = mongoTemplate.find(query, AlarmRecord.class);
        log.info("list.size= {}", list == null ? null : list.size());
        list.stream().forEach(e -> {
            if (StringUtils.isNotBlank(e.getWarningCode())) {
                int warningCode = Integer.valueOf(e.getWarningCode());
                String boxOutFactoryCode =
                    StringUtils.isBlank(e.getBoxOutFactoryCode()) ? "" : e.getBoxOutFactoryCode();

                String deviceName = null;

                if (StringUtils.isNotBlank(boxOutFactoryCode)) {
                    if (DeviceStatusCodeType.C2001.getCode() <= warningCode &&
                        warningCode <= DeviceStatusCodeType.C2999.getCode()) {
                        // 控制器的状态
                        e.setBoxOutFactoryCode("控制器\n" + boxOutFactoryCode);
                        if (StringUtils.isNotBlank(e.getCtrlName())) {
                            deviceName = "(" + e.getCtrlName() + ")";
                        }
                    } else if (DeviceStatusCodeType.C20000.getCode() == warningCode) {
                        e.setBoxOutFactoryCode("辐射仪\n" + boxOutFactoryCode);
                        if (StringUtils.isNotBlank(e.getCtrlName())) {
                            deviceName = "(" + e.getCtrlName() + ")";
                        }
                    } else if (DeviceStatusCodeType.C30000.getCode() == warningCode) {
                        e.setBoxOutFactoryCode("SIM卡\n" + boxOutFactoryCode);
                        if (StringUtils.isNotBlank(e.getCtrlName())) {
                            deviceName = "(" + e.getCtrlName() + ")";
                        }
                    } else {
                        e.setBoxOutFactoryCode("充电桩\n" + boxOutFactoryCode);
                        if (StringUtils.isNotBlank(e.getEvseName())) {
                            deviceName = "(" + e.getEvseName() + ")";
                        }
                    }
                }

                String sourceNo = StringUtils.isBlank(e.getSourceNo()) ?
                    boxOutFactoryCode :
                    e.getSourceNo();

                if (StringUtils.isNotBlank(deviceName)) {
                    e.setBoxOutFactoryCode(e.getBoxOutFactoryCode() + deviceName);
                }

                if (uploadDevice.get(e.getWarningType()) != null) {
                    sourceNo = String.format("%s\n%s", uploadDevice.get(e.getWarningType()),
                        sourceNo);
                }

                //光伏逆变器
                if (e.getWarningType().equals(AlarmEventTypeEnum.ALARM_PV_WARNING.getValue())) {
                    //告警对象
                    StringBuilder stringBuilder = new StringBuilder()
                        .append("光伏逆变器\n");

                    if (e.getConnectorId() != null) {
                        stringBuilder.append(e.getConnectorId());
                    }
                    if (StringUtils.isNotEmpty(e.getPlugName())) {
                        stringBuilder.append("(").append(e.getPlugName()).append(")");
                    }
                    e.setBoxOutFactoryCode(stringBuilder.toString());

                    //上报设备
                    StringBuilder builder = new StringBuilder();
                    if (StringUtils.isNotEmpty(e.getDeviceId())) {
                        builder.append("光储充控制器\n")
                            .append(e.getDeviceId())
                            .append("(").append(e.getEvseName()).append(")");
                        sourceNo = builder.toString();
                    } else {
                        sourceNo = "--";
                    }
                }
                //储能ESS
                if (e.getWarningType().equals(AlarmEventTypeEnum.ALARM_ESS_WARNING.getValue())) {
                    //告警对象
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append("储能ESS\n");
                    if (StringUtils.isNotEmpty(e.getEssEquipName())) {
                        stringBuilder.append(e.getEssEquipName());
                    }
                    if (StringUtils.isNotEmpty(e.getDno())) {
                        stringBuilder.append("(").append(e.getDno()).append(")");
                    }
                    e.setBoxOutFactoryCode(stringBuilder.toString());

                    //上报设备
                    StringBuilder builder = new StringBuilder();
                    builder.append("控制器\n");
                    if (e.getDeviceId() != null) {
                        builder.append(e.getDeviceId());
                    }
                    if (StringUtils.isNotEmpty(e.getEvseName())) {
                        builder.append("(").append(e.getEvseName()).append(")");
                    }
                    sourceNo = builder.toString();
                }
                e.setSourceNo(sourceNo);
            }
        });

        return RestUtils.buildListResponse(list, total);
    }

    public List<AlarmRecord> getUnfinishedOfflineRecords(int days, int start, int size) {
        WarningBiParam param = new WarningBiParam();

        ZonedDateTime endtTime = ZonedDateTime.now();
        ZonedDateTime starTime = endtTime.minusDays(days);
        TimeFilter tf = new TimeFilter();
        tf.setStartTime(Date.from(starTime.toInstant()));
        tf.setEndTime(Date.from(endtTime.toInstant()));
        param.setWarnStartTimeFilter(tf);
        param.setWarningCode(AlarmTypeEnum.ALARM_CONNECT_LOST.getValue());
        param.setStatus(List.of(AlarmStatusEnum.ALARM_STATUS_NOT_END));
        param.setWarningType(
            List.of(AlarmEventTypeEnum.ALARM_WARNING, AlarmEventTypeEnum.ALARM_MALFUNCTION));

        Query query = this.warningBiParamToQuery(param);
        query.with(Sort.by(Sort.Direction.DESC, "startTime"));
        query.skip(start).limit(size);
        return mongoTemplate.find(query, AlarmRecord.class);
    }

    @Override
    public ObjectResponse<Map<String, List<AlarmRecord>>> getRecentWarning(
        WarningBiParam param) {
        Assert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");
        Map<String, List<AlarmRecord>> map = new HashMap<>();
        param.getEvseNoList().stream().forEach(e -> {
            param.setSourceNo(e);
            Query query = this.warningBiParamToQuery(param);
            query.with(Sort.by(Direction.DESC, "startTime"));
            query.skip(param.getStart()).limit(param.getSize());
            List<AlarmRecord> list = mongoTemplate.find(query, AlarmRecord.class);

            if (CollectionUtils.isNotEmpty(list)) {
                List<AlarmRecord> list1 = list.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(o -> o.getWarningCode()))),
                        ArrayList::new));
                map.put(e, list1);
            }
        });
        return RestUtils.buildObjectResponse(map);
    }

    @Override
    public ListResponse<WWarningSummaryRecord> getWarningSumaryList(WarningSummaryParam param) {

        Assert.isTrue(param.getStart() != null && param.getSize() != null, "分页参数不能为空");
        List<AggregationOperation> operations = new ArrayList<>();
        //站点
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            operations.add(Aggregation.match(Criteria.where("siteId")
                .in(param.getSiteIdList().stream().map(String::valueOf)
                    .collect(Collectors.toList()))));
        }
        //告警时间
        if (param.getStartTime() != null) {
            operations.add(Aggregation.match(Criteria.where("startTime")
                .gte(param.getStartTime())));
        }
        if (param.getEndTime() != null) {
            operations.add(Aggregation.match(Criteria.where("startTime")
                .lt(param.getEndTime())));
        }
        //告警名称
        if (CollectionUtils.isNotEmpty(param.getWarningCodeList())) {
            operations.add(Aggregation.match(Criteria.where("warningCode")
                .in(param.getWarningCodeList().stream().map(String::valueOf)
                    .collect(Collectors.toList()))));
        }
        //设备型号
        if (CollectionUtils.isNotEmpty(param.getModelNameList())) {
            //仅存在空字符串，只查询不存在modelName 的文档
            if (param.getModelNameList().contains("") && param.getModelNameList().size() == 1) {
                operations.add(Aggregation.match(Criteria.where("modelName").exists(false)));
            } else if (!param.getModelNameList().contains("")) {
                //不存在空字符串，查询存在modelName的文档
                operations.add(Aggregation.match(Criteria.where("modelName").exists(true)));
                operations.add(Aggregation.match(Criteria.where("modelName")
                    .in(param.getModelNameList().stream().map(String::valueOf)
                        .collect(Collectors.toList()))));
            } else {
                operations.add(Aggregation.match(Criteria.where("modelName")
                    .in(param.getModelNameList().stream().map(String::valueOf)
                        .collect(Collectors.toList()))));
            }
        }
        //软件版本
        if (CollectionUtils.isNotEmpty(param.getFirmwareVerList())) {
            //为空，只查询不存在
            if (param.getFirmwareVerList().contains("") && param.getFirmwareVerList().size() == 1) {
                operations.add(Aggregation.match(Criteria.where("firmwareVer").exists(false)));
            } else if (!param.getFirmwareVerList().contains("")) {
                //不包含空，字段存在
                operations.add(Aggregation.match(Criteria.where("firmwareVer").exists(true)));
                operations.add(Aggregation.match(Criteria.where("firmwareVer")
                    .in(param.getFirmwareVerList().stream().map(String::valueOf)
                        .collect(Collectors.toList()))));
            } else {
                operations.add(Aggregation.match(Criteria.where("firmwareVer")
                    .in(param.getFirmwareVerList().stream().map(String::valueOf)
                        .collect(Collectors.toList()))));
            }
        }
        if (param.getGroupBy() == null) {
            throw new DcServiceException("汇总方式不能为空");
        }
        //条件不能为空
//        operations.add(Aggregation.match(Criteria.where("modelName").exists(true)));
//        operations.add(Aggregation.match(Criteria.where("firmwareVer").exists(true)));
//        operations.add(Aggregation.match(Criteria.where("siteId").exists(true)));
        //分组
        operations.add(Aggregation.group(param.getGroupBy()).first(param.getGroupBy())
            .as(param.getGroupBy()).count().as("warningAccount")
            .addToSet("siteId").as("siteIdList") //站点分布
            .addToSet("modelName").as("modelNameList") //设备型号
            .addToSet("firmwareVer").as("firmwareList") //软件版本
            .addToSet("warningCode").as("warningCodeList") //告警分布
            .addToSet("warningName").as("warningNameList") //告警名称
            .addToSet("siteName").as("siteNameList") //场站名称
        );

        Aggregation aggregation = Aggregation.newAggregation(operations);

        AggregationResults<WWarningSummaryRecord> results = mongoTemplate.aggregate(aggregation,
            "w_warning_record", WWarningSummaryRecord.class);
        int total = results.getMappedResults().size();
        log.info("param={}", results.getMappedResults());
        if (total == 0) {
            return new ListResponse<>(null, Long.valueOf(total));
        }

        //排序
        if (param.getOrderBy() != null && param.getOrderBy().equals("ASC")) {
            operations.add(Aggregation.sort(Direction.ASC, "warningAccount"));
        } else {
            operations.add(Aggregation.sort(Direction.DESC, "warningAccount"));
        }

        //分页
        operations.add(Aggregation.skip(param.getStart()));
        operations.add(Aggregation.limit(param.getSize()));

        Aggregation aggregation1 = Aggregation.newAggregation(operations);
        AggregationResults<WWarningSummaryRecord> result = mongoTemplate.aggregate(aggregation1,
            "w_warning_record", WWarningSummaryRecord.class);
        //排序
        return new ListResponse<>(result.getMappedResults(), Long.valueOf(total));


    }

    private Query warningBiParamToQuery(WarningBiParam param) {
        Query query = new Query();
        String evseNo = param.getEvseNo();  // 设备编号， 优先取 evseNo
        if (StringUtils.isBlank(evseNo)) {
            evseNo = param.getBoxOutFactoryCode();  // 设备编号， 如果evseNo没有， 则取 boxOutFactoryCode
        }
        if (param.getWarnStartTimeFilter() != null) {
            query.addCriteria(Criteria.where("startTime")
                .gte(param.getWarnStartTimeFilter().getStartTime())
                .lt(param.getWarnStartTimeFilter().getEndTime()));
        }
        if (param.getWarnEndTimeFilter() != null) {
            query.addCriteria(Criteria.where("endTime")
                .gte(param.getWarnEndTimeFilter().getStartTime())
                .lt(param.getWarnEndTimeFilter().getEndTime()));
        }
        if (param.getWarningId() != null) {
            query.addCriteria(Criteria.where("warningId").is(param.getWarningId()));
        }
        if (StringUtils.isNotBlank(param.getSourceNo())
            && StringUtils.isNotBlank(param.getBoxOutFactoryCode())) {

            Pattern pattern = Pattern.compile(param.getBoxOutFactoryCode());
            Pattern pattern1 = Pattern.compile(param.getSourceNo());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("boxOutFactoryCode").regex(pattern),
                    Criteria.where("evseName").regex(pattern),
                    Criteria.where("ctrlName").regex(pattern),
                    Criteria.where("plugName").regex(pattern),
                    Criteria.where("sourceNo").exists(false).and("boxOutFactoryCode")
                        .regex(pattern1),
                    Criteria.where("sourceNo").regex(pattern1)
                ));
            this.connectorIdCriteriaHandler(temp, param.getBoxOutFactoryCode());

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        } else if (StringUtils.isNotBlank(param.getBoxOutFactoryCode())) {
            Pattern pattern = Pattern.compile(param.getBoxOutFactoryCode());

            List<Criteria> temp = new ArrayList<>(
                List.of(
                    Criteria.where("boxOutFactoryCode").regex(pattern),
                    Criteria.where("evseName").regex(pattern),
                    Criteria.where("ctrlName").regex(pattern),
                    Criteria.where("plugName").regex(pattern)
                ));
            this.connectorIdCriteriaHandler(temp, param.getBoxOutFactoryCode());

            query.addCriteria(new Criteria()
                .orOperator(temp.toArray(new Criteria[0])));
        } else if (StringUtils.isNotBlank(param.getSourceNo())) {
            Pattern pattern = Pattern.compile(param.getSourceNo());
            query.addCriteria(new Criteria()
                .orOperator(Criteria.where("sourceNo").exists(false)
                        .and("boxOutFactoryCode").regex(pattern),
                    Criteria.where("sourceNo").regex(pattern)));
        }

        //桩管家查询
        if (StringUtils.isNotEmpty(param.getEvseName())) {
            Pattern pattern = Pattern.compile(param.getEvseName());
            query.addCriteria(Criteria.where("evseName").regex(pattern));
        }
        if (StringUtils.isNotEmpty(param.getEvseNo())) {
            Pattern pattern = Pattern.compile(param.getEvseNo());
            query.addCriteria(Criteria.where("deviceId").regex(pattern));
        }

        if (CollectionUtils.isNotEmpty(param.getWarningType())) {
            query.addCriteria(Criteria.where("warningType").in(param.getWarningType().stream().
                map(AlarmEventTypeEnum::getValue).collect(Collectors.toList())));
        }
        if (StringUtils.isNotBlank(param.getWarningCode())) {
            query.addCriteria(Criteria.where("warningCode").is(param.getWarningCode()));
        }

        //是否剔除桩离线
        if (param.getIsContainOffline() != null && Boolean.TRUE.equals(
            param.getIsContainOffline())) {
            List<String> codeList = DeviceStatusCodeType.getCodeList();
            List<String> collect = codeList.stream()
                .filter(e -> !e.equals(String.valueOf(DeviceStatusCodeType.C1101.getCode())))
                .collect(Collectors.toList());
            query.addCriteria(Criteria.where("warningCode").in(collect));
        }
        if (CollectionUtils.isNotEmpty(param.getStatus())) {
            query.addCriteria(Criteria.where("status").in(param.getStatus().stream()
                .map(AlarmStatusEnum::getValue).collect(Collectors.toList())));
        }
        if (StringUtils.isNotBlank(param.getWarningName())) {
            Pattern pattern = Pattern.compile(param.getWarningName(), Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("warningName").regex(pattern));
        }
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            if (StringUtils.isNotBlank(evseNo)) {
                log.info("单独查询桩 {} 的告警,忽略场站ID字段", evseNo);
            } else {
                query.addCriteria(Criteria.where("siteId").in(param.getSiteIdList()));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getWarningCodeList())) {
            query.addCriteria(Criteria.where("warningCode").in(param.getWarningCodeList()));
        }
        if (CollectionUtils.isNotEmpty(param.getCommList())) {
            query.addCriteria(Criteria.where("businessId")
                .in(param.getCommList().stream().map(String::valueOf)
                    .collect(Collectors.toList())));
        }
        if (StringUtils.isNotBlank(param.getWarningInstructions())) {
            Pattern pattern = Pattern.compile(param.getWarningInstructions());
            query.addCriteria(Criteria.where("warningInstructions").regex(pattern));
        }
        return query;
    }

    private void connectorIdCriteriaHandler(List<Criteria> temp, String boxOutFactoryCode) {
        try {
            temp.add(Criteria.where("connectorId").is(Integer.parseInt(boxOutFactoryCode)));
        } catch (Exception ignored) {
            // nothing to do
        }
    }

//    /**
//     * 告警分类条件
//     *
//     * @param param
//     * @return
//     */
//    private Query warningSummaryParamToQuery(WarningSummaryParam param) {
//        Query query = new Query();
//        //告警code
//        if (CollectionUtils.isNotEmpty(param.getWarningCodeList())) {
//            query.addCriteria(Criteria.where("warningCode")
//                .in(param.getWarningCodeList().stream().map(String::valueOf)
//                    .collect(Collectors.toList())));
//        }
//        //取告警发生时间
//        if (param.getStartTime() != null) {
//            query.addCriteria(Criteria.where("startTime")
//                .gte(param.getStartTime()));
//        }
//        if (param.getEndTime() != null) {
//            query.addCriteria(Criteria.where("startTime")
//                .lt(param.getEndTime()));
//        }
//        //站点
//        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
//            query.addCriteria(Criteria.where("siteId")
//                .in(param.getSiteIdList().stream().map(String::valueOf)
//                    .collect(Collectors.toList())));
//        }
//        //设备型号
//        if (CollectionUtils.isNotEmpty(param.getModelNameList())) {
//            query.addCriteria(Criteria.where("modelName")
//                .in(param.getModelNameList().stream().map(String::valueOf)
//                    .collect(Collectors.toList())));
//        }
//        //软件版本
//        if (CollectionUtils.isNotEmpty(param.getFirmwareVerList())) {
//            query.addCriteria(Criteria.where("firmwareVer")
//                .in(param.getFirmwareVerList().stream().map(String::valueOf)
//                    .collect(Collectors.toList())));
//
//        }
//
//        return query;
//    }

    @Override
    public BaseResponse checkWarningBiListCount(WarningBiParam param) {

        Query query = this.warningBiParamToQuery(param);

        long total = mongoTemplate.count(query, AlarmRecord.class);

        if (total > 100000) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }
        return RestUtils.success();
    }


    /**
     * 用于快速创建运维工单，查询告警持续{n}分钟的桩
     */
    @Override
    public ListResponse<AlarmRecord> getEvseWarningList(WarningBiParam param) {
        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            return RestUtils.buildListResponse(null);
        }
        Query query = new Query();
        // 设备
        query.addCriteria(Criteria.where("deviceId").in(param.getEvseNoList()));
        // 忽略的告警码
        if (CollectionUtils.isNotEmpty(param.getIgnoreWarningCodeList())) {
            query.addCriteria(Criteria.where("warningCode").nin(param.getIgnoreWarningCodeList()));
        }
        // 告警状态
        if (CollectionUtils.isNotEmpty(param.getStatus())) {
            query.addCriteria(Criteria.where("status").in(param.getStatus().stream()
                .map(AlarmStatusEnum::getValue).collect(Collectors.toList())));
        }

        if (param.getWarningDuration() != null) {
            LocalDateTime now = LocalDateTime.now();  // 当前时间
            LocalDateTime endDate = now.minusMinutes(param.getWarningDuration());  // 当前时间向前推 n 分钟
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 构建查询条件
            query.addCriteria(Criteria.where("startTime").lt(endDate));
        }
        // 未创建工单
        query.addCriteria(
            new Criteria().orOperator(
                Criteria.where("ywOrderNo").exists(false), // 字段不存在
                Criteria.where("ywOrderNo").is(null), // 字段值为null
                Criteria.where("ywOrderNo").is("") // 字段值为空字符串，如果适用的话
            )
        );

        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(500);
        }
        query.skip(param.getStart()).limit(param.getSize());
        List<AlarmRecord> list = mongoTemplate.find(query, AlarmRecord.class);
        return RestUtils.buildListResponse(list, 0);
    }

    @Override
    public BaseResponse updateYwOrderByEvseList(UpdateWarningYwParam param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()), "桩编号不能为空");
        IotAssert.isNotBlank(param.getYwOrderNo(), "工单号不能为空");

        Query query = new Query();
        query.addCriteria(Criteria.where("deviceId").in(param.getEvseNoList()));
        query.addCriteria(Criteria.where("status")
            .in(List.of(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()))); // 为结束的告警

        if (CollectionUtils.isNotEmpty(param.getIgnoreWarningCodeList())) {
            query.addCriteria(
                Criteria.where("warningCode").nin(param.getIgnoreWarningCodeList())); // 忽略的故障码
        }
        // 更新内容
        Update update = new Update();
        update.set("ywOrderNo", param.getYwOrderNo());
        update.set("warningUpdateTime", this.dual2MongoDate(new Date()));

        mongoTemplate.updateMulti(query, update, AlarmRecord.class);
        return RestUtils.success();
    }
}
