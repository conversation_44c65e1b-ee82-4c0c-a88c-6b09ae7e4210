package com.cdz360.iot.monitor.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.monitor.entity.result.WarningDetailVo;
import com.cdz360.iot.monitor.mapper.WarningDetailMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 告警描述说明配置表
 *
 * @ClassName WarningDetailServiceImpl
 * <AUTHOR>
 * @Description
 * @Date 2019.4.16
 */
@Slf4j
@Service
public class WarningDetailServiceImpl implements WarningDetailService {

    @Autowired
    private WarningDetailMapper warningDetailMapper;

    /**
     * 根据条件查询告警描述说明配置
     *
     * @param warningDetailVo
     * @return
     */
    @Override
    public List<WarningDetailVo> queryWarningDetail(WarningDetailVo warningDetailVo) {

        List<WarningDetailVo> list = warningDetailMapper.queryWarningDetailList(warningDetailVo);

        log.info("根据条件查询告警描述说明配置条件{}结果{}",
            JsonUtils.toJsonString(warningDetailVo), JsonUtils.toJsonString(list));
        return list;
    }
}
