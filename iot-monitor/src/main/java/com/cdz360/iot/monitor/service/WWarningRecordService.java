package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.EssVo.ErrorEquip;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.request.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 处理桩端上报service服务类
 *
 * <AUTHOR>
 * @date Create on 2018/09/13 19:39
 */
public interface WWarningRecordService {

    /**
     * 处理桩端上报告警插入
     *
     * @param acAlarmInfo 桩端上报消息体
     */
    void insertWWarningRecordByDevice(MonitorAlarmRequest acAlarmInfo) throws DcServiceException;

    /**
     * 处理控制器上报告警插入
     * @param monitorSiteCtrlRequest
     * @throws DcServiceException
     */
    void insertWWarningSiteCtrlRecordByDevice(MonitorSiteCtrlRequest monitorSiteCtrlRequest) throws DcServiceException;

    void insertWWarningEvseRecordByDevice(MonitorEvseRequest request) throws DcServiceException;

    void insertWWarningEvseRecordByPv(MonitorPvGtiRequest request) throws DcServiceException;

    void insertWWarningEvseRecordBySrs(MonitorPvGtiRequest request) throws DcServiceException;

    void insertWWarningEvseRecordBySim(MonitorPvGtiRequest request) throws DcServiceException;

    void insertWWarningEssRecordByEssEquip(MonitorEssRequest request) throws DcServiceException;

    List<AlarmRecord> addEquipAlarmRecord(EssEquipType equipType, ErrorEquip equip, List<Long> alarmCodes,
        EssVo<?> ess, LocalDateTime lts, String tz);

    void updateWWarningEvseRecordByPv(List<Long> warningIdList) throws DcServiceException;

    void updateWWarningEvseRecordBySrs(List<Long> warningIdList) throws DcServiceException;

    void updateWWarningEvseRecordBySim(List<Long> warningIdList) throws DcServiceException;

    void updateWWarningEssRecordByEssEquip(List<Long> warningIdList) throws DcServiceException;

    /**
     * 批量终止设备告警， 按设备号（dno）和告警码结束告警
     */
    void stopEquipAlarms(EssEquipType equipType, String dno, List<Long> alarmCodes, LocalDateTime lts);

    long notEndWarningRecordNum(MonitorAlarmRequest monitorAlarmRequest);

    void sendSysLeveAlarmToMassage(SysAlarmMsgRequest request);

    void sendTransFinshAlarmToMassage(TransFinshAlarmRequest request);
}
