package com.cdz360.iot.monitor.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 文件资源配置
 *
 * <AUTHOR>
 * Create on 2018/7/25 9:35
 */
@Data
@Component
public class PropertiesConfig {


    /**
     * 邮件发送地址
     */
    @Value("${spring.mail.fromAdress:mailFromAddress}")
    private String mailFromAddress;


//    /**
//     * 短信发送API KEY
//     */
//    @Value("${_chargerlink.sms_apikey:smsApiKey:smsApiKey}")
//    private String smsApiKey;
//
//    /**
//     * 告警短信模板
//     */
//    @Value("${_chargerlink.alarm.alarm_sms_module_id:1000000}")
//    private Long alarmSmsModuleId;
//
//    /**
//     * 调用消息中心API推送短信消息
//     */
//    @Value("${_chargerlink.message_center.message_push_sms_url:https://sms.yunpian.com/v2/sms/single_send.json}")
//    private String messagePushSmsUrl;
//
//    /**
//     * 网关登录超时告警短信模板
//     */
//    @Value("${_chargerlink.alarm.alarm_sms_gwtout_module_id:1000000}")
//    private Long gwloginToutModuleId;
//    /**
//     * 微服务告警短信模板
//     */
//    @Value("${_chargerlink.alarm.alarm_sms_micro_service_module_id:1000000}")
//    private Long microServiceModuleId;
//
//    @Value("${_chargerlink.alarm.alarm_sms_trans_finish_module_id:1000000}")
//    private Long transFinishModuleId;

}
