package com.cdz360.iot.monitor.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.vo.EssAlarmVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.monitor.entity.po.EssAlarmPo;
import com.cdz360.iot.monitor.entity.request.ListEssAlarmParam;
import com.cdz360.iot.monitor.service.EssAlarmService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/ess/alarm")
public class EssAlarmRest {

    @Autowired
    private EssAlarmService essAlarmService;

    @Operation(summary = "获取储能设备告警参数")
    @PostMapping(value = "/getEssAlarmRecordList")
    public Mono<ListResponse<EssAlarmVo>> getEssAlarmRecordList(
        @RequestBody ListEssAlarmParam param) {
        log.info("获取储能设备告警参数: {}", JsonUtils.toJsonString(param));
        return essAlarmService.getEssAlarmRecordList(param);
    }


    @Operation(summary = "获取储能设备告警参数")
    @PostMapping(value = "/getEssAlarmList")
    public Mono<ListResponse<EssAlarmPo>> getEssAlarmList(
        @RequestBody ListEssAlarmParam param) {
        log.info("获取储能设备告警参数: {}", JsonUtils.toJsonString(param));
        return Mono.just(essAlarmService.getEssAlarmList(param));
    }

    @Operation(summary = "测试推送逻辑")
    @PostMapping(value = "/testNotify")
    public Mono<BaseResponse> testNotify(
        @RequestBody EssAlarmNotify data) {
        log.info("测试推送逻辑: {}", JsonUtils.toJsonString(data));
        essAlarmService.userEssAlarm(data);
        return Mono.just(RestUtils.success());
    }
}
