package com.cdz360.iot.monitor.entity.po;

import com.cdz360.iot.model.alarm.po.AlarmRecordPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * 告警记录表
 *
 * <AUTHOR>
 * Create on 2018.8.13 9:44
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Document(collection = "w_warning_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmRecord extends AlarmRecordPo implements Serializable {



}
