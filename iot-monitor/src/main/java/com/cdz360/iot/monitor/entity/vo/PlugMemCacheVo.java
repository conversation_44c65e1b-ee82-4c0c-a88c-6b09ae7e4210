package com.cdz360.iot.monitor.entity.vo;

import com.cdz360.base.model.base.type.PlugStatus;
import lombok.Data;

@Data
public class PlugMemCacheVo {

    private String plugNo;

    private PlugStatus status;

    private int errorCode;

    private int alertCode;

    private long seq;


    public PlugMemCacheVo(String plugNo,  PlugStatus status) {
        this.plugNo = plugNo;
        this.status = status;
        errorCode = 0;
        alertCode = 0;
        this.seq = 0L;
    }
}
