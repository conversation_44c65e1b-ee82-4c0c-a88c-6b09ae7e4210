package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 0-系统管理员 1-省级管理员 2-市级管理员 3-公有云商户
 *
 * <AUTHOR>
 * @Date Create on 2018/7/24 19:51
 */
@Getter
public enum ManagerTypeEnum {

    MANAGER_ADMIN(0, "系统管理员"),
    MANAGER_PROVINCE(1, "省级管理员"),
    MANAGER_CITY(2, "市级管理员"),
    MANAGER_COMMERCIAL(3, "公有云商户"),
    MANAGER_MERCHANT(4, "物业账号"),
    MANAGER_MONITOR(5, "物业总管理员"),
    MANAGER_EMERGENCY(6, "应急唯一账号");

    private int value;
    private String label;

    ManagerTypeEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

}
