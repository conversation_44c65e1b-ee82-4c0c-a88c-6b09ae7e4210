package com.cdz360.iot.monitor.entity.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PvMode implements DcEnum {

    UNKNOWN(0, "未知"),
    NORMAL(1, "正常"),
    FAULT(2, "异常"),
    WAIT(3, "待机");


    @JsonValue
    private final int code;
    private final String desc;

    PvMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PvMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PvMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PvMode) {
            return (PvMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PvMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PvMode.UNKNOWN;
    }
}
