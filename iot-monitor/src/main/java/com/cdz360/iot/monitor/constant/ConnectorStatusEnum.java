package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 充电插座实际状态
 *
 * <AUTHOR>
 * @date Create on 2017/11/21 15:07
 */
@Getter
public enum ConnectorStatusEnum {

    STATUS_IDLE(10, "空闲"),
    STATUS_CHARGING(100, "充电中"),
    STATUS_ONLINE_OCCUPY(12, "充电接口占用"),
    STATUS_ONLINE_OCCUPY_COMPLETE(31, "充电后占用"),
    STATUS_ONLINE_BREAK_DOWN(-100, "充电接口故障"),
    STATUS_UNACTIVITY_OR_OFFLINE(0, "离线"),
    ;


    private int value;
    private String label;

    ConnectorStatusEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }

}
