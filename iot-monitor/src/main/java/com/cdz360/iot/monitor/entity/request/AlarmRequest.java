package com.cdz360.iot.monitor.entity.request;
//
//import com.chargerlink.device.common.param.BaseRequest;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.Date;
//
///**
// * 告警列表查询
// *
// * <AUTHOR>
// * @date Create on 2018/7/31 14:31
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class AlarmRequest extends BaseRequest {
//    /**
//     * 查询开始时间
//     */
//    private Date startTime;
//    /**
//     * 查询结束时间
//     */
//    private Date endTime;
//    /**
//     * 告警类型 {@link com.chargerlink.device.monitor.constant.AlarmTypeEnum}
//     */
//    private String alarmTypeList;
//    /**
//     * 设备ID
//     */
//    private String deviceId;
//    /**
//     * 告警状态
//     */
//    private Integer alarmStatus;
//    /**
//     * 需要查询多个设备类型告警数据，用逗号隔开
//     */
//    private String deviceType;
//    /**
//     * 物业id
//     */
//    private Long merchantId;
//    /**
//     * 代理商id
//     */
//    private Long commId;
//    /**
//     * 邮箱地址
//     */
//    private String emailAddress;
//    /**
//     * 站点ID
//     */
//    private String siteId;
//
//}
