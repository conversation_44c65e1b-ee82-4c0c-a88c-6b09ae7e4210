package com.cdz360.iot.monitor.entity.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname BaseAlarmMsgRequest
 * @Description 告警请求基类（微服务DOWN掉 网关登录超时）
 * <AUTHOR>
 * @Date 2019/8/20 17:23
 * @Email <EMAIL>
 */
@Data
public class BaseAlarmMsgRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 一次发送多个的手机号
     */
    List<String> phones;

    /**
     * 发生的告警名
     */
    String warningName;

    /**
     * 告警码
     */
    String warningCode;
}

