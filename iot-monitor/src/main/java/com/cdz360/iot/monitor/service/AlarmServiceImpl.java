package com.cdz360.iot.monitor.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.EssVo.ErrorEquip;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.iot.feign.biz.BizUserFeignClient;
import com.cdz360.iot.model.alarm.param.GetAlarmListParam;
import com.cdz360.iot.model.alarm.type.AlarmStatusEnum;
import com.cdz360.iot.model.sys.vo.Dict;
import com.cdz360.iot.monitor.constant.AlarmEventTypeEnum;
import com.cdz360.iot.monitor.constant.AlarmTypeEnum;
import com.cdz360.iot.monitor.constant.DeviceStatusCodeType;
import com.cdz360.iot.monitor.dao.AlarmRecordDao;
import com.cdz360.iot.monitor.entity.convert.WarningRecordConvert;
import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.po.WWarningSummaryRecord;
import com.cdz360.iot.monitor.entity.request.MonitorAlarmRequest;
import com.cdz360.iot.monitor.entity.request.MonitorErrorDevRequest;
import com.cdz360.iot.monitor.entity.request.MonitorEssRequest;
import com.cdz360.iot.monitor.entity.request.MonitorEvseRequest;
import com.cdz360.iot.monitor.entity.request.MonitorPvGtiRequest;
import com.cdz360.iot.monitor.entity.request.MonitorSiteCtrlRequest;
import com.cdz360.iot.monitor.entity.request.SysAlarmMsgRequest;
import com.cdz360.iot.monitor.entity.request.TransFinshAlarmRequest;
import com.cdz360.iot.monitor.entity.request.UpdateWarningYwParam;
import com.cdz360.iot.monitor.entity.request.WarningBiParam;
import com.cdz360.iot.monitor.entity.request.WarningSummaryParam;
import com.cdz360.iot.monitor.entity.result.AlarmListVo;
import com.cdz360.iot.monitor.entity.vo.AlarmRecordMemVo;
import com.cdz360.iot.monitor.entity.vo.EquipAlarmsMemVo;
import com.cdz360.iot.monitor.entity.vo.PlugMemCacheVo;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 告警数据service层接口实现类
 *
 * <AUTHOR> Create on 2018.8.9 14:48
 */
@Slf4j
@Service
public class AlarmServiceImpl {

    private static final String ALARM_SYS_LEVE_PHONE_DICT_TYPE = "alarmSysLevePhone";
    private static final String ALARM_TRANS_FINSH_PHONE_DICT_TYPE = "alarmTranFinishPhone";
    @Autowired
    private AlarmRecordDao wWarningRecordRepository;

    @Autowired
    private AlarmRecordDs alarmRecordDs;
    @Autowired
    private BizUserFeignClient bizUserFeignClient;
    @Autowired
    private PlugMemCacheService plugMemCacheService;

    @Autowired
    private AlarmMemCacheService alarmMemCacheService;

    @Autowired
    private RedisIotReadService redisIotReadService;


    public List<AlarmRecord> getAlarmList(GetAlarmListParam paramIn) {
        return this.alarmRecordDs.getAlarmList(paramIn);
    }

    /**
     * 处理告警类型
     *
     * @param alarmTypeParam 告警类型参数，逗号隔开
     * @return 告警类型列表
     */
    private List<String> getAlarmTypeList(String alarmTypeParam) {
        List<String> alarmTypeList = new ArrayList<>();
        if (!StringUtils.isBlank(alarmTypeParam)) {
            String[] alarmTypeArray = alarmTypeParam.split(",");
            Collections.addAll(alarmTypeList, alarmTypeArray);
        } else {
            alarmTypeList = null;
        }
        return alarmTypeList;
    }

    /**
     * 抽取组织告警数据展示公共部分
     *
     * @param wWarningRecordList 展示的告警数据列表
     * @param total              总条目
     * @return 分页数据
     */
    private ListResponse<AlarmListVo> dealWithQueryAlarmData(
        List<AlarmRecord> wWarningRecordList, long total) {
        List<AlarmListVo> alarmListVOList = new ArrayList<>();
        if (null != wWarningRecordList && !wWarningRecordList.isEmpty()) {
            for (AlarmRecord wWarningRecord : wWarningRecordList) {
                AlarmListVo alarmListVo = WarningRecordConvert.convertWarningRecordToAlarmListVo(
                    wWarningRecord);
                //由于int类型json串形式返回前端为空的话是0，为了优化页面效果
                if (wWarningRecord.getConnectorId() != null && wWarningRecord.getWarningId() == 0) {
                    alarmListVo.setConnectorId("");
                } else if (wWarningRecord.getConnectorId() != null
                    && wWarningRecord.getConnectorId() != 0) {
                    alarmListVo.setConnectorId(wWarningRecord.getConnectorId().toString());
                }
                //持续时间更改,返回前端是秒
                Date startTime = wWarningRecord.getStartTime();
                Date endTime =
                    wWarningRecord.getEndTime() == null ? Calendar.getInstance().getTime()
                        : wWarningRecord.getEndTime();
                alarmListVo.setDuration((endTime.getTime() - startTime.getTime()) / 1000);
                //告警名称
                alarmListVo.setWarningName(wWarningRecord.getWarningName());
                alarmListVo.setWarningCode(wWarningRecord.getWarningCode());
                //告警处理说明
                alarmListVo.setWarningInstructions(wWarningRecord.getWarningInstructions());
                alarmListVOList.add(alarmListVo);
            }
        }
        ListResponse<AlarmListVo> result = new ListResponse<>(alarmListVOList, total);
        return result;
    }


    /**
     * 网络故障超过8小时推送支付宝生活号
     */

    public void alarmOffline8h() throws DcServiceException {

    }


    public void dualAlarmEvseMsg(MonitorEvseRequest request) {
        log.info(">> 处理桩端上报的告警消息。request = {}", request);

        request.setInfoType(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        // TODO 控制器第一期只会传【离线】和【正常】状态
        // 判断是不是故障中
        if (EvseStatus.OFFLINE.equals(request.getStatus())) {
            // 离线
            log.info("持续告警或故障");
            request.setAlertCode(DeviceStatusCodeType.C1101.getCode());
            request.setAlarmCode(DeviceStatusCodeType.C1101.getCode());
            request.setStartTime(Calendar.getInstance().getTime());
        } else {
            // 正常
            log.info("结束告警或故障。AlarmCode = {}", request.getAlarmCode());
            long count = alarmRecordDs.notEndWarningEvseRecordNum(request);
            if (count < 1L) {
                log.info("桩无未结束的告警，不做处理");
                return;
            }
            request.setAlarmCode(0);
            request.setEndTime(Calendar.getInstance().getTime());
        }

        alarmRecordDs.insertWWarningEvseRecordByDevice(request);
        log.info("<< 处理桩端上报的告警消息。request = {}", request);
    }

    public void dualAlarmPvGtiMsg(MonitorPvGtiRequest request) {
        //获取告警中的故障
        List<AlarmRecord> recordList = alarmRecordDs.getRecentPvGtiAlert(request);

        if (CollectionUtils.isEmpty(request.getErrorCodeList())) {
            log.info("告警码列表为空,gwno={},recId={}", request.getGwno(), request.getRecId());

            if (CollectionUtils.isNotEmpty(recordList)) {
                alarmRecordDs.updateWWarningEvseRecordByPv(recordList.stream()
                    .map(AlarmRecord::getWarningId).collect(Collectors.toList()));
            }
            return;
        }

        if (CollectionUtils.isEmpty(recordList)) {
            request.getErrorCodeList().forEach(e -> {
                request.setStartTime(Calendar.getInstance().getTime());
                request.setWarningCode(String.valueOf(e));
                request.setWarningName(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                request.setWarningInstruction(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                alarmRecordDs.insertWWarningEvseRecordByPv(request);
            });
        } else {
            List<Long> warningCodeList = recordList.stream()
                .map(e -> Long.valueOf(e.getWarningCode())).collect(Collectors.toList());
            //已经恢复的故障
            List<Long> warningIdList = recordList.stream()
                .filter(e -> !request.getErrorCodeList().contains(Long.valueOf(e.getWarningCode())))
                .map(AlarmRecord::getWarningId).collect(Collectors.toList());
            //新增故障码
            List<Long> codeList = request.getErrorCodeList().stream()
                .filter(e -> !warningCodeList.contains(e)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(warningIdList)) {
                alarmRecordDs.updateWWarningEvseRecordByPv(warningIdList);
            }
            if (CollectionUtils.isNotEmpty(codeList)) {
                codeList.forEach(e -> {
                    request.setStartTime(Calendar.getInstance().getTime());
                    request.setWarningCode(String.valueOf(e));
                    request.setWarningName(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                    request.setWarningInstruction(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                    alarmRecordDs.insertWWarningEvseRecordByPv(request);
                });
            }

        }
    }

    public void dualAlarmSrsMsg(MonitorPvGtiRequest request) {
        //获取告警中的故障
        List<AlarmRecord> recordList = alarmRecordDs.getRecentSrsAlert(request);

        if (CollectionUtils.isEmpty(request.getErrorCodeList())) {
            log.info("告警码列表为空,gwno={},recId={}", request.getGwno(), request.getRecId());

            if (CollectionUtils.isNotEmpty(recordList)) {
                alarmRecordDs.updateWWarningEvseRecordBySrs(recordList.stream()
                    .map(AlarmRecord::getWarningId).collect(Collectors.toList()));
            }
            return;
        }

        if (CollectionUtils.isEmpty(recordList)) {
            request.getErrorCodeList().forEach(e -> {
                request.setStartTime(Calendar.getInstance().getTime());
                request.setWarningCode(String.valueOf(e));
                request.setWarningName(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                request.setWarningInstruction(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                alarmRecordDs.insertWWarningEvseRecordBySrs(request);
            });
        } else {
            List<Long> warningCodeList = recordList.stream()
                .map(e -> Long.valueOf(e.getWarningCode())).collect(Collectors.toList());
            //已经恢复的故障
            List<Long> warningIdList = recordList.stream()
                .filter(e -> !request.getErrorCodeList().contains(Long.valueOf(e.getWarningCode())))
                .map(AlarmRecord::getWarningId).collect(Collectors.toList());
            //新增故障码
            List<Long> codeList = request.getErrorCodeList().stream()
                .filter(e -> !warningCodeList.contains(e)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(warningIdList)) {
                alarmRecordDs.updateWWarningEvseRecordBySrs(warningIdList);
            }
            if (CollectionUtils.isNotEmpty(codeList)) {
                codeList.forEach(e -> {
                    request.setStartTime(Calendar.getInstance().getTime());
                    request.setWarningCode(String.valueOf(e));
                    request.setWarningName(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                    request.setWarningInstruction(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                    alarmRecordDs.insertWWarningEvseRecordBySrs(request);
                });
            }

        }
    }

    public void dualAlarmSimMsg(MonitorPvGtiRequest request) {
        //获取告警中的故障
        List<AlarmRecord> recordList = alarmRecordDs.getRecentSimAlert(request);

        if (CollectionUtils.isEmpty(request.getErrorCodeList())) {
            log.info("告警码列表为空,gwno={},recId={}", request.getGwno(), request.getRecId());

            if (CollectionUtils.isNotEmpty(recordList)) {
                alarmRecordDs.updateWWarningEvseRecordBySim(recordList.stream()
                    .map(AlarmRecord::getWarningId).collect(Collectors.toList()));
            }
            return;
        }

        if (CollectionUtils.isEmpty(recordList)) {
            request.getErrorCodeList().forEach(e -> {
                request.setStartTime(Calendar.getInstance().getTime());
                request.setWarningCode(String.valueOf(e));
                request.setWarningName(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                request.setWarningInstruction(
                    DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                alarmRecordDs.insertWWarningEvseRecordBySim(request);
            });
        } else {
            List<Long> warningCodeList = recordList.stream()
                .map(e -> Long.valueOf(e.getWarningCode())).collect(Collectors.toList());
            //已经恢复的故障
            List<Long> warningIdList = recordList.stream()
                .filter(e -> !request.getErrorCodeList().contains(Long.valueOf(e.getWarningCode())))
                .map(AlarmRecord::getWarningId).collect(Collectors.toList());
            //新增故障码
            List<Long> codeList = request.getErrorCodeList().stream()
                .filter(e -> !warningCodeList.contains(e)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(warningIdList)) {
                alarmRecordDs.updateWWarningEvseRecordBySim(warningIdList);
            }
            if (CollectionUtils.isNotEmpty(codeList)) {
                codeList.forEach(e -> {
                    request.setStartTime(Calendar.getInstance().getTime());
                    request.setWarningCode(String.valueOf(e));
                    request.setWarningName(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                    request.setWarningInstruction(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getInstruction());
                    alarmRecordDs.insertWWarningEvseRecordBySim(request);
                });
            }

        }
    }

    /**
     * 储能ESS告警
     */
    public void dualAlarmEssMsg(MonitorEssRequest request) {
        log.info("储能ESS-处理告警开始。request = {}", request);
        if (CollectionUtils.isEmpty(request.getErrorList())) {
            log.info("告警设备信息为空,gwno={},dno={}", request.getGwno(), request.getDno());
            return;
        }
        request.getErrorList().forEach(e -> {
            if (null != e.getErrorCode()) {//存在errorCode,一个故障码对应多个设备
                e.getEquipList().forEach(i -> {
                    i.setErrorCodeList(List.of(e.getErrorCode()));
                    this.updateEssAlarmRecord(request, i);
                });
            } else { //不存在errorCode,设备对应多个故障码
                e.getEquipList().forEach(i -> {
                    this.updateEssAlarmRecord(request, i);
                });
            }
        });
    }


    /**
     * 储能PCS告警
     */
    public void processEssAlarms(EssVo<?> msgIn) {
        log.info("储能ESS-处理告警开始 equipType = {}, dno = {}",
            msgIn.getEssEquipType(), msgIn.getDno());
        if (msgIn.getErrorList() == null) {
            return;
        }
        List<EssVo.ErrorObj> errList = msgIn.getErrorList();
        for (EssVo.ErrorObj err : errList) {
            LocalDateTime lts = err.getLts();
            for (var equip : err.getEquipList()) {
                this.processEssAlarm(err.getEquipType(), equip, lts, err.getTz(), msgIn);
            }
        }

    }

    /**
     * 处理单个设备的告警数据
     */
    private void processEssAlarm(EssEquipType equipType, ErrorEquip equip, LocalDateTime lts, String tz, EssVo<?> ess) {
        log.info("处理单个设备告警开始 equipType = {}, dno = {}, alarmCodes = {}, lts = {}, tz = {}",
            equipType, equip.getDno(), equip.getErrorCodeList(), lts, tz);
//        List<Long> stopAlarmIds = new ArrayList<>();    // 待关闭的告警ID
        List<Long> toDeleteCodes = new ArrayList<>();  // 待移除的告警码
        List<Long> toAddCodes = new ArrayList<>();  // 等待新增的告警码
        EquipAlarmsMemVo equipAlarmRec = this.alarmMemCacheService.getAlarmEquip(
            equipType, equip.getDno());
        if (equipAlarmRec == null) {
            // 设备缓存信息没哟，说明之前没有告警
            if (CollectionUtils.isNotEmpty(equip.getErrorCodeList())) {
                toAddCodes = equip.getErrorCodeList();
            }
        } else if (CollectionUtils.isEmpty(equip.getErrorCodeList())) {
            // 设备正常，没有任何告警
            if (equipAlarmRec != null) {
                for (var rec : equipAlarmRec.getAlarms().values()) {
//                    stopAlarmIds.add(rec.getWarningId());
                    toDeleteCodes.add(rec.getCode());
                }
            }
        } else {
            Set<Long> newCodes = equip.getErrorCodeList().stream().sorted()
                .collect(Collectors.toSet());
            Set<Long> oldCodes = equipAlarmRec.getAlarms().values()
                .stream().map(AlarmRecordMemVo::getCode).sorted().collect(Collectors.toSet());

            for (Long newCode : newCodes) {
                if (!oldCodes.contains(newCode)) {
                    // 当新的告警码不存在时，需要新增告警记录
                    toAddCodes.add(newCode);
                }
            }
            for (Long oldCode : oldCodes) {
                if (!newCodes.contains(oldCode)) {
                    // 当旧的告警码不再存在时，需要删除告警记录
                    AlarmRecordMemVo memRec = equipAlarmRec.getAlarms().get(oldCode);
//                    stopAlarmIds.add(memRec.getWarningId());
                    toDeleteCodes.add(memRec.getCode());
                }
            }
        }
        if (!toDeleteCodes.isEmpty()) {
            log.info("告警结束. equipType = {}, dno = {}, alarmCodes = {}",
                equipType, equip.getDno(), toDeleteCodes);
            this.alarmRecordDs.stopEquipAlarms(equipType, equip.getDno(), toDeleteCodes, lts);
            this.alarmMemCacheService.removeAlarms(equipType, equip.getDno(), toDeleteCodes);
        }
        if (!toAddCodes.isEmpty()) {
            log.info("新增告警记录. equipType = {}, dno = {}, 告警码 = {}",
                equipType, equip.getDno(), toAddCodes);

           List<AlarmRecord> records = alarmRecordDs.addEquipAlarmRecord(equipType, equip, toAddCodes, ess, lts, tz);
           for(AlarmRecord rec : records){

               alarmMemCacheService.addRecord(equipType, equip.getDno(), rec);  // 增加到缓存
           }
        }
    }

    /**
     * 光储ESS告警处理
     *
     * @param request
     * @param errorDevRequest
     */
    public void updateEssAlarmRecord(MonitorEssRequest request,
        MonitorErrorDevRequest errorDevRequest) {
        //获取告警中的故障
        List<AlarmRecord> recordList = alarmRecordDs.getRecentEssAlert(request.getGwno(),
            request.getDno(), errorDevRequest.getEquipId());
        log.info("告警中的故障,size={}", recordList.size());
        if (CollectionUtils.isEmpty(recordList)) {
            errorDevRequest.getErrorCodeList().forEach(e -> {
                request.setStartTime(Calendar.getInstance().getTime())
                    .setWarningCode(String.valueOf(e))
                    .setEquipId(errorDevRequest.getEquipId())
                    .setEquipName(errorDevRequest.getEquipName())
                    .setClusterNo(errorDevRequest.getClusterNo())
                    .setPackNo(errorDevRequest.getPackNo());

                DeviceStatusCodeType codeType = DeviceStatusCodeType.getByCode(Math.toIntExact(e));
                if (DeviceStatusCodeType.C00.equals(codeType)) {
                    request.setWarningName(errorDevRequest.getEquipName().concat("告警"));
                } else {
                    request.setWarningName(
                        DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                }
                alarmRecordDs.insertWWarningEssRecordByEssEquip(request);
            });
        } else {
            List<Long> warningCodeList = recordList.stream()
                .map(e -> Long.valueOf(e.getWarningCode())).collect(Collectors.toList());
            //已经恢复的故障
            List<Long> warningIdList = recordList.stream()
                .filter(e -> !errorDevRequest.getErrorCodeList()
                    .contains(Long.valueOf(e.getWarningCode())))
                .map(AlarmRecord::getWarningId).collect(Collectors.toList());
            log.info("已恢复的故障,size={}", warningIdList.size());
            //新增故障码
            List<Long> codeList = errorDevRequest.getErrorCodeList().stream()
                .filter(e -> !warningCodeList.contains(e)).collect(Collectors.toList());
            log.info("新增的故障,size={}", codeList.size());
            if (CollectionUtils.isNotEmpty(warningIdList)) {
                alarmRecordDs.updateWWarningEssRecordByEssEquip(warningIdList);
            }
            if (CollectionUtils.isNotEmpty(codeList)) {
                codeList.forEach(e -> {
                    request.setStartTime(Calendar.getInstance().getTime())
                        .setWarningCode(String.valueOf(e))
                        .setEquipId(errorDevRequest.getEquipId())
                        .setEquipName(errorDevRequest.getEquipName())
                        .setClusterNo(errorDevRequest.getClusterNo())
                        .setPackNo(errorDevRequest.getPackNo());
                    DeviceStatusCodeType codeType = DeviceStatusCodeType.getByCode(
                        Math.toIntExact(e));
                    if (DeviceStatusCodeType.C00.equals(codeType)) {
                        request.setWarningName(errorDevRequest.getEquipName().concat("告警"));
                    } else {
                        request.setWarningName(
                            DeviceStatusCodeType.getByCode(Math.toIntExact(e)).getDesc());
                    }
                    alarmRecordDs.insertWWarningEssRecordByEssEquip(request);
                });
            }
        }
    }

    private boolean isSameStatus(PlugMemCacheVo oldPlug, Integer newErrorCode,
        Integer newAlertCode) {
        return NumberUtils.equals(oldPlug.getErrorCode(), newErrorCode)
            && NumberUtils.equals(oldPlug.getAlertCode(), newAlertCode);
    }

    /**
     * 桩端状态上报，告警处理
     *
     * @param request
     */

    public void dualAlarmChargeMsg(MonitorAlarmRequest request, PlugVo plug) {
        log.info(">> 处理枪端上报的告警消息。request = {}", request);
        // 故障码
        Integer errorCode = Optional.ofNullable(request.getErrorCode()).orElse(0);
        // 桩端确认告警码
        Integer alertCode = Optional.ofNullable(request.getAlertCode()).orElse(0);
        if (plug.getStatus() == PlugStatus.OFFLINE && errorCode == 0) {
            // 填入枪头离线异常码
            errorCode = Integer.valueOf(AlarmTypeEnum.ALARM_CONNECT_LOST.getValue());
        }
        PlugMemCacheVo memPlug = plugMemCacheService.getPlug(plug.getPlugNo());
        if (memPlug != null && isSameStatus(memPlug, errorCode, alertCode)) {
            log.info("枪头异常状态无变化,忽略. memPlug = {}", memPlug);
            return;
        }
        if (memPlug == null) {
            memPlug = new PlugMemCacheVo(plug.getPlugNo(), plug.getStatus());
        }
        memPlug.setErrorCode(errorCode);
        memPlug.setAlertCode(alertCode);
        plugMemCacheService.updatePlug(memPlug);

        // 桩端上报故障 告警：AlertCode暂时为空
        if (alertCode != 0) {
            // 告警
            if (StringUtils.isNotBlank(request.getLinkId())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
            } else {
                request.setInfoType(AlarmEventTypeEnum.ALARM_WARNING.getValue());
            }
            request.setAlarmCode(alertCode);
        } else {
            // 故障
            if (StringUtils.isNotBlank(request.getLinkId())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
            } else {
                request.setInfoType(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
            }
            request.setAlarmCode(errorCode);
        }
        // 离线告警码设置 AlarmCode 离线告警码:1101 直流正常: 0 交流正常:1000
        if (AlarmTypeEnum.ALARM_NORMAL_DC.getValue().equals(String.valueOf(request.getAlarmCode()))
            ||
            AlarmTypeEnum.ALARM_NORMAL_AC.getValue()
                .equals(String.valueOf(request.getAlarmCode()))) {
            if (request.getPlugStatus() == PlugStatus.OFFLINE) {
                request.setAlarmCode(Integer.valueOf(AlarmTypeEnum.ALARM_CONNECT_LOST.getValue()));
            }
        }
        // 判断是不是故障中
        if (AlarmTypeEnum.ALARM_NORMAL_DC.getValue().equals(request.getAlarmCode().toString()) ||
            AlarmTypeEnum.ALARM_NORMAL_AC.getValue().equals(request.getAlarmCode().toString())) {
            log.info("结束告警或故障。AlarmCode = {}", request.getAlarmCode().toString());
            long notEndWarningRecordCount = alarmRecordDs.notEndWarningRecordNum(request);
            if (notEndWarningRecordCount < 1L) {
                log.info("枪头无未结束的告警，不做处理");
                return;
            }
            // 告警或故障结束
            request.setEndTime(Calendar.getInstance().getTime());
        } else {
            log.info("持续告警或故障");
            // 告警或故障持续
            request.setStartTime(Calendar.getInstance().getTime());
        }

        alarmRecordDs.insertWWarningRecordByDevice(request);
        log.info("<< 处理枪端上报的告警消息。request = {}", request);
    }

    /**
     * 定时任务-自动结束离线告警记录
     * <p>检查未完成的离线告警记录，如果设备已恢复在线则结束告警</p>
     *
     * @param days
     * @return
     */
    public BaseResponse finishOfflineWarning(int days) {
        List<Long> needFinishWarningIds = new ArrayList<>();
        final int size = 50;
        int start = 0;
        List<AlarmRecord> offlineRecords = new ArrayList<>();
        do {
            // 循环分页读取 获取最近n天的离线告警记录
            offlineRecords = wWarningRecordRepository.getUnfinishedOfflineRecords(days, start,
                size);
            if (CollectionUtils.isEmpty(offlineRecords)) {
                break; // 没有更多数据，正常结束循环
            }
            offlineRecords.forEach(record -> {
                try {
                    if (StringUtils.isBlank(record.getBoxOutFactoryCode())) {
                        return;
                    }
                    // 获取redis中的桩信息
                    EvseVo evseCache = redisIotReadService.getEvseRedisCache(
                        record.getBoxOutFactoryCode());
                    if (evseCache != null && !EvseStatus.OFFLINE.equals(evseCache.getStatus())
                        && !EvseStatus.UNKNOWN.equals(evseCache.getStatus())) {
                        // 如果桩非离线，则结束离线告警
                        needFinishWarningIds.add(record.getWarningId());
                    }
                } catch (Exception e) {
                    log.error("处理离线告警记录异常, boxOutFactoryCode: {}, error: {}",
                        record.getBoxOutFactoryCode(), e.getMessage(), e);
                }
            });
            start += size;
        } while (CollectionUtils.isNotEmpty(offlineRecords));

        if (CollectionUtils.isNotEmpty(needFinishWarningIds)) {
            log.info("开始结束离线告警记录，数量：{}", needFinishWarningIds.size());
            AlarmRecord wWarningRecord = new AlarmRecord();
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_END_AUTO.getValue());
            wWarningRecord.setEndTime(new Date());
            wWarningRecordRepository.updateBatchStatusById(needFinishWarningIds, wWarningRecord);
            log.info("离线告警记录处理完成");
        } else {
            log.info("没有需要结束的离线告警记录");
        }
        return RestUtils.success();
    }

    public void dualAlarmSiteCtrlMsg(MonitorSiteCtrlRequest request) {
        log.info(">> 处理控制器的告警或异常消息。request = {}", request);

        // 故障码
        Integer errorCode = Optional.ofNullable(request.getErrorCode()).orElse(0);
        // 桩端确认告警码
        Integer alertCode = Optional.ofNullable(request.getAlertCode()).orElse(0);

        // 桩端上报故障 告警：AlertCode暂时为空
        if (alertCode != 0) {
            // 告警
            request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
            request.setAlarmCode(alertCode);
        } else if (errorCode != 0) {
            // 故障
            request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
            request.setAlarmCode(errorCode);
        }

        switch (request.getStatus()) {

            case STARTUP:
            case WORK:
            case UPGRADE:
            case DEBUG:
            case ONLINE:
                log.info("结束告警或故障。AlarmCode = {}", request.getAlarmCode());
                long notEndWarningRecordCount = alarmRecordDs.notEndWarningSiteCtrlRecordNum(
                    request);
                if (notEndWarningRecordCount < 1L) {
                    log.info("场站控制器无未结束的告警，不做处理");
                    return;
                } else {
                    request.setEndTime(Calendar.getInstance().getTime());
                }
                break;
            case ALERT:
            case ERROR:
            case OFFLINE:
                log.info("持续告警或故障");
                // 告警或故障持续
                request.setStartTime(Calendar.getInstance().getTime());
                break;
        }

        alarmRecordDs.insertWWarningSiteCtrlRecordByDevice(request);
        log.info("<< 处理桩端上报的告警消息。request = {}", request);
    }


    public void saveSysLeveAlarmMsg(SysAlarmMsgRequest request) throws DcServiceException {
        ListResponse<Dict> dictPhonesRes = bizUserFeignClient.findDictDataByTypeV2(
            ALARM_SYS_LEVE_PHONE_DICT_TYPE)
            .block(Duration.ofSeconds(30L));
        if (dictPhonesRes == null || dictPhonesRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS
            || dictPhonesRes.getData() == null) {
            throw new DcServiceException("【系统级别告警】获取推送的手机号列表失败");
        }
        List<String> phones = dictPhonesRes.getData().stream()
            .filter(dict -> StringUtils.isNotBlank(dict.getValue()))
            .map(dict -> dict.getValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phones)) {
            log.info("【系统级别告警】未配置告警人，告警结束");
            return;
        }
        request.setPhones(phones);
        alarmRecordDs.sendSysLeveAlarmToMassage(request);
    }


    public void sendTransFinshAlarm(TransFinshAlarmRequest request) throws DcServiceException {
        ListResponse<Dict> dictPhonesRes = bizUserFeignClient.findDictDataByTypeV2(
            ALARM_TRANS_FINSH_PHONE_DICT_TYPE)
            .block(Duration.ofSeconds(30L));
        if (dictPhonesRes == null || dictPhonesRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS
            || dictPhonesRes.getData() == null) {
            throw new DcServiceException("【资金周转告警】获取推送的手机号列表失败");
        }
        List<String> phones = dictPhonesRes.getData().stream()
            .filter(dict -> com.cdz360.base.utils.StringUtils.isNotBlank(dict.getValue()))
            .map(dict -> dict.getValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phones)) {
            log.info("【资金周转告警】未配置告警人，告警结束");
            return;
        }
        request.setPhones(phones);
        alarmRecordDs.sendTransFinshAlarmToMassage(request);
    }

    public List<AlarmRecord> evseErrorAlarms(Date startTime,
        Date endTime,
        Integer plugId,
        String evseNo,
        String siteId) {
        return alarmRecordDs.evseErrorAlarms(startTime, endTime, plugId, evseNo, siteId);
    }

    public ListResponse<AlarmRecord> getWarningBiList(WarningBiParam param) {
        return wWarningRecordRepository.getWarningBiList(param);
    }

    public ListResponse<AlarmRecord> getEvseWarningList(WarningBiParam param) {
        return wWarningRecordRepository.getEvseWarningList(param);
    }

    public ObjectResponse<Map<String, List<AlarmRecord>>> getRecentWarning(
        WarningBiParam param) {
        return wWarningRecordRepository.getRecentWarning(param);
    }

    public ListResponse<WWarningSummaryRecord> getWarningSummaryList(WarningSummaryParam param) {
        return wWarningRecordRepository.getWarningSumaryList(param);
    }

    public BaseResponse updateYwOrderByEvseList(UpdateWarningYwParam param) {
        return wWarningRecordRepository.updateYwOrderByEvseList(param);
    }

    public BaseResponse checkWarningBiListCount(WarningBiParam param) {
        return wWarningRecordRepository.checkWarningBiListCount(param);
    }

}