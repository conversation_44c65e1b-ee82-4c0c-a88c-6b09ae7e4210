package com.cdz360.iot.monitor.entity.po;

import com.cdz360.base.model.iot.type.EssEquipType;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AlarmCodeCfgPo {

    private Long id;

    private EssEquipType equipType;

    private Long code;

    private Integer level;

    private String name;

    private String desc;

    private String proposal;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
