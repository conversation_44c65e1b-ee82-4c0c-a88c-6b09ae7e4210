package com.cdz360.iot.monitor.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * @Classname DeviceStatusCodeType
 * @Description
 * @Date 5/12/2020 1:40 PM
 * @Created by Rafael
 */
public enum DeviceStatusCodeType {
    C00(0,"正常停充",""),
    C01(1,"充电机绝缘故障",""),
    C02(2,"BMS绝缘",""),
    C03(3,"充电机泄放电路故障",""),
    C04(4,"漏电故障",""),
    C05(5,"急停故障",""),
    C06(6,"",""),
    C07(7,"",""),
    C08(8,"",""),
    C09(9,"",""),
    C10(10,"连接器故障(导引电路检测到故障)-枪过温",""),
    C11(11,"连接器故障(导引电路检测到故障)-枪锁反馈",""),
    C12(12,"连接器故障(导引电路检测到故障)-CC1信号",""),
    C13(13,"充电机其他故障",""),
    C14(14,"电子锁锁定错误",""),
    C15(15,"K1K2外侧电压检测失败1",""),
    C16(16,"K1K2外侧电压检测失败2",""),
    C17(17,"预充电电压调整失败",""),
    C18(18,"输出接触器故障检测",""),
    C19(19,"电池反接",""),
    C20(20,"充电机过温-直流模块过温/或者通讯故障",""),
    C21(21,"充电机内部过温-箱体温度",""),
    C22(22,"充电机电量不能传送",""),
    C23(23,"充电机检测到充电电流不匹配",""),
    C24(24,"充电机检测到充电电压异常",""),
    C25(25,"输出短路",""),
    C26(26,"主从机通讯故障",""),
    C27(27,"桩体风扇故障",""),
    C28(28,"",""),
    C29(29,"",""),
    C30(30,"输出连接器过温",""),
    C31(31,"输出连接器过温-BMS元件",""),
    C32(32,"连接器故障(导引电路检测到故障，充电连接器)-BMS",""),
    C33(33,"电池组温度过高",""),
    C34(34,"车辆连接器粘连(高压继电器)",""),
    C35(35,"CC2电压检测故障",""),
    C36(36,"BST报告其他故障",""),
    C37(37,"BMS检测到充电电流过大",""),
    C38(38,"BMS检测到充电电压异常",""),
    C39(39,"电池单体电压过高",""),
    C40(40,"电池单体电压过低",""),
    C41(41,"SOC过高",""),
    C42(42,"SOC过低",""),
    C43(43,"蓄电池输出连接器状态",""),
    C44(44,"BMS电池过温(BMS)",""),
    C45(45,"BMS过流",""),
    C46(46,"BMS绝缘",""),
    C47(47,"BMS电池过温",""),
    C48(48,"",""),
    C49(49,"",""),
    C50(50,"接收超时BRM",""),
    C51(51, "接收超时BCP",""),
    C52(52, "接收超时BRO",""),
    C53(53, "接收超时BCS",""),
    C54(54, "接收超时BCL",""),
    C55(55, "接收超时BST",""),
    C56(56, "接收超时BSD",""),
    C57(57, "接收超时BHM/BRM",""),
    C58(58,"",""),
    C59(59,"",""),
    C60(60, "接收超时CRM_00",""),
    C61(61, "接收超时CRM_AA",""),
    C62(62, "接收超时CTS，CML",""),
    C63(63, "接收超时CRO",""),
    C64(64, "接收超时CCS",""),
    C65(65, "接收超时CST",""),
    C66(66, "接收超时CSD",""),
    C67(67,"其他",""),
    C68(68,"",""),
    C69(69,"",""),
    C70(70,"烟雾报警",""),
    C71(71,"水浸检测",""),
    C72(72,"倾斜检测",""),
    C73(73,"开门检测",""),
    C74(74,"后台通讯",""),
    C75(75,"屏通讯",""),
    C76(76,"读卡器",""),
    C77(77,"输入接触器检测",""),
    C78(78,"功率分配接触器故障",""),
    C79(79,"(模块)输入过欠压",""),
    C80(80,"多抢，模块输入过欠压，不均流",""),
    C81(81,"UI-安全管理板通讯中断",""),
    C82(82,"安全管理板-UI通讯中断",""),
    C83(83,"安全管理板-接触板通讯中断",""),
    C84(84,"防雷器故障",""),
    C85(85,"UI板或安全管理板5V跌落",""),
    C86(86, "充电中桩掉电",""),
    C87(87,"UI-充电控制板通讯中断",""),
    C88(88,"充电控制板-UI通讯中断",""),
    C89(89,"安全管理板-充电控制板通讯中断",""),
    C90(90,"充电控制板-安全管理板通讯中断",""),
    C91(91,"直流功率表通讯故障",""),
    C92(92,"绝缘检测表通讯故障",""),
    C93(93,"电池充电安全检测",""),
    C94(94,"泄放电阻温度",""),
    C95(95,"泄放电路驱动故障",""),
    C96(96,"与BMS通讯中断(3次超时)",""),
    C97(97,"充电前功率组未就绪",""),
    C98(98,"BMS辅组电源电压故障",""),
    C99(99, "VIN码未找到",""),

    C100(100, "内存不足故障",""),
    C101(101, "交流进线断电", ""),
    C102(102, "电表电量异常", ""),
    C103(103, "SD卡格式错误",""),
    C104(104, "FTP模式配置失败",""),
    C105(105, "RFID通信超时",""),
    C106(106, "压力传感器通信超时",""),
    C107(107, "摄像头通信超时",""),
    C108(108, "绝缘模块检测电压失败",""),
    C109(109, "电表电量数据异常",""),
    C110(110, "急停处理泄放超时",""),
    C111(111, "急停处理解锁超时",""),
    C112(112, "正常停止处理泄放超时",""),
    C113(113, "正常停止处理解锁超时",""),
    C114(114, "绝缘检测电压",""),
    C115(115, "绝缘检测数据",""),
    C116(116, "绝缘检测报警",""),
    C117(117, "防盗检测",""),
    C119(119, "单枪，模块输出过欠压，不均流",""),

    C130(130, "PC01板内存不足",""),
    C131(131, "PDU通信超时故障",""),
    C132(132, "PDU控制命令错误",""),
    C133(133, "调整功率组输出电压超时",""),
    C134(134, "CTT通道执行断开操作超时",""),
    C135(135, "CTT通道执行闭合操作超时",""),
    C136(136, "CTT通道粘连",""),
    C137(137, "CTT通道驱动失效",""),
    C138(138, "CTT通道其他故障",""),
    C139(139, "PDU故障",""),
    C140(140, "UI板写订单记录故障",""),
    C141(141, "主动充电安全故障",""),
    C142(142, "熔断器故障",""),
    C143(143, "压力过小故障",""),
    C144(144, "压力过大故障",""),
    C145(145, "CP 采样板通讯故障",""),
    C146(146, "电动推杆通讯故障",""),
    C147(147, "电动推杆运动控制故障", ""),

    C148(148, "BRM 报文数据异常",""),
    C149(149, "BCP 报文数据异常",""),
    C150(150, "接收超时 BRO_00",""),
    C151(151, "接收超时 BRO_AA",""),
    C157(157, "充电中有电压无电流",""),
    C158(158, "车辆最高允许充电电压低于充电机最低输出电压",""),
    C159(159, "电池电压过低",""),
    C160(160, "电池电压过高",""),
    C161(161, "K1K2后端电压大于车辆最高允许电压",""),
    C162(162, "电表电量异常",""),

    C1001(1001, "网关离线",""),
    C1101(1101, "桩离线",""),
    C1111(1111, "下发校时指令桩端返回失败",""),
    C1112(1112, "电价下发桩端返回失败",""),
    C1501(1501, "桩端返回启动充电失败",""),
    C1502(1502, "桩端返回停止充电失败",""),
    C1503(1503, "桩端返回预约充电失败",""),
    C1504(1504, "充电控制板-UI通讯中断",""),
    C1505(1505, "安全管理板-充电控制板通讯中断",""),
    C1506(1506, "桩端刷卡(离线卡)失败",""),
    C1901(1901, "下行指令发送超时",""),
    C1002(1002, "新网关MAC地址重复",""),
    C1003(1003, "新网关MAC地址为空",""),
    C1120(1120, "桩端（心跳）返回故障码",""),
    C1130(1130, "桩端（心跳）返回告警码",""),

    C1005(1005, "交流-急停故障",""),
    C1010(1010, "交流-枪高温",""),
    C1012(1012, "交流-枪连接异常",""),
    C1014(1014, "交流-枪锁异常",""),
    C1018(1018, "交流-交流接触器",""),
    C1021(1021, "交流-枪体高温",""),
    C1023(1023, "交流-枪过流",""),
    C1029(1029, "交流-充电电流为0",""),
    C1058(1058, "交流-枪低温",""),
    C1059(1059, "交流-桩温度传感器故障",""),
    C1068(1068, "交流-枪温度传感器故障",""),
    C1069(1069, "交流-S2开关异常",""),
    C1073(1073, "交流-门禁",""),
    C1074(1074, "交流-网络通讯",""),
    C1075(1075, "交流-DWIN通讯故障",""),
    C1076(1076, "交流-读卡器",""),
    C1079(1079, "交流-交流欠压",""),
    C1080(1080, "交流-S2开关异常",""),
    C1083(1083, "交流-电表通讯",""),
    C1084(1084, "交流-CP电压采样异常",""),
    C1086(1086, "交流-充电中桩掉电",""),
    C1096(1096, "交流-CP通讯故障", ""),
    C1099(1099, "交流-桩端异常", ""),


    /*=控制器告警 2001~2999====================*/
    /*=控制器告警==============================*/
    C2001(2001, "场站控制器-告警-负载告警",""),
    C2002(2002, "场站控制器-告警-配电柜温度告警",""),
    /*=控制器故障==============================*/
    C2501(2501, "场站控制器-故障-控制器配置信息异常",""),
    C2502(2502, "场站控制器-故障-负载异常",""),
    C2503(2503, "场站控制器-故障-配电柜温度异常",""),

    C2999(2999, "场站控制器-待定",""),



    // 固德威逆变器故障码
//    C10000(10000, "GFCI Device Check Failure",""),
//    C10001(10001, "AC HCT Check Failure",""),
//    C10002(10002, "TBD",""), // 预留
//    C10003(10003, "DCI Consistency Failure",""),
//    C10004(10004, "GFCI Consistency Failure",""),
//    C10005(10005, "TBD",""), // 预留
//    C10006(10006, "GFCI Device Failure",""),
//    C10007(10007, "Relay Device Failure",""),
//    C10008(10008, "AC HCT Failure",""),
//    C10009(10009, "Utility Loss",""),
//    C10010(10010, "Gournd I Failure",""),
//    C10011(10011, "DC Bus High",""),
//    C10012(10012, "Internal Fan Failure(Back-Up Over Load for ES)",""),
//    C10013(10013, "Over Temperature",""),
//    C10014(10014, "Auto Test Failure",""),
//    C10015(10015, "PV Over Voltage",""),
//    C10016(10016, "External Fan Failure",""),
//    C10017(10017, "Vac Failure",""),
//    C10018(10018, "Isolation Failure",""),
//    C10019(10019, "DC Injection High",""),
//    C10020(10020, "TBD",""), // 预留
//    C10021(10021, "TBD",""), // 预留
//    C10022(10022, "Fac Consistency Failure",""),
//    C10023(10023, "Vac Consistency Failure",""),
//    C10024(10024, "TBD",""), // 预留
//    C10025(10025, "Relay Check Failure",""),
//    C10026(10026, "TBD",""), // 预留
//    C10027(10027, "TBD",""), // 预留
//    C10028(10028, "TBD",""), // 预留
//    C10029(10029, "Fac Failure",""),
//    C10030(10030, "EEPROM R/W Failure",""),
//    C10031(10031, "Internal Communication Failure",""),
    /**固德威逆变器故障码开始**/
    C10000(10000, "漏电流检测设备检测失败","漏电流检测电路异常"),
    C10001(10001, "输出电流传感器检测失败","输出电流传感器异常"),
    C10002(10002, "TBD",""), // 预留
    C10003(10003, "DC注入不一致","主备输出直流电流不一致"),
    C10004(10004, "漏电流检测不一致","主备漏电流检测值不一致"),
    C10005(10005, "TBD",""), // 预留
    C10006(10006, "漏电流检测设备故障","漏电流检测设备检测失败3次"),
    C10007(10007, "继电器故障","继电器检测失败3次"),
    C10008(10008, "输出电流传感器故障","输出电流传感器检测失败3次"),
    C10009(10009, "交流侧无电压","交流侧无电压"),
    C10010(10010, "接地失效","地线电流过高"),
    C10011(10011, "直流总线电压过高","直流总线电压过高"),
    C10012(10012, "内部风扇故障","内部风扇故障"),
    C10013(10013, "过温告警","温度过高"),
    C10014(10014, "自动检测失败","自动检测失败"),
    C10015(10015, "光伏过压","光伏输入电压超过额定最大值"),
    C10016(10016, "外部风扇故障","外部风扇故障"),
    C10017(10017, "VAC失效","网格电压越限"),
    C10018(10018, "对地绝缘阻抗失效","光伏对地绝缘阻抗过低"),
    C10019(10019, "DC注入过高","注入到网格的DC过高"),
    C10020(10020, "TBD",""), // 预留
    C10021(10021, "TBD",""), // 预留
    C10022(10022, "FAC不一致","主备网格频率值不一致"),
    C10023(10023, "VAC不一致","主备网格电压值不一致"),
    C10024(10024, "TBD",""), // 预留
    C10025(10025, "继电器检测失败","继电器检测失败"),
    C10026(10026, "TBD",""), // 预留
    C10027(10027, "TBD",""), // 预留
    C10028(10028, "TBD",""), // 预留
    C10029(10029, "FAC失效","网格频率越限"),
    C10030(10030, "EEPROM读写失败","无法读取或者写入EEPROM"),
    C10031(10031, "内部通讯失败","微处理器之间通讯失败"),
    /*固德威逆变器故障码结束*/

    /*欣顿逆变器故障码开始*/
  //C10100(10100, "无", ""),
    C10101(10101, "电池过压", ""),
    C10102(10102, "电池过压告警", ""),
    C10103(10103, "电池低压", ""),
    // TODO:欣顿逆变器故障码目前没用到

    /*华为逆变器故障码开始*/
    C10200(10200, "输入组串电压高", ""),
    C10201(10201, "直流电弧故障[1]", ""),
    C10202(10202, "组串反接", ""),
    C10203(10203, "组串电流反灌", ""),
    C10204(10204, "组串功率异常", ""),
    C10205(10205, "AFCI 自检失败", ""),
    C10206(10206, "电网相线对PE短路", ""),
    C10207(10207, "电网掉电", ""),
    C10208(10208, "电网欠压", ""),
    C10209(10209, "电网过压", ""),
    C10210(10210, "电网电压不平衡", ""),
    C10211(10211, "电网过频", ""),
    C10212(10212, "电网欠频", ""),
    C10213(10213, "电网频率不稳定", ""),
    C10214(10214, "输出过流", ""),
    C10215(10215, "输出电流直流分量过大", ""),
    C10216(10216, "残余电流异常", ""),
    C10217(10217, "系统接地异常", ""),
    C10218(10218, "绝缘阻抗低", ""),
    C10219(10219, "温度过高", ""),
    C10220(10220, "设备异常", ""),
    C10221(10221, "升级失败或版本不匹配", ""),
    C10222(10222, "License 到期", ""),
    C10223(10223, "监控单元故障", ""),
    C10224(10224, "功率采集器故障[2]", ""),
    C10225(10225, "储能设备异常", ""),
    C10226(10226, "主动孤岛", ""),
    C10227(10227, "被动孤岛", ""),
    C10228(10228, "瞬时交流过压", ""),
    C10229(10229, "外部设备端口短路", ""),
    C10230(10230, "离网输出过载", ""),
    C10231(10231, "电池板配置异常", ""),
    C10232(10232, "优化器故障", ""),
    C10233(10233, "内置PID工作异常", ""),
    C10234(10234, "输入组串对地电压高", ""),
    C10235(10235, "外部风扇异常", ""),
    C10236(10236, "储能反接", ""),
    C10237(10237, "并离网控制器异常", ""),
    C10238(10238, "组串丢失", ""),
    C10239(10239, "内部风扇异常", ""),
    C10240(10240, "直流保护单元异常", ""),
    /*华为逆变器故障码结束*/


    /**辐射仪告警开始**/
    C20000(20000, "辐射仪离线",""),
    /**辐射仪告警结束**/


    /**SIM卡告警开始**/
    C30000(30000, "SIM卡状态异常","SIM卡状态由已激活变更为异常"),
    /**SIM卡告警结束**/


    /**
     * 储能设备故障码 START
     * 规则：(300 + 设备类型ID前两位) * 100000 + 设备对应告警表序号(三位) * 100 + bit位
     */

    // BMS (tab20-1)
    C31020102(31020102, "BMS:单体温差","EMS上报"),
    C31020104(31020104, "BMS:充电过流","EMS上报"),
    C31020106(31020106, "BMS:放电过流","EMS上报"),
    C31020107(31020107, "BMS:极柱过温","EMS上报"),
    C31020108(31020108, "BMS:单体过压","EMS上报"),
    C31020109(31020109, "BMS:单体压差","EMS上报"),
    C31020110(31020110, "BMS:放电低温","EMS上报"),
    C31020111(31020111, "BMS:低压关机","EMS上报"),
    C31020112(31020112, "BMS:单体欠压","EMS上报"),
    C31020113(31020113, "BMS:ISO通讯故障 ","EMS上报"),
    C31020114(31020114, "BMS:LMU SN 重复","EMS上报"),
    C31020115(31020115, "BMS:BMU SN 重复","EMS上报"),
    C31020116(31020116, "BMS:绝缘故障","EMS上报"),
    C31020117(31020117, "BMS:LMU通讯故障","EMS上报"),
    C31020118(31020118, "BMS:单体过温","EMS上报"),
    C31020119(31020119, "BMS:BMU通讯故障","EMS上报"),
    C31020120(31020120, "BMS:INV通讯故障","EMS上报"),
    C31020121(31020121, "BMS:充电低温","EMS上报"),
    C31020122(31020122, "BMS:TOPBMU 通讯故障","EMS上报"),
    C31020123(31020123, "BMS:总压异常","EMS上报"),
    C31020124(31020124, "BMS:线束故障","EMS上报"),
    C31020125(31020125, "BMS:簇切除故障","EMS上报"),
    C31020126(31020126, "BMS:继电器故障","EMS上报"),
    C31020131(31020131, "BMS:温度传感器故障","EMS上报"),

    // EMS (tab10-1)
    C32010100(32010100, "EMS:PCS通讯丢失","EMS上报"),
    C32010101(32010101, "EMS:电网关口电表丢失","EMS上报"),
    C32010102(32010102, "EMS:BMS通讯丢失","EMS上报"),
    C32010104(32010104, "EMS:光伏关口电表通讯丢失","EMS上报"),
    C32010107(32010107, "EMS:SD卡丢失","EMS上报"),
    C32010117(32010117, "EMS:光伏逆变器通讯丢失","EMS上报"),
    C32010118(32010118, "EMS:油机和光伏冲突","EMS上报"),
    C32010119(32010119, "EMS:光伏逆变器故障","EMS上报"),
    C32010120(32010120, "EMS:空调故障","EMS上报"),
    C32010121(32010121, "EMS:消防故障","EMS上报"),
    C32010122(32010122, "EMS:消防控制器故障","EMS上报"),
    C32010123(32010123, "EMS:油机故障","EMS上报"),
    C32010124(32010124, "EMS:空调通讯丢失","EMS上报"),
    C32010125(32010125, "EMS:过流故障","EMS上报"),
    C32010126(32010126, "EMS:PCS工作模式错误","EMS上报"),
    C32010127(32010127, "EMS:电池能量低故障","EMS上报"),
    C32010128(32010128, "EMS:充电桩电表通讯丢失","EMS上报"),
    C32010129(32010129, "EMS:储能并网点电表通讯丢失","EMS上报"),





    // PCS中DCAC故障信息(tab40-1)
    C32140100(32140100, "AC模块组：交流母线过压","EMS上报"),
    C32140101(32140101, "AC模块组：交流母线过频","EMS上报"),
    C32140102(32140102, "AC模块组：交流母线欠压","EMS上报"),
    C32140103(32140103, "AC模块组：逆变器孤岛保护","EMS上报"),
    C32140104(32140104, "AC模块组：直流输入过压","EMS上报"),
    C32140105(32140105, "AC模块组：并离网切换错误","EMS上报"),
    C32140106(32140106, "AC模块组：交流反序","EMS上报"),
    C32140107(32140107, "AC模块组：直流输入欠压","EMS上报"),
    C32140108(32140108, "AC模块组：过载报警","EMS上报"),
    C32140109(32140109, "AC模块组：交流母线电压异常","EMS上报"),
    C32140110(32140110, "AC模块组：交流缺相","EMS上报"),
    C32140111(32140111, "AC模块组：交流母线电压不平衡","EMS上报"),
    C32140112(32140112, "AC模块组：交流母线欠频","EMS上报"),
    C32140113(32140113, "AC模块组：电池电量不足","EMS上报"),
    C32140114(32140114, "AC模块组：直流输入过流","EMS上报"),
    C32140115(32140115, "AC模块组：离网电压反序","EMS上报"),
    C32140116(32140116, "AC模块组：锁相失败","EMS上报"),
    C32140117(32140117, "AC模块组：环境温度过温","EMS上报"),
    C32140118(32140118, "AC模块组：环温探头故障","EMS上报"),
    C32140119(32140119, "AC模块组：柜温探头故障","EMS上报"),
    C32140120(32140120, "AC模块组：机柜温度过温","EMS上报"),
    C32140121(32140121, "AC模块组：离网电压缺相","EMS上报"),






    // DCAC故障信息表2(tab40-2)
    C32140200(32140200, "AC模块组: 24V辅助电源故障","EMS上报"),
    C32140201(32140201, "AC模块组: 紧急停机","EMS上报"),
    C32140202(32140202, "AC模块组: 接地故障","EMS上报"),
    C32140203(32140203, "AC模块组: 直流母线过压","EMS上报"),
    C32140204(32140204, "AC模块组: 模块温度过温","EMS上报"),
    C32140205(32140205, "AC模块组: 模块电流不平衡","EMS上报"),
    C32140206(32140206, "AC模块组: 风扇故障","EMS上报"),
    C32140207(32140207, "AC模块组: 直流继电器开路","EMS上报"),
    C32140208(32140208, "AC模块组: 校准参数异常","EMS上报"),
    C32140209(32140209, "AC模块组: 母线电压不平衡","EMS上报"),
    C32140210(32140210, "AC模块组: 保险故障","EMS上报"),
    C32140211(32140211, "AC模块组: DSP初始化故障","EMS上报"),
    C32140212(32140212, "AC模块组: 直流软启动失败","EMS上报"),
    C32140213(32140213, "AC模块组: CANA通讯故障","EMS上报"),
    C32140214(32140214, "AC模块组: 直流输入反接","EMS上报"),
    C32140215(32140215, "AC模块组: 交流电流直流分量异常","EMS上报"),
    C32140216(32140216, "AC模块组: 变压器过温","EMS上报"),
    C32140217(32140217, "AC模块组: U2通信异常2","EMS上报"),
    C32140218(32140218, "AC模块组: BMS故障或直流开关断开","EMS上报"),
    C32140219(32140219, "AC模块组: 防雷器故障","EMS上报"),
    C32140220(32140220, "AC模块组: 过载超时故障","EMS上报"),
    C32140221(32140221, "AC模块组: 交流软启动失败","EMS上报"),
    C32140222(32140222, "AC模块组: 同步信号故障1","EMS上报"),
    C32140223(32140223, "AC模块组: DSP版本故障","EMS上报"),
    C32140224(32140224, "AC模块组: 交流继电器开路","EMS上报"),
    C32140225(32140225, "AC模块组: 采样零点异常","EMS上报"),
    C32140226(32140226, "AC模块组: U2通信异常1","EMS上报"),
    C32140227(32140227, "AC模块组: 15V辅助电源故障","EMS上报"),
    C32140228(32140228, "AC模块组: 模块重号故障","EMS上报"),
    C32140229(32140229, "AC模块组: RS485通讯故障","EMS上报"),
    C32140230(32140231, "AC模块组: CANB通讯故障","EMS上报"),
    C32140231(32140231, "AC模块组: 重启过多","EMS上报"),





    // PCS中DCAC故障(tab40-3)
    C32140300(32140300, "AC模块组: CPLD版本故障","EMS上报"),
    C32140302(32140302, "AC模块组: 直流继电器短路","EMS上报"),
    C32140303(32140303, "AC模块组: 直流母线欠压","EMS上报"),
    C32140304(32140304, "AC模块组: 交流继电器短路","EMS上报"),
    C32140305(32140305, "AC模块组: 同步信号故障2","EMS上报"),
    C32140306(32140306, "AC模块组: 参数不匹配","EMS上报"),
    C32140307(32140307, "AC模块组: CANC通讯故障","EMS上报"),
    C32140308(32140308, "AC模块组: 环境湿度过高","EMS上报"),
    C32140309(32140309, "AC模块组: BMS电压异常","EMS上报"),
    C32140310(32140310, "AC模块组: BMS电流异常","EMS上报"),
    C32140311(32140311, "AC模块组: BMS温度异常","EMS上报"),
    C32140312(32140312, "AC模块组: BMS跳机","EMS上报"),
    C32140313(32140313, "AC模块组: 绝缘检测异常","EMS上报"),
    C32140314(32140314, "AC模块组: 硬件采样异常","EMS上报"),
    C32140315(32140315, "AC模块组: 远程通信丢失","EMS上报"),





    // STS (tab30-1)
    C32230100(32230100, "STS: 电网电压反序","EMS上报"),
    C32230101(32230101, "STS: 电网电压缺相","EMS上报"),
    C32230102(32230102, "STS: 离网电压反序","EMS上报"),
    C32230103(32230103, "STS: 离网电压缺相","EMS上报"),
    C32230104(32230104, "STS: 校准参数异常","EMS上报"),
    C32230105(32230105, "STS: 采样零点异常","EMS上报"),
    C32230106(32230106, "STS: 过载报警","EMS上报"),
    C32230107(32230107, "STS: 环境温度过温","EMS上报"),
    C32230108(32230108, "STS: 锁相失败","EMS上报"),
    C32230109(32230109, "STS: 电网电压不平衡","EMS上报"),
    C32230110(32230110, "STS: 电网侧欠压","EMS上报"),
    C32230111(32230111, "STS: 电网侧过压","EMS上报"),
    C32230112(32230112, "STS: 电网侧欠频","EMS上报"),
    C32230113(32230113, "STS: 电网侧过频","EMS上报"),
    C32230114(32230114, "STS: 频繁切换故障","EMS上报"),
    C32230115(32230115, "STS: 电网掉电","EMS上报"),
    C32230116(32230116, "STS: 紧急停机","EMS上报"),
    C32230117(32230117, "STS: 15V辅助电源故障","EMS上报"),
    C32230118(32230118, "STS: 24V辅助电源故障","EMS上报"),
    C32230119(32230119, "STS: CANA通讯故障","EMS上报"),
    C32230120(32230120, "STS: CANB通讯故障","EMS上报"),
    C32230121(32230121, "STS: RS-485通讯故障","EMS上报"),
    C32230122(32230122, "STS: DSP初始化故障","EMS上报"),
    C32230123(32230123, "STS: 并网空开开路","EMS上报"),
    C32230124(32230124, "STS: 并网空开短路","EMS上报"),
    C32230125(32230125, "STS: 过载超时故障","EMS上报"),
    C32230126(32230126, "STS: 同步信号1故障","EMS上报"),
    C32230127(32230127, "STS: 环温探头故障","EMS上报"),
    C32230128(32230128, "STS: 柜温探头故障","EMS上报"),
    C32230129(32230129, "STS: 机柜过温故障","EMS上报"),
    C32230130(32230130, "STS: 模块温度过温","EMS上报"),
    C32230131(32230131, "STS: 风扇故障","EMS上报"),




    // STS (tab30-2)
    C32230200(32230200, "STS: DSP版本故障","EMS上报"),
    C32230201(32230201, "STS: CPLD版本故障","EMS上报"),
    C32230202(32230202, "STS: 旁路故障","EMS上报"),
    C32230203(32230203, "STS: 防雷器故障","EMS上报"),
    C32230204(32230204, "STS: 模块温度探头故障","EMS上报"),
    C32230205(32230205, "STS: 电网电压畸变","EMS上报"),



    // BatteryStack中BMS故障(tab20-1)
    C33020102(33020102, "单体温差",""),
    C33020104(33020104, "充电过流",""),
    C33020106(33020106, "放电过流",""),
    C33020107(33020107, "级柱过温",""),
    C33020108(33020108, "单体过压",""),
    C33020109(33020109, "单体压差",""),
    C33020110(33020110, "放电低温",""),
    C33020112(33020112, "单体欠压",""),
    C33020116(33020116, "绝缘故障",""),
    C33020117(33020117, "LMU通讯故障",""),
    C33020118(33020118, "单体过温",""),
    C33020119(33020119, "BMU通讯故障",""),
    C33020121(33020121, "充电低温",""),
    C33020123(33020123, "总压异常",""),
    C33020124(33020124, "线束故障",""),
    C33020126(33020126, "继电器故障",""),
    C33020131(33020131, "温度传感器故障",""),



    // DCAC (tab40-1)
    C37040100(37040100, "AC模块组: 交流母线过压","EMS上报"),
    C37040101(37040101, "AC模块组: 交流母线过频","EMS上报"),
    C37040102(37040102, "AC模块组: 交流母线欠压","EMS上报"),
    C37040103(37040103, "AC模块组: 逆变器孤岛保护","EMS上报"),
    C37040104(37040104, "AC模块组: 直流输入过压","EMS上报"),
    C37040105(37040105, "AC模块组: 并离网切换错误","EMS上报"),
    C37040106(37040106, "AC模块组: 交流反序","EMS上报"),
    C37040107(37040107, "AC模块组: 直流输入欠压","EMS上报"),
    C37040108(37040108, "AC模块组: 过载报警","EMS上报"),
    C37040109(37040109, "AC模块组: 交流母线电压异常","EMS上报"),
    C37040110(37040110, "AC模块组: 交流缺相","EMS上报"),
    C37040111(37040111, "AC模块组: 交流母线电压不平衡","EMS上报"),
    C37040112(37040112, "AC模块组: 交流母线欠频","EMS上报"),
    C37040113(37040113, "AC模块组: 电池电量不足","EMS上报"),
    C37040114(37040114, "AC模块组: 直流输入过流","EMS上报"),
    C37040115(37040115, "AC模块组: 离网电压反序","EMS上报"),
    C37040116(37040116, "AC模块组: 锁相失败","EMS上报"),
    C37040117(37040117, "AC模块组: 环境温度过温","EMS上报"),
    C37040118(37040118, "AC模块组: 环温探头故障","EMS上报"),
    C37040119(37040119, "AC模块组: 柜温探头故障","EMS上报"),
    C37040120(37040120, "AC模块组: 机柜温度过温","EMS上报"),
    C37040121(37040121, "AC模块组: 离网电压缺相","EMS上报"),


    // DCAC (tab40-2)
    C37040200(37040200, "AC模块组: 24V辅助电源故障","EMS上报"),
    C37040201(37040201, "AC模块组: 紧急停机","EMS上报"),
    C37040202(37040202, "AC模块组: 接地故障","EMS上报"),
    C37040203(37040203, "AC模块组: 直流母线过压","EMS上报"),
    C37040204(37040204, "AC模块组: 模块温度过温","EMS上报"),
    C37040205(37040205, "AC模块组: 模块电流不平衡","EMS上报"),
    C37040206(37040206, "AC模块组: 风扇故障","EMS上报"),
    C37040207(37040207, "AC模块组: 直流继电器开路","EMS上报"),
    C37040208(37040208, "AC模块组: 校准参数异常","EMS上报"),
    C37040209(37040209, "AC模块组: 母线电压不平衡","EMS上报"),
    C37040210(37040210, "AC模块组: 保险故障","EMS上报"),
    C37040211(37040211, "AC模块组: DSP初始化故障","EMS上报"),
    C37040212(37040212, "AC模块组: 直流软启动失败","EMS上报"),
    C37040213(37040213, "AC模块组: CANA通讯故障","EMS上报"),
    C37040214(37040214, "AC模块组: 直流输入反接","EMS上报"),
    C37040215(37040215, "AC模块组: 交流电流直流分量异常","EMS上报"),
    C37040216(37040216, "AC模块组: 变压器过温","EMS上报"),
    C37040217(37040217, "AC模块组: U2通信异常2","EMS上报"),
    C37040218(37040218, "AC模块组: BMS故障或直流开关断开","EMS上报"),
    C37040219(37040219, "AC模块组: 防雷器故障","EMS上报"),
    C37040220(37040220, "AC模块组: 过载超时故障","EMS上报"),
    C37040221(37040221, "AC模块组: 交流软启动失败","EMS上报"),
    C37040222(37040222, "AC模块组: 同步信号故障1","EMS上报"),
    C37040223(37040223, "AC模块组: DSP版本故障","EMS上报"),
    C37040224(37040224, "AC模块组: 交流继电器开路","EMS上报"),
    C37040225(37040225, "AC模块组: 采样零点异常","EMS上报"),
    C37040226(37040226, "AC模块组: U2通信异常1","EMS上报"),
    C37040227(37040227, "AC模块组: 15V辅助电源故障","EMS上报"),
    C37040228(37040228, "AC模块组: 模块重号故障","EMS上报"),
    C37040229(37040229, "AC模块组: RS485通讯故障","EMS上报"),
    C37040230(37040230, "AC模块组: CANB通讯故障","EMS上报"),
    C37040231(37040231, "AC模块组: 重启过多","EMS上报"),


    // DCAC (tab40-3)
    C37040300(37040300, "AC模块组: CPLD版本故障","EMS上报"),
    C37040301(37040301, "AC模块组: 硬件版本故障","EMS上报"),
    C37040302(37040302, "AC模块组: 直流继电器短路","EMS上报"),
    C37040303(37040303, "AC模块组: 直流母线欠压","EMS上报"),
    C37040304(37040304, "AC模块组: 交流继电器短路","EMS上报"),
    C37040305(37040305, "AC模块组: 同步信号故障2","EMS上报"),
    C37040306(37040306, "AC模块组: 参数不匹配","EMS上报"),
    C37040307(37040307, "AC模块组: CANC通讯故障","EMS上报"),
    C37040308(37040308, "AC模块组: 环境湿度过高","EMS上报"),
    C37040309(37040309, "AC模块组: BMS电压异常","EMS上报"),
    C37040310(37040310, "AC模块组: BMS电流异常","EMS上报"),
    C37040311(37040311, "AC模块组: BMS温度异常","EMS上报"),
    C37040312(37040312, "AC模块组: BMS跳机","EMS上报"),
    C37040313(37040313, "AC模块组: 绝缘检测异常","EMS上报"),
    C37040314(37040314, "AC模块组: 硬件采样异常","EMS上报"),
    C37040315(37040315, "AC模块组: 远程通信丢失","EMS上报"),


    // DCAC (tab41-6)
    C37041600(37041600, "DCAC模块1故障",""),
    C37041601(37041601, "DCAC模块2故障",""),
    C37041602(37041602, "DCAC模块3故障",""),
    C37041603(37041603, "DCAC模块4故障",""),
    C37041604(37041604, "DCAC模块5故障",""),
    C37041605(37041605, "DCAC模块6故障",""),
    C37041606(37041606, "DCAC模块7故障",""),
    C37041607(37041607, "DCAC模块8故障",""),
    C37041608(37041608, "DCAC模块9故障",""),
    C37041609(37041609, "DCAC模块10故障",""),
    C37041610(37041610, "机架功能板故障",""),




    // DCAC (tab41-7)
    C37041700(37041700, "PCS: 光伏欠压",""),
    C37041701(37041701, "PCS: 光伏过压",""),
    C37041702(37041702, "PCS: 光伏接触器短路",""),
    C37041703(37041703, "PCS: 光伏接触器开路",""),
    C37041704(37041704, "PCS: PE-N接触器短路",""),
    C37041705(37041705, "PCS: PE-N接触器开路",""),
    C37041706(37041706, "PCS: N接触器短路",""),
    C37041707(37041707, "PCS: N接触器开路",""),




    // DCDC (tab50-1)
    C37150100(37150100, "DC模块组: 直流母线过压","EMS上报"),
    C37150101(37150101, "DC模块组: 直流母线欠压","EMS上报"),
    C37150102(37150102, "DC模块组: 直流输入过压","EMS上报"),
    C37150103(37150103, "DC模块组: 直流输入欠压","EMS上报"),
    C37150104(37150104, "DC模块组: 直流输入过流","EMS上报"),
    C37150105(37150105, "DC模块组: 直流输入掉电","EMS上报"),
    C37150106(37150106, "DC模块组: 重启过多","EMS上报"),
    C37150107(37150107, "DC模块组: 电池继电器短路","EMS上报"),
    C37150108(37150108, "DC模块组: 光伏能量不足","EMS上报"),
    C37150109(37150109, "DC模块组: 电池电量不足","EMS上报"),
    C37150110(37150110, "DC模块组: 环境温度过高","EMS上报"),
    C37150111(37150111, "DC模块组: BMS故障或直流开关断开","EMS上报"),
    C37150112(37150112, "DC模块组: U2通信异常1","EMS上报"),
    C37150113(37150113, "DC模块组: 机柜温度过高","EMS上报"),
    C37150114(37150114, "DC模块组: 柜温探头故障","EMS上报"),
    C37150115(37150115, "DC模块组: 环温探头故障","EMS上报"),
    C37150116(37150116, "DC模块组: 模块电流不平衡","EMS上报"),
    //    C37150117(37150117, ""),
    C37150118(37150118, "DC模块组: 24V辅助电源故障","EMS上报"),
    C37150119(37150119, "DC模块组: 紧急停机","EMS上报"),
    C37150120(37150120, "DC模块组: 接地故障","EMS上报"),
    C37150121(37150121, "DC模块组: 母线电压不平衡","EMS上报"),
    C37150122(37150122, "DC模块组: 模块温度过温","EMS上报"),
    C37150123(37150123, "DC模块组: 风扇故障","EMS上报"),
    C37150124(37150124, "DC模块组: 电池继电器开路","EMS上报"),
    C37150125(37150125, "DC模块组: 校准参数异常","EMS上报"),
    C37150126(37150126, "DC模块组: 保险故障","EMS上报"),
    C37150127(37150127, "DC模块组: DSP初始化故障","EMS上报"),
    C37150128(37150128, "DC模块组: 电池软启动失败","EMS上报"),
    C37150129(37150129, "DC模块组: CANA通讯故障","EMS上报"),
    C37150130(37150130, "DC模块组: 母线继电器开路","EMS上报"),
    C37150131(37150131, "DC模块组: 母线软启动失败","EMS上报"),


    // DCDC (tab50-2)
    C37150200(37150200, "DC模块组: DSP版本故障","EMS上报"),
    C37150201(37150201, "DC模块组: CPLD版本故障","EMS上报"),
    C37150202(37150202, "DC模块组: 参数不匹配","EMS上报"),
    C37150203(37150203, "DC模块组: 硬件版本故障","EMS上报"),
    C37150204(37150204, "DC模块组: 485通讯故障","EMS上报"),
    C37150205(37150205, "DC模块组: CANB通讯故障","EMS上报"),
    C37150206(37150206, "DC模块组: 模块重号故障","EMS上报"),
    C37150207(37150207, "DC模块组: 15V辅助电源故障","EMS上报"),
    C37150208(37150208, "DC模块组: 母线继电器短路","EMS上报"),
    C37150209(37150209, "DC模块组: BMS电压异常","EMS上报"),
    C37150210(37150210, "DC模块组: BMS电流异常","EMS上报"),
    C37150211(37150211, "DC模块组: BMS温度异常","EMS上报"),
    C37150212(37150212, "DC模块组: BMS跳机","EMS上报"),
    C37150213(37150213, "DC模块组: 绝缘检测异常","EMS上报"),
    //    C37150214(37150214, ""),
    C37150215(37150215, "PCS: 光伏欠压","EMS上报"),
    C37150216(37150216, "PCS: 光伏过压","EMS上报"),
    C37150217(37150217, "PCS: 光伏接触器短路","EMS上报"),
    C37150218(37150218, "PCS: 光伏接触器开路","EMS上报"),
    C37150219(37150219, "PCS: PE-N接触器短路","EMS上报"),
    C37150220(37150220, "PCS: PE-N接触器开路","EMS上报"),
    C37150221(37150221, "PCS: N接触器短路","EMS上报"),
    C37150222(37150222, "PCS: N接触器开路","EMS上报"),




    // DCDC (tab51-4)
    C37151400(37151400, "DCDC模块1故障",""),
    C37151401(37151401, "DCDC模块2故障",""),
    C37151402(37151402, "DCDC模块3故障",""),
    C37151403(37151403, "DCDC模块4故障",""),
    C37151404(37151404, "DCDC模块5故障",""),
    C37151405(37151405, "DCDC模块6故障",""),
    C37151406(37151406, "DCDC模块7故障",""),
    C37151407(37151407, "DCDC模块8故障",""),
    C37151408(37151408, "DCDC模块9故障",""),
    C37151409(37151409, "DCDC模块10故障",""),
    C37151410(37151410, "机架功能板故障",""),



    // AIR_CONDITION (tab60-1)
    C39060100(39060100, "空调:高温告警","EMS上报"),
    C39060101(39060101, "空调:内风机故障告警","EMS上报"),
    C39060102(39060102, "空调:外风机故障告警","EMS上报"),
    C39060103(39060103, "空调:压缩机故障告警","EMS上报"),
    C39060104(39060104, "空调:柜内回风温度温度探头故障","EMS上报"),
    C39060105(39060105, "空调:系统高压力告警","EMS上报"),
    C39060106(39060106, "空调:低温告警","EMS上报"),
    C39060107(39060107, "空调:直流过压告警","EMS上报"),
    C39060108(39060108, "空调:直流欠压告警","EMS上报"),
    C39060109(39060109, "空调:交流过压告警","EMS上报"),
    C39060110(39060110, "空调:交流欠压告警","EMS上报"),
    C39060111(39060111, "空调:交流掉电告警","EMS上报"),
    C39060112(39060112, "空调:蒸发器温度传感器故障","EMS上报"),
    C39060113(39060113, "空调:冷凝器温度传感器故障","EMS上报"),
    C39060114(39060114, "空调:环境温度传感器故障","EMS上报"),
    C39060115(39060115, "空调:蒸发器冻结报警","EMS上报"),
    C39060116(39060116, "空调:频繁高压力告警","EMS上报"),
    C39060117(39060117, "空调:严重告警总状态","EMS上报"),

    /**
     * 储能设备故障码 END
     * 规则：(300 + 设备类型ID前两位) * 100000 + 设备对应告警表序号 * 100 + bit位
     */
    ;

    private final int code;//桩端故障码
    private final String desc;
    private final String instruction;

    DeviceStatusCodeType(int code, String desc,String instruction) {
        this.desc = desc;
        this.code = code;
        this.instruction = instruction;
    }

    public static DeviceStatusCodeType getByCode(int code) {
        for (DeviceStatusCodeType type : DeviceStatusCodeType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return DeviceStatusCodeType.C00;
    }

    public static String getDescByCode(int code) {
        for (DeviceStatusCodeType type : DeviceStatusCodeType.values()) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return DeviceStatusCodeType.C00.getDesc();
    }
    public static List<String> getCodeList () {
        List<String> list = new ArrayList<>();
        for (DeviceStatusCodeType type : DeviceStatusCodeType.values()) {
           list.add(String.valueOf(type.getCode()));
        }
        return list;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getInstruction() {
        return this.instruction;
    }


}