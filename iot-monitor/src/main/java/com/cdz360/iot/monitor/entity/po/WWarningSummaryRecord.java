package com.cdz360.iot.monitor.entity.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;


import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Document(collection = "w_warning_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WWarningSummaryRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String warningName;

    /**
     * 站点名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    /**
     * 设备型号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String modelName;

    /**
     * 软件版本
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String firmwareVer;

    /**
     * 告警数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long warningAccount;

    /**
     * 站点分布汇总
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    /**
     * 设备型号分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> modelNameList;

    /**
     * 软件版本分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> firmwareList;

    /**
     * 告警分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> warningCodeList;

    /**
     * 告警名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> warningNameList;

    /**
     * 场站列表
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteNameList;






}
