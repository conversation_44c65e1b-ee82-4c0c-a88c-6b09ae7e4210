package com.cdz360.iot.monitor.mapper;

//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.chargerlink.device.monitor.entity.po.WarningDetail;
import com.cdz360.iot.monitor.entity.result.WarningDetailVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 告警描述说明配置表
 *
 * @ClassName WarningDetailMapper
 * <AUTHOR>
 * @Description
 * @Date 2019.4.16
 */
@Mapper
public interface WarningDetailMapper //extends BaseMapper<WarningDetail>
{
    /**
     * 查询列表
     */
    List<WarningDetailVo> queryWarningDetailList(WarningDetailVo warningDetailVo);
}
