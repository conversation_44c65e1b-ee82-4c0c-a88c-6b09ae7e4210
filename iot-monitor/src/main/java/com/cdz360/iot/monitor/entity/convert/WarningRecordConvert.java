package com.cdz360.iot.monitor.entity.convert;

import com.cdz360.iot.monitor.entity.po.AlarmRecord;
import com.cdz360.iot.monitor.entity.result.AlarmListVo;

/**
 * 将告警记录表中的数据转化为告警列表类型对像
 *
 * <AUTHOR>
 * Create on 2018/08/14 09:54
 */
public class WarningRecordConvert {

    public static AlarmListVo convertWarningRecordToAlarmListVo(AlarmRecord wWarningRecord) {
        AlarmListVo alarmListVo = new AlarmListVo();
        alarmListVo.setId(wWarningRecord.getWarningId());
        alarmListVo.setAlarmType(wWarningRecord.getWarningCode());
        alarmListVo.setBoxOutFactoryCode(wWarningRecord.getBoxOutFactoryCode());
        if (null != wWarningRecord.getConnectorId()) {
            alarmListVo.setConnectorId(wWarningRecord.getConnectorId().toString());
        }
        if (null != wWarningRecord.getSiteId()) {
            alarmListVo.setSiteId(wWarningRecord.getSiteId());
        }
        alarmListVo.setSiteName(wWarningRecord.getSiteName());
        alarmListVo.setCreateTime(wWarningRecord.getStartTime());
        alarmListVo.setStatus(wWarningRecord.getStatus());
        return alarmListVo;
    }
}
