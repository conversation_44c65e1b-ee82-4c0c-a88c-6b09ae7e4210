package com.cdz360.iot.monitor.constant;

import lombok.Getter;

/**
 * 设备类型
 *
 * <AUTHOR>
 * @Date Create on 2018/7/31 21:18
 */
@Getter
public enum BoxTypeVoEnum {

    CHARGER_INNER(0, 0, "内置"),
    CHARGER_OUTER(1, 1, "外置"),
    CHARGER_X8(2, 301, "X8设备"),
    CHARGER_X5(3, 302, "X5设备"),
    CHARGER_X9(4, 303, "X9设备"),
    CHARGER_X10(5, 304, "X10设备"),
    CHARGER_A6(6, 305, "A6设备"),
    CHARGER_SINEXCEL(7, 306, "SINEXCEL设备"),
    CHARGER_R6(8, 307, "R6设备"),
    CHARGER_R8(9, 308, "R8设备"),
    CHARGER_X6P(10, 309, "X6P设备"),
    CHARGER_X6L(11, 311, "X6L设备"),
    CHARGER_R9(12, 312, "R9设备"),
    CHARGER_A10(13, 313, "A10设备"),
    CHARGER_D10(14, 314, "D10设备"),
    CHARGER_X10E(15, 316, "X10E设备"),
    ;

    private int value;
    private int num;
    private String lable;

    BoxTypeVoEnum(int value, int num, String lable) {
        this.value = value;
        this.num = num;
        this.lable = lable;
    }

}
