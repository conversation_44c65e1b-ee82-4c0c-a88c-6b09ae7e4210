package com.cdz360.iot.monitor.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 警告列表
 *
 * <AUTHOR>
 * @Date Create on 2018/7/25 10:19
 */
@Data
public class AlarmListVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 告警id
     */
    private Long id;

    /**
     * 告警大类名称（充电桩告警、充电插座告警）
     */
    private String warningName;

    /**
     * 告警编码
     */
    private String warningCode;

    /**
     * 告警处理说明
     */
    private String warningInstructions;

    /**
     * 告警类型{@link com.cdz360.iot.monitor.constant.AlarmTypeEnum}
     */
    private String alarmType;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 告警类型名称
     */
    private String alarmTypeName;

    /**
     * 所属充电桩编号
     */
    private String boxOutFactoryCode;

    /**
     * 所属充电插座序号
     */
    private String connectorId;

    /**
     * 所属站点ID
     */
    private String siteId;

    /**
     * 所属站点名称
     */
    private String siteName;

    /**
     * 告警产生时间
     */
    private Date createTime;

    /**
     * 告警持续时长
     */
    private Long duration;

    /**
     * 告警状态
     */
    private Integer status;

    /**
     *
     */
    private Integer connectID;

    /*
     * 产品类型
     */
    private String productName;

    /**
     * 设备code
     */
    private String boxCode;
}
