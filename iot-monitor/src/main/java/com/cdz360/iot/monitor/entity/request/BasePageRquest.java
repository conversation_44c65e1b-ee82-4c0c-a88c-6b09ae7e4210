package com.cdz360.iot.monitor.entity.request;

/**
 * 分页相关数据
 *
 * @ClassName PageRquest
 * <AUTHOR>
 * @Description
 * @Date 2019.5.14
 */
public class BasePageRquest {
    /**
     * 当前页,GET时默认为1
     */
    private Integer page;
    /**
     * 每页大小,GET时默认为10
     */
    private Integer rows;

    public Integer getPage() {
        if (page == null || page == 0) {
            return 1;
        }
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        if (rows == null || rows == 0) {
            return 10;
        }
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}
