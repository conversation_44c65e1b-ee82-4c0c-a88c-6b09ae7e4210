<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.monitor.mapper.WarningDetailMapper">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">

        id,warning_code AS warningCode, warning_name AS warningName,
        warning_instructions AS warningInstructions,
        warning_level AS warningLevel
    </sql>

    <select id="queryWarningDetailList" resultType="com.cdz360.iot.monitor.entity.result.WarningDetailVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM w_warning_detail
        <where>
            <if test="warningCode != null and warningCode!= ''">
                AND warning_code = #{warningCode}
            </if>
            <if test="warningName != null and warningName!= ''">
                AND warning_name like CONCAT('%',#{warningName},'%')
            </if>
            <if test="warningLevel != null ">
                AND warning_level = #{warningLevel}
            </if>
        </where>
        order by create_time desc ,update_time desc
    </select>

</mapper>
