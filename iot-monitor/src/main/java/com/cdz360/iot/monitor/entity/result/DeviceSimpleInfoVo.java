package com.cdz360.iot.monitor.entity.result;

///**
// * 充电桩部分信息
// *
// * <AUTHOR>
// * @Date Create on 2018/8/2 000217:31
// */
//@Data
//public class DeviceSimpleInfoVo implements Serializable {
//
//    /**
//     * 充电桩id
//     */
//    private String boxCode;
//    /**
//     * 充电桩编号
//     */
//    private String boxOutFactoryCode;
//    /**
//     * 名称备注
//     */
//    private String remark;
//
//    /**
//     * 充电桩状态
//     */
//    private Integer status;
//
//    /**
//     * 插座状态统计，插座状态
//     **/
//    private Map<String, Integer> connectorStatusMap;
//
//    /*
//     * 充电桩类型
//     * **/
//    private Integer boxType;
//
//    /**
//     * 父设备
//     */
//    private String parentDeviceId;
//}
