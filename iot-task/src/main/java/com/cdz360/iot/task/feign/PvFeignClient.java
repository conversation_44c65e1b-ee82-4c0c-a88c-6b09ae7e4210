package com.cdz360.iot.task.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_PV)
public interface PvFeignClient {
    /**
     * 采集并处理华为云设备日数据
     */
    @PostMapping("/iot/biz/pv/collectAndProcessKpiDay")
    BaseResponse collectAndProcessDeviceKpiDay(@RequestParam String date);
}