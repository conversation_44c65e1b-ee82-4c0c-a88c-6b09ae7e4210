package com.cdz360.iot.task.task;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.task.feign.PvFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 设备日数据采集定时任务
 * 每天凌晨1点执行，获取前一天的设备日数据
 */
@Slf4j
@Component
public class HwCloudDeviceDayTask {

    @Autowired
    private PvFeignClient pvFeignClient;

    /**
     * 每天凌晨1:00执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void collectDeviceKpiDay() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("开始执行华为云设备日数据采集任务，采集日期: {}", yesterday);
        try {
            BaseResponse response = pvFeignClient.collectAndProcessDeviceKpiDay(yesterday.toString());
            if (response != null && response.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                log.info("华为云设备日数据采集任务执行成功");
            } else {
                log.error("华为云设备日数据采集任务执行失败: {}",
                        response != null ? response.getError() : "");
            }
        } catch (Exception e) {
            log.error("华为云设备日数据采集任务执行异常: {}", e.getMessage(), e);
        }
        log.info("设备日数据采集任务执行完成");
    }
}