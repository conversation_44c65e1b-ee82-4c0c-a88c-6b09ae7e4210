package com.cdz360.iot.task;

import com.cdz360.iot.task.biz.GracefulShutdownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;


@SpringBootApplication
@ComponentScan(basePackages = {"com.cdz360"})
@EnableDiscoveryClient
@EnableRabbit
@EnableFeignClients(basePackages = {"com.cdz360.iot.device.*.feign", "com.cdz360.iot.task.feign"})
@EnableScheduling
public class IotTaskMain {
    private final Logger logger = LoggerFactory.getLogger(IotTaskMain.class);


    @Autowired
    private GracefulShutdownService gracefulShutdownService;

    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(IotTaskMain.class).web(WebApplicationType.SERVLET).run(args);
    }

    @PreDestroy
    public void destroy() {
        gracefulShutdownService.shutdown();
    }

}
