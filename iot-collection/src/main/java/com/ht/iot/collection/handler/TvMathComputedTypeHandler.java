package com.ht.iot.collection.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ht.iot.collection.model.TvMathComputed;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(List.class)
public class TvMathComputedTypeHandler extends AbstractJsonTypeHandler<List<TvMathComputed>> {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<TvMathComputed> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting TvMathComputed list to JSON", e);
        }
    }

    protected List<TvMathComputed> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<TvMathComputed>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing TvMathComputed JSON: " + json, e);
        }
    }
}
