package com.ht.iot.collection.handler;


import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.cdz360.iot.model.modbus.dto.ModbusAbstractTv;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;


@MappedTypes(List.class)
public class ModbusAbstractTvTypeHandler extends AbstractJsonTypeHandler<List<ModbusAbstractTv>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<ModbusAbstractTv> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting ModbusAbstractTv list to JSON", e);
        }
    }

    protected List<ModbusAbstractTv> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<ModbusAbstractTv>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing ModbusAbstractTv JSON: " + json, e);
        }
    }
}