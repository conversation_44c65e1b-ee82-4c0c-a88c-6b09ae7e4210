package com.ht.iot.collection.task;

import com.ht.iot.collection.biz.LaunchingExecutor;
import com.ht.iot.collection.biz.TimeoutMonitorExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TimeoutMonitorTask {

    @Autowired
    private TimeoutMonitorExecutor timeoutMonitorExecutor;

    /**
     * 将待下发的设备添加到activeGwRepo
     */
    @Scheduled(initialDelay = 1000, fixedRate = 1 * 1000)
    public void monitTimeout() {
        //log.trace(">>");
        timeoutMonitorExecutor.checkTimeout();
        //log.trace("<<");
    }
}
