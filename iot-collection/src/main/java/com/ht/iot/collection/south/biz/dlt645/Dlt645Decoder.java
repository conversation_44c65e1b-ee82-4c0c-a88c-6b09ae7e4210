package com.ht.iot.collection.south.biz.dlt645;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcNeedMoreDataException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.meter.vo.MeterAbcItem;
import com.cdz360.base.model.meter.vo.MeterTransformationRatio;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import com.cdz360.iot.model.modbus.type.P645DataType;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

/**
 * 国标 DL/T 645-2007 电表 报文解码
 */
@Slf4j
@Service
public class Dlt645Decoder {


    //    public static final byte[] READ_CMD_DLT645_HEADER = {(byte) 0xFE, (byte) 0xFE, (byte) 0xFE,
//        (byte) 0xFE};
//    public static final byte DLT645_PREFIX_BYTE = (byte)0xFE;   // 前导字节
//    public static final byte READ_CMD_DLT645_CODE = 0x68;
//    public static final byte READ_CMD_HIKING_CONTROL_CODE = 0x11; // 主站请求控制码
//    public static final byte READ_CMD_HIKING_END_CODE = 0x16;
    public static final byte BYTE_0x33 = 0x33;
//    public static final byte BYTE_0x91 = (byte) 0x91; // 从站正常应答控制码(无后续数据帧)


    public String parseMeterNo(String tid, String dno, byte[] buf,
        P645DataType dataType) {
        int msgLen = parseCommonInfo(tid, dno, buf, dataType);
        log.debug("[{}] {} msgLen = {}", tid, dno, msgLen);
        if (msgLen == 0) {
            return null;
        }
        int idx = 14;    // 从数据部分开始
        byte[] meterNoBytes = new byte[6];
        System.arraycopy(buf, idx, meterNoBytes, 0, 6);
        return ByteUtils.bytesToHex(meterNoBytes);
    }

    public String parseMeterModel(String tid, String dno, byte[] buf,
        P645DataType dataType) {
        int msgLen = parseCommonInfo(tid, dno, buf, dataType);
        log.debug("[{}] {} msgLen = {}", tid, dno, msgLen);
        if (msgLen == 0) {
            return null;
        }
        int idx = 14;    // 从数据部分开始
        return ByteUtils.byteBuf2Ascii(buf, idx, 10);
    }

//    public MeterRtInfo parseBasicInfo(String tid, String dno, byte[] buf,
//        P645DataType dataType) {
//        int msgLen = parseCommonInfo(tid, dno, buf, dataType);
//        if (msgLen == 0) {
//            return null;
//        }
//        log.debug("[{}] {} msgLen = {}", tid, dno, msgLen);
//        int idx = 14;    // 从数据部分开始
//
//        MeterRtInfo info = new MeterRtInfo();
//        idx += 6;    // 通信地址
//
//        // 表号
//        String deviceNo = ByteUtil.hexDump(buf, idx, 6);
//        info.setSn(deviceNo);
//        idx += 6;
//
//        idx += 32;    //资产管理编码
//        idx += 6; // 额定电压
//        idx += 6; // 额定电流
//        idx += 6; // 最大电流
//        idx += 4; // 有功准确度等级
//        idx += 4; // 无功准确度等级
//        idx += 3; // 电表有功常数
//        idx += 3; // 电表无功常数
//
//        // 电表型号
//        String model = ByteUtil.bytes2ASCII(buf, idx, 10);
//        info.setDeviceModel(model);
//        idx += 10;
//
//        return info;
//    }
//
//    public MeterKwhItem parsePowerData(String tid, String dno, byte[] buf,
//        @Nullable MeterTransformationRatio tr,
//        P645DataType p645DataType) {
//        int msgLength = parseCommonInfo(tid, dno, buf, p645DataType);
//        if (msgLength == 0) {
//            return null;
//        }
//        int idx = 9;    // 从length字段开始
//
//        MeterKwhItem item = new MeterKwhItem();
//        int length = buf[idx++] & 0xFF;
//        log.debug("length = {}", length);
//
//        // 循环减去0x33，获取原始值，此处包括DI0~3
//        byte[] tmp = new byte[length];
//        System.arraycopy(buf, idx, tmp, 0, length);
//        int i = 0;
//        while (i < tmp.length) {
//            tmp[i] = (byte) (tmp[i] - BYTE_0x33);
//            i++;
//        }
//
//        int offset = 0;
//
//        offset += 4; // skip DI0~3
//        int itemCount = (length - Dlt645Constants.BYTE_DLT645_LENGTH_ADD) / 4;
//
//        BigDecimal total = new BigDecimal(ByteUtils.byte2BcdIntSE(tmp, offset, 4))
//            .movePointLeft(2)
//            .setScale(2, RoundingMode.HALF_UP);
//        item.setTotal(this.multiplyByMeterTransformationRatio(tr, p645DataType, total));
//        offset += 4;
//        itemCount--;
//
//        if (itemCount > 0) {
//            BigDecimal v1 = new BigDecimal(ByteUtils.byte2BcdIntSE(tmp, offset, 4))
//                .movePointLeft(2)
//                .setScale(2, RoundingMode.HALF_UP);
//            item.setV1(this.multiplyByMeterTransformationRatio(tr, p645DataType, v1));
//            offset += 4;
//            itemCount--;
//        }
//
//        if (itemCount > 0) {
//            BigDecimal v2 = new BigDecimal(ByteUtils.byte2BcdIntSE(tmp, offset, 4))
//                .movePointLeft(2)
//                .setScale(2, RoundingMode.HALF_UP);
//            item.setV2(this.multiplyByMeterTransformationRatio(tr, p645DataType, v2));
//            offset += 4;
//            itemCount--;
//        }
//
//        if (itemCount > 0) {
//            BigDecimal v3 = new BigDecimal(ByteUtils.byte2BcdIntSE(tmp, offset, 4))
//                .movePointLeft(2)
//                .setScale(2, RoundingMode.HALF_UP);
//            item.setV3(this.multiplyByMeterTransformationRatio(tr, p645DataType, v3));
//            offset += 4;
//            itemCount--;
//        }
//
//        if (itemCount > 0) {
//            BigDecimal v4 = new BigDecimal(ByteUtils.byte2BcdIntSE(tmp, offset, 4))
//                .movePointLeft(2)
//                .setScale(2, RoundingMode.HALF_UP);
//            item.setV4(this.multiplyByMeterTransformationRatio(tr, p645DataType, v4));
//            offset += 4;
//            itemCount--;
//        }
//
//        return item;
//    }

    public List<Dlt645Tv> parseDlt645Data(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        if (p645DataType == null) {
            log.error("[{} {}] 参数错误, p645DataType不能为空", gwno, dno);
            throw new DcArgumentException("参数错误, p645DataType不能为空");
        } else if (P645DataType.CUR_POWER_ACTIVE_BLOCK == p645DataType
            || P645DataType.CUR_POWER_REACTIVE_BLOCK == p645DataType
            || P645DataType.CUR_POWER_APPARENT_BLOCK == p645DataType) {
            return this.parsePowerBlock(gwno, dno, buf, p645DataType); // 有功/无功/视在功率数据块
        } else if (P645DataType.CUR_POWER_ACTIVE == p645DataType
            || P645DataType.CUR_POWER_ACTIVE_A == p645DataType
            || P645DataType.CUR_POWER_ACTIVE_B == p645DataType
            || P645DataType.CUR_POWER_ACTIVE_C == p645DataType
            || P645DataType.CUR_POWER_REACTIVE == p645DataType
            || P645DataType.CUR_POWER_REACTIVE_A == p645DataType
            || P645DataType.CUR_POWER_REACTIVE_B == p645DataType
            || P645DataType.CUR_POWER_REACTIVE_C == p645DataType
            || P645DataType.CUR_POWER_APPARENT == p645DataType
            || P645DataType.CUR_POWER_APPARENT_A == p645DataType
            || P645DataType.CUR_POWER_APPARENT_B == p645DataType
            || P645DataType.CUR_POWER_APPARENT_C == p645DataType) {
            /**
             *总 /A/B/C三相有功功率(单个数据) <br />
             * 总 /A/B/C三相无功功率(单个数据) <br />
             * 总 /A/B/C三相视在功率(单个数据) <br />
             */
            return this.parsePower(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CUR_POWER_FACTOR_BLOCK == p645DataType) {
            return this.parsePowerFactorBlock(gwno, dno, buf); // 功率因数数据块
        } else if (P645DataType.CUR_POWER_FACTOR == p645DataType
            || P645DataType.CUR_POWER_FACTOR_A == p645DataType
            || P645DataType.CUR_POWER_FACTOR_B == p645DataType
            || P645DataType.CUR_POWER_FACTOR_C == p645DataType) {
            return this.parsePowerFactor(gwno, dno, buf, p645DataType); // 总/A/B/C三相功率因数(单个数据)
        } else if (P645DataType.CUR_VOLTAGE == p645DataType) {
            return this.parseVoltageAbc(gwno, dno, buf);  // ABC三相电压
        } else if (P645DataType.CUR_VOLTAGE_A == p645DataType
            || P645DataType.CUR_VOLTAGE_B == p645DataType
            || P645DataType.CUR_VOLTAGE_C == p645DataType) {
            return this.parseVoltageA(gwno, dno, buf, p645DataType); // A/B/C单相电压
        } else if (P645DataType.CUR_CURRENT == p645DataType) {
            return this.parseCurrentAbc(gwno, dno, buf);  // ABC三相电流
        } else if (P645DataType.CUR_CURRENT_A == p645DataType
            || P645DataType.CUR_CURRENT_B == p645DataType
            || P645DataType.CUR_CURRENT_C == p645DataType) {
            return this.parseCurrentA(gwno, dno, buf, p645DataType); // A/B/C单相电流
        } else if (P645DataType.POWER_COMBINED_BLOCK == p645DataType    // (当前)组合有功电能数据块
            || P645DataType.POWER_POSITIVE_BLOCK == p645DataType        // (当前)正向有功电能数据块
            || P645DataType.POWER_NEGATIVE_BLOCK == p645DataType         // (当前)反向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY01_BLOCK == p645DataType         // (上1结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY02_BLOCK == p645DataType         // (上2结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY03_BLOCK == p645DataType         // (上3结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY04_BLOCK == p645DataType         // (上4结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY05_BLOCK == p645DataType         // (上5结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY06_BLOCK == p645DataType         // (上6结算日)正向有功电能数据块
            || P645DataType.POWER_POSITIVE_DAY07_BLOCK == p645DataType         // (上7结算日)正向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY01_BLOCK == p645DataType         // (上1结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY02_BLOCK == p645DataType         // (上2结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY03_BLOCK == p645DataType         // (上3结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY04_BLOCK == p645DataType         // (上4结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY05_BLOCK == p645DataType         // (上5结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY06_BLOCK == p645DataType         // (上6结算日)反向有功电能数据块
            || P645DataType.POWER_NEGATIVE_DAY07_BLOCK == p645DataType         // (上7结算日)反向有功电能数据块
        ) {
            return this.parseKwhBlock(gwno, dno, buf, p645DataType); // (当前)组合/正向/反向有功电能数据块
        } else if (P645DataType.POWER_COMBINED == p645DataType
            || P645DataType.POWER_POSITIVE == p645DataType
            || P645DataType.POWER_NEGATIVE == p645DataType) {
            /**
             * (当前)正向有功总电能(单个数据) <br />
             * (当前)反向有功总电能(单个数据) <br />
             */
            return this.parseHwh(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CUR_POSITIVE_POWER_DEMAND == p645DataType    // (当前）正向有功总最大需量及发生时间
        ) {
            return this.parsePowerDemand(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CFG_TIME_ZONE_NUM == p645DataType       // 年时区数
            || P645DataType.CFG_TIME_BUCKET_TABLE_NUM == p645DataType     // 日时段表数
            || P645DataType.CFG_TIME_BUCKET_IN_DAY_NUM == p645DataType      // 日时段数(每日切换数)
            || P645DataType.CFG_PRICE_SCHEMA_NUM == p645DataType              // 费率数
        ) {
            return this.parseBcdInt8(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CFG_PARAM_3 == p645DataType           // 电表运行状态字3
            || P645DataType.CFG_PAZZWORD_L2 == p645DataType           // 2级密码
            || P645DataType.CFG_PAZZWORD_L4 == p645DataType           // 4级密码
        ) {
            return this.parseHexString(gwno, dno, buf, p645DataType);
        } else if (P645DataType.DATE_AND_WEEK == p645DataType   // 日期及星期(其中0代表星期天)， YYMMDDWW
            || P645DataType.TIME == p645DataType           // 时间，hhmmss
        ) {
            return this.parseHexStringReverse(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CFG_TIME_ZONE_ONE == p645DataType           // 第一套时区表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D1 == p645DataType      // 第一套第1日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D2 == p645DataType      // 第一套第2日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D3 == p645DataType      // 第一套第3日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D4 == p645DataType      // 第一套第4日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D5 == p645DataType      // 第一套第5日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D6 == p645DataType      // 第一套第6日时段表数据
            || P645DataType.CFG_TIME_BUCKET_ONE_D7 == p645DataType      // 第一套第7日时段表数据
        ) {
            return this.parseHexStringList3(gwno, dno, buf, p645DataType);
        } else if (P645DataType.CFG_TIME_ZONE_SWITCH_TIME == p645DataType  // 两套时区表切换时间
            || P645DataType.CFG_TIME_BUCKET_SWITCH_TIME == p645DataType    // 两套日时段表切换时间
        ) {
            return this.parseYYMMDDhhmm(gwno, dno, buf, p645DataType);  // 按YYMMDDhhmm格式解析时间
        } else {
            log.error("[{} {}] 暂不支持的DLT645地址类型: {}", gwno, dno, p645DataType.hex());
            return null;
        }


    }

    /**
     * 解析写指令的响应消息
     *
     * @return 0表示成功, 其他都表示失败
     */
    public long parseDlt645WriteRes(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        if (dataBytes == null) {
            log.warn("[{} {}] 写指令操作失败.", gwno, dno);
            return -1;
        }
        int offset = 0;
        final int itemLength = 3; // 数据长度
        return 0l;
    }

    private byte[] getDataBytes(String gwno, String dno, byte[] buf, P645DataType dataType) {
        int msgLength = parseCommonInfo(gwno, dno, buf, dataType);
        if (msgLength == 0) {
            return null;
        }

        int startPos = 0;   // 去除前导字节后，实际报文的开始位置，即0x68在的位置
        while (Dlt645Constants.BYTE_DLT645_HEAD == buf[startPos]) {   // 忽略前导字节 FEFE..
            startPos++;
        }
        int idx = startPos + 9;
        int length = buf[idx++] & 0xFF;
        log.debug("[{} {}] length = {}", gwno, dno, length);

        for (int i = 0; i < 4; i++) {   // 检查DI0～DI3
            if (buf[idx++] != dataType.getDataType()[i] + 0x33) {
                log.warn("[{} {}] 期望的DLT645地址不匹配. 期望地址= {}, 报文= {}",
                    gwno, dno, dataType.hex(), ByteUtils.bytesToHex(buf));
                throw new DcServiceException("期望的DLT645地址不匹配");
            }
        }
        // 循环减去0x33，获取原始值，此处包括DI0~3
        byte[] tmp = new byte[buf.length - idx - 2];    // 仅数据部分，不含尾部的crc和16
        System.arraycopy(buf, idx, tmp, 0, tmp.length);
        int i = 0;
        while (i < tmp.length) {
            tmp[i] = (byte) (tmp[i] - BYTE_0x33);
            i++;
        }
        log.debug("[{} {}] 减0x33后buf= {}",
            gwno, dno, ByteUtils.bytesToHex(tmp));
        return tmp;

    }

    /**
     * 解析三相/单相电压
     */
    private List<Dlt645Tv> parseVoltageAbc(String gwno, String dno, byte[] buf) {
        P645DataType p645DataType = P645DataType.CUR_VOLTAGE;
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);

        MeterAbcItem voltage = parseRtItemFloat21(dataBytes, 0, dno,
            null, p645DataType, false, 1);    // 相电压 0.1V
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(P645DataType.CUR_VOLTAGE_A)
            .setV(voltage.getV1());
        result.add(v1);

        Dlt645Tv v2 = new Dlt645Tv();
        v2.setAddr(P645DataType.CUR_VOLTAGE_B)
            .setV(voltage.getV2());
        result.add(v2);

        Dlt645Tv v3 = new Dlt645Tv();
        v3.setAddr(P645DataType.CUR_VOLTAGE_C)
            .setV(voltage.getV3());
        result.add(v3);
        return result;
    }

    /**
     * 解析A/b/c单相电压
     */
    private List<Dlt645Tv> parseVoltageA(String gwno, String dno, byte[] buf,
        P645DataType p645DataType
    ) {

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);

        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal21Value(dataBytes, 0));

        result.add(v1);

        return result;
    }


    /**
     * 电流ABC三相电流
     */
    public List<Dlt645Tv> parseCurrentAbc(String gwno, String dno, byte[] buf) {
        P645DataType p645DataType = P645DataType.CUR_CURRENT;

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);

        MeterAbcItem current = parseRtItemFloat33(dataBytes, 0, dno,
            null, p645DataType,
            false, 3);   // 相电流 0.001A
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(P645DataType.CUR_CURRENT_A)
            .setV(current.getV1());
        result.add(v1);

        Dlt645Tv v2 = new Dlt645Tv();
        v2.setAddr(P645DataType.CUR_CURRENT_B)
            .setV(current.getV2());
        result.add(v2);

        Dlt645Tv v3 = new Dlt645Tv();
        v3.setAddr(P645DataType.CUR_CURRENT_C)
            .setV(current.getV3());
        result.add(v3);
        return result;
    }

    /**
     * 解析A/b/c单相电流
     */
    private List<Dlt645Tv> parseCurrentA(String gwno, String dno, byte[] buf,
        P645DataType p645DataType
    ) {

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);

        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal33ValueWithSign(dataBytes, 0));
//        idx += 2;

        result.add(v1);

        return result;
    }

    /**
     * 有功/无功/视在功率数据块
     */
    public List<Dlt645Tv> parsePowerBlock(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        List<P645DataType> dataTypes = null;
        if (P645DataType.CUR_POWER_ACTIVE_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.CUR_POWER_ACTIVE, // 瞬时总有功功率
                P645DataType.CUR_POWER_ACTIVE_A,    // 瞬时A相有功功率
                P645DataType.CUR_POWER_ACTIVE_B,    // 瞬时B相有功功率
                P645DataType.CUR_POWER_ACTIVE_C     // 瞬时C相有功功率
            );
        } else if (P645DataType.CUR_POWER_REACTIVE_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.CUR_POWER_REACTIVE, // 瞬时无有功功率
                P645DataType.CUR_POWER_REACTIVE_A,    // 瞬时A相无功功率
                P645DataType.CUR_POWER_REACTIVE_B,    // 瞬时B相无功功率
                P645DataType.CUR_POWER_REACTIVE_C     // 瞬时C相无功功率
            );
        } else if (P645DataType.CUR_POWER_APPARENT_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.CUR_POWER_APPARENT, // 瞬时总视在功率
                P645DataType.CUR_POWER_APPARENT_A,    // 瞬时A相视在功率
                P645DataType.CUR_POWER_APPARENT_B,    // 瞬时B相视在功率
                P645DataType.CUR_POWER_APPARENT_C     // 瞬时C相视在功率
            );
        } else {
            log.error("[{} {}] 不支持的数据块地址. dataType= {}",
                gwno, dno, p645DataType);
            throw new DcArgumentException("不支持的数据块地址");
        }

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        int offset = 0;
        final int itemLength = 3; // 数据长度

        List<Dlt645Tv> result = new ArrayList<>();

        for (int i = 0; i < 4; i++) {
            Dlt645Tv v = new Dlt645Tv();
            BigDecimal decimal = parseDecimal34ValueWithSign(dataBytes, offset);
            offset += itemLength;
            v.setAddr(dataTypes.get(i))
                .setV(decimal);
            result.add(v);
        }
        return result;
    }

    /**
     * 总/A/B/C三相有功/无功/视在功率(单个数据)
     */
    public List<Dlt645Tv> parsePower(String gwno, String dno, byte[] buf,
        P645DataType p645DataType
    ) {

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);

        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal34ValueWithSign(dataBytes, 0));
        result.add(v1);
        return result;
    }

    /**
     * 功率因数数据块
     */
    public List<Dlt645Tv> parsePowerFactorBlock(String gwno, String dno, byte[] buf) {
        P645DataType p645DataType = P645DataType.CUR_POWER_FACTOR_BLOCK;

        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        int offset = 0;
        final int itemLength = 2; // 数据长度

        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v = new Dlt645Tv();

        BigDecimal decimal = parseDecimal23ValueWithSign(dataBytes, offset);
        offset += itemLength;
        v.setAddr(P645DataType.CUR_POWER_FACTOR)    // 总功率因数
            .setV(decimal);
        result.add(v);

        decimal = parseDecimal23ValueWithSign(dataBytes, offset);
        offset += itemLength;
        v = new Dlt645Tv();
        v.setAddr(P645DataType.CUR_POWER_FACTOR_A)    // A相功率因数
            .setV(decimal);
        result.add(v);

        decimal = parseDecimal23ValueWithSign(dataBytes, offset);
        offset += itemLength;
        v = new Dlt645Tv();
        v.setAddr(P645DataType.CUR_POWER_FACTOR_B)    // B相功率因数
            .setV(decimal);
        result.add(v);

        decimal = parseDecimal23ValueWithSign(dataBytes, offset);
        offset += itemLength;
        v = new Dlt645Tv();
        v.setAddr(P645DataType.CUR_POWER_FACTOR_C)    // C相功率因数
            .setV(decimal);
        result.add(v);

        return result;
    }

    /**
     * 总/A/B/C三相功率因数(单个数据)
     */
    public List<Dlt645Tv> parsePowerFactor(String gwno, String dno, byte[] buf,
        P645DataType p645DataType
    ) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal23ValueWithSign(dataBytes, 0));
        result.add(v1);
        return result;
    }

    /**
     * (当前)组合有功电能数据块 <br /> (当前)正向有功电能数据块 <br /> (当前)反向有功电能数据块 <br />
     */
    public List<Dlt645Tv> parseKwhBlock(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        List<P645DataType> dataTypes = null;
        if (P645DataType.CUR_POWER_ACTIVE_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_COMBINED_BLOCK, // 组合有功电能数据块
                P645DataType.POWER_COMBINED,    // (当前)组合有功总电能
                P645DataType.POWER_COMBINED_01,    // (当前)组合有功费率1电能
                P645DataType.POWER_COMBINED_02,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_03,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_04,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_05,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_06,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_07,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_08,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_09,    // (当前)组合有功费率N电能
                P645DataType.POWER_COMBINED_10    // (当前)组合有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE, // (当前)正向有功总电能
                P645DataType.POWER_POSITIVE_01,    // (当前)正向有功费率1电能
                P645DataType.POWER_POSITIVE_02,    // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_03,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_04,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_05,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_06,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_07,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_08,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_09,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_10,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_11,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_12,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_13,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_14,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_15,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_16,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_17,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_18,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_19,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_20,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_21,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_22,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_23,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_24,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_25,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_26,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_27,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_28,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_29,     // (当前)正向有功费率N电能
                P645DataType.POWER_POSITIVE_30     // (当前)正向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE, // (当前)反向有功总电能
                P645DataType.POWER_NEGATIVE_01,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_02,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_03,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_04,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_05,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_06,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_07,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_08,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_09,    // (当前)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_10    // (当前)反向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY01_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY01, // (上1结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY01_01,    // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_02,    // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_03,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_04,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_05,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_06,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_07,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_08,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_09,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_10,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_11,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_12,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_13,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_14,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_15,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_16,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_17,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_18,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_19,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_20,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_21,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_22,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_23,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_24,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_25,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_26,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_27,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_28,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_29,     // (上1结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY01_30     // (上1结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY02_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY02, // (上2结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY02_01,    // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_02,    // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_03,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_04,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_05,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_06,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_07,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_08,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_09,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_10,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_11,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_12,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_13,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_14,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_15,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_16,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_17,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_18,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_19,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_20,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_21,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_22,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_23,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_24,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_25,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_26,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_27,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_28,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_29,     // (上2结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY02_30     // (上2结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY03_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY03, // (上3结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY03_01,    // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_02,    // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_03,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_04,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_05,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_06,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_07,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_08,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_09,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_10,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_11,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_12,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_13,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_14,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_15,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_16,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_17,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_18,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_19,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_20,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_21,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_22,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_23,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_24,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_25,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_26,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_27,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_28,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_29,     // (上3结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY03_30     // (上3结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY04_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY04, // (上4结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY04_01,    // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_02,    // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_03,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_04,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_05,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_06,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_07,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_08,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_09,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_10,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_11,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_12,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_13,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_14,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_15,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_16,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_17,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_18,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_19,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_20,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_21,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_22,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_23,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_24,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_25,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_26,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_27,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_28,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_29,     // (上4结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY04_30     // (上4结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY05_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY05, // (上5结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY05_01,    // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_02,    // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_03,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_04,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_05,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_06,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_07,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_08,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_09,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_10,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_11,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_12,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_13,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_14,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_15,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_16,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_17,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_18,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_19,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_20,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_21,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_22,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_23,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_24,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_25,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_26,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_27,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_28,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_29,     // (上5结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY05_30     // (上5结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY06_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY06, // (上6结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY06_01,    // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_02,    // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_03,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_04,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_05,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_06,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_07,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_08,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_09,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_10,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_11,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_12,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_13,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_14,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_15,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_16,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_17,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_18,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_19,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_20,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_21,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_22,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_23,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_24,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_25,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_26,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_27,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_28,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_29,     // (上6结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY06_30     // (上6结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_POSITIVE_DAY07_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_POSITIVE_DAY07, // (上7结算日)正向有功总电能
                P645DataType.POWER_POSITIVE_DAY07_01,    // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_02,    // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_03,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_04,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_05,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_06,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_07,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_08,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_09,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_10,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_11,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_12,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_13,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_14,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_15,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_16,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_17,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_18,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_19,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_20,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_21,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_22,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_23,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_24,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_25,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_26,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_27,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_28,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_29,     // (上7结算日)正向有功费率N电能
                P645DataType.POWER_POSITIVE_DAY07_30     // (上7结算日)正向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY01_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY01, // (上1结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY01_01,    // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_02,    // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_03,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_04,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_05,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_06,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_07,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_08,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_09,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_10,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_11,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_12,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_13,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_14,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_15,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_16,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_17,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_18,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_19,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_20,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_21,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_22,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_23,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_24,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_25,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_26,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_27,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_28,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_29,     // (上1结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY01_30     // (上1结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY02_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY02, // (上2结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY02_01,    // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_02,    // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_03,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_04,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_05,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_06,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_07,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_08,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_09,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_10,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_11,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_12,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_13,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_14,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_15,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_16,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_17,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_18,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_19,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_20,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_21,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_22,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_23,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_24,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_25,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_26,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_27,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_28,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_29,     // (上2结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY02_30     // (上2结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY03_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY03, // (上3结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY03_01,    // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_02,    // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_03,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_04,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_05,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_06,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_07,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_08,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_09,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_10,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_11,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_12,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_13,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_14,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_15,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_16,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_17,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_18,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_19,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_20,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_21,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_22,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_23,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_24,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_25,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_26,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_27,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_28,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_29,     // (上3结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY03_30     // (上3结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY04_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY04, // (上4结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY04_01,    // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_02,    // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_03,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_04,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_05,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_06,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_07,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_08,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_09,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_10,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_11,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_12,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_13,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_14,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_15,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_16,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_17,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_18,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_19,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_20,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_21,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_22,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_23,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_24,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_25,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_26,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_27,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_28,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_29,     // (上4结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY04_30     // (上4结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY05_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY05, // (上5结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY05_01,    // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_02,    // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_03,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_04,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_05,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_06,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_07,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_08,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_09,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_10,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_11,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_12,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_13,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_14,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_15,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_16,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_17,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_18,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_19,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_20,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_21,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_22,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_23,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_24,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_25,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_26,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_27,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_28,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_29,     // (上5结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY05_30     // (上5结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY06_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY06, // (上6结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY06_01,    // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_02,    // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_03,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_04,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_05,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_06,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_07,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_08,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_09,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_10,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_11,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_12,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_13,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_14,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_15,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_16,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_17,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_18,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_19,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_20,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_21,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_22,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_23,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_24,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_25,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_26,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_27,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_28,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_29,     // (上6结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY06_30     // (上6结算日)反向有功费率N电能
            );
        } else if (P645DataType.POWER_NEGATIVE_DAY07_BLOCK == p645DataType) {
            dataTypes = List.of(P645DataType.POWER_NEGATIVE_DAY07, // (上7结算日)反向有功总电能
                P645DataType.POWER_NEGATIVE_DAY07_01,    // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_02,    // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_03,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_04,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_05,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_06,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_07,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_08,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_09,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_10,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_11,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_12,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_13,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_14,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_15,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_16,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_17,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_18,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_19,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_20,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_21,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_22,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_23,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_24,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_25,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_26,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_27,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_28,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_29,     // (上7结算日)反向有功费率N电能
                P645DataType.POWER_NEGATIVE_DAY07_30     // (上7结算日)反向有功费率N电能
            );
        } else {
            log.error("[{} {}] 不支持的数据块地址. dataType= {}",
                gwno, dno, p645DataType);
            throw new DcArgumentException("不支持的数据块地址");
        }
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        int offset = 0;
        final int itemLength = 4; // 数据长度

        List<Dlt645Tv> result = new ArrayList<>();
        for (int i = 0; i < dataTypes.size() && i < (dataBytes.length / itemLength); i++) {
            Dlt645Tv v = new Dlt645Tv();
            BigDecimal decimal = parseDecimal42Value(dataBytes, offset);
            offset += itemLength;
            v.setAddr(dataTypes.get(i))
                .setV(decimal);
            result.add(v);
        }
        return result;
    }

    /**
     * (当前)正向有功总电能(单个数据) <br /> (当前)反向有功总电能(单个数据) <br />
     */
    public List<Dlt645Tv> parseHwh(String gwno, String dno, byte[] buf,
        P645DataType p645DataType
    ) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal42Value(dataBytes, 0));
        result.add(v1);
        return result;
    }

//    /**
//     * 解析功率因数数据块
//     *
//     * @param buf
//     * @param dno
//     * @return
//     */
//    public MeterAbcData parsePowerFactor(String tid, String dno, byte[] buf,
//        MeterTransformationRatio tr,
//        P645DataType p645DataType) {
//        int msgLength = parseCommonInfo(tid, dno, buf, p645DataType);
//        if (msgLength == 0) {
//            return null;
//        }
//        MeterAbcData info = new MeterAbcData();
//        int idx = 9;
//        int length = buf[idx++] & 0xFF;
//        log.debug("length = {}", length);
//
//        // 循环减去0x33，获取原始值，此处包括DI0~3
//        byte[] tmp = new byte[buf.length];
//        System.arraycopy(buf, 0, tmp, 0, buf.length);
//        int i = idx;
//        while (i < idx + length) {
//            tmp[i] = (byte) (tmp[i] - BYTE_0x33);
//            i++;
//        }
//
//        idx += 4; // skip DI0~4
//
//        info.setPf(parseRtItemFloat23(tmp, idx, dno,
//            tr, p645DataType,
//            true, 3));
//
//        return info;
//    }

//    public MeterAbcItem parseRtDataPowerDlt645(String tid, byte[] buf, String dno,
//        MeterTransformationRatio tr,
//        P645DataType p645DataType) {
//        int msgLen = parseCommonInfo(tid, dno, buf, p645DataType);
//        if (msgLen == 0) {
//            return null;
//        }
//        log.debug("[{}] {} msgLen = {}", tid, dno, msgLen);
//        int idx = 14;    // 从数据部分开始
//
//        MeterAbcItem item = new MeterAbcItem();
//        // 循环减去0x33，获取原始值，此处不包括DI0~3
//        byte[] tmp = new byte[msgLen];
//        System.arraycopy(buf, idx, tmp, 0, msgLen);
//        int i = 0;
//        while (i < tmp.length) {
//            tmp[i] = (byte) (tmp[i] - Dlt645Constants.BYTE_DLT645_DATA_ADD);
//            i++;
//        }
//
//        final int itemLength = 3; // 数据长度
//        int count = msgLen / itemLength;
//        int offset = 0;
//
//        BigDecimal decimal = parseDecimal34ValueWithSign(tmp, offset);
//        item.setTotal(this.multiplyByMeterTransformationRatio(tr, p645DataType, decimal));
//        offset += itemLength;
//        count--;
//
//        if (count > 0) {
//            decimal = parseDecimal34ValueWithSign(tmp, offset);
//            item.setV1(this.multiplyByMeterTransformationRatio(tr, p645DataType, decimal));
//            offset += itemLength;
//            count--;
//        }
//
//        if (count > 0) {
//            decimal = parseDecimal34ValueWithSign(tmp, offset);
//            item.setV2(this.multiplyByMeterTransformationRatio(tr, p645DataType, decimal));
//            offset += itemLength;
//            count--;
//        }
//
//        if (count > 0) {
//            decimal = parseDecimal34ValueWithSign(tmp, offset);
//            item.setV3(this.multiplyByMeterTransformationRatio(tr, p645DataType, decimal));
//            offset += itemLength;
//            count--;
//        }
//
//        return item;
//    }

    /**
     * 解析最大需量
     */
    public List<Dlt645Tv> parsePowerDemand(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(parseDecimal34Value(dataBytes, 0));
        result.add(v1);

        int idx = 3;

        // 发生时间解析
        int minute = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int hour = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int day = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int month = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int year = 2000 + ByteUtils.byte2BcdInt(dataBytes[idx++]);
        v1 = new Dlt645Tv();
        if (P645DataType.CUR_POSITIVE_POWER_DEMAND == p645DataType) {
            v1.setAddr(P645DataType.CUR_POSITIVE_POWER_DEMAND_TIME);
        }
        v1.setV(LocalDateTime.of(year, month, day, hour, minute));
        result.add(v1);
        return result;
//
//
//        int msgLen = parseCommonInfo(tid, dno, buf, p645DataType);
//        if (msgLen == 0) {
//            return false;
//        }
//        log.debug("[{}] {} msgLen = {}", tid, dno, msgLen);
//        int idx = 14;    // 从数据部分开始
//
//        // 循环减去0x33，获取原始值，此处不包括DI0~3
//        byte[] tmp = new byte[msgLen];
//        System.arraycopy(buf, idx, tmp, 0, msgLen);
//        int i = 0;
//        while (i < tmp.length) {
//            tmp[i] = (byte) (tmp[i] - Dlt645Constants.BYTE_DLT645_DATA_ADD);
//            i++;
//        }
//
//        int offset = 0;
//        // 需量解析
//        BigDecimal decimal = parseDecimal34Value(tmp, offset);
//        BigDecimal result = (this.multiplyByMeterTransformationRatio(tr, p645DataType, decimal));
////        if (P645DataType.CUR_ACTIVE_POWER_DEMAND.equals(p645DataType)) {
////            rtData.setActiveDemand(result);
////            meterRtData.getSensors().get(MeterKvCode.MAX_DEMAND_RT).setV(result);
////        } else if (P645DataType.TOTAL_POSITIVE_ACTIVE_POWER_MAXIMUM_DEMAND.equals(p645DataType)) {
////            rtData.setActiveDemandMax(result);
////            meterRtData.getSensors().get(MeterKvCode.MAX_DEMAND_LAST).setV(result);
////        }
//        offset = offset + 3;
//
//        if (offset < msgLen) {
//            // 发生时间解析
//            int minute = ByteUtils.byte2BcdInt(tmp[offset++]);
//            int hour = ByteUtils.byte2BcdInt(tmp[offset++]);
//            int day = ByteUtils.byte2BcdInt(tmp[offset++]);
//            int month = ByteUtils.byte2BcdInt(tmp[offset++]);
//            int year = 2000 + ByteUtils.byte2BcdInt(tmp[offset++]);
//            rtData.setActiveDemandTime(LocalDateTime.of(year, month, day, hour, minute));
//        }
//
//        return true;
    }

    /**
     * 解析一字节BCD码,10进制
     */
    public List<Dlt645Tv> parseBcdInt8(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(ByteUtils.byte2BcdInt(dataBytes[0]));
        result.add(v1);

        return result;
    }

    /**
     * 返回16进制hex编码字符串
     */
    private List<Dlt645Tv> parseHexString(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(ByteUtils.bytesToHex(dataBytes));
        result.add(v1);
        return result;
    }

    /**
     * 返回16进制hex编码并反转字符串
     */
    private List<Dlt645Tv> parseHexStringReverse(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(ByteUtils.bytesToHex(ByteUtils.reverseBytes(dataBytes)));
        result.add(v1);
        return result;
    }

    /**
     * 按16进制解析，并以3个字节为一组
     */
    private List<Dlt645Tv> parseHexStringList3(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        int num = dataBytes.length / 3;
        List<String> resultList = new ArrayList<>();

        for (int i = 0; i < num; i++) {
            String hex = ByteUtils.bytesToHex(ByteUtils.reverseBytes(dataBytes, i * 3, 3));
            resultList.add(hex);
        }
        Dlt645Tv v1 = new Dlt645Tv();
        v1.setAddr(p645DataType)
            .setV(resultList);
        result.add(v1);
        return result;
    }

    /**
     * 按 YYMMDDhhmm 格式解析时间字段
     */
    private List<Dlt645Tv> parseYYMMDDhhmm(String gwno, String dno, byte[] buf,
        P645DataType p645DataType) {
        byte[] dataBytes = getDataBytes(gwno, dno, buf, p645DataType);
        List<Dlt645Tv> result = new ArrayList<>();
        Dlt645Tv v1 = new Dlt645Tv();
        int idx = 0;
        int minute = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int hour = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int day = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int month = ByteUtils.byte2BcdInt(dataBytes[idx++]);
        int year = 2000 + ByteUtils.byte2BcdInt(dataBytes[idx++]);

        v1.setAddr(p645DataType)
            .setV(LocalDateTime.of(year, month, day, hour, minute));
        result.add(v1);
        return result;
    }


    private MeterAbcItem parseRtItemFloat21(byte[] buf,
        int idx,
        String dno,
        MeterTransformationRatio tr,
        P645DataType p645DataType,
        boolean hasTotal,    // 是否有total字段
        int decimal  // 小数位数
    ) {
        MeterAbcItem item = new MeterAbcItem();

        BigDecimal v1 = parseDecimal21Value(buf, idx);
        item.setV1(this.multiplyByMeterTransformationRatio(tr, p645DataType, v1));
        idx += 2;

        BigDecimal v2 = parseDecimal21Value(buf, idx);
        item.setV2(this.multiplyByMeterTransformationRatio(tr, p645DataType, v2));
        idx += 2;

        BigDecimal v3 = parseDecimal21Value(buf, idx);
        item.setV3(this.multiplyByMeterTransformationRatio(tr, p645DataType, v3));
        idx += 2;

        return item;
    }

    private MeterAbcItem parseRtItemFloat23(byte[] buf,
        int idx,
        String dno,
        MeterTransformationRatio tr,
        P645DataType p645DataType,
        boolean hasTotal,    // 是否有total字段
        int decimal  // 小数位数
    ) {
        MeterAbcItem item = new MeterAbcItem();

        if (hasTotal) {
            BigDecimal total = parseDecimal23ValueWithSign(buf, idx);
            item.setTotal(this.multiplyByMeterTransformationRatio(tr, p645DataType, total));
            idx += 2;
        }

        BigDecimal v1 = parseDecimal23ValueWithSign(buf, idx);
        item.setV1(this.multiplyByMeterTransformationRatio(tr, p645DataType, v1));
        idx += 2;

        BigDecimal v2 = parseDecimal23ValueWithSign(buf, idx);
        item.setV2(this.multiplyByMeterTransformationRatio(tr, p645DataType, v2));
        idx += 2;

        BigDecimal v3 = parseDecimal23ValueWithSign(buf, idx);
        item.setV3(this.multiplyByMeterTransformationRatio(tr, p645DataType, v3));
        idx += 2;

        return item;
    }

    private MeterAbcItem parseRtItemFloat33(byte[] buf,
        int idx,
        String dno,
        MeterTransformationRatio tr,
        P645DataType p645DataType,
        boolean hasTotal,    // 是否有total字段
        int decimal  // 小数位数
    ) {
        MeterAbcItem item = new MeterAbcItem();

        BigDecimal v1 = parseDecimal33ValueWithSign(buf, idx);
        item.setV1(this.multiplyByMeterTransformationRatio(tr, p645DataType, v1));
        idx += 3;

        BigDecimal v2 = parseDecimal33ValueWithSign(buf, idx);
        item.setV2(this.multiplyByMeterTransformationRatio(tr, p645DataType, v2));
        idx += 3;

        BigDecimal v3 = parseDecimal33ValueWithSign(buf, idx);
        item.setV3(this.multiplyByMeterTransformationRatio(tr, p645DataType, v3));
        idx += 3;

        return item;
    }

    /**
     * 2字节，1位小数
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal21Value(byte[] bufIn, int idx) {
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(c * 10 + d / 10).add(BigDecimal.valueOf(d % 10).movePointLeft(1));
    }

    /**
     * 2字节，3位小数 最高位表示方向，0正，1负
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal23ValueWithSign(byte[] bufIn, int idx) {
        int sign = getSign(bufIn[idx + 1]);
        int c = ByteUtils.byte2BcdIntSkipSign(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        if (sign < 0) {
            return BigDecimal.valueOf(-c * 100L).subtract(BigDecimal.valueOf(d)).movePointLeft(3);
        }
        return BigDecimal.valueOf(c * 100L).add(BigDecimal.valueOf(d)).movePointLeft(3);
    }

    /**
     * 3字节，3位小数
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal33Value(byte[] bufIn, int idx) {
        int b = ByteUtils.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(b * 10 + c / 10).add(BigDecimal.valueOf(c % 10).movePointLeft(1))
            .add(BigDecimal.valueOf(d).movePointLeft(3));
    }

    /**
     * 3字节，3位小数 最高位表示方向，0正，1负
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal33ValueWithSign(byte[] bufIn, int idx) {
        int sign = getSign(bufIn[idx + 2]);
        int b = ByteUtils.byte2BcdIntSkipSign(bufIn[idx + 2]);
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        if (sign < 0) {
            return BigDecimal.valueOf(-b * 10 - c / 10)
                .subtract(BigDecimal.valueOf(c % 10).movePointLeft(1))
                .subtract(BigDecimal.valueOf(d).movePointLeft(3));
        }
        return BigDecimal.valueOf(b * 10 + c / 10).add(BigDecimal.valueOf(c % 10).movePointLeft(1))
            .add(BigDecimal.valueOf(d).movePointLeft(3));
    }

    /**
     * <p>3字节，4位小数</p>
     * 无符号
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal34Value(byte[] bufIn, int idx) {
        int b = ByteUtils.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        return BigDecimal.valueOf(b).add(DecimalUtils.divide100(c))
            .add(BigDecimal.valueOf(d).movePointLeft(4));
    }

    /**
     * <p>4字节，2位小数</p>
     * 无符号
     */
    public BigDecimal parseDecimal42Value(byte[] bufIn, int idx) {
        int a = ByteUtils.byte2BcdInt(bufIn[idx + 3]);
        int b = ByteUtils.byte2BcdInt(bufIn[idx + 2]);
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        return DecimalUtils.multiply10000(BigDecimal.valueOf(a))
            .add(DecimalUtils.multiply100(BigDecimal.valueOf(b)))
            .add(BigDecimal.valueOf(c))
            .add(BigDecimal.valueOf(d).movePointLeft(2));
    }

    /**
     * <p>3字节，4位小数</p>
     * 最高位表示方向，0正，1负
     *
     * @param bufIn
     * @param idx
     * @return
     */
    public BigDecimal parseDecimal34ValueWithSign(byte[] bufIn, int idx) {
        int sign = getSign(bufIn[idx + 2]);
        int b = ByteUtils.byte2BcdIntSkipSign(bufIn[idx + 2]);
        int c = ByteUtils.byte2BcdInt(bufIn[idx + 1]);
        int d = ByteUtils.byte2BcdInt(bufIn[idx]);
        if (sign < 0) {
            return BigDecimal.valueOf(-b).subtract(DecimalUtils.divide100(c))
                .subtract(BigDecimal.valueOf(d).movePointLeft(4));
        }
        return BigDecimal.valueOf(b).add(DecimalUtils.divide100(c))
            .add(BigDecimal.valueOf(d).movePointLeft(4));
    }

    /**
     * 解析符号位
     *
     * @param in
     * @return
     */
    private int getSign(byte in) {
        if ((0x80 & in) > 0) {
            // 符号位为负
            return -1;
        } else {
            return 1;
        }
    }

    public Pair<P645DataType, byte[]> analysisDataType(String tid, String dno, byte[] buf) {
        if (buf == null) {
            throw new DcServiceException("获取设备信息失败");
        }
        int idx = 0;
        if (buf[idx] == Dlt645Constants.BYTE_DLT645_HEAD) {
            // 无需关注请求帧
            // TODO: 4/13/2024 WZFIX 可能粘包，待修正
            return null;
        }
        if (buf[idx] != Dlt645Constants.BYTE_DLT645_START) {
            throw new DcServiceException("获取设备信息失败..");
        }
        idx += 8;

        if ((byte) buf[idx++] != Dlt645Constants.BYTE_READ_RESULT_91) {
            log.warn("[{}] dno = {} 解析DLT645电表消息失败, 报文 = {}", tid, dno,
                ByteUtils.bytesToHex(buf));
            return null;
        }
        idx++; // 数据域长度

        ByteArrayOutputStream bObj = new ByteArrayOutputStream();
        bObj.write((buf[idx++] - Dlt645Constants.BYTE_DLT645_DATA_ADD));
        bObj.write((buf[idx++] - Dlt645Constants.BYTE_DLT645_DATA_ADD));
        bObj.write((buf[idx++] - Dlt645Constants.BYTE_DLT645_DATA_ADD));
        bObj.write((buf[idx++] - Dlt645Constants.BYTE_DLT645_DATA_ADD));
        byte[] byteArray = bObj.toByteArray();
        return Pair.of(P645DataType.valueOf(byteArray), byteArray);
    }

    public int parseCommonInfo(String gwno, String dno, byte[] buf, P645DataType dataType) {
        int idx = 0;
        int startPos = 0;   // 去除前导字节后，实际报文的开始位置，即0x68在的位置
        if (buf == null) {
            throw new DcServiceException("获取设备信息失败");
        } else {
//            while(idx < READ_CMD_DLT645_HEADER.length) {
//                if(READ_CMD_DLT645_HEADER[idx] != buf[idx]) {
//                    throw new DcServiceException("获取设备信息失败.");
//                }
//                idx++;
//            }
            while (Dlt645Constants.BYTE_DLT645_HEAD == buf[startPos]) {   // 忽略前导字节 FEFE..
                startPos++;
            }
            idx = startPos;
            if (buf[idx] != Dlt645Constants.BYTE_DLT645_START) {
                throw new DcServiceException("获取设备信息失败..");
            }
        }

        idx += 8;

        /**
         * 第8个字节为‘控制码’. 0x91 正常应答，无后续数据帧；0xB1 正常应答，且有后续数据帧； 0xD1 异常应答
         */
        byte ctrlCode = buf[idx++];
//        boolean success = false;
        if (ctrlCode == Dlt645Constants.BYTE_READ_RESULT_D1) {
            log.warn("[{} {}] 收到读DLT645电表异常应答消息, 报文= {}", gwno, dno,
                ByteUtils.bytesToHex(buf));
            idx++;  // 跳过长度
            int errorCode = buf[idx++] & 0xFF;  // 错误码
            log.warn("[{} {}] 收到读DLT645电表异常应答消息, 错误码= {}", gwno, dno,
                errorCode);
            throw new DcServiceException("收到电表异常应答");
        } else if (ctrlCode == Dlt645Constants.BYTE_WRITE_RESULT_FAIL) {
            log.warn("[{} {}] 收到写DLT645电表异常应答消息, 报文= {}", gwno, dno,
                ByteUtils.bytesToHex(buf));
//            idx++;  // 跳过长度
            int errorCode = buf[idx + 1] & 0xFF;  // 错误码
            log.warn("[{} {}] 收到写DLT645电表异常应答消息, 错误码= {}", gwno, dno,
                errorCode);
//            throw new DcServiceException("收到电表异常应答");
            return 0;
        } else if (ctrlCode == Dlt645Constants.BYTE_WRITE_RESULT_SUCCESS) {
            // 写指令下发返回成功的响应消息
            log.error(
                "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx success ###############################");    // 用error级，方便搜索日志
//            success = true;
        } else if (ctrlCode == Dlt645Constants.BYTE_READ_RESULT_91) {
            // 读数据指令下发返回的成功响应消息
//            success = true;
        } else {
            log.warn("[{} {}] 解析DLT645电表消息失败, 报文 = {}", gwno, dno,
                ByteUtils.bytesToHex(buf));
            return 0;
        }

        final int len = buf.length;

        int msgLength = (buf[idx++] & 0xFF) - Dlt645Constants.BYTE_DLT645_LENGTH_ADD;
        // 12 为: 0x68 + 6字节表号 + 0x68 + 1字节控制码 + 1字节长度 + 末尾的CS和0x16
        int expectBufLen = startPos + 12 + msgLength;   //  期望的报文总长度。 从前导字节FE到CS和0x16的结束.
        if (expectBufLen > buf.length) {
            log.info(
                "[{} {}] 接收到的数据报文不完整,还需等待更多的上报报文. expectBufLen= {}, resBuf.length= {}",
                gwno, dno, expectBufLen, buf.length);
            throw new DcNeedMoreDataException();
        }
//        log.debug("[{}] {} length = {}", tid, dno, msgLength);
//        if(success) {
        for (int i = 0; i < dataType.getDataType().length; i++) {
            if (dataType.getDataType()[i] != (buf[idx++]
                - Dlt645Constants.BYTE_DLT645_DATA_ADD)) {
                log.warn("[{} {}] 电表返回数据地址与期望不符. i={}, 期望地址 = {}/{}",
                    gwno, dno, i,
                    dataType.getNameTag(),
                    ByteUtils.bytesToHex(dataType.getDataType()));
                return 0;
            }
        }
//        }

        final int crc = buf[len - 2] & 0xFF;

//        buf[0] = 0;
//        buf[1] = 0;
//        buf[2] = 0;
//        buf[3] = 0;
        buf[len - 1] = 0;
        buf[len - 2] = 0;

        int cs = 0;
        for (int i = 0; i < buf.length - startPos - 2; i++) {
            cs += (buf[i + startPos] & 0xFF);
        }
        if (((cs % 256) & 0xFF) != crc) {
            log.error("[{} {}] CS校验不匹配. 报文CS = {}, 计算CS = {}",
                gwno, dno, crc, cs % 256);
            throw new DcServiceException("CRC校验不匹配");
        }
        return msgLength;
    }

    /**
     * 乘以互感器变比
     *
     * @param tr
     * @param p645DataType
     * @param value
     * @return
     */
    public BigDecimal multiplyByMeterTransformationRatio(MeterTransformationRatio tr,
        P645DataType p645DataType,
        BigDecimal value) {
        if (value == null || DecimalUtils.isZero(value)) {
            return value;
        }
        if (tr == null) {
            return value;
        }
        BigDecimal ctr = Optional.of(tr.getCtr()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
        BigDecimal vtr = Optional.of(tr.getVtr()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);

        BigDecimal result = value;
        switch (p645DataType.getFieldType()) {
            case CURRENT:
                result = result.multiply(ctr);
                break;
            case VOLTAGE:
                result = result.multiply(vtr);
                break;
            case POWER:
                result = result.multiply(ctr).multiply(vtr);
                break;
            case KWH:
                result = result.multiply(ctr).multiply(vtr);
                break;
            default:
                break;
        }

        return result;
    }
}
