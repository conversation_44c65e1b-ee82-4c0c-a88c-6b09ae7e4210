package com.ht.iot.collection.handler;

import com.cdz360.iot.model.modbus.type.P645DataType;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

// 其他import同上...

@MappedTypes(List.class)
public class P645DataTypeTypeHandler extends AbstractJsonTypeHandler<List<P645DataType>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<P645DataType> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting P645DataType list to JSON", e);
        }
    }

    protected List<P645DataType> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<P645DataType>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing P645DataType JSON: " + json, e);
        }
    }
}