package com.ht.iot.collection.model.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateDlt645DeviceInfoDto extends UpdateDeviceInfoDto {

    /**
     * hex格式的电表表号，6字节
     */
    private String meterNo;

    /**
     * 要采集的DLT645地址列表, HEX格式4字节, DI3,DI2,DI1,DI0
     */
    private List<String> addrs;

    /**
     * 要映射的DLT645值
     */
    private List<Dlt645TvCfg> tvs;



    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
