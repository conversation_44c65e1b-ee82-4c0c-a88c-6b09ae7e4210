package com.ht.iot.collection.south.biz;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.ht.iot.collection.cfg.MqttCfg;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Timer;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service
public class MqttService {

    private static boolean mqttConnectFlag = false;
    private static Long idx = 0L;
    @Autowired
    private Tracer tracer;
    @Autowired
    private MqttCfg mqttCfg;
    @Autowired
    private MqttHandler mqttHandler;
    //    @Autowired
//    private IotCmdHandler iotCmdHandler;
    //    @Autowired
//    private IotUpService iotUpService;
    private ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newCachedThreadPool();

    private MqttClient mqttClient = null;
    private MqttConnectOptions connOpts = null;
    private Timer timerMQTT = null;

    public void printMqttConnectFlag() {
        log.debug("print mqttConnectFlag: {}", mqttConnectFlag);
    }

    @PostConstruct
    public void init() {
        this.updateProps("", "", mqttCfg);
    }

    public synchronized boolean updateProps(String tid, String gwno, MqttCfg props) {
        try {

            Assert.notNull(props, "参数不能为空");
            log.debug("[{}] MqttServiceImpl updateProps. mqttConnectFlag: {}", tid,
                mqttConnectFlag);
            if (mqttConnectFlag) {
                return true;
            }

            return this.subscribe(tid, gwno, props);
        } catch (Exception e) {
            log.error("[{}] 修改MQTT配置失败。 error: {}", tid, e.getMessage(), e);
            return false;
        }
    }


    public boolean subscribe(String tid, String gwno, MqttCfg props) {
        log.info("[{}] MqttServiceImpl subscribe. props: {}", tid, props);
        try {
            boolean isNewClient = mqttClient == null
                || !StringUtils.equals(mqttClient.getServerURI(), props.getUrl())
                || !StringUtils.equals(mqttClient.getClientId(), props.getClientId());
            if (isNewClient) {
                MemoryPersistence persistence = new MemoryPersistence();
                mqttClient = new MqttClient(props.getUrl(), props.getClientId(), persistence);
            }
            if (connOpts == null) {
                connOpts = new MqttConnectOptions();
            }
            connOpts.setUserName(props.getUsername());
            connOpts.setPassword(props.getPassword().toCharArray());
            connOpts.setCleanSession(false);    // false: mqtt断线重连后不需要重新subscribe
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            connOpts.setKeepAliveInterval(60);
            connOpts.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);
            connOpts.setAutomaticReconnect(true);
            connOpts.setConnectionTimeout(30);
//            connOpts.setWill(props.getLwt(), gwno.getBytes(StandardCharsets.UTF_8), 1, false);

            log.info("[{}] Connecting to mqtt broker: {}, topic: {}", tid, props.getUrl(),
                props.getUpTopic());
            mqttClient.connect(connOpts);
            mqttConnectFlag = true;
            log.info("[{}] MQTT connected", tid);

            mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("[{}] MQTT连接意外断开。 msg: {}", tid, cause.getMessage(), cause);
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    log.info("[{}] topic: {}, message: {}", tid, topic,
                        JsonUtils.toJsonString(message));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    log.info("[{}] token: {}", tid, token.toString());
                }
            });

            mqttClient.subscribe(props.getUpTopic(), (tp, msg) -> {
                processMessage(tp, msg);
            });

        } catch (MqttException me) {
            mqttConnectFlag = false;
            log.error("[{}] reason: {}, loc: {}, cause: {}, msg: {} ", tid,
                me.getReasonCode(), me.getLocalizedMessage(), me.getCause(), me.getMessage(), me);
        }
        return mqttConnectFlag;
    }

    private void processMessage(String topic, MqttMessage message) {
        //sampleClient.subscribe(topic, (tp, msg) -> {
        Span span = this.tracer.nextSpan().name("MqttService");
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            log.info("处理 mqtt 消息 topic= {}, msg= {}", topic, JsonUtils.toJsonString(message));
            LocalDateTime ts = LocalDateTime.now();
            Long idxTmp = idx++;

//        executor.submit(() -> {
//            log.debug("[{}] idx: {}, 处理前", tid, idxTmp);
//            iotCmdHandler.process(new String(message.getPayload(), StandardCharsets.UTF_8));
            try {
//            byte[] resMsg = ByteUtils.hexToBytes(
//                new String(message.getPayload(), StandardCharsets.UTF_8));
                byte[] resMsg = message.getPayload();
                mqttHandler.processMqttMessage(ts, topic, resMsg);
            } catch (Exception e) {
                log.warn("处理mqtt消息失败. topic= {}", topic, e);
            }
        } catch (Exception e) {
            log.warn("处理mqtt消息失败. topic= {}, exception = {}", topic, e.getMessage());
            throw e;
        } finally {
            span.end();
        }
//            log.debug("[{}] idx: {}, 处理后", tid, idxTmp);
//        });
    }

    public boolean sendMsg(String tid, String topic, int qos, String message,
        boolean wLog, boolean fullLog) {
        if (wLog) {
            log.debug("[{}] 发送MQTT消息 topic = {}, message = {}", tid, topic,
                fullLog || message.length() < 100 ? message
                    : message.substring(0, 100));  // 避免打印的日志太大
        }
        MqttMessage msg = new MqttMessage();
        msg.setQos(qos);
        msg.setPayload(ByteUtils.hexToBytes(message));
        try {
            mqttClient.publish(topic, msg);
            return true;
        } catch (Exception e) {
            log.error("[{}] 发送MQTT消息失败 error = {}. topic = {}, message = {}", tid,
                e.getMessage(), topic, message);
            return false;
        }
    }
}
