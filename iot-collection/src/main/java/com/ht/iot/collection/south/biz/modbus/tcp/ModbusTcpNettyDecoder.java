package com.ht.iot.collection.south.biz.modbus.tcp;

import com.ht.iot.collection.model.ModbusTcpRes;
import com.ht.iot.collection.utils.NettyUtils;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;

@Slf4j
//@Component
//@Scope(value = "singleton")
@ChannelHandler.Sharable
public class ModbusTcpNettyDecoder extends MessageToMessageDecoder<ByteBuf> {

    private static final int MAX_FRAME_SIZE = 1024 * 10; // 10M

    private Tracer tracer;
    private ModbusTcpChannelRepo channelRepo;

    public ModbusTcpNettyDecoder(Tracer tracer, ModbusTcpChannelRepo channelRepo) {
        this.tracer = tracer;
        this.channelRepo = channelRepo;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) {
        Span span = this.tracer.nextSpan().name("ModbusTcpNettyDecoder");
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String rootId = RandomStringUtils.randomAlphabetic(12);
            String channelKey = NettyUtils.genChKey(ctx);

//        if (msg.readableBytes() > MAX_FRAME_SIZE) {
//            log.error("客户端报文太大 !!! rootId = {}, channel: {}", rootId, channelKey);
//            resetClient(ctx.channel());     //发送大量报文/或者从缓冲区收到大量报文直接踢掉
//            throw new TooLongFrameException("Frame too big!");
//        }

            String rawBytes = ByteBufUtil.hexDump(msg);
            log.info("收到报文 rootId = {} , channelKey: {} , bytes: {}", rootId, channelKey,
                rawBytes);

            ModbusTcpRes modbusRes = new ModbusTcpRes();
            modbusRes.setTs(LocalDateTime.now())
                .setSeq(msg.readUnsignedShort());
            msg.skipBytes(2);   // 跳过 00 00
            modbusRes.setLen(msg.readUnsignedShort());
            modbusRes.setSid(msg.readByte() & 0xFF);
            modbusRes.setCmd(msg.readByte());   // 指令码
            int dataLen;
            if ((modbusRes.getCmd() & 0xFF) > 0x80) {
                // 返回modbus错误码, 数据部分为1字节的错误码
                dataLen = 1;    // 数据部分的长度
            } else {
                // 返回正常的响应数据
                dataLen = msg.readByte() & 0xFF;    // 数据部分的长度
            }
            modbusRes.setData(new byte[dataLen]);
            msg.readBytes(modbusRes.getData());
//        modbusRes.setCrc(msg.readUnsignedShortLE());
            out.add(modbusRes);
        } catch (Exception e) {
            log.warn("exception = {}", e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }

//    private void resetClient(Channel channel) {
//        idempotenceService.remove(channel.remoteAddress().toString());//踢出之前将前面发送的报文清空
//        nettyChannelRepository.remove(channel.remoteAddress().toString());
//        channel.close();
//    }
}
