package com.ht.iot.collection.utils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.*;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.Map;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EquationDecoder {
    private static final ExpressionParser parser = new SpelExpressionParser();
    private static final Map<String, Expression> expressionCache = new HashMap<>();

    public static Object calculate(String expression, Map<String, Object> resultValue)
            throws CalculationException {
        try {

            // 1. 提取变量名
            String[] variables = extractVariableNames(expression);

            // 2. 验证并修正表达式格式
            String validExpression = validateAndFixExpression(expression, variables);

            // 3. 解析表达式
            Expression expr = expressionCache.computeIfAbsent(
                    validExpression,
                    parser::parseExpression
            );

            // 4. 创建安全的评估上下文
            EvaluationContext context = createSafeContext(resultValue);

            // 5. 执行表达式
            return expr.getValue(context);

        } catch (SpelEvaluationException | ParseException e) {
            log.debug(e.getMessage());
            throw handleParseError(e, expression);
        } catch (Exception e) {
            throw new CalculationException("处理表达式时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 创建安全的评估上下文
     */
    private static EvaluationContext createSafeContext(Map<String, Object> resultValue) {
        // 创建支持类型访问的上下文
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 允许访问 java.lang.Math
        context.setTypeLocator(new StandardTypeLocator());

        // 注册变量
        resultValue.forEach((key, value) -> {
            if (value instanceof String strValue) {
                if (strValue.matches("-?\\d+")) {
                    context.setVariable(key, Integer.parseInt(strValue));
                } else if (strValue.matches("-?\\d+\\.\\d+")) {
                    context.setVariable(key, Double.parseDouble(strValue));
                } else {
                    context.setVariable(key, value);
                }
            } else {
                context.setVariable(key, value);
            }
        });

        return context;
    }

    private static class StandardTypeLocator extends org.springframework.expression.spel.support.StandardTypeLocator {
        @Override
        public Class<?> findType(String typeName) throws EvaluationException {
            // 特殊处理 Math 类型
            if ("Math".equals(typeName)) {
                return Math.class;
            }
            return super.findType(typeName);
        }
    }

    /**
     * 提取表达式中所有变量名
     */
    private static String[] extractVariableNames(String expression) {
        Pattern pattern = Pattern.compile("#\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(expression);
        return matcher.results()
                .map(matchResult -> matchResult.group(1).trim())
                .toArray(String[]::new);
    }

    /**
     * 验证并修正表达式格式
     */
    private static String validateAndFixExpression(String expression, String[] variables) {
        String transformed = expression;
        for (String var : variables) {
            transformed = transformed.replace("#{" + var + "}", "#" + var);
        }

        // 简化 Math 调用（可选）
        transformed = transformed
                .replace("ceil(", "T(Math).ceil(")
                .replace("floor(", "T(Math).floor(")
                .replace("round(", "T(Math).round(");

        return transformed;
    }

    /**
     * 自动添加变量标记
     */
    private static String addVariableNotation(String expression) {
        // 匹配所有单词字符组成的标识符
        Pattern pattern = Pattern.compile("\\b([a-zA-Z_][\\w.]*)\\b");
        Matcher matcher = pattern.matcher(expression);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String var = matcher.group(1);

            // 跳过关键字和数值
            if (!isNumber(var) && !isOperator(var) && !isKeyword(var)) {
                matcher.appendReplacement(sb, "#{" + var + "}");
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private static boolean isNumber(String token) {
        return token.matches("^\\d+(\\.\\d+)?$");
    }

    private static boolean isOperator(String token) {
        return token.matches("[-+*/()]");
    }

    private static boolean isKeyword(String token) {
        return token.equals("and") || token.equals("or") || token.equals("div") ||
                token.equals("mod") || token.equals("eq") || token.equals("ne") ||
                token.equals("lt") || token.equals("le") || token.equals("gt") || token.equals("ge");
    }

    /**
     * 处理解析错误
     */
    private static CalculationException handleParseError(Exception e, String expression) {
        String errorMsg = e.getMessage();

        StringBuilder report = new StringBuilder("### 表达式解析失败报告 ###\n");
        report.append("原始表达式: ").append(expression).append("\n");
        report.append("错误信息: ").append(errorMsg).append("\n\n");

        // 分析错误位置
        report.append("问题分析: \n");
        if (e instanceof ParseException parseEx) {
            report.append("位置: ").append(parseEx.getPosition()).append("\n");
            report.append("表达式位置内容: ").append(getPositionContext(expression, parseEx.getPosition()));
        }

        // 识别问题标记
        Pattern tokenPattern = Pattern.compile("Expected '([^']+)' but was '([^']+)'");
        Matcher tokenMatcher = tokenPattern.matcher(errorMsg);

        if (tokenMatcher.find()) {
            String expected = tokenMatcher.group(1);
            String unexpected = tokenMatcher.group(2);

            report.append("\n标记分析: \n");
            report.append("  预期: ").append(expected).append("\n");
            report.append("  实际: ").append(unexpected).append("\n");

            // 特殊处理大括号情况
            if ("lcurly({)".equals(unexpected)) {
                report.append("解决方案: \n");
                report.append("1. 检查是否有多余的大括号包围表达式\n");
                report.append("2. 确保所有变量名使用 #{variable} 格式\n");
                report.append("3. 嵌套表达式应有明确的结束符\n");
            }
        }

        report.append("\n自动修复建议: \n");
        report.append("  尝试修复后表达式: ").append(suggestFix(expression)).append("\n");

        return new CalculationException(errorMsg, report.toString());
    }

    private static String getPositionContext(String expression, int position) {
        int start = Math.max(0, position - 10);
        int end = Math.min(expression.length(), position + 10);

        if (position >= expression.length()) {
            return expression.substring(expression.length() - 20) + "<<< 结尾";
        }

        String context = expression.substring(start, end);
        return context.substring(0, position - start) + "【问题位置】" + context.substring(position - start);
    }

    private static String suggestFix(String expression) {
        // 移除多余的大括号
        String fixed = expression.trim().replaceAll("^\\{|}$", "");

        // 确保所有变量有正确的标记
        if (!fixed.contains("#{")) {
            return addVariableNotation(fixed);
        }

        // 尝试自动修复不平衡的大括号
        if (fixed.contains("{")) {
            int openCount = countMatches(fixed, "#{");
            int closeCount = countMatches(fixed, "}");

            if (openCount > closeCount) {
                return fixed + "}";
            } else if (closeCount > openCount) {
                return fixed.replaceFirst("}", "");
            }
        }

        return fixed;
    }

    private static int countMatches(String str, String sub) {
        int lastIndex = 0;
        int count = 0;

        while (lastIndex != -1) {
            lastIndex = str.indexOf(sub, lastIndex);
            if (lastIndex != -1) {
                count++;
                lastIndex += sub.length();
            }
        }
        return count;
    }

    /**
     * 自定义异常类
     */
    @Getter
    public static class CalculationException extends Exception {
        private final String diagnosticReport;

        public CalculationException(String message, String report) {
            super(message);
            this.diagnosticReport = report;
        }

        public CalculationException(String message, Throwable cause) {
            super(message, cause);
            this.diagnosticReport = generateDefaultReport(message);
        }

        private String generateDefaultReport(String message) {
            return "表达式计算失败，错误信息: " + message;
        }
    }
}