package com.ht.iot.collection.model.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import com.ht.iot.collection.model.type.ModbusTcpSocketType;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateModbusDeviceInfoDto extends UpdateDeviceInfoDto {


    /**
     * modbus通信id
     */
    private Integer sid;

    /**
     * 当前仅用于ModbusTcp,用于标识本地为tcp client或tcp server
     */
    private ModbusTcpSocketType socketType;

    /**
     * 当前仅用于ModbusTcp,用于设置对端的IP
     */
    private String peerIp;

    /**
     * 当前仅用于ModbusTcp,用于设置对端的端口
     */
    private Integer peerPort;

    /**
     * 要采集的modbus地址列表
     */
    private List<ModbusAddrRange> addrs;

    /**
     * 要映射的modbus值
     */
    private List<ModbusTvCfg> tvs;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
