package com.ht.iot.collection.model.dto;

import com.cdz360.iot.model.modbus.type.ModbusDataType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ModbusTvCfg extends TvCfg {

    private Integer addr;
    private Integer num;

    private String order;

    /**
     * 值类型
     */
    private ModbusDataType t;

    /**
     * 小数位数
     */
    private Integer decimal;


    private Integer shift;

}
