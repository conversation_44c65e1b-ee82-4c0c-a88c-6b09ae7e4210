package com.ht.iot.collection.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class NotifyCfg {
    /**
     * 推送采集数据的地址, 支持 http(s)://
     */
    private String url;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String username;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passcode;
}
