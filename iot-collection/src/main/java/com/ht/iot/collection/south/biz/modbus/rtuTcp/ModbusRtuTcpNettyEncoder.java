package com.ht.iot.collection.south.biz.modbus.rtuTcp;

import com.cdz360.base.utils.ByteUtils;
import com.ht.iot.collection.model.ModbusRtuReq;
import com.ht.iot.collection.model.ModbusTcpReq;
import com.ht.iot.collection.utils.NettyUtils;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;
import java.io.ByteArrayOutputStream;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ChannelHandler.Sharable
public class ModbusRtuTcpNettyEncoder extends MessageToMessageEncoder<ModbusRtuReq> {

    @Override
    protected void encode(ChannelHandlerContext ctx, ModbusRtuReq reqMsg, List<Object> out)
        throws Exception {
        log.info("[{} {}] 开始编码待发送的 modbusRtu over tcp 消息. sid= {},  cmd= {}, addr= {}, num= {}",
            reqMsg.getGwno(), reqMsg.getDno(),
            reqMsg.getSid(),
            reqMsg.getAddr().getReadCmd(),
            reqMsg.getAddr().getAddr(),
            reqMsg.getAddr().getNum());
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write((byte)(reqMsg.getSid() & 0xFF)); //  报文序列号

        baos.write( reqMsg.getAddr().getReadCmd());

        baos.write(ByteUtils.intToByte2BE(reqMsg.getAddr().getAddr()));
        baos.write(ByteUtils.intToByte2BE(reqMsg.getAddr().getNum()));
        int crc = ByteUtils.crc16(baos.toByteArray(), baos.size());
        baos.write(ByteUtils.intToByte2LE(crc));

        byte[] buf = baos.toByteArray();
        log.info("[{} {} {}] 发送 modbusRtuOnTcp 消息: {}",
            reqMsg.getGwno(), reqMsg.getDno(),
            NettyUtils.genChKey(ctx.channel()),
            ByteUtils.bytesToHex(buf));
//        out.add(buf);
        out.add(Unpooled.wrappedBuffer(buf));
    }
}
