package com.ht.iot.collection.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import com.cdz360.iot.model.modbus.type.P645DataType;
import com.ht.iot.collection.model.dto.Dlt645Data;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LaunchedDlt645Device extends LaunchedDevice {

    /**
     * hex格式的电表表号，6字节
     */
    private String hexMeterNo;
    /**
     * 电表表号，6字节
     */
    private byte[] meterNoBytes;

    /**
     * 待发送的下行写指令队列
     */
    private Queue<Dlt645Data> writeQ;
    /**
     * 待发送的下行读指令（数据采集）队列
     */
    private Queue<P645DataType> readQ;
    /**
     * 已发送的
     */
//    private P645DataType reqAddr;
    private List<Dlt645Tv> tvs;

    public LaunchedDlt645Device() {
        this.writeQ = new ConcurrentLinkedDeque<>();
        this.readQ = new ConcurrentLinkedDeque<>();
    }

    /**
     * @return true，待采集/写指令的队列都为空
     */
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(this.readQ)
            && CollectionUtils.isEmpty(this.writeQ);
    }

    /**
     * @return 待采集/写消息的队列长度
     */
    public int getQueueSize() {
        int size = 0;
        if (this.writeQ != null) {
            size = this.writeQ.size();
        }
        if (this.readQ != null) {
            size += this.readQ.size();
        }
        return size;
    }

    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
