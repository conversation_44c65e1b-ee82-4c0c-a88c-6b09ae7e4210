package com.ht.iot.collection.north.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.ht.iot.collection.model.dto.NotifyDataDto;
import java.io.IOException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HttpNotifyService {

    X509TrustManager x509TrustManager = new X509TrustManager() {

        @Override
        public void checkClientTrusted(
            X509Certificate[] x509Certificates, String s) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] x509Certificates, String s)
            throws CertificateException {

        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    };
    TrustManager[] trustAllCerts = new TrustManager[]{
        new X509TrustManager() {
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                X509Certificate[] x509Certificates = new X509Certificate[0];
                return x509Certificates;
            }

            public void checkClientTrusted(
                java.security.cert.X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(
                java.security.cert.X509Certificate[] certs, String authType) {
            }
        }
    };

    private OkHttpClient createHttpClient(URL url) {
//        int keepAliveTimeX = keepAliveTime == null || keepAliveTime < 1 ? DEFAULT_KEEP_ALIVE_TIME : keepAliveTime;
//        int timeoutX = timeout == null || timeout < 1 ? DEFAULT_TIMEOUT : timeout;
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.connectTimeout(5, TimeUnit.SECONDS)
            .readTimeout(5, TimeUnit.SECONDS)
            .writeTimeout(5, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(20, 3, TimeUnit.MINUTES))
            .retryOnConnectionFailure(true);
        if (url.getProtocol().equalsIgnoreCase("https")) {

            builder.sslSocketFactory(getTrustedSSLSocketFactory(), x509TrustManager)
                .hostnameVerifier((hostname, session) -> true); // 默认不校验域名,全部通过
        }
        return builder.build();
    }

    private SSLSocketFactory getTrustedSSLSocketFactory() {
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            return sc.getSocketFactory();
        } catch (KeyManagementException | NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void sendNotify(String gwno, String dno, String notifyUrl, NotifyDataDto data) {
        URL url;
        try {
            url = new URL(notifyUrl);
        } catch (Exception e) {
            log.error("[{} {}] 构建URL失败. notifyUrl = {}", gwno, dno, notifyUrl);
            throw new DcArgumentException("URL错误", Level.WARN, e);
        }
        OkHttpClient httpClient = this.createHttpClient(url);
        if (httpClient == null) {
            log.error("[{} {}] 创建http客户端失败. notifyUrl= {}",
                gwno, dno, notifyUrl);
            return;
        }
        // data部分含有LocalDateTime，需要做格式约定
        String jsonMsg = JsonUtils.toJsonString(data,
            DcConstants.DC_DEFAULT_DATE_TIME_FORMAT,
            Locale.CHINESE);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), jsonMsg);

        Request httpReq = new Request.Builder()
            .url(url)
            .post(body)
            .build();

        log.debug("[{} {}] 推送消息 url= {}, jsonMsg= {}",
            gwno, dno, notifyUrl, jsonMsg);
        httpClient.newCall(httpReq)
            .enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("[{} {}] 发送推送消息失败. url= {}, jsonMsg= {}",
                        gwno, dno, notifyUrl, jsonMsg);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
//                    Long beginTime = System.nanoTime();
                    if (response.code() < 200 || response.code() > 299) {
                        log.error("[{} {}] 发送推送消息失败. url= {}, jsonMsg= {}",
                            gwno, dno, notifyUrl, jsonMsg);
                    } else {
                        log.debug("[{} {}] 发送推送消息成功. url= {}, jsonMsg= {}",
                            gwno, dno, notifyUrl, jsonMsg);
                    }
                    response.close();
                }
            });
    }
}
