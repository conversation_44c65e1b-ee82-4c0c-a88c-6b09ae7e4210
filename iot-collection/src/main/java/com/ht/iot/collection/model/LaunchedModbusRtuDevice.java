package com.ht.iot.collection.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAbstractTv;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LaunchedModbusRtuDevice extends LaunchedDevice {

    public LaunchedModbusRtuDevice() {
        this.writeQ = new ConcurrentLinkedDeque<>();
        this.readQ = new ConcurrentLinkedDeque<>();
    }

    /**
     * modbus通信id
     */
    private Integer sid;


    /**
     * 待发送的下行写指令队列
     */
    private Queue<Object> writeQ;

    /**
     * 待发送的
     */
    private Queue<ModbusAddrRange> readQ;

    /**
     * 要映射的modbus值
     */
    private List<ModbusAbstractTv> tvs;

    /**
     * @return true，待采集/写指令的队列都为空
     */
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(this.readQ)
            && CollectionUtils.isEmpty(this.writeQ);
    }

    /**
     * @return 待采集的地址队列长度
     */
    public int getQueueSize() {
        int size = 0;
        if (this.writeQ != null) {
            size = this.writeQ.size();
        }
        if (this.readQ != null) {
            size += this.readQ.size();
        }
        return size;
    }

    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
