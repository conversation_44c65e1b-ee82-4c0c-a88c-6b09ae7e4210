package com.ht.iot.collection.model.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum Dlt645WriteDataType implements DcEnum {

    UNKNOWN(0),

    HEX(1),     // HEX字符串
    HEX_LIST(2) // HEX字符串数组
    ;

    @JsonValue
    final int code;

    Dlt645WriteDataType(int code) {
        this.code = code;
    }


    @JsonCreator
    public static Dlt645WriteDataType valueOf(Object codeIn) {
        if (codeIn == null) {
            return Dlt645WriteDataType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof Dlt645WriteDataType) {
            return (Dlt645WriteDataType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (Dlt645WriteDataType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return Dlt645WriteDataType.UNKNOWN;
    }


}
