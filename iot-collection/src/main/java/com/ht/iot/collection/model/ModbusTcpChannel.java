package com.ht.iot.collection.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ht.iot.collection.model.type.ModbusTcpSocketType;
import io.netty.channel.Channel;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomUtils;

@Data
@Accessors(chain = true)
public class ModbusTcpChannel {

    private String gwno;

    private ModbusTcpSocketType socketType;

    @JsonIgnore
    private Socket socket;


    private AtomicInteger seqNum = new AtomicInteger(RandomUtils.nextInt());

    private String peerIp;

    private Integer peerPort;


}
