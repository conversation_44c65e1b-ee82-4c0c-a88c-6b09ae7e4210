package com.ht.iot.collection;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcGwException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.iot.model.base.BaseGwResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;

@ControllerAdvice(annotations = RestController.class)
public class IotCollectionExceptionHandler {

    private final Logger logger = LoggerFactory.getLogger(IotCollectionExceptionHandler.class);


    @ExceptionHandler({DcServiceException.class,
        DcArgumentException.class, DcGwException.class})
    public ResponseEntity<BaseGwResponse> handleServiceException(DcException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());

        if (ex instanceof DcGwException) {
            ResponseEntity<BaseGwResponse> res = ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(result);
            return res;
        } else {
            ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
            return res;
        }
    }


    @ExceptionHandler
    public ResponseEntity<BaseGwResponse> handle(Exception ex) {
        logger.error("error = {}", ex.getMessage(), ex);
        // TODO: 需要按异常类型做处理, 返回不同的status和error
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        }
        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
        return res;
    }


    @ExceptionHandler({DcServerException.class})
    public ResponseEntity<BaseGwResponse> handleServerException(DcServerException ex) {
        logger.warn(ex.getMessage(), ex);
        BaseGwResponse result = new BaseGwResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        ResponseEntity<BaseGwResponse> res = ResponseEntity.ok(result);
        return res;
    }


}
