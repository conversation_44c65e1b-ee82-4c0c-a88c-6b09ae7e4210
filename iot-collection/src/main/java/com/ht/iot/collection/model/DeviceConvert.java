package com.ht.iot.collection.model;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentLinkedQueue;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DeviceConvert {


    public static LaunchedDevice toLaunchedDevice(HoldingDevice deviceIn) {
        LaunchedDevice result = null;
        if (Rs485Protocol.MODBUS == deviceIn.getProtocol()) {
            HoldingModbusRtuDevice modbusDeviceIn = (HoldingModbusRtuDevice) deviceIn;
            LaunchedModbusRtuDevice modbusDevice = new LaunchedModbusRtuDevice();
            modbusDevice.setSid(modbusDeviceIn.getSid())
                .setReadQ(new ConcurrentLinkedQueue<>())
                .setTvs(new ArrayList<>());
            if(CollectionUtils.isNotEmpty(modbusDeviceIn.getAddrs())) {
                // 将最后一个设置为强制上报
                modbusDeviceIn.getAddrs()
                    .get(modbusDeviceIn.getAddrs().size() -1)
                    .setForceNotify(true);
                modbusDevice.getReadQ().addAll(modbusDeviceIn.getAddrs());
            }

            modbusDevice.getTvs().addAll(modbusDeviceIn.getTvs());
            result = modbusDevice;
        } else if (IotCollectionConstants.TCP_PROTOCOLS.contains(
            deviceIn.getProtocol())) { // modbusTcp, modbusRtu over Tcp
            HoldingModbusTcpDevice modbusDeviceIn = (HoldingModbusTcpDevice) deviceIn;
            LaunchedModbusTcpDevice modbusDevice = new LaunchedModbusTcpDevice();
            modbusDevice.setSocketType(modbusDeviceIn.getSocketType())
                .setPeerIp(modbusDeviceIn.getPeerIp())
                .setPeerPort(modbusDeviceIn.getPeerPort())
//            modbusDevice.s(modbusDeviceIn.getChannel())
                .setSid(modbusDeviceIn.getSid())
                .setReadQ(new ConcurrentLinkedQueue<>())
                .setTvs(new ArrayList<>());
            if(CollectionUtils.isNotEmpty(modbusDeviceIn.getAddrs())) {
                // 将最后一个设置为强制上报
                modbusDeviceIn.getAddrs()
                    .get(modbusDeviceIn.getAddrs().size() -1)
                    .setForceNotify(true);
                modbusDevice.getReadQ().addAll(modbusDeviceIn.getAddrs());
            }
            modbusDevice.getTvs().addAll(modbusDeviceIn.getTvs());
            result = modbusDevice;
        } else if (Rs485Protocol.DLT645 == deviceIn.getProtocol()) {
            HoldingDlt645Device dlt645DeviceIn = (HoldingDlt645Device) deviceIn;
            LaunchedDlt645Device dlt645Device = new LaunchedDlt645Device();
            dlt645Device.setHexMeterNo(dlt645DeviceIn.getHexMeterNo())
                .setMeterNoBytes(ByteUtils.hexToBytes(dlt645DeviceIn.getHexMeterNo()))
                .setReadQ(new ConcurrentLinkedQueue<>())
                .setTvs(new ArrayList<>());
//            if(CollectionUtils.isNotEmpty(dlt645DeviceIn.getAddrs())) {
//                // 将最后一个设置为强制上报
//                dlt645DeviceIn.getAddrs()
//                    .get(dlt645DeviceIn.getAddrs().size() -1)
//                    .setForceNotify(true);
//                dlt645Device.getReadQ().addAll(dlt645DeviceIn.getAddrs());
//            }
            dlt645Device.getReadQ().addAll(dlt645DeviceIn.getAddrs());
            dlt645Device.getTvs().addAll(dlt645DeviceIn.getTvs());
            result = dlt645Device;
        } else {
            log.error("[{} {}] 不识别的设备通信协议. protocol= {}", deviceIn.getGwno(),
                deviceIn.getDno(), deviceIn.getProtocol());
            return null;
        }
        result.setGwno(deviceIn.getGwno())
            .setDno(deviceIn.getDno())
            .setProtocol(deviceIn.getProtocol())
            .setDuration(deviceIn.getDuration())
            .setExpireDur(deviceIn.getExpireDur())
            .setMqttDownTopic(deviceIn.getMqttDownTopic())
            .setNotifyCfg(deviceIn.getNotifyCfg())
            .setDeviceType(deviceIn.getDeviceType())
            .setCombineNotify(deviceIn.getCombineNotify())
            .setTvMathComputed(deviceIn.getTvMathComputed());
        return result;
    }
}
