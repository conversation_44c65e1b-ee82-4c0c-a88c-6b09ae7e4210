package com.ht.iot.collection.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ht.iot.collection.model.type.ModbusTcpSocketType;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomUtils;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HoldingModbusTcpDevice extends HoldingModbusRtuDevice{

//    private ModbusTcpChannel channel;

//    @JsonIgnore
//    private Socket socket;



    private ModbusTcpSocketType socketType;

    private String peerIp;

    private Integer peerPort;


    private AtomicInteger seqNum = new AtomicInteger(RandomUtils.nextInt());
}
