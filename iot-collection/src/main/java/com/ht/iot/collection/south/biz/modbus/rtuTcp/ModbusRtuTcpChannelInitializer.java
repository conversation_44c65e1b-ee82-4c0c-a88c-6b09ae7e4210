package com.ht.iot.collection.south.biz.modbus.rtuTcp;

import io.micrometer.tracing.Tracer;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Component
//@Qualifier("modbusRtuTcpChannelInitializer")
public class ModbusRtuTcpChannelInitializer extends ChannelInitializer<SocketChannel> {

    private static final EventExecutorGroup group;

    static {
        final int nThreads = Runtime.getRuntime().availableProcessors();
        group = new DefaultEventExecutorGroup(nThreads * 5);
    }

    private Tracer tracer;
    private ModbusRtuTcpChannelRepo channelRepo;
    private ModbusRtuTcpService modbusRtuTcpService;

    public ModbusRtuTcpChannelInitializer(Tracer tracer, ModbusRtuTcpChannelRepo channelRepo,
        ModbusRtuTcpService modbusRtuTcpService) {
        this.tracer = tracer;
        this.channelRepo = channelRepo;
        this.modbusRtuTcpService = modbusRtuTcpService;
    }

    @Override
    protected void initChannel(SocketChannel socketChannel) {
        ChannelPipeline pipeline = socketChannel.pipeline();

        long timeout = 600;  //initCheckClientTimeout();
        pipeline.addLast(new IdleStateHandler(timeout, 0, 0, TimeUnit.SECONDS));

//        pipeline.addLast(new StripPrefixDecoder());
        // 分包、组包
        pipeline.addLast(new ModbusRtuTcpPackageDecoder(this.tracer, this.channelRepo,
            1024, 2, 1, 2, 0));
        pipeline.addLast(new ModbusRtuTcpNettyDecoder(this.channelRepo));
        pipeline.addLast(new ModbusRtuTcpNettyEncoder());

        pipeline.addLast(group, "handler",
            new ModbusRtuTcpNettyChannelHandler(this.channelRepo,
                this.modbusRtuTcpService));// 将I/O线程和业务线程分开
    }

}
