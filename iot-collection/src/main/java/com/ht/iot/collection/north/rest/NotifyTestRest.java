package com.ht.iot.collection.north.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 仅用于测试接收推送消息
 */
@Slf4j
@RestController
@RequestMapping(value = "/iot/collection/notify", produces = MediaType.APPLICATION_JSON_VALUE)
public class NotifyTestRest {


    @PostMapping(value = "/test/{dno}")
    public Mono<ObjectResponse<String>> notifyTest(@PathVariable String dno,
        @RequestBody String msg) {
        log.info("收到采集的推送消息 dno= {}, msg= {}", dno, msg);
        return Mono.just("true")
            .map(RestUtils::buildObjectResponse);
    }
}
