package com.ht.iot.collection.south.biz.modbus.tcp;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModbusTcpChannelRepo {

    private Map<String, String> channels = new ConcurrentHashMap<String, String>();


    public synchronized void addChannel(String chKey, String gwno) {
        this.channels.put(chKey, gwno);
    }

    public synchronized void removeChannel(String chKey) {
        this.channels.remove(chKey);
    }

    public String getGwno(String chKey) {
        return this.channels.get(chKey);
    }
}
