package com.ht.iot.collection.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import com.cdz360.iot.model.modbus.type.P645DataType;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HoldingDlt645Device extends HoldingDevice {

    /**
     * hex格式的电表表号，6字节
     */
    private String hexMeterNo;

    /**
     * 电表表号，6字节
     */
    private byte[] meterNoBytes;

    private List<P645DataType> addrs;

    private List<Dlt645Tv> tvs;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
