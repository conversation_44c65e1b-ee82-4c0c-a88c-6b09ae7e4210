package com.ht.iot.collection.south.biz;

import com.cdz360.base.model.base.exception.DcNeedMoreDataException;
import com.ht.iot.collection.biz.ActiveGwRepo;
import com.ht.iot.collection.biz.LaunchingExecutor;
import com.ht.iot.collection.model.ActiveGw;
import com.ht.iot.collection.model.LaunchedDevice;
import com.ht.iot.collection.model.ModbusRtuRes;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqttHandler {

    @Autowired
    private ActiveGwRepo gwRepo;
    @Autowired
    private LaunchingExecutor launchingExecutor;

    /**
     * @param topic 格式 /aaa/bbb/{gwno}/ccc
     */
    public void processMqttMessage(LocalDateTime ts, String topic, byte[] resMsg) {
        String[] toks = topic.split("/");
        String gwno = toks[toks.length - 2];    // 最后第二个
        ActiveGw gw = gwRepo.getGwInfo(gwno);
        if (gw == null) {
            log.info("[{}] 收到未配置网关的mqtt消息,忽略. topic= {}", gwno, topic);
            return;
        }
        byte[] fullMsg = resMsg;
        if (gw.getSlice() != null) {
            fullMsg = new byte[gw.getSlice().length + resMsg.length];
            System.arraycopy(gw.getSlice(), 0, fullMsg, 0, gw.getSlice().length);
            System.arraycopy(resMsg, 0, fullMsg, gw.getSlice().length, resMsg.length);

        }
        LaunchedDevice device = gw.getDevices().peek();
        if (device == null) {
            log.warn("[{}] << 网关的设备队列为空,未处于数据采集状态", gw.getGwno());
            return;
        }
        try {
            launchingExecutor.processRes(gw, device, ts, fullMsg);
            gw.setSlice(null);
        } catch (DcNeedMoreDataException e) {
            gw.setSlice(fullMsg);   // 保存当前收到的报文,待后续上报后再做处理
        }
    }



}
