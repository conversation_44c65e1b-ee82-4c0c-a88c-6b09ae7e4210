package com.ht.iot.collection.utils;

import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;

public class NettyUtils {

    public static String genChKey(Channel ch) {
        return ch.remoteAddress().toString().replaceFirst("/", "")
            + "-"
            + ch.localAddress().toString().replaceFirst("/", "");
    }

    public static String genChKey(ChannelHandlerContext ctx) {
        return NettyUtils.genChKey(ctx.channel());
    }

    public static String toLocalIpPort(Channel ch) {
        return ch.localAddress().toString().replaceFirst("/", "");
    }

    public static String toLocalIpPort(ChannelHandlerContext ctx) {
        return NettyUtils.toLocalIpPort(ctx.channel());
    }
}
