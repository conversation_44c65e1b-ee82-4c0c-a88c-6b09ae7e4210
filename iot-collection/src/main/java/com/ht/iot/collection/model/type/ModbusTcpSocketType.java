package com.ht.iot.collection.model.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ModbusTcpSocketType implements DcEnum {

    UNKNOWN(0),
    TCP_CLIENT(1),
    TCP_SERVER(2);


    @JsonValue
    final int code;

    ModbusTcpSocketType(int code) {
        this.code = code;
    }


    @JsonCreator
    public static ModbusTcpSocketType valueOf(Object codeIn) {
        if (codeIn == null) {
            return ModbusTcpSocketType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ModbusTcpSocketType) {
            return (ModbusTcpSocketType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ModbusTcpSocketType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ModbusTcpSocketType.UNKNOWN;
    }

}
