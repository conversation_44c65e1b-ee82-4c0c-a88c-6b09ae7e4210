package com.ht.iot.collection.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcNeedMoreDataException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import com.cdz360.iot.model.modbus.type.P645DataType;
import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.ht.iot.collection.model.*;
import com.ht.iot.collection.model.dto.Dlt645Data;
import com.ht.iot.collection.model.dto.NotifyCfg;
import com.ht.iot.collection.model.dto.NotifyDataDto;
import com.ht.iot.collection.model.type.SouthMsgType;
import com.ht.iot.collection.north.biz.HttpNotifyService;
import com.ht.iot.collection.south.biz.MqttService;
import com.ht.iot.collection.south.biz.dlt645.Dlt645Decoder;
import com.ht.iot.collection.south.biz.dlt645.Dlt645Encoder;
import com.ht.iot.collection.south.biz.modbus.rtu.ModbusRtuDecoder;
import com.ht.iot.collection.south.biz.modbus.rtu.ModbusRtuEncoder;
import com.ht.iot.collection.south.biz.modbus.rtuTcp.ModbusRtuTcpService;
import com.ht.iot.collection.south.biz.modbus.tcp.ModbusTcpService;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.ht.iot.collection.utils.EquationDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采集执行器
 */
@Slf4j
@Service
public class LaunchingExecutor {


    @Autowired
    private ActiveGwRepo gwRepo;
    @Autowired
    private HoldingDeviceRepo deviceRepo;

    @Autowired
    private MqttService mqttService;
    @Autowired
    private ModbusTcpService modbusTcpService;
    @Autowired
    private ModbusRtuTcpService modbusRtuTcpService;

    @Autowired
    private ModbusRtuDecoder modbusRtuParser;

    @Autowired
    private Dlt645Encoder dlt645Encoder;

    @Autowired
    private HttpNotifyService httpNotifyService;
    @Autowired
    private Dlt645Decoder dlt645Decoder;
    @Autowired
    private ModbusRtuEncoder modbusRtuEncoder;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);



    public void push2LaunchingRepo() {
        List<LaunchedDevice> devices = deviceRepo.collectLaunchingDevices();
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }
        log.info("新增待下发采集的设备数量: {}", devices.size());
        LocalDateTime curTime = LocalDateTime.now();
        for (LaunchedDevice device : devices) {
            boolean launchNow = gwRepo.addDevice(device);
            if (launchNow) { // 立即下发采集指令
                this.sendNextRequest(device.getGwno(), curTime);
            }
        }
    }

    public void sendNextRequest(String gwno, LocalDateTime curTime) {
        LaunchedDevice device = gwRepo.getNextReq(gwno);
        if (device == null) {
            return;
        }

        log.debug("[{} {}] 准备下发采集指令", device.getGwno(), device.getDno());
        device.setReqTime(curTime);

        ActiveGw gw = gwRepo.getGwInfo(device.getGwno());
        synchronized (gw) { // 避免同一个网关被响应消息和超时消息同时调用，触发重复发送采集指令
            if (gw.getExpireTime() != null && curTime.isBefore(gw.getExpireTime())) {
                // 已经有下发中的指令，等待响应返回或超时后再下发
                log.info("[{}] 网关已有下发中的指令, 等待响应返回或超时后再下发", gw.getGwno());
                return;
            }
            
            // 创建消息上下文
            MessageContext messageContext = createMessageContext(device);
            gw.setCurrentMessageContext(messageContext);
            
            if (IotCollectionConstants.TCP_PROTOCOLS.contains(device.getProtocol())) {
                // 通过tcp发送
                LaunchedModbusTcpDevice deviceX = (LaunchedModbusTcpDevice) device;
                ModbusTcpReq reqMsg = new ModbusTcpReq();
                reqMsg.setGwno(deviceX.getGwno())
                    .setDno(deviceX.getDno())
                    .setSid(deviceX.getSid())
                    .setAddr(deviceX.getReadQ().peek());
                if (Rs485Protocol.MODBUS_RTU_TCP == device.getProtocol()) {  // modbus rtu over tcp
                    modbusRtuTcpService.sendReadRequest(device.getDno(), gw, deviceX,
                        reqMsg, true);
                } else if (Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
                    reqMsg.setSeq(deviceX.getSeqNum().getAndIncrement());
                    modbusTcpService.sendReadRequest(device.getDno(), gw, deviceX,
                        reqMsg, true);
                }
            } else {
//        LocalDateTime nextTime = curTime.plusSeconds(device.getDuration());
                ByteArrayOutputStream reqBuf = this.buildReqBuf(device);      // 组包
                log.debug("[{} {}] 发送采集指令. messageId={}, {}",
                    device.getGwno(), device.getDno(), messageContext.getMessageId(),
                    ByteUtils.bytesToHex(reqBuf.toByteArray()));

                if (Rs485Protocol.MODBUS == device.getProtocol()
                    || Rs485Protocol.DLT645 == device.getProtocol()) {
                    // 发送mqtt消息
                    mqttService.sendMsg(device.getDno(), device.getMqttDownTopic(), 1,
                        ByteUtils.bytesToHex(reqBuf.toByteArray()), true, false);

                } else {
                    log.error("[{} {}] 设备通信协议类型暂不支持. protocol= {}", device.getGwno(),
                        device.getDno(), device.getProtocol());
                }
            }
            gw.setExpireTime(curTime.plusSeconds(device.getExpireDur()));   // 设置超时时间
        }
    }

    public void processRes(ActiveGw gw, LaunchedDevice device, LocalDateTime ts, byte[] resMsg) {
        log.info("[{} {}] 处理收到的响应消息: {}",
            gw.getGwno(), device.getDno(), ByteUtils.bytesToHex(resMsg));
        
        // 验证消息是否匹配当前发送的消息
        MessageContext currentContext = gw.getCurrentMessageContext();
        if (currentContext == null) {
            log.warn("[{} {}] 收到响应消息但没有对应的发送上下文，可能是超时后的延迟响应，忽略处理", 
                gw.getGwno(), device.getDno());
            return;
        }
        
        if (!currentContext.isResponseMatching(device.getDno(), device.getProtocol())) {
            log.warn("[{} {}] 响应消息与当前发送消息不匹配. 期望设备: {}, 期望协议: {}, 实际设备: {}, 实际协议: {}",
                gw.getGwno(), device.getDno(), 
                currentContext.getDno(), currentContext.getProtocol(),
                device.getDno(), device.getProtocol());
            return;
        }
        
        log.info("[{} {}] 响应消息匹配成功. messageId: {}", 
            gw.getGwno(), device.getDno(), currentContext.getMessageId());
        
        Map<String, Object> resMap = null;
        Boolean forceNotify = null;
        if (Rs485Protocol.MODBUS == device.getProtocol()) {
            forceNotify = ((LaunchedModbusRtuDevice) device).getReadQ().peek().getForceNotify();
            resMap = this.processModbusRtuRes(gw, (LaunchedModbusRtuDevice) device, resMsg, currentContext);
        } else if (Rs485Protocol.MODBUS_RTU_TCP == device.getProtocol()) {
            forceNotify = ((LaunchedModbusTcpDevice) device).getReadQ().peek().getForceNotify();
            resMap = this.processModbusRtuTcpRes(gw, (LaunchedModbusTcpDevice) device, resMsg, currentContext);
        } else if (Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
            forceNotify = ((LaunchedModbusTcpDevice) device).getReadQ().peek().getForceNotify();
            resMap = this.processModbusRtuTcpRes(gw, (LaunchedModbusTcpDevice) device, resMsg, currentContext);
        } else if (Rs485Protocol.DLT645 == device.getProtocol()) {
            resMap = this.processDlt645Res(gw, (LaunchedDlt645Device) device, resMsg, currentContext);
        } else {
            log.error("[{} {}] 设备通信协议类型暂不支持. device= {}", device.getGwno(),
                device.getDno(), device);
        }

        if (device.getData() == null) {
            device.setData(new ConcurrentHashMap<>());
        }

        if (resMap != null && !resMap.isEmpty()) {
            Map<String, String> stringMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : resMap.entrySet()) {
                stringMap.put(entry.getKey(), entry.getValue().toString());
            }
            device.getData().putAll(stringMap);
        }

        Map<String, Boolean> filedPushTypeMap = getDeviceFieldPushType(device);
        Map<String, Object> needPushData = getPushData(filedPushTypeMap, device.getData());

        if (!device.getData().isEmpty()) {  // 采集到的数据不为空
            if (Boolean.TRUE.equals(device.getCombineNotify()) || Boolean.TRUE.equals(forceNotify)) {    // 需要合并上报
                if (device.getQueueSize() <= 0) {
                    Map<String, Object> equationMap = processEquation(gw, device);
                    if (equationMap != null && !equationMap.isEmpty()) {
                        needPushData.putAll(equationMap);
                    }
                    this.notifyResult(device, ts, needPushData);  // 通知数据接收方
                    device.getData().clear();
                } else {
                    log.debug("[{} {}] 等待采集更多信息后一起上报. queue.size= {}",
                        gw.getGwno(), device.getDno(), device.getQueueSize());
                }
            } else {
                Map<String, Object> equationMap = processEquation(gw, device);
                if (equationMap != null && !equationMap.isEmpty()) {
                    needPushData.putAll(equationMap);
                }
                this.notifyResult(device, ts, needPushData);  // 通知数据接收方
                device.getData().clear();
            }
        }
        gw.setExpireTime(null); // 清空超时等待时间
        gw.setCurrentMessageContext(null); // 清空消息上下文
        boolean sendNext = gwRepo.checkAndRemoveDevice(gw.getGwno(), device);   // 检查是否还有要下发的采集指令
        if (sendNext) {
            scheduler.schedule(() -> {
                this.sendNextRequest(gw.getGwno(), LocalDateTime.now());
            }, 500, TimeUnit.MILLISECONDS);
        }
    }

    private Map<String, Boolean> getDeviceFieldPushType(LaunchedDevice device) {
        Map<String, Boolean> needPushField = new HashMap<>();
        if (Rs485Protocol.MODBUS == device.getProtocol()) {
            ((LaunchedModbusRtuDevice) device).getTvs().forEach(i -> needPushField.put(i.getName(), !Boolean.FALSE.equals(i.getIsPushData())));
        } else if (Rs485Protocol.MODBUS_RTU_TCP == device.getProtocol()) {
            ((LaunchedModbusTcpDevice) device).getTvs().forEach(i -> needPushField.put(i.getName(), !Boolean.FALSE.equals(i.getIsPushData())));
        } else if (Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
            ((LaunchedModbusTcpDevice) device).getTvs().forEach(i -> needPushField.put(i.getName(), !Boolean.FALSE.equals(i.getIsPushData())));
        } else if (Rs485Protocol.DLT645 == device.getProtocol()) {
            ((LaunchedDlt645Device) device).getTvs().forEach(i -> needPushField.put(i.getName(), !Boolean.FALSE.equals(i.getIsPushData())));
        }
        return needPushField;
    }

    private Map<String, Object> getPushData(Map<String, Boolean> filedPushTypeMap, Map<String, Object> deviceData) {
        Map<String, Object> pushData = new HashMap<>();
        for (Map.Entry<String, Object> entry : deviceData.entrySet()) {
            Boolean value = filedPushTypeMap.get(entry.getKey());
            if (value != null && value) {
                pushData.put(entry.getKey(), entry.getValue());
            }
        }
        return pushData;
    }

    public Map<String, Object> processEquation(ActiveGw gw, LaunchedDevice device) {
        if (device.getTvMathComputed() == null || device.getTvMathComputed().isEmpty()) {
            log.debug("[{} {}] 无需要计算的公式内容.", gw.getGwno(), device.getDno());
            return null;
        }

        Map<String, Object> deviceData = device.getData();
        Map<String, Object> resMap = new HashMap<>();

        for (TvMathComputed tvComputed : device.getTvMathComputed()) {
            try {
                Object equationRes = EquationDecoder.calculate(tvComputed.getEquation(), deviceData);
                if (equationRes != null) {
                    deviceData.put(tvComputed.getName(), equationRes);
                    if (!Boolean.FALSE.equals(tvComputed.getIsPushData())) {
                        resMap.put(tvComputed.getName(), equationRes);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

//        if (!resMap.isEmpty()) {
//            this.notifyResult(device, LocalDateTime.now(), resMap);
//        }
        return resMap;
    }

    private Map<String, Object> processDlt645Res(ActiveGw gw, LaunchedDlt645Device device,
        byte[] resBuf, MessageContext messageContext) {
        SouthMsgType reqType = device.getCurMsgType();
        
        // 验证消息类型是否匹配
        if (!reqType.equals(messageContext.getMsgType())) {
            log.warn("[{} {}] DLT645响应消息类型不匹配. messageId: {}, 期望: {}, 实际: {}",
                gw.getGwno(), device.getDno(), messageContext.getMessageId(),
                messageContext.getMsgType(), reqType);
        }
        
        if (SouthMsgType.READ == reqType) {
            return this.processDlt645Res4Read(gw.getGwno(), device, resBuf, messageContext);
        } else if (SouthMsgType.WRITE == reqType) {
            return this.processDlt645Res4Write(gw.getGwno(), device, resBuf, messageContext);
        } else {
            log.warn("[{} {}] 下行指令类型错误. reqType= {}",
                gw.getGwno(), device.getDno(), device.getCurMsgType());
            throw new DcServiceException("下行指令类型错误");
        }
    }

    private Map<String, Object> processDlt645Res4Read(String gwno, LaunchedDlt645Device device,
        byte[] resBuf, MessageContext messageContext) {
        String dno = device.getDno();
        Map<String, Object> result = new HashMap<>();

        List<Dlt645Tv> resTvs = null;
        P645DataType reqAddr = device.getReadQ().peek();
        
        // 验证数据类型是否匹配
        if (!reqAddr.equals(messageContext.getDlt645DataType())) {
            log.warn("[{} {}] DLT645响应数据类型不匹配. messageId: {}, 期望: {}, 实际: {}",
                gwno, dno, messageContext.getMessageId(),
                messageContext.getDlt645DataType(), reqAddr);
        }
        
        try {
            resTvs = dlt645Decoder.parseDlt645Data(gwno, dno, resBuf, reqAddr);
        } catch (DcNeedMoreDataException e1) {
            log.info("[{} {}] 报文不完整,等待更多的报文",
                gwno, dno);
            throw e1;
        } catch (Exception e) {
            log.warn("[{} {}] 解析收到的DLT645电表数据失败",
                gwno, dno, e);
        }
        device.getReadQ().poll();   // 移除第一个地址，已采集完成
//            device.setReqAddr(null);
        device.setCurMsgType(null);
        if (CollectionUtils.isEmpty(resTvs)) {
            log.error("[{} {}] 收到的响应消息解析失败. messageId: {}, reqAddr= {}, resMsg= {}",
                gwno, dno, messageContext.getMessageId(), reqAddr, ByteUtils.bytesToHex(resBuf));
            return null;
        }
        Map<P645DataType, Dlt645Tv> tvs = device.getTvs().stream()
            .collect(Collectors.toMap(Dlt645Tv::getAddr, o -> o));
        for (Dlt645Tv tv : resTvs) {
            Dlt645Tv expect = tvs.get(tv.getAddr());
            if (expect == null) {
                log.warn("[{} {}] 收到未被需求的采集信息. addr= {}, value= {}",
                    gwno, dno, tv.getAddr(), tv.getV());
            } else {
                if (expect.getMultiple() == null) {
                    result.put(expect.getName(), tv.getV());
                } else if ((tv.getV() instanceof BigDecimal)) {
                    BigDecimal v = (BigDecimal) tv.getV();
                    result.put(expect.getName(),
                        v.multiply(expect.getMultiple()));
                } else {
                    log.warn(
                        "[{} {}] 配置异常,非数字型字段配置倍率,做无效忽略处理. 字段= {}, clazz= {}, v= {}",
                        gwno, dno, tv.getName(), tv.getV().getClass().getSimpleName(),
                        tv.getV());
                    result.put(expect.getName(), tv.getV());
                }
            }
        }
        // 返回结果可能含有LocalDateTime，需要做时间格式约定
        log.debug("[{} {}] result= {}", gwno, dno, JsonUtils.toJsonString(result,
            DcConstants.DC_DEFAULT_DATE_TIME_FORMAT,
            Locale.CHINA));

        return result;
    }

    private Map<String, Object> processDlt645Res4Write(String gwno, LaunchedDlt645Device device,
        byte[] resBuf, MessageContext messageContext) {
        String dno = device.getDno();
        Map<String, Object> result = new HashMap<>();
        try {
            Dlt645Data reqData = device.getWriteQ().peek();
            P645DataType reqAddr = P645DataType.valueOf(
                ByteUtils.reverseBytes(ByteUtils.hexToBytes(reqData.getAddr())));
            
            // 验证数据类型是否匹配
            if (!reqAddr.equals(messageContext.getDlt645DataType())) {
                log.warn("[{} {}] DLT645写响应数据类型不匹配. messageId: {}, 期望: {}, 实际: {}",
                    gwno, dno, messageContext.getMessageId(),
                    messageContext.getDlt645DataType(), reqAddr);
            }
            
            long res = dlt645Decoder.parseDlt645WriteRes(gwno, dno, resBuf, reqAddr);
            result.put("result", res);

        } catch (DcNeedMoreDataException e1) {
            log.info("[{} {}] 报文不完整,等待更多的报文",
                gwno, dno);
            throw e1;
        } catch (Exception e) {
            log.warn("[{} {}] 解析收到的DLT645电表数据失败",
                gwno, dno, e);
        }
        device.getWriteQ().poll();   // 移除第一个地址，已采集完成
        device.setCurMsgType(null);

        // 返回结果可能含有LocalDateTime，需要做时间格式约定
        log.debug("[{} {}] result= {}", gwno, dno, JsonUtils.toJsonString(result,
            DcConstants.DC_DEFAULT_DATE_TIME_FORMAT,
            Locale.CHINA));

        return result;
    }


    private Map<String, Object> processModbusRtuRes(ActiveGw gw, LaunchedModbusRtuDevice device,
        byte[] resBuf, MessageContext messageContext) {
        String gwno = gw.getGwno();
        String dno = device.getDno();
        ModbusAddrRange reqAddr = device.getReadQ().peek();
        Integer len = modbusRtuParser.parseHeader(gwno, dno, resBuf, device.getSid(),
            reqAddr.getReadCmd());
        int fullLen = len + ModbusRtuDecoder.MODBUS_HEAD_LEN
            + ModbusRtuDecoder.MODBUS_CRC_LEN;
        if (resBuf.length < fullLen) {
            log.info(
                "[{} {}] 接收到的数据报文不完整,还需等待更多的上报报文. fullLen= {}, resBuf.length= {}",
                gw.getGwno(), device.getDno(), fullLen, resBuf.length);
            throw new DcNeedMoreDataException();
        }



        Map<String, Object> result = modbusRtuParser.parseModbusRes(gwno, dno, reqAddr,
            device.getTvs(), resBuf, 3);

        device.getReadQ().poll();   // 移除第一个地址，已采集完成
//        device.setReqAddr(null);
        log.debug("[{} {}] addr={} ModbusRtu 采集到的数据= {}",
            gwno, dno, reqAddr.getAddr(), JsonUtils.toJsonString(result));
        return result;
    }

    private Map<String, Object> processModbusRtuTcpRes(ActiveGw gw, LaunchedModbusTcpDevice device,
        byte[] resBuf, MessageContext messageContext) {
        Map<String, Object> result = new HashMap<>();
        String gwno = gw.getGwno();
        String dno = device.getDno();

        ModbusAddrRange reqAddr = device.getReadQ().peek();
        if (resBuf == null || resBuf.length < 1) {
            device.getReadQ().poll();   // 移除第一个地址，已采集完成
//            device.setReqAddr(null);
            log.warn("[{} {}] 采集数据失败. addr= {}", gwno, dno, reqAddr.getAddr());
            return result;
        }



        result = modbusRtuParser.parseModbusRes(gwno, dno, reqAddr, device.getTvs(), resBuf, 0);

        device.getReadQ().poll();   // 移除第一个地址，已采集完成
//        device.setReqAddr(null);
        log.debug("[{} {}] addr={} 采集到的数据= {}", gwno, dno, reqAddr.getAddr(),
            JsonUtils.toJsonString(result));
        return result;
    }

    private ByteArrayOutputStream buildReqBuf(LaunchedDevice device) {
        if (Rs485Protocol.MODBUS == device.getProtocol()) {
            return this.buildModbusRtuReqBuf((LaunchedModbusRtuDevice) device);
        }
//        else if (Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
//            return this.buildModbusTcpReqBuf((LaunchedModbusTcpDevice) device);
//        }
        else if (Rs485Protocol.DLT645 == device.getProtocol()) {
            return this.buildDlt645ReqBuf((LaunchedDlt645Device) device);
        } else {
            log.error("[{} {}] 设备通信协议类型暂不支持. device= {}", device.getGwno(),
                device.getDno(), device);
            return null;
        }
    }

    /**
     * 构建下行的modbus采集指令
     */
    private ByteArrayOutputStream buildModbusRtuReqBuf(LaunchedModbusRtuDevice modbusDevice) {
        if (CollectionUtils.isEmpty(modbusDevice.getReadQ())) {
            log.warn("[{} {}] ModbusRtu设备已无待采集的指令", modbusDevice.getGwno(),
                modbusDevice.getDno());
            throw new DcServiceException("设备已无待采集的指令");
        }
        ModbusAddrRange addr = modbusDevice.getReadQ().peek();
//        modbusDevice.setReqAddr(addr);
        ByteArrayOutputStream buf = null;
        try {
            buf = modbusRtuEncoder.buildModbusReqBuf(modbusDevice.getGwno(),
                modbusDevice.getDno(), modbusDevice.getSid(),
                addr);
        } catch (Exception e) {
            log.error("[{} {}] 构建ModbusRtp下发指令失败. addr= {}",
                modbusDevice.getGwno(),
                modbusDevice.getDno(), addr);
        }
        return buf;

    }


    /**
     * 构建下行的Dlt645采集指令
     */
    private ByteArrayOutputStream buildDlt645ReqBuf(LaunchedDlt645Device p645Device) {
        if (p645Device.isEmpty()) {
            log.warn("[{} {}] DLT645设备已无待采集的指令", p645Device.getGwno(),
                p645Device.getDno());
            throw new DcServiceException("设备已无待采集的指令");
        }
        ByteArrayOutputStream buf = null;
        if (CollectionUtils.isNotEmpty(p645Device.getWriteQ())) {    // 优先发写队列里的指令
            p645Device.setCurMsgType(SouthMsgType.WRITE);
            Dlt645Data writeData = p645Device.getWriteQ().peek();
//            p645Device.setReqAddr(addr);
            try {
                P645DataType addr = P645DataType.valueOf(
                    ByteUtils.reverseBytes(ByteUtils.hexToBytes(writeData.getAddr())));
                buf = this.dlt645Encoder.encodeWriteRequest(p645Device.getGwno(),
                    p645Device.getDno(), p645Device.getHexMeterNo(), writeData.getPasscode(), addr,
                    writeData, true);
            } catch (Exception e) {
                log.error("[{} {}] 构建下发指令失败. writeData= {}",
                    p645Device.getGwno(), p645Device.getDno(),
                    writeData, e);
            }
        } else {    // 如果下行指令队列为空，发送采集数据指令
            p645Device.setCurMsgType(SouthMsgType.READ);
            P645DataType addr = p645Device.getReadQ().peek();
//            p645Device.setReqAddr(addr);
            try {
                buf = this.dlt645Encoder.buildReadRequest(p645Device.getGwno(),
                    p645Device.getDno(), p645Device.getHexMeterNo(), addr, true);
            } catch (Exception e) {
                log.error("[{} {}] 构建下发指令失败. addr= {}",
                    p645Device.getGwno(), p645Device.getDno(),
                    addr.hex());
            }
        }
        return buf;

    }

    /**
     * 将采集到的信息推送给到接收方(多个)
     */
    private void notifyResult(LaunchedDevice device, LocalDateTime ts, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        Map<String, Object> layeredData = this.toLayeredMap(data);  // 扁平结构的Map数据改为层级结构， key使用 . 分隔
        monitorData(device, layeredData);
        NotifyDataDto notifyData = new NotifyDataDto();
        notifyData.setGwno(device.getGwno())
            .setDno(device.getDno())
            .setTs(ts)
            .setData(layeredData)
            .setDeviceType(device.getDeviceType());
        for (NotifyCfg receiver : device.getNotifyCfg()) {
            this.notifyOne(device.getGwno(), device.getDno(), receiver, notifyData);
        }
    }

    /**
     * 监控重点数据日志告警
     */
    private void monitorData(LaunchedDevice device, Map<String, Object> data) {
        try {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (key.equals("pa") || key.equals("pb") || key.equals("pc") || key.equals("p")) {
                    if (new BigDecimal(value.toString()).compareTo(BigDecimal.ZERO) < 0) {
                        log.warn("[{} {}] [{}]有功功率值异常 数据为 {}", device.getGwno(), device.getDno(), key, value);
                    }
                }
            }
        } catch (Exception e) {
            log.info("[{} {}]数据监控异常 {}", device.getGwno(), device.getDno(), e.getMessage());
        }

    }

    /**
     * 将扁平结构的Map数据改为层级结构， key使用 . 分隔
     */
    private Map<String, Object> toLayeredMap(Map<String, Object> flatData) {
        Map<String, Object> out = new HashMap<>();
        for (String k : flatData.keySet()) {
            String[] toks = k.split("\\.");
            Object v = flatData.get(k);
            Map<String, Object> map = out;
            for (int idx = 0; idx < toks.length; idx++) {
                String tok = toks[idx];
                if (!map.containsKey(tok)) {
                    if (idx == (toks.length - 1)) {    // 最后一级，作为值放到map里
                        map.put(tok, v);
                    } else {
                        map.put(tok, new HashMap<String, Object>());
                        map = (Map<String, Object>) map.get(tok);
                    }
                } else {
                    map = (Map<String, Object>) map.get(tok);
                }
            }

        }
        return out;
    }

    /**
     * 将结果推送给一个接收方
     *
     * @param receiver   接受方
     * @param notifyData 推送的数据
     */
    private void notifyOne(String gwno, String dno, NotifyCfg receiver, NotifyDataDto notifyData) {
        try {
            String url = receiver.getUrl();
            if (url.startsWith("http://")
                || url.startsWith("https://")) {
                // 将采集到的信息通过http post推送给到接收方
                this.httpNotifyService.sendNotify(gwno, dno,
                    url,
                    notifyData);
            } else {
                log.warn("[{} {}] 推送地址格式不支持. receiver= {}",
                    gwno, dno,
                    JsonUtils.toJsonString(receiver));
            }
        } catch (Exception e) {
            log.warn("[{} {}] 发送推送消息失败. receiver= {}",
                gwno, dno, JsonUtils.toJsonString(receiver), e);
        }
    }

    /**
     * 将要下发的下行DLT645写指令放到队列里
     */
    public void addDlt645WriteMsg(LaunchedDlt645Device device) {
//        ByteArrayOutputStream buf = dlt645Encoder.encodeHexStringList(downData.getGwno(), )
        boolean launchNow = this.gwRepo.addDevice(device);
        if (launchNow) { // 立即下发指令
            this.sendNextRequest(device.getGwno(), LocalDateTime.now());
        }
    }
    
    /**
     * 创建消息上下文
     */
    private MessageContext createMessageContext(LaunchedDevice device) {
        if (Rs485Protocol.MODBUS == device.getProtocol()) {
            LaunchedModbusRtuDevice modbusDevice = (LaunchedModbusRtuDevice) device;
            ModbusAddrRange addrRange = modbusDevice.getReadQ().peek();
            return MessageContext.createModbusContext(device.getDno(), device.getProtocol(), 
                addrRange, modbusDevice.getSid());
        } else if (Rs485Protocol.MODBUS_RTU_TCP == device.getProtocol() 
                   || Rs485Protocol.MODBUS_TCP == device.getProtocol()) {
            LaunchedModbusTcpDevice modbusDevice = (LaunchedModbusTcpDevice) device;
            ModbusAddrRange addrRange = modbusDevice.getReadQ().peek();
            return MessageContext.createModbusContext(device.getDno(), device.getProtocol(), 
                addrRange, modbusDevice.getSid());
        } else if (Rs485Protocol.DLT645 == device.getProtocol()) {
            LaunchedDlt645Device dlt645Device = (LaunchedDlt645Device) device;
            SouthMsgType msgType = dlt645Device.getCurMsgType();
            P645DataType dataType = null;
            
            if (SouthMsgType.READ == msgType) {
                dataType = dlt645Device.getReadQ().peek();
            } else if (SouthMsgType.WRITE == msgType) {
                // 对于写操作，从写队列获取数据类型
                if (CollectionUtils.isNotEmpty(dlt645Device.getWriteQ())) {
                    Dlt645Data writeData = dlt645Device.getWriteQ().peek();
                    dataType = P645DataType.valueOf(
                        ByteUtils.reverseBytes(ByteUtils.hexToBytes(writeData.getAddr())));
                }
            }
            
            return MessageContext.createDlt645Context(device.getDno(), msgType, dataType);
        } else {
            log.error("[{} {}] 不支持的协议类型: {}", device.getGwno(), device.getDno(), device.getProtocol());
            return null;
        }
    }
}
