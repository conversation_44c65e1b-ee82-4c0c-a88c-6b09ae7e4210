package com.ht.iot.collection.north.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.ht.iot.collection.model.dto.UpdateDeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateDlt645DeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateModbusDeviceInfoDto;
import com.ht.iot.collection.north.biz.DeviceMgmBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping(value = "/iot/collection/device", produces = MediaType.APPLICATION_JSON_VALUE)
public class IotDeviceRest {

    @Autowired
    private DeviceMgmBizService deviceMgmBizService;

    /**
     * 更新Modbus采集设备信息
     *
     * @param param 被采集设备
     * @return 设备dno
     */
    @PostMapping(value = "/updateModbusDevice")
    public Mono<ObjectResponse<String>> updateModbusDevice(@RequestBody UpdateModbusDeviceInfoDto param) {
        log.info("更新基于Modbus协议的采集设备信息 = {}", JsonUtils.toJsonString(param));
        return deviceMgmBizService.updateModbusDevice(param)
            .doOnNext(dno -> log.info("[{}] 更新基于Modbus协议的设备采集信息成功", dno))
            .doOnError(err -> {
                log.error("[{} {}] 更新基于Modbus协议的采集设备信息失败", param.getGwno(), param.getDno());
            })
            .map(RestUtils::buildObjectResponse);
    }


    /**
     * 更新Dlt645采集设备信息
     *
     * @param param 被采集设备
     * @return 设备dno
     */
    @PostMapping(value = "/updateDlt645Device")
    public Mono<ObjectResponse<String>> updateDlt645Device(@RequestBody UpdateDlt645DeviceInfoDto param) {
        log.info("更新基于DLT645协议的采集设备信息 = {}", JsonUtils.toJsonString(param));
        return deviceMgmBizService.updateDlt645Device(param)
            .doOnNext(dno -> log.info("[{}] 更新基于DLT645协议的设备采集信息成功", dno))
            .doOnError(err -> {
                log.error("[{} {}] 更新基于DLT645协议的采集设备信息失败", param.getGwno(), param.getDno());
            })
            .map(RestUtils::buildObjectResponse);
    }

    @PostMapping(value = "/updateDeviceInfo")
    public Mono<ObjectResponse<String>> updateDeviceInfo(@RequestBody UpdateDeviceInfoDto param) {
        log.info("更新设备信息 dno = {}", param.getDno());
        deviceMgmBizService.updateDeviceBasicInfo(param);
        return Mono.just("true").map(RestUtils::buildObjectResponse);
    }

    /**
     * 根据dno逻辑删除设备（将enable字段设置为0，不从数据库中物理删除）
     * @param dno 设备编号
     * @return 操作结果
     */
    @GetMapping(value = "/removeDevice")
    public Mono<ObjectResponse<String>> removeDevice(@RequestParam String dno) {
        log.info("逻辑删除设备信息 dno= {}", dno);
        deviceMgmBizService.removeDevice(dno);
        return Mono.just("true")
                .map(RestUtils::buildObjectResponse);
    }


    /**
     * 给DLT645电表下发指令
     */
    @PostMapping(value = "/writeDlt645")
    public Mono<ObjectResponse<String>> writeDlt645(@RequestBody String param) {
        log.info("收到要下发的 DLT645 写指令 = {}", param);
        return deviceMgmBizService.addDlt645WriteMsg(param)
            .doOnNext(dno -> log.info("[{}] 增加 DLT645 写指令成功", dno))
            .doOnError(err -> {
                log.error("增加 DLT645 写指令失败. param= {}", param);
            })
            .map(RestUtils::buildObjectResponse);
    }

}
