package com.ht.iot.collection.south.biz.modbus.tcp;

import com.cdz360.base.utils.StringUtils;
import com.ht.iot.collection.model.ModbusTcpRes;
import com.ht.iot.collection.utils.NettyUtils;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

@Slf4j
//@Component
@ChannelHandler.Sharable
public class ModbusTcpNettyChannelHandler extends ChannelInboundHandlerAdapter {

    private ModbusTcpChannelRepo channelRepo;
    private ModbusTcpService modbusTcpService;

    public ModbusTcpNettyChannelHandler(ModbusTcpChannelRepo channelRepo,
        ModbusTcpService modbusTcpService) {
        this.channelRepo = channelRepo;
        this.modbusTcpService = modbusTcpService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
//        Assert.notNull(this.nettyChannelRepository, "[Assertion failed] - NettyChannelRepository is required; it must not be null");
//
        ctx.fireChannelActive();
        if (log.isDebugEnabled()) {
            log.debug("tcp conn: {}", ctx.channel().remoteAddress());
        }
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("channelActive. channelKey= {}", channelKey);
//        nettyChannelRepository.put(channelKey, ctx.channel());

        //ctx.writeAndFlush("Your channel key is " + channelKey + "\r\n");

        if (log.isDebugEnabled()) {
//            log.debug("Binded Channel Count is {}", this.nettyChannelRepository.size());
        }

    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        String chKey = NettyUtils.genChKey(ctx.channel());
        String gwno = null;
        if (StringUtils.isNotBlank(chKey)) {
            gwno = this.channelRepo.getGwno(chKey);
        }
        if (null == msg) {
            log.info("[{}] channelRead end. msg is null. chKey= {}", gwno, chKey);
            return;
        }
        log.info("[{}] channelRead. chKey= {}", gwno, chKey);
        if (StringUtils.isBlank(gwno)) {
            log.error("缺少网关信息. chKey= {}", chKey);
            return;
        }
        ModbusTcpRes modbusRes = (ModbusTcpRes) msg;  // 由 ModbusRtuTcpNettyDecoder 解包
        log.info("[{}] modbusTcpRes = {}", gwno, modbusRes);
        this.modbusTcpService.processModbusTcpMessage(gwno, modbusRes);
    }

//    private void sendReply(ChannelHandlerContext ctx, byte[] buf, BaseEvseMsgUp iotMsg) {
//
//        if (buf != null && buf.length > 0) {
//            asyncRequest(iotMsg.getBase().getTid(), iotMsg.getBase().getChKey(),
//                iotMsg.getBase().getEvseNo(), buf);
//        }
//
//        long endTime = System.nanoTime();
//        long indicatorsBySecond = 1000000 * 2000;// 2秒
//        long errorIndicators = 1000000 * 5000;// 5秒
//
//
//        if(endTime - iotMsg.getBase().getRecvTime() > errorIndicators) {
//            log.error("业务处理 delay xxx : {} ms, traceId:{}, msg: {}",
//                (endTime - iotMsg.getBase().getRecvTime()) / 1000000L, iotMsg.getBase().getTid(), ByteUtil.byteArrayToHexStr(buf));
//        }
//        else if (endTime - iotMsg.getBase().getRecvTime() > indicatorsBySecond) {
//            log.warn("业务处理 delay xxx : {} ms, traceId:{}, msg: {}",
//                (endTime - iotMsg.getBase().getRecvTime()) / 1000000L, iotMsg.getBase().getTid(), ByteUtil.byteArrayToHexStr(buf));
//        }
//
//        log.debug("[{}] reply to evse done. traceId: {}", iotMsg.getBase().getChKey(), iotMsg.getBase().getTid());
//    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Assert.notNull(ctx,
            "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

//        String channelKey = ctx.channel().remoteAddress().toString();
//
//        log.error("桩异常断开，上报桩离线。channelKey: {}", channelKey, cause);
//
//        evseStatusReportService.reportWhenException(channelKey);
//
//        ctx.channel().close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
//        Assert.notNull(this.nettyChannelRepository, "[Assertion failed] - NettyChannelRepository is required; it must not be null");
//        Assert.notNull(ctx, "[Assertion failed] - ChannelHandlerContext is required; it must not be null");
//
//        String channelKey = ctx.channel().remoteAddress().toString();
//
//        log.info("桩TCP连接已断开。channelKey: {}", channelKey);
//
//        //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
//        evseStatusReportService.reportWhenTimeout(channelKey);
//
//        this.nettyChannelRepository.remove(channelKey);
//        if (log.isDebugEnabled()) {
//            log.debug("Binded Channel Count is " + this.nettyChannelRepository.size());
//        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            if (((IdleStateEvent) evt).state().equals(IdleState.READER_IDLE)) {

                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                String channelKey = ctx.channel().remoteAddress().toString();
                log.info("长时间未写入数据，已将其断开。channelKey: {}", channelKey);

                // 在调用ctx.channel().close();后，netty内部的机制会触发channelInactive()事件，在channelInactive里处理状态上报的逻辑。
                // evseStatusReportService.reportWhenIdle(channelKey);
                // this.nettyChannelRepository.remove(channelKey);

                ctx.channel().close();
            }
        }

        super.userEventTriggered(ctx, evt);
    }

}
