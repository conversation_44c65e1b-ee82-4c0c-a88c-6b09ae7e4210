<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ht.iot.collection.ds.mapper.IotDeviceMapper">
    <resultMap id="holdingDeviceMap" type="com.ht.iot.collection.model.HoldingDevice">
        <result property="gwno" column="gwno"/>
        <result property="dno" column="dno"/>
        <result property="protocol" column="protocol" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
        <result property="duration" column="duration"/>
        <result property="expireDur" column="expireDur"/>
        <result property="mqttDownTopic" column="mqttDownTopic"/>
        <result property="notifyCfg" column="notifyCfg" typeHandler="com.ht.iot.collection.handler.NotifyCfgTypeHandler"/>
        <result property="tvMathComputed" column="tvMathComputed" typeHandler="com.ht.iot.collection.handler.TvMathComputedTypeHandler"/>
        <result property="combineNotify" column="combineNotify"/>
        <result property="enable" column="enable"/>
        <discriminator javaType="int" column="protocol">
            <case value="1" resultMap="modbusDeviceResultMap"/>
            <case value="2" resultMap="modbusTcpDeviceResultMap"/>
            <case value="4" resultMap="modbusTcpDeviceResultMap"/>
            <case value="11" resultMap="dlt645DeviceResultMap"/>
        </discriminator>
    </resultMap>



    <resultMap id="modbusDeviceResultMap" type="com.ht.iot.collection.model.HoldingModbusRtuDevice" extends="holdingDeviceMap">
        <result property="sid" column="sid"/>
        <result property="addrs" column="addrs" typeHandler="com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler"/>
        <result property="tvs" column="tvs" typeHandler="com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler"/>
    </resultMap>

    <resultMap id="modbusTcpDeviceResultMap" type="com.ht.iot.collection.model.HoldingModbusTcpDevice" extends="holdingDeviceMap">
        <result property="sid" column="sid"/>
        <result property="socketType" column="socketType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
        <result property="peerIp" column="peerIp"/>
        <result property="peerPort" column="peerPort"/>
        <result property="addrs" column="addrs" typeHandler="com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler"/>
        <result property="tvs" column="tvs" typeHandler="com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler"/>
    </resultMap>

    <resultMap id="dlt645DeviceResultMap" type="com.ht.iot.collection.model.HoldingDlt645Device" extends="holdingDeviceMap">
        <result property="hexMeterNo" column="serialNo"/>
        <result property="addrs" column="addrs" typeHandler="com.ht.iot.collection.handler.P645DataTypeTypeHandler"/>
        <result property="tvs" column="tvs" typeHandler="com.ht.iot.collection.handler.Dlt645TvTypeHandler"/>
    </resultMap>

    <insert id="insertDevice" parameterType="com.ht.iot.collection.model.HoldingDevice">
    INSERT INTO t_collect_equip_cfg
    (gwno, dno, protocol, deviceType, socketType, sid, peerIp, peerPort, serialNo, addrs, tvs, duration, expireDur,
     mqttDownTopic, notifyCfg, tvMathComputed, combineNotify, createTime, updateTime)
    VALUES
    (
        #{gwno},
        #{dno},
        #{protocol, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
        #{deviceType},
        <choose>
            <when test="protocol != null and protocol.code == 1">
                null,
                #{sid},
                null, null, null,
                #{addrs, typeHandler=com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler},
                #{tvs, typeHandler=com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler}
            </when>
            <when test="protocol != null and (protocol.code == 2 or protocol.code == 4)">
                #{socketType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
                #{sid},
                #{peerIp}, #{peerPort},
                null,
                #{addrs, typeHandler=com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler},
                #{tvs, typeHandler=com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler}
            </when>
            <when test="protocol != null and protocol.code == 11">
                null, null, null, null,
                #{hexMeterNo},
                #{addrs, typeHandler=com.ht.iot.collection.handler.P645DataTypeTypeHandler},
                #{tvs, typeHandler=com.ht.iot.collection.handler.Dlt645TvTypeHandler}
            </when>
            <otherwise>
                null, null, null, null, null, null, null
            </otherwise>
        </choose>
        , #{duration},
        #{expireDur},
        #{mqttDownTopic},
        #{notifyCfg, typeHandler=com.ht.iot.collection.handler.NotifyCfgTypeHandler},
        #{tvMathComputed,typeHandler=com.ht.iot.collection.handler.TvMathComputedTypeHandler},
        #{combineNotify},
        now(),
        now()
    )
    </insert>

    <update id="updateDevice" parameterType="com.ht.iot.collection.model.HoldingDevice">
        UPDATE t_collect_equip_cfg
        <set>
            <if test="gwno != null and gwno != ''">
                gwno = #{gwno},
            </if>
            <if test="protocol != null">
                protocol = #{protocol, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
            </if>
            <if test="deviceType != null">
                deviceType = #{deviceType},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="expireDur != null">
                expireDur = #{expireDur},
            </if>
            <if test="mqttDownTopic != null">
                mqttDownTopic = #{mqttDownTopic},
            </if>
            <if test="notifyCfg != null">
                notifyCfg = #{notifyCfg, typeHandler=com.ht.iot.collection.handler.NotifyCfgTypeHandler},
            </if>
            <if test="tvMathComputed != null">
                tvMathComputed = #{tvMathComputed,typeHandler=com.ht.iot.collection.handler.TvMathComputedTypeHandler},
            </if>
            <if test="combineNotify != null">
                combineNotify = #{combineNotify},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>

            <!-- 动态协议字段处理 -->
            <choose>
                <!-- Modbus协议的特殊字段 -->
                <when test="protocol != null and protocol.code == 1">
                    <if test="sid != null">
                        sid = #{sid},
                    </if>
                    <if test="addrs != null">
                        addrs = #{addrs, typeHandler=com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler},
                    </if>
                    <if test="tvs != null">
                        tvs = #{tvs, typeHandler=com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler},
                    </if>
                    serialNo = null, <!-- 确保切换协议时清空其他协议字段 -->
                </when>
                <when test="protocol != null and (protocol.code == 2 or protocol.code == 4)">
                    <if test="sid != null">
                        sid = #{sid},
                    </if>
                    <if test="socketType != null">
                        socketType = #{socketType, typeHandler=com.cdz360.ds.type.EnumTypeHandler},
                    </if>
                    <if test="peerIp != null">
                        peerIp = #{peerIp},
                    </if>
                    <if test="peerPort != null">
                        peerPort = #{peerPort},
                    </if>
                    <if test="addrs != null">
                        addrs = #{addrs, typeHandler=com.ht.iot.collection.handler.ModbusAddrRangeTypeHandler},
                    </if>
                    <if test="tvs != null">
                        tvs = #{tvs, typeHandler=com.ht.iot.collection.handler.ModbusAbstractTvTypeHandler},
                    </if>
                    serialNo = null, <!-- 确保切换协议时清空其他协议字段 -->
                </when>


                <!-- DLT645协议的特殊字段 -->
                <when test="protocol != null and protocol.code == 11">
                    <if test="hexMeterNo != null and hexMeterNo != ''">
                        serialNo = #{hexMeterNo},
                    </if>
                    <if test="addrs != null">
                        addrs = #{addrs, typeHandler=com.ht.iot.collection.handler.P645DataTypeTypeHandler},
                    </if>
                    <if test="tvs != null">
                        tvs = #{tvs, typeHandler=com.ht.iot.collection.handler.Dlt645TvTypeHandler},
                    </if>
                    sid = null, <!-- 清空其他协议字段 -->
                </when>
            </choose>
            updateTime = NOW()
        </set>
        WHERE dno = #{dno}
    </update>

    <update id="deleteDeviceByDno">
        update t_collect_equip_cfg
        set enable = 0, updateTime = NOW()
        where dno = #{dno,jdbcType=VARCHAR}
    </update>

    <select id="getIdByDno" resultType="java.lang.Long">
        select id from t_collect_equip_cfg
        where dno = #{dno}
    </select>

    <select id="getAllDevicesByPage" resultMap="holdingDeviceMap">
        SELECT * FROM t_collect_equip_cfg
        WHERE id > #{lastId} AND enable = 1
        ORDER BY id
        LIMIT 100000
    </select>

</mapper>