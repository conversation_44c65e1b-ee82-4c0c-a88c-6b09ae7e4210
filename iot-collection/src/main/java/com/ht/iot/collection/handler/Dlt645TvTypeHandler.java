package com.ht.iot.collection.handler;

import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(List.class)
public class Dlt645TvTypeHand<PERSON> extends AbstractJsonTypeHandler<List<Dlt645Tv>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<Dlt645Tv> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting Dlt645Tv list to JSON", e);
        }
    }

    protected List<Dlt645Tv> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<Dlt645Tv>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing Dlt645Tv JSON: " + json, e);
        }
    }
}