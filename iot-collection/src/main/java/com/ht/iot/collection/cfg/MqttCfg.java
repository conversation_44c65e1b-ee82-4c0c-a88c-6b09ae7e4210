package com.ht.iot.collection.cfg;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class MqttCfg {

//    @Value("${env}")
//    private String env;


    @Value("${iot.collect.mqtt.name}")
    private String name;

    @Value("${iot.collect.mqtt.serverUrl}")
    private String url;


    @Value("${iot.collect.mqtt.clientId}")
    private String clientId;

//    @Value("${eureka.instance.hostname}")
//    private String hostIp;

    @Value("${iot.collect.mqtt.username}")
    private String username;

    @Value("${iot.collect.mqtt.password}")
    private String password;

    @Value("${iot.collect.mqtt.upTopic}")
    private String upTopic;

}
