package com.ht.iot.collection.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import io.netty.buffer.ByteBuf;

public class ByteBufUtils {

    public static long byteBuf2Long(ByteBuf bufIn) {
        return byteBuf2Long(bufIn, 4);
    }

    public static long byteBuf2Long(ByteBuf bufIn, int len) {
        long result = 0L;
        for (int i = len - 1; i > -1; i--) {
            result <<= 8;
            result |= (bufIn.getByte(i + bufIn.readerIndex()) & 0xFF);
        }
        bufIn.skipBytes(len);
        return result;
    }

    public static long byteBuf2LongBE(ByteBuf bufIn) {
        return byteBuf2LongBE(bufIn, 4);
    }

    public static long byteBuf2LongBE(ByteBuf bufIn, int len) {
        long result = 0L;
        for (int i = 0; i < len; i++) {
            result <<= 8;
            result |= (bufIn.getByte(i + bufIn.readerIndex()) & 0xFF);
        }
        bufIn.skipBytes(len);
        return result;
    }

    public static int byteBuf2Int(ByteBuf bufIn) {
        return byteBuf2Int(bufIn, 4);
    }

    public static int byteBuf2Int(ByteBuf bufIn, int len) {
        int result = 0;
        for (int i = len - 1; i > -1; i--) {
            result <<= 8;
            result |= (bufIn.getByte(i + bufIn.readerIndex()) & 0xFF);
        }
        bufIn.skipBytes(len);
        return result;
    }

    public static int byteBuf2IntBE(ByteBuf bufIn) {
        return byteBuf2IntBE(bufIn, 4);
    }

    public static int byteBuf2IntBE(ByteBuf bufIn, int len) {
        int result = 0;
        for (int i = 0; i < len; i++) {
            result <<= 8;
            result |= (bufIn.getByte(i + bufIn.readerIndex()) & 0xFF);
        }
        bufIn.skipBytes(len);
        return result;
    }
    public static int byteBuf2IntBE(ByteBuf bufIn, int offset, int len) {
        if(len == 2) {
            return bufIn.getUnsignedShort(offset);
        }else {
            throw new DcServiceException("参数错误");
        }
    }

    public static int crc16(ByteBuf buf, int len) {
        int crc = 65535;

        for(int i = 0; i < len; ++i) {
            int c = buf.getByte(i) & 255;
            crc ^= c;

            for(int j = 0; j < 8; ++j) {
                if ((crc & 1) != 0) {
                    crc >>= 1;
                    crc ^= 40961;
                } else {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }
}
