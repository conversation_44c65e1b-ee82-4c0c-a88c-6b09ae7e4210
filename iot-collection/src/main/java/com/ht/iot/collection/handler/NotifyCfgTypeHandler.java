package com.ht.iot.collection.handler;

import com.ht.iot.collection.model.dto.NotifyCfg;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(List.class)
public class NotifyCfgTypeHandler extends AbstractJsonTypeHandler<List<NotifyCfg>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<NotifyCfg> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting NotifyCfg list to JSON", e);
        }
    }

    protected List<NotifyCfg> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<NotifyCfg>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing NotifyCfg JSON: " + json, e);
        }
    }
}