package com.ht.iot.collection.south.biz.modbus.tcp;

import io.micrometer.tracing.Tracer;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Component
//@Qualifier("modbusTcpChannelInitializer")
public class ModbusTcpChannelInitializer extends ChannelInitializer<SocketChannel> {

    private static final EventExecutorGroup group;

    static {
        final int nThreads = Runtime.getRuntime().availableProcessors();
        group = new DefaultEventExecutorGroup(nThreads * 5);
    }

    private Tracer tracer;
    private ModbusTcpChannelRepo channelRepo;
    private ModbusTcpService modbusTcpService;

    public ModbusTcpChannelInitializer(Tracer tracer, ModbusTcpChannelRepo channelRepo,
        ModbusTcpService modbusTcpService) {
        this.tracer = tracer;
        this.channelRepo = channelRepo;
        this.modbusTcpService = modbusTcpService;
    }

    @Override
    protected void initChannel(SocketChannel socketChannel) {
        ChannelPipeline pipeline = socketChannel.pipeline();

        long timeout = 600;  //initCheckClientTimeout();
        pipeline.addLast(new IdleStateHandler(timeout, 0, 0, TimeUnit.SECONDS));

//        pipeline.addLast(new CePackageDecoder(1024*10, 17, 2));
        pipeline.addLast(new LengthFieldBasedFrameDecoder(1024 * 5, 4, 2, 0, 0));
        pipeline.addLast(new ModbusTcpNettyDecoder(this.tracer, this.channelRepo));
        pipeline.addLast(new ModbusTcpNettyEncoder(this.channelRepo));

        pipeline.addLast(group, "handler",
            new ModbusTcpNettyChannelHandler(this.channelRepo,
                this.modbusTcpService));// 将I/O线程和业务线程分开
    }

}
