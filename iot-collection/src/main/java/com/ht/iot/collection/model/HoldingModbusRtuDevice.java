package com.ht.iot.collection.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAbstractTv;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HoldingModbusRtuDevice extends HoldingDevice {

    /**
     * modbus通信id
     */
    private Integer sid;


    /**
     * 需采集的地址列表
     */
    private List<ModbusAddrRange> addrs;

    /**
     * 要映射的modbus值
     */
    private List<ModbusAbstractTv> tvs;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
