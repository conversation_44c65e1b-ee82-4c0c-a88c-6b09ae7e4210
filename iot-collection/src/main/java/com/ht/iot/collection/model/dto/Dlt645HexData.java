package com.ht.iot.collection.model.dto;

import com.ht.iot.collection.model.type.Dlt645WriteDataType;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class Dlt645HexData extends Dlt645Data {

    private String data;

    @Override
    public Dlt645WriteDataType getType() {
        return Dlt645WriteDataType.HEX;
    }
}
