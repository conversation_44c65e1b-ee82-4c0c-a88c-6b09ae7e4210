package com.ht.iot.collection.south.biz.modbus.rtuTcp;

import com.cdz360.base.utils.ByteUtils;
import com.ht.iot.collection.model.ModbusRtuRes;
import com.ht.iot.collection.utils.ByteBufUtils;
import com.ht.iot.collection.utils.NettyUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.context.annotation.Scope;

@Slf4j
//@Component
//@Scope(value = "singleton")
@ChannelHandler.Sharable
public class ModbusRtuTcpNettyDecoder extends MessageToMessageDecoder<ByteBuf> {

    private static final int MAX_FRAME_SIZE = 1024 * 10; // 10M

    private ModbusRtuTcpChannelRepo channelRepo;

    public ModbusRtuTcpNettyDecoder(ModbusRtuTcpChannelRepo channelRepo) {
        this.channelRepo = channelRepo;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) {
        String tid = RandomStringUtils.randomAlphabetic(12);
        String chKey = NettyUtils.genChKey(ctx.channel());
        String gwno = this.channelRepo.getGwno(chKey);

        String rawBytes = ByteBufUtil.hexDump(msg);
        log.info("[{}] 收到报文 tid = {} , chKey: {} , bytes: {}", gwno, tid, chKey,
            rawBytes);

        ModbusRtuRes modbusRes = new ModbusRtuRes();
        modbusRes.setTs(LocalDateTime.now())
            .setSid(msg.readByte() & 0xFF)
            .setCmd(msg.readByte())
            .setLen(msg.readByte() & 0xFF);
        modbusRes.setData(new byte[modbusRes.getLen()]);
        msg.readBytes(modbusRes.getData());
        modbusRes.setCrc(msg.readUnsignedShortLE());


        int crc = ByteBufUtils.crc16(msg, 3 + modbusRes.getLen());  // 3 为设备ID、指令码、长度3个字节
        if (crc != modbusRes.getCrc()) {
            log.error("[{}] CRC校验不匹配. 报文crc = {}, 计算crc = {}, 报文 = {}",
                gwno, modbusRes.getCrc(),
                crc, ByteBufUtil.hexDump(msg));
//            throw new DcServiceException("CRC校验不匹配");
            return;
        }
        out.add(modbusRes);

    }

}
