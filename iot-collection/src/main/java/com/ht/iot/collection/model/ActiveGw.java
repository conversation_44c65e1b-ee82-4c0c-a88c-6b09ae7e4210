package com.ht.iot.collection.model;

import io.netty.channel.Channel;
import java.time.LocalDateTime;
import java.util.Queue;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活跃的网关，即处于数据采集状态的网关
 */
@Data
@Accessors(chain = true)
public class ActiveGw {

    private String gwno;
    /**
     * 最后一次发送请求的时间
     */
    private LocalDateTime reqTime;
    /**
     * 对应最后一次请求指令的超时时间
     */
    private LocalDateTime expireTime;

    /**
     * 收到的不完整数据
     */
    private byte[] slice;

    private Queue<LaunchedDevice> devices;

    private Channel nettyChannel;
    
    /**
     * 当前发送消息的上下文信息，用于消息匹配验证
     */
    private MessageContext currentMessageContext;

    public boolean isConnected() {
        if (this.nettyChannel == null) {
            return false;
        } else {
            return this.nettyChannel.isActive();
        }
    }

    public void disconnect() {
        if (this.isConnected()) {
            try {
                this.nettyChannel.disconnect();
            } catch (Exception e) {
                // ignore exception
            }
        }
    }
}
