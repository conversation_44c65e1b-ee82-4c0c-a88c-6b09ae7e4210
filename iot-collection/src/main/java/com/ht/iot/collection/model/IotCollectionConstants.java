package com.ht.iot.collection.model;

import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import java.util.List;

public class IotCollectionConstants {

    /**
     * modbus 协议
     */
    public static List<Rs485Protocol> MODBUS_PROTOCOLS = List.of(Rs485Protocol.MODBUS,
        Rs485Protocol.MODBUS_RTU_TCP, Rs485Protocol.MODBUS_TCP);

    /**
     * modbusRtu 相关的协议类型
     */
    public static List<Rs485Protocol> MODBUS_RTU_PROTOCOLS = List.of(Rs485Protocol.MODBUS,
        Rs485Protocol.MODBUS_RTU_TCP);

    /**
     * 使用tcp协议的类型
     */
    public static List<Rs485Protocol> TCP_PROTOCOLS = List.of(Rs485Protocol.MODBUS_RTU_TCP,
        Rs485Protocol.MODBUS_TCP);

}
