package com.ht.iot.collection.model;

import com.cdz360.base.utils.ByteUtils;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ModbusRtuRes {


    private int sid;
    private byte cmd;
    private int len;
    private byte[] data;
    private int crc;

    /**
     * 服务器端收到消息的时间
     */
    private LocalDateTime ts;

    @Override
    public String toString() {
        StringBuilder buf = new StringBuilder();
        buf.append(ModbusRtuRes.class.getSimpleName())
            .append("(sid=").append(this.sid)
            .append(",cmd=").append(this.cmd)
            .append(",len=").append(this.len)
            .append(",data=").append(ByteUtils.bytesToHex(this.data))
            .append(",crc=").append(this.crc)
            .append(")");
        return buf.toString();
    }
}
