package com.ht.iot.collection.south.biz.modbus.rtuTcp;

import com.cdz360.base.utils.StringUtils;
import com.ht.iot.collection.utils.NettyUtils;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import java.nio.ByteOrder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModbusRtuTcpPackageDecoder extends LengthFieldBasedFrameDecoder {

    private Tracer tracer;
    private ModbusRtuTcpChannelRepo channelRepo;


    public ModbusRtuTcpPackageDecoder(Tracer tracer, ModbusRtuTcpChannelRepo channelRepo,
        int maxFrameLength, int lengthFieldOffset,
        int lengthFieldLength) {
        super(maxFrameLength, lengthFieldOffset, lengthFieldLength);
        this.tracer = tracer;
        this.channelRepo = channelRepo;
    }

    public ModbusRtuTcpPackageDecoder(Tracer tracer, ModbusRtuTcpChannelRepo channelRepo,
        int maxFrameLength, int lengthFieldOffset,
        int lengthFieldLength,
        int lengthAdjustment, int initialBytesToStrip) {
        super(maxFrameLength, lengthFieldOffset, lengthFieldLength, lengthAdjustment,
            initialBytesToStrip);
        this.tracer = tracer;
        this.channelRepo = channelRepo;
    }

    public ModbusRtuTcpPackageDecoder(Tracer tracer, ModbusRtuTcpChannelRepo channelRepo,
        int maxFrameLength, int lengthFieldOffset,
        int lengthFieldLength,
        int lengthAdjustment, int initialBytesToStrip, boolean failFast) {
        super(maxFrameLength, lengthFieldOffset, lengthFieldLength, lengthAdjustment,
            initialBytesToStrip, failFast);
        this.tracer = tracer;
        this.channelRepo = channelRepo;
    }

    public ModbusRtuTcpPackageDecoder(Tracer tracer, ModbusRtuTcpChannelRepo channelRepo,
        ByteOrder byteOrder, int maxFrameLength,
        int lengthFieldOffset,
        int lengthFieldLength, int lengthAdjustment, int initialBytesToStrip, boolean failFast) {
        super(byteOrder, maxFrameLength, lengthFieldOffset, lengthFieldLength, lengthAdjustment,
            initialBytesToStrip, failFast);
        this.tracer = tracer;
        this.channelRepo = channelRepo;
    }

    @Override
    protected Object decode(ChannelHandlerContext ctx, ByteBuf in) throws Exception {
        Object result = null;
        Span span = this.tracer.nextSpan().name("ModbusRtuTcpPackageDecoder");
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String chKey = NettyUtils.genChKey(ctx);
            String gwno = null;
            if (StringUtils.isNotBlank(chKey)) {
                gwno = this.channelRepo.getGwno(chKey);
            }
            log.info("[{}] chKey= {}, 收到消息: {}",
                gwno, chKey, ByteBufUtil.hexDump(in));
            int readerIndex = in.readerIndex();
            int readableBytes = in.readableBytes();
            log.info("[{}] 1 readerIndex = {}, readableBytes = {}", gwno, readerIndex,
                readableBytes);
            // 常熟高压的宽域通信管理机会发送前置的0XFF,需要自动过滤掉. 此处跳过所有前导的 0xFF
            while (readerIndex < in.writerIndex() && in.getByte(readerIndex) == (byte) 0xFF) {
                readerIndex++;
            }

            // 设置新的 readerIndex
            in.readerIndex(readerIndex);

            log.info("[{}] 2 readerIndex = {}, readableBytes = {}", gwno, readerIndex,
                in.readableBytes());

            in.readerIndex(readerIndex);

            result = super.decode(ctx, in);
            log.info("[{}] frame = {}", gwno, result);

        } catch (Exception e) {
            log.warn("exception = {}", e.getMessage());
            throw e;
        } finally {
            span.end();
        }
        return result;
    }
}
