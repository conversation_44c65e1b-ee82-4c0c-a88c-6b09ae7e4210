package com.ht.iot.collection.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.model.meter.dto.Dlt645Tv;
import com.cdz360.iot.model.modbus.dto.ModbusAbstractTv;
import com.cdz360.iot.model.modbus.dto.ModbusBcdTv;
import com.cdz360.iot.model.modbus.dto.ModbusDecimalTv;
import com.cdz360.iot.model.modbus.dto.ModbusIeee754Tv;
import com.cdz360.iot.model.modbus.dto.ModbusIntegerTv;
import com.cdz360.iot.model.modbus.dto.ModbusLongTv;
import com.cdz360.iot.model.modbus.dto.ModbusNumberTv;
import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.cdz360.iot.model.modbus.type.ModbusValueOrder;
import com.cdz360.iot.model.modbus.type.P645DataType;
import com.cdz360.iot.model.modbus.type.Rs485Protocol;
import com.fasterxml.jackson.databind.JsonNode;
import com.ht.iot.collection.model.HoldingDevice;
import com.ht.iot.collection.model.HoldingDlt645Device;
import com.ht.iot.collection.model.HoldingModbusRtuDevice;
import com.ht.iot.collection.model.HoldingModbusTcpDevice;
import com.ht.iot.collection.model.IotCollectionConstants;
import com.ht.iot.collection.model.LaunchedDlt645Device;
import com.ht.iot.collection.model.dto.Dlt645Data;
import com.ht.iot.collection.model.dto.Dlt645HexData;
import com.ht.iot.collection.model.dto.Dlt645HexListData;
import com.ht.iot.collection.model.dto.Dlt645TvCfg;
import com.ht.iot.collection.model.dto.ModbusTvCfg;
import com.ht.iot.collection.model.dto.UpdateDeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateDlt645DeviceInfoDto;
import com.ht.iot.collection.model.dto.UpdateModbusDeviceInfoDto;
import com.ht.iot.collection.model.type.Dlt645WriteDataType;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IotDeviceInfoParser {

    public HoldingDevice parseDlt645DeviceInfo(UpdateDlt645DeviceInfoDto param) {
        HoldingDevice device = null;
        if (Rs485Protocol.DLT645 != param.getProtocol()) {
            log.error("[{}] 设备通信协议类型暂不支持. param= {}", param.getGwno(), param);
            throw new DcArgumentException("设备通信协议类型暂不支持");
        }
        device = this.buildDlt645Device(param);
        device = toHoldingDevice(device, param);
        return device;
    }


    public HoldingDevice parseModbusDeviceInfo(UpdateModbusDeviceInfoDto param) {
        if (!IotCollectionConstants.MODBUS_PROTOCOLS.contains(param.getProtocol())) {
            log.error("[{}] 设备通信协议类型暂不支持,需传递modbus协议. param= {}", param.getGwno(),
                param);
            throw new DcArgumentException("设备通信协议类型暂不支持");
        }
        HoldingDevice device = this.buildModbusDevice(param);
        device = toHoldingDevice(device, param);
        return device;
    }

    private HoldingDevice toHoldingDevice(HoldingDevice target, UpdateDeviceInfoDto source) {
        if (StringUtils.isBlank(target.getGwno())) {
            target.setGwno(source.getGwno());
        }
        target.setDno(source.getDno())
            .setDuration(source.getDuration())
            .setExpireDur(source.getExpireDur())
            .setMqttDownTopic(source.getMqttDownTopic())
            .setNotifyCfg(source.getNotifyCfg())
            .setCombineNotify(source.getCombineNotify())
            .setTvMathComputed(source.getTvMathComputed())
            .setDeviceType(source.getDeviceType())
            .setEnable(source.getEnable());
        return target;
    }

    private HoldingDevice buildModbusDevice(UpdateModbusDeviceInfoDto param) {
        HoldingModbusRtuDevice result;
        if (Rs485Protocol.MODBUS == param.getProtocol()) {
            result = new HoldingModbusRtuDevice();
        } else if (IotCollectionConstants.TCP_PROTOCOLS.contains(param.getProtocol())) {
            HoldingModbusTcpDevice device = new HoldingModbusTcpDevice();
            device.setSocketType(param.getSocketType())
                .setPeerIp(param.getPeerIp())
                .setPeerPort(param.getPeerPort());
            if (param.getPeerPort() == null) {
                device.setGwno(param.getGwno());
            } else {
                device.setGwno(param.getGwno() + "_" + param.getPeerPort());
            }
            result = device;
        } else {
            log.error("[{} {}] 不支持的通信协议. param= {}",
                param.getGwno(), param.getDno(), param);
            return null;
        }
        result.setSid(param.getSid())
            .setAddrs(param.getAddrs())
            .setTvs(this.parseModbusTvList(param.getTvs()))
            .setTvMathComputed(param.getTvMathComputed())
            .setProtocol(param.getProtocol());
        return result;
    }

    /**
     *
     */
    public List<ModbusAbstractTv> parseModbusTvList(List<ModbusTvCfg> jsonIn) {
        if (jsonIn == null) {
            return null;
        } else if (CollectionUtils.isEmpty(jsonIn)) {
            log.warn("modbus字段配置错误");
            return null;
        }
        List<ModbusAbstractTv> result = new ArrayList<>();
        int size = jsonIn.size();
        for (int i = 0; i < size; i++) {
            ModbusTvCfg node = jsonIn.get(i);
            ModbusAbstractTv av = null;
            ModbusDataType type = node.getT();
            if (type == ModbusDataType.UINT16) {
                av = new ModbusIntegerTv();
            } else if (type == ModbusDataType.INT64
                || type == ModbusDataType.UINT32) {
                av = new ModbusLongTv();
            } else if (type == ModbusDataType.DECIMAL ||
                    type == ModbusDataType.INT16 ||
                    type == ModbusDataType.INT32
            ) {
                ModbusDecimalTv dv = new ModbusDecimalTv();
                av = dv;
                dv.setDecimal(node.getDecimal());
            } else if (type == ModbusDataType.BCD) {
                ModbusBcdTv dv = new ModbusBcdTv();
                av = dv;
                dv.setDecimal(node.getDecimal());
            } else if (type == ModbusDataType.IEEE754) {
                ModbusIeee754Tv dv = new ModbusIeee754Tv();
                av = dv;
            }

            if (av != null && av instanceof ModbusNumberTv) {
                ModbusNumberTv nv = (ModbusNumberTv) av;
                if (StringUtils.isNotBlank(node.getOrder())) {
                    nv.setOrder(ModbusValueOrder.valueOf(node.getOrder()));
                }
                if (node.getMultiple() != null) {
                    nv.setMultiple(node.getMultiple());
                }
                if (node.getShift() != null) {
                    nv.setShift(node.getShift());
                }
            }
            if (av != null) {
                av.setName(node.getName())
                    .setAddr(node.getAddr())
                    .setNum(node.getNum())
                    .setT(node.getT().getCode())
                    .setIsPushData(node.getIsPushData());
                result.add(av);
            }
        }

        return result;
    }


    private HoldingDlt645Device buildDlt645Device(UpdateDlt645DeviceInfoDto param) {
        HoldingDlt645Device device = new HoldingDlt645Device();
        device.setHexMeterNo(param.getMeterNo())
            .setMeterNoBytes(ByteUtils.reverseBytes(ByteUtils.hexToBytes(param.getMeterNo())))
            .setAddrs(new ArrayList<>())
            .setTvs(new ArrayList<>())
            .setTvMathComputed(param.getTvMathComputed())
            .setProtocol(Rs485Protocol.DLT645);
        for (String hexAddr : param.getAddrs()) {
            P645DataType addr = P645DataType.valueOf(
                ByteUtils.reverseBytes(ByteUtils.hexToBytes(hexAddr)));
            if (P645DataType.UNKNOWN == addr) {
                log.warn("[{}] 不支持的地址: {}", param.getDno(), hexAddr);
                throw new DcArgumentException("地址不支持. addr= " + hexAddr);
            }
            device.getAddrs().add(addr);
        }
        for (Dlt645TvCfg tvIn : param.getTvs()) {
            P645DataType tvAddr = P645DataType.valueOf(
                ByteUtils.reverseBytes(ByteUtils.hexToBytes(tvIn.getAddr())));
            if (P645DataType.UNKNOWN == tvAddr) {
                log.warn("[{}] 不支持的地址: {}", param.getDno(), tvIn.getAddr());
                throw new DcArgumentException("地址不支持. addr= " + tvIn.getAddr());
            }
            Dlt645Tv tv = new Dlt645Tv();
            tv.setAddr(tvAddr)
                .setMultiple(tvIn.getMultiple())
                .setName(tvIn.getName())
                .setIsPushData(tvIn.getIsPushData());
            device.getTvs().add(tv);
        }
        return device;
    }

    /**
     * 解析下行指令的参数
     */
    public LaunchedDlt645Device parseDlt645WriteMsg(String param) {
        JsonNode json = JsonUtils.fromJson(param);
        Dlt645Data writeData = null;
        if (json.get("type").asInt() == Dlt645WriteDataType.HEX.getCode()) {
            writeData = JsonUtils.fromJson(json, Dlt645HexData.class);
        } else if (json.get("type").asInt() == Dlt645WriteDataType.HEX_LIST.getCode()) {
            writeData = JsonUtils.fromJson(json, Dlt645HexListData.class);
        } else {
            log.error("不支持的下行数据类型, param= {}", param);
            return null;
        }
        if (writeData.getPasscode() == null || writeData.getPasscode().length() != 6) {
            log.warn("[{} {}] 参数错误,密码必须为6位数字", writeData.getGwno(), writeData.getDno());
            throw new DcArgumentException("参数错误,密码必须为6位数字");
        }
        LaunchedDlt645Device result = new LaunchedDlt645Device();
        result.setHexMeterNo(writeData.getMeterNo())
            .setProtocol(Rs485Protocol.DLT645)
            .setGwno(writeData.getGwno())
            .setDno(writeData.getDno())
            .setExpireDur(writeData.getExpireDur())
            .setMqttDownTopic(writeData.getMqttDownTopic())
            .setNotifyCfg(writeData.getNotifyCfg());
        result.getWriteQ().add(writeData);
        return result;
    }
}
