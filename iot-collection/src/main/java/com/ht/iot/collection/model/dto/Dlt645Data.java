package com.ht.iot.collection.model.dto;

import com.ht.iot.collection.model.type.Dlt645WriteDataType;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public abstract class Dlt645Data {

    private String seq;

    @NotNull(message = "网关编号不能为空")
    private String gwno;

    /**
     * 设备编号. 空表示新设备
     */
    @NotNull(message = "设备编号不能为空")
    private String dno;

    /**
     * hex格式的电表表号，6字节
     */
    @NotNull(message = "电表编号不能为空")
    private String meterNo;

    /**
     * 电表密码6位数字
     */
    private String passcode;

    /**
     * HEX格式4字节地址, DI3,DI2,DI1,DI0
     */
    private String addr;

    /**
     * 超时等待时间，单位秒
     */
    private Long expireDur;

    /**
     * 下行（发送给设备）的mqtt topic
     */
    private String mqttDownTopic;

    /**
     * 推送采集数据的地址, 支持 http(s)://
     */
    private List<NotifyCfg> notifyCfg;


    public abstract Dlt645WriteDataType getType();
}
