package com.ht.iot.collection.handler;


import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(List.class)
public class ModbusAddrRangeTypeHandler extends AbstractJsonTypeHandler<List<ModbusAddrRange>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<ModbusAddrRange> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new SQLException("Error converting ModbusAddrRange list to JSON", e);
        }
    }

    protected List<ModbusAddrRange> parseJson(String json) {
        try {
            return json != null ?
                    objectMapper.readValue(json, new TypeReference<List<ModbusAddrRange>>() {})
                    : null;
        } catch (Exception e) {
            throw new RuntimeException("Error parsing ModbusAddrRange JSON: " + json, e);
        }
    }
}