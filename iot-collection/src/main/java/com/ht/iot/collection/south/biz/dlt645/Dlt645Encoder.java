package com.ht.iot.collection.south.biz.dlt645;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.iot.model.modbus.type.P645DataType;
import com.ht.iot.collection.model.dto.Dlt645Data;
import com.ht.iot.collection.model.dto.Dlt645HexListData;
import com.ht.iot.collection.model.type.Dlt645WriteDataType;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class Dlt645Encoder {

//    public static void main(String[] args) {
//        Dlt645Encoder encoder = new Dlt645Encoder();
//        String deviceAddr = "403100200523";
//        byte[] byteArrayLittle = encoder.encodeMeterNo("xxxxxxx", deviceAddr);
//
//        byte[] bytes = new Dlt645Encoder().add33(P645DataType.COMM_ADDRESS.getDataType());
//
////        String msg = "68 40 31 00 20 05 23 68 11 04 33 33 33 33";
//        String msg = "6840310020052368110437333734";
//        byte[] s = ByteUtils.hexToBytes(msg.replaceAll(" ", ""));
//
//        int cs = 0;
//        for (byte b : s) {
//            cs += (b & 0xFF);
//        }
//        // 计算CS校验和
//        System.out.println("cs: " + Integer.toHexString((byte) (cs % 256) & 0xFF));
//    }

    /**
     * 将hex编码的表号转为byte,并调换字节顺序
     *
     * @return 表号的6个字节
     */
    private byte[] encodeMeterNo(String gwno, String dno, String meterNo) {
        if (meterNo == null) {
            log.error("[{} {}] 电表表号不能为空.", gwno, dno);
            throw new DcArgumentException("电表表号不能为空");
        } else if (meterNo.length() != Dlt645Constants.METER_NO_LENGTH * 2) {
            log.error("[{} {}] 电表表号长度异常,表号= {}, 期望的表号长度为 {} 字节.",
                gwno, dno, meterNo, Dlt645Constants.METER_NO_LENGTH);
            throw new DcArgumentException("电表表号长度异常");
        }
        byte[] result = new byte[Dlt645Constants.METER_NO_LENGTH];
        byte[] bytes = ByteUtils.hexToBytes(meterNo);
        if (bytes == null || bytes.length != Dlt645Constants.METER_NO_LENGTH) {
            log.error("[{} {}] 电表表号长度异常,表号= {}, 期望的表号长度为 {} 字节.",
                gwno, dno, meterNo, Dlt645Constants.METER_NO_LENGTH);
            throw new DcArgumentException("电表表号长度异常");
        }
        result[0] = bytes[5];
        result[1] = bytes[4];
        result[2] = bytes[3];
        result[3] = bytes[2];
        result[4] = bytes[1];
        result[5] = bytes[0];
        return result;
    }

    public ByteArrayOutputStream buildReadRequest(String gwno, String dno,
        String meterNo,
        P645DataType p645DataType, boolean wLog) throws IOException {
        byte[] leadBytes = Dlt645Constants.BYTE_DLT645_HEAD_4;
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        buf.write(leadBytes);

        buf.write(Dlt645Constants.BYTE_DLT645_START);
        buf.write(this.encodeMeterNo(gwno, dno, meterNo));
        buf.write(Dlt645Constants.BYTE_DLT645_START);
        buf.write(Dlt645Constants.BYTE_DLT645_READ_CMD);

        int dataLength = p645DataType.getDataType().length;
        buf.write((byte) (dataLength & 0xFF));

        buf.write(add33(p645DataType.getDataType()));   // 数据部分每个字节都要加0x33
        int cs = 0;
        byte[] byteArray = buf.toByteArray();
        for (int i = 0; i < byteArray.length; i++) {
            if (i < leadBytes.length) {
                // 忽略前导字节
                continue;
            }
            cs += (byteArray[i] & 0xFF);
        }
        buf.write((byte) (cs % 256) & 0xFF);
        buf.write(Dlt645Constants.BYTE_DLT645_END);
        return buf;
    }

    /**
     * 构建下行的写指令
     */
    public ByteArrayOutputStream encodeWriteRequest(String gwno, String dno, String meterNo,
        String passcode,
        P645DataType p645DataType, Dlt645Data writeData, boolean wLog) throws IOException {
        if (Dlt645WriteDataType.HEX_LIST == writeData.getType()) {
            Dlt645HexListData hexListData = (Dlt645HexListData) writeData;
            return this.encodeHexStringList(gwno, dno, meterNo, passcode, p645DataType, hexListData.getData(),
                wLog);
        } else {
            log.error("[{} {}] 不支持的下行指令数据类型. writeDataType= {}",
                gwno, dno, writeData.getType());
            throw new DcServiceException("不支持的下行指令数据类型");
        }
    }

    private ByteArrayOutputStream encodeHexStringList(String gwno, String dno,
        String meterNo, String passcode,
        P645DataType p645DataType,
        List<String> hexData,
        boolean wLog) throws IOException {
        ByteArrayOutputStream dataBuf = new ByteArrayOutputStream();
        for (int i = 0; i < hexData.size(); i++) {
            dataBuf.write(ByteUtils.reverseBytes(ByteUtils.hexToBytes(hexData.get(i))));
        }
        return this.buildWriteRequest(gwno, dno, meterNo, passcode, p645DataType, dataBuf.toByteArray(),
            wLog);
    }

    private ByteArrayOutputStream buildWriteRequest(String gwno, String dno,
        String meterNo, String passcode,
        P645DataType p645DataType,
        byte[] data,
        boolean wLog) throws IOException {
        byte[] leadBytes = Dlt645Constants.BYTE_DLT645_HEAD_4;
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        buf.write(leadBytes);

        buf.write(Dlt645Constants.BYTE_DLT645_START);
        buf.write(this.encodeMeterNo(gwno, dno, meterNo));
        buf.write(Dlt645Constants.BYTE_DLT645_START);
        buf.write(Dlt645Constants.BYTE_DLT645_WRITE_CMD);

        ByteArrayOutputStream payloadBuf = new ByteArrayOutputStream();
        payloadBuf.write(p645DataType.getDataType());
        payloadBuf.write(0x02); // 密码等级
        payloadBuf.write(ByteUtils.hexToBytes(passcode));   // 密码
//        payloadBuf.write(new byte[]{0x02, 0x00, 0x00, 0x00});
        payloadBuf.write(new byte[]{0x00, 0x00, 0x00, 0x00});      // 操作者代码
        payloadBuf.write(data);
        int dataLength = payloadBuf.size();
        buf.write((byte) (dataLength & 0xFF));

        buf.write(add33(payloadBuf.toByteArray()));   // 数据部分每个字节都要加0x33
        int cs = 0;
        byte[] byteArray = buf.toByteArray();
        for (int i = 0; i < byteArray.length; i++) {
            if (i < leadBytes.length) {
                // 忽略前导字节
                continue;
            }
            cs += (byteArray[i] & 0xFF);
        }
        buf.write((byte) (cs % 256) & 0xFF);
        buf.write(Dlt645Constants.BYTE_DLT645_END);
        return buf;
    }


    private byte[] add33(byte[] in) {
        byte[] out = new byte[in.length];
        int i = 0;
        while (i < in.length) {
            out[i] = (byte) ((in[i] + Dlt645Constants.BYTE_DLT645_DATA_ADD) & 0xFF);
            i++;
        }
        return out;
    }
}
