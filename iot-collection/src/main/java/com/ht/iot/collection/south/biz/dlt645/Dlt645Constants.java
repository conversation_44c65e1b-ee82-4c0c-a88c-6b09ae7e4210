package com.ht.iot.collection.south.biz.dlt645;

public interface Dlt645Constants {

    byte BYTE_DLT645_HEAD = (byte) 0xFE;

    byte[] BYTE_DLT645_HEAD_4 = new byte[]{(byte) 0xFE, (byte) 0xFE, (byte) 0xFE, (byte) 0xFE};

    byte BYTE_DLT645_START = 0x68;

    byte BYTE_DLT645_READ_CMD = 0x11;

    /**
     * 写数据
     */
    byte BYTE_DLT645_WRITE_CMD = 0x14;

    byte BYTE_DLT645_END = 0x16;

    byte BYTE_DLT645_LENGTH_ADD = 0x04;

    byte BYTE_DLT645_DATA_ADD = 0x33;


    byte BYTE_READ_RESULT_91 = (byte) 0x91; // 从站正常应答控制码(无后续数据帧)
    byte BYTE_READ_RESULT_D1 = (byte) 0xD1; // 从站异常应答控制码

    byte BYTE_WRITE_RESULT_FAIL = (byte) 0xD4;   // 写数据失败的返回码
    byte BYTE_WRITE_RESULT_SUCCESS = (byte) 0x94;    // 写数据成功的返回码


    int MONITOR_MAX_TIMEOUT = 10; // 监听模式最大超时次数，超过认为离线


    int METER_NO_LENGTH = 6;    // 电表表号长度为6字节
}
