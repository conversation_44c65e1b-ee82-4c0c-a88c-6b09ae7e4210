package com.ht.iot.collection.south.biz.modbus.tcp;

import com.cdz360.base.utils.ByteUtils;
import com.ht.iot.collection.model.ModbusTcpReq;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;
import java.io.ByteArrayOutputStream;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ChannelHandler.Sharable
public class ModbusTcpNettyEncoder extends MessageToMessageEncoder<ModbusTcpReq> {

    private ModbusTcpChannelRepo channelRepo;

    public ModbusTcpNettyEncoder(ModbusTcpChannelRepo channelRepo) {
        this.channelRepo = channelRepo;
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, ModbusTcpReq reqMsg, List<Object> out)
        throws Exception {
        log.info("[{} {}] 开始编码待发送的 modbus tcp 消息. seq= {}, cmd= {}, addr= {}, num= {}",
            reqMsg.getGwno(), reqMsg.getDno(),
            reqMsg.getSeq(),
            reqMsg.getAddr().getReadCmd(),
            reqMsg.getAddr().getAddr(),
            reqMsg.getAddr().getNum());
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(ByteUtils.intToByte2BE(reqMsg.getSeq())); //  报文序列号
        baos.write(0x00);    // 2个0x00 表示 modbus tcp 协议
        baos.write(0x00);
        ByteArrayOutputStream bodyBuf = new ByteArrayOutputStream();
        bodyBuf.write(reqMsg.getSid() & 0xFF);  // 从机地址
        bodyBuf.write(reqMsg.getAddr().getReadCmd());   // 指令码
        bodyBuf.write(ByteUtils.intToByte2BE(reqMsg.getAddr().getAddr()));  // 寄存器地址
        bodyBuf.write(ByteUtils.intToByte2BE(reqMsg.getAddr().getNum()));   // 寄存器数量

        baos.write(ByteUtils.intToByte2BE(bodyBuf.size()));  // 报文长度
        baos.write(bodyBuf.toByteArray());
        byte[] buf = baos.toByteArray();
        log.info("[{} {}] 发送 modbus tcp 消息: {}",
            reqMsg.getGwno(), reqMsg.getDno(), ByteUtils.bytesToHex(buf));
//        out.add(buf);
        out.add(Unpooled.wrappedBuffer(buf));
    }
}
