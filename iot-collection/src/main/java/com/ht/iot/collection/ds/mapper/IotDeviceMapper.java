package com.ht.iot.collection.ds.mapper;

import com.ht.iot.collection.model.HoldingDevice;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface IotDeviceMapper {
    void insertDevice(HoldingDevice device);

    void updateDevice(HoldingDevice device);

    /**
     * 逻辑删除设备（将enable字段设置为0）
     * @param dno 设备编号
     */
    void deleteDeviceByDno(String dno);

    List<HoldingDevice> getAllDevicesByPage(long lastId);

    Long getIdByDno(String dno);
}
