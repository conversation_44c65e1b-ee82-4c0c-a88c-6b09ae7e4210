package com.ht.iot.collection.task;

import com.ht.iot.collection.biz.LaunchingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LaunchingTask {

    @Autowired
    private LaunchingExecutor launchingExecutor;

    /**
     * 将待下发的设备添加到activeGwRepo
     */
    @Scheduled(initialDelay = 1000, fixedRate = 1 * 1000)
    public void launchingDevices() {
        //log.trace(">>");
        launchingExecutor.push2LaunchingRepo();
        //log.trace("<<");
    }
}
