package com.ht.iot.collection.south.biz.modbus.tcp;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import com.ht.iot.collection.biz.ActiveGwRepo;
import com.ht.iot.collection.biz.LaunchingExecutor;
import com.ht.iot.collection.model.ActiveGw;
import com.ht.iot.collection.model.LaunchedDevice;
import com.ht.iot.collection.model.LaunchedModbusTcpDevice;
import com.ht.iot.collection.model.ModbusTcpChannel;
import com.ht.iot.collection.model.ModbusTcpReq;
import com.ht.iot.collection.model.ModbusTcpRes;
import com.ht.iot.collection.utils.NettyUtils;
import io.micrometer.tracing.Tracer;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.ConnectTimeoutException;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import jakarta.annotation.PostConstruct;
import java.net.ConnectException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModbusTcpService {

    private Bootstrap bootstrap;
    @Autowired
    private Tracer tracer;
    @Autowired
    private ModbusTcpChannelRepo channelRepo;
    @Autowired
    private ActiveGwRepo gwRepo;
    @Autowired
    private LaunchingExecutor launchingExecutor;

    @PostConstruct
    public void init() {
        bootstrap = new Bootstrap();
        bootstrap = bootstrap.group(new NioEventLoopGroup());
        bootstrap = bootstrap.channel(NioSocketChannel.class)
            .option(ChannelOption.TCP_NODELAY, true);
        bootstrap = bootstrap.handler(new ModbusTcpChannelInitializer(this.tracer,
            channelRepo, this));
    }


    public boolean connect(String dno,
        ActiveGw gw,
        LaunchedModbusTcpDevice device,
        boolean wLog) {
        try {
            if (gw.getNettyChannel() != null) {
                gw.getNettyChannel().closeFuture().sync();
                gw.setNettyChannel(null);
            }
        } catch (Exception e) {
            gw.setNettyChannel(null);
        }
        try {
            bootstrap.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000);    // 设置连接超时时间为5000毫秒（5秒）
            ChannelFuture future = bootstrap.connect(device.getPeerIp(), device.getPeerPort())
                .sync();
            if (future.isSuccess()) {
                channelRepo.addChannel(NettyUtils.genChKey(future.channel()), device.getGwno());
                gw.setNettyChannel(future.channel());
            }
//            channel.setSocket(new Socket(channel.getPeerIp(), channel.getPeerPort()));
        } catch (Exception e) {
            if (wLog) {
                if (e instanceof ConnectTimeoutException
                    || (e.getCause() != null && e.getCause() instanceof ConnectException)) {
                    log.error("[{} {}] 创建 modbusTcp 连接失败. device = {}, error = {}",
                        device.getGwno(), dno,
                        device,
                        e.getMessage());
                } else {
                    log.error("[{} {}] 创建 modbusTcp 连接失败. device = {}, error = {}",
                        device.getGwno(), dno,
                        device,
                        e.getMessage(),
                        e);
                }
            } else {
                log.error("[{} {}] 创建 modbusTcp 连接失败. device = {}, error = {}",
                    device.getGwno(), dno,
                    device,
                    e.getMessage());
            }
            return false;
        }
        if (gw.getNettyChannel() == null) {
            log.error("[{} {}] 创建 modbusTcp 连接失败. tcpCfg = {}", device.getGwno(), dno,
                device);
            return false;
        }

//        modbusWrapper = new ModbusTcpWrapper();
//        modbusWrapper.setSocket(this.socket);
//        modbusWrapper.setModbusLogger(this.getModbusLogger());

        log.info("[{} {}] 创建 modbusTcp 连接成功. 对端: {}:{}, 本地: {}",
            device.getGwno(), dno, device.getPeerIp(), device.getPeerPort(),
            gw.getNettyChannel().id().toString());
        return true;
    }

    public void closePort(ModbusTcpChannel modbusChannel) {
        try {
            if (modbusChannel.getSocket() != null) {
                log.info("关闭端口: 对端: {}:{}, 本地: {}:{}",
                    modbusChannel.getSocket().getRemoteSocketAddress().toString(),
                    modbusChannel.getSocket().getPort(),
                    modbusChannel.getSocket().getLocalAddress().toString(),
                    modbusChannel.getSocket().getLocalPort());
                modbusChannel.getSocket().close();
                modbusChannel.setSocket(null);
            }
        } catch (Exception e) {
            log.error("关闭端口失败. error = {}", e.getMessage(), e);
        }
    }


    /**
     *
     */
    public boolean sendReadRequest(//String tid,
        String dno,
        ActiveGw gw,
//        int addr,
//        int did,
        LaunchedModbusTcpDevice device,
//        byte readCmd,
//        int num,
//        String nameTag,
//        int seqNum,
//        boolean appendCrc16,
        ModbusTcpReq reqMsg,
//        byte[] reqBuf,
        boolean wLog) {
        if (!gw.isConnected()) {
            boolean connResult = this.connect(dno, gw, device, wLog);
            if (!connResult) {
                log.warn("[{} {}] 创建TCP连接失败, 对端: {}:{}",
                    gw.getGwno(), dno, device.getPeerIp(), device.getPeerPort());
                return connResult;
            }
        }
        if (!gw.isConnected()) {
            log.warn("[{} {}] 网关TCP连接失败, 发送 modbusTcp 消息失败",
                gw.getGwno(), device.getDno());
            return false;
        }
//        ByteArrayOutputStream buf = new ByteArrayOutputStream();
//        buf.write(ByteUtils.intToByte2BE(seqNum));  // 事务标识符，由客户端产生，可用于事务处理配对
//        buf.write(0x00);    // 0 = modbus 协议; 1 = uni-te 协议
//        buf.write(0x00);    // 0 = modbus 协议; 1 = uni-te 协议
//        int len = 6;    // 单元标识符1字节 + 功能码1字节 + 起始地址2字节 + 寄存器个数2字节
//        buf.write(ByteUtils.intToByte2BE(len));
//
//        buf.write(did);    // 单元标识符/地址
//        buf.write(readCmd);    // 功能码 - 读连续多个保持寄存器数据
//        buf.write(ByteUtils.intToByte2BE(addr));
//        buf.write(ByteUtils.intToByte2BE(num));
//        byte[] buf2;
//        if (appendCrc16) {
//            try {
//                buf.write(ByteUtils.ZERO_2_BYTES);
//            } catch (IOException e) {
//                log.error("[{} {}] {} 拼接 modbus crc 失败",
//                    tid, dno, nameTag);
//                return false;
//            }
//            buf2 = buf.toByteArray();
//            int crc = ByteUtils.crc16(buf2, buf2.length - 2);
//            buf2[buf2.length - 2] = (byte) (crc & 0xFF);
//            buf2[buf2.length - 1] = (byte) (crc >> 8 & 0xFF);
//        } else {
//            buf2 = buf.toByteArray();
//        }

        if (wLog) {
            log.debug("[{} {}] 准备发送Modbus TCP数据: {}",
                device.getGwno(), dno, reqMsg);
        }
        try {
            gw.getNettyChannel().writeAndFlush(reqMsg);
//            channel.getSocket().getOutputStream().write(reqBuf, 0, reqBuf.length);
//            ModbusFileLogger fileLogger = this.modbusLogger;
//            if (fileLogger != null) {
//                fileLogger.writeRequest(tid, dno, ByteBufUtil.hexDump(buf));
//            }
            return true;
        }
//        catch (Exception e1) {
//            if (wLog) {
//                log.error("[{} {}] 发送Modbus TCP数据失败 error = {} msg = {}",
//                    channel.getGwno(), dno, e1.getMessage(), ByteBufUtil.hexDump(reqBuf), e1);
//            }
////            this.closePort();
//            return false;
//        }
        catch (Exception e) {
            if (wLog) {
                log.error("[{} {}] 发送Modbus TCP数据失败 error = {} msg = {}",
                    device.getGwno(), dno, e.getMessage(), reqMsg, e);
            }
            return false;
        }
    }


    public void processModbusTcpMessage(String gwno, ModbusTcpRes modbusRes) {

        ActiveGw gw = gwRepo.getGwInfo(gwno);
        if (gw == null) {
            log.info("[{}] 收到未配置网关的mqtt消息,忽略. modbusTcpRes= {}", gwno, modbusRes);
            return;
        }

        LaunchedDevice device = gw.getDevices().peek();
        if (device == null) {
            log.warn("[{}] << 网关的设备队列为空,未处于数据采集状态", gw.getGwno());
            return;
        } else if (!(device instanceof LaunchedModbusTcpDevice)) {
            log.warn("[{}] 设备类型不匹配. device.class= {}", gwno,
                device.getClass().getSimpleName());
            return;
        }
        LaunchedModbusTcpDevice deviceX = (LaunchedModbusTcpDevice) device;
        if (deviceX.getSid() != modbusRes.getSid()) {
            log.warn("[{}] 设备地址不匹配. 期望设备dno= {}, 报文设备地址= {}, 期望设备地址= {}",
                gwno, device.getDno(),
                modbusRes.getSid(), deviceX.getSid());
            return;
        }
        ModbusAddrRange reqAddr = deviceX.getReadQ().peek();
        if (reqAddr == null) {
            log.warn("[{} {}] 设备读指令队列为空. 总队列长度= {}",
                gwno, device.getDno(),
                deviceX.getQueueSize());
            return;
        } else if (reqAddr.getReadCmd() != modbusRes.getCmd()) {
            if ((reqAddr.getReadCmd() | (byte) 0x80) == modbusRes.getCmd()) {
                log.warn("[{} {}] 从机返回失败. 寄存器地址= {}, 报文指令码= {}, 期望指令码= {}",
                    gwno, device.getDno(),
                    reqAddr.getAddr(), modbusRes.getCmd() & 0xFF, reqAddr.getReadCmd());
                modbusRes.setData(new byte[0]); // 将buf制空
            } else {
                log.warn("[{} {}] 指令码不匹配. 寄存器地址= {}, 报文指令码= {}, 期望指令码= {}",
                    gwno, device.getDno(),
                    reqAddr.getAddr(), modbusRes.getCmd() & 0xFF, reqAddr.getReadCmd());
                return;
            }
        }
        try {
            launchingExecutor.processRes(gw, deviceX, modbusRes.getTs(), modbusRes.getData());
        } catch (Exception e) {
            log.error("[{} {}] 处理失败. modbusRes.data= {}",
                gwno, device.getDno(), ByteUtils.bytesToHex(modbusRes.getData()), e);
        }
    }
}
