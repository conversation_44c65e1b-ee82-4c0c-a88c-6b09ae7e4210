package com.ht.iot.collection.south.biz.modbus.rtu;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.iot.model.modbus.dto.ModbusAddrRange;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ModbusRtuEncoder {


    /**
     * 构建下行的modbus采集指令
     */
    public ByteArrayOutputStream buildModbusReqBuf(String gwno, String dno, int sid,
        ModbusAddrRange addr) throws IOException {

        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        buf.write((byte) (sid & 0xFF));
        buf.write(addr.getReadCmd());

        buf.write(ByteUtils.intToByte2BE(addr.getAddr()));
        buf.write(ByteUtils.intToByte2BE(addr.getNum()));
        int crc = ByteUtils.crc16(buf.toByteArray(), buf.size());
        buf.write(ByteUtils.intToByte2LE(crc));
        return buf;

    }

}
