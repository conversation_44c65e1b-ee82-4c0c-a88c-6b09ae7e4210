package com.ht.iot.collection.south.biz;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.iot.model.ess.modbus.P485ResMsg;
import com.cdz360.iot.model.modbus.dto.ModbusIeee754Tv;
import com.cdz360.iot.model.modbus.dto.ModbusIntegerTv;
import com.cdz360.iot.model.modbus.type.ModbusDataType;
import com.cdz360.iot.model.modbus.type.ModbusValueOrder;
import com.ht.iot.collection.IotCollectionTestMain;
import com.ht.iot.collection.south.biz.modbus.rtu.ModbusRtuDecoder;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = IotCollectionTestMain.class)
@ExtendWith(SpringExtension.class)
@ExtendWith(MockitoExtension.class)
public class ModbusRtuDecoderTest {

    @Autowired
    private ModbusRtuDecoder modbusRtuParser;

    private String tid;
    private String dno;
    private P485ResMsg modbusMsg;
    private ModbusIntegerTv intTv;
    private ModbusIeee754Tv ieeeTv;

    @BeforeEach
    public void init() {
        this.tid = RandomStringUtils.randomAlphabetic(8);
        this.dno = RandomStringUtils.randomAlphabetic(8);
        modbusMsg = new P485ResMsg();
        modbusMsg.setBuf(ByteUtils.hexToBytes(
            "01 03 08 43 0A 4B 45 FD BF 41 9A 09 F5".replace(" ", "")));
        intTv = new ModbusIntegerTv();
        intTv.setOrder(ModbusValueOrder.AABB)
            .setT(ModbusDataType.INT16.getCode())
            .setNum(1);

        ieeeTv = new ModbusIeee754Tv();
        ieeeTv.setOrder(ModbusValueOrder.AABBCCDD)
            .setT(ModbusDataType.IEEE754.getCode())
            .setNum(2);
    }

    @Test
    public void test_parseInteger() {
        ModbusIntegerTv result = modbusRtuParser.parseInteger(tid, dno, modbusMsg.getBuf(), 3, intTv);
        log.info("[{} {}] 解析后的结果: {}", tid, dno, result.getV());
    }


    @Test
    public void test_parseDouble32() {
        modbusMsg.setBuf(ByteUtils.hexToBytes(
            "01 03 08 47 84 9A 0D FD BF 41 9A 09 F5".replace(" ", "")));
        ModbusIeee754Tv result = modbusRtuParser.parseIeee754(tid, dno, modbusMsg.getBuf(), 3, ieeeTv);
        log.info("[{} {}] 解析后的结果: {}", tid, dno, result.getV());
        Assertions.assertEquals(new BigDecimal("67892.1015625"), result.getV());
    }
}
