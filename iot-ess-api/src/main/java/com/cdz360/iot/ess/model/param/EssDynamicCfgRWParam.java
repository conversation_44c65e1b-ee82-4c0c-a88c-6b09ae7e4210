package com.cdz360.iot.ess.model.param;

import com.cdz360.base.model.es.vo.RegisterRwValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户储设备动态配置项读取请求参数")
@Data
@Accessors(chain = true)
public class EssDynamicCfgRWParam {

    @Schema(description = "读取配置类别")
    @JsonInclude(Include.NON_NULL)
    private String category;

    @Schema(description = "指定需要读取配置项")
    @JsonInclude(Include.NON_NULL)
    private List<RegisterRwValue> tvs;
}
