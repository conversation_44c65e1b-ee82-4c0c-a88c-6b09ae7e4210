package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.dto.ControlStrategyParamDto;
import com.cdz360.base.model.es.dto.EmsConfigParamDto;
import com.cdz360.base.model.es.dto.PcsConfigParamDto;
import com.cdz360.base.model.es.dto.UpdateEssCfgDto;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.type.PcsGridMode;
import com.cdz360.base.model.es.vo.EssInOutStrategyDto;
import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.cdz360.base.model.es.vo.EssPriceDto;
import com.cdz360.base.model.es.vo.EssPriceItem;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ds.rw.EssCfgRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.ess.ds.PcsRwDs;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssChargeStrategyPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.po.EssPriceCfgPo;
import com.cdz360.iot.model.ess.po.EssStrategyCfgPo;
import com.cdz360.iot.model.pcs.po.PcsPo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class EssCfgService {

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private PcsRwDs pcsRwDs;

    @Autowired
    private EssCfgRwDs essCfgRwDs;

    @Autowired
    private EssRwDs essRwDs;


    /**
     * 从数据库获取计费模板
     */
    @Transactional(readOnly = true)
    public EssPriceDto getPriceCfg(String essDno, Long essCfgId) {
        EssCfgPo essCfg = essCfgRoDs.getByCfgId(essCfgId);
        if (essCfg == null) {
            log.warn("获取充放电配置失败. essDno = {},  cfgId = {}", essDno, essCfgId);
            return null;
//            throw new DcArgumentException("获取EMU配置失败");
        }

        EssPriceDto priceDto = new EssPriceDto(); //
        priceDto.setCfgId(essCfgId)
            .setCfgNo(essCfg.getCfgNo());
        List<EssPriceItem> items = new ArrayList<>();
        if (essCfg.getPriceCfg() != null) {
            priceDto.setName(essCfg.getPriceCfg().getName());
            if (CollectionUtils.isNotEmpty(essCfg.getPriceCfg().getItems())) {
                items.addAll(essCfg.getPriceCfg().getItems());
            }
        }

        if (CollectionUtils.isEmpty(items)) {
            log.warn("EMU 未配置分时计费时段. cfgId = {}", essCfgId);
            return priceDto;
        }
        // 按开始时间做排序,正序
        items = items.stream()
            .sorted((a, b) -> a.getStart() - b.getStart())
            .collect(Collectors.toList());
        priceDto.setItems(items);
        return priceDto;
    }

    /**
     * 从数据库获取计费模板
     */
    @Transactional(readOnly = true)
    public EssPriceDto getDischargePriceCfg(String essDno, Long essCfgId) {
        EssCfgPo essCfg = essCfgRoDs.getByCfgId(essCfgId);
        if (essCfg == null) {
            log.warn("获取充放电配置失败. essDno = {}, cfgId = {}", essDno, essCfgId);
//            throw new DcArgumentException("获取EMU配置失败");
            return null;
        }

        EssPriceDto priceDto = new EssPriceDto(); //
        priceDto.setCfgId(essCfgId)
            .setCfgNo(essCfg.getCfgNo());
        List<EssPriceItem> items = new ArrayList<>();
        if (essCfg.getDischargePriceCfg() != null) {
            priceDto.setName(essCfg.getDischargePriceCfg().getName());
            if (CollectionUtils.isNotEmpty(essCfg.getDischargePriceCfg().getItems())) {
                items.addAll(essCfg.getDischargePriceCfg().getItems());
            }
        }

        if (CollectionUtils.isEmpty(items)) {
            log.warn("EMU 未配置分时计费时段. cfgId = {}", essCfgId);
            return priceDto;
        }
        // 按开始时间做排序,正序
        items = items.stream()
            .sorted((a, b) -> a.getStart() - b.getStart())
            .collect(Collectors.toList());
        priceDto.setItems(items);
        return priceDto;
    }


    /**
     * 配置计费模板
     */
    @Transactional(readOnly = false)
    public EssCfgPo configPriceCfg(EssPo emu, EssPriceDto priceIn) {
        Long oldCfgId = emu.getDeliverCfgId();
        if (null == oldCfgId || 0 == oldCfgId) {
            oldCfgId = emu.getCfgId();
        }

        int lastEnd = 0;
        if (CollectionUtils.isEmpty(priceIn.getItems())) {
            log.warn("参数错误,计费时段不能为空. dno = {}, priceIn = {}", emu.getDno(),
                priceIn);
            throw new DcServiceException("参数错误,计费时段不能为空");
        }
        for (var item : priceIn.getItems()) {
            if (lastEnd != item.getStart()) {
                log.warn("参数错误,分段的时间不连续. dno = {}, priceIn = {}", emu.getDno(),
                    priceIn);
                throw new DcServiceException("参数错误,分段的时间不连续");
            }
            lastEnd = item.getEnd();
        }
        if (lastEnd != 1440) {
            log.warn("参数错误,分段结束时间不是1440. dno = {}, priceIn = {}", emu.getDno(),
                priceIn);
            throw new DcServiceException("参数错误,分段结束时间不合法");
        }

        EssCfgPo oldCfg = essCfgRwDs.getByCfgId(oldCfgId, true);
        if (oldCfg == null) {
            oldCfg = new EssCfgPo();
            oldCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US));
        }

        this.updateChargePrice(oldCfg, priceIn.getItems());
        this.updateDischargePrice(oldCfg, priceIn.getItems());

        oldCfg.setCfgId(null);
        essCfgRwDs.insertEssCfg(oldCfg);
        if (oldCfg.getCfgId() == null) {
            log.warn("记录储能计费模板失败.dno = {}, priceIn = {}", emu.getDno(), priceIn);
            throw new DcServiceException("记录储能计费模板失败");
        }
        EssPo emu4Update = new EssPo();
        emu4Update.setDno(emu.getDno())
//            .setCfgId(oldCfg.getCfgId())
            .setCfgStatus(EquipCfgStatus.SEND_2_GW)
            .setDeliverCfgId(oldCfg.getCfgId());
        essRwDs.updateEss(emu4Update);
        return oldCfg;
    }


    /**
     * 从数据库获取充放电策略
     */
    @Transactional(readOnly = true)
    public EssInOutStrategyDto getChargeStrategyDto(String essDno, Long essCfgId) {
        EssCfgPo essCfg = essCfgRoDs.getByCfgId(essCfgId);
        if (essCfg == null) {
            log.warn("获取充放电配置失败. essDno = {}, cfgId = {}", essDno, essCfgId);
//            throw new DcArgumentException("获取EMU配置失败");
            return null;
        }

        EssInOutStrategyDto chargeStrategyDto = new EssInOutStrategyDto(); // PCS充放电时段设置
        chargeStrategyDto.setId(essCfgId)
            .setSno(essCfg.getCfgNo());
        List<EssInOutStrategyItem> items = new ArrayList<>();
        if (essCfg.getChargeStrategy() != null) {
            chargeStrategyDto.setName(essCfg.getChargeStrategy().getName());
            if (CollectionUtils.isNotEmpty(essCfg.getChargeStrategy().getItems())) {
                items.addAll(essCfg.getChargeStrategy().getItems());
            }
        }

        if (CollectionUtils.isEmpty(items)) {
            log.warn("EMU 未配置充放电时段. cfgId = {}", essCfgId);
            return chargeStrategyDto;
        }
        // 按开始时间做排序,正序
        items = items.stream()
            .sorted((a, b) -> a.getStart() - b.getStart())
            .collect(Collectors.toList());
        chargeStrategyDto.setItems(items);
        return chargeStrategyDto;
    }


    @Transactional(readOnly = false)
    public EssCfgPo configChargeStrategy(EssPo emu, EssInOutStrategyDto strategyIn) {
        Long oldCfgId = emu.getDeliverCfgId();
        if (null == oldCfgId || 0 == oldCfgId) {
            oldCfgId = emu.getCfgId();
        }

        int lastEnd = 0;
        if (CollectionUtils.isEmpty(strategyIn.getItems())) {
            log.warn("参数错误,充放电策略的时段不能为空. dno = {}, strategy = {}", emu.getDno(),
                strategyIn);
            throw new DcServiceException("参数错误,充放电策略的时段不能为空");
        }
        for (var item : strategyIn.getItems()) {
            if (lastEnd != item.getStart()) {
                log.warn("参数错误,分段的时间不连续. dno = {}, strategy = {}", emu.getDno(),
                    strategyIn);
                throw new DcServiceException("参数错误,分段的时间不连续");
            }
            lastEnd = item.getEnd();
        }
        if (lastEnd != 1440) {
            log.warn("参数错误,分段结束时间不是1440. dno = {}, strategy = {}", emu.getDno(),
                strategyIn);
            throw new DcServiceException("参数错误,分段结束时间不合法");
        }

        EssCfgPo oldCfg = essCfgRwDs.getByCfgId(oldCfgId, true);
        if (oldCfg == null) {
            oldCfg = new EssCfgPo();
            oldCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US));
        }

        this.updateChargeStrategy(oldCfg, strategyIn.getItems());

        oldCfg.setCfgId(null);
        essCfgRwDs.insertEssCfg(oldCfg);
        if (oldCfg.getCfgId() == null) {
            log.warn("记录储能充放电配置失败.dno = {}, strategy = {}", emu.getDno(), strategyIn);
            throw new DcServiceException("记录储能充放电配置失败");
        }
        EssPo emu4Update = new EssPo();
        emu4Update.setDno(emu.getDno())
//            .setCfgId(oldCfg.getCfgId())
            .setCfgStatus(EquipCfgStatus.SEND_2_GW)
            .setDeliverCfgId(oldCfg.getCfgId());
        essRwDs.updateEss(emu4Update);
        return oldCfg;
    }

    @Transactional
    public EssCfgPo updateEssCfg(EssPo emu, UpdateEssCfgDto dto) {
        Long cfgId = emu.getCfgId();
        if (EquipCfgStatus.SEND_2_GW.equals(emu.getCfgStatus()) &&
            null != emu.getDeliverCfgId() && emu.getDeliverCfgId() > 0) {
            cfgId = emu.getDeliverCfgId();
        }

        EssCfgPo oldCfg = essCfgRwDs.getByCfgId(cfgId, true);
        if (oldCfg == null) {
            oldCfg = new EssCfgPo();
            oldCfg.setCfgNo(RandomStringUtils.randomAlphabetic(8).toUpperCase(Locale.US));
        }

        // EMS参数
        if (null != dto.getEmsConfigParam()) {
            this.updateEmsParam(oldCfg, dto.getEmsConfigParam());
        }

        // PCS参数
        PcsConfigParamDto pcsCfg = dto.getPcsConfigParam();
        if (null != pcsCfg) {
            List<PcsPo> pcsList = this.pcsRoDs.getPcsListByEssDno(emu.getDno());
            if (CollectionUtils.isNotEmpty(pcsList)) {
                // 当前默认只有一个
                PcsPo pcs = pcsList.get(0);
                PcsPo updatePcs = new PcsPo().setId(pcs.getId()).setDno(pcs.getDno());

                // 仅更新传入数据
                if (null != pcsCfg.getInverterRuntimeMode()) {
                    switch (pcsCfg.getInverterRuntimeMode()) {
                        case INIT:
                            updatePcs.setGridMode(PcsGridMode.INIT);
                            break;
                        case OFF_GRID:
                            updatePcs.setGridMode(PcsGridMode.OFF_GRID);
                            break;
                        case PARALLEL_GRID:
                            updatePcs.setGridMode(PcsGridMode.ON_GRID);
                            break;
                    }
                }

                if (null != pcsCfg.getInverterActivePower()) {
                    updatePcs.setInvPg(pcsCfg.getInverterActivePower());
                }
                if (null != pcsCfg.getInverterReactivePower()) {
                    updatePcs.setInvQg(pcsCfg.getInverterReactivePower());

                }
                if (null != pcsCfg.getPcsRatedPower()) {
                    updatePcs.setRatedPower(pcsCfg.getPcsRatedPower());

                }
                if (null != pcsCfg.getSyncSysTime2Pcs()) {
                    updatePcs.setSyncSysTime(pcsCfg.getSyncSysTime2Pcs());
                }

                pcsRwDs.updatePcs(updatePcs);
            } else {
                log.warn(">>当前EMU没有配置PCS<<");
            }
        }

        // 控制策略参数
        if (null != dto.getControlStrategyParam()) {
            this.updateEmsStrategyCfg(oldCfg, dto.getControlStrategyParam());
        }

        // 时段电价
        if (CollectionUtils.isNotEmpty(dto.getInOutItems())) {
            this.updateChargeStrategy(oldCfg, dto.getInOutItems());
        }

        // 分时电价
        if (CollectionUtils.isNotEmpty(dto.getChargePriceItems())) {
            this.updateChargePrice(oldCfg, dto.getChargePriceItems());
        }
        if (CollectionUtils.isNotEmpty(dto.getDischargePriceItems())) {
            this.updateDischargePrice(oldCfg, dto.getDischargePriceItems());
        }

        oldCfg.setCfgId(null);
        essCfgRwDs.insertEssCfg(oldCfg);
        if (oldCfg.getCfgId() == null) {
            log.warn("记录储能充放电配置失败.dno = {}, strategy = {}", emu.getDno(), dto);
            throw new DcServiceException("记录储能充放电配置失败");
        }
        EssPo emu4Update = new EssPo();
        emu4Update.setDno(emu.getDno())
//            .setCfgId(oldCfg.getCfgId())
            .setCfgStatus(EquipCfgStatus.SEND_2_GW)
            .setDeliverCfgId(oldCfg.getCfgId());
        essRwDs.updateEss(emu4Update);
        return oldCfg;
    }

    private void updateDischargePrice(EssCfgPo oldCfg, List<EssPriceItem> dischargePriceItems) {
        int lastEnd = 0;
        if (CollectionUtils.isEmpty(dischargePriceItems)) {
            log.warn("参数错误,计费时段不能为空. {}", dischargePriceItems);
            throw new DcServiceException("参数错误,计费时段不能为空");
        }
        for (var item : dischargePriceItems) {
            if (lastEnd != item.getStart()) {
                log.warn("参数错误,分段的时间不连续. {}", item);
                throw new DcServiceException("参数错误,分段的时间不连续");
            }
            lastEnd = item.getEnd();
        }
        if (lastEnd != 1440) {
            log.warn("参数错误,分段结束时间不是1440. {}", dischargePriceItems);
            throw new DcServiceException("参数错误,分段结束时间不合法");
        }
        oldCfg.setDischargePriceCfg(new EssPriceCfgPo().setItems(dischargePriceItems));
    }

    private void updateChargePrice(EssCfgPo oldCfg, List<EssPriceItem> chargePriceItems) {
        int lastEnd = 0;
        if (CollectionUtils.isEmpty(chargePriceItems)) {
            log.warn("参数错误,计费时段不能为空. {}", chargePriceItems);
            throw new DcServiceException("参数错误,计费时段不能为空");
        }
        for (var item : chargePriceItems) {
            if (lastEnd != item.getStart()) {
                log.warn("参数错误,分段的时间不连续. {}", item);
                throw new DcServiceException("参数错误,分段的时间不连续");
            }
            lastEnd = item.getEnd();
        }
        if (lastEnd != 1440) {
            log.warn("参数错误,分段结束时间不是1440. {}", chargePriceItems);
            throw new DcServiceException("参数错误,分段结束时间不合法");
        }
        oldCfg.setPriceCfg(new EssPriceCfgPo().setItems(chargePriceItems));
    }

    private void updateChargeStrategy(EssCfgPo oldCfg, List<EssInOutStrategyItem> inOutItems) {
        int lastEnd = 0;
        if (CollectionUtils.isEmpty(inOutItems)) {
            throw new DcServiceException("参数错误,充放电策略的时段不能为空");
        }
        for (var item : inOutItems) {
            if (lastEnd != item.getStart()) {
                log.warn("参数错误,分段的时间不连续. item = {}", item);
                throw new DcServiceException("参数错误,分段的时间不连续");
            }
            lastEnd = item.getEnd();
        }
        if (lastEnd != 1440) {
            log.warn("参数错误,分段结束时间不是1440. item = {}", inOutItems);
            throw new DcServiceException("参数错误,分段结束时间不合法");
        }

        oldCfg.setChargeStrategy(new EssChargeStrategyPo().setItems(inOutItems));
    }

    private void updateEmsStrategyCfg(EssCfgPo oldCfg,
        ControlStrategyParamDto strategyParam) {
        if (null == strategyParam) {
            strategyParam = new ControlStrategyParamDto();
        }

        EssStrategyCfgPo result = new EssStrategyCfgPo();
//        BeanUtils.copyProperties(strategyParam, result);
        BeanUtils.copyProperties(oldCfg.getStrategyCfg(), result);

        // 仅仅修改传入的内容
        // 无功优化
        if (null != strategyParam.getQg()) {
            result.setQg(strategyParam.getQg());
            if (null != strategyParam.getPf()) {
                result.setPf(strategyParam.getPf());
            }
        }

        // 有功防超限
        if (null != strategyParam.getFnl()) {
            result.setFnl(strategyParam.getFnl());
            if (null != strategyParam.getPnl()) {
                result.setPnl(strategyParam.getPnl());
            }
            if (null != strategyParam.getPx()) {
                result.setPx(strategyParam.getPx());
            }
        }

        // 有功防逆流
        if (null != strategyParam.getFcx()) {
            result.setFcx(strategyParam.getFcx());
            if (null != strategyParam.getPnl()) {
                result.setPnl(strategyParam.getPnl());
            }
            if (null != strategyParam.getNxs()) {
                result.setNxs(strategyParam.getNxs());
            }

            if (null != strategyParam.getFnxs()) {
                result.setFnxs(strategyParam.getFnxs());
            }
            if (null != strategyParam.getPx()) {
                result.setPx(strategyParam.getPx());
            }
        }

        if (null == strategyParam.getIncreaseFactor()) {
            result.setIncreaseFactor(oldCfg.getStrategyCfg() != null &&
                null != oldCfg.getStrategyCfg().getIncreaseFactor() ? oldCfg.getStrategyCfg()
                .getIncreaseFactor() : BigDecimal.ONE);
        }
        if (null == strategyParam.getDecreaseFactor()) {
            result.setDecreaseFactor(oldCfg.getStrategyCfg() != null &&
                null != oldCfg.getStrategyCfg().getDecreaseFactor() ? oldCfg.getStrategyCfg()
                .getDecreaseFactor() : BigDecimal.ONE);
        }

        oldCfg.setStrategyCfg(result);
    }

    private void updateEmsParam(EssCfgPo oldCfg, EmsConfigParamDto configParam) {
        if (null != configParam.getUploadDeviceInfoDuration()) {
            oldCfg.setUploadInfoTime(configParam.getUploadDeviceInfoDuration());
        }
        if (null != configParam.getUploadRtDataDuration()) {
            oldCfg.setUploadDataTime(configParam.getUploadRtDataDuration());
        }
    }
}
