package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.UserEssService;
import com.cdz360.iot.model.ess.dto.BindDeviceDto;
import com.cdz360.iot.model.ess.dto.ModifyDeviceBasicDto;
import com.cdz360.iot.model.ess.dto.UserDeviceCountDto;
import com.cdz360.iot.model.ess.param.CountUserEssParam;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.param.UpdateBatteryNameplateParam;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.EssAttachCfgVo;
import com.cdz360.iot.model.ess.vo.EssAttachDtuVo;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能设备操作接口", description = "储能设备操作CRUD相关接口合集")
@Slf4j
@RestController
@RequestMapping("/user/ess")
public class UserEssRest {

    @Autowired
    private UserEssService userEssService;

    // 获取用户设备列表
    @Operation(summary = "获取户用储能设备列表")
    @PostMapping("/fetch")
    public Mono<ListResponse<UserEssVo>> fetchUserEss(@RequestBody FetchUserEssParam param) {
        log.info("获取用户设备列表: param = {}", param);
        return userEssService.fetchUserEss(param);
    }

    @Operation(summary = "统计用户设备数量")
    @PostMapping("/count")
    public Mono<ListResponse<UserDeviceCountDto>> countUserEss(
        @RequestBody CountUserEssParam param) {
        log.info("统计用户设备数量: param = {}", param);
        return userEssService.countUserEss(param)
            .map(RestUtils::buildListResponse);
    }

    // 用户绑定设备
    @Operation(summary = "用户绑定设备")
    @PostMapping("/bind")
    public Mono<ObjectResponse<EssPo>> userEssBind(@RequestBody BindDeviceDto dto) {
        log.info("用户绑定设备: dto = {}", dto);
        return userEssService.userEssBind(dto)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取设备的信息")
    @GetMapping("/{dno}/detail")
    public Mono<ObjectResponse<EssPo>> userEssDetail(
        ServerHttpRequest request,
        @Parameter(description = "用户ID") @RequestParam(value = "uid", required = false) Long uid,
        @Parameter(description = "通讯棒SN") @RequestParam(value = "sn", required = false) String sn,
        @Parameter(description = "设备编号") @PathVariable("dno") String dno) {
        log.info("获取设备信息: uid = {}, sn = {}, dno = {}", uid, sn, dno);
        return userEssService.userEssDetail(uid, sn, dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取设备主账号用户")
    @GetMapping("/{dno}/master")
    public Mono<ObjectResponse<Long>> userEssMaster(
        ServerHttpRequest request,
        @Parameter(description = "设备编号") @PathVariable @Valid @NotBlank String dno) {
        log.info("获取设备主账号用户: dno = {}", dno);
        return userEssService.userEssMaster(dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "更新储能设备中电池的铭牌信息")
    @PostMapping("/battery/nameplate")
    public Mono<BaseResponse> updateBatteryNameplate(
        ServerHttpRequest request,
        @RequestBody @Valid UpdateBatteryNameplateParam param) {
        log.debug("获取储能设备主账户信息: {}", param);
        return userEssService.updateBatteryNameplate(param);
    }

    @Operation(summary = "获取设备的信息附带配置信息")
    @GetMapping("/{dno}/detailAttachCfg")
    public Mono<ObjectResponse<EssAttachCfgVo>> userEssDetailAttachCfg(
        ServerHttpRequest request,
        @Parameter(description = "用户ID") @RequestParam(value = "uid", required = false) Long uid,
        @Parameter(description = "设备编号") @PathVariable @Valid @NotBlank String dno) {
        log.info("获取设备信息: uid = {}, dno = {}", uid, dno);
        return userEssService.userEssDetailAttachCfg(uid, dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取设备的信息附带配置信息")
    @GetMapping("/{dno}/detailAttachDtu")
    public Mono<ObjectResponse<EssAttachDtuVo>> userEssDetailAttachDtu(
        ServerHttpRequest request,
        @Parameter(description = "用户ID") @RequestParam(value = "uid", required = false) Long uid,
        @Parameter(description = "设备编号") @PathVariable @Valid @NotBlank String dno) {
        log.info("获取设备信息: uid = {}, dno = {}", uid, dno);
        return userEssService.userEssDetailAttachDtu(uid, dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "用户删除设备(软删除)")
    @DeleteMapping("/{dno}/delete")
    public Mono<ObjectResponse<EssPo>> userEssDelete(
        ServerHttpRequest request,
        @Parameter(description = "用户ID") @RequestParam(value = "uid", required = false) Long uid,
        @Parameter(description = "设备编号") @PathVariable @Valid @NotBlank String dno) {
        log.info("用户删除设备: uid = {}, dno = {}", uid, dno);
        return userEssService.userEssDelete(uid, dno)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "用户更新设备信息")
    @PostMapping("/modifyBasicInfo")
    public Mono<ObjectResponse<EssPo>> userEssModifyBasicInfo(
        @RequestBody ModifyDeviceBasicDto dto) {
        log.info("用户更新设备信息: dto = {}", dto);
        return userEssService.userEssModifyBasicInfo(dto)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "绑定分享设备列表")
    @PostMapping("/scanAndBindEss")
    public Mono<ObjectResponse<Integer>> scanAndBindEss(
        @Parameter(description = "分享设备用户ID") @RequestParam(value = "shareUid") Long shareUid,
        @Parameter(description = "扫码绑定用户ID") @RequestParam(value = "bindUid") Long bindUid) {
        log.info("绑定分享设备列表: shareUid = {}, bindUid = {}", shareUid, bindUid);
        return userEssService.scanAndBindEss(shareUid, bindUid)
            .map(RestUtils::buildObjectResponse);
    }
}
