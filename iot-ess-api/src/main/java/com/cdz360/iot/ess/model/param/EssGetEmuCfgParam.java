package com.cdz360.iot.ess.model.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssGetEmuCfgParam {

    @Schema(title = "MGC编号", required = false)
    @JsonProperty(required = false)
    private String gwno;

    @Schema(title = "配置的唯一ID")
    private Long cfgId;

    @Schema(title = "配置编号")
    private String cfgNo;
}
