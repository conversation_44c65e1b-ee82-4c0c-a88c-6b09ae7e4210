package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.type.DevicePowerOpType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.north.EssNorthBizService;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import com.cdz360.iot.model.gw.vo.UpgradeLogVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能设备操作相关接口", description = "储能设备操作")
@RequestMapping("/iot/biz/ess/op")
public class EssOpRest {

    @Autowired
    private EssNorthBizService essNorthBizService;

    @Operation(summary = "设备开机/关机")
    @PostMapping(value = "/{dno}")
    public Mono<BaseResponse> devicePowerOp(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno,
        @Parameter(name = "true需要开机；false需要关机", required = true) @RequestParam("on") Boolean on) {
        log.info("设备开机/关机: on = {}", on);
        return essNorthBizService.devicePowerOp(dno, on)
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "PCS设备开机/关机")
    @PostMapping(value = "/{dno}/pcs")
    public Mono<BaseResponse> devicePcsPowerOp(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno,
        @Parameter(name = "设备电源操作类型", required = true)
        @RequestParam("powerOp") DevicePowerOpType powerOp) {
        log.info("PCS设备开机/关机: powerOp = {}", powerOp);
        return essNorthBizService.devicePcsPowerOp(dno, powerOp)
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "升级包信息下发")
    @PostMapping(value = "/upgrade")
    public Mono<BaseResponse> deviceUpgradeOp(@RequestBody StartUpgradeTaskParam param) {
        log.info("升级包信息下发: {}", param);
        return essNorthBizService.deviceUpgradeOp(param)
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "设备升级信息")
    @GetMapping("/{dno}/upgradeInfo")
    public Mono<ObjectResponse<UpgradeLogVo>> deviceUpgradeInfo(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno) {
        log.info("设备升级信息: {}", dno);
        return essNorthBizService.getDeviceUpgradeInfo(dno);
    }

    @Operation(summary = "EMU启用")
    @GetMapping("/{dno}/softStart")
    public Mono<BaseResponse> deviceSoftStart(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno) {
        log.info("设备升级信息: {}", dno);
        return essNorthBizService.deviceSoftStart(dno);
    }

}
