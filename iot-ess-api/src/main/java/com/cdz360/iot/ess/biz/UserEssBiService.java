package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.vo.EssMapDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class UserEssBiService {

    @Autowired
    private EssDtuRoDs essDtuRoDs;

    public Mono<ListResponse<EssMapDataVo>> essMapData(EssMapDataParam param) {
        return Mono.just(param)
            .map(essDtuRoDs::essMapData)
            .map(RestUtils::buildListResponse);
    }
}
