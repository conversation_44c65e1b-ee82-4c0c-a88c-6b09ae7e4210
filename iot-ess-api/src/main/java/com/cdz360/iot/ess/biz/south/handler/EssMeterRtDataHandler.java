package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.Meter;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssMeterRtDataHandler extends AbstractEssEquipRtHandler {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssEquipType.GRID_GATEWAY_METER, this);
        handlerFactory.addHandler(EssEquipType.ESS_GATEWAY_METER, this);
        handlerFactory.addHandler(EssEquipType.PV_INV_GATEWAY_METER, this);
    }

    @Override
    public Mono<Boolean> process(EssRtReq<JsonNode> rtReq) {
        log.info("电表运行数据: {}", JsonUtils.toJsonString(rtReq));

        // 获取ESS信息
        EssPo ess = essRoDs.getByDno(rtReq.getDno());
        if (null == ess) {
            log.error("储能ESS设备不存在: dno = {}", rtReq.getDno());
            return Mono.just(false);
        }

        return this.pushData2Redis(ess.getTimeZone(), rtReq)
            .map(rt -> JsonUtils.fromJson(rt.getRtData(), Meter.class))
            .doOnNext(data -> {
                essEquipRwDs.updateEssEquipStatus(
                    rtReq.getDno(), List.of(rtReq.getEquipId()), EquipStatus.NORMAL,
                    EquipAlertStatus.OK);
            })
            .doOnNext(data -> {
                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), false);
                if (null == gw) {
                    log.error("储能ESS挂载控制器不存在: gwno = {}", ess.getGwno());
                    return;
                }

                EssEquipPo equip = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                    rtReq.getEquipId());
                if (null == equip) {
                    log.error("储能设备没有入库: dno = {}, equipId = {}", ess.getDno(),
                        rtReq.getEquipId());
                    return;
                }

                // 推送数据到device(MQ)
                EssVo<Meter> essVo = new EssVo<>();
                essVo.setGwno(ess.getGwno())
                    .setGwName(gw.getName())
                    .setDno(rtReq.getDno())
                    .setSn(ess.getSn())
                    .setName(ess.getName())
                    .setSiteId(gw.getSiteId())
                    .setSiteName(gw.getSiteName())
                    .setSiteCommId(gw.getSiteCommId())
                    .setEssEquipType(EssEquipType.BATTERY_STACK)
                    .setRtData(data);
                this.dcEventPublisher.publishEssInfo(IotEvent.STATE_CHANGE, essVo);
            })
            .map(data -> true);
    }
}
