package com.cdz360.iot.ess.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备同步时间参数")
@Data
@Accessors(chain = true)
public class EssSyncTimeParam {

    //    PV_INFO=9999-15082041T&ICCID=898602B5191650863487
    @Schema(description = "储能设备的Serial Number")
    @JsonProperty("PV_INFO")
    private String pvInfo;

    @Schema(description = "Communication module ID(SIM card ID or empty)")
    @JsonProperty("ICCID")
    private String iccid;
}
