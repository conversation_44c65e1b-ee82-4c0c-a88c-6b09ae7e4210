package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssGwPushEventFacade {

    @Autowired
    private EssGwPushEventHandlerFactory handlerFactory;

    public Mono<Boolean> process(EssPushEventType type, JsonNode json) {
        EssGwPushEventHandler handler = handlerFactory.getHandler(type);
        if (handler == null) {
            log.error("该设备运行时数据处理不支持. type = {}", type);
            return Mono.just(false);
        }

        return handler.process(json);
    }
}
