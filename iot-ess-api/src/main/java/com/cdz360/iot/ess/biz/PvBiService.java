package com.cdz360.iot.ess.biz;

import com.cdz360.iot.ds.ro.pv.ds.GtiDailyRoDs;
import com.cdz360.iot.model.pv.vo.PvDataBi;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvBiService {

    @Autowired
    private GtiDailyRoDs gtiDailyRoDs;

    public Mono<PvDataBi> pvMapData(List<String> gids) {
        return Mono.just(gids)
            .map(gtiDailyRoDs::getPvMapDataVo);
    }

}
