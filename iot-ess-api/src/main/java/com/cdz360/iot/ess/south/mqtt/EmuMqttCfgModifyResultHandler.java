package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ess.biz.south.EssSouthDataService;
import com.cdz360.iot.ess.utils.RedisMqUtils;
import com.cdz360.iot.model.site.mqtt.ModifyEssEquipCfgReq;
import com.cdz360.iot.mqtt.IMqttMsgHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * EMU配置修改结果上报
 */
@Slf4j
@Service
public class EmuMqttCfgModifyResultHandler implements IMqttMsgHandler {

    @Autowired
    private RedisMqUtils redisMqUtils;

    @Autowired
    private EssSouthDataService essSouthDataService;


    @PostConstruct
    public void init() {
        EmuMqttHandlerFactory.addHandler(EmuMqttHandlerName.EMU_CFG_MODIFY_RESULT, this);
    }

    @Override
    public boolean handleMessage(String handlerName, String msg) {
        log.info("收到 mqtt 消息 {}", msg);
        JsonNode json = JsonUtils.fromJson(msg);
        if (null != json && null != json.get("d")) {
            JsonNode data = json.get("d");
            ModifyEssEquipCfgReq cfgResult = JsonUtils.fromJson(data, ModifyEssEquipCfgReq.class);
            log.info("反序列化后 cfgResult = {}", cfgResult);

            val node = new ObjectMapper().createObjectNode();
            node.put("dno", cfgResult.getEquipDno());
            node.put("data", JsonUtils.toJsonString(data));
            redisMqUtils.publish_equip(
                cfgResult.getEquipDno(), JsonUtils.toJsonString(node));
        }
        return true;
    }

}
