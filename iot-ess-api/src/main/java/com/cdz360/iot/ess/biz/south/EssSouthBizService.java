package com.cdz360.iot.ess.biz.south;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.BmsStatus;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.type.FfsAlarmCode;
import com.cdz360.base.model.es.type.PcsAlarmCode;
import com.cdz360.base.model.es.type.PcsStatus;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.EssStrategyCfgDto;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.meter.vo.MeterTransformationRatio;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.ro.SiteRoDs;
import com.cdz360.iot.ds.ro.ess.ds.BmsRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ess.biz.EssCfgService;
import com.cdz360.iot.ess.biz.EssUpgradeService;
import com.cdz360.iot.ess.biz.RedisEssEquipRtDataService;
import com.cdz360.iot.ess.biz.south.event.EssGwPushEventFacade;
import com.cdz360.iot.ess.biz.south.handler.EssEquipRtDataFacade;
import com.cdz360.iot.ess.model.emu.dto.EmuRtInfoDto;
import com.cdz360.iot.ess.model.param.EssGetEmuCfgParam;
import com.cdz360.iot.ess.utils.RedisMqUtils;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.bms.dto.BatteryStackCfgDto;
import com.cdz360.iot.model.bms.dto.BmsCfgDto;
import com.cdz360.iot.model.bms.po.BmsPo;
import com.cdz360.iot.model.bms.type.BmsVendor;
import com.cdz360.iot.model.ess.dto.EmsCfgDto;
import com.cdz360.iot.model.ess.dto.EquipCfgDto;
import com.cdz360.iot.model.ess.dto.EssDynamicCfgDto;
import com.cdz360.iot.model.ess.dto.EssEquipDto;
import com.cdz360.iot.model.ess.dto.EssEquipReq;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.EssStatusReq;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.meter.dto.MeterCfgDto;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import com.cdz360.iot.model.pcs.dto.PcsCfgDto;
import com.cdz360.iot.model.pcs.po.PcsPo;
import com.cdz360.iot.model.pcs.type.PcsVendor;
import com.cdz360.iot.model.pv.dto.EquipCfgReplyReq;
import com.cdz360.iot.model.pv.dto.EssUpgradeReplyReq;
import com.cdz360.iot.model.site.po.SitePo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssSouthBizService {

    @Autowired
    private RedisMqUtils redisMqUtils;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private BmsRoDs bmsRoDs;

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssCfgService essCfgService;

    @Autowired
    private EssUpgradeService essUpgradeService;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private BatteryBizService batteryBizService;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private EssEquipRtDataFacade essEquipRtDataFacade;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private RedisEssEquipRtDataService redisEssEquipRtDataService;

    @Autowired
    private EssGwPushEventFacade essGwPushEventFacade;

    public Mono<BaseGwResponse> essStatus(EssStatusReq req) {
        if (StringUtils.isBlank(req.getDno())) {
            throw new DcArgumentException("储能设备ESS设备号无效");
        }

        EssPo ess = essRoDs.getByDno(req.getDno());
        if (null == ess) {
            throw new DcArgumentException("储能设备ESS设备号无效");
        }

        EquipStatus oldStatus = ess.getStatus();
        ess.setStatus(req.getStatus()).setSn(req.getSn());
        boolean b = essRwDs.upsetEss(ess);

        if (b && EquipStatus.OFFLINE == req.getStatus()) {
            essEquipRwDs.offlineByEssDno(req.getDno());
        }

        log.info("更新ESS状态: dno = {}, old status = {}, status = {}, b = {}", req.getDno(),
            oldStatus, req.getStatus(), b);
        return Mono.just(new BaseGwResponse());
    }

    @Transactional
    public void updateStatus(String dno, EquipStatus statusIn) {
        EssPo ess = essRoDs.getByDno(dno);
        if (null == ess) {
            log.warn("无法找到储能设备. dno = {}, status = {}", dno, statusIn);
            throw new DcArgumentException("储能设备ESS设备号无效");
        }
        EquipStatus oldStatus = ess.getStatus();
        if (oldStatus == statusIn) {
            return;
        }
        essRwDs.updateStatus(dno, statusIn);
        log.info("更新储能设备状态. dno = {}, 状态 {} ==> {}", dno, oldStatus, statusIn);
    }

    public Mono<BaseGwResponse> essEquips(EssEquipReq req) {
        if (StringUtils.isBlank(req.getDno()) || StringUtils.isBlank(req.getDno())) {
            log.warn("dno或sn信息不全，不做处理: {}", JsonUtils.toJsonString(req));
            throw new DcArgumentException("dno或sn信息不全，不做处理");
        }

        EssPo ess = essRoDs.getByDnoAndSn(req.getDno(), req.getSn());
        if (null == ess) {
            log.warn("ESS不存在，不做处理: {}", JsonUtils.toJsonString(req));
            throw new DcArgumentException("ESS不存在，不做处理");
        }

        if (CollectionUtils.isNotEmpty(req.getEquipData())) {
            this.batchUpsetEssEquip(ess, req.getEquipData());
        }
        return Mono.just(new BaseGwResponse());
    }

    @Transactional
    private void batchUpsetEssEquip(EssPo ess, List<EssEquipDto> equipData) {
        Map<EssEquipType, Long> eTypeMap = new HashMap<>();

        List<EssEquipPo> collect = equipData.stream().map(equip -> {
            EssEquipPo po = new EssEquipPo();
            po.setEquipId(equip.getEquipId()).setEquipTypeId(equip.getEquipTypeId())
                .setEquipType(equip.getEquipType()).setEquipNameCn(equip.getEquipNameCn())
                .setEquipNameEn(equip.getEquipNameEn());

            /**
             * 同类型设备数量大于1时，名称增加数字后缀以便区分
             */
            AtomicReference<String> nameRef = new AtomicReference<>(
                ess.getName() + equip.getEquipType().getDesc());
            eTypeMap.computeIfPresent(equip.getEquipType(), (k, v) -> {
                v = v + 1;
                nameRef.set(nameRef.get() + v);
                return v;
            });
            eTypeMap.computeIfAbsent(equip.getEquipType(), k -> 1L);

            po.setEnable(true).setStatus(EquipStatus.NORMAL).setEssDno(ess.getDno())
                .setName(nameRef.get());

            return po;
        }).collect(Collectors.toList());
        boolean b = this.essEquipRwDs.batchUpset(collect);

        // FIXME: 这里需不需要将下线的设备推送告警????

        // 没有上传的设备设置为下线状态
        b = this.essEquipRwDs.offEquip(ess.getDno(),
            equipData.stream().map(EssEquipDto::getEquipId).collect(Collectors.toList()));
    }

    public Mono<BaseGwResponse> essRtData(EssRtReq<JsonNode> req) {
        if (StringUtils.isBlank(req.getDno()) || StringUtils.isBlank(req.getDno())) {
            log.warn("dno或sn信息不全，不做处理: {}", JsonUtils.toJsonString(req));
            throw new DcArgumentException("dno或sn信息不全，不做处理");
        }

        EssPo ess = essRoDs.getByDnoAndSn(req.getDno(), req.getSn());
        if (null == ess) {
            log.warn("ESS不存在，不做处理: {}", JsonUtils.toJsonString(req));
            throw new DcArgumentException("ESS不存在，不做处理");
        }

        return essEquipRtDataFacade.process(req).map(r -> new BaseGwResponse());
    }

    public Mono<BaseGwResponse> essCfgReply(EquipCfgReplyReq req) {
        if (CollectionUtils.isEmpty(req.getReplyList())) {
            return Mono.just(new BaseGwResponse());
        }

        return Mono.just(req).map(EquipCfgReplyReq::getReplyList).flatMapMany(Flux::fromIterable)
            .map(reply -> {
                String dno = reply.getDno();
                if (StringUtils.isNotBlank(dno)) {
                    EssPo ess = essRoDs.getByDno(dno);
                    if (null != ess) {
                        if (EquipCfgStatus.ARRIVE_GTI.equals(reply.getStatus())) {
                            ess.setCfgSuccessId(ess.getCfgId()).setCfgSuccessTime(new Date());
                        }

                        ess.setCfgStatus(reply.getStatus());

                        if (EquipCfgStatus.ARRIVE_GTI.equals(reply.getStatus())) {
                            ess.setCfgId(ess.getDeliverCfgId());
                        }
                        return essRwDs.upsetEss(ess) ? 1 : 0;
                    }
                }
                return 0;
            }).count().switchIfEmpty(Mono.just(0L)).map(r -> new BaseGwResponse());
    }

    @Transactional(readOnly = true)
    public EmsCfgDto getEmuCfg(GwObjReqMsg<EssGetEmuCfgParam> req) {
        String emuDno = req.getGwno();
        if (StringUtils.isBlank(emuDno)) {
            log.warn("EMU 设备号无效, emuDno = {}", emuDno);
            throw new DcArgumentException("EMU 设备号无效");
        }
        EmsCfgDto cfgDto = new EmsCfgDto();
        EssPo emu = essRoDs.getByDno(emuDno);
        if (emu == null) {
            log.warn("EMU设备号无效,设备信息不存在");
            throw new DcArgumentException("EMU 设备号无效");
        }
        cfgDto.setSiteId(emu.getSiteId());  // 对应的场站ID
        if (StringUtils.isNotBlank(emu.getSiteId())) {
            SitePo site = siteRoDs.getSite(emu.getSiteId());
            if (site != null) {
                cfgDto.setTimeZone(site.getTimeZone());
            }
        }
        if (cfgDto.getTimeZone() == null) {
            cfgDto.setTimeZone("+0");   // 默认时区
        }
        Long cfgId = req.getData().getCfgId();  // 如果有指定的 cfgId, 优先使用
        if (cfgId == null) {
            cfgId = emu.getCfgId();
        }
        EssCfgPo essCfg = this.essCfgRoDs.getByCfgId(cfgId);
        if (essCfg == null) {
            log.warn("获取EMU配置失败. emuDno = {}, cfgId = {}", emuDno, cfgId);
//            throw new DcArgumentException("获取EMU配置失败");
        } else {
            // 配置设置时间
            cfgDto.setUploadDeviceInfoDuration(essCfg.getUploadInfoTime());
            cfgDto.setUploadRtDataDuration(essCfg.getUploadDataTime());
        }
        if (cfgDto.getUploadDeviceInfoDuration() == null) {
            cfgDto.setUploadDeviceInfoDuration(60 * 60 * 4);    // 默认上报时间，单位s
        }
        if (cfgDto.getUploadRtDataDuration() == null) {
            cfgDto.setUploadRtDataDuration(60 * 10);    // 默认上报时间，单位s
        }

        cfgDto.setPriceCfg(this.essCfgService.getPriceCfg(emuDno, cfgId));    // 价格策略
        cfgDto.setDischargePriceCfg(
            this.essCfgService.getDischargePriceCfg(emuDno, cfgId));    // 价格策略
        cfgDto.setChargeStrategy(this.essCfgService.getChargeStrategyDto(emuDno, cfgId)); // 充放电策略

        cfgDto.setPcsList(this.getPcsCfgs(emu.getDno()));   // PCS 列表
        cfgDto.setBmsList(this.getBmsCfgs(emu.getDno()));  // BMS 列表

        cfgDto.setDehList(
            this.getEquipCfgs(emu.getDno(),
                List.of(EssEquipType.DEHUMIDIFIER, EssEquipType.AIR_CONDITION)));   // 除湿器列表
        cfgDto.setFfsList(
            this.getEquipCfgs(emu.getDno(), List.of(EssEquipType.FIRE_FIGHTING)));   // 消防系统
        cfgDto.setUpsList(this.getEquipCfgs(emu.getDno(), List.of(EssEquipType.UPS)));   // UPS列表
        cfgDto.setLiquidList(
            this.getEquipCfgs(emu.getDno(), List.of(EssEquipType.LIQUID_COOLING)));   // 液冷
        cfgDto.setMeterList(this.getMeterCfgList(emu.getDno()));    // 电表

        this.fillEquipCfg(cfgDto);  // 设置 netType 和 netCfg

        cfgDto.setStrategyCfg(this.getEssStrategyCfg(emuDno, cfgId));    // 策略配置: 防逆流、防超限

        log.info("返回 EMU 配置信息. enuDno = {}, cfg = {}", emuDno,
            JsonUtils.toJsonString(cfgDto));
        return cfgDto;
    }


    public void updateEmuCfgResult(String emuDno) {
        if (StringUtils.isBlank(emuDno)) {
            log.warn("EMU 设备号无效, emuDno = {}", emuDno);
            throw new DcArgumentException("EMU 设备号无效");
        }

        EssPo emu = essRoDs.getByDno(emuDno);
        if (emu == null) {
            log.warn("EMU设备号无效,设备信息不存在");
            throw new DcArgumentException("EMU 设备号无效");
        }

        if (EquipCfgStatus.SEND_2_GW.equals(emu.getCfgStatus())) {
            EssPo emu4Update = new EssPo();
            emu4Update.setDno(emu.getDno())
                .setCfgId(emu.getDeliverCfgId())
                .setCfgStatus(EquipCfgStatus.ARRIVE_GTI);
            essRwDs.updateEss(emu4Update);
        }
    }

    private List<PcsCfgDto> getPcsCfgs(String essDno) {
        List<PcsPo> pcsList = this.pcsRoDs.getPcsListByEssDno(essDno);
        return pcsList.stream().map(pcs -> {
            PcsCfgDto cfg = new PcsCfgDto();
            BeanUtils.copyProperties(pcs, cfg);
            return cfg;
        }).collect(Collectors.toList());
    }

    private List<BmsCfgDto> getBmsCfgs(String essDno) {
        List<BmsPo> bmsList = this.bmsRoDs.getBmsListByEssDno(essDno);
        Map<String, List<BatteryStackCfgDto>> stackListMap = batteryBizService.getBatteryStackCfgsByEssDno(
            essDno);

        List<BmsCfgDto> bmsCfgList = bmsList.stream().map(bms -> {
            BmsCfgDto cfg = new BmsCfgDto();
            BeanUtils.copyProperties(bms, cfg);
            if (stackListMap.containsKey(bms.getDno())) {
                cfg.setBatteryStackList(stackListMap.get(bms.getDno()));
            }
            return cfg;
        }).collect(Collectors.toList());

        return bmsCfgList;
    }


    private List<EquipCfgDto> getEquipCfgs(String essDno, List<EssEquipType> equipTypes) {
        ListEssEquipParam listEquipParam = new ListEssEquipParam();
        listEquipParam.setEssDno(essDno);
        listEquipParam.setEquipTypes(equipTypes);
        List<EssEquipPo> equipList = this.essEquipRoDs.getEquipList(listEquipParam);
        return equipList.stream().map(equip -> {
            EquipCfgDto cfg = new EquipCfgDto();
            BeanUtils.copyProperties(equip, cfg);
            return cfg;
        }).collect(Collectors.toList());
    }

    private List<MeterCfgDto> getMeterCfgList(String essDno) {
        List<EssEquipType> equipTypes = List.of(EssEquipType.ESS_GATEWAY_METER,   // 储能并网点电表
            EssEquipType.GRID_GATEWAY_METER, // 电网关口电表
            EssEquipType.ESS_INSIDE_METER,    // 储能内部用电电表
            EssEquipType.HIGH_VOLTAGE_SIDE_METER      // 高压侧用电电表
        );
        ListEssEquipParam listEquipParam = new ListEssEquipParam();
        listEquipParam.setEssDno(essDno);
        listEquipParam.setEquipTypes(equipTypes);
        List<EssEquipPo> equipList = this.essEquipRoDs.getEquipList(listEquipParam);
        List<String> dnos = equipList.stream().map(EssEquipPo::getDno).collect(Collectors.toList());
        Map<String, MeterVo> meters = new ConcurrentHashMap<>();
        if (CollectionUtils.isNotEmpty(dnos)) {
            MeterListParam listMeterParam = new MeterListParam();
            listMeterParam.setDnoList(dnos).setStart(0L).setSize(dnos.size());
            List<MeterVo> meterList = this.meterRoDs.getMeterVoList(listMeterParam);
            for (var m : meterList) {
                meters.put(m.getDno(), m);
            }
        }
        return equipList.stream().map(equip -> {
            MeterCfgDto cfg = new MeterCfgDto();
            BeanUtils.copyProperties(equip, cfg);
            MeterVo m = meters.get(cfg.getDno());
            if (m != null) {
                cfg.setDeviceAddr(m.getNo()).setSamplingTime(1);
                cfg.setTr(new MeterTransformationRatio());
                cfg.getTr().setCtr(m.getCtr())
                    .setVtr(m.getVtr());
            }
            return cfg;
        }).collect(Collectors.toList());
    }

    private void fillEquipCfg(EmsCfgDto cfgDto) {
        List<String> dnoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cfgDto.getPcsList())) {
            dnoList.addAll(
                cfgDto.getPcsList().stream().map(PcsPo::getDno).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(cfgDto.getBmsList())) {
            dnoList.addAll(
                cfgDto.getBmsList().stream().map(BmsPo::getDno).collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(dnoList)) {
            return;
        }
        Map<String, EssEquipPo> equipMap = new HashMap();
        ListEssEquipParam listEquipParam = new ListEssEquipParam();
        listEquipParam.setDnoList(dnoList).setSize(dnoList.size());

        List<EssEquipPo> equipList = this.essEquipRoDs.getEquipList(listEquipParam);
        for (EssEquipPo equip : equipList) {
            equipMap.put(equip.getDno(), equip);
        }
        for (PcsCfgDto pcs : cfgDto.getPcsList()) {
            EssEquipPo equip = equipMap.get(pcs.getDno());
            if (equip != null) {
                pcs.setNetType(equip.getNetType()).setNetCfg(equip.getNetCfg())
                    .setModbusAddrCfg(equip.getModbusAddrCfg())
                    .setModbusTvCfg(equip.getModbusTvCfg())
                    .setLogCfg(equip.getLogCfg())
                    .setVendor(PcsVendor.valueOf(equip.getVendor()));
            }
        }
        for (BmsCfgDto bms : cfgDto.getBmsList()) {
            EssEquipPo equip = equipMap.get(bms.getDno());
            if (equip != null) {
                bms.setNetType(equip.getNetType())
                    .setNetCfg(equip.getNetCfg())
                    .setModbusAddrCfg(equip.getModbusAddrCfg())
                    .setModbusTvCfg(equip.getModbusTvCfg())
                    .setLogCfg(equip.getLogCfg())
                    .setVendor(BmsVendor.valueOf(equip.getVendor()));
            }
        }


    }

    /**
     * 策略配置: 防逆流、防超限
     */
    private EssStrategyCfgDto getEssStrategyCfg(String essDno, Long essCfgId) {
        EssCfgPo essCfg = essCfgRoDs.getByCfgId(essCfgId);
        if (essCfg == null) {
            log.warn("获取充放电配置失败. essDno = {}, cfgId = {}", essDno, essCfgId);
//            throw new DcArgumentException("获取EMU配置失败");
            return null;
        }

        EssStrategyCfgDto strategy = new EssStrategyCfgDto();
        if (null != essCfg.getStrategyCfg()) {
            BeanUtils.copyProperties(essCfg.getStrategyCfg(), strategy);
        }
        return strategy;
    }

    @Transactional
    public void publishEmsInfo(EmuRtInfoDto essRtInfo) {
//
        EssPo ess = essRoDs.getByDno(essRtInfo.getDno());
        SitePo site = siteRoDs.getSite(ess.getSiteId());
        // 推送数据到device(MQ)
        EssVo<EmuRtInfoDto> essVo = new EssVo<>();
        essVo.setGwno(ess.getGwno()).setGwName(ess.getName()).setDno(ess.getDno())
            .setSn(ess.getSn()).setName(ess.getName()).setSiteId(ess.getSiteId())
            .setSiteName(site.getName()).setSiteCommId(site.getCommId())
            .setEssEquipType(EssEquipType.EMS).setErrorList(new ArrayList<>());

        this.buildPcsEvent(essVo, essRtInfo);
        this.buildBmsEvent(essVo, essRtInfo);
        this.buildFfsEvent(essVo, essRtInfo);

        this.dcEventPublisher.publishEssInfo(IotEvent.STATE_CHANGE, essVo);
        log.info("推送Ems设备信息. essDno = {}, errorList = {}", ess.getDno(),
            essVo.getErrorList());
    }

    private void buildPcsEvent(EssVo<?> ess, EmuRtInfoDto essRtInfo) {
        for (var pcsRtInfo : essRtInfo.getPcsList()) {
            Long offlineCode = null;
            PcsPo pcs = pcsRoDs.getByDno(pcsRtInfo.getDno());
            if (pcsRtInfo.getStatus() == PcsStatus.OFFLINE) {
                offlineCode = 1L;   //
            }
            EssVo.ErrorObj errorObj = new EssVo.ErrorObj();
            EssVo.ErrorEquip errorEquip = new EssVo.ErrorEquip();
            errorEquip.setDno(pcs.getDno()).setEquipId(pcs.getId()).setEquipName(pcs.getName());
            // 告警信息
            if (CollectionUtils.isNotEmpty(pcsRtInfo.getAlarms())) {

                errorEquip.setErrorCodeList(
                    pcsRtInfo.getAlarms().stream().map(o -> (long) o.getCode())
                        .collect(Collectors.toList()));
                if (offlineCode != null) {
                    errorEquip.getErrorCodeList().add(offlineCode);
                }
            } else if (offlineCode != null) {
                errorEquip.setErrorCodeList(List.of(offlineCode));
            }
            errorObj.setEquipType(EssEquipType.PCS).setLts(
                    LocalDateTime.ofEpochSecond(pcsRtInfo.getTs(), 0, ZoneOffset.of(essRtInfo.getTz())))
                .setTz(essRtInfo.getTz())
                .setEquipList(List.of(errorEquip)); // 即使没有故障，也需要构建一个空的对象， 用于 monitor 那边关闭故障
            ess.getErrorList().add(errorObj);
        }
    }


    private void buildBmsEvent(EssVo<?> ess, EmuRtInfoDto essRtInfo) {
        for (BmsRtInfo bmsRtInfo : essRtInfo.getBmsList()) {
            BmsAlarmCode offlineError = null;
            BmsPo bms = bmsRoDs.getByDno(bmsRtInfo.getDno());
            if (bmsRtInfo.getStatus() == BmsStatus.OFFLINE) {
                offlineError = BmsAlarmCode.OFFLINE;
            }

            EssVo.ErrorObj errorObj = new EssVo.ErrorObj();
            EssVo.ErrorEquip errorEquip = new EssVo.ErrorEquip();
            errorEquip.setDno(bms.getDno()).setEquipName(bms.getName());

            List<BmsAlarmCode> bmsErrorList = new ArrayList<>();
            if (offlineError != null) {
                bmsErrorList.add(offlineError);
            }
            for (var stack : bmsRtInfo.getStackInfoList()) {    // 提取电池堆的告警信息
                if (CollectionUtils.isNotEmpty(stack.getAlarms())) {
                    bmsErrorList.addAll(stack.getAlarms());
                }
                for (var bundle : stack.getBundleList()) {  // 提取电池蔟的告警信息
                    if (CollectionUtils.isNotEmpty(bundle.getAlarms())) {
                        bmsErrorList.addAll(bundle.getAlarms());
                    }
                }
            }
            // 去重
            bmsErrorList = bmsErrorList.stream().sorted().distinct().collect(Collectors.toList());
            errorEquip.setErrorCodeList(
                bmsErrorList.stream().map(o -> (long) o.getCode()).collect(Collectors.toList()));

            errorObj.setEquipType(EssEquipType.BMS).setLts(
                    LocalDateTime.ofEpochSecond(bmsRtInfo.getTs(), 0, ZoneOffset.of(essRtInfo.getTz())))
                .setTz(essRtInfo.getTz())
                .setEquipList(List.of(errorEquip)); // 即使没有故障，也需要构建一个空的对象， 用于 monitor 那边关闭故障
            ess.getErrorList().add(errorObj);
        }
    }


    private void buildFfsEvent(EssVo<?> ess, EmuRtInfoDto essRtInfo) {
        for (var info : essRtInfo.getFfsList()) {
            FfsAlarmCode offlineError = null;
            EssEquipPo equip = essEquipRoDs.getByDno(info.getDno());
            if (info.getStatus() == EquipStatus.OFFLINE) {
                offlineError = FfsAlarmCode.OFFLINE;   //
            }
            EssVo.ErrorObj errorObj = new EssVo.ErrorObj();
            EssVo.ErrorEquip errorEquip = new EssVo.ErrorEquip();
            errorEquip.setDno(equip.getDno()).setEquipId(equip.getId())
                .setEquipName(equip.getName());

            if (offlineError != null) {
                errorEquip.setErrorCodeList(List.of((long) offlineError.getCode()));
            }
            if (Boolean.FALSE.equals(info.getFireAlarmStatus()) || Boolean.FALSE.equals(
                info.getStartStatus()) || Boolean.FALSE.equals(info.getWorkStatus())) {
                errorEquip.setErrorCodeList(List.of((long) FfsAlarmCode.WORK.getCode()));
            }
            if (Boolean.FALSE.equals(info.getErrorStatus())) {
                errorEquip.setErrorCodeList(List.of((long) FfsAlarmCode.FAULT.getCode()));
            }
            errorObj.setEquipType(EssEquipType.FIRE_FIGHTING).setLts(
                    LocalDateTime.ofEpochSecond(info.getTs(), 0, ZoneOffset.of(essRtInfo.getTz())))
                .setTz(essRtInfo.getTz())
                .setEquipList(List.of(errorEquip)); // 即使没有故障，也需要构建一个空的对象， 用于 monitor 那边关闭故障
            ess.getErrorList().add(errorObj);
        }
    }

    /**
     * 检查PCS的状态信息是否变更，是否要做MQ推送
     */
    private boolean isPcsInfoChanged(PcsRtInfo oldInfo, PcsRtInfo newInfo) {
        if (oldInfo == null || oldInfo.getStatus() == null) {
            return true;
        } else if (!oldInfo.getStatus().equals(newInfo.getStatus())) {    // 检查状态是否一致
            return true;
        } else if (CollectionUtils.isEmpty(oldInfo.getAlarms()) && CollectionUtils.isNotEmpty(
            newInfo.getAlarms())) { // 检查故障/告警列表
            return true;
        } else if (CollectionUtils.isNotEmpty(oldInfo.getAlarms()) && CollectionUtils.isEmpty(
            newInfo.getAlarms())) {    // 检查故障/告警列表
            return true;
        } else if (CollectionUtils.isEmpty(oldInfo.getAlarms()) && CollectionUtils.isEmpty(
            newInfo.getAlarms())) {
            return false;
        }
        String oldErrorCodes = oldInfo.getAlarms().stream().map(PcsAlarmCode::getCode)
            .sorted().collect(Collectors.toList()).toString();
        String newErrorCodes = newInfo.getAlarms().stream().map(PcsAlarmCode::getCode)
            .sorted().collect(Collectors.toList()).toString();
        if (!oldErrorCodes.equals(newErrorCodes)) {
            return true;
        } else {
            return false;
        }
    }

    public void essGwPushEvent(JsonNode json) {
        JsonNode type = json.get("type");
        if (null == type || StringUtils.isBlank(type.asText())) {
            return;
        }
        EssPushEventType eventType = EssPushEventType.valueOf(type.asText());
        essGwPushEventFacade.process(eventType, json)
            .block(Duration.ofSeconds(30L));
    }

    public void essCfgUpload(EmsCfgDto cfgData) {
        if (StringUtils.isNotBlank(cfgData.getDno())) {
            EssPo ess = essRoDs.getByDno(cfgData.getDno());
            if (null != ess) {
                // 不支持分时段最大功率设置
                // supportDivision = false
                redisEssEquipRtDataService.essRealTimeCfg(
                    ess.getDno(), JsonUtils.toJsonString(cfgData));
            }
        }
    }

    public void essCfgInfo(InOutTimeRangeDto cfgData) {
        if (StringUtils.isNotBlank(cfgData.getDno())) {
            EssPo ess = essRoDs.getByDno(cfgData.getDno());
            if (null != ess) {
                // 不支持分时段最大功率设置
                // supportDivision = false
                redisEssEquipRtDataService.essRealTimeCfg(
                    ess.getDno(), JsonUtils.toJsonString(cfgData));
            }
        }
    }

    public void essDynamicCfgInfo(EssDynamicCfgDto cfgData) {
        if (StringUtils.isNotBlank(cfgData.getDno())) {
            ObjectNode node = new ObjectMapper().createObjectNode();
            node.put("seq", cfgData.getSeq());
            node.put("dno", cfgData.getDno());
            node.put("type", "OP_RESS_RW_CFG");
            node.put("data", JsonUtils.toJsonString(cfgData));
            redisMqUtils.publish_dynamic(
                cfgData.getDno(), JsonUtils.toJsonString(node));
        }
    }

    public Mono<BaseGwResponse> essUpgradeReply(EssUpgradeReplyReq data) {
        log.info("设备升级结果推送: {}", data);
        return essUpgradeService.essUpgradeReply(data);
    }
}
