package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.UserDeviceRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuEssRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ess.biz.RedisEssEquipRtDataService;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class AbstractEssGwPushEventHandler implements EssGwPushEventHandler {

    @Autowired
    protected GwInfoRoDs gwInfoRoDs;

    @Autowired
    protected UserDeviceRefRoDs userDeviceRefRoDs;

    @Autowired
    protected EssDtuRoDs essDtuRoDs;

    @Autowired
    protected EssDtuEssRefRoDs essDtuEssRefRoDs;

    @Autowired
    protected EssRoDs essRoDs;

    @Autowired
    protected EssRwDs essRwDs;

    @Autowired
    protected EssEquipRoDs essEquipRoDs;

    @Autowired
    protected EssEquipRwDs essEquipRwDs;

    @Autowired
    protected EssGwPushEventHandlerFactory handlerFactory;

    @Autowired
    protected RedisEssEquipRtDataService redisEssEquipRtDataService;

    protected Mono<EssRtReq<JsonNode>> pushData2Redis(String timeZone, EssRtReq<JsonNode> rtReq) {
        return Mono.just(rtReq)
            .doOnNext(rt -> redisEssEquipRtDataService.pushRtData(
                timeZone, rt.getDno(), rt.getEquipId(), rt.getRtData()));
    }

    protected void pushData2Redis(String timeZone, String essDno, JsonNode data) {
        redisEssEquipRtDataService.pushRtData(timeZone, essDno, 0L, data); // 户储所有数据存储到redis
    }

    protected void recordStatus2Redis(String essDno, String data) {
        redisEssEquipRtDataService.recordStatus2Redis(essDno, data); // 户储所有数据存储到redis
    }
}
