package com.cdz360.iot.ess.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.iot.model.evse.dto.UpgradeTaskDto;
import com.cdz360.iot.model.evse.param.StartUpgradeTaskParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
    fallbackFactory = DeviceMgmFeignHystrix.class)
public interface DeviceMgmFeignClient {

    // 启动升级任务
    @PostMapping("/device/upgrade/startUpgradeTask")
    Mono<ObjectResponse<UpgradeTaskDto>> startUpgradeTask(
        @RequestBody StartUpgradeTaskParam param);

}
