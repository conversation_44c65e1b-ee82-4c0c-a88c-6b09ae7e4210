package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.common.utils.TimeZoneParser;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.UserDeviceRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuEssRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssInOutCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.UserDeviceRefRwDs;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.dto.BindDeviceDto;
import com.cdz360.iot.model.ess.dto.ModifyDeviceBasicDto;
import com.cdz360.iot.model.ess.dto.UserDeviceCountDto;
import com.cdz360.iot.model.ess.mqtt.DeliverEssBaseInfoReq;
import com.cdz360.iot.model.ess.param.CountUserEssParam;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.param.ListEssEquipParam;
import com.cdz360.iot.model.ess.param.UpdateBatteryNameplateParam;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.po.UserDeviceRefPo;
import com.cdz360.iot.model.ess.vo.EquipInfo;
import com.cdz360.iot.model.ess.vo.EssAttachCfgVo;
import com.cdz360.iot.model.ess.vo.EssAttachDtuVo;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class UserEssService {

    @Autowired
    private EssDtuRoDs essDtuRoDs;

    @Autowired
    private EssDtuEssRefRoDs essDtuEssRefRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssInOutCfgRoDs essInOutCfgRoDs;

    @Autowired
    private UserDeviceRefRwDs userDeviceRefRwDs;

    @Autowired
    private UserDeviceRefRoDs userDeviceRefRoDs;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private MqService mqService;

    @Autowired
    private SequenceRwService sequenceRwService;

    public Mono<ListResponse<UserEssVo>> fetchUserEss(FetchUserEssParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.essRoDs::userEssList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (Boolean.TRUE.equals(param.getTotal())) {
                    res.setTotal(this.essRoDs.userEssCount(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<List<UserDeviceCountDto>> countUserEss(CountUserEssParam param) {
        return Mono.just(essRoDs.countUserEss(param));
    }

    public Mono<EssPo> userEssBind(BindDeviceDto dto) {
        IotAssert.isNotNull(dto.getUid(), "用户ID无效");
//        IotAssert.isNotNull(dto.getEssDtuType(), "设备类型不能为空");
//        IotAssert.isNotNull(dto.getCommunicationWay(), "通讯方式不能为空");
//        IotAssert.isNotBlank(dto.getDeviceName(), "设备名称不能为空");
//        IotAssert.isNotBlank(dto.getDeviceModel(), "设备型号不能为空");
//        IotAssert.isNotBlank(dto.getHardwareVer(), "硬件版本不能为空");
//        IotAssert.isNotBlank(dto.getSoftwareVer(), "软件版本不能为空");
//        IotAssert.isNotBlank(dto.getIccid(), "ICCID不能为空");
        IotAssert.isNotBlank(dto.getSerialNo(), "ess.sn.invalid");

        EssDtuPo dtu = essDtuRoDs.getBySerialNo(dto.getSerialNo());
        IotAssert.isNotNull(dtu, "ess.sn.invalid");

        List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(dto.getSerialNo());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(refList), "当前通讯设备没有挂在ESS");

        final EssPo ess = essRoDs.getByDno(refList.get(0).getDno()); // 户储ESS和通讯设备是1:1关系
        IotAssert.isNotNull(ess, "ess.dno.invalid");

//        final EssCfgPo cfg = essCfgRoDs.getByCfgId(ess.getCfgId());
//        IotAssert.isNotNull(cfg, "ess.config.invalid");
//        IotAssert.isNotBlank(dto.getAuthCode(), "ess.auth.code.invalid");
//        IotAssert.isTrue(dto.getAuthCode().equals(cfg.getAuthCode()), "ess.bind.auth-code.invalid");

        if (dto.getTimeZone() != null) {
            IotAssert.isTrue(TimeZoneParser.isValidTimeZone(dto.getTimeZone()),
                "ess.timeZone.invalid");
        }
        // 更新时区信息
        ess.setCountryCode(dto.getCountryCode())
            .setTimeZone(dto.getTimeZone());
        boolean bl = essRwDs.updateEss(new EssPo()
            .setDno(ess.getDno())
            .setCountryCode(dto.getCountryCode())
            .setTimeZone(dto.getTimeZone()));
        if (!bl) {
            log.warn("更新设备时区信息失败: {} / {}", ess.getDno(), dto.getTimeZone());
        }

        UserDeviceRefPo ref = userDeviceRefRoDs.getOneValidByUidAndDno(
            dto.getUid(), ess.getDno());
        if (null == ref) {
            ref = userDeviceRefRoDs.getOneValidByDno(ess.getDno());
            IotAssert.isNull(ref, "ess.bind.already.bound");

            ref = userDeviceRefRoDs.getOneByUidAndDno(dto.getUid(), ess.getDno());
            boolean b;
            if (null != ref) {
                ref.setCountryCode(dto.getCountryCode())
                    .setEnable(true)
                    .setMaster(true);
                b = userDeviceRefRwDs.updateUserDeviceRef(ref);
            } else {
                ref = new UserDeviceRefPo()
                    .setEnable(true)
                    .setMaster(true)
                    .setDno(ess.getDno())
                    .setCountryCode(dto.getCountryCode())
                    .setUid(dto.getUid());
                b = userDeviceRefRwDs.insertUserDeviceRef(ref);
            }
            if (!b) {
                log.error("绑定失败: {}", JsonUtils.toJsonString(ref));
                throw new DcServiceException("ess.bind.fail");
            }
        }

        // 下发设备时区信息
        this.essTimeZonePublish(dtu, ess);

        return Mono.just(ess);
    }

    private void essTimeZonePublish(final EssDtuPo dtu, final EssPo ess) {
        if (dtu != null) {
            GwInfoDto gwInfo = gwInfoRoDs.getByGwno(dtu.getGwno());
            if (gwInfo != null) {
                if (gwInfo.getVer() == IotConstants.IOT_GW_VER_1) {
                    throw new DcServiceException("废弃的逻辑", Level.ERROR);
                } else if (gwInfo.getVer() == IotConstants.IOT_GW_VER_2
                    || gwInfo.getVer() == IotConstants.IOT_GW_VER_3) {
                    DeliverEssBaseInfoReq.builder builder = new DeliverEssBaseInfoReq.builder(
                        dtu.getGwno(), this.sequenceRwService.getNextOutRequestSeq());
                    builder.dno(ess.getDno())
                        .serialNo(dtu.getSerialNo())
                        .timeZone(ess.getTimeZone());
                    DeliverEssBaseInfoReq.REQ req = builder.build();
                    mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(req));
                } else {
                    log.error("网关版本无效: {}，无法下发获取ESS配置指令。", dtu.getGwno());
                }
            }
        }
    }

    public Mono<EssPo> userEssDetail(Long uid, String sn, String dno) {
        if (StringUtils.isNotBlank(sn)) {
            List<EssDtuEssRefPo> dtu = essDtuEssRefRoDs.getBySerialNo(sn);
            if (CollectionUtils.isEmpty(dtu)) {
                return Mono.empty();
            }

            dno = dtu.get(0).getDno();
        }

        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        if (null != uid) {
            final UserDeviceRefPo device = userDeviceRefRoDs.getOneValidByUidAndDno(uid, dno);
            IotAssert.isNotNull(device, "ess.dno.invalid");
        }

        return Mono.justOrEmpty(essRoDs.getByDno(dno));
    }

    public Mono<Long> userEssMaster(String dno) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        val ess = essRoDs.getByDno(dno);
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        val master = userDeviceRefRoDs.getMasterByDno(dno);
        return Mono.justOrEmpty(master != null ? master.getUid() : null);
    }

    @Transactional
    public Mono<BaseResponse> updateBatteryNameplate(UpdateBatteryNameplateParam param) {
        IotAssert.isNotBlank(param.getEssDno(), "ess.dno.invalid");

        val ess = essRoDs.getByDno(param.getEssDno());
        IotAssert.isNotNull(ess, "ess.dno.invalid");

        // 更新设备铭牌信息
        param.getBatteryInfoList().forEach(x -> essEquipRwDs.updateEssEquipNameplate(
            param.getEssDno(), x.getEquipId(), x.getNameplateInfo()));

        return Mono.just(RestUtils.success());
    }

    public Mono<EssAttachDtuVo> userEssDetailAttachDtu(Long uid, String dno) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        if (null != uid) {
            final UserDeviceRefPo device = userDeviceRefRoDs.getOneValidByUidAndDno(uid, dno);
            IotAssert.isNotNull(device, "ess.dno.invalid");
        }

        final EssPo ess = essRoDs.getByDno(dno);
        if (null == ess) {
            return Mono.empty();
        }

        EssAttachDtuVo result = new EssAttachDtuVo();
        BeanUtils.copyProperties(ess, result);

        // DTU 信息
        EssDtuPo dtu = essDtuRoDs.getByEssDno(dno);
        result.setDtuSerialNo(dtu.getSerialNo())
            .setDtuGwno(dtu.getGwno())
            .setDtuEssDtuType(dtu.getEssDtuType())
            .setDtuCommunicationWay(dtu.getCommunicationWay())
            .setDtuDeviceName(dtu.getDeviceName())
            .setDtuDeviceModel(dtu.getDeviceModel())
            .setDtuHardwareVer(dtu.getHardwareVer())
            .setDtuSoftwareVer(dtu.getSoftwareVer())
            .setDtuIccid(dtu.getIccid());

        ListEssEquipParam searchBatParam = new ListEssEquipParam();
        searchBatParam.setEssDno(dno).setEquipType(EssEquipType.BMS.getCode()).setSize(100);
        List<EssEquipPo> bmsInfos = essEquipRoDs.getEquipList(searchBatParam);
        if (CollectionUtils.isNotEmpty(bmsInfos)) {
            result.setBmsInfoList(
                bmsInfos.stream().map(x -> new EquipInfo()
                        .setEquipId(x.getEquipId())
                        .setStatus(x.getStatus()))
                    .collect(Collectors.toList()));
        }

        searchBatParam.setEssDno(dno).setEquipType(EssEquipType.PV_INV.getCode()).setSize(100);
        List<EssEquipPo> pvInfos = essEquipRoDs.getEquipList(searchBatParam);
        if (CollectionUtils.isNotEmpty(pvInfos)) {
            result.setPvInfoList(
                pvInfos.stream().map(x -> new EquipInfo()
                        .setEquipId(x.getEquipId())
                        .setStatus(x.getStatus()))
                    .collect(Collectors.toList()));
        }

        return Mono.just(result);
    }

    public Mono<EssAttachCfgVo> userEssDetailAttachCfg(Long uid, String dno) {
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        if (null != uid) {
            final UserDeviceRefPo device = userDeviceRefRoDs.getOneValidByUidAndDno(uid, dno);
            IotAssert.isNotNull(device, "ess.dno.invalid");
        }

        final EssPo ess = essRoDs.getByDno(dno);
        if (null == ess) {
            return Mono.empty();
        }

        EssAttachCfgVo result = new EssAttachCfgVo();
        BeanUtils.copyProperties(ess, result);

        // DTU 信息
        EssDtuPo dtu = essDtuRoDs.getByEssDno(dno);
        if (null != dtu) {
            result.setDtuSerialNo(dtu.getSerialNo());
        }

        if (null != ess.getCfgId()) {
            final EssCfgPo cfg = essCfgRoDs.getByCfgId(ess.getCfgId());
            if (null != cfg) {
                result.setAuthCode(cfg.getAuthCode())
                    .setStrategy(cfg.getStrategy())
                    .setOtherStrategy(cfg.getOtherStrategy())
                    .setRepeatCycle(cfg.getRepeatCycle())
                    .setEffectiveStartTime(cfg.getEffectiveStartTime())
                    .setEffectiveEndTime(cfg.getEffectiveEndTime());

                if (cfg.getChargeStrategy() != null) {
                    if (CollectionUtils.isNotEmpty(
                        cfg.getChargeStrategy().getItems())) {
                        result.getInOutItems().addAll(cfg.getChargeStrategy().getItems());
                    }
                }

                result.setSupportDivision(false); // 当前固定不支持，后续根据实际调整
            }
        }

        // 如果是下发中，则将下发的数据也返回
        if (List.of(EquipCfgStatus.SEND_2_GW, EquipCfgStatus.ARRIVE_GW)
            .contains(ess.getCfgStatus())) {
            if (null != ess.getDeliverCfgId()) {
                final EssCfgPo cfg = essCfgRoDs.getByCfgId(ess.getDeliverCfgId());
                if (null != cfg) {
                    result.setDeliverCfgId(ess.getDeliverCfgId())
                        .setDeliverStrategy(cfg.getStrategy())
                        .setDeliverOtherStrategy(cfg.getOtherStrategy())
                        .setDeliverRepeatCycle(cfg.getRepeatCycle())
                        .setDeliverEffectiveStartTime(cfg.getEffectiveStartTime())
                        .setDeliverEffectiveEndTime(cfg.getEffectiveEndTime())
                        .setDeliverCreateTime(
                            cfg.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());

                    if (cfg.getChargeStrategy() != null) {
                        if (CollectionUtils.isNotEmpty(
                            cfg.getChargeStrategy().getItems())) {
                            result.getDeliverInOutItems()
                                .addAll(cfg.getChargeStrategy().getItems());
                        }
                    }
                }
            }
        }

        return Mono.just(result);
    }

    public Mono<EssPo> userEssDelete(Long uid, String dno) {
        IotAssert.isNotNull(uid, "用户ID无效");
        IotAssert.isNotBlank(dno, "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dno);
        if (null == ess) {
            return Mono.empty();
        }

        final UserDeviceRefPo device = userDeviceRefRoDs.getOneValidByUidAndDno(uid, dno);
        IotAssert.isNotNull(device, "ess.dno.invalid");
        boolean b = userDeviceRefRwDs.resetNewMaster(device);
        if (!b) {
            log.warn("删除设备不成功: {}", dno);
            throw new DcServiceException("ess.delete.fail");
        }
        return Mono.just(ess);
    }

    public Mono<EssPo> userEssModifyBasicInfo(ModifyDeviceBasicDto dto) {
        IotAssert.isNotBlank(dto.getDno(), "ess.dno.invalid");

        final EssPo ess = essRoDs.getByDno(dto.getDno());
        if (null == ess) {
            return Mono.empty();
        }

        boolean update = false;
        EssPo updateEss = new EssPo().setDno(ess.getDno());
        if (StringUtils.isNotBlank(dto.getName()) && !dto.getName().equals(ess.getName())) {
            update = true;
            updateEss.setName(dto.getName() != null ? dto.getName() : "");
        }

        boolean updateTZ = false;
        if (StringUtils.isNotBlank(dto.getTimeZone()) && !dto.getTimeZone()
            .equals(ess.getTimeZone())) {
            String timeZone = dto.getTimeZone().trim();
            IotAssert.isTrue(TimeZoneParser.isValidTimeZone(timeZone),
                "ess.timeZone.invalid");
            update = true;
            updateTZ = true;
            updateEss.setTimeZone(timeZone);
            ess.setTimeZone(timeZone);
        }

        if (update) {
            essRwDs.updateEss(updateEss);
        }

        if (updateTZ) {
            EssDtuPo dtu = essDtuRoDs.getByEssDno(ess.getDno());
            this.essTimeZonePublish(dtu, ess);
        }
        return Mono.just(ess);
    }

    public Mono<Integer> scanAndBindEss(final Long shareUid, final Long bindUid) {
        IotAssert.isNotNull(shareUid, "分享设备用户ID无效");
        IotAssert.isNotNull(bindUid, "扫码绑定用户ID无效");

        if (shareUid.equals(bindUid)) {
            return Mono.just(0);
        }

        final List<UserDeviceRefPo> refPos = userDeviceRefRoDs.getByUidAndMaster(
            shareUid, true);
        if (CollectionUtils.isEmpty(refPos)) {
            return Mono.just(0);
        }

        refPos.forEach(x -> {
            UserDeviceRefPo update = userDeviceRefRoDs.getOneByUidAndDno(
                bindUid, x.getDno());
            if (null == update) {
                update = new UserDeviceRefPo()
                    .setUid(bindUid)
                    .setDno(x.getDno());
            }

            update.setCountryCode(x.getCountryCode())
                .setEnable(true)
                .setMaster(false);
            userDeviceRefRwDs.upsetUserDeviceRef(update);
        });

        return Mono.just(refPos.size());
    }
}
