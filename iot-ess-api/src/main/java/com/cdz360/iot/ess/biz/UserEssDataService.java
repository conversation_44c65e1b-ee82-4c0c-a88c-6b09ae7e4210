package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.model.ess.vo.DayUserEssRtDataBi;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserEssDataService {

    @Autowired
    private EssMongoBizService essMongoBizService;

    public List<DayUserEssRtDataBi> userEssDayOfRangeKwh(SamplingParam param) {
        IotAssert.isNotBlank(param.getDno(), "ess.dno.invalid");
        return essMongoBizService.userEssDayOfRangeKwh(param)
            .stream().sorted(Comparator.comparing(DayUserEssRtDataBi::getDate))
            .collect(Collectors.toList());
    }

}
