package com.cdz360.iot.ess.model.mongo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ess-collect-data")
@Schema(description = "ESS设备采集数据信息")
@Data
@Accessors(chain = true)
public class EssCollectDataInMongo {

    @Schema(description = "ESS设备唯一编号")
    private String dno;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @Schema(description = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    public EssCollectDataInMongo(LocalDateTime time, String dno) {
        this.time = time;
        this.dno = dno;
    }

    @Schema(description = "BMS采集数据")
    private BmsCollectData bmsCollectData;
}
