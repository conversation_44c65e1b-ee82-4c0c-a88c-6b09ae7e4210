package com.cdz360.iot.ess.model.emu.dto;

import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.DehRtData;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.FfsRtData;
import com.cdz360.base.model.es.vo.LiquidRtData;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.model.es.vo.UpsRtData;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EmuRtDataDto extends EmuRtData {

    /**
     * 消息序列号
     */
    private Long seq;

    /**
     * EMU设备的（本地）日期
     */
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;


    private List<PcsRtData> pcsData;

    private List<BmsRtData> bmsData;

    /**
     * 液冷数据
     */
    private List<LiquidRtData> liquidData;

    private List<DehRtData> dehData;

    private List<UpsRtData> upsData;

    private List<MeterRtData> meterData;

    private List<FfsRtData> ffsData;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
