package com.cdz360.iot.ess.biz;

import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.UpgradeLogRwDs;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.evse.type.UpgradeStatus;
import com.cdz360.iot.model.gw.po.UpgradeLogPo;
import com.cdz360.iot.model.pv.dto.EssUpgradeReplyReq;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssUpgradeService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private UpgradeLogRwDs upgradeLogRwDs;

    public Mono<BaseGwResponse> essUpgradeReply(EssUpgradeReplyReq data) {
        EssPo ess = essRoDs.getByDno(data.getDno());
        if (null == ess || null == ess.getExpectUpgradeLogId()) {
            return Mono.just(new BaseGwResponse().setStatus(-1).setError("设备编号无效"));
        }

        Long upgradeLogId = ess.getExpectUpgradeLogId();
        UpgradeLogPo upgradeLog = upgradeLogRwDs.getById(upgradeLogId, true);
        if (UpgradeStatus.UPGRADE_SUCCESS.equals(upgradeLog.getUpgradeStatus())) {
            log.warn("升级已经成功，不需要重新调整记录进度");
            return Mono.just(new BaseGwResponse());
        }
        UpgradeLogPo updateLog = new UpgradeLogPo().setId(upgradeLogId);
        switch (data.getStatus()) {
            case INIT:
                updateLog.setReceiveTime(new Date());
            case ING:
                updateLog.setUpgradeStatus(UpgradeStatus.CMD_RECEIVE);
                break;
            case COMPLETED:
                updateLog.setUpgradeStatus(UpgradeStatus.UPGRADE_SUCCESS)
                    .setFinishTime(new Date());
                break;
            case FAIL:
                updateLog.setUpgradeStatus(UpgradeStatus.UPGRADE_FAIL)
                    .setRemark(data.getError())
                    .setFinishTime(new Date());
                break;
            default:
                return Mono.just(new BaseGwResponse().setStatus(-2).setError("状态码不识别"));
        }

        updateLog.setProgress(data.getProgress());
        upgradeLogRwDs.updateUpgradeLog(updateLog);

        if (UpgradeStatus.UPGRADE_SUCCESS.equals(updateLog.getUpgradeStatus())) {
            essRwDs.updateEss(new EssPo().setDno(ess.getDno())
                .setActualUpgradeLogId(ess.getExpectUpgradeLogId()));
        }

        return Mono.just(new BaseGwResponse());
    }
}
