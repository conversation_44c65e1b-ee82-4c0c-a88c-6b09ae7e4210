package com.cdz360.iot.ess.rest.internal.pv;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.PvBiService;
import com.cdz360.iot.model.pv.vo.PvDataBi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能设备统计操作接口", description = "储能设备统计操作接口合集")
@Slf4j
@RestController
@RequestMapping("/iot/pv/bi")
public class PvBiRest {

    @Autowired
    private PvBiService pvBiService;

    @Operation(summary = "光伏地图数据统计")
    @PostMapping("/map/data")
    public Mono<ObjectResponse<PvDataBi>> pvMapData(@RequestBody List<String> gids) {
        log.info("光伏地图数据统计: {}", gids);
        return pvBiService.pvMapData(gids)
            .map(RestUtils::buildObjectResponse);
    }

}
