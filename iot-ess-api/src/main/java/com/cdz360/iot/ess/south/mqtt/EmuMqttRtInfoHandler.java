package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarmNotify.ErrorObj;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.vo.BmsBundleRtInfo;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.BmsStackRtInfo;
import com.cdz360.base.model.es.vo.DehRtInfo;
import com.cdz360.base.model.es.vo.EmuRtInfo;
import com.cdz360.base.model.es.vo.EssBaseRtInfo;
import com.cdz360.base.model.es.vo.FfsRtInfo;
import com.cdz360.base.model.es.vo.LiquidRtInfo;
import com.cdz360.base.model.es.vo.MeterRtInfo;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.es.vo.UpsRtInfo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisEmuRwService;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.ess.ds.BmsRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.ess.ds.PcsRoDs;
import com.cdz360.iot.ess.biz.south.EssSouthBizService;
import com.cdz360.iot.ess.model.emu.dto.EmuRtInfoDto;
import com.cdz360.iot.ess.utils.EssAlarmBuilder;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.pv.param.ListCtrlParam;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.mqtt.IMqttMsgHandler;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EmuMqttRtInfoHandler implements IMqttMsgHandler {

    @Autowired
    private PcsRoDs pcsRoDs;

    @Autowired
    private BmsRoDs bmsRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private RedisEmuRwService redisEmuRwService;

    @Autowired
    private EssSouthBizService essSouthBizService;
    @Autowired
    private EssRoDs essRoDs;

    @PostConstruct
    public void init() {
        EmuMqttHandlerFactory.addHandler(EmuMqttHandlerName.EMU_RT_INFO, this);
    }

    @Override
    public boolean handleMessage(String handlerName, String msg) {
        log.info("收到 mqtt 消息 {}", msg);
        EmuRtInfoDto emuRtInfoIn = JsonUtils.fromJson(msg, EmuRtInfoDto.class);
//        log.info("反序列化后 emuRtInfoIn = {}", emuRtInfoIn);

        // 将 emu 信息存到 redis
        EmuRtInfo emuRtInfoX = new EmuRtInfo();
        BeanUtils.copyProperties(emuRtInfoIn, emuRtInfoX);

        GwInfoDto gw = gwInfoRoDs.getByGwno(emuRtInfoIn.getDno());

        this.processEmuInfo(gw, emuRtInfoIn, emuRtInfoX);   // EMU 层级的实时信息
        this.processPcsInfo(gw, emuRtInfoIn, emuRtInfoX);   // PCS 实时信息
        this.processBmsInfo(gw, emuRtInfoIn, emuRtInfoX);   // BMS 实时信息
        this.processLiquidInfo(gw, emuRtInfoIn, emuRtInfoX);    // 液冷
        this.processFfsInfo(gw, emuRtInfoIn, emuRtInfoX);   // 消防系统 实时信息
        this.processUpsInfo(gw, emuRtInfoIn, emuRtInfoX);   // UPS 实时信息
        this.processDehInfo(gw, emuRtInfoIn, emuRtInfoX);   // 除湿器 实时信息
        this.processMeterInfo(gw, emuRtInfoIn, emuRtInfoX);   // 电表 实时信息

        redisEmuRwService.updateEmuRtInfo(emuRtInfoIn.getDno(), emuRtInfoX);

        essSouthBizService.updateStatus(emuRtInfoIn.getDno(), emuRtInfoIn.getStatus());

        this.essSouthBizService.publishEmsInfo(emuRtInfoIn); // 推送设备信息变更的mq消息

        return true;
    }

    private void processEmuInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        EssVo ess = essRoDs.getEssVo(emuRtInfoIn.getDno());
        EssAlarms alarms = EssAlarmBuilder.build4Emu(ess, emuRtInfoIn.getTs(), emuRtInfoIn.getTz(),
            emuRtInfoIn.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private void processPcsInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getPcsList())) {
            emuRtInfo.setPcsDnos(new ArrayList<>());
            for (PcsRtInfo pcsRtInfo : emuRtInfoIn.getPcsList()) {
//                PcsRtInfo oldPcsInfo = redisEmuRwService.getPcsRtInfo(pcsRtInfo.getDno());
                emuRtInfo.getPcsDnos().add(pcsRtInfo.getDno());
                pcsRtInfo.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 PCS 信息. EMU = {}, PCS = {}", emuRtInfoIn.getDno(),
                    pcsRtInfo.getDno());
                redisEmuRwService.updatePcsRtInfo(pcsRtInfo.getDno(), pcsRtInfo);
                EssEquipPo pcsEquip = essEquipRoDs.getByDno(pcsRtInfo.getDno());
                // PCS故障上报
                this.processPcsErrorCode(gw, emuRtInfo.getTz(), pcsEquip, pcsRtInfo);
            }
        }
    }

    private void processPcsErrorCode(GwInfoDto gw, String tz, EssEquipPo pcsEquip,
        PcsRtInfo pcsRtInfo) {
        EssAlarms alarms = EssAlarmBuilder.build4Pcs(gw, pcsRtInfo.getTs(), tz, pcsEquip,
            pcsRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }


    private void processBmsInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getBmsList())) {
            emuRtInfo.setBmsDnos(new ArrayList<>());

            Map<String, String> dnoNameMap = this.generateDnoNameMap(emuRtInfoIn);

            for (BmsRtInfo info : emuRtInfoIn.getBmsList()) {
                emuRtInfo.getBmsDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 BMS 信息. EMU = {}, BMS = {}", emuRtInfoIn.getDno(),
                    info.getDno());

                this.setBundleName(info, dnoNameMap);
                redisEmuRwService.updateBmsRtInfo(info.getDno(), info);

                // BMS故障上报
                this.processBmsErrorCode(gw, info, emuRtInfoIn);
            }
        }
    }

    protected Map<String, String> generateDnoNameMap(EmuRtInfoDto emuRtInfoIn) {
        Map<String, String> dnoNameMap = new HashMap<>();
        if (emuRtInfoIn == null || CollectionUtils.isEmpty(emuRtInfoIn.getBmsList())) {
            return dnoNameMap;
        }
        try {
            List<String> bundleDnoList = emuRtInfoIn.getBmsList().stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getStackInfoList()))
                .flatMap(x -> {
                    return x.getStackInfoList().stream()
                        .filter(e -> CollectionUtils.isNotEmpty(e.getBundleList()))
                        .flatMap(e -> {
                            return e.getBundleList().stream().map(EssBaseRtInfo::getDno);
                        });
                }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(bundleDnoList)) {
                List<EssEquipPo> equipList = essEquipRoDs.findEquipList(
                    new ListCtrlParam().setDnoList(bundleDnoList));
                if (CollectionUtils.isNotEmpty(equipList)) {
                    dnoNameMap = equipList.stream().collect(Collectors.toMap(EssEquipPo::getDno,
                        EssEquipPo::getName));
                }
            }
        } catch (Exception e) {
            log.error("generateDnoNameMap异常 dno: {}, bmsList.size: {}, error: {}",
                emuRtInfoIn.getDno(),
                emuRtInfoIn.getBmsList() == null ? null : emuRtInfoIn.getBmsList().size(),
                e.getMessage(), e);
        }
        return dnoNameMap;
    }

    protected void setBundleName(BmsRtInfo info, Map<String, String> dnoNameMap) {
        if (info == null || CollectionUtils.isEmpty(info.getStackInfoList())
            || dnoNameMap == null) {
            return;
        }
        info.getStackInfoList().forEach(s -> {
            if (CollectionUtils.isEmpty(s.getBundleList())) {
                return;
            }
            s.getBundleList().forEach(b -> {
                b.setName(dnoNameMap.get(b.getDno()));
            });
        });
    }

    private void processBmsErrorCode(GwInfoDto gw, BmsRtInfo bmsRtInfo, EmuRtInfo emuRtInfoIn) {
//        if (CollectionUtils.isNotEmpty(bmsRtInfo.getStackInfoList())) {
//            bmsRtInfo.getStackInfoList().stream()
//                .filter(s -> CollectionUtils.isNotEmpty(s.getBundleList()))
//                .map(BmsStackRtInfo::getBundleList)
//                .collect(Collectors.toList());
//        }
        String tz = emuRtInfoIn.getTz();

        EssEquipPo bmsEquip = essEquipRoDs.getByDno(bmsRtInfo.getDno());
        this.processBmsErrorCodeX(gw, tz, bmsEquip, bmsRtInfo);

        // 电池堆/电池簇分开处理
        if (CollectionUtils.isNotEmpty(bmsRtInfo.getStackInfoList())) {
            for (BmsStackRtInfo stack : bmsRtInfo.getStackInfoList()) {
                if (CollectionUtils.isNotEmpty(stack.getAlarms())) {
                    EssEquipPo bsEquip = essEquipRoDs.getByDno(stack.getDno());
                    EssAlarms batteryStackAlarms = EssAlarmBuilder.build4BatteryStack(gw,
                        bmsRtInfo.getTs(),
                        tz, bsEquip, stack.getAlarms());
                    this.dcEventPublisher.publishEssAlarm(batteryStackAlarms);
                }

                if (CollectionUtils.isNotEmpty(stack.getBundleList())) {
                    for (BmsBundleRtInfo bundle : stack.getBundleList()) {
                        if (CollectionUtils.isNotEmpty(bundle.getAlarms())) {
                            EssEquipPo bundleEquip = essEquipRoDs.getByDno(bundle.getDno());
                            EssAlarms batteryBundleAlarmList = EssAlarmBuilder.build4BatteryBundle(
                                gw,
                                bmsRtInfo.getTs(),
                                tz, bundleEquip, bundle.getAlarms());
                            this.dcEventPublisher.publishEssAlarm(batteryBundleAlarmList);
                        }
                    }
                }
            }
        }

//        this.publishAlarms(batteryStackAlarms);
//        this.publishAlarms(batteryBundleAlarmList);
    }

    private void processBmsErrorCodeX(GwInfoDto gw, String tz, EssEquipPo bmsEquip,
        BmsRtInfo bmsRtInfo) {

        EssAlarms alarms = EssAlarmBuilder.build4Bms(gw, bmsRtInfo.getTs(), tz, bmsEquip,
            bmsRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
//
//        List<EssAlarmDto> alarmList = null;
//        // OFFLINE
////        if (null != bmsRtInfo.getStatus() && BmsStatus.OFFLINE.equals(
////            bmsRtInfo.getStatus())) {
////            bmsErrorObjs.add(new ErrorObj().setWarnCode((long) -1)
////                .setLts(LocalDateTime.now()));
////        }
//        if (CollectionUtils.isNotEmpty(bmsRtInfo.getAlarms())) {
////            LocalDateTime now = LocalDateTime.now();
//            alarmList = bmsRtInfo.getAlarms().stream()
//                .map(x -> EssAlarmBuilder.build4Bms(gw, bmsRtInfo.getTs(), tz, bmsEquip, x))
//                .collect(Collectors.toList());
//        } else {
//            alarmList = new ArrayList<>();
//        }
//
//        this.publishAlarms(alarmList);
    }

//    private void publishAlarms(List<EssAlarmDto> alarmList) {
//
//        EssAlarms alarmNotify = new EssAlarms();
//        alarmNotify.setAlarms(alarmList);
//
//        this.essEventPublisher.publishEssAlarm(alarmNotify);
//    }

//    private void processBatClusterErrorCode(GwInfoDto gw, BmsRtInfo bmsRtInfo,
//        List<ErrorObj> errorObjs) {
//        EssAlarmNotify alarmNotify = this.alarmNotifyData(gw, errorObjs)
//            .setSubDeviceType(WarnSubDeviceType.BATTERY_CLUSTER)
//            .setSubDeviceDno(bmsRtInfo.getDno());
//
//        if (StringUtils.isNotBlank(bmsRtInfo.getName())) {
//            alarmNotify.setSubDeviceName(bmsRtInfo.getName());
//        } else {
//            BmsPo bms = bmsRoDs.getByDno(bmsRtInfo.getDno());
//            if (null != bms) {
//                alarmNotify.setSubDeviceName(bms.getName());
//            }
//        }
//
//        this.essEventPublisher.publishEssAlarm(alarmNotify);
//    }

    private void processDehInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getDehList())) {
            emuRtInfo.setDehDnos(new ArrayList<>());
            for (DehRtInfo info : emuRtInfoIn.getDehList()) {
                if (StringUtils.isBlank(info.getDno())) {
                    log.info("除湿设备号为空");
                    continue;
                }
                emuRtInfo.getDehDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 除湿器 信息. EMU = {}, DEH = {}", emuRtInfoIn.getDno(),
                    info.getDno());
                redisEmuRwService.updateDehRtInfo(info.getDno(), info);

                // 空调故障上报
                this.processDehErrorCode(gw, emuRtInfoIn.getTz(), info);
            }
        }
    }

    private void processDehErrorCode(GwInfoDto gw, String tz, DehRtInfo dehRtInfo) {
        EssEquipPo deh = essEquipRoDs.getByDno(dehRtInfo.getDno());

        EssAlarms alarms = EssAlarmBuilder.build4Deh(gw, dehRtInfo.getTs(), tz, deh,
            dehRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private void processLiquidInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getLiquidList())) {
            emuRtInfo.setLiquidDnos(new ArrayList<>());
            for (LiquidRtInfo info : emuRtInfoIn.getLiquidList()) {
                if (StringUtils.isBlank(info.getDno())) {
                    log.info("液冷设备号为空");
                    continue;
                }
                emuRtInfo.getLiquidDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 液冷 信息. EMU = {}, Liquid = {}", emuRtInfoIn.getDno(),
                    info.getDno());
                redisEmuRwService.updateLiquidRtInfo(info.getDno(), info);

                // 液冷 故障上报
                this.processLiquidErrorCode(gw, emuRtInfoIn.getTz(), info);
            }
        }
    }

    private void processFfsInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getFfsList())) {
            emuRtInfo.setFfsDnos(new ArrayList<>());
            for (FfsRtInfo info : emuRtInfoIn.getFfsList()) {
                if (StringUtils.isBlank(info.getDno())) {
                    log.info("消防设备号为空");
                    continue;
                }
                emuRtInfo.getFfsDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 消防系统 信息. EMU = {}, FFS = {}", emuRtInfoIn.getDno(),
                    info.getDno());
                redisEmuRwService.updateFfsRtInfo(info.getDno(), info);

                // Ffs故障上报
                this.processFfsErrorCode(gw, emuRtInfoIn.getTz(), info);
            }
        }
    }

    private void processLiquidErrorCode(GwInfoDto gw, String tz, LiquidRtInfo liquidRtInfo) {

        EssEquipPo liquid = essEquipRoDs.getByDno(liquidRtInfo.getDno());

        EssAlarms alarms = EssAlarmBuilder.build4Liquid(gw, liquidRtInfo.getTs(), tz, liquid,
            liquidRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private void processFfsErrorCode(GwInfoDto gw, String tz, FfsRtInfo ffsRtInfo) {

        EssEquipPo ffs = essEquipRoDs.getByDno(ffsRtInfo.getDno());

        EssAlarms alarms = EssAlarmBuilder.build4Ffs(gw, ffsRtInfo.getTs(), tz, ffs,
            ffsRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private void processUpsInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getUpsList())) {
            emuRtInfo.setUpsDnos(new ArrayList<>());
            for (UpsRtInfo info : emuRtInfoIn.getUpsList()) {
                if (StringUtils.isBlank(info.getDno())) {
                    log.info("UPS设备号为空");
                    continue;
                }
                emuRtInfo.getUpsDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 UPS 信息. EMU = {}, UPS = {}", emuRtInfoIn.getDno(),
                    info.getDno());
                redisEmuRwService.updateUpsRtInfo(info.getDno(), info);

                // UPS故障上报
                this.processUpsErrorCode(gw, emuRtInfoIn.getTz(), info);
            }
        }
    }

    private void processUpsErrorCode(GwInfoDto gw, String tz, UpsRtInfo upsRtInfo) {

        EssEquipPo ups = essEquipRoDs.getByDno(upsRtInfo.getDno());

        EssAlarms alarms = EssAlarmBuilder.build4Ups(gw, upsRtInfo.getTs(), tz, ups,
            upsRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private void processMeterInfo(GwInfoDto gw, EmuRtInfoDto emuRtInfoIn, EmuRtInfo emuRtInfo) {
        if (CollectionUtils.isNotEmpty(emuRtInfoIn.getMeterList())) {
            emuRtInfo.setMeterDnos(new ArrayList<>());
            for (MeterRtInfo info : emuRtInfoIn.getMeterList()) {
                if (StringUtils.isBlank(info.getDno())) {
                    log.info("电表设备号为空");
                    continue;
                }
                emuRtInfo.getDehDnos().add(info.getDno());
                info.setEssDno(emuRtInfoIn.getDno());
                log.info("往 redis 更新 电表 信息. emuDno = {}, meterDno = {}",
                    emuRtInfoIn.getDno(),
                    info.getDno());
                redisEmuRwService.updateMeterRtInfo(info.getDno(), info);

                // 电表故障上报
                this.processMeterErrorCode(gw, emuRtInfoIn.getTz(), info);
            }
        }
    }

    private void processMeterErrorCode(GwInfoDto gw, String tz, MeterRtInfo meterRtInfo) {

        EssEquipPo meter = essEquipRoDs.getByDno(meterRtInfo.getDno());

        EssAlarms alarms = EssAlarmBuilder.build4Meter(gw, meterRtInfo.getTs(), tz, meter,
            meterRtInfo.getAlarms());
        this.dcEventPublisher.publishEssAlarm(alarms);
    }

    private EssAlarmNotify alarmNotifyData(GwInfoDto gw, List<ErrorObj> errorObjs) {
        EssAlarmNotify alarmNotify = new EssAlarmNotify();
        alarmNotify.setGwName(gw.getName())
            .setGwno(gw.getGwno())
            .setSiteId(gw.getSiteId())
            .setSiteName(gw.getSiteName())
            .setDno(gw.getGwno())
            .setDeviceType(WarnDeviceType.COMM_ESS)
//            .setSubDeviceType(WarnSubDeviceType.PCS)
//            .setSubDeviceDno(upsRtInfo.getDno())
            .setDeviceName(gw.getName())
            .setVersion(gw.getSwVer())
            .setErrorList(errorObjs);
        return alarmNotify;
    }
}
