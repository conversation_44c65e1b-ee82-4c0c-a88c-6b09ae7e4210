package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.iot.ess.biz.UserEssBiService;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.vo.EssMapDataVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能设备统计操作接口", description = "储能设备统计操作接口合集")
@Slf4j
@RestController
@RequestMapping("/user/ess/bi")
public class UserEssBiRest {

    @Autowired
    private UserEssBiService userEssBiService;

    @Operation(summary = "储能地图数据统计")
    @PostMapping("/map/data")
    public Mono<ListResponse<EssMapDataVo>> essMapData(ServerHttpRequest request,
        @RequestBody @Valid EssMapDataParam param) {
        log.info("储能地图数据统计: {}", param);
        return userEssBiService.essMapData(param);
    }

}
