package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtDataTemplate;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.ess.EssComponentDataEvent;
import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.po.EssPo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssComponentDataEventHandler extends AbstractEssGwPushEventHandler {

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssPushEventType.COMPONENT_DATA, this);
    }

    @Override
    public Mono<Boolean> process(JsonNode json) {
        log.info("处理设备组件上传数据: {}", JsonUtils.toJsonString(json));
        EssComponentDataEvent event = JsonUtils.fromJson(json, EssComponentDataEvent.class);
        Object data = event.getData();
        if (null != data) {
            EssDtuPo dtu = essDtuRoDs.getBySerialNo(event.getSerialNo());
            if (null == dtu) {
                log.warn("ESS透传设备不存在");
                return Mono.just(false);
            }

            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(event.getSerialNo());
            if (CollectionUtils.isNotEmpty(refList)) {
                if (null != event.getMqSubType()) {
                    EssPo ess = essRoDs.getByDno(refList.get(0).getDno()); // 默认只有一个
                    if (null != ess) {
                        switch (event.getMqSubType()) {
                            case MQ_ESS_AMMETER_COMPONENT_MSG: // 电表
                                EssRtReq<JsonNode> meterRt = new EssRtReq<>();
                                meterRt.setDno(refList.get(0).getDno());
                                meterRt.setEquipId((long) EssEquipType.ESS_INSIDE_METER.getCode());
                                meterRt.setEquipTypeId(EssEquipType.ESS_INSIDE_METER.getCode());
                                meterRt.setEquipType(EssEquipType.ESS_INSIDE_METER);
                                meterRt.setRtData(JsonUtils.fromJson(data.toString()));
                                this.pushData2Redis(ess.getTimeZone(), meterRt).subscribe();
                                break;
                            case MQ_ESS_BMS_COMPONENT_MSG: // BMS
                                ModuleBmsRtData bms = JsonUtils.fromJson(
                                    data.toString(), new TypeReference<ModuleBmsRtData>() {
                                    });

                                if (null != bms &&
                                    CollectionUtils.isNotEmpty(bms.getBatteryCupboardList())) {
                                    for (int i = 0; i < bms.getBatteryCupboardList().size(); i++) {
                                        ModuleBmsRtDataTemplate bmsData =
                                            bms.getBatteryCupboardList().get(i);
                                        EssRtReq<JsonNode> bmsRt = new EssRtReq<>();
                                        bmsRt.setDno(refList.get(0).getDno());
                                        bmsRt.setEquipId((long) EssEquipType.BMS.getCode() + i);
                                        bmsRt.setEquipTypeId(EssEquipType.BMS.getCode());
                                        bmsRt.setEquipType(EssEquipType.BMS);
                                        bmsRt.setRtData(
                                            JsonUtils.fromJson(JsonUtils.toJsonString(bmsData)));
                                        this.pushData2Redis(ess.getTimeZone(), bmsRt)
                                            .subscribe(
                                                x -> log.info("写入redis成功: {}", bmsRt.getDno()));

                                        // BMS状态更新
                                        essEquipRwDs.updateEssEquipStatus(refList.get(0).getDno(),
                                            List.of(bmsRt.getEquipId()),
                                            Boolean.TRUE.equals(bmsData.getOpStatus())
                                                ? EquipStatus.NORMAL
                                                : EquipStatus.OFFLINE, null);
                                    }
                                }
                                break;
                            case MQ_ESS_OTHER_COMPONENT_MSG:
                                // 其他数据
                                break;
                            default:
                                log.warn("数据类型无效: {}", event.getMqSubType());
                        }
                    }
                } else {
                    log.warn("<<<< 数据没有提供子数据类型");
                }
            }
        }
        return Mono.just(true);
    }
}
