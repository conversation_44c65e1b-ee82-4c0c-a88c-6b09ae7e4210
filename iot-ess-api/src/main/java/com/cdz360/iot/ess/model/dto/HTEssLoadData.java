package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备中负载运行时数据")
@Data
@Accessors(chain = true)
public class HTEssLoadData {

    @Schema(description = "Load L1 output power(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac1;

    @Schema(description = "Load L2 output power, not used in single phase inverter(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac2;

    @Schema(description = "Load L3 output power, not used in single phase inverter(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac3;

    @Schema(description = "Load frequency(0.01Hz)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fac;

    @Schema(description = "Load L1 voltage(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc1;

    @Schema(description = "Load L1 current(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc1;

    @Schema(description = "Load L2 voltage, not used in single phase inverter(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc2;

    @Schema(description = "Load L2 current, not used in single phase inverter(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc2;

    @Schema(description = "Load L3 voltage, not used in single phase inverter(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc3;

    @Schema(description = "Load L3 current, not used in single phase inverter(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc3;

    @Schema(description = "The accumulated energy consumed by GEN a day(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayLoad;

    @Schema(description = "The accumulated energy consumed by GEN a month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonLoad;

    @Schema(description = "The accumulated energy consumed by GEN a year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearLoad;

    @Schema(description = "The total accumulated energy consumed by GEN(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalLoad;

}
