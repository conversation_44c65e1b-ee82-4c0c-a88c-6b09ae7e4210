package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.iot.mqtt.IMqttMsgHandler;
import jakarta.inject.Singleton;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Singleton
public class EmuMqttHandlerFactory {

    private static Map<String, IMqttMsgHandler> _handlers = new ConcurrentHashMap<>();


    public static void addHandler(String name, IMqttMsgHandler handler) {
        _handlers.put(name, handler);
    }

    public static IMqttMsgHandler getHandler(String name) {
        return _handlers.get(name);
    }
}
