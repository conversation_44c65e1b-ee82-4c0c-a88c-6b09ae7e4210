package com.cdz360.iot.ess.model.param;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.PvComVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GtiEditParam {

    @Schema(description = "逆变器唯一编号")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 16, message = "dno 长度不能超过 16")
    private String dno;

    @Schema(description = "设备名称")
    @Size(max = 32, message = "name 长度不能超过 32")
    private String name;

    @Schema(description = "设备铭牌编号")
    @NotNull(message = "serialNo 不能为 null")
    @Size(max = 32, message = "serialNo 长度不能超过 32")
    private String serialNo;

    @Schema(description = "品牌名称")
    @NotNull(message = "vendor 不能为 null")
    @Size(max = 16, message = "vendor 长度不能超过 16")
    private GtiVendor vendor;

    @Schema(description = "设备型号")
    @NotNull(message = "deviceModel 不能为 null")
    @Size(max = 32, message = "deviceModel 长度不能超过 32")
    private String deviceModel;

    @Schema(description = "场站ID")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @NotNull(message = "status 不能为 null")
    private EquipStatus status;

    @Schema(description = "软件版本号")
    private String swVer;

    @Schema(description = "MPPT电压min")
    private BigDecimal mpptVoltageMin;

    @Schema(description = "MPPT电压max")
    private BigDecimal mpptVoltageMax;

    @Schema(description = "额定功率")
    private Long power;

    @Schema(description = "输出电压")
    private BigDecimal outputVoltage;

    @Schema(description = "最大输出电流")
    private BigDecimal outputCurrent;

    @Schema(description = "最大视在功率")
    private BigDecimal apparentPower;

    @Schema(description = "通信模块")
    private PvComVo com;

}
