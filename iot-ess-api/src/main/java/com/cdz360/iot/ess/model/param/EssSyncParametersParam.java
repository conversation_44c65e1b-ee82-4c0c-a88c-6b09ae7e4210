package com.cdz360.iot.ess.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备同步参数字段信息参数")
@Data
@Accessors(chain = true)
public class EssSyncParametersParam {

    //    GoodsID=9999-15082041T&Rsp=ACK&Hybrid=addr1=value1,addr2=value2…
    @Schema(description = "Inverter serial number")
    @JsonProperty("GoodsID")
    private String goodsId;

    @Schema(description = "ACK or NAK(if send ACK to server, server reply \"ok1\", "
        + "remote setting is finished; "
        + "If send NAK, server sends remote command again)")
    @JsonProperty("Rsp")
    private String rsp;

    @Schema(description = "参数列表")
    @JsonProperty("Hybrid")
    private String hybrid;
}
