package com.cdz360.iot.ess.model.mongo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ess-day-data")
@Schema(description = "ESS设备统计数据信息")
@Data
@Accessors(chain = true)
public class EssSatDataInMongo {

    @Schema(description = "年")
    private int year;

    @Schema(description = "月")
    private int month;

    @Schema(description = "日")
    private int day;

    @Schema(description = "设备唯一编号")
    private String dno;

    public EssSatDataInMongo(int year, int month, int day, String dno) {
        this.year = year;
        this.month = month;
        this.day = day;
        this.dno = dno;
    }

    //    0x125C	4	自发电量	kW.h		测量-统计
    @Schema(description = "自发电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal selfElectricity;
    //    0x125E	4	电池充电量	kW.h		测量-统计
    @Schema(description = "电池充电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batChargeElectricity;
    //    0x1260	4	电池放电量	kW.h		测量-统计
    @Schema(description = "电池放电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batDischargeElectricity;
    //    0x1262	4	负载用电量	kW.h		测量-统计
    @Schema(description = "负载用电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadUsingElectricity;
    //    0x1264	4	当天自发电量	kW.h		测量-统计
    @Schema(description = "当天自发电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal selfElectricityToday;
    //    0x1266	4	当天购电量	kW.h		测量-统计
    @Schema(description = "当天购电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal purchaseElectricityToday;
    //    0x1268	4	当天馈电量	kW.h		测量-统计
    @Schema(description = "当天馈电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal offerElectricityToday;
    //    0x126A	4	当天电池充量	kW.h		测量-统计
    @Schema(description = "当天电池充量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batChargeElectricityToday;
    //    0x126C	4	当天电池放量	kW.h		测量-统计
    @Schema(description = "当天电池放量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batDischargeElectricityToday;
    //    0x126E	4	当天负载用电量	kW.h		测量-统计
    @Schema(description = "当天负载用电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadUsingElectricityToday;
}
