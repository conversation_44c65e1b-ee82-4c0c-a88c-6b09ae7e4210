package com.cdz360.iot.ess.rest.external;

import com.cdz360.iot.ess.biz.UserEssInteractService;
import com.cdz360.iot.ess.model.param.EssRemoteUpgradeParam;
import com.cdz360.iot.ess.model.param.EssRtDataReportParam;
import com.cdz360.iot.ess.model.param.EssSyncParametersParam;
import com.cdz360.iot.ess.model.param.EssSyncTimeParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "户用储能设备数据上报接口", description = "户用储能设备数据上报相关接口合集")
@Slf4j
@RestController
@RequestMapping("/user/ess/report")
public class UserEssInteractRest {

    @Autowired
    private UserEssInteractService userEssInteractService;

    @Operation(summary = "Energy Data and Real Time Data")
    @PostMapping(value = "/rtData", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Mono<String> essRtData(@ModelAttribute EssRtDataReportParam param) {
        log.info("户用储能设备运行时数据上传: {}", param);
        userEssInteractService.essRtData(param);
        return Mono.just("<Rsp_Code>0</Rsp_Code>\\r");
    }

    @Operation(summary = "Synchronization time")
    @PostMapping(value = "/syncTime", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Mono<String> essSynchronizationTime(@ModelAttribute EssSyncTimeParam param) {
        log.info("户用储能设备时间同步: {}", param);
        return Mono.just("TT2018-10-10 17:58:17\\r");
    }

    @Operation(summary = "Remote Upgrade Inverter Firmware")
    @PostMapping(value = "/remoteUpgrade", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Mono<String> essRemoteUpgrade(@ModelAttribute EssRemoteUpgradeParam param) {
        log.info("户用储能设备远程升级: {}", param);
        return Mono.just("00 3D 80 00 00 03 80 00 00 01 00 00 01 40");
    }

    @Operation(summary = "Update Inverter Parameters to Server")
    @PostMapping(value = "/syncParameters", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Mono<String> essSynchronizationParameters(@ModelAttribute EssSyncParametersParam param) {
        log.info("户用储能设备同步参数: {}", param);
        return Mono.just("ok1\\r");
    }

/**
 * 协议问题:
 * 1. 协议中使用的时间格式，时区是怎样的？
 * 2. INV 在 <<1.1 Format>> 定义的数据项和 <<1.2 Description>> 中描述的数据项不一致，哪个为准？
 * 3. <<See Mode Table>> 这关联到AppendixTable中的 ErrorCode2 是怎样理解？
 * 4. Synchronization time 接口响应的时间格式怎么理解？时区信息??
 * 5. ERROR_INFO 这个数据结构示例缺少
 */
}
