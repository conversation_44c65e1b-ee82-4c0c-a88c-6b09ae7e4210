package com.cdz360.iot.ess.south.modbus;

import com.cdz360.base.utils.JsonUtils;
import io.netty.buffer.ByteBuf;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * MODBUS TCP数据帧的MBAP头部
 */
@Schema(description = "MODBUS TCP 消息MBAP头部")
@Data
@Accessors(chain = true)
public class ModbusTcpMBAPHeader {

    @Schema(description = "Identification of a\n"
        + "MODBUS Request /\n"
        + "Response transaction. \n", required = true)
    private short transactionId;

    @Schema(description = "0 = MODBUS protocol")
    private short protocolId;

    @Schema(description = "Number of following bytes")
    private short length;

    @Schema(description = "Identification of a\n"
        + "remote slave\n"
        + "connected on a serial\n"
        + "line or on other buses. ")
    private short unitId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    public static ModbusTcpMBAPHeader decode(ByteBuf data) {
        return new ModbusTcpMBAPHeader()
            .setTransactionId(data.readShort())
            .setProtocolId(data.readShort())
            .setLength(data.readShort())
            .setUnitId(data.readUnsignedByte());
    }

    public static void encode(ByteBuf msg, ModbusTcpMBAPHeader mbapHeader) {
        msg.writeShort(mbapHeader.getTransactionId());
        msg.writeShort(mbapHeader.getProtocolId());
        msg.writeShort(mbapHeader.getLength());
        msg.writeByte(mbapHeader.getUnitId());
    }
}
