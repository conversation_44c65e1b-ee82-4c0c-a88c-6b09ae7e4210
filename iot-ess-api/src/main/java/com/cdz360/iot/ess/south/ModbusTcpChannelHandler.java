package com.cdz360.iot.ess.south;

import com.cdz360.iot.ess.south.modbus.ModbusTcpByteBufHolder;
import com.cdz360.iot.ess.south.modbus.ModbusTcpMBAPHeader;
import com.cdz360.iot.ess.south.modbus.ModbusTcpPDUPayload;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * Modbus TCP 链路处理器
 */
@Component
@Slf4j
@ChannelHandler.Sharable
public class ModbusTcpChannelHandler extends SimpleChannelInboundHandler<ModbusTcpByteBufHolder> {

    public static final int HEADER_LENGTH = 8;

    @Autowired
    private ModbusTcpChannelMgm modbusTcpChannelMgm;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        Assert.notNull(this.modbusTcpChannelMgm,
            "[Assertion failed] - NettyChannelRepository is required; it must not be null");

        ctx.fireChannelActive();
        if (log.isDebugEnabled()) {
            log.debug(ctx.channel().remoteAddress() + "");
        }
        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("ModbusTCP已连接。channelKey: {}", channelKey);
        modbusTcpChannelMgm.put(channelKey, ctx.channel());

        if (log.isDebugEnabled()) {
            log.debug("Bound Channel Count is {}", this.modbusTcpChannelMgm.size());
        }
    }

    @Override
    public void channelRead0(ChannelHandlerContext ctx, ModbusTcpByteBufHolder msg) {
        // TODO: IotDecoder 实现后, 可直接转成类型 IotEvseMessage 或其子类
        // TODO: 需要由IotDecoder解包
        log.debug("[{}] channelRead start", ctx.channel().remoteAddress().toString());
        if (null == msg) {
            return;
        }

        int totalLen = msg.content().readableBytes();
        if (totalLen < HEADER_LENGTH) {
            log.info("无效MODBUS");
            return;
        }

        ModbusTcpMBAPHeader mbapHeader = ModbusTcpMBAPHeader.decode(msg.content());
        ModbusTcpPDUPayload pduPayload = new ModbusTcpPDUPayload()
            .setFunctionCode(msg.content().readUnsignedByte())
            .setLength(totalLen > HEADER_LENGTH ?
                (short) (totalLen - HEADER_LENGTH) : (short) 0)
            .setData(msg.content());

        // 处理接收到的数据
        log.info("MBAP Header:" + mbapHeader);
        log.info("PDU Payload:" + pduPayload);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Assert.notNull(ctx,
            "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

        String channelKey = ctx.channel().remoteAddress().toString();
        log.error("ModbusTCP异常断开: {}", channelKey, cause);
        ctx.channel().close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Assert.notNull(this.modbusTcpChannelMgm,
            "[Assertion failed] - NettyChannelRepository is required; it must not be null");
        Assert.notNull(ctx,
            "[Assertion failed] - ChannelHandlerContext is required; it must not be null");

        String channelKey = ctx.channel().remoteAddress().toString();
        log.info("ModbusTCP连接已断开: {}", channelKey);

        this.modbusTcpChannelMgm.remove(channelKey);
        if (log.isDebugEnabled()) {
            log.debug("Bound Channel Count is " + this.modbusTcpChannelMgm.size());
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            if (((IdleStateEvent) evt).state().equals(IdleState.READER_IDLE)) {

                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                String channelKey = ctx.channel().remoteAddress().toString();
                log.info("ModbusTCP长时间未写入数据，将其断开。channelKey: {}", channelKey);

                ctx.channel().close();
            }
        }

        super.userEventTriggered(ctx, evt);
    }

}