package com.cdz360.iot.ess.south;


import com.cdz360.iot.ess.south.modbus.ModbusTcpByteBufHolder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * TCP数据的解包
 */
@Component
@Slf4j
@Scope(value = "singleton")
@ChannelHandler.Sharable
public class ModbusDecoder extends MessageToMessageDecoder<ByteBuf> {

    @Autowired
    private ModbusTcpChannelMgm modbusTcpChannelMgm;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) {
        log.info("收到数据: {}", ByteBufUtil.hexDump(msg));
        try {
            msg.resetReaderIndex();
            int count = msg.readableBytes();
            log.info("接收数据长度:" + count);
            out.add(new ModbusTcpByteBufHolder(Unpooled.copiedBuffer(msg)));
            msg.clear();
        } catch (Exception e) {
            log.error("接受处理异常: {}", e.getMessage(), e);
            this.resetClient(ctx.channel());
        }
    }

    private void resetClient(Channel channel) {
        modbusTcpChannelMgm.remove(channel.remoteAddress().toString());
        channel.close();
    }

}
