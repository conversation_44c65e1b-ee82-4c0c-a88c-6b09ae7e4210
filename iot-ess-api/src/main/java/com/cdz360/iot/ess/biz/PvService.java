package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ess.model.param.GtiEditParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.vo.GtiVo;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvService {

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    /**
     * 新增光伏逆变器
     *
     * @param param 逆变器信息
     * @return 新增结果
     */
    @Transactional
    public Mono<Boolean> addGti(GtiEditParam param) {
        EssEquipType equipType = EssEquipType.PV_INV;
        return Mono.just(param).map(e -> {
            GtiPo gtiPo = this.validateGtiData(e);
            if (StringUtils.isBlank(gtiPo.getGwno())) {
                gtiPo.setGwno("");
            }
            gtiPo.setDno(dnoGenerator.genDno(equipType));
            gtiPo.setCreateTime(new Date());
            return gtiPo;
        }).flatMap(gtiPo -> {
            // 先插入 t_ess_equip，得到自增id
            EssEquipPo essEquipPo = new EssEquipPo();
            essEquipPo.setEssDno(gtiPo.getSiteId())  // 特殊处理，填入SiteId
                .setDno(gtiPo.getDno())
                .setName(gtiPo.getName())
                .setEquipId((long) equipType.getCode())
                .setEquipType(equipType)
                .setEquipTypeId(equipType.getCode())
                .setEquipNameCn(equipType.getDesc())
                .setEquipNameEn(equipType.getDesc()).setEnable(true)
                .setStatus(EquipStatus.NORMAL).setCreateTime(new Date());
            if (StringUtils.isNotBlank(param.getDeviceModel())) {
                essEquipPo.setEquipModel(param.getDeviceModel());
            }
            if (StringUtils.isNotBlank(param.getSwVer())) {
                essEquipPo.setSwVer(param.getSwVer());
            }
            boolean essInsertResult = essEquipRwDs.insertEssEquip(essEquipPo);
            if (!essInsertResult) {
                return Mono.error(new DcServiceException("保存失败"));
            }

            // 将id填入essEquipId，再插入 t_gti
            gtiPo.setEssEquipId(essEquipPo.getId());
            boolean gtiInsertResult = gtiRwDs.upsetGti(gtiPo);

            return Mono.just(gtiInsertResult);
        });
    }

    /**
     * 修改光伏逆变器
     *
     * @param param 逆变器信息
     * @return 修改结果
     */
    @Transactional
    public Mono<Boolean> updateGti(GtiEditParam param) {
        return Mono.just(param).flatMap(e -> {
            IotAssert.isNotBlank(e.getDno(), "设备编号不能为空");

            GtiPo gtiPo = this.validateGtiData(e);
            gtiPo.setUpdateTime(new Date());

            if (StringUtils.isNotBlank(param.getDeviceModel()) || StringUtils.isNotEmpty(
                param.getSwVer())) {

                EssEquipPo essEquipPo = new EssEquipPo();
                essEquipPo.setDno(gtiPo.getDno());
                essEquipPo.setName(gtiPo.getName());
                essEquipPo.setEssDno(gtiPo.getSiteId()); // 特殊处理，填入SiteId
                essEquipPo.setUpdateTime(new Date());
                if (StringUtils.isNotBlank(gtiPo.getDeviceModel())) {
                    essEquipPo.setEquipModel(gtiPo.getDeviceModel());
                }
                if (StringUtils.isNotBlank(param.getSwVer())) {
                    essEquipPo.setSwVer(param.getSwVer());
                }
                boolean essUpdateResult = essEquipRwDs.updateEssEquip(essEquipPo);
                if (!essUpdateResult) {
                    return Mono.error(new DcServiceException("保存失败"));
                }
            }

            // 更新t_gti
            boolean updateResult = gtiRwDs.updateGtiByDno(gtiPo);
            if (!updateResult) {
                return Mono.error(new RuntimeException("保存失败"));
            }
            return Mono.just(updateResult);
        });
    }

    /**
     * 根据ID查询光伏逆变器
     *
     * @param id 逆变器ID
     * @return 逆变器信息
     */
    public Mono<GtiPo> getGtiById(Long id) {
        return Mono.just(id)
            .filter(idValue -> idValue != null && idValue > 0)
            .switchIfEmpty(Mono.error(new DcArgumentException("逆变器ID不能为空或无效")))
            .map(idValue -> gtiRwDs.getById(idValue, false));
    }

    /**
     * 根据设备编号查询光伏逆变器
     *
     * @param dno 设备编号
     * @return 逆变器信息
     */
    public Mono<GtiPo> getGtiByDno(String dno) {
        return Mono.just(dno)
            .doOnNext(this::validateDno)
            .map(gtiRoDs::getByDno);
    }

    /**
     * 批量查询光伏逆变器
     *
     * @param param 查询参数
     * @return 逆变器列表
     */
    public Mono<ListResponse<GtiVo>> listGti(ListGtiParam param) {
        return Mono.just(param)
            .filter(Objects::nonNull)
            .switchIfEmpty(Mono.error(new DcArgumentException("查询参数不能为空")))
            .map(p -> {
                List<GtiVo> list = gtiRoDs.findGtiList(p);
                Long total = gtiRoDs.count(p);
                return new ListResponse<>(list, total);
            });
    }

    /**
     * 删除光伏逆变器
     *
     * @param dno 设备编号
     * @return 删除结果
     */
    public Mono<Boolean> deleteGti(String dno) {
        return Mono.just(dno)
            .doOnNext(this::validateDno)
            .flatMap(dnoValue -> {
                // 先检查设备是否存在
                return getGtiByDno(dnoValue)
                    .flatMap(existingGti -> {
                        GtiPo gtiPo = new GtiPo();
                        gtiPo.setDno(dnoValue);
                        gtiPo.setUpdateTime(new Date());
                        // 这里可以根据业务需求设置状态为删除或下线
                        return Mono.just(gtiRwDs.updateGtiByDno(gtiPo));
                    })
                    .switchIfEmpty(Mono.error(new DcArgumentException("设备不存在: " + dnoValue)));
            });
    }

    /**
     * 验证逆变器数据
     *
     * @param param 逆变器信息
     */
    private GtiPo validateGtiData(GtiEditParam param) {
        IotAssert.isNotNull(param, "逆变器信息不能为空");
        IotAssert.isNotBlank(param.getName(), "设备名称不能为空");
        IotAssert.isNotBlank(param.getSerialNo(), "设备铭牌编号不能为空");
        IotAssert.isNotNull(param.getVendor(), "品牌名称不能为空");
        IotAssert.isNotBlank(param.getDeviceModel(), "设备型号不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");

        GtiPo gtiPo = new GtiPo();
        gtiPo.setDno(param.getDno())
            .setName(param.getName())
            .setSerialNo(param.getSerialNo())
            .setVendor(param.getVendor())
            .setDeviceModel(param.getDeviceModel())
            .setSiteId(param.getSiteId())
            .setStatus(param.getStatus())
            .setMpptVoltageMin(param.getMpptVoltageMin())
            .setMpptVoltageMax(param.getMpptVoltageMax())
            .setPower(param.getPower())
            .setOutputVoltage(param.getOutputVoltage())
            .setOutputCurrent(param.getOutputCurrent())
            .setApparentPower(param.getApparentPower())
            .setCom(param.getCom());
        return gtiPo;
    }

    /**
     * 验证设备编号
     *
     * @param dno 设备编号
     */
    private void validateDno(String dno) {
        IotAssert.isNotBlank(dno, "设备编号不能为空");
    }

}
