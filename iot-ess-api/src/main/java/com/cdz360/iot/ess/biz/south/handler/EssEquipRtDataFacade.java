package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssEquipRtDataFacade {

    @Autowired
    private EssEquipRtDataHandlerFactory handlerFactory;

    public Mono<Boolean> process(EssRtReq<JsonNode> req) {
        EssEquipRtDataHandler handler = handlerFactory.getHandler(req.getEquipType());
        if (handler == null) {
            log.error("该设备运行时数据处理不支持. type = {}", req.getEquipType());
            return Mono.just(false);
        }

        return handler.process(req);
    }
}
