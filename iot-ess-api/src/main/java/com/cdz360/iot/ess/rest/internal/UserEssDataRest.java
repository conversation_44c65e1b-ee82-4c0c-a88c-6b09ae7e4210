package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.UserEssDataService;
import com.cdz360.iot.model.ess.vo.DayUserEssRtDataBi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能运行时数据相关接口", description = "储能服务")
@RequestMapping("/user/ess/data")
public class UserEssDataRest {

    @Autowired
    private UserEssDataService userEssDataService;

    @Operation(summary = "获取户用ESS下指定时间范围储能数据量",
        description = "仅返回带数据的日期,注意空数据日期")
    @PostMapping(value = "/userEssDayOfRangeKwh")
    public Mono<ListResponse<DayUserEssRtDataBi>> userEssDayOfRangeKwh(
        @RequestBody SamplingParam param) {
        log.info("获取户用ESS下指定时间范围储能数据量: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(this.userEssDataService.userEssDayOfRangeKwh(param))
            .map(RestUtils::buildListResponse);
    }

}
