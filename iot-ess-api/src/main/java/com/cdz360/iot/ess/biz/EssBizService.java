package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.GwInfoRoDs;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssCfgRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipLangRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ess.biz.south.EssMqService;
import com.cdz360.iot.model.ess.dto.EssCfgDto;
import com.cdz360.iot.model.ess.dto.RedisEquipRtData;
import com.cdz360.iot.model.ess.param.FetchUserEssParam;
import com.cdz360.iot.model.ess.po.EssCfgPo;
import com.cdz360.iot.model.ess.po.EssEquipLangPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.EssVo;
import com.cdz360.iot.model.ess.vo.UserEssVo;
import com.cdz360.iot.model.meter.dto.EssMeterCfg;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.site.po.GwInfoPo;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssBizService {

    @Autowired
    private EssCfgRoDs essCfgRoDs;

    @Autowired
    private EssRoDs essRoDs;
    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private GwInfoRoDs gwInfoRoDs;

    @Autowired
    private EssMqService essMqService;

    @Autowired
    private EssMongoBizService essMongoBizService;

    @Autowired
    private RedisEssEquipRtDataService essEquipRtDataService;

    public Mono<Boolean> modifyEssCfg(String dnoIn, Long cfgId) {
        if (StringUtils.isBlank(dnoIn)) {
            throw new DcArgumentException("逆变器编号无效");
        }
        IotAssert.isNotNull(cfgId, "配置模板ID无效");

        return Mono.just(dnoIn)
            .doOnNext(dno -> {
                EssPo ess = essRoDs.getByDno(dnoIn);
                if (null == ess) {
                    throw new DcArgumentException("控制器不存在指定的ESS");
                }

                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), true);
                if (null == gwInfo) {
                    throw new DcArgumentException("ESS没有绑定有效的控制器");
                }

                EssCfgPo cfg = essCfgRoDs.getByCfgId(cfgId);
                if (null == cfg) {
                    throw new DcArgumentException("配置ID无效");
                }

                ess.setCfgId(cfgId)
                    .setCfgStatus(EquipCfgStatus.SEND_2_GW);
                essRwDs.upsetEss(ess);

                // FIXME: 这里可根据需要调整下发字段信息
                EssCfgDto dto = new EssCfgDto();
                dto.setSn(ess.getSn())
                    .setDno(ess.getDno())
                    .setSiteId(ess.getSiteId())
                    .setSamplingTime(cfg.getSamplingTime())
                ;
                if (GtiVendor.ZL_ESS == ess.getVendor()) {
                    dto.setIsOnlySupportP232(true);
                    dto.setEssMeterVo(essRoDs.getEssMeterVo(ess.getDno()));
                }
                this.essMqService.modifyEssCfg(gwInfo,
                    10L,   // TODO: 临时调试代码
                    false,
                    List.of(dto));
            })
            .map(siteId -> Boolean.TRUE);
    }

    public Mono<Boolean> sendCfgByEquipId(Long equipId) {
        IotAssert.isNotNull(equipId, "ESS挂载设备ID无效");

        return Mono.just(equipId)
            .doOnNext(e -> {
                EssEquipPo essEquipPo = essEquipRoDs.getById(e);
                if (null == essEquipPo) {
                    throw new DcArgumentException("挂载设备不存在");
                }

                EssPo ess = essRoDs.getByDno(essEquipPo.getEssDno());

                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), true);
                if (null == gwInfo) {
                    throw new DcArgumentException("ESS没有绑定有效的控制器");
                }

                EssCfgPo cfg = essCfgRoDs.getByCfgId(ess.getCfgSuccessId());
                if (null == cfg) {
                    throw new DcArgumentException("配置ID无效");
                }

                // FIXME: 这里可根据需要调整下发字段信息
                EssCfgDto dto = new EssCfgDto();
                dto.setSn(ess.getSn())
                    .setDno(ess.getDno())
                    .setSiteId(ess.getSiteId())
                    .setSamplingTime(cfg.getSamplingTime())
                ;
                if (GtiVendor.ZL_ESS == ess.getVendor()) {
                    dto.setIsOnlySupportP232(true);
                    dto.setEssMeterVo(essRoDs.getEssMeterVo(ess.getDno()));
                }
                this.essMqService.modifyEssCfg(gwInfo,
                    10L,   // TODO: 临时调试代码
                    false,
                    List.of(dto));
            })
            .map(siteId -> Boolean.TRUE);
    }

    public Mono<Boolean> uploadEssDataFile(String siteIdIn, Date date) {
        return Mono.just(siteIdIn)
            .doOnNext(siteId -> {
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfo(siteId);
                if (null == gwInfo) {
                    throw new DcArgumentException("控制器编号无效");
                }
                this.essMqService.uploadEssDataFile(gwInfo, date, null);
            })
            .map(o -> Boolean.TRUE);
    }

    public Mono<Boolean> syncEssEquip(String dnoIn) {
        return Mono.just(dnoIn)
            .doOnNext(dno -> {
                EssPo essPo = essRoDs.getByDno(dno);
                if (essPo == null) {
                    throw new DcArgumentException("无法找到设备信息");
                }
                GwInfoPo gwInfo = gwSiteRefRoDs.getGwInfoByGwno(essPo.getGwno(), Boolean.FALSE);
                if (gwInfo == null) {
                    throw new DcServiceException("无法获取所属微网控制器信息");
                }
                this.essMqService.syncEssEquip(gwInfo, List.of(dno));
            })
            .map(o -> Boolean.TRUE);
    }

    public Mono<Boolean> modifyEssMeter(EssMeterCfg param) {
        return Mono.just(param)
            .doOnNext(cfgParam -> {
                GwInfoPo gwInfo = gwInfoRoDs.getByGwno(cfgParam.getGwno());

                IotAssert.isNotNull(gwInfo, "无法获取所属微网控制器信息");
                IotAssert.isTrue(gwInfo.isEnable(), "控制器当前不可用");
                this.essMqService.modifyEssMeter(gwInfo, cfgParam);
            })
            .map(o -> Boolean.TRUE);
    }



    public Mono<Long> syncEssUserRtData2Mongo(LocalDate destDate) {
        final LocalDate yesterday = null == destDate ? LocalDate.now().plusDays(-1) : destDate;

        long start = 0;
        int size = 100;
        FetchUserEssParam param = new FetchUserEssParam();
        param.setStart(start).setSize(size);

        while (true) {
            List<UserEssVo> userEssList = this.essRoDs.userEssList(param); // 只考虑户用储能ESS
            if (CollectionUtils.isNotEmpty(userEssList)) { // 户储处理逻辑调整
                TypeReference<RedisEquipRtData<InverterRtData>> typeReference =
                    new TypeReference<>() {
                    };
                userEssList.forEach(ess -> {
                    RedisEquipRtData<InverterRtData> data = essEquipRtDataService.latestRtData(
                        ess.getDno(), 0L, yesterday, typeReference);
                    if (null != data && null != data.getData()) {
                        essMongoBizService.updateEssDataToday(yesterday, ess.getDno(),
                            data.getData());
                    }
                });

                if (userEssList.size() < size) { // 退出
                    break;
                }

                start += userEssList.size();
            } else {
                break;
            }
        }
        return Mono.just(0L);
    }

    public Mono<EssVo> getEssVo(String dno) {
        return Mono.just(this.essRoDs.getEssVo(dno));
    }

}
