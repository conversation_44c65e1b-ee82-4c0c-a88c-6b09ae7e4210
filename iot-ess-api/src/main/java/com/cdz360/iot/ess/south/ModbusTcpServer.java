package com.cdz360.iot.ess.south;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import java.net.InetSocketAddress;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Modbus TCP server
 */
@Slf4j
@Component
public class ModbusTcpServer {

    @Autowired
    @Qualifier("modbusTcpServerBootstrap")
    private ServerBootstrap serverBootstrap;

    @Autowired
    @Qualifier("modbusTcpSocketAddress")
    private InetSocketAddress tcpPort;

    private Channel serverChannel;

    public void start() throws Exception {
        serverChannel = serverBootstrap
            .bind(tcpPort).sync()
            .channel().closeFuture().sync()
            .channel();
    }

    @PreDestroy
    public void stop() {
        if (serverChannel != null) {
            serverChannel.close();
            serverChannel.parent().close();
        }
    }
}
