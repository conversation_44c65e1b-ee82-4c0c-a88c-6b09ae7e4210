package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备中PV运行时数据")
@Data
@Accessors(chain = true)
public class HTEssPvData {

    @Schema(description = "Power of MPPT N(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pdc;

    @Schema(description = "peak power of today(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pPeak;

    @Schema(description = "energy of today(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDay;

    @Schema(description = "energy in this month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonth;

    @Schema(description = "energy in this year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYear;

    @Schema(description = "Total energy(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotal;

    @Schema(description = "power of PV to load(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pdcToLoad;

    @Schema(description = "energy of today(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayToLoad;

    @Schema(description = "energy in this month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonToLoad;

    @Schema(description = "energy in this year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearToLoad;

    @Schema(description = "Total energy(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalToLoad;

    @Schema(description = "Voltage of MPPT N(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vMppt1;

    @Schema(description = "Current of MPPT N(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iMppt1;

    @Schema(description = "Voltage of MPPT N(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vMppt2;

    @Schema(description = "Current of MPPT N(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iMppt2;

}
