package com.cdz360.iot.ess.south.mqtt;
//
//import com.cdz360.base.model.es.dto.EssAlarms;
//import com.cdz360.base.model.es.type.EmuAlarmCode;
//import com.cdz360.base.utils.CollectionUtils;
//import com.cdz360.base.utils.JsonUtils;
//import com.cdz360.data.cache.RedisEmuRwService;
//import com.cdz360.data.sync.service.DcEventPublisher;
//import com.cdz360.data.sync.service.EssEventPublisher;
//import com.cdz360.iot.ds.ro.GwInfoRoDs;
//import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
//import com.cdz360.iot.ess.model.emu.dto.EmuDiDataDto;
//import com.cdz360.iot.ess.utils.EssAlarmBuilder;
//import com.cdz360.iot.model.ess.vo.EssVo;
//import com.cdz360.iot.mqtt.IMqttMsgHandler;
//import java.util.List;
//import java.util.stream.Collectors;
//import jakarta.annotation.PostConstruct;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//@Slf4j
//@Service
//public class EmuMqttDiDataHandler implements IMqttMsgHandler {
//
//    @Autowired
//    protected GwInfoRoDs gwInfoRoDs;
//
//    @Autowired
//    private DcEventPublisher dcEventPublisher;
//
//    @Autowired
//    private EssRoDs essRoDs;
//
//    @Autowired
//    private RedisEmuRwService redisEmuRwService;
//
//    @PostConstruct
//    public void init() {
//        EmuMqttHandlerFactory.addHandler(EmuMqttHandlerName.EMU_DI_DATA, this);
//    }
//
//    @Override
//    public boolean handleMessage(String handlerName, String msg) {
//        log.info("收到 mqtt 消息 {}", msg);
//        EmuDiDataDto emuDiDataIn = JsonUtils.fromJson(msg, EmuDiDataDto.class);
//        log.info("反序列化后 emuDiDataIn = {}", emuDiDataIn);
//
//        EssVo ess = essRoDs.getEssVo(emuDiDataIn.getDno());
////        GwInfoDto gw = gwInfoRoDs.getByGwno(emuDiDataIn.getDno());
//        if (null == ess) {
//            log.warn("设备不存在: {}", emuDiDataIn.getDno());
//            return false;
//        }
//
//        List<EmuAlarmCode> errorCodes = null;
//        if (CollectionUtils.isNotEmpty(emuDiDataIn.getIdxList())) {
//            // DI发生记录时间
//            redisEmuRwService.updateEmuRtDiData(emuDiDataIn.getDno(), emuDiDataIn.getIdxList());
//
////            LocalDateTime now = LocalDateTime.now();
//
//            errorCodes = emuDiDataIn.getIdxList().stream()
//                .map(x -> EmuAlarmCode.valueOf(x))
//                .collect(Collectors.toList());
//        } else {
//            errorCodes = List.of();
//        }
//
//        EssAlarms alarms = EssAlarmBuilder.build4Emu(ess, emuDiDataIn.getTs(), emuDiDataIn.getTz(),
//            errorCodes);
//
//        this.dcEventPublisher.publishEssAlarm(alarms);
//
//        return true;
//    }
//
//}
