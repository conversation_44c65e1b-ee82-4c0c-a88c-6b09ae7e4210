package com.cdz360.iot.ess.cfg;

import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.iot.common.base.IotConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {

    @Bean
    public Queue siteGroupQueue() {
        return new Queue(IotConstants.MQ_QUEUE_ESS_GW_PUSH_DATA, true, false, true);
    }

    @Bean
    public DirectExchange exchangeSite() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_ESS, true, true);
    }

    @Bean
    public Binding bindingExchangeSiteGroup(Queue siteGroupQueue, DirectExchange exchangeSite) {
        return BindingBuilder.bind(siteGroupQueue).to(exchangeSite)
            .with(DcMqConstants.MQ_ROUTING_KEY_ESS);
    }

}
