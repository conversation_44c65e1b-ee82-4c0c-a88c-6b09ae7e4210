package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备运行时数据信息")
@Data
@Accessors(chain = true)
public class HTEssInvData {

    @Schema(description = "时间", example = "YYYYMMDDHHMM")
    @JsonInclude(Include.NON_NULL)
    private String time;

    @Schema(description = "Inverter model name")
    @JsonInclude(Include.NON_NULL)
    private String modelName;

    @Schema(description = "Inverter serial number")
    @JsonInclude(Include.NON_NULL)
    private String serialNumber;

    @Schema(description = "AAA_BBBBBB_C_DD:"
        + " AAA-->Picture index;"
        + " BBBBBB-->strings of every MPPT;"
        + " C-->phases(1 or 3);"
        + " DD-->00:On grid/01: AC couple/02: Hybrid")
    @JsonInclude(Include.NON_NULL)
    private String displayFormat;

    @Schema(description = "Material number_internal version",
        example = "G9511-061000-02_010400")
    @JsonInclude(Include.NON_NULL)
    private String materialVersion;

    @Schema(description = "DSP2 version")
    @JsonInclude(Include.NON_NULL)
    private String dsp2Version;

    @Schema(description = "DSP3 version")
    @JsonInclude(Include.NON_NULL)
    private String dsp3Version;

    @Schema(description = "CSB version")
    @JsonInclude(Include.NON_NULL)
    private String csbVersion;

    @Schema(description = "model(类型不清晰)")
    @JsonInclude(Include.NON_NULL)
    private String model;

    @Schema(description = "EMS system work mode: "
        + "Self used mode/Priority feed in mode/Back up/ Battery test")
    @JsonInclude(Include.NON_NULL)
    private String emsStatus;

    @Schema(description = "Inverter inner temperature(℃)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal internalTemperature;

    @Schema(description = "Total run time(minute)")
    @JsonInclude(Include.NON_NULL)
    private Long hTotal;

    @Schema(description = "Self_consumption_day(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfUsedDayRate;

    @Schema(description = "Self_sufficiency_day(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfSufficiencyDayRate;

    @Schema(description = "Self_consumption_month(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfUsedMonRate;

    @Schema(description = "Self_sufficiency_month(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfSufficiencyMonRate;

    @Schema(description = "Self_consumption_year(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfUsedYearRate;

    @Schema(description = "Self_sufficiency_year(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfSufficiencyYearRate;

    @Schema(description = "Self_consumption_total(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfUsedTotalRate;

    @Schema(description = "Self_sufficiency_total(%)")
    @JsonInclude(Include.NON_NULL)
    private Integer selfSufficiencyTotalRate;

    @Schema(description = "DRM Status(%)")
    @JsonInclude(Include.NON_NULL)
    private String drmStatus;
}
