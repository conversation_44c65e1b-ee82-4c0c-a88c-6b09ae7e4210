package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.event.ess.EssCollectDataEvent;
import com.cdz360.data.sync.event.ess.EssPushEventType;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.dto.BatteryCluster;
import com.cdz360.iot.model.ess.dto.Ems;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.Meter;
import com.cdz360.iot.model.ess.dto.PvInv;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssCollectDataEventHandler extends AbstractEssGwPushEventHandler {

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssPushEventType.COLLECT_DATA, this);
    }

    @Override
    public Mono<Boolean> process(JsonNode json) {
        log.info("处理设备模拟量: {}", JsonUtils.toJsonString(json));
        EssCollectDataEvent event = JsonUtils.fromJson(json, EssCollectDataEvent.class);
        InverterRtData data = event.getData();
        if (null != data) {
            EssDtuPo dtu = essDtuRoDs.getBySerialNo(event.getSerialNo());
            if (null == dtu) {
                log.warn("ESS透传设备不存在");
                return Mono.just(false);
            }

            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(event.getSerialNo());
            if (CollectionUtils.isNotEmpty(refList)) {
                EssPo ess = essRoDs.getByDno(refList.get(0).getDno()); // 默认只有一个
                if (null != ess) {
                    // 直接存储原始数据
                    this.pushData2Redis(ess.getTimeZone(), refList.get(0).getDno(),
                        JsonUtils.fromJson(JsonUtils.toJsonString(data)));

                    if (!EquipStatus.NORMAL.equals(ess.getStatus())) {
                        essRwDs.updateStatus(ess.getDno(), EquipStatus.NORMAL);
                    }

                    // EMS运行数据处理
                    EssEquipPo ems = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                        (long) EssEquipType.EMS.getCode());
                    if (null != ems) {
                        EssRtReq<JsonNode> meterRt = new EssRtReq<>();
                        meterRt.setDno(ess.getDno());
                        meterRt.setEquipId(ems.getEquipId());
                        meterRt.setEquipTypeId(ems.getEquipTypeId());
                        meterRt.setEquipType(ems.getEquipType());
                        meterRt.setRtData(
                            JsonUtils.fromJson(JsonUtils.toJsonString(toEms(data))));
                        this.pushData2Redis(ess.getTimeZone(), meterRt)
                            .subscribe(x -> log.debug("电表数据保存完成"));
                    }

                    // 光伏逆变器运行数据
                    if (CollectionUtils.isNotEmpty(data.getPvCurrentList()) &&
                        CollectionUtils.isNotEmpty(data.getPvVoltageList()) &&
                        CollectionUtils.isNotEmpty(data.getPvPowerList())) {
                        EssEquipPo pv = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                            (long) EssEquipType.PV_INV.getCode());
                        if (null != pv && CollectionUtils.isNotEmpty(data.getPvVoltageList())) {
                            for (int i = 0; i < data.getPvVoltageList().size(); i++) {
                                EssRtReq<JsonNode> pvRt = new EssRtReq<>();
                                pvRt.setDno(ess.getDno());
                                pvRt.setEquipId(pv.getEquipId());
                                pvRt.setEquipTypeId(pv.getEquipTypeId());
                                pvRt.setEquipType(pv.getEquipType());
                                pvRt.setRtData(
                                    JsonUtils.fromJson(JsonUtils.toJsonString(toPvInv(i, data))));
                                this.pushData2Redis(ess.getTimeZone(), pvRt)
                                    .subscribe(x -> log.debug("光伏数据保存完成"));
                            }
                        }
                    }

                    // 电表运行数据处理
//                    EssEquipPo meter = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
//                        (long) EssEquipType.ESS_INSIDE_METER.getCode());
//                    if (null != meter) {
//                        EssRtReq<JsonNode> meterRt = new EssRtReq<>();
//                        meterRt.setDno(ess.getDno());
//                        meterRt.setEquipId(meter.getEquipId());
//                        meterRt.setEquipTypeId(meter.getEquipTypeId());
//                        meterRt.setEquipType(meter.getEquipType());
//                        meterRt.setRtData(
//                            JsonUtils.fromJson(JsonUtils.toJsonString(toMeter(data))));
//                        this.pushData2Redis(meterRt).subscribe(x -> log.debug("电表数据保存完成"));
//                    }

                    // 电池运行数据处理
                    EssEquipPo bat = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                        (long) EssEquipType.BATTERY_PACK.getCode());
                    if (null != bat && CollectionUtils.isNotEmpty(data.getBatVoltageList())) {
                        for (int i = 0; i < data.getBatVoltageList().size(); i++) {
                            EssRtReq<JsonNode> batRt = new EssRtReq<>();
                            batRt.setDno(ess.getDno());
                            batRt.setEquipId(bat.getEquipId() + i);
                            batRt.setEquipTypeId(bat.getEquipTypeId());
                            batRt.setEquipType(bat.getEquipType());
                            batRt.setRtData(
                                JsonUtils.fromJson(
                                    JsonUtils.toJsonString(toBatteryCluster(i, data))));
                            this.pushData2Redis(ess.getTimeZone(), batRt)
                                .subscribe(x -> log.debug("电池数据保存完成"));
                        }
                    }
                }
            }
        }
        return Mono.just(true);
    }

    private static BatteryCluster toBatteryCluster(int idx, InverterRtData data) {
        BatteryCluster cluster = new BatteryCluster();
//        0x120C	4	电池电压1	V		测量-电池
//        0x120E	4	电池电流1	A		测量-电池
//        0x1210	4	电池功率1	kW		测量-电池
//        0x1212	4	电池满容量百分比1	%		测量-电池
//        0x1214	4	电池满载支撑时间1	h		测量-电池
//        0x1216	4	电池电压2	V		测量-电池
//        0x1218	4	电池电流2	A		测量-电池
//        0x121A	4	电池功率2	kW		测量-电池
//        0x121C	4	电池满容量百分比2	%		测量-电池
//        0x121E	4	电池满载支撑时间2	h		测量-电池
//        0x1238	4	电池1充电次数			测量-电池
//        0x123A	4	电池1放电次数			测量-电池
//        0x123C	4	电池2充电次数			测量-电池
//        0x123E	4	电池2放电次数			测量-电池
//        0x125E	4	电池充电量	kW.h		测量-统计
//        0x1260	4	电池放电量	kW.h		测量-统计
//        0x126A	4	当天电池充量	kW.h		测量-统计
//        0x126C	4	当天电池放量	kW.h		测量-统计
        if (CollectionUtils.isNotEmpty(data.getBatVoltageList())) {
            cluster.setClusterVoltage(data.getBatVoltageList().get(idx));
        }

        if (CollectionUtils.isNotEmpty(data.getBatCurrentList())) {
            cluster.setClusterCurrent(data.getBatCurrentList().get(idx));
        }

        if (CollectionUtils.isNotEmpty(data.getBatCapacityPercentageList())) {
            cluster.setSoc(data.getBatCapacityPercentageList().get(idx));
        }
        return cluster;
    }

    private static Meter toMeter(InverterRtData data) {
        Meter meter = new Meter();
        meter.setImportPower(data.getPurchaseElecAll())
            .setExportPower(data.getFeedEelcAll());
        return meter;
    }

    private static PvInv toPvInv(int idx, InverterRtData data) {
        PvInv pvInv = new PvInv();
//        0x1200	4	光伏电压1	V		测量-光伏
//        0x1202	4	光伏电流1	A		测量-光伏
//        0x1204	4	光伏功率1	kW		测量-光伏
//        0x1206	4	光伏电压2	V		测量-光伏
//        0x1208	4	光伏电流2	A		测量-光伏
//        0x120A	4	光伏功率2	kW		测量-光伏
//        0x1244	4	光伏1负载率	%		测量-光伏
//        0x1246	4	光伏2负载率	%		测量-光伏
//        0x1264	4	当天自发电量	kW.h		测量-统计
//        0x127C	4	光伏3电压	V		测量-光伏
//        0x127E	4	光伏3电流	A		测量-光伏
//        0x1280	4	光伏3功率	kW		测量-光伏
//        0x1282	4	光伏4电压	V		测量-光伏
//        0x1284	4	光伏4电流	A		测量-光伏
//        0x1286	4	光伏4功率	kW		测量-光伏
//        0x1288	4	光伏3负载率	%		测量-光伏
//        0x128A	4	光伏4负载率	%		测量-光伏

        // 户储只有一个光伏
//        private List<BigDecimal> pvVoltageList;
//        private List<BigDecimal> pvCurrentList;
//        private List<BigDecimal> pvPowerList;

        if (CollectionUtils.isNotEmpty(data.getPvCurrentList())) {
            pvInv.setCurrentL1(data.getPvCurrentList().get(idx)); // 电流
        }
        if (CollectionUtils.isNotEmpty(data.getPvVoltageList())) {
            pvInv.setVoltageL1(data.getPvVoltageList().get(idx)); // 电压
        }
        if (CollectionUtils.isNotEmpty(data.getPvPowerList())) {
            pvInv.setFeedingPower(data.getPvPowerList().get(idx).longValue()); // 功率(转换后经度会丢失)
        }

//        // 当天发电量
//        if (null != data.getKwhToday()) {
//            pvInv.setTodayKwh(data.getKwhToday());
//        }
//
//        // 发电量总量
//        if (null != data.getKwhAll()) {
//            pvInv.setTotalKwh(data.getKwhAll());
//        }
        return pvInv;
    }

    private static Ems toEms(InverterRtData data) {
        Ems ems = new Ems();
//        0x102E	4	电网A相有功功率	kW		测量-电网
//        0x1030	4	电网B相有功功率	kW		测量-电网
//        0x1032	4	电网C相有功功率	kW		测量-电网
        ems.setGridAActivePower(data.getGridActivePowerA())
            .setGridBActivePower(data.getGridActivePowerB())
            .setGridCActivePower(data.getGridActivePowerC());

//        0x1074	4	负载A相有功功率	kW		测量-负载
//        0x1076	4	负载B相有功功率	kW		测量-负载
//        0x1078	4	负载C相有功功率	kW		测量-负载
        ems.setLoadAActivePower(data.getLoadActivePowerA())
            .setLoadBActivePower(data.getLoadActivePowerB())
            .setLoadCActivePower(data.getLoadActivePowerC());

//        0x10C0	4	逆变A相有功功率	kW		测量-逆变
//        0x10C2	4	逆变B相有功功率	kW		测量-逆变
//        0x10C4	4	逆变C相有功功率	kW		测量-逆变
        ems.setInvAActivePower(data.getInverterActivePowerA())
            .setInvBActivePower(data.getInverterActivePowerB())
            .setInvCActivePower(data.getInverterActivePowerC());

        return ems;
    }

}
