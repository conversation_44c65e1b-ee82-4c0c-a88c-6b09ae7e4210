package com.cdz360.iot.ess.rest.external;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.dto.EssDtuDto;
import com.cdz360.base.model.es.type.hi.EssType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.biz.utils.GwRestUtils;
import com.cdz360.iot.ess.biz.UserEssInteractService;
import com.cdz360.iot.ess.model.dto.EssCfgInfo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "iot网关接入API", description = "南向-储能网关接口")
@RequestMapping("/iot/ess/user")
public class IotUserEssRest extends IotRestBase {

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private UserEssInteractService userEssInteractService;

    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }

    @Operation(summary = "储能DTU设备注册")
    @PostMapping(value = {"/dtu/register"})
    public ObjectResponse<EssCfgInfo> dtuRegister(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssDtuDto> req) {
        log.info(">> 储能DTU设备注册: authHd = {}, gwno = {},req = {}",
            authHd, gwno, JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        EssPo ess = userEssInteractService.dtuRegister(req.getData(), gwno);
        LogHelper.logLatency(log, IotUserEssRest.class.getSimpleName(),
            "dtuRegister", "储能DTU设备注册", startTime);
        return GwRestUtils.buildObjResponse(req.getSeq(), new EssCfgInfo()
            .setSerialNo(req.getData().getSerialNo()).setDeviceAddr(1)
            .setEssDno(ess.getDno())
            .setTimeZone(ess.getTimeZone())
            .setRate(10L)
            .setSupportMergeInstruction(EssType.THREE_PHASE.equals(ess.getType()))
            .setSamplingTime(60)); // 上面数据临时默认处理
    }

    @Operation(summary = "储能DTU设备连接中断")
    @PostMapping(value = {"/dtu/interruption"})
    public BaseResponse dtuInterruption(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "v") int ver,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssDtuDto> req) {
        log.info(">> 储能DTU设备连接中断: authHd = {}, gwno = {},req = {}",
            authHd, gwno, JsonUtils.toJsonString(req));
        long startTime = System.nanoTime();    // debug 性能问题
        checkToken(authHd, gwno);
        userEssInteractService.dtuInterruption(req.getData(), gwno);
        LogHelper.logLatency(log, IotUserEssRest.class.getSimpleName(),
            "dtuRegister", "储能DTU设备连接中断", startTime);
        return RestUtils.success();
    }

}
