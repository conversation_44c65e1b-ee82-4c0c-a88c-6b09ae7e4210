package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能采集设置项")
@Data
@Accessors(chain = true)
public class EssCfgInfo {

    @Schema(description = "通讯设备序列号", example = "E47F23500003")
    private String serialNo;

    @Schema(description = "挂载ESS设备平台唯一编号")
    private String essDno;

    @JsonProperty("tz")
    @Schema(title = "时区", example = "GMT+08:00/UTC+08:00")
    private String timeZone;

    @Schema(description = "设备通讯地址")
    @JsonProperty("addr")
    private Integer deviceAddr;

    @Schema(description = "日志输出频率")
    private Long rate;

    @Schema(description = "采样周期(单位秒)", example = "60")
    @JsonProperty("st")
    private Integer samplingTime;

    @Schema(description = "是否支持合并指令下发")
    @JsonProperty("smi")
    private Boolean supportMergeInstruction;
}
