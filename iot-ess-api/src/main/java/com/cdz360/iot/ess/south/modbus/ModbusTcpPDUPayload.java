package com.cdz360.iot.ess.south.modbus;

import com.cdz360.base.utils.JsonUtils;
import io.netty.buffer.ByteBuf;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * MODBUS TCP数据帧的PDU有效数据
 */
@Schema(description = "MODBUS TCP 消息PDU有效数据")
@Data
@Accessors(chain = true)
public class ModbusTcpPDUPayload {

    @Schema(description = "Function code")
    private short functionCode;

    @Schema(description = "数据长度")
    private short length;

    @Schema(description = "有效数据")
    private byte[] data;

    public ModbusTcpPDUPayload setData(ByteBuf content) {
        this.data = new byte[length];
        content.readBytes(this.data);
        return this;
    }

    public ModbusTcpPDUPayload setData(byte[] data) {
        this.data = data;
        return this;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
