package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.dto.UpdateEssCfgDto;
import com.cdz360.base.model.es.type.EssConfigType;
import com.cdz360.base.model.es.vo.EssCfgVo;
import com.cdz360.base.model.es.vo.EssInOutStrategyDto;
import com.cdz360.base.model.es.vo.EssPriceDto;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.north.EssNorthBizService;
import com.cdz360.iot.ess.model.param.EssDynamicCfgRWParam;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能配置相关接口", description = "储能配置")
@RequestMapping("/iot/biz/ess/cfg")
public class EssCfgRest {

    @Autowired
    private EssNorthBizService essNorthBizService;


    @Operation(summary = "获取储能设备计费模板")
    @GetMapping(value = "/{dno}/getEssPriceCfg")
    public Mono<ObjectResponse<EssPriceDto>> getEssPriceCfg(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno) {
        log.info("获取储能设备计费模板。dno = {}", dno);
        return Mono.just(essNorthBizService.getPriceCfg(dno))
            .map(v -> RestUtils.buildObjectResponse(v));
    }

    @Operation(summary = "获取设备配置")
    @GetMapping(value = "/{dno}/getSetting")
    public Mono<ObjectResponse<EssCfgVo>> getEssSetting(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno) {
        log.info("获取储能设备配置信息: dno = {}", dno);
        return Mono.just(essNorthBizService.getEssSetting(dno))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取ESS设备配置")
    @GetMapping(value = "/{dno}/getConfig")
    public Mono<ObjectResponse<EssCfgVo>> getEssConfig(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno) {
        log.info("获取储能设备配置信息: dno = {}", dno);
        return Mono.just(essNorthBizService.getEssConfig(dno))
            .map(RestUtils::buildObjectResponse);
    }

    //    /iot/biz/cfg/evse/getCfg
    // 获取设备配置信息
    @Operation(summary = "获取工商储能设备配置(从设备交互中实时获取)")
    @PostMapping(value = "/{emuDno}/getEssCfgInTime")
    public Mono<ObjectResponse<String>> getEssCfgInTime(
        @Parameter(name = "ESS编号", required = true) @PathVariable("emuDno") String emuDno,
        @Parameter(name = "获取目标类型", required = true) @RequestBody List<EssConfigType> types) {
        log.info("获取工商储能设备配置(从设备交互中实时获取): types = {}", types);
        return essNorthBizService.getEssCfgInTime(emuDno, types);
    }

    //    /iot/biz/cfg/evse/getCfg
    // 获取设备充放电时段配置
    @Operation(summary = "获取户用储能设备配置(从设备交互中实时获取)")
    @GetMapping(value = "/{dno}/getUserEssCfgInTime")
    public Mono<BaseResponse> getUserEssCfgInTime(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno) {
        log.info("获取户用储能设备配置(从设备交互中实时获取): dno = {}", dno);
        return essNorthBizService.getUserEssCfgInTime(dno);
    }

    @Operation(summary = "更新设备配置")
    @PostMapping(value = "/refreshSetting")
    public Mono<BaseResponse> deviceRefreshSetting(@RequestBody InOutTimeRangeDto dto) {
        log.info("更新设备配置: dto = {}", dto);
        return essNorthBizService.deviceRefreshSetting(dto)
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "APP设备配置更新通知")
    @PostMapping(value = "/notifySetting")
    public Mono<BaseResponse> deviceNotifySetting(@RequestBody InOutTimeRangeDto dto) {
        log.info("更新设备配置: dto = {}", dto);
        return essNorthBizService.deviceNotifySetting(dto)
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "更新设备配置")
    @PostMapping(value = "/{dno}/update")
    public Mono<ObjectResponse<String>> deviceUpdateSetting(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno,
        @RequestBody UpdateEssCfgDto dto) {
        log.info("更新设备配置: dto = {}", JsonUtils.toJsonString(dto));
        return essNorthBizService.deviceUpdateSetting(dno, dto)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "配置储能设备计费模板")
    @PostMapping(value = "/{dno}/configEssPriceCfg")
    public Mono<BaseResponse> configEssPriceCfg(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno,
        @Parameter(name = "计费模板", required = true) @RequestBody EssPriceDto priceCfg) {
        log.info("配置储能设备计费模板。dno = {}, priceCfg = {}", dno, priceCfg);
        return Mono.just(priceCfg)
            .map(s -> essNorthBizService.configPriceCfg(dno, s))
            .doOnNext(
                cfg -> essNorthBizService.sendUpdateEssCfgCmd(dno, cfg))  // 下发mqtt给到EMU,通知EMU更新配置
            .map(v -> RestUtils.success());
    }

    @Operation(summary = "户储读取配置信息")
    @PostMapping(value = "/{dno}/readCfg")
    public Mono<ObjectResponse<String>> userEssReadCfg(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno,
        @Parameter(name = "配置列表", required = true) @RequestBody EssDynamicCfgRWParam param) {
        log.info("配置储能设备动态数据。dno = {}, param = {}", dno, param);
        return Mono.just(dno)
            .map(d -> essNorthBizService.userEssReadCfg(dno, param))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "户储更新配置信息")
    @PostMapping(value = "/{dno}/refreshCfg")
    public Mono<ObjectResponse<String>> userEssRefreshCfg(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno,
        @Parameter(name = "配置列表", required = true) @RequestBody EssDynamicCfgRWParam param) {
        log.info("配置储能设备动态数据。dno = {}, param = {}", dno, param);
        return Mono.just(dno)
            .map(d -> essNorthBizService.userEssRefreshCfg(dno, param))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "配置储能设备动态数据")
    @PostMapping(value = "/{dno}/configEquipCfg")
    public Mono<BaseResponse> configEquipCfg(
        @Parameter(name = "ESS编号", required = true) @PathVariable("dno") String dno,
        @Parameter(name = "配置列表", required = true) @RequestBody JsonNode cfg) {
        log.info("配置储能设备动态数据。dno = {}, cfg = {}", dno, cfg);
        return Mono.just(dno)
            .doOnNext(d -> essNorthBizService.configEquipCfg(dno, cfg))
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "获取储能设备充放电策略")
    @GetMapping(value = "/{dno}/getEssChargeStrategy")
    public Mono<ObjectResponse<EssInOutStrategyDto>> getEssChargeStrategy(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno) {
        log.info("获取储能设备充放电策略。dno = {}", dno);
        return Mono.just(essNorthBizService.getChargeStrategy(dno))
            .map(v -> RestUtils.buildObjectResponse(v));
    }

    @Operation(summary = "配置储能设备充放电策略")
    @PostMapping(value = "/{dno}/configEssChargeStrategy")
    public Mono<BaseResponse> configEssChargeStrategy(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno,
        @Parameter(name = "充放电策略", required = true) @RequestBody EssInOutStrategyDto strategy) {
        log.info("配置储能设备充放电策略。dno = {}, strategy = {}", dno, strategy);
        return Mono.just(strategy)
            .map(s -> essNorthBizService.configChargeStrategy(dno, s))
            .doOnNext(
                cfg -> essNorthBizService.sendUpdateEssCfgCmd(dno, cfg))   // 下发mqtt给到EMU,通知EMU更新配置
            .map(v -> RestUtils.success());
    }

    @Operation(summary = "设备告警清除")
    @PostMapping(value = "/{dno}/faultClear")
    public Mono<BaseResponse> deviceFaultClear(
        @Parameter(name = "ESS编号", required = true) @PathVariable String dno) {
        log.info("设备告警清除: dno = {}", dno);
        return essNorthBizService.deviceFaultClear(dno)
            .map(x -> RestUtils.success());
    }
}
