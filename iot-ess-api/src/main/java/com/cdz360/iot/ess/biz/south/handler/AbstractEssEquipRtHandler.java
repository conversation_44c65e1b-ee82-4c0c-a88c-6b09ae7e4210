package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ess.biz.RedisEssEquipRtDataService;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class AbstractEssEquipRtHandler implements EssEquipRtDataHandler {

    @Autowired
    protected EssEquipRwDs essEquipRwDs;

    @Autowired
    protected DcEventPublisher dcEventPublisher;

    @Autowired
    protected EssEquipRtDataHandlerFactory handlerFactory;

    @Autowired
    protected RedisEssEquipRtDataService redisEssEquipRtDataService;

    protected Mono<EssRtReq<JsonNode>> pushData2Redis(String timeZone, EssRtReq<JsonNode> rtReq) {
        return Mono.just(rtReq)
            .doOnNext(rt -> redisEssEquipRtDataService.pushRtDataLt(
                timeZone, rt.getDno(), rt.getEquipId(), rt.getRtData()));
    }
}
