package com.cdz360.iot.ess.model.mongo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "BMS采集数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BmsCollectDataVo extends BmsCollectData {

    @Schema(description = "时间(格式化的时间)")
    private String time;

    @Schema(description = "设备编号")
    private String dno;
}
