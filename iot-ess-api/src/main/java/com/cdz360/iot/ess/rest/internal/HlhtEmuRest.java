package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.bi.vo.SamplingMinuteDataVo;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.EmuDailyFeeFull;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.data.cache.RedisEmuRwService;
import com.cdz360.iot.ess.biz.EssMongoBizService;
import com.cdz360.iot.ess.biz.south.EssSouthDataService;
import com.cdz360.iot.ess.model.emu.dto.EmuDailyFeeFullDto;
import com.cdz360.iot.ess.model.emu.dto.EmuRtDataDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDateTime;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能互联互通相关接口", description = "储能服务")
@RequestMapping("/iot/biz/ess/hlht/emu")
public class HlhtEmuRest {

    @Autowired
    private EssMongoBizService mongoBizService;

    @Autowired
    private RedisEmuRwService redisEmuRwService;

    @Autowired
    private EssSouthDataService essSouthDataService;

    @Operation(summary = "运行实时数据")
    @PostMapping(value = "/rtData")
    public Mono<BaseResponse> rtData(@RequestBody EmuRtDataDto param) {
        log.info("收到运行实时数据消息 EmuRtDataDto = {} ", param);
        // 将 emu 信息存到 redis
        EmuRtData emuRtDataX = new EmuRtData();
        BeanUtils.copyProperties(param, emuRtDataX);

        emuRtDataX = this.savePcsRtData(param, emuRtDataX); // 将 PCS 数据存到redis

        emuRtDataX = this.saveBmsRtData(param, emuRtDataX); // 将 BMS 数据存到redis

        redisEmuRwService.updateEmuRtData(emuRtDataX.getDno(), emuRtDataX);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "每日的充放电计费数据")
    @PostMapping(value = "/dailyFee")
    public Mono<BaseResponse> dailyFee(@RequestBody EmuDailyFeeFullDto param) {
        log.info("收到每日的充放电计费数据消息 EmuDailyFeeFull = {} ", param);
        essSouthDataService.saveDailyEmuData(param).subscribe();
        return Mono.just(RestUtils.success());
    }

    private EmuRtData savePcsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {
        if (CollectionUtils.isNotEmpty(emuRtDataIn.getPcsData())) {
            emuRtDataResult.setPcsDnos(new ArrayList<>());
            for (PcsRtData pcsRtData : emuRtDataIn.getPcsData()) {
                emuRtDataResult.getPcsDnos().add(pcsRtData.getDno());
                pcsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 PCS 数据. EMU = {}, PCS = {}", emuRtDataIn.getDno(),
                    pcsRtData.getDno());
                redisEmuRwService.updatePcsRtData(pcsRtData.getDno(), pcsRtData);

                this.savePcsInDaySamplingData(pcsRtData);  // 保存日内的功率、电压、电流曲线数据
            }
        }
        return emuRtDataResult;
    }

    private EmuRtData saveBmsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getBmsData())) {
            emuRtDataResult.setBmsDnos(new ArrayList<>());
            for (BmsRtData bmsRtData : emuRtDataIn.getBmsData()) {
                emuRtDataResult.getBmsDnos().add(bmsRtData.getDno());
                bmsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 BMS 数据. EMU = {}, BMS = {}", emuRtDataIn.getDno(),
                    bmsRtData.getDno());
                redisEmuRwService.updateBmsRtData(bmsRtData.getDno(), bmsRtData);

                this.saveBmsInDaySamplingData(emuRtDataIn.getDno(), bmsRtData);    // 保存日内的SOC曲线数据
            }
        }
        return emuRtDataResult;
    }

    private void savePcsInDaySamplingData(PcsRtData pcsData) {
        LocalDateTime time = pcsData.getLdt();  // 设备侧采样的时间点
        if (time == null) {
            return;
        }
        if (pcsData.getAcData() == null) { // 没有交流侧数据
            return;
        }

        int minute = time.getHour() * 60 + time.getMinute();
        SamplingMinuteDataVo data = new SamplingMinuteDataVo();
        data.setMinute(minute);
        if (pcsData.getAcData().getActivePower() != null) {
            data.setV1(pcsData.getAcData().getActivePower().getTotal()); // 有功功率
        }
        if (pcsData.getAcData().getVoltage() != null) {
            data.setV2(pcsData.getAcData().getVoltage().getV1());   // 电压
        }
        if (pcsData.getAcData().getCurrent() != null) {
            data.setV3(pcsData.getAcData().getCurrent().getV1());   // 电流
        }

        // 充电量/放电量
        if (null != pcsData.getChargeData()) {
            if (null != pcsData.getChargeData().getInKwh()) {
                data.setV4(pcsData.getChargeData().getInKwh()); // 充电量
            }
            if (null != pcsData.getChargeData().getOutKwh()) {
                data.setV5(pcsData.getChargeData().getOutKwh()); // 放电量
            }
        }

        if (pcsData.getAcData().getReactivePower() != null) {
            data.setV6(pcsData.getAcData().getReactivePower().getTotal()); // 无功功率
        }

        redisEmuRwService.appendPcsInDaySamplingData(pcsData.getDno(), time.toLocalDate(), data);
    }

    private void saveBmsInDaySamplingData(String emuDno, BmsRtData bmsData) {
        LocalDateTime time = bmsData.getLdt();  // 设备侧采样的时间点
        if (time == null) {
            return;
        }
        int minute = time.getHour() * 60 + time.getMinute();
        SamplingMinuteDataVo data = new SamplingMinuteDataVo();
        data.setMinute(minute)
            .setV1(bmsData.getSoc())    // SOC
            .setV2(bmsData.getBatteryTempMax())         // 最高单体温度
            .setV3(bmsData.getBatteryTempMin());        // 最低单体温度
        redisEmuRwService.appendBmsInDaySamplingData(bmsData.getDno(), time.toLocalDate(), data);

        mongoBizService.appendEssCollectData(emuDno, bmsData);
    }
}
