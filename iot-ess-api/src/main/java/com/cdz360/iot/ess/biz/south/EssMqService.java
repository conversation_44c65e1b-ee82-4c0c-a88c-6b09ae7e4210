package com.cdz360.iot.ess.biz.south;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.model.ess.dto.EssCfgDto;
import com.cdz360.iot.model.modbus.dto.ModbusDecimalTv;
import com.cdz360.iot.model.meter.dto.Meter485Cfg;
import com.cdz360.iot.model.site.mqtt.GetEssEquipReq;
import com.cdz360.iot.model.site.mqtt.ModifyEssCfgReq;
import com.cdz360.iot.model.site.mqtt.ModifyEssEquipCfgReq;
import com.cdz360.iot.model.site.mqtt.MqModifyMeterInfo;
import com.cdz360.iot.model.site.mqtt.UpdateEssCfgReq;
import com.cdz360.iot.model.site.mqtt.UploadEssDataFileReq;
import com.cdz360.iot.model.site.po.GwInfoPo;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssMqService {

    @Autowired
    private MqService mqService;

    public String modifyEssCfg(GwInfoPo gwInfo, Long cfgVer, boolean full,
        List<EssCfgDto> essCfgDtoList) {
        ModifyEssCfgReq.builder builder = new ModifyEssCfgReq.builder();
        builder.setCfgVer(cfgVer)
            .setFull(full)
            .setEssCfgDtoList(essCfgDtoList)
            .setGwno(gwInfo.getGwno());
        ModifyEssCfgReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

    /**
     * 发送更新配置的下行指令，通知EMS去云端获取新的配置
     */
    public String sendUpdateEssCfgCmd(GwInfoPo gwInfo, Long cfgId, String cfgNo) {
        UpdateEssCfgReq.builder builder = new UpdateEssCfgReq.builder();
        builder.setCfgId(cfgId)
            .setCfgNo(cfgNo)
            .setGwno(gwInfo.getGwno());
        UpdateEssCfgReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

    /**
     * 设备配置下发
     */
    public String sendUpdateEssEquipCfgCmd(
        GwInfoPo gwInfo, String equipDno, EssEquipType equipType, List<ModbusDecimalTv> tvs) {
        ModifyEssEquipCfgReq.REQ req = new ModifyEssEquipCfgReq.builder(equipDno, equipType)
            .setTvs(tvs)
            .setGwno(gwInfo.getGwno())
            .build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }


    /**
     * 下发指令，上传ESS数据文件
     *
     * @param gwInfo
     * @param date
     * @param dnoList
     * @return
     */
    public String uploadEssDataFile(GwInfoPo gwInfo, Date date, List<String> dnoList) {
        UploadEssDataFileReq.builder builder = new UploadEssDataFileReq.builder();
        builder.setDate(date)
            .setDnoList(dnoList);
        UploadEssDataFileReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

    /**
     * 下发指令，获取ESS设备列表
     *
     * @param gwInfo
     * @param dnoList
     * @return
     */
    public String syncEssEquip(GwInfoPo gwInfo, List<String> dnoList) {
        GetEssEquipReq.builder builder = new GetEssEquipReq.builder(gwInfo.getGwno());
        builder.setDnoList(dnoList);
        GetEssEquipReq.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

    /**
     * 下发修改电表信息
     *
     * @param gwInfo
     * @param cfg
     * @return
     */
    public String modifyEssMeter(GwInfoPo gwInfo, Meter485Cfg cfg) {
        MqModifyMeterInfo.builder builder = new MqModifyMeterInfo.builder(gwInfo.getGwno());
        builder.setCfg(cfg);
        MqModifyMeterInfo.REQ req = builder.build();
        mqService.publishMessage(gwInfo, false, req.toString());
        return req.getSeq();
    }

}
