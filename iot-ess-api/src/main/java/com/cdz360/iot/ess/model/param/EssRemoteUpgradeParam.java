package com.cdz360.iot.ess.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能远程升级参数")
@Data
@Accessors(chain = true)
public class EssRemoteUpgradeParam {

    //    SN=9999-15082041T&ORDERNUM=1
    @Schema(description = "Inverter serial number")
    @JsonProperty("SN")
    private String sn;

    @Schema(description = "The firmware file will be divided as many block by server. "
        + "Inverter posts the Blcok Index, server relpys the corresponding block data")
    @JsonProperty("ORDERNUM")
    private String ordernum;
}
