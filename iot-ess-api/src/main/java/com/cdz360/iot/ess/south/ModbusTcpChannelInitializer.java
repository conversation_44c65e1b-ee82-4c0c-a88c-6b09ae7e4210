package com.cdz360.iot.ess.south;

import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Modbus TCP 链路初始化, 设置tcp消息的 decoder 和 encoder
 */
@Component
@Slf4j
@Qualifier("modbusTcpChannelInitializer")
public class ModbusTcpChannelInitializer extends ChannelInitializer<SocketChannel> {

    private static final ModbusEncoder ENCODER = new ModbusEncoder();

    @Autowired
    private ModbusDecoder DECODER;

    @Autowired
    @Qualifier("modbusTcpChannelHandler")
    private ChannelInboundHandlerAdapter modbusTcpChannelHandler;

    private static final EventExecutorGroup group;

    static {
        final int nThreads = Runtime.getRuntime().availableProcessors();
        group = new DefaultEventExecutorGroup(nThreads * 5);
    }

    @Override
    protected void initChannel(SocketChannel socketChannel) {
        ChannelPipeline pipeline = socketChannel.pipeline();

//        pipeline.addLast(new IdleStateHandler(timeout, 0, 0, TimeUnit.MINUTES));

        // Add the text line codec combination first,
        // pipeline.addLast(new DelimiterBasedFrameDecoder(1024*1024, Delimiters.lineDelimiter()));
        // the encoder and decoder are static as these are sharable
        pipeline.addLast(DECODER);
        pipeline.addLast(ENCODER);

        // Tell the pipeline to run MyBusinessLogicHandler's event handler methods
        // in a different thread than an I/O thread so that the I/O thread is not blocked by
        // a time-consuming task.
        // If your business logic is fully asynchronous or finished very quickly, you don't
        // need to specify a group.
        // see: https://netty.io/4.1/api/index.html - <Building a pipeline>
        pipeline.addLast(group, "handler", modbusTcpChannelHandler);// 将I/O线程和业务线程分开
    }
}