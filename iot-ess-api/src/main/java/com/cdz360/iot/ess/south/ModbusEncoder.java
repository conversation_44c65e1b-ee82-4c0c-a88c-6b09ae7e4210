package com.cdz360.iot.ess.south;

import com.cdz360.iot.ess.south.modbus.ModbusTcpMBAPHeader;
import com.cdz360.iot.ess.south.modbus.ModbusTcpMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * TCP数据的组包
 */
@Slf4j
@ChannelHandler.Sharable
public class ModbusEncoder extends MessageToByteEncoder<ModbusTcpMessage> {

    @Override
    protected void encode(ChannelHandlerContext ctx, ModbusTcpMessage msg, ByteBuf out)
        throws Exception {
        ModbusTcpMBAPHeader.encode(out, msg.getMbapHeader());
        out.writeByte(msg.getPduPayload().getFunctionCode());
        out.writeBytes(msg.getPduPayload().getData());
    }
}
