package com.cdz360.iot.ess.south.modbus;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.DefaultByteBufHolder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Schema(description = "")
@Accessors(chain = true)
@Setter
@Getter
public class ModbusTcpByteBufHolder extends DefaultByteBufHolder {

    private short functionCode;

    public ModbusTcpByteBufHolder(ByteBuf data) {
        super(data);
    }
}
