package com.cdz360.iot.ess.biz.south.event;

import com.cdz360.data.sync.event.ess.EssPushEventType;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;

@Service
public class EssGwPushEventHandlerFactory {

    private Map<EssPushEventType, EssGwPushEventHandler> handlers = new HashMap<>();

    public void addHandler(EssPushEventType type, EssGwPushEventHandler handler) {
        this.handlers.put(type, handler);
    }

    public EssGwPushEventHandler getHandler(EssPushEventType type) {
        return this.handlers.get(type);
    }
}
