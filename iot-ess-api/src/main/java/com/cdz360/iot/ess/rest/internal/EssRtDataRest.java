//package com.cdz360.iot.ess.rest.internal;
//
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.base.utils.JsonUtils;
//import com.cdz360.base.utils.RestUtils;
//import com.cdz360.iot.ess.biz.EssRtDataService;
//import com.cdz360.iot.model.ess.vo.DayEssDataBi;
//import com.cdz360.iot.model.ess.vo.DaySiteEssRtDataBi;
//import com.cdz360.iot.model.ess.vo.EssStatusBi;
//import com.cdz360.iot.model.ess.vo.TotalEssRtDataBi;
//import com.cdz360.iot.model.pv.param.DayKwhParam;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Mono;
//
//@Slf4j
//@RestController
//@Tag(name = "储能运行时数据相关接口", description = "储能服务")
//@RequestMapping("/iot/biz/ess/rt")
//public class EssRtDataRest {
//
//    @Autowired
//    private EssRtDataService essRtDataService;
//
//    @Operation(summary = "获取场站近七天储能数据", description = "从昨天开始近七天(仅返回有数据的天数)")
//    @PostMapping(value = "/siteRtData7Day")
//    public Mono<ListResponse<DayEssDataBi>> siteRtData7Day(
//            @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId) {
//        log.info("获取场站近七天发电数据: siteId = {}", siteId);
//        return this.essRtDataService.siteRtData7Day(siteId)
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取累计数据")
//    @PostMapping(value = "/rtDataTotal")
//    public Mono<ObjectResponse<TotalEssRtDataBi>> rtDataTotal(@RequestBody DayKwhParam param) {
//        log.info("获取场站下发电数据: param = {}", JsonUtils.toJsonString(param));
//        return this.essRtDataService.rtDataTotal(param)
//                .map(RestUtils::buildObjectResponse);
//    }
//
//    @Operation(summary = "获取场站下指定时间范围储能数据量",
//            description = "仅返回带数据的日期,注意空数据日期")
//    @PostMapping(value = "/siteDayOfRangeKwh")
//    public Mono<ListResponse<DaySiteEssRtDataBi>> siteDayOfRangeKwh(
//            @RequestBody DayKwhParam param) {
//        log.info("获取场站下指定时间范围储能数据量: param = {}", JsonUtils.toJsonString(param));
//        return this.essRtDataService.siteDayOfRangeKwh(param)
//                .collectList()
//                .map(RestUtils::buildListResponse);
//    }
//
//    @Operation(summary = "获取储能状态统计数据")
//    @GetMapping(value = "/getEssStatusBi")
//    public Mono<ListResponse<EssStatusBi>> getEssStatusBi(
//            @RequestParam(value = "commIdChain", required = false) String commIdChain,
//            @RequestParam(value = "siteId", required = false) String siteId) {
//        log.info("获取储能状态统计数据: commIdChain = {}, siteId = {}", commIdChain, siteId);
//        return this.essRtDataService.getEssStatusBi(commIdChain, siteId)
//                .map(RestUtils::buildListResponse);
//    }
//
//}
