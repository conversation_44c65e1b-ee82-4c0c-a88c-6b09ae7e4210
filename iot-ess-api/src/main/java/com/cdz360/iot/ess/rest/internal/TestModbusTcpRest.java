package com.cdz360.iot.ess.rest.internal;

import com.cdz360.iot.ess.south.ModbusTcpChannelMgm;
import com.cdz360.iot.ess.south.modbus.ModbusFunctionCode;
import com.cdz360.iot.ess.south.modbus.ModbusTcpMBAPHeader;
import com.cdz360.iot.ess.south.modbus.ModbusTcpMessage;
import com.cdz360.iot.ess.south.modbus.ModbusTcpPDUPayload;
import io.netty.channel.Channel;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "测试Modbus TCP通讯接口")
@Slf4j
@RestController
@RequestMapping("/test/modbus/tcp")
public class TestModbusTcpRest {

    @Autowired
    private ModbusTcpChannelMgm channelMgm;

    @GetMapping(value = "/list")
    public Set<String> list() {
        return channelMgm.allChannelKey();
    }

    //读取线圈状态
    @GetMapping(value = "/readCoils")
    public String readCoils(@RequestParam("id") String ip) {
        Channel channel = channelMgm.get(ip);
        if (channel != null) {
            channel.writeAndFlush(new ModbusTcpMessage()
                .setMbapHeader(newReadCoilsReqHeader())
                .setPduPayload(newReadCoilsReqPdu()));
            return "sucess";
        } else {
            return "fail";
        }
    }

    public static ModbusTcpMBAPHeader newReadCoilsReqHeader() {
        byte uuid = 1;
        short transactionId = 1;
        short length = 6; // uuid(1) + code(1) + start(2) + num(2)
        ModbusTcpMBAPHeader mbapHeader = new ModbusTcpMBAPHeader();
        mbapHeader.setTransactionId(transactionId);
        mbapHeader.setProtocolId((short) 0);
        mbapHeader.setUnitId(uuid);
        mbapHeader.setLength(length);
        return mbapHeader;
    }

    public static ModbusTcpPDUPayload newReadCoilsReqPdu() {
        ModbusTcpPDUPayload pduPayload = new ModbusTcpPDUPayload();
        String address = "00AC";//寄存器起始地址
        int quantity = 1;//寄存器的数量
        short length = 4;
        byte[] pduBytes = new byte[4];
        pduPayload.setFunctionCode(ModbusFunctionCode.READ_COILS.getCode());
        pduPayload.setLength(length);
        pduBytes[0] = 0x00;
        pduBytes[1] = (byte) 0xAC;
        pduBytes[2] = 0x00;
        pduBytes[3] = 0x01;
        pduPayload.setData(pduBytes);
        return pduPayload;
    }
}
