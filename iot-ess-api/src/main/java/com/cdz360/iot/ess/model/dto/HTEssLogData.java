package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备中告警数据")
@Data
@Accessors(chain = true)
public class HTEssLogData {

    @Schema(description = "alarm code at the time of this data timestamp, only the code of active alarm")
    @JsonInclude(Include.NON_NULL)
    private Long errorCode1;

    @Schema(description = "alarm code at the time of this data timestamp, only the code of active alarm")
    @JsonInclude(Include.NON_NULL)
    private Long errorCode2;
}
