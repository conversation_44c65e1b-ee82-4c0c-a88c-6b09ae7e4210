package com.cdz360.iot.ess.model.param;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import com.cdz360.iot.model.pv.type.GtiVendor;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "电表编辑参数")
public class MeterEditParam {

    @Schema(description = "电表唯一编号")
    @Size(max = 16, message = "dno 长度不能超过 16")
    private String dno;

    @Schema(description = "场站ID")
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "电表设备编号")
    @NotNull(message = "no 不能为 null")
    @Size(max = 20, message = "no 长度不能超过 20")
    private String no;

    @Schema(description = "电表名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 100, message = "name 长度不能超过 100")
    private String name;

    @Schema(description = "品牌名称")
    private GtiVendor vendor;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "状态")
    private MeterStatusType status;

    @Schema(description = "标签（电表类型）")
    private EssEquipType type;

    @Schema(description = "电流变比")
    private Integer ctr;

    @Schema(description = "电压变比")
    private Integer vtr;

    @Schema(description = "区域")
    @Size(max = 32, message = "area 长度不能超过 32")
    private String area;

//    @Schema(description = "电价模板")
//    private String priceCode;
//
//    @Schema(description = "安装日期")
//    private String installDate;
//
//    @Schema(description = "数采方式")
//    private String collectType;
//
//    @Schema(description = "网关编号")
//    private String gwno;
//
//    @Schema(description = "下行topic")
//    private String downTopic;
//
//    @Schema(description = "串口")
//    private String serialPort;
//
//    @Schema(description = "最大读数")
//    private Integer maxRead;
//
//    @Schema(description = "通信地址")
//    private Integer comAddress;


}