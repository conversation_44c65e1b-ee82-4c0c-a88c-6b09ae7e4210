package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.base.model.es.vo.BmsSamplingDataVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ess.ds.EssDailyRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.dto.EssDto;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.CommEssMapDataVo;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CommEssBiService {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssDtuRoDs essDtuRoDs;

    @Autowired
    private EssDailyRoDs essDailyRoDs;

    @Autowired
    private EssMongoBizService essMongoBizService;

    @Deprecated(since = "20240523")
    public Mono<ObjectResponse<CommEssMapDataVo>> commEssMapData(EssMapDataParam param) {
        return Mono.just(param)
            .map(essDailyRoDs::commEssMapData)
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<List<BmsSamplingDataVo>> commEssBatterySampling(
        String siteId, SamplingParam param) {
        if (StringUtils.isBlank(siteId) && (null == param || StringUtils.isBlank(param.getDno()))) {
            throw new DcArgumentException("ess.dno.invalid");
        }

        final Map<String, EssDto> dnoMap = new HashMap<>();
        if (StringUtils.isNotBlank(siteId)) {
            List<EssDto> essList = essRoDs.findBySiteId(siteId);
            if (CollectionUtils.isEmpty(essList)) {
                return Mono.just(List.of());
            }
            dnoMap.putAll(essList.stream().collect(Collectors.toMap(EssDto::getDno, o -> o)));
            param.setDnoList(new ArrayList<>(dnoMap.keySet()));
        } else {
            EssPo ess = essRoDs.getByDno(param.getDno());
            IotAssert.isNotNull(ess, "ess.dno.invalid");
            EssDto essDto = new EssDto();
            essDto.setDno(ess.getDno()).setName(ess.getName());
            dnoMap.put(param.getDno(), essDto);
        }

        return Mono.just(param)
            .map(essMongoBizService::bmsCollectSampling)
            .map(list -> list.stream().map(x -> {
                    BmsSamplingDataVo tmp = new BmsSamplingDataVo();
                    BeanUtils.copyProperties(x, tmp);
                    tmp.setName(dnoMap.getOrDefault(x.getDno(), new EssDto()).getName());
                    return tmp;
                }).sorted(Comparator.comparing(BmsSamplingDataVo::getTime))
                .collect(Collectors.toList()));
    }
}
