package com.cdz360.iot.ess.biz.south;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ess.model.emu.dto.EmuDailyFeeFullDto;
import com.cdz360.iot.feign.biz.BizDataCoreFeignClient;
import com.cdz360.iot.model.ess.po.EssDailyPo;
import com.cdz360.iot.model.ess.po.EssPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssSouthDataService {


    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private BizDataCoreFeignClient bizDataCoreFeignClient;


    @Transactional
    public Mono<BaseResponse> saveDailyEmuData(EmuDailyFeeFullDto emuDailyFeeIn) {

        EssPo ess = essRoDs.getByDno(emuDailyFeeIn.getDno());
        if (null == ess) {
            log.error("ESS设备编号不存在: {}", emuDailyFeeIn.getDno());
            return Mono.empty();
        }

        EssDailyPo dailyPo = new EssDailyPo();
        dailyPo.setEssId(ess.getId())
            .setSiteId(ess.getSiteId());
        return bizDataCoreFeignClient.saveEssDailyData(ess.getSiteId(),
            emuDailyFeeIn.getSeq(),
            emuDailyFeeIn);
    }
}
