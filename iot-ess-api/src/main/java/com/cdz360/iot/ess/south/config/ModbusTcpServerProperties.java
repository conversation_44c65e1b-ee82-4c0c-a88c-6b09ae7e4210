package com.cdz360.iot.ess.south.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * netty 相关的配置参数
 */
@Data
@Accessors(chain = true)
@ConfigurationProperties(prefix = "modbus.tcp")
public class ModbusTcpServerProperties {

    private int port;

    private int bossCount;

    private int workerCount;

    private boolean keepAlive;

    private int backlog;
}