package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.iot.ess.south.config.EmuMqttConfig;
import com.cdz360.iot.mqtt.IMqttMsgHandler;
import com.cdz360.iot.mqtt.MqttListener;
import com.cdz360.iot.mqtt.MqttProperties;
import java.util.HashMap;
import java.util.Map;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EmuMqttService {

    String TOPIC_DEVICE_INFO = "/emu/deviceInfo";
    String TOPIC_DI_DATA = "/emu/diData";

    String TOPIC_RT_DATA = "/emu/rtData";
    String TOPIC_DAILY_FEE = "/emu/dailyFee";
    String TOPIC_MODIFY_RESULT = "/emu/modifyResult"; // 设置修改成功的通知

    @Autowired
    private EmuMqttConfig emuMqttConfig;

    private MqttListener mqttListener;

    @PostConstruct
    public void init() {
        log.info("初始化 MQTT 服务");
        this.initListener();
    }

    private void initListener() {
        MqttProperties mqttProps = new MqttProperties();
        mqttProps.setEnv(emuMqttConfig.getEnv())
            .setName("EMU")
            .setHostIp(emuMqttConfig.getHostIp())
            .setUrl(emuMqttConfig.getUrl())
            .setUsername(emuMqttConfig.getUsername())
            .setPassword(emuMqttConfig.getPassword());
        this.mqttListener = new MqttListener(mqttProps, new IMqttMsgHandler() {
            @Override
            public boolean handleMessage(String handlerName, String msg) {
                try {
                    // traceId
                    Map<String, String> context = new HashMap<>();
                    context.put("traceId",
                        "mqtt-" + RandomStringUtils.randomAlphanumeric(11).toLowerCase());
                    context.put("spanId",
                        "mqtt-" + RandomStringUtils.randomAlphanumeric(11).toLowerCase());
                    MDC.setContextMap(context);

                    return EmuMqttHandlerFactory.getHandler(handlerName)
                        .handleMessage(handlerName, msg);
                } catch (Exception e) {
                    log.error("处理 mqtt 消息失败. handlerName = {}, msg = {}", handlerName, msg,
                        e);
                }
                return false;
            }
        });

        this.mqttListener.connect(mqttProps);

        this.mqttListener.subscribe(EmuMqttHandlerName.EMU_RT_INFO, TOPIC_DEVICE_INFO); // 设备实时信息
        this.mqttListener.subscribe(EmuMqttHandlerName.EMU_RT_DATA, TOPIC_RT_DATA); // 设备实时数据
        this.mqttListener.subscribe(EmuMqttHandlerName.EMU_DI_DATA, TOPIC_DI_DATA); // 设备DI数据
        this.mqttListener.subscribe(EmuMqttHandlerName.EMU_DAILY_FEE, TOPIC_DAILY_FEE); // 每日充放电计费数据
        this.mqttListener.subscribe(EmuMqttHandlerName.EMU_CFG_MODIFY_RESULT, TOPIC_MODIFY_RESULT);
    }
}
