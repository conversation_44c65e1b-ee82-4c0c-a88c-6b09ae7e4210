package com.cdz360.iot.ess.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备运行时数据上传信息")
@Data
@Accessors(chain = true)
public class EssRtDataReportParam {

    @Schema(description = "Version control of the protocol")
    @JsonProperty("STRID")
    private String strId;

    @Schema(description = "设备复合信息",
        example = "[YYYYMMDDHHMM],[model name],[serial number],[display format],[Material number_internal version(DSP1_version)], [DSP2_version],[DSP3_version],[CSB Version],[mode],[EMS_status],[temp],[H-total], [Self_consumption_day], [Self_sufficiency_day], [Self_consumption_mon], [Self_sufficiency_mon],[Self_consumption_year],[Self_sufficiency_year], [Self_consumption_total],[Self_sufficiency_total],[DRM_Status]")
    @JsonProperty("INV")
    private String inv;

    @Schema(description = "光伏复合信息",
        example = "[Pdc],[Ppeak],[Eday], [Emon], [Eyear], [Etotal], [PdcToload],[EdayToload], [EmonToload], [EyearToload], [EtotalToload], [Vmppt1], [Imppt1], [Vmppt2], [Imppt2]& ")
    @JsonProperty("PV")
    private String pv;

    @Schema(description = "电池复合信息",
        example = "[Bat type],[Reserved],[Ah],[BMS version],[BMS_Status],[Vbat],[Ibat],[Pbat],[SOC],[SOH],[Temp], [Cycles times], [BatErrorCode], [Eday_batChrg], [Emon_batChrg], [Eyear_batChrg], [Etotal_batChrg], [Eday_batDischrg],[Emon_batDischrg], [Eyear_batDischrg], [Etotal_batDischrg] ")
    @JsonProperty("BAT")
    private String bat;

    @Schema(description = "应急电源复合信息",
        example = "[Pac1],[Pac2],[Pac3],[Fac],[Vac1],[Iac1],[Vac2],[Iac2],[Vac3],[Iac3],[EdayEps],[EmonEps], [EyearEps], [EtotalEps]")
    @JsonProperty("EPS")
    private String eps;

    @Schema(description = "GEN复合信息",
        example = "[Pac1],[Pac2],[Pac3],[Fac],[Vac1],[Iac1],[Vac2],[Iac2],[Vac3],[Iac3],[EdayGen],[EmonGen], [EyearGen], [EtotalGen]")
    @JsonProperty("GEN")
    private String gen;

    @Schema(description = "LOAD复合信息",
        example = "[Pac1],[Pac2],[Pac3],[Fac],[Vac1],[Iac1],[Vac2],[Iac2],[Vac3],[Iac3],[EdayLoad], [EmonLoad], [EyearLoad], [EtotalLoad]")
    @JsonProperty("LOAD")
    private String load;

    @Schema(description = "GRID复合信息",
        example = "[Pac1],[Pac2],[Pac3],[Fac],[Vac1],[Iac1],[Vac2],[Iac2],[Vac3],[Iac3],[EdayToGrid], [EmonToGrid], [EyearToGrid], [EtotalToGrid], [EdayFromGrid], [EmonFromGrid], [EyearFromGrid], [EtotalFromGrid]")
    @JsonProperty("GRID")
    private String grid;

    @Schema(description = "日志信息",
        example = "[ErrorCode1],[ErrorCode2]")
    @JsonProperty("LOG")
    private String log;

    @Schema(description = "错误信息",
        example = "[YYYYMMDD], [HHMMSS], [sts],[history warning code]……(Next Error Information)")
    @JsonProperty("ERROR_INFO")
    private String errorInfo;
}
