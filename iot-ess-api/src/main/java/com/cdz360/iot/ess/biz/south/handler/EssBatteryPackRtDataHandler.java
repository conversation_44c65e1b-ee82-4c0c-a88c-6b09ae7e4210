package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssBatteryBundleRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssBatteryBundleRwDs;
import com.cdz360.iot.ds.rw.EssBatteryPackRwDs;
import com.cdz360.iot.model.ess.dto.BatteryCluster;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.po.EssBatteryBundlePo;
import com.cdz360.iot.model.ess.po.EssBatteryPackPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.vo.BatteryClusterVo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssBatteryPackRtDataHandler extends AbstractEssEquipRtHandler {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private EssBatteryBundleRwDs essBatteryClusterRwDs;

    @Autowired
    private EssBatteryBundleRoDs essBatteryBundleRoDs;

    @Autowired
    private EssBatteryPackRwDs essBatteryPackRwDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssEquipType.BATTERY_PACK, this);
    }

    @Override
    public Mono<Boolean> process(EssRtReq<JsonNode> rtReq) {
        log.info("电池簇运行数据: {}", JsonUtils.toJsonString(rtReq));
        // 获取ESS信息
        EssPo ess = essRoDs.getByDno(rtReq.getDno());
        if (null == ess) {
            log.error("储能ESS设备不存在: dno = {}", rtReq.getDno());
            return Mono.just(false);
        }

        return this.pushData2Redis(ess.getTimeZone(), rtReq)
            .map(rt -> JsonUtils.fromJson(rt.getRtData(), BatteryCluster.class))
            .doOnNext(data -> {
                // 电池组数据存入redis
                if (CollectionUtils.isNotEmpty(data.getPackList())) {
                    data.getPackList().forEach(i -> {
                        if (null != i.getLmuSn()) {
                            redisEssEquipRtDataService.pushBatteryPackRtData(
                                rtReq.getDno(), rtReq.getEquipId(), i.getLmuSn(), i);
                        } else {
                            log.error(
                                "电池簇中存在lumSn 为空的电池组: essDno = {}, cluster equipId = {}",
                                rtReq.getDno(), rtReq.getEquipId());
                        }
                    });

                }
            })
            .doOnNext(data -> {
                Long stackEquipId = 3000l;
                redisEssEquipRtDataService.pushBatteryStackAndCluster(rtReq.getDno(), stackEquipId,
                    rtReq.getEquipId(), rtReq.getRtData());
            })
            .doOnNext(data -> {
                // 设备状态更新
                essEquipRwDs.updateEssEquipStatus(rtReq.getDno(), List.of(rtReq.getEquipId()),
                    EquipStatus.NORMAL, null);
            })
            .doOnNext(data -> {

                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), false);
                if (null == gw) {
                    log.error("储能ESS挂载控制器不存在: gwno = {}", ess.getGwno());
                    return;
                }

                EssEquipPo equip = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                    rtReq.getEquipId());
                if (null == equip) {
                    log.error("储能设备没有入库: dno = {}, equipId = {}", ess.getDno(),
                        rtReq.getEquipId());
                    return;
                }

                // FIXME: 目前上传簇号逻辑不明确，暂时调整
                data.setClusterNo(rtReq.getEquipId());

                BatteryClusterVo rtReqEx = new BatteryClusterVo();
                BeanUtils.copyProperties(data, rtReqEx);
                rtReqEx.setEquipId(rtReq.getEquipId())
                    .setAlertStatus(equip.getAlertStatus())
                    .setStatus(equip.getStatus());

                // 推送数据到device(MQ)
                EssVo<BatteryCluster> essVo = new EssVo<>();
                essVo.setGwno(ess.getGwno())
                    .setGwName(gw.getName())
                    .setDno(rtReq.getDno())
                    .setSn(ess.getSn())
                    .setName(ess.getName())
                    .setSiteId(gw.getSiteId())
                    .setSiteName(gw.getSiteName())
                    .setSiteCommId(gw.getSiteCommId())
                    .setEssEquipType(EssEquipType.BATTERY_PACK)
                    .setRtData(rtReqEx);
                this.dcEventPublisher.publishEssInfo(IotEvent.STATE_CHANGE, essVo);

                // FIXME: 这里默认ESS只有一个堆
                // 获取设备堆
                EssEquipPo essStack = essEquipRoDs.getEssStack(ess.getDno());
                if (null == essStack) {
                    log.warn("储能堆设备没有入库: dno = {}", ess.getDno());
                    return;
                }

                // 将堆和簇关系入库
                EssBatteryBundlePo cluster = essBatteryBundleRoDs.getByEssDnoEquipIdStackEquipId(
                    ess.getDno(), rtReq.getEquipId(), essStack.getEquipId());

                essBatteryClusterRwDs.upsetEssBatteryCluster(new EssBatteryBundlePo()
                    // 唯一: dno / essDno + stackEquipId + equipId
                    .setDno(null != cluster ? cluster.getDno() : dnoGenerator.genDno(EssEquipType.BATTERY_PACK))
                    .setClusterNo(data.getClusterNo())
                    .setEssDno(ess.getDno())
                    .setStackEquipId(essStack.getEquipId())
                    .setEquipId(rtReq.getEquipId()));

                if (CollectionUtils.isNotEmpty(data.getPackList())) {
                    // 当前仅将数据入库，逻辑待调整
                    // 有没有减少/增加逻辑待实现
                    essBatteryPackRwDs.batchUpsetEssBatteryPack(data.getPackList().stream()
                        .filter(item -> item.getLmuSn() > 0)
                        .map(item -> new EssBatteryPackPo()
                            .setPackNo(item.getPackNo())
                            .setEssDno(ess.getDno())
                            .setStackEquipId(essStack.getEquipId())
                            .setClusterEquipId(rtReq.getEquipId())
                            .setLmuSn((long) item.getLmuSn()))
                        .collect(Collectors.toList()));
                }
            })
            .map(d -> true);
    }

}
