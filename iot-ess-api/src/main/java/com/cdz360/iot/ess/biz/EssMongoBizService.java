package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.common.utils.CollectionUtils;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ess.model.mongo.BmsCollectData;
import com.cdz360.iot.ess.model.mongo.BmsCollectDataVo;
import com.cdz360.iot.ess.model.mongo.EssCollectDataInMongo;
import com.cdz360.iot.ess.model.mongo.EssSatDataInMongo;
import com.cdz360.iot.model.ess.vo.DayUserEssRtDataBi;
import com.mongodb.client.result.UpdateResult;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssMongoBizService {

    @Autowired
    private MongoTemplate template;

    // 目前仅仅收集BMS数据
    public void appendEssCollectData(String essDno, BmsRtData bmsData) {
        EssCollectDataInMongo mongoData = new EssCollectDataInMongo(
            bmsData.getLdt(), essDno);

        // 采集必要数据
        mongoData.setBmsCollectData(new BmsCollectData().setCellTempMax(bmsData.getBatteryTempMax())
            .setCellTempMin(bmsData.getBatteryTempMin()));

        template.insert(mongoData);
    }

    // BMS采集数据查询
    public List<BmsCollectDataVo> bmsCollectSampling(SamplingParam param) {
        if (CollectionUtils.isNotEmpty(param.getDnoList())) {
            if (StringUtils.isNotBlank(param.getDno())) {
                param.getDnoList().add(param.getDno());
            }
        }

        LocalDateTime from = param.getRange().getFrom();
        LocalDateTime to = param.getRange().getTo();
        Criteria criteria = CollectionUtils.isNotEmpty(param.getDnoList()) ?
            Criteria.where("dno").in(param.getDnoList()) :
            Criteria.where("dno").is(param.getDno());

        criteria.andOperator(Criteria.where("time").gte(from),
            Criteria.where("time").lt(to));
        MatchOperation matchState = Aggregation.match(criteria);

        String dateFormatString = "";
        switch (param.getSummaryType()) {
            case HOUR:
                dateFormatString = "%Y-%m-%d %H:00:00";
                break;
            case DAY:
                dateFormatString = "%Y-%m-%d";
                break;
            case MONTH:
                dateFormatString = "%Y-%m-01";
                break;
        }

        ProjectionOperation showTime = Aggregation.project("dno", "bmsCollectData")
            .and("time").dateAsFormattedString(dateFormatString).as("show-time");

//        GroupOperation groupBy = Aggregation.group("$show-time")
//            .max("bmsCollectData.cellTempMax").as("cellTempMax")
//            .min("bmsCollectData.cellTempMin").as("cellTempMin");

        Fields groupFields = Fields.from(Fields.field("dno", "$dno"),
            Fields.field("time", "$show-time"));
        GroupOperation groupBy = Aggregation.group(groupFields)
            .max("bmsCollectData.cellTempMax").as("cellTempMax")
            .min("bmsCollectData.cellTempMin").as("cellTempMin");

        ProjectionOperation projectFields = Aggregation.project(Fields.from(
            Fields.field("cellTempMax", "$cellTempMax"),
            Fields.field("cellTempMin", "$cellTempMin"),
            Fields.field("dno", "$_id.dno"),
            Fields.field("time", "$_id.time")));

        Aggregation aggregation = Aggregation.newAggregation(
            matchState, showTime, groupBy, projectFields);
        List<BmsCollectDataVo> result = template.aggregate(aggregation,
                EssCollectDataInMongo.class, BmsCollectDataVo.class)
            .getMappedResults();
        log.debug(">>>> result: {}", result);
        return result;
    }

    /**
     * 更新ESS设备统计数据
     *
     * @param day    日期
     * @param essDno ESS设备唯一编号 //     * @param essBrand ESS设备品牌
     * @param data   数据
     */
    public void updateEssDataToday(
        final LocalDate day, String essDno, InverterRtData data) {
        log.info(">> 更新ESS设备当天数据: {}, {}", day, essDno);
        IotAssert.isNotBlank(essDno, "ESS设备唯一编号不能为空");
        IotAssert.isNotNull(data, "数据不能为空");

        Update update = new Update();

        EssSatDataInMongo dataInMongo = this.getEssSatDataInMongo(day, essDno);
        if (null == dataInMongo) {
            this.insertEssData(day, essDno, data);
            return;
        }

        // 数据转换
        this.dataConvert(update, data);

        // 更新查询条件
        Query query = new Query();
        query.addCriteria(Criteria.where("dno").is(essDno))
            .addCriteria(Criteria.where("year").is(day.getYear()))
            .addCriteria(Criteria.where("month").is(day.getMonthValue()))
            .addCriteria(Criteria.where("day").is(day.getDayOfMonth()));
        log.info("从mongodb中更新ESS数据的查询条件: {} , update ==> {}", query, update);

        UpdateResult result = template.upsert(
            query, update, EssSatDataInMongo.class);
        log.info("<< 更新结果: result={}", result);
    }

    private void dataConvert(Update update, InverterRtData data) {
        update.set("selfElectricity", data.getKwhAll())
            .set("batChargeElectricity", data.getBatChargeAll())
            .set("batDischargeElectricity", data.getBatDischargeAll())
            .set("loadUsingElectricity", data.getLoadKwhAll())
            .set("selfElectricityToday", data.getKwhToday())
            .set("purchaseElectricityToday", data.getPurchaseElecToday())
            .set("offerElectricityToday", data.getFeedEelcToday())
            .set("batChargeElectricityToday", data.getBatChargeToday())
            .set("batDischargeElectricityToday", data.getBatDischargeToday())
            .set("loadUsingElectricityToday", data.getLoadKwhToday())
        ;
    }

    /**
     * 新增ESS设备统计数据
     *
     * @param day    日期
     * @param essDno ESS设备唯一编号 //     * @param essBrand ESS设备品牌
     * @param data   数据
     */
    public void insertEssData(
        final LocalDate day, String essDno, InverterRtData data) {
        EssSatDataInMongo mongoData = new EssSatDataInMongo(
            day.getYear(), day.getMonthValue(), day.getDayOfMonth(), essDno);

        // 统计信息
        mongoData.setSelfElectricity(data.getKwhAll())
            .setBatChargeElectricity(data.getBatChargeAll())
            .setBatDischargeElectricity(data.getBatDischargeAll())
            .setLoadUsingElectricity(data.getLoadKwhAll())
            .setSelfElectricityToday(data.getKwhToday())
            .setPurchaseElectricityToday(data.getPurchaseElecToday())
            .setOfferElectricityToday(data.getFeedEelcToday())
            .setBatChargeElectricityToday(data.getBatChargeToday())
            .setBatDischargeElectricityToday(data.getBatDischargeToday())
            .setLoadUsingElectricityToday(data.getLoadKwhToday());

        template.insert(mongoData);
        log.info("<< 完成ESS统计数据到mongodb: {}", mongoData.getDno());
    }

    public EssSatDataInMongo getEssSatDataInMongo(
        final LocalDate day, String essDno) {
        Query query = new Query();
        query.addCriteria(Criteria.where("dno").is(essDno))
            .addCriteria(Criteria.where("year").is(day.getYear()))
            .addCriteria(Criteria.where("month").is(day.getMonthValue()))
            .addCriteria(Criteria.where("day").is(day.getDayOfMonth()));
        log.info("更新ESS数据的查询条件: {}", query);

        EssSatDataInMongo data = template.findOne(query, EssSatDataInMongo.class);
        if (data == null) {
            log.info("数据不存在: {}, {}", day, essDno);
        }
        return data;
    }

    public List<DayUserEssRtDataBi> userEssDayOfRangeKwh(SamplingParam param) {
        LocalDateTime from = param.getRange().getFrom();
        LocalDateTime to = param.getRange().getTo();

        Query query = new Query();
        query.addCriteria(Criteria.where("dno").is(param.getDno()));

        // (==from.year and >= from.month)
        // (from.year < t.year < to.year)
        // (==to.year and < to.month)
        List<Criteria> temp = new ArrayList<>(
            List.of(
                Criteria.where("year").is(from.getYear())
                    .andOperator(Criteria.where("month").gte(from.getMonthValue())),
                Criteria.where("year").gt(from.getYear()).lt(to.getYear()),
                Criteria.where("year").is(to.getYear())
                    .andOperator(Criteria.where("month").lte(to.getMonthValue()))
            ));

        query.addCriteria(new Criteria()
            .orOperator(temp.toArray(new Criteria[0])));
        log.info("query condition: {}", query);
        switch (param.getSummaryType()) {
            case DAY:
                return template.find(query, EssSatDataInMongo.class).stream()
                    .map(x -> new DayUserEssRtDataBi()
                        .setDate(LocalDate.of(x.getYear(), x.getMonth(), x.getDay()))
                        .setSelfElectricityTotal(x.getSelfElectricity())
                        .setBatChargeElectricityTotal(x.getBatChargeElectricity())
                        .setBatDischargeElectricityTotal(x.getBatDischargeElectricity())
                        .setLoadUsingElectricityTotal(x.getLoadUsingElectricity())
                        .setSelfElectricityToday(x.getSelfElectricityToday())
                        .setPurchaseElectricityToday(x.getPurchaseElectricityToday())
                        .setOfferElectricityToday(x.getOfferElectricityToday())
                        .setBatChargeElectricityToday(x.getBatChargeElectricityToday())
                        .setBatDischargeElectricityToday(x.getBatDischargeElectricityToday())
                        .setLoadUsingElectricityToday(x.getLoadUsingElectricityToday()))
                    .collect(Collectors.toList());
            case MONTH:
                Map<LocalDate, List<EssSatDataInMongo>> collect = template.find(query,
                    EssSatDataInMongo.class).stream().collect(
                    Collectors.groupingBy(o -> LocalDate.of(o.getYear(), o.getMonth(), 1)));

                return collect.keySet().stream().sorted()
                    .map(key -> {
                        List<EssSatDataInMongo> target = collect.get(key);
                        return new DayUserEssRtDataBi().setDate(key)
                            .setSelfElectricityTotal(target.stream().map(
                                    EssSatDataInMongo::getSelfElectricity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setBatChargeElectricityTotal(target.stream().map(
                                    EssSatDataInMongo::getBatChargeElectricity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setBatDischargeElectricityTotal(target.stream().map(
                                    EssSatDataInMongo::getBatDischargeElectricity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setLoadUsingElectricityTotal(target.stream().map(
                                    EssSatDataInMongo::getLoadUsingElectricity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setSelfElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getSelfElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setPurchaseElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getPurchaseElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setOfferElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getOfferElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setBatChargeElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getBatChargeElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setBatDischargeElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getBatDischargeElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setLoadUsingElectricityToday(target.stream().map(
                                    EssSatDataInMongo::getLoadUsingElectricityToday)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                    })
                    .collect(Collectors.toList());
            default:
                throw new DcArgumentException("无效类型");
        }
    }
}
