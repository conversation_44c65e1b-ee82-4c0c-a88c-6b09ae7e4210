package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.ess.model.param.MeterEditParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class MeterService {

    @Autowired
    private MeterRoDs meterRoDs;

    @Autowired
    private MeterRwDs meterRwDs;

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    @Transactional
    public Mono<Boolean> addMeter(MeterEditParam param) {
        return Mono.just(param).flatMap(e -> {
            this.validateMeterData(e);

            MeterPo meterPo = new MeterPo();
            BeanUtils.copyProperties(e, meterPo);
            meterPo.setDno(dnoGenerator.genDno(EssEquipType.METER));
            meterPo.setCreateTime(new Date());

            EssEquipType equipType = param.getType();

            // 先插入 t_ess_equip
            EssEquipPo essEquipPo = new EssEquipPo();
            essEquipPo.setEssDno(meterPo.getSiteId())  // 特殊处理，填入SiteId
                .setDno(meterPo.getDno())
                .setName(meterPo.getName())
                .setEquipId((long) equipType.getCode())
                .setEquipType(equipType)
                .setEquipTypeId(equipType.getCode())
                .setEquipNameCn(equipType.getDesc())
                .setEquipNameEn(equipType.getDesc())
                .setEnable(true)
                .setStatus(EquipStatus.NORMAL)
                .setCreateTime(new Date());
            if (StringUtils.isNotBlank(param.getDeviceModel())) {
                essEquipPo.setEquipModel(param.getDeviceModel());
            }
            boolean essInsertResult = essEquipRwDs.insertEssEquip(essEquipPo);
            if (!essInsertResult) {
                return Mono.error(new DcServiceException("保存失败"));
            }

            boolean insertResult = meterRwDs.insertMeter(meterPo);
            if (!insertResult) {
                return Mono.error(new DcServiceException("保存失败"));
            }
            return Mono.just(insertResult);
        });
    }

    @Transactional
    public Mono<Boolean> updateMeter(MeterEditParam param) {
        return Mono.just(param).flatMap(e -> {
            IotAssert.isNotBlank(e.getDno(), "电表dno不能为空");

            this.validateMeterData(e);

            MeterPo meterPo = new MeterPo();
            BeanUtils.copyProperties(e, meterPo);
            meterPo.setUpdateTime(new Date());

            if (StringUtils.isNotBlank(param.getDeviceModel())
                || StringUtils.isNotEmpty(param.getName())
                || StringUtils.isNotEmpty(param.getSiteId())) {
                EssEquipPo essEquipPo = new EssEquipPo();
                essEquipPo.setDno(e.getDno());
                essEquipPo.setEssDno(param.getSiteId()); // 特殊处理，填入SiteId
                essEquipPo.setName(param.getName());
                essEquipPo.setEquipModel(param.getDeviceModel());
                boolean essUpdateResult = essEquipRwDs.updateEssEquip(essEquipPo);
                if (!essUpdateResult) {
                    return Mono.error(new DcServiceException("保存失败"));
                }
            }

            boolean updateResult = meterRwDs.updateMeter(meterPo);
            if (!updateResult) {
                return Mono.error(new RuntimeException("保存失败"));
            }
            return Mono.just(updateResult);
        });
    }

    public Mono<MeterPo> getMeterById(Long id) {
        return Mono.just(id).flatMap(mId -> {
            IotAssert.isTrue(mId != null && mId > 0, "电表ID不能为空或无效");
            MeterPo meter = meterRwDs.getById(mId, false);
            return Mono.justOrEmpty(meter);
        });
    }

    public Mono<MeterPo> getMeterByDno(String dno) {
        return Mono.just(dno).doOnNext(this::validateDno).flatMap(dnoValue -> {
            MeterPo meter = meterRoDs.getByDno(dnoValue);
            return meter != null ? Mono.just(meter) : Mono.empty();
        });
    }

    public Mono<ListResponse<MeterVo>> listMeter(MeterListParam param) {
        return Mono.just(param).flatMap(e -> {
            IotAssert.isNotNull(e, "查询参数不能为空");
            // 查询电表列表
            List<MeterVo> meterVoList = meterRoDs.getMeterVoList2(e);
            // 查询总数
            long total = meterRoDs.getMeterVoList2Count(e);
            return Mono.just(RestUtils.buildListResponse(meterVoList, total));
        });
    }

    @Transactional
    public Mono<Boolean> deleteMeter(String dno) {
        return Mono.just(dno).doOnNext(this::validateDno).flatMap(dnoValue -> {
            // 先检查设备是否存在
            return getMeterByDno(dnoValue).flatMap(existingMeter -> {
                MeterPo meterPo = new MeterPo();
                meterPo.setDno(dnoValue);
                meterPo.setUpdateTime(new Date());
                // 这里可以根据业务需求设置状态为删除或下线
                return Mono.just(meterRwDs.updateMeter(meterPo));
            }).switchIfEmpty(Mono.error(new DcArgumentException("设备不存在: " + dnoValue)));
        });
    }

    /**
     * 验证电表数据
     */
    private void validateMeterData(MeterEditParam param) {
        log.info("验证电表数据，param={}", JsonUtils.toJsonString(param));

        IotAssert.isNotBlank(param.getNo(), "电表编号不能为空");
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");
        IotAssert.isNotBlank(param.getName(), "设备名称不能为空");
        IotAssert.isNotNull(param.getType(), "标签不能为空");
//        IotAssert.isNotNull(param.getStatus(), "设备状态不能为空");
//        IotAssert.isNotNull(param.getOtherDevice(), "otherDevice不能为空");
    }

    /**
     * 验证设备编号
     */
    private void validateDno(String dno) {
        IotAssert.isNotBlank(dno, "设备编号不能为空");
    }

}