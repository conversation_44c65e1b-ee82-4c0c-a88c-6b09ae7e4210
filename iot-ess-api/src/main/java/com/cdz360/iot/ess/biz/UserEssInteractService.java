package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.es.dto.EssDtuDto;
import com.cdz360.base.model.es.type.hi.EssType;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuEssRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssDtuRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.EssRwDs;
import com.cdz360.iot.ds.rw.ess.ds.EssDtuEssRefRwDs;
import com.cdz360.iot.ds.rw.ess.ds.EssDtuRwDs;
import com.cdz360.iot.ess.model.dto.HTEssBatteryData;
import com.cdz360.iot.ess.model.dto.HTEssEpsData;
import com.cdz360.iot.ess.model.dto.HTEssErrorInfoData;
import com.cdz360.iot.ess.model.dto.HTEssGenData;
import com.cdz360.iot.ess.model.dto.HTEssGridData;
import com.cdz360.iot.ess.model.dto.HTEssInvData;
import com.cdz360.iot.ess.model.dto.HTEssLoadData;
import com.cdz360.iot.ess.model.dto.HTEssLogData;
import com.cdz360.iot.ess.model.dto.HTEssPvData;
import com.cdz360.iot.ess.model.param.EssRtDataReportParam;
import com.cdz360.iot.model.dtu.po.EssDtuEssRefPo;
import com.cdz360.iot.model.dtu.po.EssDtuPo;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class UserEssInteractService {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";

    @Autowired
    private EssEquipRwDs essEquipRwDs;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private EssRwDs essRwDs;

    @Autowired
    private EssDtuRoDs essDtuRoDs;

    @Autowired
    private EssDtuRwDs essDtuRwDs;

    @Autowired
    private EssDtuEssRefRwDs essDtuEssRefRwDs;

    @Autowired
    private EssDtuEssRefRoDs essDtuEssRefRoDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    public void essRtData(EssRtDataReportParam param) {
        val version = param.getStrId(); // 当前设备协议版本

        // 数据拆分
        val inv = essInvData(param.getInv());

        // 设备序列号 <-关联-> 系统设备
        if (null == inv || StringUtils.isEmpty(inv.getSerialNumber())) {
            log.warn("数据格式错误，缺少设备序列号信息");
            return;
        }

        val ess = essRoDs.getUserEssBySerialNumber(inv.getSerialNumber());
        if (null == ess) {
            log.error("户用储能设备序列号无效: {}", JsonUtils.toJsonString(inv));
            return;
        }

        if (StringUtils.isNotBlank(version) &&
            (null == ess.getSoftVersion() || !version.equals(ess.getSoftVersion()))) {
            try {
                essRwDs.updateUserEss(new EssPo().setDno(ess.getDno()).setSoftVersion(version));
            } catch (Exception e) {
                log.error("更新户用储能设备版本好失败: {} - {}", version, ess);
            }
        }

        val errorInfo = essErrorInfoData(param.getErrorInfo());
        val log = essLogData(param.getLog());
        val grid = essGridData(param.getGrid());
        val load = essLoadData(param.getLoad());
        val gen = essGenData(param.getGen());
        val eps = essEpsData(param.getEps());
        val bat = essBatteryData(param.getBat());
        val pv = essPvData(param.getPv());

        // 分别存储到redis中
        val time = inv.getTime(); // 数据发生时间

        // 正在告警信息处理
    }

    private HTEssErrorInfoData essErrorInfoData(String errorInfo) {
        val data = validData(errorInfo, "告警记录", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        // FIXME: 没有参考数据，暂时不做处理
        return new HTEssErrorInfoData();
    }

    private HTEssLogData essLogData(String log) {
        val data = validData(log, "正在告警", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssLogData()
            .setErrorCode1(Long.parseLong(data[i++]))
            .setErrorCode2(Long.parseLong(data[i++]));
    }

    private HTEssGridData essGridData(String grid) {
        val data = validData(grid, "GRID", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssGridData()
            .setPac1(new BigDecimal(data[i++]))
            .setPac2(new BigDecimal(data[i++]))
            .setPac3(new BigDecimal(data[i++]))
            .setFac(new BigDecimal(data[i++]))
            .setVAc1(new BigDecimal(data[i++]))
            .setIAc1(new BigDecimal(data[i++]))
            .setVAc2(new BigDecimal(data[i++]))
            .setIAc2(new BigDecimal(data[i++]))
            .setVAc3(new BigDecimal(data[i++]))
            .setIAc3(new BigDecimal(data[i++]))
            .setEDayToGrid(new BigDecimal(data[i++]))
            .setEMonToGrid(new BigDecimal(data[i++]))
            .setEYearToGrid(new BigDecimal(data[i++]))
            .setETotalToGrid(new BigDecimal(data[i++]))
            .setEDayFromGrid(new BigDecimal(data[i++]))
            .setEMonFromGrid(new BigDecimal(data[i++]))
            .setEYearFromGrid(new BigDecimal(data[i++]))
            .setETotalFromGrid(new BigDecimal(data[i++]))
            ;
    }

    private HTEssLoadData essLoadData(String load) {
        val data = validData(load, "LOAD", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssLoadData()
            .setPac1(new BigDecimal(data[i++]))
            .setPac2(new BigDecimal(data[i++]))
            .setPac3(new BigDecimal(data[i++]))
            .setFac(new BigDecimal(data[i++]))
            .setVAc1(new BigDecimal(data[i++]))
            .setIAc1(new BigDecimal(data[i++]))
            .setVAc2(new BigDecimal(data[i++]))
            .setIAc2(new BigDecimal(data[i++]))
            .setVAc3(new BigDecimal(data[i++]))
            .setIAc3(new BigDecimal(data[i++]))
            .setEDayLoad(new BigDecimal(data[i++]))
            .setEMonLoad(new BigDecimal(data[i++]))
            .setEYearLoad(new BigDecimal(data[i++]))
            .setETotalLoad(new BigDecimal(data[i++]))
            ;
    }

    private HTEssGenData essGenData(String gen) {
        val data = validData(gen, "GEN", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssGenData()
            .setPac1(new BigDecimal(data[i++]))
            .setPac2(new BigDecimal(data[i++]))
            .setPac3(new BigDecimal(data[i++]))
            .setFac(new BigDecimal(data[i++]))
            .setVAc1(new BigDecimal(data[i++]))
            .setIAc1(new BigDecimal(data[i++]))
            .setVAc2(new BigDecimal(data[i++]))
            .setIAc2(new BigDecimal(data[i++]))
            .setVAc3(new BigDecimal(data[i++]))
            .setIAc3(new BigDecimal(data[i++]))
            .setEDayGen(new BigDecimal(data[i++]))
            .setEMonGen(new BigDecimal(data[i++]))
            .setEYearGen(new BigDecimal(data[i++]))
            .setETotalGen(new BigDecimal(data[i++]))
            ;
    }

    private HTEssEpsData essEpsData(String eps) {
        val data = validData(eps, "EPS", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssEpsData()
            .setPac1(new BigDecimal(data[i++]))
            .setPac2(new BigDecimal(data[i++]))
            .setPac3(new BigDecimal(data[i++]))
            .setFac(new BigDecimal(data[i++]))
            .setVAc1(new BigDecimal(data[i++]))
            .setIAc1(new BigDecimal(data[i++]))
            .setVAc2(new BigDecimal(data[i++]))
            .setIAc2(new BigDecimal(data[i++]))
            .setVAc3(new BigDecimal(data[i++]))
            .setIAc3(new BigDecimal(data[i++]))
            .setEDayEPS(new BigDecimal(data[i++]))
            .setEMonEPS(new BigDecimal(data[i++]))
            .setEYearEPS(new BigDecimal(data[i++]))
            .setETotalEPS(new BigDecimal(data[i]));
    }

    private HTEssBatteryData essBatteryData(String bat) {
        val data = validData(bat, "电池", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssBatteryData()
            .setBatteryType(Integer.parseInt(data[i++]))
            .setReserved(data[i++])
            .setAh(new BigDecimal(data[i++]))
            .setBmsVersion(data[i++])
            .setBmsStatus(Integer.parseInt(data[i++]))
            .setVolt(new BigDecimal(data[i++]))
            .setCur(new BigDecimal(data[i++]))
            .setPBat(new BigDecimal(data[i++]))
            .setSoc(Integer.parseInt(data[i++]))
            .setSoh(Integer.parseInt(data[i++]))
            .setTemp(new BigDecimal(data[i++]))
            .setCyclesTimes(Integer.parseInt(data[i++]))
            .setBatErrorCode(Long.parseLong(data[i++]))
            .setEDayBatChrg(new BigDecimal(data[i++]))
            .setEMonBatChrg(new BigDecimal(data[i++]))
            .setEYearBatChrg(new BigDecimal(data[i++]))
            .setETotalBatChrg(new BigDecimal(data[i++]))
            .setEDayBatDischrg(new BigDecimal(data[i++]))
            .setEMonBatDischrg(new BigDecimal(data[i++]))
            .setEYearBatDischrg(new BigDecimal(data[i++]))
            .setETotalBatDischrg(new BigDecimal(data[i]));
    }

    private HTEssPvData essPvData(String pv) {
//        0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
        val data = validData(pv, "光伏", 15);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssPvData()
            .setPdc(new BigDecimal(data[i++]))
            .setPPeak(new BigDecimal(data[i++]))
            .setEDay(new BigDecimal(data[i++]))
            .setEMonth(new BigDecimal(data[i++]))
            .setEYear(new BigDecimal(data[i++]))
            .setETotal(new BigDecimal(data[i++]))
            .setPdcToLoad(new BigDecimal(data[i++]))
            .setEDayToLoad(new BigDecimal(data[i++]))
            .setEMonToLoad(new BigDecimal(data[i++]))
            .setEYearToLoad(new BigDecimal(data[i++]))
            .setETotalToLoad(new BigDecimal(data[i++]))
            .setVMppt1(new BigDecimal(data[i++]))
            .setIMppt1(new BigDecimal(data[i++]))
            .setVMppt2(new BigDecimal(data[i++]))
            .setIMppt2(new BigDecimal(data[i]));
    }

    private HTEssInvData essInvData(String inv) {
//        INV=[YYYYMMDDHHMM],[model name],[serial number],[display format],[Material number_internal version(DSP1_version)],
//        [DSP2_version],[DSP3_version],[CSB Version],[mode],[EMS_status],
//        [temp],[H-total], [Self_consumption_day], [Self_sufficiency_day], [Self_consumption_mon],
//        [Self_sufficiency_mon],[Self_consumption_year],[Self_sufficiency_year], [Self_consumption_total],[Self_sufficiency_total],
//        [DRM_Status]
//        202207041428,SE 5000HB-100,2135-89030333DH,000_110000_1_02,G9504-060000-_,,0,010403,0,0,0,0,0,0,0,0,0,0,0,0,0
        val data = validData(inv, "基础", 21);
        if (null == data) {
            return null;
        }
        short i = 0;
        return new HTEssInvData()
            .setTime(data[i++])
            .setModelName(data[i++])
            .setSerialNumber(data[i++])
            .setDisplayFormat(data[i++])
            .setMaterialVersion(data[i++])
            .setDsp2Version(data[i++])
            .setDsp3Version(data[i++])
            .setCsbVersion(data[i++])
            .setModel(data[i++])
            .setEmsStatus(data[i++])
            .setInternalTemperature(new BigDecimal(data[i++]))
            .setHTotal(Long.parseLong(data[i++]))
            .setSelfUsedDayRate(Integer.parseInt(data[i++]))
            .setSelfSufficiencyDayRate(Integer.parseInt(data[i++]))
            .setSelfUsedMonRate(Integer.parseInt(data[i++]))
            .setSelfSufficiencyMonRate(Integer.parseInt(data[i++]))
            .setSelfUsedYearRate(Integer.parseInt(data[i++]))
            .setSelfSufficiencyYearRate(Integer.parseInt(data[i++]))
            .setSelfUsedTotalRate(Integer.parseInt(data[i++]))
            .setSelfSufficiencyTotalRate(Integer.parseInt(data[i++]))
            .setDrmStatus(data[i]);
    }

    private String[] validData(String data, String name, int len) {

        if (StringUtils.isEmpty(data)) {
            log.warn(String.format("设备中%s数据格式无效", name));
            return null;
        }

        val result = data.split(",");
        if (result.length != len) {
            log.warn(String.format("%s数据项有误，需要人工介入排查: {}", name), data);
            return null;
        }

        return result;
    }

    @Transactional
    public EssPo dtuRegister(EssDtuDto data, String gwno) {
        IotAssert.isNotBlank(data.getSerialNo(), "设备序列号不能为空");

        // 通讯设备信息是否已经存在
        EssDtuPo dtu = essDtuRwDs.getBySerialNo(data.getSerialNo(), true);
        if (null == dtu) {
            dtu = new EssDtuPo();
            BeanUtils.copyProperties(data, dtu);
            dtu.setGwno(gwno);

            boolean b = essDtuRwDs.insertEssDtu(dtu);
            if (!b) {
                log.warn("插入DTU数据失败: {}", dtu);
            }

            // 新建关系
            return this.newEssAndRef(data.getSerialNo(), gwno);
        } else {
            BeanUtils.copyProperties(data, dtu);
            dtu.setGwno(gwno);
            boolean b = essDtuRwDs.updateEssDtu(dtu);
            if (!b) {
                log.warn("更新DTU数据失败: {}", dtu);
            }

            // 查询关联关系
            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(dtu.getSerialNo());
            if (CollectionUtils.isEmpty(refList)) {
                // 新建关系
                return this.newEssAndRef(data.getSerialNo(), gwno);
            } else {
                // ESS 状态变更
                refList.forEach(r -> essRwDs.updateEss(
                    new EssPo().setGwno(gwno).setDno(r.getDno()).setStatus(EquipStatus.NORMAL)));

                if (CollectionUtils.isNotEmpty(refList)) {
                    return essRoDs.getByDno(refList.get(0).getDno());
                }

                return new EssPo().setDno(refList.get(0).getDno());
            }
        }
    }

    private EssPo newEssAndRef(String serialNo, String gwno) {
        EssPo ess = newEssPo("1", gwno);
        boolean b = essRwDs.upsetEss(ess);
        if (!b) {
            log.warn("插入ESS数据失败: {}", ess);
        }

        // ess - dtu 关系建立
        b = essDtuEssRefRwDs.insertEssDtuEssRef(
            new EssDtuEssRefPo().setDno(ess.getDno()).setSerialNo(serialNo));
        if (!b) {
            log.warn("插入ESS与DTU关系数据失败: {} - {}", serialNo, ess);
        }

        // 附加设备
        this.batchUpsetEssEquip(ess);

        return ess;
    }

    private void batchUpsetEssEquip(EssPo ess) {
        Map<EssEquipType, Long> eTypeMap = new HashMap<>();

        List<EssEquipPo> collect = new ArrayList<>();
        // EMS
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.EMS.getCode())
            .setEquipType(EssEquipType.EMS)
            .setEquipTypeId(EssEquipType.EMS.getCode())
            .setEquipNameCn(EssEquipType.EMS.getDesc())
            .setEquipNameEn(EssEquipType.EMS.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.EMS.getDesc())
        );

        // BMS: 默认2个
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.BMS.getCode())
            .setEquipType(EssEquipType.BMS)
            .setEquipTypeId(EssEquipType.BMS.getCode())
            .setEquipNameCn(EssEquipType.BMS.getDesc())
            .setEquipNameEn(EssEquipType.BMS.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.BMS.getDesc())
        );
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.BMS.getCode() + 1)
            .setEquipType(EssEquipType.BMS)
            .setEquipTypeId(EssEquipType.BMS.getCode())
            .setEquipNameCn(EssEquipType.BMS.getDesc())
            .setEquipNameEn(EssEquipType.BMS.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.BMS.getDesc())
        );

        // 电池簇: 默认2个
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.BATTERY_PACK.getCode())
            .setEquipType(EssEquipType.BATTERY_PACK)
            .setEquipTypeId(EssEquipType.BATTERY_PACK.getCode())
            .setEquipNameCn(EssEquipType.BATTERY_PACK.getDesc())
            .setEquipNameEn(EssEquipType.BATTERY_PACK.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.BATTERY_PACK.getDesc())
        );
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.BATTERY_PACK.getCode() + 1)
            .setEquipType(EssEquipType.BATTERY_PACK)
            .setEquipTypeId(EssEquipType.BATTERY_PACK.getCode())
            .setEquipNameCn(EssEquipType.BATTERY_PACK.getDesc())
            .setEquipNameEn(EssEquipType.BATTERY_PACK.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.BATTERY_PACK.getDesc())
        );

        // 电表
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.ESS_INSIDE_METER.getCode())
            .setEquipType(EssEquipType.ESS_INSIDE_METER)
            .setEquipTypeId(EssEquipType.ESS_INSIDE_METER.getCode())
            .setEquipNameCn(EssEquipType.ESS_INSIDE_METER.getDesc())
            .setEquipNameEn(EssEquipType.ESS_INSIDE_METER.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.ESS_INSIDE_METER.getDesc())
        );

        // 光伏: 默认2个
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.PV_INV.getCode())
            .setEquipType(EssEquipType.PV_INV)
            .setEquipTypeId(EssEquipType.PV_INV.getCode())
            .setEquipNameCn(EssEquipType.PV_INV.getDesc())
            .setEquipNameEn(EssEquipType.PV_INV.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.PV_INV.getDesc())
        );
        collect.add(new EssEquipPo()
            .setDno("")
            .setEquipId((long) EssEquipType.PV_INV.getCode() + 1)
            .setEquipType(EssEquipType.PV_INV)
            .setEquipTypeId(EssEquipType.PV_INV.getCode())
            .setEquipNameCn(EssEquipType.PV_INV.getDesc())
            .setEquipNameEn(EssEquipType.PV_INV.getDesc())
            .setEnable(true)
            .setStatus(EquipStatus.NORMAL)
            .setEssDno(ess.getDno())
            .setName(EssEquipType.PV_INV.getDesc())
        );

        boolean b = this.essEquipRwDs.batchUpset(collect);
    }

    private EssPo newEssPo(String sn, String gwno) {
        return new EssPo().setDno(dnoGenerator.genDno(EssEquipType.ESS)).setSn(sn)
            .setGwno(gwno)
            .setStatus(EquipStatus.NORMAL)
            .setType(EssType.THREE_PHASE)
            .setVendor(GtiVendor.HT_ESS);
    }

    public void dtuInterruption(EssDtuDto data, String gwno) {
        IotAssert.isNotBlank(data.getSerialNo(), "设备序列号不能为空");

        // ESS 状态变更
        EssDtuPo dtu = essDtuRwDs.getBySerialNo(data.getSerialNo(), true);
        if (null != dtu) {
            List<EssDtuEssRefPo> refList = essDtuEssRefRoDs.getBySerialNo(dtu.getSerialNo());
            if (CollectionUtils.isNotEmpty(refList)) {
                refList.forEach(r -> essRwDs.upsetEss(
                    new EssPo().setGwno(gwno).setDno(r.getDno()).setStatus(EquipStatus.OFFLINE)));
            }
        }
    }
}
