package com.cdz360.iot.ess.model.emu.dto;

import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.DehRtInfo;
import com.cdz360.base.model.es.vo.FfsRtInfo;
import com.cdz360.base.model.es.vo.LiquidRtInfo;
import com.cdz360.base.model.es.vo.MeterRtInfo;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.es.vo.UpsRtInfo;
import com.cdz360.base.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EmuRtInfoDto extends com.cdz360.base.model.es.vo.EmuRtInfo {

    /**
     * 消息序列号
     */
    private Long seq;

    /**
     * PCS 信息
     */
    private List<PcsRtInfo> pcsList = new ArrayList<>();

    /**
     * BMS 信息
     */
    private List<BmsRtInfo> bmsList = new ArrayList<>();

    /**
     * 液冷 信息
     */
    private List<LiquidRtInfo> liquidList = new ArrayList<>();

    /**
     * 除湿器 信息
     */
    private List<DehRtInfo> dehList = new ArrayList<>();

    /**
     * UPS 信息
     */
    private List<UpsRtInfo> upsList = new ArrayList<>();

    /**
     * 消防系统 信息
     */
    private List<FfsRtInfo> ffsList = new ArrayList<>();

    /**
     * 电表 信息
     */
    private List<MeterRtInfo> meterList = new ArrayList<>();

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
