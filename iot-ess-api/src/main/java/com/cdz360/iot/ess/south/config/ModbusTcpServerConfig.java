package com.cdz360.iot.ess.south.config;

import com.cdz360.iot.ess.south.ModbusTcpChannelInitializer;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@EnableConfigurationProperties(ModbusTcpServerProperties.class)
@Configuration
public class ModbusTcpServerConfig {

    @Autowired
    private ModbusTcpServerProperties serverProperties;

    @Autowired
    private ModbusTcpChannelInitializer modbusTcpChannelInitializer;

    @Bean(name = "modbusTcpServerBootstrap")
    public ServerBootstrap modbusTcpServerBootstrap() {
        ServerBootstrap b = new ServerBootstrap();
        b.group(bossGroup(), workerGroup())
            .channel(NioServerSocketChannel.class)
            .handler(new LoggingHandler(LogLevel.DEBUG))
            .childHandler(modbusTcpChannelInitializer)
        ;
        Map<ChannelOption<?>, Object> tcpChannelOptions = tcpChannelOptions();
        Set<ChannelOption<?>> keySet = tcpChannelOptions.keySet();
        for (@SuppressWarnings("rawtypes") ChannelOption option : keySet) {
            b.option(option, tcpChannelOptions.get(option));
        }
        return b;
    }

    private Map<ChannelOption<?>, Object> tcpChannelOptions() {
        Map<ChannelOption<?>, Object> options = new HashMap<>(30);
        options.put(ChannelOption.SO_BACKLOG, serverProperties.getBacklog());
        return options;
    }

    private NioEventLoopGroup bossGroup() {
        return new NioEventLoopGroup(serverProperties.getBossCount());
    }

    private NioEventLoopGroup workerGroup() {
        return new NioEventLoopGroup(serverProperties.getWorkerCount());
    }

    @Bean(name = "modbusTcpSocketAddress")
    public InetSocketAddress modbusTcpSocketAddress() {
        return new InetSocketAddress(serverProperties.getPort());
    }
}
