package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.PvInv;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.ess.type.PvInvWorkMode;
import com.cdz360.iot.model.pv.dto.PvRtDataDto;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.type.PvMode;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssPvInvRtDataHandler extends AbstractEssEquipRtHandler {

    private static final String RANDOM_CHAR = "123456789ABCDEFGHIJKLMNOPQRSTUVWSYZ";
    private final Integer DEFAULT_GROUP_NUM = 2;

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private GtiRoDs gtiRoDs;

    @Autowired
    private GtiRwDs gtiRwDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @Autowired
    private DnoGenerator dnoGenerator;

    private static PvRtDataDto convert(PvInv data, String gtiDno) {
        return new PvRtDataDto()
            .setDeviceId(0)
            .setDno(gtiDno)
            .setSerialNo(data.getMachineModel().toString())
            .setErrorCodeList(data.getFaultInfoList().stream().map(Integer::longValue)
                .collect(Collectors.toList()))
            .setTotalKwh(data.getTotalKwh())

            .setPv1Voltage(data.getVoltageL1())
            .setPv1Current(data.getCurrentL1())
            .setPv2Voltage(data.getVoltageL2())
            .setPv2Current(data.getCurrentL2())

            .setVoltageA(data.getVoltageL1())
            .setCurrentA(data.getCurrentL1())
            .setFrequencyA(data.getFrequencyL1())
            .setVoltageB(data.getVoltageL2())
            .setCurrentB(data.getCurrentL2())
            .setFrequencyB(data.getFrequencyL2())
            .setVoltageC(data.getVoltageL3())
            .setCurrentC(data.getCurrentL3())
            .setFrequencyC(data.getFrequencyL3())
            .setActivePower(BigDecimal.valueOf(data.getFeedingPower()))
            .setRtMode(mapMode(data.getWorkMode()))
            .setDeviceTemp(data.getInternalTemp())
            .setTodayKwh(data.getTodayKwh());
    }

    private static PvMode mapMode(PvInvWorkMode workMode) {
        switch (workMode) {
            case WAIT:
                return PvMode.WAIT;
            case NORMAL:
                return PvMode.NORMAL;
            case FAULT:
            case PERMANENT_FAULT:
                return PvMode.FAULT;
            default:
                return PvMode.UNKNOWN;
        }
    }

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssEquipType.PV_INV, this);
    }

    @Override
    public Mono<Boolean> process(EssRtReq<JsonNode> rtReq) {
        log.info("光伏逆变器运行数据: {}", JsonUtils.toJsonString(rtReq));

        // 获取ESS信息
        EssPo ess = essRoDs.getByDno(rtReq.getDno());
        if (null == ess) {
            log.error("储能ESS设备不存在: dno = {}", rtReq.getDno());
            return Mono.just(false);
        }

        return this.pushData2Redis(ess.getTimeZone(), rtReq)
            .map(rt -> JsonUtils.fromJson(rt.getRtData(), PvInv.class))
            .doOnNext(data -> {
                // 设备状态更新
                if (CollectionUtils.isNotEmpty(data.getFaultInfoList())) {
                    essEquipRwDs.updateEssEquipStatus(rtReq.getDno(), List.of(rtReq.getEquipId()),
                        EquipStatus.NORMAL, EquipAlertStatus.ABNORMAL);
                } else {
                    essEquipRwDs.updateEssEquipStatus(rtReq.getDno(), List.of(rtReq.getEquipId()),
                        EquipStatus.NORMAL, EquipAlertStatus.OK);
                }
            })
            .doOnNext(data -> {
                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), false);
                if (null == gw) {
                    log.error("储能ESS挂载控制器不存在: gwno = {}", ess.getGwno());
                    return;
                }

                EssEquipPo equip = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                    rtReq.getEquipId());
                if (null == equip) {
                    log.error("储能设备没有入库: dno = {}, equipId = {}", ess.getDno(),
                        rtReq.getEquipId());
                    return;
                }

                // 推送数据到device(MQ)
                EssVo<PvInv> essVo = new EssVo<>();
                essVo.setGwno(ess.getGwno())
                    .setGwName(gw.getName())
                    .setDno(rtReq.getDno())
                    .setSn(ess.getSn())
                    .setName(ess.getName())
                    .setSiteId(gw.getSiteId())
                    .setSiteName(gw.getSiteName())
                    .setSiteCommId(gw.getSiteCommId())
                    .setEssEquipType(EssEquipType.PCS)
                    .setRtData(data);

                // 告警信息
                if (CollectionUtils.isNotEmpty(data.getFaultInfoList())) {
                    EssVo.ErrorObj errorObj = new EssVo.ErrorObj();
                    EssVo.ErrorEquip errorEquip = new EssVo.ErrorEquip();
                    errorEquip.setEquipId(equip.getEquipId())
                        .setDno(equip.getEssDno())
                        .setEquipName(equip.getName())
                        .setErrorCodeList(data.getFaultInfoList().stream()
                            .map(Integer::longValue)
                            .collect(Collectors.toList()));
                    errorObj.setEquipList(List.of(errorEquip));
                    essVo.setErrorList(List.of(errorObj));
                }
                this.dcEventPublisher.publishEssInfo(IotEvent.STATE_CHANGE, essVo);

                GtiPo update = new GtiPo();
                // 逆变器数据
                GtiPo gti = gtiRoDs.getByEssEquipId(equip.getId());
                boolean exists = gti != null;
                if (exists) {
                    update.setDno(gti.getDno());
                } else {
                    update
                        .setDno(dnoGenerator.genDno(EssEquipType.PV_INV))
                        .setGwno(ess.getGwno())
                        .setSiteId(gw.getSiteId())
                        .setDeviceModel(data.getMachineModel() != null
                            ? data.getMachineModel().toString() : null)
                        .setGroupNum(DEFAULT_GROUP_NUM)
                        .setSid(0)
                        .setEssEquipId(equip.getId())
                        .setName(equip.getName())
                        .setVendor(GtiVendor.GOOD_WE); // FIXME: 为了界面显示，暂时使用这个
                }

                if (CollectionUtils.isNotEmpty(data.getFaultInfoList())) {
                    update.setAlertStatus(EquipAlertStatus.ABNORMAL);
                } else {
                    update.setAlertStatus(EquipAlertStatus.OK);
                }
                update.setStatus(EquipStatus.NORMAL);
                boolean b = exists ? gtiRwDs.updateGtiByDno(update) : gtiRwDs.upsetGti(update);

                // 数据临时存入缓存: 获取实时数据可用
                this.redisEssEquipRtDataService.pushPvRtData(update.getDno(), convert(data,
                    update.getDno()));
            })
            .map(data -> true);
    }

}
