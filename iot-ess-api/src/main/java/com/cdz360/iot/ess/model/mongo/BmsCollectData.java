package com.cdz360.iot.ess.model.mongo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

@Schema(description = "BMS采集数据")
@Data
@Accessors(chain = true)
public class BmsCollectData {

    @Field(targetType = FieldType.DECIMAL128)
    @Schema(description = "单体最高温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal cellTempMax;

    @Field(targetType = FieldType.DECIMAL128)
    @Schema(description = "单体最低温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal cellTempMin;

//    @Schema(description = "最高单体温度")
//    private BmuSummary maxCellTemp;
//
//    @Schema(description = "最低单体温度")
//    private BmuSummary minCellTemp;
//
//    @Schema(description = "最高单体电压")
//    private BmuSummary maxCellVol;
//
//    @Schema(description = "最低单体电压")
//    private BmuSummary minCellVol;
}
