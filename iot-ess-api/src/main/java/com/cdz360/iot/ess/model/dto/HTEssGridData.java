package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备中并网运行时数据")
@Data
@Accessors(chain = true)
public class HTEssGridData {

    @Schema(description = "Grid L1 output power(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac1;

    @Schema(description = "Grid L2 output power, not used in single phase inverter(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac2;

    @Schema(description = "Grid L3 output power, not used in single phase inverter(0.1W)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pac3;

    @Schema(description = "Grid frequency(0.01Hz)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fac;

    @Schema(description = "Grid L1 voltage(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc1;

    @Schema(description = "Grid L1 current(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc1;

    @Schema(description = "Grid L2 voltage, not used in single phase inverter(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc2;

    @Schema(description = "Grid L2 current, not used in single phase inverter(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc2;

    @Schema(description = "Grid L3 voltage, not used in single phase inverter(0.1V)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal vAc3;

    @Schema(description = "Grid L3 current, not used in single phase inverter(0.01A)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal iAc3;

    @Schema(description = "The accumulated energy feed into grid a day(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayToGrid;

    @Schema(description = "The accumulated energy feed into grid a month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonToGrid;

    @Schema(description = "The accumulated energy feed into grid a year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearToGrid;

    @Schema(description = "The total accumulated energy feed into grid (1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalToGrid;

    @Schema(description = "The accumulated energy from grid a day(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eDayFromGrid;

    @Schema(description = "The accumulated energy from grid a month(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eMonFromGrid;

    @Schema(description = "The accumulated energy from grid a year(1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eYearFromGrid;

    @Schema(description = "The total accumulated energy from grid (1kWh)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal eTotalFromGrid;

}
