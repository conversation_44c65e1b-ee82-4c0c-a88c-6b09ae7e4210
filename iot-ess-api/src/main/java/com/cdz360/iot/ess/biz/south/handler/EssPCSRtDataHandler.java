package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ds.ro.GwSiteRefRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ds.ro.ess.ds.EssRoDs;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.Pcs;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.ess.po.EssPo;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.type.EquipAlertStatus;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssPCSRtDataHandler extends AbstractEssEquipRtHandler {

    @Autowired
    private EssRoDs essRoDs;

    @Autowired
    private GwSiteRefRoDs gwSiteRefRoDs;

    @Autowired
    private EssEquipRoDs essEquipRoDs;

    @PostConstruct
    public void init() {
        handlerFactory.addHandler(EssEquipType.PCS, this);
    }

    @Override
    public Mono<Boolean> process(EssRtReq<JsonNode> rtReq) {
        log.info("PCS运行数据: {}", JsonUtils.toJsonString(rtReq));

        // 获取ESS信息
        EssPo ess = essRoDs.getByDno(rtReq.getDno());
        if (null == ess) {
            log.error("储能ESS设备不存在: dno = {}", rtReq.getDno());
            return Mono.just(false);
        }

        return this.pushData2Redis(ess.getTimeZone(), rtReq)
            .map(rt -> JsonUtils.fromJson(rt.getRtData(), Pcs.class))
            .doOnNext(data -> {
                // 设备状态更新
                if (CollectionUtils.isNotEmpty(data.getDcacFaultMessageList())) {
                    essEquipRwDs.updateEssEquipStatus(rtReq.getDno(), List.of(rtReq.getEquipId()),
                        EquipStatus.NORMAL, EquipAlertStatus.ABNORMAL);
                } else {
                    essEquipRwDs.updateEssEquipStatus(rtReq.getDno(), List.of(rtReq.getEquipId()),
                        EquipStatus.NORMAL, EquipAlertStatus.OK);
                }
            })
            .doOnNext(data -> {
                GwInfoDto gw = gwSiteRefRoDs.getGwInfoByGwno(ess.getGwno(), false);
                if (null == gw) {
                    log.error("储能ESS挂载控制器不存在: gwno = {}", ess.getGwno());
                    return;
                }

                EssEquipPo equip = essEquipRoDs.getByEssDnoAndEquipId(ess.getDno(),
                    rtReq.getEquipId());
                if (null == equip) {
                    log.error("储能设备没有入库: dno = {}, equipId = {}", ess.getDno(),
                        rtReq.getEquipId());
                    return;
                }

                // 推送数据到device(MQ)
                EssVo<Pcs> essVo = new EssVo<>();
                essVo.setGwno(ess.getGwno())
                    .setGwName(gw.getName())
                    .setDno(rtReq.getDno())
                    .setSn(ess.getSn())
                    .setName(ess.getName())
                    .setSiteId(gw.getSiteId())
                    .setSiteName(gw.getSiteName())
                    .setSiteCommId(gw.getSiteCommId())
                    .setEssEquipType(EssEquipType.PCS)
                    .setRtData(data);

                // 告警信息
                if (CollectionUtils.isNotEmpty(data.getDcacFaultMessageList())) {
                    EssVo.ErrorObj errorObj = new EssVo.ErrorObj();
                    EssVo.ErrorEquip errorEquip = new EssVo.ErrorEquip();
                    errorEquip.setEquipId(equip.getEquipId())
                        .setEquipName(equip.getName())
                        .setErrorCodeList(data.getDcacFaultMessageList().stream()
                            .map(Integer::longValue)
                            .collect(Collectors.toList()));
                    errorObj.setEquipList(List.of(errorEquip));
                    essVo.setErrorList(List.of(errorObj));
                }
                this.dcEventPublisher.publishEssInfo(IotEvent.STATE_CHANGE, essVo);

                // 数据推送websocket
//                    this.dcEventPublish.publishPlugInfo
            })
            .map(data -> true);
    }
}
