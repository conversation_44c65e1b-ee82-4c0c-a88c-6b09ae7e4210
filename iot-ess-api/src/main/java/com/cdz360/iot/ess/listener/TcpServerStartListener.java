package com.cdz360.iot.ess.listener;

import com.cdz360.iot.ess.south.ModbusTcpServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 用于启动指定TCP服务
 */
@Slf4j
@Component
public class TcpServerStartListener implements ApplicationRunner {

    @Autowired
    private ModbusTcpServer modbusTcpServer;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.debug("启动指定TCP服务器");
        modbusTcpServer.start(); // MODBUS TCP SERVER 启动
    }
}
