package com.cdz360.iot.ess.biz.south.handler;

import com.cdz360.base.model.iot.type.EssEquipType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EssEquipRtDataHandlerFactory {
    private Map<EssEquipType, EssEquipRtDataHandler> handlers = new HashMap<>();

    public void addHandler(EssEquipType type, EssEquipRtDataHandler handler) {
        this.handlers.put(type, handler);
    }

    public EssEquipRtDataHandler getHandler(EssEquipType type) {
        return this.handlers.get(type);
    }
}
