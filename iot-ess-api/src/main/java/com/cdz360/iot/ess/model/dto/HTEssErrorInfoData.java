package com.cdz360.iot.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户用储能设备运行时告警记录数据")
@Data
@Accessors(chain = true)
public class HTEssErrorInfoData {

    @Schema(description = "Error occurred time") // [YYYYMMDD], [HHMMSS]
    @JsonInclude(Include.NON_NULL)
    private Long date;

    @Schema(description = "Error code type, "
        + "if the value is '0', ErrorCode mean some alarm had been triggered, otherwise,"
        + "if the value is '1', ErrorCode mean some alarm had been cleared.")
    @JsonInclude(Include.NON_NULL)
    private Integer sts;

    @Schema(description = "Error Code")
    @JsonInclude(Include.NON_NULL)
    private Long errorCode;
}
