package com.cdz360.iot.ess.rest.external;

import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.biz.IotCacheService;
import com.cdz360.iot.biz.rest.IotRestBase;
import com.cdz360.iot.ess.biz.EssMongoBizService;
import com.cdz360.iot.ess.biz.south.EssSouthBizService;
import com.cdz360.iot.ess.model.param.EssGetEmuCfgParam;
import com.cdz360.iot.model.base.BaseGwResponse;
import com.cdz360.iot.model.base.CommonResponse;
import com.cdz360.iot.model.ess.dto.EmsCfgDto;
import com.cdz360.iot.model.ess.dto.EssDynamicCfgDto;
import com.cdz360.iot.model.ess.dto.EssEquipReq;
import com.cdz360.iot.model.ess.dto.EssRtReq;
import com.cdz360.iot.model.ess.dto.EssStatusReq;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.model.pv.dto.EquipCfgReplyReq;
import com.cdz360.iot.model.pv.dto.EssUpgradeReplyReq;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "iot网关接入API (v3)", description = "南向-EMU接口")
@RequestMapping("/iot/ess")
public class IotEssRestV3 extends IotRestBase {

    @Autowired
    private EssMongoBizService essMongoBizService;

    @Autowired
    private IotCacheService iotCacheService;

    @Autowired
    private EssSouthBizService essSouthBizService;

    @Override
    protected IotCacheService getCacheService() {
        return this.iotCacheService;
    }

    @Operation(summary = "ESS在线状态上传")
    @PostMapping(value = "/essStatus", params = {"v=3"})
    public Mono<BaseGwResponse> essStatus(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssStatusReq> gwReq) {
        log.debug("ESS在线状态上传。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        return this.essSouthBizService.essStatus(gwReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "essStatus", "ESS在线状态上传", startTime);
            });
    }

    @Operation(summary = "上传ESS挂载设备列表信息")
    @PostMapping(value = "/essEquips", params = {"v=3"})
    public Mono<BaseGwResponse> essEquips(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssEquipReq> gwReq) {
        log.debug("ESS挂载设备上传。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        return this.essSouthBizService.essEquips(gwReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "essEquips", "ESS挂载设备上传", startTime);
            });
    }

    @Operation(summary = "上传ESS设备运行态数据")
    @PostMapping(value = "/essRtData", params = {"v=3"})
    public Mono<BaseGwResponse> essRtData(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssRtReq<JsonNode>> gwReq) {
        log.debug("ESS设备上传运行数据。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        return this.essSouthBizService.essRtData(gwReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "essRtData", "ESS设备上传运行数据", startTime);
            });
    }

    @Operation(summary = "上传ESS设备配置信息(emu)")
    @PostMapping(value = "/cfg/upload", params = {"v=3"})
    public BaseGwResponse essCfgUpload(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @Parameter(name = "配置内容") @RequestBody GwObjReqMsg<EmsCfgDto> essCfg) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("上传ESS设备配置信息. gwno = {}, msg = {}", gwno, essCfg);
        String seq = essCfg.getSeq();
        EmsCfgDto cfgData = essCfg.getData();
        checkToken(authHd, gwno);
        var res = new BaseGwResponse(seq);
        if (null == cfgData) {
            return res;
        }

        if (StringUtils.isBlank(cfgData.getDno())) {
            cfgData.setDno(gwno);
        }

        this.essSouthBizService.essCfgUpload(cfgData);
        log.info("<< 上传ESS设备配置信息 ok: {}", res);
        LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
            "essCfgInfo", "上传ESS设备配置信息", startTime);
        return res;
    }

    @Operation(summary = "上传ESS设备配置信息(户储)")
    @PostMapping(value = "/cfg/info", params = {"v=3"})
    public BaseGwResponse essCfgInfo(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @Parameter(name = "配置内容") @RequestBody GwObjReqMsg<InOutTimeRangeDto> essCfg) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("上传ESS设备配置信息. gwno = {}, msg = {}", gwno, essCfg);
        String seq = essCfg.getSeq();
        InOutTimeRangeDto cfgData = essCfg.getData();
        checkToken(authHd, gwno);
        var res = new BaseGwResponse(seq);
        if (null == cfgData) {
            return res;
        }
        this.essSouthBizService.essCfgInfo(cfgData);
        log.info("<< 上传ESS设备配置信息 ok: {}", res);
        LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
            "essCfgInfo", "上传ESS设备配置信息", startTime);
        return res;
    }

    @Operation(summary = "上传ESS设备动态配置信息(户储)")
    @PostMapping(value = "/cfg/dynamic/info", params = {"v=3"})
    public BaseGwResponse essDynamicCfgInfo(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @Parameter(name = "配置内容") @RequestBody GwObjReqMsg<EssDynamicCfgDto> dynamicCfg) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info("上传ESS设备动态配置信息. gwno = {}, msg = {}", gwno, dynamicCfg);
        String seq = dynamicCfg.getSeq();
        EssDynamicCfgDto cfgData = dynamicCfg.getData();
        checkToken(authHd, gwno);
        var res = new BaseGwResponse(seq);
        if (null == cfgData) {
            return res;
        }
        this.essSouthBizService.essDynamicCfgInfo(cfgData);
        log.info("<< 上传ESS设备动态配置信息 ok: {}", res);
        LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
            "essCfgInfo", "上传ESS设备配置信息", startTime);
        return res;
    }

    @Operation(summary = "配置下发结果上报")
    @PostMapping(value = "/cfg/result")
    public Mono<BaseGwResponse> essCfgReply(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EquipCfgReplyReq> gwReq) {
        log.debug("配置下发结果上报。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        return this.essSouthBizService.essCfgReply(gwReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "essCfgReply", "配置下发结果上报", startTime);
            });
    }

    @Operation(summary = "设备升级结果推送")
    @PostMapping(value = "/upgrade/result")
    public Mono<BaseGwResponse> essUpgradeReply(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssUpgradeReplyReq> gwReq) {
        log.debug("设备升级结果推送。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        return this.essSouthBizService.essUpgradeReply(gwReq.getData())
            .doOnNext(res1 -> {
                res1.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "essUpgradeResult", "设备升级结果推送", startTime);
            });
    }

    @Operation(summary = "获取 EMU 配置信息")
    @PostMapping(value = "/cfg/getEmuCfg")
    public Mono<CommonResponse<EmsCfgDto>> getEmuCfg(
        @RequestHeader(value = "Authorization", required = false) String authHd,
        @RequestParam(value = "n") String gwno,
        @RequestBody GwObjReqMsg<EssGetEmuCfgParam> gwReq) {
        log.debug("获取 EMU 配置信息。authHd = {}, gwno = {}, req = {}", authHd, gwno,
            JsonUtils.toJsonString(gwReq));
        long startTime = System.nanoTime();    // debug 性能问题

        super.checkToken(authHd, gwno);
        gwReq.setGwno(gwno);
        return Mono.just(new CommonResponse<>(this.essSouthBizService.getEmuCfg(gwReq)))
            .doOnNext(res -> this.essSouthBizService.updateEmuCfgResult(gwReq.getGwno()))
            .doOnNext(res1 -> log.debug("返回给 EMU 的配置 gwno = {}", gwno))
            .doOnNext(res2 -> {
                res2.setSeq(gwReq.getSeq());
                LogHelper.logLatency(log, IotEssRestV3.class.getSimpleName(),
                    "getEmuCfg", "获取 EMU 配置信息", startTime);
            });
    }

}
