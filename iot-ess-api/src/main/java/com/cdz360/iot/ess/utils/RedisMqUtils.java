package com.cdz360.iot.ess.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisMqUtils {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void publish(String channel, String message) {
        redisTemplate.convertAndSend(channelKey(channel, "OP_POWER"), message);
        log.info("推送状态变化: {} / {}", channel, message);
    }

    public void publish_equip(String channel, String message) {
        redisTemplate.convertAndSend(channelKey(channel, "OP_EQUIP_RW_CFG"), message);
        log.info("推送读写数据: {} / {}", channel, message);
    }

    public void publish_dynamic(String channel, String message) {
        redisTemplate.convertAndSend(channelKey(channel, "OP_RESS_RW_CFG"), message);
        log.info("推送户储动态配置: {} / {}", channel, message);
    }

    private String channelKey(String channel, String type) {
        return "op:" + type.toLowerCase() + ":" + channel;
    }
}
