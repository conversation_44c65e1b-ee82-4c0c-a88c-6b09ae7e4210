package com.cdz360.iot.ess.south.modbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * MODBUS TCP的数据帧可分为两部分：MBAP + PDU
 */
@Schema(description = "MODBUS TCP 消息")
@Data
@Accessors(chain = true)
public class ModbusTcpMessage {

    @Schema(description = "MODBUS TCP的 MBAP")
    private ModbusTcpMBAPHeader mbapHeader;

    @Schema(description = "MODBUS TCP的 PDU")
    private ModbusTcpPDUPayload pduPayload;
}
