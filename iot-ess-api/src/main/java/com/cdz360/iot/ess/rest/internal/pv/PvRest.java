package com.cdz360.iot.ess.rest.internal.pv;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.PvService;
import com.cdz360.iot.ess.model.param.GtiEditParam;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.vo.GtiVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能设备统计操作接口", description = "储能设备统计操作接口合集")
@Slf4j
@RestController
@RequestMapping("/iot/pv")
public class PvRest {

    @Autowired
    private PvService pvService;

    @PostMapping("/addGti")
    @Operation(summary = "新增光伏逆变器", description = "新增一个光伏逆变器设备")
    public Mono<BaseResponse> addGti(
        @Parameter(description = "逆变器信息", required = true)
        @Valid @RequestBody GtiEditParam param) {
        return pvService.addGti(param)
            .map(result -> RestUtils.success());
    }

    @PostMapping("/gti/update")
    @Operation(summary = "修改光伏逆变器", description = "根据设备编号修改光伏逆变器信息")
    public Mono<BaseResponse> updateGti(
        @Parameter(description = "逆变器信息", required = true)
        @Valid @RequestBody GtiEditParam param) {
        return pvService.updateGti(param)
            .map(result -> RestUtils.success());
    }

    @GetMapping("/gti/{id}")
    @Operation(summary = "根据ID查询光伏逆变器", description = "根据逆变器ID查询详细信息")
    public Mono<ObjectResponse<GtiPo>> getGtiById(
        @Parameter(description = "逆变器ID", required = true)
        @PathVariable @NotNull Long id) {
        return pvService.getGtiById(id)
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    @GetMapping("/gti/dno/{dno}")
    @Operation(summary = "根据设备编号查询光伏逆变器", description = "根据设备编号查询逆变器详细信息")
    public Mono<ObjectResponse<GtiPo>> getGtiByDno(
        @Parameter(description = "设备编号", required = true)
        @PathVariable @NotBlank String dno) {
        return pvService.getGtiByDno(dno)
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    @PostMapping("/gti/list")
    @Operation(summary = "批量查询光伏逆变器", description = "根据条件分页查询光伏逆变器列表")
    public Mono<ListResponse<GtiVo>> listGti(
        @Parameter(description = "查询参数", required = true)
        @Valid @RequestBody ListGtiParam param) {
        return pvService.listGti(param)
            .map(result -> RestUtils.buildListResponse(result.getData()));
    }

    @PostMapping("/gti/delete")
    @Operation(summary = "删除光伏逆变器", description = "根据设备编号删除光伏逆变器")
    public Mono<BaseResponse> deleteGti(
        @Parameter(description = "设备编号", required = true)
        @RequestParam @NotBlank String dno) {
        return pvService.deleteGti(dno)
            .map(result -> RestUtils.success());
    }

}
