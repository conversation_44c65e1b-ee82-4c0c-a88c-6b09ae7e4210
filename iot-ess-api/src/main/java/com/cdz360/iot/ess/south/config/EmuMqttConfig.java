package com.cdz360.iot.ess.south.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
public class EmuMqttConfig {

    @Value("${env}")
    private String env;

    @Value("${iot.gw.mqtt.serverUrl}")
    private String url;


    @Value("${eureka.instance.hostname}")
    private String hostIp;

    @Value("${iot.gw.mqtt.username}")
    private String username;

    @Value("${iot.gw.mqtt.password}")
    private String password;
}
