package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.base.model.bi.vo.SamplingMinuteDataVo;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.DehRtData;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.FfsRtData;
import com.cdz360.base.model.es.vo.LiquidRtData;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.model.es.vo.UpsRtData;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisEmuRwService;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.cdz360.data.sync.service.EssEventPublisher;
import com.cdz360.iot.ess.biz.EssMongoBizService;
import com.cdz360.iot.ess.model.emu.dto.EmuRtDataDto;
import com.cdz360.iot.mqtt.IMqttMsgHandler;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EmuMqttRtDataHandler implements IMqttMsgHandler {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private EssMongoBizService mongoBizService;

    @Autowired
    private RedisEmuRwService redisEmuRwService;

    @PostConstruct
    public void init() {
        EmuMqttHandlerFactory.addHandler(EmuMqttHandlerName.EMU_RT_DATA, this);
    }

    @Override
    public boolean handleMessage(String handlerName, String msg) {
        if (msg != null && msg.length() > 501) {
            log.info("收到 mqtt 消息 {}", msg.substring(0, 500));
        } else {
            log.info("收到 mqtt 消息 {}", msg);
        }
        EmuRtDataDto emuRtDataIn = JsonUtils.fromJson(msg, EmuRtDataDto.class);
//        log.info("反序列化后 emuRtDataIn = {}", emuRtDataIn);

        // 将 emu 信息存到 redis
        EmuRtData emuRtDataX = new EmuRtData();
        BeanUtils.copyProperties(emuRtDataIn, emuRtDataX);

        emuRtDataX = this.savePcsRtData(emuRtDataIn, emuRtDataX); // 将 PCS 数据存到redis

        emuRtDataX = this.saveBmsRtData(emuRtDataIn, emuRtDataX); // 将 BMS 数据存到redis

        emuRtDataX = this.saveLiquidRtData(emuRtDataIn, emuRtDataX); // 将 液冷 数据存到redis

        emuRtDataX = this.saveDehRtData(emuRtDataIn, emuRtDataX); // 将 除湿器 数据存到redis

        emuRtDataX = this.saveUpsRtData(emuRtDataIn, emuRtDataX); // 将 UPS 数据存到redis

        emuRtDataX = this.saveMeterRtData(emuRtDataIn, emuRtDataX); // 将电表数据存到redis

        emuRtDataX = this.saveFfsRtData(emuRtDataIn, emuRtDataX); // 将消防系统数据存到redis

        redisEmuRwService.updateEmuRtData(emuRtDataX.getDno(), emuRtDataX);

        // MQ推送数据
        dcEventPublisher.publishEssRtData(emuRtDataX);

        return true;
    }

    private EmuRtData savePcsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {
        if (CollectionUtils.isNotEmpty(emuRtDataIn.getPcsData())) {
            emuRtDataResult.setPcsDnos(new ArrayList<>());
            for (PcsRtData pcsRtData : emuRtDataIn.getPcsData()) {
                emuRtDataResult.getPcsDnos().add(pcsRtData.getDno());
                pcsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 PCS 数据. EMU= {}, PCS= {}", emuRtDataIn.getDno(),
                    pcsRtData.getDno());
                redisEmuRwService.updatePcsRtData(pcsRtData.getDno(), pcsRtData);

                this.savePcsInDaySamplingData(pcsRtData);  // 保存日内的功率、电压、电流曲线数据
            }
        }
        return emuRtDataResult;
    }

    private EmuRtData saveBmsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getBmsData())) {
            emuRtDataResult.setBmsDnos(new ArrayList<>());
            for (BmsRtData bmsRtData : emuRtDataIn.getBmsData()) {
                emuRtDataResult.getBmsDnos().add(bmsRtData.getDno());
                bmsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 BMS 数据. EMU= {}, BMS= {}", emuRtDataIn.getDno(),
                    bmsRtData.getDno());
                redisEmuRwService.updateBmsRtData(bmsRtData.getDno(), bmsRtData);

                this.saveBmsInDaySamplingData(emuRtDataIn.getDno(), bmsRtData);    // 保存日内的SOC曲线数据
            }
        }
        return emuRtDataResult;
    }

    private void saveBmsInDaySamplingData(String emuDno, BmsRtData bmsData) {
        LocalDateTime time = bmsData.getLdt();  // 设备侧采样的时间点
        if (time == null) {
            return;
        }
        int minute = time.getHour() * 60 + time.getMinute();
        SamplingMinuteDataVo data = new SamplingMinuteDataVo();
        data.setMinute(minute)
            .setV1(bmsData.getSoc())    // SOC
            .setV2(bmsData.getBatteryTempMax())         // 最高单体温度
            .setV3(bmsData.getBatteryTempMin());        // 最低单体温度
        redisEmuRwService.appendBmsInDaySamplingData(bmsData.getDno(), time.toLocalDate(), data);

        mongoBizService.appendEssCollectData(emuDno, bmsData);
    }

    private EmuRtData saveLiquidRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getLiquidData())) {
            emuRtDataResult.setLiquidDnos(new ArrayList<>());
            for (LiquidRtData liquidRtData : emuRtDataIn.getLiquidData()) {
                emuRtDataResult.getLiquidDnos().add(liquidRtData.getDno());
                liquidRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 液冷 数据. EMU= {}, Liquid= {}", emuRtDataIn.getDno(),
                    liquidRtData.getDno());
                redisEmuRwService.updateLiquidRtData(liquidRtData.getDno(), liquidRtData);

            }
        }
        return emuRtDataResult;
    }

    private EmuRtData saveDehRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getDehData())) {
            emuRtDataResult.setDehDnos(new ArrayList<>());
            for (DehRtData dehRtData : emuRtDataIn.getDehData()) {
                emuRtDataResult.getDehDnos().add(dehRtData.getDno());
                dehRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 除湿器 数据. EMU= {}, DEH= {}", emuRtDataIn.getDno(),
                    dehRtData.getDno());
                redisEmuRwService.updateDehRtData(dehRtData.getDno(), dehRtData);

            }
        }
        return emuRtDataResult;
    }

    private EmuRtData saveUpsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getUpsData())) {
            emuRtDataResult.setUpsDnos(new ArrayList<>());
            for (UpsRtData upsRtData : emuRtDataIn.getUpsData()) {
                emuRtDataResult.getUpsDnos().add(upsRtData.getDno());
                upsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新 UPS 数据. EMU= {}, UPS= {}", emuRtDataIn.getDno(),
                    upsRtData.getDno());
                redisEmuRwService.updateUpsRtData(upsRtData.getDno(), upsRtData);

            }
        }
        return emuRtDataResult;
    }

    private void savePcsInDaySamplingData(PcsRtData pcsData) {
        LocalDateTime time = pcsData.getLdt();  // 设备侧采样的时间点
        if (time == null) {
            return;
        }
        if (pcsData.getAcData() == null) { // 没有交流侧数据
            return;
        }

        int minute = time.getHour() * 60 + time.getMinute();
        SamplingMinuteDataVo data = new SamplingMinuteDataVo();
        data.setMinute(minute);
        if (pcsData.getAcData().getActivePower() != null) {
            data.setV1(pcsData.getAcData().getActivePower().getTotal()); // 有功功率
        }
        if (pcsData.getAcData().getVoltage() != null) {
            data.setV2(pcsData.getAcData().getVoltage().getV1());   // 电压
        }
        if (pcsData.getAcData().getCurrent() != null) {
            data.setV3(pcsData.getAcData().getCurrent().getV1());   // 电流
        }

        // 充电量/放电量
        if (null != pcsData.getChargeData()) {
            if (null != pcsData.getChargeData().getInKwh()) {
                data.setV4(pcsData.getChargeData().getInKwh()); // 充电量
            }
            if (null != pcsData.getChargeData().getOutKwh()) {
                data.setV5(pcsData.getChargeData().getOutKwh()); // 放电量
            }
        }

        if (pcsData.getAcData().getReactivePower() != null) {
            data.setV6(pcsData.getAcData().getReactivePower().getTotal()); // 无功功率
        }

        redisEmuRwService.appendPcsInDaySamplingData(pcsData.getDno(), time.toLocalDate(), data);
    }

    private EmuRtData saveMeterRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getMeterData())) {
            emuRtDataResult.setMeterDnos(new ArrayList<>());
            for (MeterRtData meterRtData : emuRtDataIn.getMeterData()) {
                emuRtDataResult.getMeterDnos().add(meterRtData.getDno());
                meterRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新电表数据. EMU = {}, Meter = {}",
                    emuRtDataIn.getDno(), meterRtData.getDno());
                redisEmuRwService.updateMeterRtData(meterRtData.getDno(), meterRtData);

                this.saveMeterInDaySamplingData(emuRtDataIn.getTz(),
                    meterRtData);  // 保存日内的有功功率、无功功率、功率因数曲线数据
            }
        }
        return emuRtDataResult;
    }

    private void saveMeterInDaySamplingData(String tz, MeterRtData meterData) {
        if (meterData.getTs() == null || StringUtils.isBlank(tz)) {
            log.warn("时间时区无效: {} / {}", meterData.getTs(), tz);
            return;
        }
        LocalDateTime time = LocalDateTime.ofEpochSecond(meterData.getTs(), 0,
            ZoneOffset.of(tz));

        int minute = time.getHour() * 60 + time.getMinute();
        SamplingMinuteDataVo data = new SamplingMinuteDataVo();
        data.setMinute(minute);

        if (meterData.getAbc() != null) {
            if (null != meterData.getAbc().getActivePower()) {
                data.setV1(meterData.getAbc().getActivePower().getTotal()); // 有功功率
            }

            if (null != meterData.getAbc().getReactivePower()) {
                data.setV2(meterData.getAbc().getReactivePower().getTotal());   // 无功功率
            }

            if (null != meterData.getAbc().getPf()) {
                data.setV3(meterData.getAbc().getPf().getTotal());   // 功率因数
            }
        }

        redisEmuRwService.appendMeterInDaySamplingData(
            meterData.getDno(), time.toLocalDate(), data);
    }

    private EmuRtData saveFfsRtData(EmuRtDataDto emuRtDataIn, EmuRtData emuRtDataResult) {

        if (CollectionUtils.isNotEmpty(emuRtDataIn.getFfsData())) {
            emuRtDataResult.setFfsDnos(new ArrayList<>());
            for (FfsRtData ffsRtData : emuRtDataIn.getFfsData()) {
                emuRtDataResult.getFfsDnos().add(ffsRtData.getDno());
                ffsRtData.setEssDno(emuRtDataIn.getDno());
                log.info("往 redis 更新消防系统数据. EMU = {}, Meter = {}",
                    emuRtDataIn.getDno(), ffsRtData.getDno());
                redisEmuRwService.updateFfsRtData(ffsRtData.getDno(), ffsRtData);
            }
        }
        return emuRtDataResult;
    }

}
