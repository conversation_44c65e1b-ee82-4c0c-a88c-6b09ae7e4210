package com.cdz360.iot.ess.rest.internal;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.base.model.es.vo.BmsSamplingDataVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.CommEssBiService;
import com.cdz360.iot.model.ess.param.EssMapDataParam;
import com.cdz360.iot.model.ess.vo.CommEssMapDataVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "工商储能设备统计操作接口", description = "工商储能设备统计操作接口合集")
@Slf4j
@RestController
@RequestMapping("/comm/ess/bi")
public class CommEssBiRest {

    @Autowired
    private CommEssBiService commEssBiService;

    @Deprecated(since = "20240523")
    @Operation(summary = "工商储能地图数据统计")
    @PostMapping("/map/data")
    public Mono<ObjectResponse<CommEssMapDataVo>> commEssMapData(
        @RequestBody @Valid EssMapDataParam param) {
        log.info("工商储能地图数据统计: {}", param);
        return commEssBiService.commEssMapData(param);
    }

    @Operation(summary = "工商储能电池数据采集")
    @PostMapping("/battery/sampling")
    public Mono<ListResponse<BmsSamplingDataVo>> commEssBatterySampling(
        @Parameter(description = "场站ID") @RequestParam(value = "siteId", required = false) String siteId,
        @RequestBody @Valid SamplingParam param) {
        log.info("工商储能电池数据采集: siteId = {}, {}", siteId, param);
        return commEssBiService.commEssBatterySampling(siteId, param)
            .map(RestUtils::buildListResponse);
    }

}
