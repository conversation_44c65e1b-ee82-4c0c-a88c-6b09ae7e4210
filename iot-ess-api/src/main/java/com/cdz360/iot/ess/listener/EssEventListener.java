package com.cdz360.iot.ess.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.common.base.IotConstants;
import com.cdz360.iot.ess.biz.south.EssSouthBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EssEventListener {

    @Autowired
    private EssSouthBizService essSouthBizService;

    @RabbitHandler
    @RabbitListener(queues = IotConstants.MQ_QUEUE_ESS_GW_PUSH_DATA)
    public void emuGwPushListener(String msg) {
        log.debug(">> 储能上报 msg = {}", msg);
        try {
            essSouthBizService.essGwPushEvent(JsonUtils.fromJson(msg));
        } catch (Exception e) {
            log.warn("message:{}", e.getMessage(), e);
            log.warn("储能上报收到未识别的消息. msg = {}", msg);
        }

        log.debug("<<");
    }
}
