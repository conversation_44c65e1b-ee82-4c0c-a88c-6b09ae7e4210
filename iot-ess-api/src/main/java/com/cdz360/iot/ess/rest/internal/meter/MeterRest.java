package com.cdz360.iot.ess.rest.internal.meter;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.biz.MeterService;
import com.cdz360.iot.ess.model.param.MeterEditParam;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "电表管理接口", description = "电表设备管理接口合集")
@Slf4j
@RestController
@RequestMapping("/iot/meter")
public class MeterRest {

    @Autowired
    private MeterService meterService;

    @PostMapping("/add")
    @Operation(summary = "新增电表", description = "新增一个电表设备")
    public Mono<BaseResponse> addMeter(
        @Parameter(description = "电表信息", required = true)
        @Valid @RequestBody MeterEditParam param) {
        return meterService.addMeter(param)
            .map(result -> RestUtils.success());
    }

    @PostMapping("/update")
    @Operation(summary = "修改电表", description = "根据设备编号修改电表信息")
    public Mono<BaseResponse> updateMeter(
        @Parameter(description = "电表信息", required = true)
        @Valid @RequestBody MeterEditParam param) {
        return meterService.updateMeter(param)
            .map(result -> RestUtils.success());
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询电表", description = "根据电表ID查询详细信息")
    public Mono<ObjectResponse<MeterPo>> getMeterById(
        @Parameter(description = "电表ID", required = true)
        @PathVariable @NotNull Long id) {
        return meterService.getMeterById(id)
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    @GetMapping("/dno/{dno}")
    @Operation(summary = "根据设备编号查询电表", description = "根据设备编号查询电表详细信息")
    public Mono<ObjectResponse<MeterPo>> getMeterByDno(
        @Parameter(description = "设备编号", required = true)
        @PathVariable @NotBlank String dno) {
        return meterService.getMeterByDno(dno)
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    @PostMapping("/list")
    @Operation(summary = "批量查询电表", description = "根据条件分页查询电表列表")
    public Mono<ListResponse<MeterVo>> listMeter(
        @Parameter(description = "查询参数", required = true)
        @Valid @RequestBody MeterListParam param) {
        return meterService.listMeter(param);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除电表", description = "根据设备编号删除电表")
    public Mono<BaseResponse> deleteMeter(
        @Parameter(description = "设备编号", required = true)
        @RequestParam @NotBlank String dno) {
        return meterService.deleteMeter(dno)
            .map(result -> RestUtils.success());
    }

}