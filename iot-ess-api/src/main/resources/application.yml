app:
  name: iot-ess

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: iot-ess-dev
  config:
    import: "configserver:http://oam-test.rnd.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc,redis,zipkin,mongodb-ess
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01


management:
  context-path: /admin
  security:
    enabled: false
  endpoints:
    web:
      exposure:
        include: info,health,shutdown,metrics,beans
  endpoint:
    shutdown:
      enabled: true



eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>/eureka/
#      defaultZone: http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000



logging:
  level:
    com.cdz360: 'DEBUG'
    org.springframework: 'WARN'
    org.mybatis: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.iot.ess.rest
  swagger-ui:
    path: /swagger-ui.html

modbus:
  tcp:
    port: 8552
    boss-count: 1
    worker-count: 1
    keep-alive: true
    backlog: 100


mqtt:
  emu:
    clientId: es_api
    url: 111
