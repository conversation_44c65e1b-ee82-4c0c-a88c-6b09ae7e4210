package com.cdz360.iot.ess.biz;

import com.cdz360.iot.common.utils.TimeZoneParser;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RedisEssEquipRtDataServiceTest {

    @Test
    public void test() {
        String[] timeZones = {
            "GMT+08:00 ",
            " UTC+08:00",
            "Asia/Shanghai",
//            "America/New_York",
            "GMT+09:00",
            "GMT+09",
            "GMT+9",
            "GMT+09:1",
            "UTC-08:00",
            "GMT+0800", //不符合规范的格式
//            "InvalidTimeZone",
            null,
//            "+08:00" //只包含偏移量
        };

        for (String timeZone : timeZones) {
            System.out.println(timeZone + ": " + TimeZoneParser.isValidTimeZone(timeZone));
        }
    }

    @Test
    public void test2() {
        ZonedDateTime now2 = ZonedDateTime.now();
        LocalDateTime now1 = now2.toLocalDateTime();
        System.out.println("LocalDateTime.now() = " + now1);
        System.out.println("ZonedDateTime.now() = " + now2);

        String timeZone = "GMT+10:02";
        String offsetString = timeZone.substring(3);

        LocalDateTime res = now1
            .atZone(ZoneId.systemDefault())
            .withZoneSameInstant(ZoneOffset.of(offsetString))
            .toLocalDateTime();
        System.out.println("res = " + res);

        res = now2
            .withZoneSameInstant(ZoneOffset.of(offsetString))
            .toLocalDateTime();
        System.out.println("res = " + res);

        String tz = "+06:00";
        ZonedDateTime zonedDateTime = now2.withZoneSameInstant(ZoneId.of(tz));
        System.out.println("zonedDateTime = " + zonedDateTime);

        tz = "UTC+06:00";
        zonedDateTime = now2.withZoneSameInstant(ZoneId.of(tz));
        System.out.println("zonedDateTime = " + zonedDateTime);

        tz = "GMT+6";
        zonedDateTime = now2.withZoneSameInstant(ZoneId.of(tz));
        System.out.println("zonedDateTime = " + zonedDateTime);

        tz = ZoneId.systemDefault().getId();
        zonedDateTime = now2.withZoneSameInstant(ZoneId.of(tz));
        System.out.println("zonedDateTime = " + zonedDateTime);
    }


}