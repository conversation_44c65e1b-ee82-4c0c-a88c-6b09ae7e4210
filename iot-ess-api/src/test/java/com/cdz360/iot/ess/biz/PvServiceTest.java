package com.cdz360.iot.ess.biz;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.ds.ro.pv.ds.GtiRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.GtiRwDs;
import com.cdz360.iot.ess.model.param.GtiEditParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.pv.param.ListGtiParam;
import com.cdz360.iot.model.pv.po.GtiPo;
import com.cdz360.iot.model.pv.type.GtiVendor;
import com.cdz360.iot.model.pv.vo.GtiVo;
import com.cdz360.iot.model.pv.vo.PvComVo;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * PvService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class PvServiceTest {

    @Mock
    private GtiRwDs gtiRwDs;

    @Mock
    private GtiRoDs gtiRoDs;

    @Mock
    private DnoGenerator dnoGenerator;

    @Mock
    private EssEquipRwDs essEquipRwDs;

    @InjectMocks
    private PvService pvService;

    private GtiEditParam gtiEditParam;
    private GtiPo gtiPo;
    private EssEquipPo essEquipPo;
    private ListGtiParam listGtiParam;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        gtiEditParam = new GtiEditParam()
            .setDno("GTI001")
            .setName("测试逆变器")
            .setSerialNo("SN001")
            .setVendor(GtiVendor.HUAWEI)
            .setDeviceModel("SUN2000-5KTL")
            .setSiteId("SITE001")
            .setStatus(EquipStatus.NORMAL)
            .setMpptVoltageMin(new BigDecimal("200"))
            .setMpptVoltageMax(new BigDecimal("1000"))
            .setPower(5000L)
            .setOutputVoltage(new BigDecimal("220"))
            .setOutputCurrent(new BigDecimal("22.7"))
            .setApparentPower(new BigDecimal("5000"))
            .setCom(new PvComVo());

        gtiPo = new GtiPo()
            .setId(1L)
            .setDno("GTI001")
            .setName("测试逆变器")
            .setSerialNo("SN001")
            .setVendor(GtiVendor.HUAWEI)
            .setDeviceModel("SUN2000-5KTL")
            .setSiteId("SITE001")
            .setStatus(EquipStatus.NORMAL)
            .setMpptVoltageMin(new BigDecimal("200"))
            .setMpptVoltageMax(new BigDecimal("1000"))
            .setPower(5000L)
            .setOutputVoltage(new BigDecimal("220"))
            .setOutputCurrent(new BigDecimal("22.7"))
            .setApparentPower(new BigDecimal("5000"))
            .setCom(new PvComVo())
            .setCreateTime(new Date())
            .setUpdateTime(new Date());

        essEquipPo = new EssEquipPo()
            .setId(1L)
            .setEssDno("ESS001")
            .setDno("GTI001")
            .setName("测试逆变器")
            .setStatus(EquipStatus.NORMAL)
            .setEquipId(1L)
            .setEquipType(EssEquipType.PV_INV)
            .setEquipModel("SUN2000-5KTL")
            .setVendor("HUAWEI")
            .setEquipNameCn("光伏逆变器")
            .setEquipNameEn("PV Inverter")
            .setEnable(true)
            .setCreateTime(new Date())
            .setUpdateTime(new Date());

        listGtiParam = new ListGtiParam()
            .setSiteId("SITE001")
            .setStatus(EquipStatus.NORMAL);
    }

    @Test
    void testAddGti_Success() {
        // Given
        when(dnoGenerator.genDno(any(EssEquipType.class))).thenReturn("GTI001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(gtiRwDs.upsetGti(any(GtiPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = pvService.addGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        verify(dnoGenerator).genDno(EssEquipType.PV_INV);
        verify(essEquipRwDs).insertEssEquip(any(EssEquipPo.class));
        verify(gtiRwDs).upsetGti(any(GtiPo.class));
    }

    @Test
    void testAddGti_ValidationFailed() {
        // Given
        gtiEditParam.setName(""); // 设置无效的名称

        // When
        Mono<Boolean> result = pvService.addGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备名称不能为空"))
            .verify();

        verifyNoInteractions(dnoGenerator, essEquipRwDs, gtiRwDs);
    }

    @Test
    void testAddGti_EssEquipInsertFailed() {
        // Given
        when(dnoGenerator.genDno(any(EssEquipType.class))).thenReturn("GTI001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = pvService.addGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).insertEssEquip(any(EssEquipPo.class));
        verifyNoInteractions(gtiRwDs);
    }

    @Test
    void testUpdateGti_Success() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(gtiRwDs.updateGtiByDno(any(GtiPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = pvService.updateGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verify(gtiRwDs).updateGtiByDno(any(GtiPo.class));
    }

    @Test
    void testUpdateGti_DnoBlank() {
        // Given
        gtiEditParam.setDno("");

        // When
        Mono<Boolean> result = pvService.updateGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备编号不能为空"))
            .verify();

        verifyNoInteractions(gtiRoDs, gtiRwDs);
    }

    @Test
    void testUpdateGti_UpdateFailed() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(gtiRwDs.updateGtiByDno(any(GtiPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = pvService.updateGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof RuntimeException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verify(gtiRwDs).updateGtiByDno(any(GtiPo.class));
    }

    @Test
    void testListGti_Success() {
        // Given
        List<GtiVo> gtiVoList = Arrays.asList(
            new GtiVo().setDno("GTI001").setName("逆变器1"),
            new GtiVo().setDno("GTI002").setName("逆变器2")
        );
        when(gtiRoDs.findGtiList(any(ListGtiParam.class))).thenReturn(gtiVoList);
        when(gtiRoDs.count(any(ListGtiParam.class))).thenReturn(2L);

        // When
        Mono<ListResponse<GtiVo>> result = pvService.listGti(listGtiParam);

        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(2, response.getTotal());
                assertEquals(2, response.getData().size());
                assertEquals("GTI001", response.getData().get(0).getDno());
                assertEquals("GTI002", response.getData().get(1).getDno());
            })
            .verifyComplete();

        verify(gtiRoDs).findGtiList(listGtiParam);
        verify(gtiRoDs).count(listGtiParam);
    }

    @Test
    void testDeleteGti_Success() {
        // Given
        String dno = "GTI001";
        when(gtiRoDs.getByDno(anyString())).thenReturn(gtiPo);
        when(gtiRwDs.updateGtiByDno(any(GtiPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = pvService.deleteGti(dno);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        verify(gtiRoDs).getByDno(dno);
        verify(gtiRwDs).updateGtiByDno(argThat(gti ->
            gti.getDno().equals(dno)));
    }

    @Test
    void testDeleteGti_DnoBlank() {
        // When
        Mono<Boolean> result = pvService.deleteGti("");

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备编号不能为空"))
            .verify();

        verifyNoInteractions(gtiRoDs, gtiRwDs);
    }

    @Test
    void testDeleteGti_GtiNotFound() {
        // Given
        String dno = "GTI001";
        when(gtiRoDs.getByDno(anyString())).thenReturn(null);

        // When
        Mono<Boolean> result = pvService.deleteGti(dno);

        // Then - 由于getByDno返回null，map操作会产生NullPointerException
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof NullPointerException &&
                    throwable.getMessage().contains("returned a null value"))
            .verify();

        verify(gtiRoDs).getByDno(dno);
        verifyNoInteractions(gtiRwDs);
    }

    @Test
    void testGetGtiById_Success() {
        // Given
        Long id = 1L;
        when(gtiRwDs.getById(id, false)).thenReturn(gtiPo);

        // When
        Mono<GtiPo> result = pvService.getGtiById(id);

        // Then
        StepVerifier.create(result)
            .expectNext(gtiPo)
            .verifyComplete();

        verify(gtiRwDs).getById(id, false);
    }

    @Test
    void testGetGtiById_InvalidId() {
        // When & Then - null值会在Mono.just()时就抛异常
        try {
            pvService.getGtiById(null);
            assert false : "应该抛出异常";
        } catch (NullPointerException e) {
            // 预期行为
        }

        verifyNoInteractions(gtiRwDs);
    }

    @Test
    void testGetGtiById_ZeroId() {
        // When
        Mono<GtiPo> result = pvService.getGtiById(0L);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("逆变器ID不能为空或无效"))
            .verify();

        verifyNoInteractions(gtiRwDs);
    }

    @Test
    void testGetGtiByDno_Success() {
        // Given
        String dno = "GTI001";
        when(gtiRoDs.getByDno(dno)).thenReturn(gtiPo);

        // When
        Mono<GtiPo> result = pvService.getGtiByDno(dno);

        // Then
        StepVerifier.create(result)
            .expectNext(gtiPo)
            .verifyComplete();

        verify(gtiRoDs).getByDno(dno);
    }

    @Test
    void testGetGtiByDno_DnoBlank() {
        // When
        Mono<GtiPo> result = pvService.getGtiByDno("");

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备编号不能为空"))
            .verify();

        verifyNoInteractions(gtiRoDs);
    }

    @Test
    void testListGti_NullParam() {
        // When & Then - null值会在Mono.just()时就抛异常
        try {
            pvService.listGti(null);
            assert false : "应该抛出异常";
        } catch (NullPointerException e) {
            // 预期行为
        }

        verifyNoInteractions(gtiRoDs);
    }

    @Test
    void testAddGti_WithBlankGwno() {
        // Given
        gtiEditParam.setCom(null); // 测试gwno为空的情况
        when(dnoGenerator.genDno(any(EssEquipType.class))).thenReturn("GTI001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(gtiRwDs.upsetGti(any(GtiPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = pvService.addGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        verify(gtiRwDs).upsetGti(argThat(gti ->
            gti.getGwno() != null && gti.getGwno().equals("")));
    }

    @Test
    void testUpdateGti_WithSwVerOnly() {
        // Given - 注意deviceModel为空字符串会触发验证失败
        gtiEditParam.setDeviceModel("");
        gtiEditParam.setSwVer("v2.0.1");

        // When
        Mono<Boolean> result = pvService.updateGti(gtiEditParam);

        // Then - 应该验证失败
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备型号不能为空"))
            .verify();

        verifyNoInteractions(essEquipRwDs, gtiRwDs);
    }

    @Test
    void testUpdateGti_EssEquipUpdateFailed() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = pvService.updateGti(gtiEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verifyNoInteractions(gtiRwDs);
    }

    @Test
    void testUpdateGti_WithoutEssEquipFields() {
        // Given - 只设置swVer为空，deviceModel在验证时会被设置
        GtiEditParam paramWithoutEssFields = new GtiEditParam()
            .setDno("GTI001")
            .setName("测试逆变器")
            .setSerialNo("SN001")
            .setVendor(GtiVendor.HUAWEI)
            .setDeviceModel("SUN2000-5KTL")  // 设置有效值避免验证失败
            .setSiteId("SITE001")
            .setStatus(EquipStatus.NORMAL)
            .setSwVer(null); // swVer为null

        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(gtiRwDs.updateGtiByDno(any(GtiPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = pvService.updateGti(paramWithoutEssFields);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // 由于deviceModel不为空，会更新ess_equip表
        verify(gtiRwDs).updateGtiByDno(any(GtiPo.class));
        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
    }
}
