package com.cdz360.iot.ess.biz;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.iot.biz.utils.DnoGenerator;
import com.cdz360.iot.ds.ro.MeterRoDs;
import com.cdz360.iot.ds.rw.EssEquipRwDs;
import com.cdz360.iot.ds.rw.MeterRwDs;
import com.cdz360.iot.ess.model.param.MeterEditParam;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import com.cdz360.iot.model.meter.po.MeterPo;
import com.cdz360.iot.model.meter.type.MeterStatusType;
import com.cdz360.iot.model.meter.vo.MeterVo;
import com.cdz360.iot.model.param.MeterListParam;
import com.cdz360.iot.model.pv.type.GtiVendor;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * MeterService 单元测试 验证电表管理业务逻辑和SQL操作的正确性
 */
@ExtendWith(MockitoExtension.class)
class MeterServiceTest {

    @Mock
    private MeterRoDs meterRoDs;

    @Mock
    private MeterRwDs meterRwDs;

    @Mock
    private EssEquipRwDs essEquipRwDs;

    @Mock
    private DnoGenerator dnoGenerator;

    @InjectMocks
    private MeterService meterService;

    private MeterEditParam meterEditParam;
    private MeterPo meterPo;
    private MeterListParam meterListParam;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        meterEditParam = new MeterEditParam()
            .setDno("METER001")
            .setNo("M001")
            .setSiteId("SITE001")
            .setName("测试电表")
            .setVendor(GtiVendor.HUAWEI)
            .setDeviceModel("DDZY1296")
            .setStatus(MeterStatusType.ONLINE)
            .setType(EssEquipType.METER)
            .setCtr(100)
            .setVtr(100)
            .setArea("区域A");

        meterPo = new MeterPo()
            .setId(1L)
            .setDno("METER001")
            .setNo("M001")
            .setSiteId("SITE001")
            .setName("测试电表")
            .setStatus(MeterStatusType.ONLINE)
            .setVendor(GtiVendor.HUAWEI)
            .setCtr(100)
            .setVtr(100)
            .setArea("区域A")
            .setOtherDevice(false)
            .setComment("")
            .setCreateTime(new Date())
            .setUpdateTime(new Date());

        meterListParam = new MeterListParam()
            .setSiteId("SITE001")
            .setStatus(EquipStatus.NORMAL);
    }

    @Test
    void testAddMeter_Success() {
        // Given
        when(dnoGenerator.genDno(EssEquipType.METER)).thenReturn("METER001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.insertMeter(any(MeterPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // 验证SQL操作顺序和参数
        verify(dnoGenerator).genDno(EssEquipType.METER);
        verify(essEquipRwDs).insertEssEquip(argThat(essEquip ->
            essEquip.getEssDno().equals("SITE001") &&
                essEquip.getDno().equals("METER001") &&
                essEquip.getName().equals("测试电表") &&
                essEquip.getEquipType() == EssEquipType.METER &&
                essEquip.getEquipModel().equals("DDZY1296") &&
                essEquip.getEnable() &&
                essEquip.getStatus() == EquipStatus.NORMAL
        ));
        verify(meterRwDs).insertMeter(argThat(meter ->
            meter.getDno().equals("METER001") &&
                meter.getNo().equals("M001") &&
                meter.getSiteId().equals("SITE001") &&
                meter.getName().equals("测试电表") &&
                meter.getCtr() == 100
        ));
    }

    @Test
    void testAddMeter_ValidationFailed_NoBlank() {
        // Given
        meterEditParam.setNo(""); // 设置无效的电表编号

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("电表编号不能为空"))
            .verify();

        verifyNoInteractions(dnoGenerator, essEquipRwDs, meterRwDs);
    }

    @Test
    void testAddMeter_ValidationFailed_SiteIdBlank() {
        // Given
        meterEditParam.setSiteId("");

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("场站ID不能为空"))
            .verify();

        verifyNoInteractions(dnoGenerator, essEquipRwDs, meterRwDs);
    }

    @Test
    void testAddMeter_ValidationFailed_NameBlank() {
        // Given
        meterEditParam.setName("");

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备名称不能为空"))
            .verify();

        verifyNoInteractions(dnoGenerator, essEquipRwDs, meterRwDs);
    }

    @Test
    void testAddMeter_ValidationFailed_TypeNull() {
        // Given
        meterEditParam.setType(null);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("标签不能为空"))
            .verify();

        verifyNoInteractions(dnoGenerator, essEquipRwDs, meterRwDs);
    }

    @Test
    void testAddMeter_EssEquipInsertFailed() {
        // Given
        when(dnoGenerator.genDno(EssEquipType.METER)).thenReturn("METER001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).insertEssEquip(any(EssEquipPo.class));
        verifyNoInteractions(meterRwDs);
    }

    @Test
    void testAddMeter_MeterInsertFailed() {
        // Given
        when(dnoGenerator.genDno(EssEquipType.METER)).thenReturn("METER001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.insertMeter(any(MeterPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).insertEssEquip(any(EssEquipPo.class));
        verify(meterRwDs).insertMeter(any(MeterPo.class));
    }

    @Test
    void testAddMeter_WithoutDeviceModel() {
        // Given
        meterEditParam.setDeviceModel(null);
        when(dnoGenerator.genDno(EssEquipType.METER)).thenReturn("METER001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.insertMeter(any(MeterPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        verify(essEquipRwDs).insertEssEquip(argThat(essEquip ->
            essEquip.getEquipModel() == null
        ));
    }

    @Test
    void testUpdateMeter_Success() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = meterService.updateMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // 验证SQL更新操作
        verify(essEquipRwDs).updateEssEquip(argThat(essEquip ->
            essEquip.getDno().equals("METER001") &&
                essEquip.getEssDno().equals("SITE001") &&
                essEquip.getName().equals("测试电表") &&
                essEquip.getEquipModel().equals("DDZY1296")
        ));
        verify(meterRwDs).updateMeter(argThat(meter ->
            meter.getDno().equals("METER001") &&
                meter.getUpdateTime() != null
        ));
    }

    @Test
    void testUpdateMeter_DnoBlank() {
        // Given
        meterEditParam.setDno("");

        // When
        Mono<Boolean> result = meterService.updateMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("电表dno不能为空"))
            .verify();

        verifyNoInteractions(essEquipRwDs, meterRwDs);
    }

    @Test
    void testUpdateMeter_EssEquipUpdateFailed() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.updateMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verifyNoInteractions(meterRwDs);
    }

    @Test
    void testUpdateMeter_MeterUpdateFailed() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.updateMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof RuntimeException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verify(meterRwDs).updateMeter(any(MeterPo.class));
    }

    @Test
    void testUpdateMeter_OnlyRequiredFields() {
        // Given - 只有必填字段，测试最小参数更新
        MeterEditParam minimalParam = new MeterEditParam()
            .setDno("METER001")
            .setNo("M001")
            .setSiteId("SITE001")  // 提供有效值
            .setName("测试电表")    // 提供有效值
            .setType(EssEquipType.METER)
            .setDeviceModel(""); // 空字符串

        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = meterService.updateMeter(minimalParam);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // 由于name和siteId不为空，会更新ess_equip
        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verify(meterRwDs).updateMeter(any(MeterPo.class));
    }

    @Test
    void testUpdateMeter_ValidationFailed_EmptyFields() {
        // Given - 测试空字段验证失败
        MeterEditParam invalidParam = new MeterEditParam()
            .setDno("METER001")
            .setNo("M001")
            .setSiteId("")  // 空字符串应该验证失败
            .setName("测试电表")
            .setType(EssEquipType.METER);

        // When
        Mono<Boolean> result = meterService.updateMeter(invalidParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("场站ID不能为空"))
            .verify();

        verifyNoInteractions(essEquipRwDs, meterRwDs);
    }

    @Test
    void testGetMeterById_Success() {
        // Given
        Long id = 1L;
        when(meterRwDs.getById(id, false)).thenReturn(meterPo);

        // When
        Mono<MeterPo> result = meterService.getMeterById(id);

        // Then
        StepVerifier.create(result)
            .expectNext(meterPo)
            .verifyComplete();

        // 验证SQL查询操作
        verify(meterRwDs).getById(id, false);
    }

    @Test
    void testGetMeterById_InvalidId() {
        // When
        Mono<MeterPo> result = meterService.getMeterById(0L);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("电表ID不能为空或无效"))
            .verify();

        verifyNoInteractions(meterRwDs);
    }

    @Test
    void testGetMeterById_NotFound() {
        // Given
        Long id = 999L;
        when(meterRwDs.getById(id, false)).thenReturn(null);

        // When
        Mono<MeterPo> result = meterService.getMeterById(id);

        // Then
        StepVerifier.create(result)
            .verifyComplete(); // Mono.justOrEmpty(null) 返回空

        verify(meterRwDs).getById(id, false);
    }

    @Test
    void testGetMeterByDno_Success() {
        // Given
        String dno = "METER001";
        when(meterRoDs.getByDno(dno)).thenReturn(meterPo);

        // When
        Mono<MeterPo> result = meterService.getMeterByDno(dno);

        // Then
        StepVerifier.create(result)
            .expectNext(meterPo)
            .verifyComplete();

        // 验证SQL查询操作
        verify(meterRoDs).getByDno(dno);
    }

    @Test
    void testGetMeterByDno_DnoBlank() {
        // When
        Mono<MeterPo> result = meterService.getMeterByDno("");

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备编号不能为空"))
            .verify();

        verifyNoInteractions(meterRoDs);
    }

    @Test
    void testGetMeterByDno_NotFound() {
        // Given
        String dno = "NOTFOUND";
        when(meterRoDs.getByDno(dno)).thenReturn(null);

        // When
        Mono<MeterPo> result = meterService.getMeterByDno(dno);

        // Then
        StepVerifier.create(result)
            .verifyComplete(); // switchIfEmpty返回空

        verify(meterRoDs).getByDno(dno);
    }

    @Test
    void testListMeter_Success() {
        // Given
        MeterVo meter1 = new MeterVo();
        meter1.setDno("METER001");
        meter1.setName("电表1");

        MeterVo meter2 = new MeterVo();
        meter2.setDno("METER002");
        meter2.setName("电表2");

        List<MeterVo> meterVoList = Arrays.asList(meter1, meter2);
        when(meterRoDs.getMeterVoList2(any(MeterListParam.class))).thenReturn(meterVoList);
        when(meterRoDs.getMeterVoList2Count(any(MeterListParam.class))).thenReturn(2L);

        // When
        Mono<ListResponse<MeterVo>> result = meterService.listMeter(meterListParam);

        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(2, response.getTotal());
                assertEquals(2, response.getData().size());
                assertEquals("METER001", response.getData().get(0).getDno());
                assertEquals("METER002", response.getData().get(1).getDno());
            })
            .verifyComplete();

        // 验证SQL查询操作
        verify(meterRoDs).getMeterVoList2(meterListParam);
        verify(meterRoDs).getMeterVoList2Count(meterListParam);
    }

    @Test
    void testListMeter_EmptyResult() {
        // Given
        when(meterRoDs.getMeterVoList2(any(MeterListParam.class))).thenReturn(Arrays.asList());
        when(meterRoDs.getMeterVoList2Count(any(MeterListParam.class))).thenReturn(0L);

        // When
        Mono<ListResponse<MeterVo>> result = meterService.listMeter(meterListParam);

        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(0, response.getTotal());
                assertEquals(0, response.getData().size());
            })
            .verifyComplete();

        verify(meterRoDs).getMeterVoList2(meterListParam);
        verify(meterRoDs).getMeterVoList2Count(meterListParam);
    }

    @Test
    void testDeleteMeter_Success() {
        // Given
        String dno = "METER001";
        when(meterRoDs.getByDno(dno)).thenReturn(meterPo);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(true);

        // When
        Mono<Boolean> result = meterService.deleteMeter(dno);

        // Then
        StepVerifier.create(result)
            .expectNext(true)
            .verifyComplete();

        // 验证SQL操作
        verify(meterRoDs).getByDno(dno);
        verify(meterRwDs).updateMeter(argThat(meter ->
            meter.getDno().equals(dno) &&
                meter.getUpdateTime() != null
        ));
    }

    @Test
    void testDeleteMeter_DnoBlank() {
        // When
        Mono<Boolean> result = meterService.deleteMeter("");

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备编号不能为空"))
            .verify();

        verifyNoInteractions(meterRoDs, meterRwDs);
    }

    @Test
    void testDeleteMeter_MeterNotFound() {
        // Given
        String dno = "NOTFOUND";
        when(meterRoDs.getByDno(dno)).thenReturn(null);

        // When
        Mono<Boolean> result = meterService.deleteMeter(dno);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcArgumentException &&
                    throwable.getMessage().contains("设备不存在: " + dno))
            .verify();

        verify(meterRoDs).getByDno(dno);
        verifyNoInteractions(meterRwDs);
    }

    @Test
    void testDeleteMeter_UpdateFailed() {
        // Given
        String dno = "METER001";
        when(meterRoDs.getByDno(dno)).thenReturn(meterPo);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.deleteMeter(dno);

        // Then
        StepVerifier.create(result)
            .expectNext(false)
            .verifyComplete();

        verify(meterRoDs).getByDno(dno);
        verify(meterRwDs).updateMeter(any(MeterPo.class));
    }

    /**
     * 测试事务回滚场景 - 添加电表时ess_equip成功但meter失败
     */
    @Test
    void testAddMeter_TransactionRollback() {
        // Given
        when(dnoGenerator.genDno(EssEquipType.METER)).thenReturn("METER001");
        when(essEquipRwDs.insertEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.insertMeter(any(MeterPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.addMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof DcServiceException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        // 验证两个表的SQL操作都被调用
        verify(essEquipRwDs).insertEssEquip(any(EssEquipPo.class));
        verify(meterRwDs).insertMeter(any(MeterPo.class));
    }

    /**
     * 测试事务回滚场景 - 更新电表时ess_equip成功但meter失败
     */
    @Test
    void testUpdateMeter_TransactionRollback() {
        // Given
        when(essEquipRwDs.updateEssEquip(any(EssEquipPo.class))).thenReturn(true);
        when(meterRwDs.updateMeter(any(MeterPo.class))).thenReturn(false);

        // When
        Mono<Boolean> result = meterService.updateMeter(meterEditParam);

        // Then
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable instanceof RuntimeException &&
                    throwable.getMessage().contains("保存失败"))
            .verify();

        // 验证两个表的SQL操作都被调用
        verify(essEquipRwDs).updateEssEquip(any(EssEquipPo.class));
        verify(meterRwDs).updateMeter(any(MeterPo.class));
    }
}
