package com.cdz360.iot.ess.south.mqtt;

import com.cdz360.base.model.es.vo.BmsBundleRtInfo;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.BmsStackRtInfo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ds.ro.ess.ds.EssEquipRoDs;
import com.cdz360.iot.ess.model.emu.dto.EmuRtInfoDto;
import com.cdz360.iot.model.ess.po.EssEquipPo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EmuMqttRtInfoHandlerTest {

    @Mock
    private EssEquipRoDs essEquipRoDs;
    @InjectMocks
    private EmuMqttRtInfoHandler emuMqttRtInfoHandler;

    @Test
    public void testGenerateDnoNameMap() {
        String json = "{\"seq\":608776,\"dno\":\"*********\",\"ts\":1724210525,\"tz\":\"+8\",\"status\":1,\"bmsList\":[{\"dno\":\"B24001\",\"name\":\"BMS_1\",\"ts\":1724210524,\"status\":0,\"sn\":\"3A83AA71\",\"alarms\":[],\"pcsDno\":\"P24001\",\"stackInfoList\":[{\"dno\":\"BA24001\",\"status\":3,\"texts\":[{\"code\":110001,\"name\":\"TOPBMU序列号SN\",\"v\":\"3A83AA71\",\"desc\":\"TOPBMU序列号SN\"},{\"code\":110010,\"name\":\"TOPBMU软件版本号\",\"v\":\"1.05\",\"desc\":\"TOPBMU软件版本号\"},{\"code\":110015,\"name\":\"协议版本号\",\"v\":\"2.3\",\"desc\":\"协议版本号\"},{\"code\":110006,\"name\":\"TOPBMU硬件版本号\",\"v\":\"1.00\",\"desc\":\"TOPBMU硬件版本号\"}],\"cfgs\":[{\"code\":110172,\"name\":\"BMU_Rly远程断开\",\"v\":0,\"desc\":\"0：失效; 1：执行\",\"max\":1,\"min\":0,\"decimal\":0},{\"code\":110108,\"name\":\"SOC校准模式使能\",\"v\":0,\"desc\":\"0：无效; 1：使能\",\"max\":1,\"min\":0,\"decimal\":0}],\"signals\":[{\"code\":110105,\"name\":\"充电标志\",\"v\":1,\"desc\":\"允许\"},{\"code\":110106,\"name\":\"放电标志\",\"v\":1,\"desc\":\"允许\"},{\"code\":110107,\"name\":\"SOC校准模式\",\"v\":0,\"desc\":\"退出\"},{\"code\":110101,\"name\":\"逆变器通讯故障\",\"v\":0,\"desc\":\"正常\"},{\"code\":110111,\"name\":\"重启次数\",\"v\":44},{\"code\":110117,\"name\":\"系统并簇数量\",\"v\":9}],\"alarms\":[],\"bmsDno\":\"B24001\",\"chargeable\":true,\"dischargeable\":true,\"bundleIds\":[0,1,2,3,4,5,6,7,8],\"bundleList\":[{\"dno\":\"BB240011\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"4A83AA71\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":0},{\"dno\":\"BB240012\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"9990E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":1},{\"dno\":\"BB240013\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"FD90E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":2},{\"dno\":\"BB240014\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"8D92E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":3},{\"dno\":\"BB240015\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"AD95E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":4},{\"dno\":\"BB240016\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"CD98E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":5},{\"dno\":\"BB240017\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"3199E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":6},{\"dno\":\"BB240018\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"9599E771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":7},{\"dno\":\"BB240019\",\"texts\":[{\"code\":110034,\"name\":\"BMU序列号SN\",\"v\":\"5D9AE771\",\"desc\":\"BMU序列号SN\"},{\"code\":110036,\"name\":\"BMU软件版本号\",\"v\":\"1.03\",\"desc\":\"BMU软件版本号\"},{\"code\":110035,\"name\":\"BMU硬件版本号\",\"v\":\"1.01\",\"desc\":\"BMU硬件版本号\"}],\"cfgs\":[],\"signals\":[{\"code\":110171,\"name\":\"主继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110175,\"name\":\"预充继电器状态\",\"v\":0,\"desc\":\"断开\"},{\"code\":110176,\"name\":\"塑壳断路器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110174,\"name\":\"负极继电器状态\",\"v\":1,\"desc\":\"闭合\"},{\"code\":110170,\"name\":\"簇状态码\",\"desc\":\"簇状态码\"},{\"code\":110210,\"name\":\"BMU SN重复\",\"desc\":\"BMU SN重复\"},{\"code\":110211,\"name\":\"BMU ID重复\",\"desc\":\"BMU ID重复\"},{\"code\":110212,\"name\":\"BMU ID不连续\",\"desc\":\"BMU ID不连续\"},{\"code\":110215,\"name\":\"各簇LMU数量不一致\",\"desc\":\"各簇LMU数量不一致\"},{\"code\":110102,\"name\":\"EMS通讯丢失判断\",\"desc\":\"EMS通讯丢失判断\"},{\"code\":110460,\"name\":\"簇总压差异检测\",\"desc\":\"簇总压差异检测\"},{\"code\":110220,\"name\":\"并机失败检测\",\"desc\":\"并机失败检测\"},{\"code\":110230,\"name\":\"无BMU报警\",\"desc\":\"无BMU报警\"},{\"code\":110104,\"name\":\"EMS通讯丢失使能标志\",\"desc\":\"EMS通讯丢失使能标志\"}],\"alarms\":[],\"idx\":8}]}],\"socCorrectMode\":false}]}";
        EmuRtInfoDto emuRtInfoIn = JsonUtils.fromJson(json, EmuRtInfoDto.class);

        List<EssEquipPo> equipPos = new ArrayList<>();
        emuRtInfoIn.getBmsList().forEach(info -> {
            info.getStackInfoList().forEach(s -> {
                for (int i = 0; i < s.getBundleList().size(); i++) {
                    equipPos.add(new EssEquipPo().setDno(s.getBundleList().get(i).getDno())
                        .setName("name" + (i + 1)));
                }
            });
        });

        Mockito.when(essEquipRoDs.findEquipList(Mockito.any())).thenReturn(equipPos);
        Map<String, String> dnoNameMap = emuMqttRtInfoHandler.generateDnoNameMap(
            emuRtInfoIn);
        System.out.println("dnoNameMap = " + JsonUtils.toJsonString(dnoNameMap));

        emuRtInfoIn.getBmsList().forEach(info -> {
            info.getStackInfoList().forEach(s -> {
                s.getBundleList().forEach(b -> {
                    b.setName(dnoNameMap.get(b.getDno()));
                });
            });
        });
        Assertions.assertEquals(equipPos.size(), dnoNameMap.size(),
            "testGenerateDnoNameMap fail");
    }

    @Test
    public void testGenerateDnoNameMap2() {
        EmuRtInfoDto emuRtInfoIn = new EmuRtInfoDto();
        List<BmsRtInfo> bmsList = new ArrayList<>();
        BmsRtInfo rtInfo = new BmsRtInfo();
        rtInfo.setStackInfoList(new ArrayList<>());
        bmsList.add(rtInfo);
        emuRtInfoIn.setBmsList(bmsList);

        Map<String, String> dnoNameMap = emuMqttRtInfoHandler.generateDnoNameMap(
            emuRtInfoIn);
        System.out.println("dnoNameMap = " + JsonUtils.toJsonString(dnoNameMap));
        Assertions.assertEquals(0, dnoNameMap.size(),
            "testGenerateDnoNameMap2 fail");
    }

    @Test
    public void testSetBundleName() {
        BmsRtInfo rtInfo = new BmsRtInfo();
        List<BmsStackRtInfo> stackInfoList = new ArrayList<>();
        rtInfo.setStackInfoList(stackInfoList);

        List<BmsBundleRtInfo> bundleList = new ArrayList<>();
        BmsBundleRtInfo bundle = new BmsBundleRtInfo();
        bundleList.add(bundle);
        stackInfoList.add(new BmsStackRtInfo().setBundleList(bundleList));

        emuMqttRtInfoHandler.setBundleName(rtInfo, new HashMap<>());
    }

}